<form class="form-horizontal" role="form" style="display: inline-block; width: 100%;">
	<div class="form-group col-md-12">
	    <div class="col-md-12"  style="margin: 5px 0;text-align: center; padding: 10px; border: 1px dotted #ccc;">
	    	<div style="border-left: 1px dotted #666;">
      			<table class="table default" >
              <thead class="header">
                <tr>
                  <th rowspan="2">Stt</th>
                  <th rowspan="2">Tên thực phẩm</th>
                  <th rowspan="2">Ng<PERSON>y nhập kho</th>
                  <th colspan="2"><PERSON><PERSON> lượng</th>
                  <th rowspan="2">Gi<PERSON> (VNĐ)</th>
                  <th rowspan="2">Tiền xuất</th>
                  <th rowspan="2">#</th>
                </tr>
                <tr>
                  <th>Nhập</th>
                  <th><PERSON><PERSON><PERSON></th>
                </tr>
              </thead>
      				<tbody class="hightlight-lowgray" ng-repeat="(ind1, item) in reback.lists">
                <tr class="title-sub">
                  <td colspan="6" class="left">
                    <label class="checkbox-inline" title="Tích chọn để xuất excel theo ngày">
                      <input type="checkbox" ng-model="item.selected" ng-true-value="1" ng-false-value="0">
                      <b>
                        <span>Ngày xuất: </span> 
                        <span ng-bind="item.date"></span> - 
                        <span ng-bind="item.note"></span>
                      </b>
                    </label>
                  </td>
                  <td class="right">
                    <span ng-bind="digit_grouping(round(item.money, 0))"></span>
                  </td>
                  <td></td>
                </tr>
      					<tr ng-repeat="(ind2, row) in item.exports">
                  <td>
                    <span ng-bind="ind2+1"></span>
                  </td>
      						<td style="text-align: left;" class="left">
      							<span ng-bind="row.name"></span>
      						</td>
                  <td class="left">
                    <span ng-bind="row.storages_date"></span>
                  </td>
                  <td>
                    <span ng-bind="row.quantity"></span>
                  </td>
                  <td>
                    <span ng-bind="row.quantity_export"></span>
                  </td>
      						<td class="right">
      							<span ng-bind="digit_grouping(row.price)"></span>
      						</td>
                  <td class="right">
                    <span ng-bind="digit_grouping(round(row.money, 0))"></span>
                  </td>
      						<td>
      							<span class="glyphicon glyphicon-trash btn-over-red color-orange" title="Khôi phục lại kho xuất trả NCC" ng-click="storage_inventory.undoExportInventoriesFinalYear(ind2, item);"></span>
      						</td>
      					</tr>
      				</tbody>
              <tfoot>
                <tr>
                  <td colspan="6"></td>
                  <td class="right">
                    <b><span ng-bind="digit_grouping(round(reback.money, 0))"></span></b>
                  </td>
                  <td></td>
                </tr>
              </tfoot>
      			</table>
	        </div>
	    </div>
	    <div class="col-md-12" style="text-align: center;">
		<img class="img-btn-info" src="../images/icons/exel-icon2.png" style="width: 20px;">
        <a class="mR5" href="{{$CFG.remote.base_url}}/dinhduong/storage_inventory/inventoriesFinalYear?dates={{storage_inventory.getDatesExportBack()}}" target="_blank">Mẫu 1 </a> | 
        <a href="{{$CFG.remote.base_url}}/dinhduong/storage_inventory/exportReturnSupplier?dates={{storage_inventory.getDatesExportBack()}}" target="_blank" title="Xuất kho trả nhà cung cấp theo riêng từng nhà cung cấp">Mẫu 2 (Tách theo NCC)</a>
        <!-- <i class="color-orange"><b>Lưu ý: </b> <span>Không chọn ngày xuất nào tương đương với chọn hết.</span></i> -->
	    </div>
	</div>
</form>