<link rel="stylesheet" type="text/css"
      href="http://localhost:3000/css/dinhduong/storage.css?=rand()">
<form class="form-horizontal" id="frm-storage" role="form">
    <div class="str-main">
        <div class="item-str-main">
            <div class="col-md-5 title-item-str">
                <p class="valid">Ngày nhập kho :</p>
            </div>
            <div class="col-md-7 content-item-str">
                <input id="txt-date" style="height: 29px;width: 160px;" date-box="storage.keysearch_date">
            </div>
        </div>

        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p class="valid">Kho :</p>
            </div>
            <div class="col-md-7 content-item-str">
                <select ng-options="item as item.name for (index,item) in storage.warehouses"
                        ng-model="storage.warehouse" id="warehouse_id" ng-change="storage.warehouseChange()"
                        class="new-style-input-str">
                </select>
            </div>
        </div>
        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p class="valid">Tên thực phẩm :</p>
            </div>
            <div class="col-md-7 content-item-str">
                <input id="food_combobox" value-field="id" param-key="q" param-value="storage.food.food_name"
                       text-field="name" combo-box-food="storage.food"
                       url="{{$CFG.remote.base_url}}/doing/dinhduong/storage/foods"
                       on-select="storage.foodSelectNew(storage.food)" data="cache.foods" mode="remote"
                       style="width: 160px;height: 29px;" placeholder="Tên thực phẩm">
            </div>
        </div>
        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p class="valid">Số lượng :</p>
            </div>
            <div class="col-md-7 content-item-str">
				<span class="container-field-measure">
					<input class="form-control color-green" min="0" id="so_luong" ng-model="add.quantity"
                           placeholder="Số lượng" style="width: 160px;z-index: 2;height: 30px;">
	                <label class="measure new-price-label measure-style-new" ng-bind="add.measure_name"></label>
	            </span>
            </div>
        </div>
        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p>Đơn giá :</p>
            </div>
            <div class="col-md-7 content-item-str">
				<span class="container-field-measure">
					<input id="txt-price" class="form-control color-green" ng-value="digit_grouping(add.price)"
                           placeholder="Đơn giá"
                           style="width: 160px;position: absolute; left: 0; top: 0; z-index: 1; height: 30px">
	                <input class="form-control color-green input-unfocus-hidden" type="number" min="0" id="don_gia"
                           ng-model="add.price" placeholder="Đơn giá" style="width: 160px;z-index: 2;height: 30px;">
	                <label class="measure new-price-label">VNĐ</label>
	            </span>
            </div>
        </div>
        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p>Nhà cung cấp :</p>
            </div>
            <div class="col-md-7 content-item-str">
                <select id="txt-supplier" ng-options="item as item.name for item in storage.suppliers track by item.id"
                        ng-model="add.supplier" class="new-style-input-str">
                    <option value="">Không có nhà cung cấp</option>
                </select>
            </div>
        </div>
        <div class="item-str-main ch">
            <div class="col-md-5 title-item-str">
                <p>Lưu NCC về Thực phẩm trường?</p>
            </div>
            <div class="col-md-7 content-item-str">
                <input id="chk_save_ncc_tptruong" type="checkbox" checked="checked" style="margin-top:8px;">
            </div>
        </div>
                    <div class="item-str-main ch" ng-init="storage.has_receipt=1" ng-if="sys.configs.pkhc_show_check_has_receipt">
                <div class="col-md-5 title-item-str">
                    <p>Mua vào có hóa đơn không</p>
                </div>
                <div class="col-md-7 content-item-str">
                    <input name="radio-print" type="radio" ng-value="1" ng-model="storage.has_receipt">&nbsp;Có
                    <input name="radio-print" type="radio" ng-value="0" ng-model="storage.has_receipt">&nbsp;Không
                </div>
            </div>
                    <button class="btn btn-default" ng-click="storage.showExtend = !storage.showExtend">
            <i class="fa fa-angle-double-down" ng-if="!storage.showExtend"> Mở rộng</i>
            <i class="fa fa-angle-double-up" ng-if="storage.showExtend"> Thu nhỏ</i>
        </button>
        <div ng-if="storage.showExtend" class="storage-detail">
            <div class="item-str-main ch">
                <div class="col-md-5 title-item-str">
                    <p>Hạn sử dụng</p>
                </div>
                <div class="col-md-7 content-item-str">
				<span class="container-field-measure">
                    <input style="height: 29px;width: 160px;" date-box="storage.expiry" value="">
	            </span>
                </div>
            </div>
            <div class="item-str-main ch">
                <div class="col-md-5 title-item-str">
                    <p>Điều kiện bảo quản</p>
                </div>
                <div class="col-md-7 content-item-str">
				<span class="container-field-measure">
					<input class="form-control color-green" ng-model="add.preserved"
                           placeholder="Điều kiện bảo quản" style="width: 160px;z-index: 2;height: 30px;">
	            </span>
                </div>
            </div>
            <div class="item-str-main ch">
                <div class="col-md-5 title-item-str">
                    <p>Chứng từ</p>
                </div>
                <div class="col-md-7 content-item-str">
				<span class="container-field-measure">
					<input class="form-control color-green" ng-model="add.license"
                           placeholder="Chứng từ" style="width: 160px;z-index: 2;height: 30px;">
	            </span>
                </div>
            </div>
            <div class="item-str-main ch">
                <div class="col-md-5 title-item-str">
                    <p>Kiểm tra cảm quan</p>
                </div>
                <div class="col-md-7 content-item-str">
                    <input name="radio-print" type="radio" ng-value="1" ng-model="add.pass">&nbsp;Đạt
                    <input name="radio-print" type="radio" ng-value="0" ng-model="add.pass">&nbsp;Không đạt
                    <input name="radio-print" type="radio" ng-value="-1" ng-model="storageDetail.pass">&nbsp;Để trống
                </div>
            </div>
            <div class="item-str-main ch">
                <div class="col-md-5 title-item-str">
                    <p>Ghi chú</p>
                </div>
                <div class="col-md-7 content-item-str">
					<input class="form-control color-green" ng-model="add.note"
                           placeholder="Ghi chú" style="width: 160px;z-index: 2;height: 30px;">
                </div>
            </div>
        </div>
    </div>
    <div class="str-main">
        <button class="btn btn-primary" id="btn-save" ng-click="storage.AddStorageNew()">
            <span class="bootstrap-dialog-button-icon glyphicon glyphicon-floppy-disk"></span>
            Thêm mới
        </button>
    </div>
</form>
