(function (app) {
    app.controller('StudentVaccinationDashboardController', DashboardController);

    function DashboardController($scope, $q, ACTIVE_MONTHS, AuthService, CommonService, CourseService, DiseaseService, StudentMedicalExaminationService,StudentVaccinationService, $uibModal) {
        var vm = this;

        $scope.sys.module.title = 'Tiêm chủng';

        vm.filters = {
            keyword: '',
            month: 0,
            school_year: $CFG.active_year || (new Date()).getFullYear(),
            course_id: 0,
        };

        vm.paginate = {
            maxSize: 5,
            limit: 50,
            page: 1,
            total: 0,
            perPages: [15, 30, 50, 100],
        };

        vm.courses = [];

        vm.years = CommonService.generateSchoolYear();

        vm.months = _.map(ACTIVE_MONTHS, function (month) {
            return {id: month, title: _.padStart(month, 2, '0')};
        });

        vm.month_years = _.map(ACTIVE_MONTHS, function (month) {
            if (month >= 1 && month <=8) {
                return {id: month, title: _.padStart(month, 2, '0') + '/' + (vm.filters.school_year + 1)};
            }
            return {id: month, title: _.padStart(month, 2, '0') + '/' + vm.filters.school_year};
        });

        vm.months.unshift({id: 0, title: 'Chọn tháng'});

        vm.medicalExaminations = {};
        vm.medicalExaminationNote = {};
        vm.medicalExaminationConclusion = {};

        CourseService.fetchCourses().then(function (courses) {
            if (!AuthService.isSuperAdmin()) {
                vm.filters.course_id = _.get(courses, '[0]courses.data[0].id', -1);
            }

            if (AuthService.isSuperAdmin()) {
                courses.unshift({id: 0, name: 'Toàn trường'});
            } else {
                courses.unshift({id: -1, name: 'Chọn lớp'});
            }

            vm.courses = _.assign({}, courses);
            vm.full_courses = Object.values(vm.courses).reduce((ob, item) => {
                if('courses' in item) {
                    item.courses.data.forEach( v => {
                        ob[v.id] = v.name;
                    })
                }
                return ob;
            },{});
        });

        function fetchStudents() {
            if (!vm.filters.school_year || !vm.filters.month || vm.filters.course_id < 0) {
                return;
            }

            $q.all([
                StudentVaccinationService.fetchStudents(vm.paginate.page, vm.paginate.limit, vm.filters)
            ]).then(function (response) {
                vm.students = response[0].students;
                vm.vaccinations = response[0].vaccinations;
                vm.paginate.total = response[0].total;
                vm.unit_level = response[0].user.level;
            });
        }

        vm.onVaccinationOfStudentChange = function (student, vaccination) {
            StudentVaccinationService.store(
                student.id,
                vaccination.id,
                vaccination.value ? 1:0,
                vm.filters.school_year,
                vm.filters.month,
                vaccination.note
            ).then(function () {
                // TODO:
            });
        };

        vm.getCourseName = function (course_id) {
            if(course_id in vm.full_courses) {
                return vm.full_courses[course_id]
            }
            return "";
        }


        vm.onSchoolYearChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onMonthChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onCourseChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onPageChange = function () {
            vm.students = [];
            fetchStudents();
        };

        vm.onKeywordChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onPageLimitChange = function () {
            vm.paginate.page = 1;
            vm.students = [];
            fetchStudents();
        };

        vm.reload = function reload() {
            vm.paginate.page = 1;
            fetchStudents();
        };
        vm.onViewModal = function (student) {
            $scope.student = student;
            StudentVaccinationService.detail(
                student.id
            ).then(function (response) {
                $scope.histories = response.data;
            });
        }
    }

    DashboardController.$inject = [
        '$scope',
        '$q',
        'ACTIVE_MONTHS',
        'AuthService',
        'CommonService',
        'CourseService',
        'DiseaseService',
        'StudentMedicalExaminationService',
        'StudentVaccinationService',
        '$uibModal'
    ];
})(window.angular_app);
