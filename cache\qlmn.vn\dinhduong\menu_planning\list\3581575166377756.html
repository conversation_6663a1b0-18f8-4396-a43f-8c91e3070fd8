<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/menu_planning.css" />
<div class="tbl_container" ng-controller="menu_planningController" id="menu_planningController">
	<div class="tbl-container-header header-kh-ct" id="tb_menu_planning_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="menu_planning"></div>
			<div class="function-kh-ct">
				<form id="frm-storage" ng-init="save_together_price.value=1;user_level = 4">
				  <span class="icon-searching glyphicon" ng-show="keysearch.group_id || keysearch.name" onclick="$.menu_planning.cleanSearch()">
						<span class="glyphicon glyphicon-filter hover-pointer" title="Bỏ tìm kiếm" >
	                        <span class="glyphicon glyphicon-remove"></span>
	                    </span>
	                </span>
						<!-- <input id="group_id" placeholder="Lọc theo nhóm trẻ"> -->
						<span>
							<input id="name" ng-model="keysearch.name" ng-model-options="{debounce: 700}" style="height: 25px; text-align: left; padding-left:5px;" placeholder="Tên hoặc mã thực đơn" ng-change="menu_planning.onchangeKeysearchName(keysearch)">
							<input id="name_dishes" ng-model="keysearch.name_dishes" ng-model-options="{debounce: 700}" style="height: 25px; text-align: left; padding-left:5px;" placeholder="Tên món ăn" ng-change="menu_planning.onchangeKeysearchName()">
						</span>
				    <select class="" name="nhomtre" id="nhomtre" style="height: 25px; width: 170px" ng-options="item.id as item.name for item in menu_planning.groups" ng-model="keysearch.group_id" ng-change="onChange_keysearchGroup(keysearch.group_id)">
				    	<option value="">--- Chọn nhóm trẻ ---</option>
				    </select>
				    					    						
				    				</form>
								
				<div class="dropdown dropdown-setting">
					<button type="button" class="btn btn-link dropdown-toggle" ng-model="menu_planning.showDelete" data-toggle="dropdown" aria-expanded="false">
						<i class="fa fa-cog fa-spin icon-setting"></i>
					</button>
					<ul class="dropdown-menu dropdown-menu-right">
						<li>
							<input type="checkbox" title="Hiện thực đơn đã xóa" onclick="change_status()" ng-click="reloadDatagrid()" class="ng-pristine ng-untouched ng-valid ng-empty"> Hiện thực đơn đã xóa
						</li>
											</ul>
				</div>

				<div class="btn-group">
					<button type="button" id="show_restore" ng-click="restore()" class="btn btn-primary hidden">
						<span class="fa fa-undo"></span>Khôi phục
					</button>
				</div>

				<div class="btn-group fR" id="hidden_show_remove">
				  	<button type="button" ng-click="addForm(true)" class="btn btn-primary">
				  		<span class="glyphicon glyphicon-plus"></span>Thêm mới
				  	</button>
				  	<button type="button" class="btn btn-primary" ng-click="editForm()">
				  		<span class="glyphicon glyphicon-edit"></span>Sửa
				  	</button>
				  	<button type="button" class="btn btn-primary" onclick="$.menu_planning.del()">
				  		<span class="glyphicon glyphicon-remove-circle"></span>Xóa
				  	</button>
					  <button type="button" class="btn btn-primary action" onclick="$.menu_planning.share2()">
						<span class="glyphicon glyphicon-share-alt"></span>Chia sẻ
					</button>
									</div>

				<div class="btn-group">
					<button type="button" ng-click="library.formMenu_planning()" class="btn btn-primary">
						<span class="glyphicon glyphicon-eya"></span>Thư viện thực đơn mẫu
					</button>
				</div>


				
											</div>
			 <div class="support-video">
                <a target="_blank" href="https://www.youtube.com/watch?v=oLtg7HXnnjs">
                    <img src="http://localhost:3000/images/icon_hotro1.gif">
                </a>
            </div>
		</div>
	</div>
	
	<div id="tbl_menu_planning"></div>
	<input hidden id="status" value="false">
</div>
<!-- <div helper-config="test"></div> -->
<script src="http://localhost:3000/js/dinhduong/balance_money.js"></script>
<script src="http://localhost:3000/js/dinhduong/balance.js?_=376"></script>
<script src="http://localhost:3000/js/dinhduong/menu_planning.js?_=376"></script>
<script type="text/javascript">
	$.menu_planning.init();

	function change_status()
	{
		var status = document.getElementById('status');
		if(status.value == "true")
		{
			status.value = "false"
			document.getElementById('hidden_show_remove').classList.remove("hidden")
			document.getElementById('show_restore').classList.add("hidden")
		}
		else
		{
			status.value = "true";
			document.getElementById('hidden_show_remove').classList.add("hidden")
			document.getElementById('show_restore').classList.remove("hidden")
		}
	}

</script>
<style type="text/css">

.save_together_price-disable .uncheck, .save_together_price-disable .uncheck input{
	display: none;
}
#frm-storage{
	width: 60%;
	float: left;
	margin-top: 10px;
	margin-bottom: 4px;
}
.support-video{
	width: 120px;
    position: fixed;
    bottom: 4px;
    right: 0px;
    z-index: 6666;
}
</style>

