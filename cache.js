const express = require("express");
const { sha256, sha224 } = require("js-sha256");
const axios = require("axios").default;
const { wrapper } = require("axios-cookiejar-support");
const tough = require("tough-cookie");
const fs = require("fs");
const path = require("path");
const mkdirp = require("mkdirp");
const app = express();
const PORT = 3000;
const BASE_DOMAIN = "https://qlmn.vn";
const LOCAL_HOST = `http://localhost:${PORT}`;

// Dùng để xử lý dữ liệu đầu vào
app.use(express.json());
app.use(express.urlencoded({ extended: true }));


// Regex định nghĩa kiểu file
const CONTENT_EXT_MAP = {
  "text/html": "html",
  "text/css": "css",
  "application/javascript": "js",
  "application/json": "json",
  "image/png": "png",
  "image/jpeg": "jpg",
  "image/svg+xml": "svg",
  "font/woff2": "woff2",
  "font/woff": "woff",
  "font/ttf": "ttf",
};



// Đọc từ cache nếu tồn tại
function readFromCache(filepath) {
  if (fs.existsSync(filepath)) {
    return fs.readFileSync(filepath);
  }
  return null;
}

app.all("*path", async (req, res) => {
  const method = req.method;
  const fullPath = req.originalUrl;

  const cleanPath = fullPath.replace(/[?#].*$/, "");

  const domainDir = path.join(__dirname, "cache", new URL(BASE_DOMAIN).hostname);
  let savePath;

  // Xác định đường dẫn lưu cache
  if (method === "GET") {
    savePath = cleanPath == "/" ? path.join(domainDir,"index.html"):path.join(domainDir, cleanPath+ (cleanPath.lastIndexOf(".")>-1? "":".html"));
     
  } else {
    const methodDir = method.toLowerCase();
    const shabody = req.body? sha256(JSON.stringify(req.body)):"";
    savePath = path.join(domainDir, cleanPath, `${methodDir}-${shabody}.json`);
  }
 
  try {
     // Kiểm tra cache
  const cached = readFromCache(savePath);
  if (cached) {
    const ext = path.extname(savePath).slice(1);
    const mime = Object.entries(CONTENT_EXT_MAP).find(([, v]) => v === ext)?.[0] || "text/plain";
    res.set("Content-Type", mime);
    if(method == "GET") {
       return res.send(cached);
    } 
    
    let jsonCache = JSON.parse(cached.toString("utf-8"));
    let response = jsonCache.response;
    let dataBuffer = Buffer.from(response.body,"base64");
    return res.status(response.status).set(response.headers).send(dataBuffer);
  }
  return res.status(404).end();
  } catch (err) {
    res.status(500).send("Proxy error: " + err.message);
  }
});


// Khởi động server
app.listen(PORT, () => {
  console.log("Đang chạy" + PORT);
});
