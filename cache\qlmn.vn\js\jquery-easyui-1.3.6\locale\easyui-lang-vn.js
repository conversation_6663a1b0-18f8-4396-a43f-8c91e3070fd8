if ($.fn.pagination){
	$.fn.pagination.defaults.beforePageText = 'Trang';
	$.fn.pagination.defaults.afterPageText = '/ {pages}';
	$.fn.pagination.defaults.displayMsg = '<PERSON><PERSON><PERSON> thị từ {from} tới {to} trong tổng số {total} bản ghi';
}
if ($.fn.datagrid){
	$.fn.datagrid.defaults.loadMsg = 'Đang xử lý, xin chờ trong giây lát ...';
}
if ($.fn.treegrid && $.fn.datagrid){
	$.fn.treegrid.defaults.loadMsg = $.fn.datagrid.defaults.loadMsg;
}
if ($.messager){
	$.messager.defaults.ok = 'Đồng ý';
	$.messager.defaults.cancel = 'Hủy bỏ';
}
if ($.fn.validatebox){
	$.fn.validatebox.defaults.missingMessage = 'Trường này không được để trống.';
	$.fn.validatebox.defaults.rules.email.message = '<PERSON><PERSON><PERSON> nhập đúng định dạng email.';
	$.fn.validatebox.defaults.rules.url.message = 'Hãy nhập đúng định dạng URL.';
	$.fn.validatebox.defaults.rules.length.message = 'Chỉ được nhập giá trị trong khoảng giữa {0} và {1}.';
	$.fn.validatebox.defaults.rules.remote.message = 'Please fix this field.';
}
if ($.fn.numberbox){
	$.fn.numberbox.defaults.missingMessage = 'Trường này không được để trống.';
}
if ($.fn.combobox){
	$.fn.combobox.defaults.missingMessage = 'Trường này không được để trống.';
}
if ($.fn.combotree){
	$.fn.combotree.defaults.missingMessage = 'Trường này không được để trống.';
}
if ($.fn.combogrid){
	$.fn.combogrid.defaults.missingMessage = 'Trường này không được để trống.';
}
if ($.fn.calendar){
	$.fn.calendar.defaults.weeks = ['CN','T2','T3','T4','T5','T6','T7'];
	$.fn.calendar.defaults.months = ['Tháng 1 -', 'Tháng 2 -', 'Tháng 3 -', 'Tháng 4 -', 'Tháng 5 -', 'Tháng 6 -', 'Tháng 7 -', 'Tháng 8 -', 'Tháng 9 -', 'Tháng 10 -', 'Tháng 11 -', 'Tháng 12 -'];
}
if ($.fn.datebox){
	$.fn.datebox.defaults.currentText = 'Hôm nay';
	$.fn.datebox.defaults.closeText = 'Đóng lại';
	$.fn.datebox.defaults.okText = 'Đồng ý';
	$.fn.datebox.defaults.missingMessage = 'Trường này không được để trống.';
}
if ($.fn.datetimebox && $.fn.datebox){
	$.extend($.fn.datetimebox.defaults,{
		currentText: $.fn.datebox.defaults.currentText,
		closeText: $.fn.datebox.defaults.closeText,
		okText: $.fn.datebox.defaults.okText,
		missingMessage: $.fn.datebox.defaults.missingMessage
	});
}
