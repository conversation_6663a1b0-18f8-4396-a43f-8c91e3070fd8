{"name": "js-sha256", "version": "0.11.1", "description": "A simple SHA-256 / SHA-224 hash function for JavaScript supports UTF-8 encoding.", "main": "src/sha256.js", "devDependencies": {"expect.js": "~0.3.1", "mocha": "~10.8.2", "nyc": "^15.1.0", "tiny-worker": "^2.3.0", "uglify-js": "^3.1.9"}, "scripts": {"test": "nyc mocha tests/node-test.js", "report": "nyc --reporter=html --reporter=text mocha tests/node-test.js", "coveralls": "nyc report --reporter=text-lcov | coveralls", "build": "uglifyjs src/sha256.js -c -m eval --comments -o build/sha256.min.js"}, "repository": {"type": "git", "url": "https://github.com/emn178/js-sha256.git"}, "keywords": ["sha", "sha2", "sha224", "sha256", "hash", "encryption", "cryptography", "HMAC"], "license": "MIT", "author": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/emn178/js-sha256", "bugs": {"url": "https://github.com/emn178/js-sha256/issues"}, "nyc": {"exclude": ["tests"]}, "browser": {"crypto": false, "buffer": false}}