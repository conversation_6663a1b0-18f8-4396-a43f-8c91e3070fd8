angular_app.controller('mainContentController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', '$templateCache', function ($scope, $routeParams, $compile, MyCache, $filter, $cookies, $templateCache) {
    /*<PERSON><PERSON> báo mẫu định mức số phần*/
    $scope.dinhmuc = {
        'ngucoc': {name: '<PERSON><PERSON> cốc', value: 3},
        'rau': {name: '<PERSON><PERSON>', value: 7},
        'traicay': {name: '<PERSON><PERSON><PERSON><PERSON> cây', value: 2},
        'damdv': {name: '<PERSON><PERSON><PERSON> ĐV', value: 7}
    };

    $scope.sys = {
        truong: {
            lists: [],
            selected: undefined
        },
        phong: {
            lists: [],
            selected: undefined
        },
        configs: {}
    };

    $scope.note_bienlai = ''

    $scope.menu = {
        select: {},
        page: 'index',
        templates: {
            top: {
                index: 'menu_top_index.htm',
                // index: $CFG.remote.base_url+'/dinhduong/index/menu_top/index',
                children: 'menu_top_children.htm'
                // children: $CFG.remote.base_url+'/dinhduong/index/menu_top/children'
            },
            bottom: {
                index: 'menu_bottom_index.htm',
                // index: $CFG.remote.base_url+'/dinhduong/index/menu_bottom/index',
                children: 'menu_bottom_children.htm'
                // children: $CFG.remote.base_url+'/dinhduong/index/menu_bottom/children'
            }
        },
        data: {
            top: {},
            bottom: {}
        }
    };
    $scope.co_cau = (type, inDay)=>{
		$CFG || ($CFG = {});
		inDay || (inDay = '');
        var cocau_chuan = {
            protein: 4, fat: 9, sugar: 4
        };
        var cocau_chinh = {
            protein: 4.1, fat: 9.3, sugar: 4.1
        };
        if (typeof type === 'undefined' || !type){
            type = $CFG.co_cau;
        }
        if (type!=='cocau_chuan') {
            return cocau_chinh;
        }
		if (inDay.toString().indexOf('/')>=0 && inDay.split('/').length >= 3 && $CFG.co_cau_from){
			if (dateparser($CFG.co_cau_from).getTime() <= dateparser(inDay).getTime()) {
				return cocau_chuan;
			}else{
				return cocau_chinh;
			}
		}else if ($CFG.co_cau_from){
			return cocau_chuan;
		}
        return cocau_chuan;
    };
    $scope.sys.phong.onSelected = function (old) {
        var id = 0;
        if ($scope.sys.phong.selected) {
            id = $scope.sys.phong.selected.id;
        }
        if (id != old.id) {
            process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
                async: true,
                id: id,
                project_id: 2
            }, function (resp) {
                $scope.$apply(function () {
                    $scope.sys.truong.lists = resp;
                });
            }, function () {
                $('#select-school').combobox('clear');
            }, false);
        }
    };
    $scope.sys.truong.onSelected = function (old) {
        var truong = $scope.sys.truong.selected;
        var id = '';
        if (truong) {
            id = truong.id;
        }
        if (id && id != old.id) {
            process($CFG.remote.base_url + '/doing/admin/unit/setTruong', {
                async: true,
                id: id,
                project_id: 2
            }, function (resp) {
                if (resp.result == 'success') {
                    location.reload();
                }
            }, function () {
                $('#select-school').combobox('clear');
            });
        }
    };
    // $scope.getGroupMenu = function($define) {
    //     $rs = {};
    //     if($define) {
    //         var kt = false;
    //         $.each($scope.menu.data.top, function(index,menus){
    //             for(var i in menus.children) {
    //                 var menu = menus.children[i];
    //                 if($define == menu.define){
    //                     kt = true;
    //                     $rs = menus;
    //                     $rs['module_name'] = menu.name;
    //                     return;
    //                 }
    //             }
    //         });
    //         if(!kt) {
    //             $.each($scope.menu.data.bottom, function(index,menus){
    //                 for(var i in menus.children) {
    //                     var menu = menus.children[i];
    //                     if($define == menu.define){
    //                         kt = true;
    //                         $rs = menus;
    //                         $rs['module_name'] = menu.name;
    //                         return;
    //                     }
    //                 }
    //             });
    //         }
    //     }
    //     return $rs;
    // }
    $scope.selected_menu_khauphandinhduong = function () {
        angular.forEach($scope.menu.data.top, function (menu, ind) {
            if (menu.icon == 'khau_phan_dinh_duong') {
                $scope.menu.selected = menu;
                setTimeout(function () {
                    $('li#danh_muc').click();
                })
            }
        })
    };
    /* Khởi tạo menu và cấu hình chung cho các báo cáo */
    $scope._initSys = function () {
        var data = $templateCache.get('sysConfigs');
        // var data = window.sysConfigs;
        if (data.rows[1]) {
            $scope.menu.data.top = data.rows[1];
            $scope.menu.selected = '';
            $.each($scope.menu.data.top, function (index, menu) {
                $scope.menu.selected = menu;
            });
        }
        if (data.rows[2]) {
            $scope.menu.data.bottom = data.rows[2];
        }
        $scope.sys.configs = data.configs;
        if (typeof(data.configs.check_show_milk)==="undefined") {
            $scope.sys.configs.check_show_milk = 1;
        }
        if (!$scope.sys.configs.fee_report_phieuthuthanhtoan_header) {
            $scope.sys.configs.fee_report_phieuthuthanhtoan_header  =  'Phiếu thu thanh toán';
        }
        angular.element(document).ready(function () {
            var li0 = $(".li0").width() + 26;
            var li1 = $(".li1").width() + 26;
            var li2 = $(".li2").width() + 26;
            var li3 = $(".li3").width() + 26;
            var li4 = $(".li4").width() + 26;
            var width = (li0 + li1 + li2 + li3 + li4 + 40);
            if ($(window).width() < 1025) {
                width = width - 76;
            }
            if (width > 50) {
                width = width + 20;
                $(".nav-main-content").css("width", width + "px");
            }
        });
        $templateCache.put('menu', $scope.menu);
        $('#statusloading-mash').hide();
    };
    $scope.detailUserForm = function () {
        $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Thông tin chi tiết tài khoản',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_detail.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            }, function (resp) {
                $("#tbl_" + self.module).datagrid('reload');
            }
        );
    };
    $scope._initSys();
}])
    .run(function ($templateCache) {
        // $templateCache.put('menu_top_index.htm','Load menu');
        // $templateCache.put($CFG.remote.base_url+'/dinhduong/index/menu_top/children','Load menu');
        // $templateCache.put($CFG.remote.base_url+'/dinhduong/index/menu_bottom/index','Load menu');
        // $templateCache.put($CFG.remote.base_url+'/dinhduong/index/menu_bottom/children','Load menu');
        var overload = 0;

        function _initSys() {
            if (overload > 5) {
                return;
            }
            process($CFG.remote.base_url + '/doing/dinhduong/index/sysConfigs', {}, function (resp) {
                $templateCache.put('sysConfigs', resp);
            }, function () {
                overload++;
                _initSys();
            }, false, false, false);
        }

        _initSys();
    });