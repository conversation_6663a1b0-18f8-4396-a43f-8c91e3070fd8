<style type="text/css">
	input.mathucdon-editable{
		opacity: 0;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
	}
	input.mathucdon-editable:focus{
		opacity: 1;
	}
	.icon-setting {
	    color: blue;
	    margin-left: 3px;
	    cursor: pointer;
	    font-size: 19px;
	    margin-bottom: 4px;
	}
	#tuychonkhac {
		text-align: center;
		padding: 3px;
		border: 2px solid rgb(70, 142, 203);
		background: rgb(236, 241, 244);
	}
	.divider {
        height: 1px;
        margin: 5px 0;
        overflow: hidden;
        background-color: #ccc;
    }
</style>
<form class="form-horizontal" id="frm-add-menu_planning" role="form">
	<div class="layout-accordion" id="{{(collefthidden?'col-left-hidden':'')}}">
		<div class="col panel-group col-left col-md-{{!collefthidden?2:''}} col-sm-{{!collefthidden?2:''}}" style="{{!collefthidden?'position: relative;':''}}">
		    <div class="panel panel-default" style="border: none; padding: 0; padding-bottom: 50px;box-shadow: none">
		    	<div class="panel-heading" ng-init="" style="background: #fff;">
		    		<div ng-if="!collefthidden" class="form-group panel-button-top col-md-12" style="position: absolute; margin: 0; z-index: 1; top: 0">
						<div class="panel-content" style="padding: 3px 5px;color:black">
					      	<span href="#tuychonkhac" data-toggle="collapse" aria-expanded="true"
					      	class="fa fa-cog fa-spin icon-setting"></span>
							<div id="tuychonkhac" class="panel-collapse collapse">
								
							  								  	<!--div style="margin-top: 10px; text-align: left;">
							  		<label class="checkbox-inline" title="Tự động lưu hệ số thải bỏ vào thực phẩm trường khi chỉnh sửa.">
							  			<input type="checkbox" ng-model="auto_update_extrude_factor" ng-value-true="true" ng-value-false="false">
							  			Tự động lưu hệ số thải bỏ.
							  		</label>
							  	</div-->
							  	<div style="margin-top: 10px; text-align: left;">
								  	<label class="checkbox-inline" title="Cố định thực mua khi thay đổi số trẻ">
								  		<input style="" type="checkbox" id="market_before" ng-model="menu_planning.codinhthucmua" ng-value="1" title="Cố định thực mua"> Cố định thực mua
								  	</label>
								</div>
								<div class="footer-of-panel"></div>
								<div style="margin-top: 10px;" title="Làm tròn cột thực mua theo đơn vị tính">
									<div style="text-align: center;">Chi tiết thực phẩm</div>
							  		<button class="" style="background: #fff; border: 1px solid #9cacdc;" ng-click="detailByMeal.formShow(0)">
								  		<span class="" style="color: #ff7e00;"> Theo bữa</span>
								  	</button>
								  	<button class="" style="background: #fff; border: 1px solid #9cacdc;" ng-click="detailByMeal.formShow(1)">
								  		<span class="" style="color: #ff7e00;"> Theo món</span>
								  	</button>
							  	</div>
								<div class="footer-of-panel"></div>
								<div style="margin-top: 10px;" title="Sắp xếp thực phẩm kho liền nhau">
									<p>
										<a href="" ng-click="orderByExport()"> Nhóm thực phẩm Xuất kho - Đi chợ </a>
									</p>
							  	</div>
								<div style="margin-top: 10px; text-align: left;">
									<label class="checkbox-inline" style="text-align: left; margin-left: 0px;">
										<input type="checkbox" inf-configs="configs.co_cau_hidden" inf-id="co_cau_hidden" ng-true-value="1" ng-false-value="0">
										Ẩn cơ cấu áp dụng trong bảng TK TP dinh dưỡng
									</label><br/>
                                    <label class="checkbox-inline" style="text-align: left; margin-left: 0px;">
                                        <input type="checkbox" inf-configs="sys.configs.tdm_show_luong_mua_1tre" inf-id="tdm_show_luong_mua_1tre" ng-true-value="1"
                                               ng-false-value="0">
                                        Hiển thị cột "Lượng mua 1 trẻ (g)"
                                    </label>
                                    <div ng-if="$CFG.school_level>=0" style="text-align: left;padding-left: 3px"
                                     title="Hiển thị đánh giá về chất cho cấp TH, THCS, THPT">
	                                    <label class="checkbox-inline">
	                                        <input type="checkbox" inf-configs="sys.configs.cdkp_hide_dgchat_cap123" inf-id="cdkp_hide_dgchat_cap123" ng-true-value="1"
	                                               ng-false-value="0">
	                                        Ẩn nhãn "Đánh giá về chất & lượng" trong CĐKP
	                                    </label>
	                                </div>
									<label class="checkbox-inline" style="text-align: left; margin-left: 0px;">
										<input type="checkbox" inf-configs="sys.configs.tmd_old_not_check_inventory"
											   inf-id="tmd_old_not_check_inventory" ng-true-value="1"
											   ng-false-value="0">
										Giữ nguyên giá thực phẩm kho đã lưu ở thực đơn mẫu cũ!
									</label>
									<label class="checkbox-inline" style="text-align: left; margin-left: 0px;">
										<input type="checkbox" inf-configs="configs.hien_thi_dinh_muc_buasangxe" inf-id="hien_thi_dinh_muc_buasangxe" ng-true-value="1" ng-false-value="0">
										Hiển thị định mức bữa sáng, bữa xế để tham khảo?
									</label>
									<div style="text-align: left;padding-left: 3px">
                                    <label class="checkbox-inline" style="text-align: left;">
                                        <input type="checkbox" inf-configs="sys.configs.cdkp_is_show_plg_rank" inf-id="cdkp_is_show_plg_rank" ng-true-value="1" ng-false-value="0">
                                        Hiển thị "Cơ cấu áp dụng" theo khoảng giá trị
                                    </label>
                                </div>
									<label class="checkbox-inline" style="text-align: left; margin-left: 0px;">
                                        <input type="checkbox" inf-configs="configs.cdkp_re_sorting_dish" inf-id="cdkp_re_sorting_dish" ng-true-value="1" ng-false-value="0">
                                        Cập nhật lại thứ tự món ăn đã lưu trong bữa ăn (lưu sau xếp dưới)
                                    </label>
							  	</div>
							  	<div role="separator" class="divider"></div>
                                <div style="text-align: left;padding-left: 3px; color:orange;">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" inf-configs="sys.configs.cdkp_auto_update_to_tp_truong" inf-id="cdkp_auto_update_to_tp_truong" ng-true-value="1" ng-false-value="0">
                                        Tự động lưu Hệ số thải bỏ, Quy đổi gam về thực phẩm trường khi chỉnh sửa
                                    </label>
                                </div>
								<div role="separator" class="divider"></div>
                                <div class="tAl">
                                    <button class="btn btn-primary" style="color: #203e00; margin: 0 10px" ng-click="onApplyPrice()"
                                            title="Giá thực phẩm đi chợ theo thực phẩm trường">Áp dụng
                                    </button>
                                    Giá TP đi chợ theo TP Trường
                                </div>
                                <div ng-if="$CFG.administrator==2" style="text-align: left;padding-left: 3px; color:red;">
                                     <label class="checkbox-inline">
                                        <input type="checkbox" inf-configs="sys.configs.tdm_check_error_quantity_edit" inf-id="tdm_check_error_quantity_edit" ng-true-value="1" ng-false-value="0">
                                        Kiểm tra & xử lý t/h lượng 1 trẻ TĐM lưu chưa chính xác?
                                    </label>
                                </div>
							</div>
						</div>
					</div>
		      		<h4 class="panel-title" ng-click="collefthidden=!collefthidden" style="text-align: center; color: #7e7ece;">
						<div href="" data-toggle="collapse" aria-expanded="true">
							<span class="glyphicon glyphicon-chevron-{{(collefthidden?'right':'left')}}" style="margin-right: -10px;z-index: 2"> </span>
							<span class="glyphicon glyphicon-chevron-{{(collefthidden?'right':'left')}}" style="z-index: 2"> </span>
						</div>
					</h4>
				</div>
				<div ng-if="!collefthidden" class="panel-content">
			      	<div class="panel-heading" ng-init="thongtinthucdon=true" style="height: 28px;">
			      		<h4 class="panel-title" ng-click="thongtinthucdon=!thongtinthucdon" style="padding: 7px;">
							<div href="#thongtinthucdon" data-toggle="collapse" aria-expanded="true">Thông tin thực đơn
								<span class="glyphicon glyphicon-chevron-{{(thongtinthucdon?'up':'down')}}" style="float: right;"> </span>
							</div>
						</h4>
					</div>
					<div id="thongtinthucdon" class="panel-collapse collapse in">
						<!-- <div class="form-group" style="margin-bottom: 0px; padding-top: 10px" ng-if="isMealsEmpty()&&false">
							<a href="#" class="dropdown-toggle" role="button" ng-click="importAllFoodInDay()">
								Nhập thực phẩm cho cả ngày
							</a>
						</div> -->
						<div class="form-group" ng-repeat="(ind, meal) in menu_informations" style="margin-bottom: 5px;">
							<div class="ba-header">
								<h4 class="col-md-12 title-bua-an navbar-right" style="margin-right: 0;">
									<text ng-bind="meal.name" style="padding-top: 3px;float: left;"
										  title="Kho: {{menu_planning.meals[meal.define].warehouse_id}}"></text>
									<div class="dropdown" style="float: right; padding-left: 35px;">
										<a href="#" class="dropdown-toggle" data-toggle="dropdown"
										   role="button" aria-haspopup="true" aria-expanded="false"
										   title="Bấm trái chuột chọn món ăn hoặc thực phẩm">
											<span class="dropbtn glyphicon glyphicon-plus btn-over-red btn-color-blue"></span>
										</a>
								        <ul class="dropdown-menu">
								            <li>
												<a href="" ng-click="showAddDish(menu_planning.meals[meal.define]);" title="Thêm món ăn">
													Thêm món ăn
												</a>
											</li>
								            <li><a href="" ng-click="showAddFood(menu_planning.meals[meal.define]);" >Thêm thực phẩm</a></li>
								        </ul>
									</div>
								</h4>
							</div>
							<div class="food-list col-md-12 no-padding-right no-padding-left">
								<ul class="food-of-meal">
									<li ng-repeat="(id,dish) in arrayWrap(menu_planning.meals[meal.define].dishes)">
										<span ng-bind="dish.name">Tên món ăn</span>
										<input ng-model="dish.name" class="mathucdon-editable" style="height: 22px"></input>
										<div class="panel-button" style="width:50px">
											<span class="glyphicon glyphicon-transfer btn-over-red btn-color-gray"
												  title="Chuyển bữa cho món ăn"
											ng-click="showChangeMeal(menu_planning.meals[meal.define],dish)"
												  ng-if="true || (id+'').split('food_id').length==1"></span>
											<span class="glyphicon glyphicon-info-sign btn-over-red btn-color-gray"
												  title="Xem chi tiết món ăn" ng-click="showDish(menu_planning.meals[meal.define],dish)"
												  ng-if="true || (id+'').split('food_id').length==1"></span>
											<span class="glyphicon glyphicon-trash btn-over-red btn-color-gray"
												  title="Xóa món ăn" ng-click="delDish(menu_planning.meals[meal.define],dish)"></span>
										</div>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div class="footer-of-panel"></div>
				</div>
				<div ng-if="!collefthidden" class="container-balance-left">
					<div class="form-group panel-button-top col-md-12" align="center" style="padding: 5px;">
						<table>
							<tfoot>
								<tr ng-if="!sys.configs.cdkp_hide_dgchat_cap123">
									<td colspan="1" style="padding: 7px 3px">Đánh giá về lượng: </td>
									<td colspan="1" class="{{caloRateBind().class}}" style="font-weight: bold;" ng-if="!selected.group.is_tt28_old">
										<span class="glyphicon glyphicon-info-sign btn-over-red btn-color-gray" title="Xem chi tiết" ng-click="showMealsCaloInfo()"></span>
										<text ng-bind="caloRateBind().text"></text>
									</td>
									<td colspan="1" style="font-weight: bold;" ng-if="selected.group.is_tt28_old">
										<span class="glyphicon glyphicon-info-sign btn-over-red btn-color-gray" title="Xem chi tiết" ng-click="showMealsCaloInfo()"></span>
										<text class="color-green" ng-if="tong.calo>getNormSelected().biggest_rate">Vượt quá định mức</text>
										<text class="color-red" ng-if="tong.calo<getNormSelected().smallest_rate">Chưa đạt</text>
										<text class="" ng-if="tong.calo<=getNormSelected().biggest_rate&&tong.calo>=getNormSelected().smallest_rate">Đạt</text>
									</td>
								</tr>
								<tr ng-if="!sys.configs.cdkp_hide_dgchat_cap123">
									<td colspan="1" style="padding: 7px 3px;">Đánh giá về chất: </td>
									<td colspan="1" class="{{plgRateBind().class}}" style="font-weight: bold;">
										<span ng-click="showPartPLGInfo()">
											<span class="glyphicon glyphicon-info-sign btn-over-red color-gray" title="Xem chi tiết"></span>
										</span>
										<text ng-bind="plgRateBind().text" ng-if="!sys.configs.cdkp_hide_dgchat_cap123"></text>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
					<div class="form-group panel-button-top col-md-12" align="center">
						<button class="btn-balance-auto bg-color-orange" ng-click="balance.show('menu_planning')">
					  		<span class="glyphicon glyphicon-dashboard"></span> Cân đối thực đơn
					  	</button>
					</div>
					
					<!-- Bảng tỉ lệ calo từng bữa -->
					<!-- <div>
						<table class="table tbl-danhgiabuaan">
					        <thead>
					        	<tr>
					        		<th colspan="3" style="text-align: center;">Đánh giá calo từng bữa</th>
					        	</tr>
					            <tr>
					                <th colspan="1" width="120" style="text-align: center;">Bữa ăn</th>
					                <th colspan="1" style="text-align: center;">Bữa(%)</th>
					                <th colspan="1" style="text-align: center;">Định Mức(%)</th>
					            </tr>
					        </thead>
					        <tbody>
					            <tr ng-repeat="(index,meal) in menu_planning.meals" ng-hide="meal.define == 'buasang' && count(menu_planning.meals.buasang.dishes)==0">
					                <td ng-bind="meal.name" style="text-align: center;"></td>
					                <td align="right" title="{{getColorForTableCalo(meal).title}}" class="{{getColorForTableCalo(meal).class}}">
					                    <text ng-bind="round(sumCaloMeal(meal)/selected.group.nutritions.calo*100,2)"></text>
					                </td>
					                <td ng-bind="selected.group.meals[meal.define].min+' - '+selected.group.meals[meal.define].max" align="center"></td>
					            </tr>
					        </tbody>
					        <tfoot>
					            <tr>
					                <td colspan="4" align="right">
					                    <i class="fa fa-square color-red" style="margin-left: 15px;"></i> <text>Chưa đạt</text>
					                    <i class="fa fa-square color-green" style="margin-left: 15px;"></i> <text>Vượt quá định mức</text>
					                </td>
					            </tr>
					        </tfoot>
					    </table>
					</div> -->
				</div>
				<!-- <div>
					{{menu_planning.tmps}}
				</div>
				<div>
					{{menu_planning.tmp_norm}}
				</div>
				<div>
					{{menu_planning.tmp_meal}}
				</div>
				<div>
					{{menu_planning.tmp_quantity}}
				</div> -->
			</div>
		</div>
		<div class="col col-right scroll-container col-md-{{!collefthidden?10:12}} col-sm-{{!collefthidden?10:12}} no-padding-left no-padding-right" ng-style="menu_planning.initStyleContainerRight()">
			<table class="tbl-thongtinthucdon">
				<tbody>
					<tr>
						<td align="right">Nhóm trẻ</td>
						<td>
							<select class="" name="nhomtre" id="nhomtre" style="width: 170px" ng-options="item.id as item.name for item in menu_planning.groups" ng-model="row.group_id" ng-change="onChange_group()"></select>
						</td>

						<td align="right" style="position: relative;">
							Số trẻ
						</td>
						<td style="position: relative;" class="content-sotre">
							<input class="form-control color-green" id="sotre" type-number="int" min="0" num-abs="true"
                                   ng-model="row.sotre" placeholder="Số trẻ" ng-blur="onChange_Sotre(row)"
                                   ng-focus="onFocus_sotre(row)" style="width: 120px;" >
						</td>
						<td align="right">Tiền 1 trẻ</td>
						<td style="position: relative;">
							<span class="container-field-measure">
								<input class="form-control color-green" type-number="int" num-abs="true" style="width: 130px;position: absolute; z-index: 1;"
                                       ng-value="digit_grouping(row.tien1tre)" placeholder="Tiền 1 trẻ">
								<input class="form-control color-green input-unfocus-hidden" style="width: 130px;z-index: 2;"
                                       type-number="int" num-abs="true" min="0" id="tien1tre" ng-model="row.tien1tre" placeholder="Tiền 1 trẻ" ng-change="onChange_tien1tre()">
								<label class="measure color-green" style="z-index: 3;">VNĐ</label>
							</span>
						</td>
						<td>
							<div >	<!-- Các nút cho tạo mới -->
																<button ng-if="menu_planning.form_add" class="btn btn-primary" id="btn-save" ng-click="addAction();">
									<span class="bootstrap-dialog-button-icon glyphicon glyphicon-floppy-disk"></span>
									Lưu
								</button>
								<button ng-if="!menu_planning.form_add" class="btn btn-primary" id="btn-save" ng-click="editAction();">
									<span class="bootstrap-dialog-button-icon glyphicon glyphicon-floppy-disk"></span>
									Lưu
								</button>
																<button ng-if="!menu_planning.form_add" class="btn btn-primary" ng-click="openDialogPrint()">
									<span class="bootstrap-dialog-button-icon glyphicon glyphicon-print"></span>
									In
								</button>
							</div>
						</td>
					</tr>
					<tr>
						<td align="right">Tên thực đơn</td>
						<td>
							<input class="form-control" id="mathucdon" ng-model="row.mathucdon" placeholder="Tên thực đơn" style="width:170px;">
						</td>
						<td align="right">
							Tiền dịch vụ
							<span class="glyphicon glyphicon-pencil color-blue btn-over-red" ng-click="editServicePrice(row.group_id)" title="Sửa tiền dịch vụ"></span>
						</td>
						<td>
							<span class="container-field-measure">
								<input class="form-control color-green" style="width: 120px; position: absolute; z-index: 1;" ng-value="digit_grouping(getTiendichvu(row.group_id))" placeholder="Tiền 1 trẻ" disabled="">
								<input class="form-control color-green input-unfocus-hidden" ng-value="digit_grouping(getTiendichvu(row.group_id))" style="width: 120px;z-index: 2;" disabled="true">
								<label class="measure" style="z-index: 3;">VNĐ</label>
							</span>
						</td>
						<td align="right">Tổng tiền</td>
						<td>
							<span class="container-field-measure">
								<input class="form-control" style="width: 130px;" ng-value="digit_grouping((row.sotre|parseFloat)*(row.tien1tre|parseFloat))" placeholder="Tổng tiền" disabled="">
								<label class="measure">VNĐ</label>
							</span>
						</td>
						<td>
																				</td>
					</tr>
					<tr>
						<td align="right">Tiền chênh lệch</td>
						<td>
							<span class="container-field-measure">
								<input class="form-control" style="width: 150px;" ng-value="digit_grouping(round(row.tongtienchenhlech/row.sotre,2))" placeholder="Tiền chênh lệch" disabled="">
								<label class="measure">VNĐ</label>
							</span>
						</td>
						<td align="right">Tổng tiền chênh lệch</td>
						<td>
							<span class="container-field-measure">
								<input class="form-control" placeholder="Tổng tiền chênh lệch" style="width: 150px;" disabled="" ng-value="digit_grouping(round(row.tongtienchenhlech,1))">
								<label class="measure">VNĐ</label>
							</span>
						</td>
						<td colspan="2">
						</td>
						<td>
														<div ng-if="!menu_planning.form_add">	<!-- Các nút cho chỉnh sửa -->
								<button class="btn btn-primary" id="btn-save" ng-click="addForm();">
									<span class="bootstrap-dialog-button-icon glyphicon glyphicon-duplicate"></span>
									Tạo bản sao
								</button>
							</div>
													</td>
					</tr>
				</tbody>
			</table>
			<div class="container-table">
				<table class="table food-lists">
					<thead class="header" style="">
						<tr class="row-head row-head-title">
							<th rowspan="2" class="col-head col-1" id="field-stt" style="width: 40px;">
								<div div class="cell"></div>
							</th>
							<th rowspan="2" class="col-head col-1" id="field-food-id" style="width: 60px;">
								<div div class="cell">Mã TP</div>
							</th>
							<th rowspan="2" class="col-head col-2 col-name" id="field-name" style="width: 220px">
								<span ng-click="balance.alertData(menu_planning.meals)">Tên thực phẩm</span><br/>
								<label class="checkbox-inline">
									<input type="checkbox" ng-model="food_detail" ng-value-true="true" ng-value-false="false">
									Hiện chi tiết thực phẩm
								</label>
							</th>
							<th rowspan="2" class="color-blue">Lượng ăn 1 trẻ (g)</th>
							<!-- <th rowspan="2" class="color-blue">Lượng 1 trẻ (g)</th> -->
							<th rowspan="2" ng-if="food_detail">Đơn giá (vnđ/kg)</th>
							<!-- Begin Options -->
							<th ng-if="food_detail" colspan="2" class="color-blue">Đạm (g)</th>
							<th ng-if="food_detail" colspan="2" class="color-blue">Béo</th>
							<th ng-if="food_detail" rowspan="2" class="color-blue">Đường</th>
							<th ng-if="food_detail" rowspan="2" class="color-blue">Calo</th>
							<!-- <th ng-if="food_detail" rowspan="2" class="color-blue">Tiền (đ)</th> -->
							<!-- End Options -->
							<th rowspan="2" class="color-blue">Lượng ăn <span ng-bind="row.sotre"></span> trẻ (kg)</th>
							<!-- <th rowspan="2" class="color-blue">Thực ăn 1 nhóm (kg)</th> -->
							<th rowspan="2">Hệ số thải bỏ (%)</th>
							<th rowspan="2" ng-if="sys.configs.tdm_show_luong_mua_1tre" class="color-blue">Lượng mua 1 trẻ (g)</th>
							<th rowspan="2" class="color-blue">Lượng mua <span ng-bind="row.sotre"></span> trẻ (kg)</th>
							<!-- <th rowspan="2" class="color-blue">Thực mua 1 nhóm (kg)</th> -->
							<th rowspan="2" class="color-blue">Lượng mua <span ng-bind="row.sotre"></span> trẻ theo ĐVT
								<span class="glyphicon glyphicon-sort-by-attributes-alt icon-setting" style="color: #ff7e00;"
								title="Làm tròn thực mua" ng-click="roundBuy()"></span></th>
							<th rowspan="2">ĐVT</th>
							<th rowspan="2">Quy đổi ra gam</th>
							<th rowspan="2" class="color-blue" ng-if="food_detail">Lượng mua 1 trẻ theo ĐVT</th>
							<th rowspan="2">Đơn giá theo ĐVT</th>
							<th rowspan="2">Tiền ăn <span ng-bind="row.sotre"></span> trẻ (VNĐ) <span ng-bind="digit_grouping(round(row.thanhtien1nhom, 2))"></span></th>
                        	<th rowspan="2" style="width: 30px"></th>
						</tr>
						<tr ng-if="food_detail">
							<th>Động vật</th>
							<th>Thực vật</th>
							<th>Động vật</th>
							<th>Thực vật</th>
						</tr>
					</thead>
					<tbody ng-if="count(datagrid.data[meal_key])>0 || true" ng-repeat="(meal_key, foods) in datagrid.data" class="body-list">
						<tr id="title" ng-if="count(datagrid.data[meal_key])>0 && count(datagrid.data)>1">
							<td colspan="{{14+(food_detail==true?7:0)}}">
								<b><span class="warehouse-name" ng-bind="menu_planning.warehouses[meal_key].name_meal">Kho chiều/kho sáng</span></b>
							</td>
						</tr>
						<tr ng-repeat="food in foods | toArray:'name'" id="item">
							<td style="" title="getMenu_infomationByMeal_key(meal_key).warehouse_id">
								<span ng-bind="$index+1" style="width: 40px;">STT</span>
								<i ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id] != undefined" style="position: absolute; left: 0; width: 7px;height: 17px;" title="Dư: {{inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id].value-food.thucmuatheodvt}}({{getMeasure(food.measure_id).name}})">
									<i class="bg-color-inventory" ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id].value-food.thucmuatheodvt>food.min_inventory" style="position: absolute;width: 100%; height: 100%;top: 0;left: 0;" ng-bind="''">Còn trong kho</i>
									<i class="bg-color-inventory-warning" ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id].value-food.thucmuatheodvt<=food.min_inventory && inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id].value-food.thucmuatheodvt>0" style="position: absolute;width: 100%; height: 100%;top: 0;left: 0;" ng-bind="''">Sắp hết</i>
									<i class="bg-color-inventory-empty" ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id].value-food.thucmuatheodvt<=0" style="position: absolute;width: 100%; height: 100%;top: 0;left: 0;" ng-bind="''">Đã hết</i>
								</i>
							</td>
							<td>
								<span ng-bind="food.food_id">Mã TP</span>
							</td>
							<td class="cell-editable" id="field-name" style="position: relative; width: 220px; text-align: left;">
								<div style="position: relative;">
									<span ng-bind="food.name" class=""></span>
									<input ng-model="food.name" class="text-edit" ng-focus="food.name_old = food.name_old || food.name" ng-change="onChange_food_name(food)"
										   ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id] == undefined && configs.editFoodNamePermission"
                                           style="position: absolute;width: 100%; height: 100%; left: 0;">
									<span class="glyphicon glyphicon-info-sign color-orange" title="Tên gốc: {{food.name_old}}"
										  ng-if="food.name_old != undefined && food.name != food.name_old" style="float: right; margin-top: 3px;"></span>
								</div>
								<span ng-if="food_detail" style="position: absolute; right: 2px; top: 2px;"
									  class="glyphicon glyphicon-transfer btn-over-red btn-color-gray"
									  title="Đổi thực phẩm"
									  ng-click="changeFoodForm(food)"></span>
							</td>
							<td class="cell-edit cell-editable">
								<span ng-bind="digit_grouping(food.luong1tre)">Lượng 1 trẻ (g)</span>
								<input id="luong1tre_{{meal_key+'_'+$index}}" type-number="float" num-abs="true"
                                       ng-model="food.luong1tre" ng-change="onChange_luong1tre(food)"
                                       ng-keyup="keyUpFood($event,$index,warehouse_id,'luong1tre')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable" ng-if="food_detail">
								<span ng-bind="digit_grouping(food.price_kg)">Đơn giá (đ/kg)</span>
								<input ng-if="allowChangePriceWarehouse(meal_key, food.food_id)"
                                       id="price_kg_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.price_kg"
                                       ng-change="onChange_price_kg(datagrid.data,food)" ng-keyup="keyUpFood($event,$index,warehouse_id,'price_kg')" autocomplete="off">
							</td>
							<!-- Begin Options -->
							<td ng-if="food_detail">
								<span ng-if="food.is_meat" ng-bind="digit_grouping(round(food.luong1tre*food.nutritions.protein/100))">Đạm động vật</span>
							</td>
							<td ng-if="food_detail">
								<span ng-if="!food.is_meat" ng-bind="digit_grouping(round(food.luong1tre*food.nutritions.protein/100))">Đạm thực vật</span>
							</td>
							<td ng-if="food_detail">
								<span ng-if="food.is_meat" ng-bind="digit_grouping(round(food.luong1tre*food.nutritions.fat/100))">Béo động vật</span>
							</td>
							<td ng-if="food_detail">
								<span ng-if="!food.is_meat" ng-bind="digit_grouping(round(food.luong1tre*food.nutritions.fat/100))">Béo thực vật</span>
							</td>
							<td ng-if="food_detail">
								<span ng-bind="digit_grouping(round(food.luong1tre*food.nutritions.sugar/100))">Đường</span>
							</td>
							<td ng-if="food_detail">
								<span ng-bind="digit_grouping(round(getCalo(food)))">Calo</span>
							</td>
							<!-- <td ng-if="food_detail">
								<span ng-bind="digit_grouping(round(food.tiendicho1tre))"></span>
							</td> -->
							<!-- End Options -->
							<td class="cell-edit cell-editable">
								<span ng-bind="digit_grouping(food.thucan1nhom)">Thực ăn 1 nhóm (kg)</span>
								<input id="thucan1nhom_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.thucan1nhom"
									   ng-change="onChange_thucan1nhom(food)" ng-keyup="keyUpFood($event,$index,meal_key,'thucan1nhom')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable" style="width: 50px;">
								<span ng-bind="food.extrude_factor">Hệ số thải bỏ (%)</span>
								<input ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id]==undefined"
									   id="extrude_factor_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.extrude_factor"
									   ng-change="onChange_extrude_factor(food)" ng-blur="onBlur_extrude_factor(food)"
									   ng-keyup="keyUpFood($event,$index,meal_key,'extrude_factor')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable" ng-if="sys.configs.tdm_show_luong_mua_1tre">
								<span ng-bind="digit_grouping(food.thucmua1tre)">Lượng mua 1 trẻ (g)</span>
								<input id="thucmua1tre_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.thucmua1tre"
									   ng-change="onChange_thucmua1tre(food)" ng-keyup="keyUpFood($event,$index,meal_key,'thucmua1tre')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable">
								<span ng-bind="digit_grouping(food.thucmua1nhom)">Thực mua 1 nhóm (kg)</span>
								<input id="thucmua1nhom_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.thucmua1nhom"
									   ng-change="onChange_thucmua1nhom(food)" ng-keyup="keyUpFood($event,$index,meal_key,'thucmua1nhom')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable">
								<span ng-bind="digit_grouping(food.thucmuatheodvt)">Thực mua theo ĐVT</span>
								<input id="thucmuatheodvt_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.thucmuatheodvt"
									   ng-change="onChange_thucmuatheodvt(food)" ng-keyup="keyUpFood($event,$index,meal_key,'thucmuatheodvt')" autocomplete="off">
							</td>
							<td>
								<span ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id]!=undefined" ng-bind="getMeasure(food.measure_id).name">ĐVT</span>
								<select ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id]==undefined"
										id="measure_id_{{meal_key+'_'+$index}}" ng-options="item.id as item.name for item in menu_planning.measures"
										ng-model="food.measure_id" style="width: 50px; border: 0px;" ng-change="onChange_measure(food)" ng-keyup="keyUpFood($event,$index,meal_key,'measure_id')"></select>
							</td>
							<td class="cell-edit cell-editable" style="width: 70px;">
								<span ng-bind="digit_grouping(food.gam_exchange)">Quy đổi ra gam</span>
								<input ng-if="inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id]==undefined"
									   id="gam_exchange_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.gam_exchange"
									   ng-change="onChange_gam_exchange(food)" ng-keyup="keyUpFood($event,$index,meal_key,'gam_exchange')"
									   ng-blur="gam_exchangeBlur(food)" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable" ng-if="food_detail">
								<span ng-bind="digit_grouping(food.thucmua1tretheodvt)">Thực mua 1 trẻ theo ĐVT</span>
								<input id="thucmua1tretheodvt_{{meal_key+'_'+$index}}" type-number="float" num-abs="true" ng-model="food.thucmua1tretheodvt"
									   ng-change="onChange_thucmua1tretheodvt(food)" ng-keyup="keyUpFood($event,$index,meal_key,'thucmua1tretheodvt')" autocomplete="off">
							</td>
							<td class="cell-edit cell-editable" ng-style="{background: (inventory[getMenu_infomationByMeal_key(meal_key).warehouse_id][food.food_id]== undefined)? '#fff' : '#f6f9f9'}">
								<span ng-bind="digit_grouping(food.price)">Đơn giá theo ĐVT</span>
								<input id="price_{{meal_key+'_'+$index}}"
                                       ng-if="allowChangePriceWarehouse(meal_key, food.food_id)"
                                       type-number="float" num-abs="true" ng-model="food.price" ng-change="onChange_price(datagrid.data,food)"
									   ng-keyup="keyUpFood($event,$index,meal_key, 'price')" autocomplete="off">
							</td>
							<td>
								<span ng-bind="digit_grouping(round(food.thanhtien1nhom))">Tổng tiền 1 nhóm (VNĐ)</span>
							</td>
							<td>
	                            <span class="glyphicon glyphicon-trash btn-over-red btn-color-gray" title="Xóa" ng-click="deleteDish(food.food_id, meal_key)"></span>
	                        </td>
						</tr>
					</tbody>
					<tfoot>
					</tfoot>
				</table>
			</div>
			<div class="form-group col-md-12 container-table" id="container-thongketpdd">
				<div class="table-title-thanhphandinhduong" ng-init="menu_planning.table_TKTPDD = true">
					<div style="float: left; margin-top: 3px;" ng-click="menu_planning.table_TKTPDD=!menu_planning.table_TKTPDD">
						<span class="glyphicon glyphicon-chevron-down color-green btn-over-red" ng-show="menu_planning.table_TKTPDD" title="Thu gọn bảng thống kê"></span>
						<span class="glyphicon glyphicon-chevron-up color-green btn-over-red" ng-show="!menu_planning.table_TKTPDD" title="Mở bảng thống kê"></span>
						<label class="control-label">Thống kê thành phần dinh dưỡng</label>
					</div>
					<div style="float: right;">
						<div class="panel-button-top col-md-12" align="center">
							<text><i class="fa fa-square color-inventory" title="Còn tồn kho"></i> Còn trong kho </text>
							<text title="Trong kho sắp hết">
								<i class="fa fa-square color-inventory-warning" style="margin-left: 5px;"></i> Sắp hết
							</text>
							<text title="Trong kho đã hết">
								<i class="fa fa-square color-inventory-empty" style="margin-left: 5px;"></i> Đã hết
							</text>
							<text><i class="fa fa-square color-gomarket" style="margin-left: 5px;" ></i> Đi chợ </text>
						</div>
					</div>
				</div>
				<table class="table thanhphandinhduong table-bordered" ng-show="menu_planning.table_TKTPDD">
					<thead>
						<tr class="row-head row-head-title">
							<th class="col-head col-1" rowspan="2" style="text-align:left; width: 190px;color: #7ac54b;">
								<div ng-repeat="(warehouse_id, meal) in row.meal_selection" ng-if="meal.visible">
									<label class="checkbox-inline">
										<input type="checkbox" ng-model="meal.selected" ng-true-value="true" ng-false-value="false" ng-change="totalCalculator()">
										<text ng-bind="meal.name"></text>
									</label>
								</div>
							</th>
							<th class="col-head col-1" colspan="2" style="min-width: 30px;">Đạm (g)</th>
							<th class="col-head col-1" colspan="2" style="min-width: 30px;">Béo (g)</th>
							<th class="col-head col-1" rowspan="2" style="min-width: 30px;">Đường (g)</th>
							<th class="col-head col-1" rowspan="2" style="min-width: 30px;">Calo (kCal)</th>
							<th class="col-head col-1" rowspan="2" style="min-width: 30px;">Tiền ăn 1 trẻ (VNĐ)</th>
							<!-- Tiêu đề bảng calo từng bữa -->
							<th colspan="4" style="background: #fff;">Đánh giá tỉ lệ calo từng bữa</th>
						</tr>
						<tr class="row-head row-head-title">
							<th class="col-head col-1">Động vật</th>
							<th class="col-head col-1">Thực vật</th>
							<th class="col-head col-1">Động vật</th>
							<th class="col-head col-1">Thực vật</th>
							<!-- Tiêu đề bảng calo từng bữa -->
							<th style="background: #fff;" style="width: 120px;">Bữa ăn</th>
							<th style="background: #fff;" style="width: 80px;">Tỉ lệ đạt (%)</th>
							<th style="background: #fff;" style="width: 100px;">Định mức (%)</th>
							<th style="background: #fff;" style="width: 80px;">Tỉ lệ kCal (%)</th>
						</tr>
					</thead>
					<tbody>
						<tr >
							<td class="col-body-title col-1"><div class="cell-label-container">Tổng cộng</div></td>
							<td><div class="cell-label-container" ng-bind="round(tong.animal_protein,2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(tong.vegetable_protein,2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(tong.animal_fat,2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(tong.vegetable_fat,2)"></div></td>
							<td><div class="cell-label-container" ng-bind="digit_grouping(round(tong.sugar,2))"></div></td>
							<td><div class="cell-label-container" ng-bind="digit_grouping(round(tong.calo,2))"></div></td>
							<td rowspan="{{ configs.co_cau_hidden?7:8 }}" style="vertical-align: middle;">
								<table style="width: 100%;">
									<tr style="border: 0px;">
										<td colspan="2" style="border: 0px;">
											<div class="cell-label-container" ng-bind="digit_grouping(round(row.thanhtien1nhom/row.sotre,2))"></div>
										</td>
									</tr>
									<tr style="border: 0px;">
										<td style="text-align: right; border: 0px;">Sáng:</td>
										<td style="text-align: left; border: 0px;">
											<span ng-bind="digit_grouping(round(getTien_an_sang()/row.sotre,2))"></span>
										</td>
									</tr>
									<tr style="border: 0px;">
										<td style="text-align: right; border: 0px;">Chính:</td>
										<td style="text-align: left; border: 0px;">
											<span ng-bind="digit_grouping(round(getTien_an_chinh()/row.sotre,2))"></span>
										</td>
									</tr>
								</table>
							</td>
							<!-- Bảng calo từng bữa (bữa sáng)-->
							<td ng-bind="menu_planning.meals[menu_informations[0].define].name" style="text-align: left; width: 130px;"></td>
			                <td align="right" style="text-align: center;">
			                	<span title="{{getColorForTableCalo(menu_planning.meals[menu_informations[0].define]).title}}" ng-class="{'color-red':(sumCaloMeal(menu_planning.meals[menu_informations[0].define])/selected.group.nutritions.calo*100<selected.group.meals[menu_informations[0].define].min),'color-green':(sumCaloMeal(menu_planning.meals[menu_informations[0].define])/selected.group.nutritions.calo*100>selected.group.meals[menu_informations[0].define].max)}" ng-if="selected.group.meals[menu_informations[0].define].selected">
			                    	<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[0].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[0].define].dishes)>0"></text>
			                	</span>
			                	<span ng-if="!selected.group.meals[menu_informations[0].define].selected" >
			                    	<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[0].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[0].define].dishes)>0"></text>
			                	</span>
			                </td>
			                <td align="center">
			                	<text ng-bind="selected.group.meals[menu_informations[0].define].min+' - '+selected.group.meals[menu_informations[0].define].max" ng-if="count(menu_planning.meals[menu_informations[0].define].dishes)>0 && (selected.group.meals[menu_informations[0].define].selected || configs['hien_thi_dinh_muc_buasangxe'])"></text>
			                </td>
			                <td align="center" ng-bind="digit_grouping(round(sumCaloMeal(menu_planning.meals[menu_informations[0].define])/tong.calo*100,0), '')"></td>
						</tr>
						<tr class="row-dinhmuc1ngay">
							<td class="col-body-title col-1"><div class="cell-label-container">Định mức một ngày</div></td>
							<td><div class="cell-label-container" ng-bind="selected.group.nutritions.animal_protein"></div></td>
							<td><div class="cell-label-container" ng-bind="selected.group.nutritions.vegetable_protein"></div></td>
							<td><div class="cell-label-container" ng-bind="selected.group.nutritions.animal_fat"></div></td>
							<td><div class="cell-label-container" ng-bind="selected.group.nutritions.vegetable_fat"></div></td>
							<td><div class="cell-label-container" ng-bind="digit_grouping(selected.group.nutritions.sugar)"></div></td>
							<td style="text-align: center;">
								<text ng-bind="(getNormSelected().smallest_rate)+' - '+(getNormSelected().biggest_rate)" ></text>
							</td>
							<!-- Bảng calo từng bữa (bữa trưa)-->
							<td style="text-align: left; width: 130px;background: #fff;">
								<span ng-bind="menu_planning.meals[menu_informations[1].define].name"></span>
							</td>
			                <td align="right" style="background: #fff;text-align: center;">
			                	<span title="{{getColorForTableCalo(menu_planning.meals.buatrua).title}}" ng-class="{'color-red':(sumCaloMeal(menu_planning.meals[menu_informations[1].define])/selected.group.nutritions.calo*100<selected.group.meals[menu_informations[1].define].min),'color-green':(sumCaloMeal(menu_planning.meals[menu_informations[1].define])/selected.group.nutritions.calo*100>selected.group.meals[menu_informations[1].define].max)}" ng-if="selected.group.meals[menu_informations[1].define].selected">
				                    <text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[1].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[1].define].dishes)>0"></text>
			                	</span>
			                	<span ng-if="!selected.group.meals[menu_informations[1].define].selected" >
				                    <text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[1].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[1].define].dishes)>0"></text>
			                	</span>
			                </td>
			                <td align="center" style="background: #fff;" >
			                	<text ng-bind="selected.group.meals[menu_informations[1].define].min+' - '+selected.group.meals[menu_informations[1].define].max" ng-if="count(menu_planning.meals[menu_informations[1].define].dishes)>0 && selected.group.meals[menu_informations[1].define].selected"></text>
			                </td>
			                <td align="center" ng-bind="digit_grouping(round(sumCaloMeal(menu_planning.meals[menu_informations[1].define])/tong.calo*100,0), '')" style="background: #fff;"></td>
						</tr>
						<tr >
							<td class="col-body-title col-1"><div class="cell-label-container">Tỷ lệ từng loại (%)</div></td>
							<td><div class="cell-label-container" ng-bind="round(getTiletungloai('animal_protein'),2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(getTiletungloai('vegetable_protein'),2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(getTiletungloai('animal_fat'),2)"></div></td>
							<td><div class="cell-label-container" ng-bind="round(getTiletungloai('vegetable_fat'),2)"></div></td>
							<td rowspan="2" style="vertical-align: middle;"><div class="cell-label-container" ng-bind="round(getTiletungloai('sugar'),2)"></div></td>
							<td rowspan="2" style="vertical-align: middle;"><div class="cell-label-container" ng-bind="round(getTiletungloai('calo'),2)"></div></td>
							<!-- Bảng calo từng bữa (bữa xế)-->
							<td ng-bind="menu_planning.meals[menu_informations[2].define].name" style="text-align: left; width: 130px;"></td>
			                <td align="right" style="text-align: center;">
			                	<span title="{{getColorForTableCalo(menu_planning.meals[menu_informations[2].define]).title}}" ng-class="{'color-red':(sumCaloMeal(menu_planning.meals[menu_informations[2].define])/selected.group.nutritions.calo*100<selected.group.meals[menu_informations[2].define].min),'color-green':(sumCaloMeal(menu_planning.meals[menu_informations[2].define])/selected.group.nutritions.calo*100>selected.group.meals[menu_informations[2].define].max)}" ng-if="selected.group.meals[menu_informations[2].define].selected">
			                		<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[2].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[2].define].dishes)>0"></text>
			                	</span>
			                	<span ng-if="!selected.group.meals[menu_informations[2].define].selected">
			                		<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[2].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[2].define].dishes)>0"></text>
			                	</span>
			                </td>
			                <td align="center" style="" >
			                	<text ng-bind="selected.group.meals[menu_informations[2].define].min+' - '+selected.group.meals[menu_informations[2].define].max" ng-if="count(menu_planning.meals[menu_informations[2].define].dishes)>0 && (selected.group.meals[menu_informations[2].define].selected || configs['hien_thi_dinh_muc_buasangxe'])"></text>
			                </td>
			                <td align="center" ng-bind="digit_grouping(round(sumCaloMeal(menu_planning.meals[menu_informations[2].define])/tong.calo*100,0), '')"></td>
						</tr>
						<tr >
							<td class="col-body-title col-1"><div class="cell-label-container">Động vật - Thực vật (%)</div></td>
							<td><div class="cell-label-container" ng-bind="NaN(round(getTile_dongthucvat('animal_protein'),2),'-')"></div></td>
							<td><div class="cell-label-container" ng-bind="NaN(round(getTile_dongthucvat('vegetable_protein'),2),'-')"></div></td>
							<td><div class="cell-label-container" ng-bind="NaN(round(getTile_dongthucvat('animal_fat'),2),'-')"></div></td>
							<td><div class="cell-label-container" ng-bind="NaN(round(getTile_dongthucvat('vegetable_fat'),2),'-')"></div></td>
							<!-- Bảng calo từng bữa (bữa phụ)-->
							<td ng-bind="menu_planning.meals[menu_informations[3].define].name" style="text-align: left; width: 130px;"></td>
			                <td align="right" style="text-align: center;">
			                	<span title="{{getColorForTableCalo(menu_planning.meals[menu_informations[3].define]).title}}"
									  ng-class="{'color-red':(sumCaloMeal(menu_planning.meals[menu_informations[3].define])/selected.group.nutritions.calo*100<selected.group.meals[menu_informations[3].define].min),'color-green':(sumCaloMeal(menu_planning.meals[menu_informations[3].define])/selected.group.nutritions.calo*100>selected.group.meals[menu_informations[3].define].max)}" ng-if="selected.group.meals[menu_informations[3].define].selected">
			                    	<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[3].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[3].define].dishes)>0"></text>
			                	</span>
			                	<span ng-if="!selected.group.meals[menu_informations[3].define].selected">
			                    	<text ng-bind="round(sumCaloMeal(menu_planning.meals[menu_informations[3].define])/selected.group.nutritions.calo*100,2)" ng-if="count(menu_planning.meals[menu_informations[3].define].dishes)>0"></text>
			                	</span>
			                </td>
			                <td align="center" style="" >
			                	<text ng-bind="selected.group.meals[menu_informations[3].define].min+' - '+selected.group.meals[menu_informations[3].define].max" ng-if="count(menu_planning.meals[menu_informations[3].define].dishes)>0 && selected.group.meals[menu_informations[3].define].selected"></text>
			                </td>
			                <td align="center" ng-bind="digit_grouping(round(sumCaloMeal(menu_planning.meals[menu_informations[3].define])/tong.calo*100,0), '')"></td>
						</tr>
						<tr class="row-tiledat">
							<td class="col-body-title col-1"><div class="cell-label-container">Tỷ lệ đạt (%)</div></td>
							<td colspan="2" align="center">
								<div class="cell-label-container" ng-bind="NaN(round(getTile_dat_protein(),2),'-')">
									Tỉ lệ đạm
								</div>
							</td>
							<td colspan="2" align="center">
								<div class="cell-label-container" ng-bind="NaN(round(getTile_dat_fat(),2),'-')">
									Tỉ lệ béo
								</div>
							</td>
							<td>
								<div class="cell-label-container" ng-bind="round(getTile_dat_sugar(),2)">Tỷ lệ đường</div>
							</td>
							<td>
								<!-- <div class="cell-label-container" ng-bind="round(getTile_dat_calo(),2)">Tỉ lệ calo</div> -->
							</td>
							<!-- bảng calo từng bữa footer -->
							<td colspan="4" rowspan="{{ configs.co_cau_hidden?2:3 }}" style="background: #fff; vertical-align: bottom;">
								<i class="fa fa-square color-red" style="margin-left: 5px;"></i> <text>Chưa đạt</text><br/>
								<i class="fa fa-square color-green" style="margin-left: 5px;"></i> <text>Vượt quá định mức</text>
							</td>
						</tr>
						<tr class="row-cocauapdung" ng-if="!configs.co_cau_hidden">
							<td class="col-body-title col-1"><div class="cell-label-container">Cơ cấu áp dụng</div></td>
							<td colspan="2" align="center">
								<div class="cell-label-container"
									 ng-bind="round((selected.group.nutritions.animal_protein+selected.group.nutritions.vegetable_protein)*co_cau(null).protein/getPLGTotal(selected.group.nutritions)*100,0)">
									Đạm
								</div>
							</td>
							<td colspan="2" align="center">
								<div class="cell-label-container"
									 ng-bind="round((selected.group.nutritions.animal_fat+selected.group.nutritions.vegetable_fat)*co_cau(null).fat/getPLGTotal(selected.group.nutritions)*100,0)">
									Béo
								</div>
							</td>
							<td>
								<div class="cell-label-container"
									 ng-bind="round(selected.group.nutritions.sugar*co_cau(null).sugar/getPLGTotal(selected.group.nutritions)*100,0)">
									Đường
								</div>
							</td>
							<td>
								<div class="cell-label-container" >
								</div>
							</td>
						</tr>
						<tr class="row-tileplg">
							<td class="col-body-title col-1"><div class="cell-label-container">Tỷ lệ P:L:G (%)</div></td>
							<td colspan="2" align="center">
								<div class="cell-label-container" ng-bind="NaN(round(getTile_PLG().protein,2),'-')">
									Đạm
								</div>
							</td>
							<td colspan="2" align="center">
								<div class="cell-label-container" ng-bind="NaN(round(getTile_PLG().fat,2),'-')">
									Béo
								</div>
							</td>
							<td>
								<div class="cell-label-container" ng-bind="NaN(round(getTile_PLG().sugar,2),'-')">
									Đường
								</div>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</form>
<div style="clear: both;"></div>
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/menu_planning.add.css?=1329217450" />
