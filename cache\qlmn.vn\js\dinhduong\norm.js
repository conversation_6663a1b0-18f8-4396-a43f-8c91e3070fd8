$.norm = {
    module: 'norm',
    id: '', /*Mã project*/
    init: function(nutritions) {
    	var self = this;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        var col = [
                { field:'ck', checkbox: true },
                { title:'Tên nhóm trẻ', field:'name', width:150, sortable:true },
                { title:'Cơ cấu áp dụng', field:'cocauapdung', width:150, sortable:true, formatter: function(value,row,index){
                    var value = '';
                    // if(row.nutritions){
                        var dam = co_cau(null).protein * $['+'](row.nutritions.vegetable_protein, row.nutritions.animal_protein);
                        var beo = co_cau(null).fat * $['+'](row.nutritions.animal_fat, row.nutritions.vegetable_fat);
                        var duong = $['*'](co_cau(null).sugar, row.nutritions.sugar);
                        var tong = dam + beo + duong;
                        value += round(dam/tong*100,0)+'-'+round(beo/tong*100,0)+'-'+round(duong/tong*100,0);
                    // }
                    return value;
                } }
            ];
            $.each(nutritions,function(index,nutrition){
                if(nutrition.is_norm==2){

                    col.push({ title:nutrition.name+' động vật', field:'animal_'+nutrition.define, width:80});
                    col.push({ title:nutrition.name+' thực vật', field:'vegetable_'+nutrition.define, width:80});
                }
                else{
                    col.push({ title:nutrition.name, field:nutrition.define, width:80});
                }
                
            });
            col.push({ title:'Calo khuyến nghị', field:'smallest_rate', width:100, formatter: function(value, row){
                value = row.nutritions.calo_rate_low + ' - ' + row.nutritions.calo_rate;
                return value;
            } })

        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [col],
            {
                onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }, onLoadSuccess:function(data){
                    if($CFG.is_gokids==1) {
                        $('.datagrid-view').height($('.datagrid-view').height() - 30);
                        $('.datagrid-body').height($('.datagrid-body').height() - 30);
                    }
                }
            }
   //          {
   //              // view: detailview,
   //              // detailFormatter: function(rowIndex, rowData){
   //              //     // console.log(rowData);
   //              //     return '<table><tr>' +
   //              // '<td rowspan=2 style="border:0"><img src="images/' + rowData.itemid + '.png" style="height:50px;"></td>' +
   //              // '<td style="border:0">' +
   //              // '<p>Attribute: ' + rowData.attr1 + '</p>' +
   //              // '<p>Status: ' + rowData.status + '</p>' +
   //              // '</td>' +
   //              // '</tr></table>';
   //              // }
			// }
        );
        $.norm.initAngular();
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                scope.norm = {};
                scope.norm.cocau = $.norm.cocau;
                scope.norm.selected = { 
                    cocau: ''
                };
                scope.sys_configs = window.sysConfigs.configs;
                scope.norm.cocauSelected = function(cocauapdung,row, key=''){
                    console.log(scope.sys_configs);
                    row.animal_fat = cocauapdung.animal_fat;
                    row.animal_protein = cocauapdung.animal_protein;
                    row.sugar = cocauapdung.sugar;
                    row.vegetable_fat = cocauapdung.vegetable_fat;
                    row.vegetable_protein = cocauapdung.vegetable_protein;
                    if($CFG.administrator && key && !scope.sys_configs[key]) {
                        var url = [$CFG.remote.base_url, 'information_configs', $CFG.project].join('/');
                        process(url, {async: true, id: key, value: 1}, function (resp) {});
                    }
                }
            });
        },0);
    }, showAddForm: function(callback) { 
    	var self = this;
        $.dm_datagrid.showAddForm(
			{
				module: $CFG.project+'/'+self.module,
				action:'add',
				title:'Thêm mới',
				content: function(element){
					loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
						$(element).html(resp);
					})
				}
			},
			function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    }, showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
        // console.log(row);
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
                    module: $CFG.project+'/'+self.module,
                    action:'edit',
                    title:'Chỉnh sửa',
                    size: size.wide,
                    content: function(element){
                        loadForm($CFG.project+'/'+self.module,'edit', {id: row.group_id}, function(resp){
                            $.norm.angular(element,resp,function(scope){
                                scope.norm.row = row;
                                scope.norm.selected.cocau = ''
                            })
							/*$(element).html(resp);*/
						});
					}
				},
				function(resp){
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Phải chọn một dòng!');
	    }
       
    },angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Chắc chắn xóa ?</div>', function(r){
            if (r){
                $.dm_datagrid.del($CFG.project+'/'+self.module,ids,function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                })
            }
        });
    }, cocau: {
        2: {
            '13 - 37 - 50': {calo:650, animal_protein:14.43, vegetable_protein: 6.18, animal_fat: 18.1, vegetable_fat:7.76, sugar:79.27, biggest_rate: 70, smallest_rate: 60},
            '14 - 36 - 50': {calo:650, animal_protein:15.54, vegetable_protein: 6.66, animal_fat: 17.61, vegetable_fat:7.55, sugar:79.27, biggest_rate: 70, smallest_rate: 60},
            '15 - 37 - 48': {calo:650, animal_protein:16.65, vegetable_protein: 7.13, animal_fat: 18.1, vegetable_fat: 7.76, sugar: 76.1, biggest_rate: 70, smallest_rate: 60}
        },
        3: {
            '13 - 37 - 50': {calo:930, animal_protein:17.69, vegetable_protein: 11.8, animal_fat: 25.9, vegetable_fat:11.1, sugar:113.41, biggest_rate: 70, smallest_rate: 60},
            '14 - 36 - 50': {calo:930, animal_protein:19.05, vegetable_protein: 12.7, animal_fat: 25.2, vegetable_fat: 10.8, sugar: 113.41, biggest_rate: 70, smallest_rate: 60},
            '15 - 37 - 48': {calo:930, animal_protein: 20.41, vegetable_protein: 13.61, animal_fat: 25.9, vegetable_fat: 11.1, sugar: 108.88, biggest_rate: 70, smallest_rate: 60}
        },
        4: {
            '13 - 37 - 50': {calo:1000, animal_protein:19.02, vegetable_protein: 12.68, animal_fat: 27.85, vegetable_fat:11.94, sugar:121.95, biggest_rate: 70, smallest_rate: 60},
            '14 - 36 - 50': {calo:1000, animal_protein:20.49, vegetable_protein: 13.66, animal_fat: 27.1, vegetable_fat: 11.61, sugar: 121.95, biggest_rate: 70, smallest_rate: 60},
            '15 - 37 - 48': {calo:1000, animal_protein:21.95, vegetable_protein: 14.63, animal_fat: 27.85, vegetable_fat: 11.94, sugar: 117.07, biggest_rate: 70, smallest_rate: 60},
        },
        5: {
            '13 - 27 - 60': {calo:1320, animal_protein:25.11, vegetable_protein: 16.74, animal_fat: 26.83, vegetable_fat:11.5, sugar:193.17, biggest_rate: 55, smallest_rate: 50},
            '14 - 26 - 60': {calo:1320, animal_protein:27.04, vegetable_protein: 18.03, animal_fat: 25.83, vegetable_fat: 11.07, sugar: 193.17, biggest_rate: 55, smallest_rate: 50},
            '15 - 25 - 60': {calo:1320, animal_protein:28.98, vegetable_protein: 19.32, animal_fat: 24.84, vegetable_fat: 10.65, sugar: 193.17, biggest_rate: 55, smallest_rate: 50},
            '16 - 35 - 49': {calo:1320, animal_protein:30.91, vegetable_protein: 20.61, animal_fat: 34.77, vegetable_fat: 14.91, sugar: 157.76, biggest_rate: 55, smallest_rate: 50, new_option: true},
        }
    }
}
angular_app.controller('normController',['$scope','$routeParams','$compile','MyCache','$filter','$cookies', function($scope,$routeParams,$compile,MyCache,$filter,$cookies){
    console.log('aaaaaaa')
}]);