angular_app.controller('mainContentController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', '$templateCache', '$templateRequest', '$sce', '$localStorage', '$location',
    function ($scope, $routeParams, $compile, MyCache, $filter, $cookies, $templateCache, $templateRequest, $sce, $localStorage, $location) {
        /*Khai báo mẫu định mức số phần*/
        $scope.dinhmuc = {
            'ngucoc': {name: '<PERSON><PERSON> cốc', value: 3},
            'rau': {name: '<PERSON><PERSON>', value: 7},
            'traicay': {name: '<PERSON>r<PERSON><PERSON> cây', value: 2},
            'damdv': {name: 'Đạm ĐV', value: 7}
        };
        $scope.sys = {
            truong: {
                lists: [],
                selected: undefined
            },
            phong: {
                lists: [],
                selected: undefined
            },
            point: {
                list: [],
                selected: undefined
            },
            configs: {},
            tempVer: (new Date()).getTime()
        };

        $scope.routeParams = $routeParams;
        $scope.menu = {
            select: {},
            page: 'index',
            templates: {
                top: {
                    index: 'menu_top_index.htm',
                    children: 'menu_top_children.htm'
                },
                bottom: {
                    index: 'menu_bottom_index.htm',
                    children: 'menu_bottom_children.htm'
                },
                group: $CFG.template.base_url + '/dinhduong/menu_main-index.html?v='+rand()
            },
            data: {
                top: {},
                bottom: {}
            }
        };
        $scope.sys.phong.onSelected = function (old) {
            var id = 0;
            if ($scope.sys.phong.selected) {
                id = $scope.sys.phong.selected.id;
            }
            if (id != old.id) {
                old.id = id;
                process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
                    async: true,
                    id: id,
                    project_id: 2
                }, function (resp) {
                    $scope.$apply(function () {
                        $scope.sys.truong.lists = resp;
                        if ($scope.phong_origin_id != id) {
                            $('#select-school').combobox('clear');
                        } else {
                            $('#select-school').combobox('setValue', $scope.sys.truong.selected.id);
                        }
                    });
                }, function () {
                    $('#select-school').combobox('clear');
                }, false);
            }
        };
        $scope.sys.point.onSelected = function (old) {
            var id = 0;
            if ($scope.sys.phong.selected) {
                id = $scope.sys.phong.selected.id;
            }
            if (id != old.id) {
                process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
                    async: true,
                    id: id,
                    project_id: 2
                }, function (resp) {
                    $scope.$apply(function () {
                        $scope.sys.truong.lists = resp;
                    });
                }, function () {
                    $('#select-school').combobox('clear');
                }, false);
            }
        };
        /*
        * Kiểm tra có phải là nhiều điểm trường chung kho khác bếp
        * */
        $scope.isSchoolPointTogether = function () {
            if ($CFG.school_points > 1 && $CFG.school_point_together == 1) {
                return true;
            }
            return false;
        };
        /*
        * Khởi tạo lựa chọn cách làm tròn ở cân đối khẩu phần
        * */
        $scope.roundOption = {
            type: 1,
            forTotal: 0
        };
        $scope.setRoundOption = function() {
            localStorage.setItem('roundOption.type', $scope.roundOption.type);
            localStorage.setItem('roundOption.forTotal', $scope.roundOption.forTotal);
        };
        $scope.getRoundOption = function() {
            $scope.roundOption.type = +(localStorage.getItem('roundOption.type') || 1);
            $scope.roundOption.forTotal = +(localStorage.getItem('roundOption.forTotal') || 0);
            if(!localStorage.getItem('roundOption.type')) {
                $scope.setRoundOption();
            }
            return $scope.roundOption;
        };
        $scope.getRoundOption();
        /*
        * Kết thức khởi tạo lựa chọn cách làm tròn ở cân đối khẩu phần
        * */
        $scope.sys.truong.onLoadSuccess = function () {
            if ($scope.sys.truong.old_id && count($scope.sys.truong.lists) > 0) {
                angular.forEach($scope.sys.truong.lists, function (item, ind) {
                    if (item.id == $scope.sys.truong.old_id) {
                        setTimeout(function () {
                            $('#select-school').val(item.text).next().find('input[type="text"]').val(item.text);
                        }, 300)
                        $scope.sys.truong.old_id = undefined;
                    }
                });
            }
        };
        $scope.sys.point.onLoadSuccess = function () {
            if ($scope.sys.point.old_id && count($scope.sys.point.lists) > 0) {
                angular.forEach($scope.sys.point.lists, function (item, ind) {
                    if (item.id == $scope.sys.point.old_id) {
                        setTimeout(function () {
                            $('#select-point').val(item.text).next().find('input[type="text"]').val(item.text);
                        }, 300);
                        $scope.sys.point.old_id = undefined;
                    }
                });
            }
        };
        $scope.sys.point.onSelected = function () {
            var point = $scope.sys.point.selected;
            var id = '';
            if (point) {
                id = point.id;
            }
            process($CFG.remote.base_url + '/doing/admin/unit/setPoint', {
                async: true,
                id: id,
            }, function () {
                statusloading();
                setTimeout(function () {
                    location.reload();
                })
            }, function () {
                $('#select-point').combobox('clear');
            });
        };
        $scope.sys.truong.onSelected = function () {
            var truong = $scope.sys.truong.selected;
            var id = '';
            if (truong) {
                id = truong.id;
            }
            process($CFG.remote.base_url + '/doing/admin/unit/setTruong', {
                async: true,
                id: id,
                project_id: 2
            }, function (resp) {
                statusloading();
                setTimeout(function () {
                    location.reload();
                })
            }, function () {
                $('#select-school').combobox('clear');
            });
        };
        $scope.getParams = function () {
            var urls = $location.path().split('/');
            var index = 2;
            if (urls[2] == 'view') {
                index = 3;
            }
            var params = {};
            params.module = urls[index];
            params.action = urls[index + 1];
            return params;
        };
        $scope.getGroupMenu = function ($define) {
            $rs = {};
            $define || ($define = $scope.getParams().module);
            if ($define) {
                var kt = false;
                $.each($scope.menu.data.top, function (index, menus) {
                    for (var i in menus.children) {
                        var menu = menus.children[i];
                        if ($define == menu.define) {
                            kt = true;
                            if ($CFG.is_only_ura) {
                                $rs = menu;
                            }else{
                                $rs = menus;
                            }
                            $rs['module_name'] = menu.name;
                            return;
                        }
                    }
                });
                if (!kt) {
                    $.each($scope.menu.data.bottom, function (index, menus) {
                        for (var i in menus.children) {
                            var menu = menus.children[i];
                            if ($define == menu.define) {
                                kt = true;
                                if ($CFG.is_only_ura) {
                                    $rs = menu;
                                }else{
                                    $rs = menus;
                                }
                                $rs['module_name'] = menu.name;
                                return;
                            }
                        }
                    });
                }
            }
            return $rs;
        };
        $scope.selected_menu_khauphandinhduong = function () {
            angular.forEach($scope.menu.data.top, function (menu, ind) {
                if (menu.icon == 'khau_phan_dinh_duong') {
                    $scope.menu.selected = menu;
                    setTimeout(function () {
                        $('li#danh_muc').click();
                    })
                }
            })
        };
        $scope.setMonth_selected = function (month) {
            setCookie('month_selected', month);
        };
        $scope.getMonth_selected = function (get_string) {
            var month = (new Date().getMonth() + 1);
            $scope.setMonth_selected(month);
            var m = parseInt(month);
            if (get_string) {
                if (m < 10) {
                    month = '0' + m;
                } else {
                    month = '' + m;
                }
            } else {
                month = m;
            }
            return month;
        };
        /* Khởi tạo menu và cấu hình chung cho các báo cáo */
        $scope._initSys = function () {
            var data = window.sysConfigs;
            data || (data = $templateCache.get('sysConfigs'));
            if (data.rows[1]) {
                $scope.menu.data.top = data.rows[1];
                $scope.menu.selected = '';
                $.each($scope.menu.data.top, function (index, menu) {
					if(menu.name=='Khẩu phần dinh dưỡng' || $scope.menu.selected=='') {
						$scope.menu.selected = menu;
					}
                });
            }
            if (data.rows[2]) {
                $scope.menu.data.bottom = data.rows[2];
            }
            $scope.sys.configs = data.configs;
            window.sysConfigs || (window.sysConfigs = data);
            angular.element(document).ready(function () {
                var li0 = $(".li0").width() + 26;
                var li1 = $(".li1").width() + 26;
                var li2 = $(".li2").width() + 26;
                var li3 = $(".li3").width() + 26;
                var li4 = $(".li4").width() + 26;
                var width = (li0 + li1 + li2 + li3 + li4 + 40);
                if ($(window).width() < 1025) {
                    width = width - 76;
                }
                if (width > 50) {
                    width = width + 20;
                    $(".nav-main-content").css("width", width + "px");
                }
                $('#statusloading-mash').hide();
            });
            $templateCache.put('menu', $scope.menu);
        };
        $scope.detailUserForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Thông tin chi tiết tài khoản',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_detail.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.detailUserSignForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Thông tin chữ kí ảnh, ký số',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_update_sign_image.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.detailUserSignReport = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Ký báo cáo',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_sign_image_in_reports.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.detailSchool = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Báo cáo thống kê',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_detail_school.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.UpdateSchoolForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Báo cáo thống kê',
                size: size.wide,
                showButton: false,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_update_school.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.updateSelfUser = function () {
            var data = {
                async: true,
                email: $CFG.user.email,
                phone: $CFG.user.phone,
                name: $CFG.user.name
            };
            process($CFG.remote.base_url + '/doing/admin/user/updateSelfUser', data, function (resp) {

            }, function () {

            });
        };
        $scope.onchangeSchool_point = function (point, callback) {
            var data = {
                async: true,
                point: point
            };
            process($CFG.remote.base_url + '/doing/admin/user/changePoint', data, function (resp) {
                if (typeof callback === 'function' && resp.result === 'success') {
                    console.log('aaaaaaaaaaaaa', resp)
                    callback(resp);
                }
            }, function () {
            });
        };
        $scope.detailUnitForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Cập nhật thông tin đơn vị',
                size: size.small,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/templates/admin/unit/popup_detail.html', '', {}, function (resp) {
                        $scope.$apply(function () {
                            $scope.$CFG.self_id_old = $scope.$CFG.self_id;
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.updateUnitInfo = function () {
            if ($scope.$CFG.self_id_old != $scope.$CFG.self_id) {
                var data = {
                    id: $scope.$CFG.self_id,
                    async: true
                };
                process($CFG.remote.base_url + '/doing/admin/unit/updateInfo', data, function (resp) {
                    if (resp.result == 'success') {
                        dialogCloseAll();
                    }
                }, function () {

                });
            }
        };
        $scope._initSys();
        /*
        * Load template for cached
        */
        $scope.loadTemplates = function (urls, scope) {
            if (typeof urls === 'string') {
                urls = [urls];
            }
            for (var i in urls) {
                $scope.getTemplate(urls[i]);
            }
            /* Bind template loaded to scope */
            $scope.templateCache = $templateCache;
        };
        $scope.parseUrlTemp = parseUrlTemp;
        $scope.getTemplate = getTemplate;

        function parseUrlTemp(url) {
            if (url.split('//').length === 1) {
                if (url[0] !== '/') {
                    url = '/' + url;
                }
                url = $CFG.template.base_url + url;
            }
            return url;
        }

        function getTemplate(url, callback, reload) {
            var fullUrl = $scope.parseUrlTemp(url);
            if (reload) {
                $templateCache.remove(fullUrl);
                $scope.sys.tempVer++;
            }
            var template = $templateCache.get(fullUrl);
            if (template) {
                if (typeof callback === 'function') {
                    callback(template);
                }
            } else {
                $templateRequest(fullUrl + '?_=' + $scope.sys.tempVer, false).then(function (template) {
                    $templateCache.put(fullUrl, template);
                    if (typeof callback === 'function') {
                        callback(template);
                    }
                }, function () {
                    if (typeof callback === 'function') {
                        callback('Lỗi khi tải.');
                    }
                });
            }
            return template;
        };
        $scope.storage = $localStorage;
    }])
    .run(function ($templateCache) {
        var overload = 0;

        function _initSys() {
            if (overload > 5) {
                return;
            }
            process($CFG.remote.base_url + '/doing/dinhduong/index/sysConfigs', {}, function (resp) {
                $templateCache.put('sysConfigs', resp);
            }, function () {
                overload++;
                _initSys();
            }, false, false, false);
        }

        if (!window.sysConfigs) {
            _initSys();
        }
    });