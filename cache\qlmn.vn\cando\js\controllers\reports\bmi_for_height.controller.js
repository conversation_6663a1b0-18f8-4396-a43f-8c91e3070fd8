(function BmiForHeightYearlyReportController(app) {
    'use strict';

    app.controller('BmiForHeightYearlyReportController', BmiForHeightYearlyReportController);

    function BmiForHeightYearlyReportController($scope, AuthService, CommonService, CourseService, StudentBmiService, BmiService, ACTIVE_MONTHS) {
        var vm = this;

        $scope.sys.module.title = 'Kết quả chiều cao';

        vm.ACTIVE_MONTHS = ACTIVE_MONTHS;

        vm.filters = {
            'school_year': $CFG.active_year || (new Date()).getFullYear(),
            'course_id': 0,
        };

        vm.students = [];

        vm.schoolYears = CommonService.generateSchoolYear();

        CourseService.fetchCourses().then(function (courses) {
            vm.courses = courses;

            if (!AuthService.isSuperAdmin()) {
                vm.filters.course_id = _.get(courses, '[0]courses.data[0].id', 0);
                vm.onCourseChanged();
            }
        });

        vm.onSchoolYearChanged = function onSchoolYearChanged() {
            fetchStudents();
        };

        vm.onCourseChanged = function onCourseChanged() {
            fetchStudents();
        };

        vm.getHeightOfMonth = function getHeightOfMonth(student, month) {
            var histories = _.get(student, 'bmi_histories.data');

            return _.get(
                _.find(histories, {'month': month}),
                'height',
                ''
            );
        };

        vm.getHeightResultOfMonth = function getHeightResultOfMonth(student, month) {
            var histories = _.get(student, 'bmi_histories.data');

            var resultId = _.get(
                _.find(histories, {'month': month}),
                'height_result',
                ''
            );

            return BmiService.getResultCodeFromLabelId(resultId);
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentBmiService.generateDownloadExcelUrlForBmiForHeight(vm.filters);
        };

        function fetchStudents() {
            vm.students = [];

            if (!vm.filters.school_year || !vm.filters.course_id) {
                return;
            }

            StudentBmiService.fetchStudents(vm.filters).then(function (students) {
                vm.students = students;
            });
        }
    }

    BmiForHeightYearlyReportController.$inject = [
        '$scope',
        'AuthService',
        'CommonService',
        'CourseService',
        'StudentBmiService',
        'BmiService',
        'ACTIVE_MONTHS',
    ];

})(window.angular_app);
