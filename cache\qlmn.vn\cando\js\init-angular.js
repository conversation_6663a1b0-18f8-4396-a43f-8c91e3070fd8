angular_app = angular.module("angular_app",['ngRoute','ngResource','ngCookies','ngAnimate','textAngular', 'ngStorage', 'ui.bootstrap'])
/*.config(['$routeProvider','$locationProvider',function ($routeProvider,$locationProvider) {*/
.config(['$routeProvider','$locationProvider', '$controllerProvider', '$compileProvider', function ($routeProvider, $locationProvider, $controllerProvider, $compileProvider) {
        $locationProvider.html5Mode(true);
        $compileProvider.debugInfoEnabled(true);
        angular_app.addController = $controllerProvider.register;
    $routeProvider
    .when('/cando', {
        redirectTo:'/cando/view/student_bmi/dashboard'
    })
    .when('/cando/view/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                '',
                $CFG.project,
                'templates',
                request.module,
                'list.html'
            ];
            delete request.module;
            delete request.action;
            var req = ['?_='+Math.random().toString().split('.')[1]];
            for(var key in request) {
                req.push(key+'='+request[key]);
            }
            return urls.join('/')+req.join('&');
        },
        controller: function($scope, $routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/cando/view/:module/:action', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                '',
                $CFG.project,
                'templates',
                request.module,
                request.action+'.html'
            ];
            delete request.module;
            delete request.action;
            var req = ['?_='+Math.random().toString().split('.')[1]];
            for(var key in request) {
                req.push(key+'='+request[key]);
            }
            return urls.join('/')+req.join('&');
        },
        controller: function($scope,$routeParams) {
            statusloadingclose();
        }
    })
    .when('/cando/view/:module/:action/:id', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                '',
                $CFG.project,
                'templates',
                request.module,
                request.action+'.html'
            ];
            delete request.module;
            delete request.action;
            var req = ['?_='+Math.random().toString().split('.')[1]];
            for(var key in request) {
                req.push(key+'='+request[key]);
            }
            return urls.join('/')+req.join('&');
        },
        controller: function($scope,$routeParams) {
            $scope.routerParams = $routeParams;
            statusloadingclose();
        },

    })
    .when('/cando/:module/:action', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action
            ];
            return urls.join('/')+'/';
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose();
        },
    })
    .when('/cando/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                'list'
            ];
            return urls.join('/')+'?_=';
        },
        controller: function($scope) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/cando/:module/:action/:id', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action,
                request.id
            ];
            return urls.join('/')+'?_=';
        },
        controller: function($scope,$routeParams) {
            $scope.routerParams = $routeParams;
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .otherwise({redirectTo:'/cando'})
}]);