{"version": 3, "sources": ["common.js", "init_angular.js", "my-angular.js", "main-angular.js", "balance_money.js", "balance.js", "index.js", "payout.js", "export.js", "receipt.js", "menu_adjust-controller.js", "menu_planning-controller.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5qEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACz2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1VA;ACAA;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACt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juBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "app.min.js", "sourcesContent": ["$(function () {\r\n    initStatusLoading();\r\n    $('#statusloading-mash').find('#spinner-container').click(function () {\r\n        $('#statusloading-mash').hide();\r\n    })\r\n});\r\n(function (angular) {\r\n    if (typeof angular != 'undefined') {\r\n        angular.fromJson = function (str) {\r\n            if (str && str.charAt(0) === '\"') {\r\n                return JSON.parse(str);\r\n            }\r\n            return str;\r\n        }\r\n    }\r\n})(window.angular);\r\n\r\n/*Bắt sự kiện đóng cửa sổ trình duyệt hoặc tải lại trang (F5)*/\r\nvar eventCloseWindows = {\r\n    title: '',\r\n    enable: false\r\n};\r\n$(window).on(\"beforeunload\", function () {\r\n    if (eventCloseWindows.enable) {\r\n        return eventCloseWindows.enable ? eventCloseWindows.title : null;\r\n    }\r\n});\r\n/*<PERSON><PERSON>t thúc bắt sự kiện đóng cửa sổ trình duyệt*/\r\nvar BootstrapDialog_cache = {};\r\nvar statusloadingCache = {};\r\nvar refress_request = 1;\r\nvar screen_size = {\r\n    width: $(window).width(),\r\n    height: $(window).height()\r\n};\r\nvar size = {\r\n    'normal': BootstrapDialog.SIZE_NORMAL,\r\n    'small': BootstrapDialog.SIZE_SMALL,\r\n    'wide': BootstrapDialog.SIZE_WIDE,\r\n    'large': BootstrapDialog.SIZE_LARGE\r\n}\r\nvar keyString = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\r\n$.selectSchoolYear = function (schoolyear, project) {\r\n    process($CFG.remote.base_url + '/doing/admin/user/set_schoolyear', {\r\n        namhoc: schoolyear,\r\n        project: project\r\n    }, function (resp) {\r\n        if (resp.result == 'success') {\r\n            location.reload();\r\n        }\r\n    });\r\n}\r\n$.timer = function (callback_realtime, m, callback_finish) {\r\n    if (m <= 0) {\r\n        if (typeof callback_finish == 'function') {\r\n            callback_finish();\r\n        }\r\n        return;\r\n    }\r\n    setTimeout(function () {\r\n        m -= 1;\r\n        callback_realtime(m);\r\n        $.timer(callback_realtime, m, callback_finish);\r\n    }, 1000);\r\n\r\n}\r\n$.dm_datagrid = {\r\n    init: function (url, module, title, columns, fixOption) { /* init */\r\n        fixOption || (fixOption = {});\r\n        fixOption.width || (fixOption.width = 500);\r\n        fixOption.height || (fixOption.height = 300);\r\n        // fixOption.onDblClickRow || (fixOption.onDblClickRow = 'showEditForm');\r\n        fixOption.idField || (fixOption.idField = 'id');\r\n        fixOption.pageSize || (fixOption.pageSize = 30);\r\n        fixOption.checkOnSelect || (fixOption.checkOnSelect = true);\r\n        fixOption.selectOnCheck || (fixOption.selectOnCheck = true);\r\n        fixOption.ctrlSelect || (fixOption.ctrlSelect = true);\r\n        fixOption.singleSelect || (fixOption.singleSelect = false);\r\n        fixOption.nowrap || (fixOption.nowrap = false);\r\n        fixOption.enableFilter || (fixOption.enableFilter = false);\r\n\r\n        if (fixOption.checkOnSelect == 'false' || fixOption.checkOnSelect == false) {\r\n            fixOption.checkOnSelect = false;\r\n        } else {\r\n            fixOption.checkOnSelect = true;\r\n        }\r\n        if (fixOption.selectOnCheck == 'false' || fixOption.selectOnCheck == false) {\r\n            fixOption.selectOnCheck = false;\r\n        } else {\r\n            fixOption.selectOnCheck = true;\r\n        }\r\n        if (fixOption.ctrlSelect == 'false' || fixOption.ctrlSelect == false) {\r\n            fixOption.ctrlSelect = false;\r\n        } else {\r\n            fixOption.ctrlSelect = true;\r\n        }\r\n        if (fixOption.pagination == 'false') {\r\n            fixOption.pagination = false;\r\n        }\r\n        if (fixOption.pagination != false) {\r\n            fixOption.pagination = true;\r\n        }\r\n        if (!jQuery.isArray(fixOption.pageList)) {\r\n            fixOption.pageList = [30, 70, 100, 200];\r\n        }\r\n        module || (module = 'view');\r\n        var exten = '_detail';\r\n        var dataoption = {\r\n            title: title,\r\n            url: url + \"?_=\" + refress_request + '&_online=' + $('#tructuyen').html(),\r\n            width: fixOption.width,\r\n            height: fixOption.height,\r\n            async: false,\r\n            fit: true,\r\n            fitColumns: true,\r\n            idField: fixOption.idField,\r\n            nowrap: fixOption.nowrap,\r\n            filterDelay: 1200,\r\n            autoRowHeight: true,\r\n            remoteFilter: true,\r\n            rownumbers: true,\r\n            pageSize: fixOption.pageSize,\r\n            pageList: fixOption.pageList,\r\n            pagination: fixOption.pagination,\r\n            ctrlSelect: fixOption.ctrlSelect,\r\n            checkOnSelect: fixOption.checkOnSelect,\r\n            selectOnCheck: fixOption.selectOnCheck,\r\n            toolbar: '#tb_' + module + exten,\r\n            columns: columns,\r\n            border: false,\r\n            onLoadSuccess: function (data) {\r\n                $('#tbl_' + module).datagrid('unselectAll');\r\n                if (typeof fixOption.onLoadSuccess === 'function') {\r\n                    fixOption.onLoadSuccess(data);\r\n                }\r\n            },\r\n            onBeforeLoad: function (data) {\r\n                $('#tbl_' + module).datagrid('unselectAll');\r\n            }\r\n        };\r\n        if (typeof fixOption.remoteFilter != 'undefined') {\r\n            dataoption.remoteFilter = fixOption.remoteFilter;\r\n        }\r\n        if (typeof fixOption.onDblClickRow === 'function') {\r\n            dataoption.onDblClickRow = fixOption.onDblClickRow;\r\n        }\r\n        $.each(fixOption, function (index, value) {\r\n            dataoption[index] = value;\r\n        });\r\n        var selector = $('#tbl_' + module);\r\n        selector.datagrid(dataoption);\r\n        if (fixOption.enableFilter !== false) {\r\n            selector.datagrid('enableFilter', fixOption.enableFilter);\r\n        }\r\n    }, showAddForm: function ($option, callback) {\r\n        var dialog_key = count(BootstrapDialog_cache) + Math.random();\r\n        var op = {\r\n            title: ($option.title || 'Mẫu nhập liệu'),\r\n            size: ($option.size || size.normal),\r\n            message: $('<div style=\"margin:20px; width:100%;height:50px;\"></div>').append(getLoadingHTML())\r\n        };\r\n        if (typeof $option == 'number') {\r\n            op.size = size.wide;\r\n        }\r\n        if ($option.showButton === 'false' || $option.showButton === false) {\r\n            $option.showButton = false;\r\n        } else {\r\n            $option.showButton = true;\r\n        }\r\n        if ($option.fullScreen === 'true' || $option.fullScreen === true) {\r\n            $option.fullScreen = true;\r\n        } else {\r\n            $option.fullScreen = false;\r\n        }\r\n        if ($option.draggable === 'true' || $option.draggable === true) {\r\n            $option.draggable = true;\r\n        } else {\r\n            $option.draggable = false;\r\n        }\r\n        if ($option.noteWarning === 'true' || $option.noteWarning === true || $option.noteWarning === undefined) {\r\n            $option.noteWarning = true;\r\n        } else {\r\n            $option.noteWarning = false;\r\n        }\r\n        var onShown = '';\r\n        if (typeof $option.onshown === 'function') {\r\n            onShown = $option.onshown;\r\n        }\r\n        if (typeof $option.onShown === 'function') {\r\n            onShown = $option.onShown;\r\n        }\r\n        var onShow = '';\r\n        if (typeof $option.onshow === 'function') {\r\n            onShow = $option.onshow;\r\n        }\r\n        if (typeof $option.onShow === 'function') {\r\n            onShow = $option.onShow;\r\n        }\r\n        $option.titleAskBeforeClose = 'Những thay đổi có thể chưa được lưu.';\r\n        if ($option.askBeforeClose != undefined && $option.askBeforeClose != '' && $option.askBeforeClose != 'false' && $option.askBeforeClose != false) {\r\n            if ($option.askBeforeClose == 'true') {\r\n                $option.askBeforeClose = true;\r\n            }\r\n            if (typeof $option.askBeforeClose === 'string') {\r\n                $option.titleAskBeforeClose = $option.askBeforeClose;\r\n                $option.askBeforeClose = true;\r\n            }\r\n            eventCloseWindows.title = $option.titleAskBeforeClose;\r\n            eventCloseWindows.enable = true;\r\n        }\r\n        if ($option.showButton && !$option.buttons) {\r\n            op['buttons'] = [\r\n                {\r\n                    id: 'btn-save',\r\n                    icon: 'glyphicon glyphicon-floppy-disk',\r\n                    label: 'Lưu',\r\n                    cssClass: 'btn-primary',\r\n                    action: function (dialogRef) {\r\n                        var data = {};\r\n                        var valid = true;\r\n                        var form_value = getSubmitForm(dialogRef.getModalBody(), true);\r\n                        if ($option.cb != undefined && typeof ($option.cb) == \"function\") {\r\n                            valid = $option.cb(form_value);\r\n                        }\r\n                        if (!valid) {\r\n                            return valid;\r\n                        }\r\n                        if (op['data']) {\r\n                            data = op['data'];\r\n                        } else {\r\n                            data = {data: arrayToJson(form_value)};\r\n                        }\r\n                        data.async = true;\r\n                        var url = $CFG.remote.base_url + '/doing/' + $option.module + '/' + $option.action;\r\n                        process(url, data, function (resp) {\r\n                            if (resp.result == \"success\") {\r\n                                dialogRef.close();\r\n                                if (typeof callback === 'function') {\r\n                                    callback(resp);\r\n                                }\r\n                            }\r\n                        });\r\n                    }\r\n                }, {\r\n                    label: 'Bỏ qua',\r\n                    icon: 'glyphicon glyphicon-log-out',\r\n                    action: function (dialog) {\r\n                        dialog.close();\r\n                    }\r\n                }\r\n            ];\r\n            op['closable'] = false;\r\n        } else {\r\n            if ($option.closable == undefined || $option.closable == true || $option.closable == 'true') {\r\n                op['closable'] = true;\r\n            } else {\r\n                op['closable'] = false;\r\n            }\r\n            if ($option.buttons) {\r\n                op.buttons = $option.buttons;\r\n            }\r\n        }\r\n        op.draggable = $option.draggable;\r\n        op['onshown'] = function (dialogRef) {\r\n            dialogRef.askBeforeClose = $option.askBeforeClose;\r\n            BootstrapDialog_cache[dialog_key] = dialogRef;\r\n            if (typeof $option.content === 'function') {\r\n                $option.content(dialogRef.getModalBody(), dialogRef);\r\n            } else if (typeof $option.content == 'string') {\r\n                if ($option.scope != undefined) {\r\n                    dialogRef.scope = $option.scope;\r\n                    dialogRef.url = $option.content;\r\n                    $option.scope.getTemplate($option.content, function (template) {\r\n                        setTimeout(function () {\r\n                            $option.scope.$apply(function () {\r\n                                dialogRef.getModalBody().html($option.scope.compile(template, $option.scope));\r\n                            });\r\n                        });\r\n                    });\r\n                } else {\r\n                    op.message.html($option.content);\r\n                }\r\n            } else {\r\n            }\r\n            setDialog2fullscreen(dialogRef, $option);\r\n            if (typeof onShown == 'function') {\r\n                onShown(dialogRef, dialogRef.getModalBody());\r\n            }\r\n            if (typeof $option.size == 'number') {\r\n                dialogRef.$modalContent.css({width: $option.size, margin: 'auto'});\r\n            }\r\n        };\r\n        op['onshow'] = function (dialogRef) {\r\n            if (typeof onShow == 'function') {\r\n                onShow(dialogRef, dialogRef.getModalBody());\r\n            }\r\n        };\r\n        op['onhide'] = function (dialog) {\r\n            if (dialog.askBeforeClose === true) {\r\n                $.messager.confirm('Đóng cửa sổ chức năng?', '<div style = \"font-size: 14px\">' + $option.titleAskBeforeClose + '</div>', function (r) {\r\n                    if (r) {\r\n                        dialog.askBeforeClose = false;\r\n                        dialog.close();\r\n                        eventCloseWindows.title = '';\r\n                        eventCloseWindows.enable = false;\r\n                    }\r\n                });\r\n                return false;\r\n            } else {\r\n                if (typeof $option.cancel === 'function') {\r\n                    $option.cancel();\r\n                }\r\n                delete BootstrapDialog_cache[dialog_key];\r\n            }\r\n        };\r\n        if (op['buttons'] && $option.noteWarning) {\r\n            if (op['buttons'].length > 0) {\r\n                op['buttons'].push({\r\n                    id: '',\r\n                    icon: '',\r\n                    label: 'Chú ý: Những mục nhập có tiêu đề màu cam là bắt buộc.',\r\n                    cssClass: 'btn-link pull-left valid-red valid-i'\r\n                });\r\n            }\r\n        }\r\n        BootstrapDialog.show(op);\r\n    }, showEditForm: function ($option, callback) {\r\n        this.showAddForm($option, callback);\r\n    }, del: function (controller, ids, callback, data_extend) {\r\n        if (controller.split('del').length == 1) {\r\n            controller += '/del';\r\n        }\r\n        if (typeof ids === 'array') {\r\n            ids = ids.join(',');\r\n        }\r\n        var data = {ids: ids, async: true};\r\n        if (typeof data_extend == 'object') {\r\n            jQuery.extend(data, data_extend);\r\n        }\r\n        process(controller, data, function (resp) {\r\n            if (resp.result == 'success') {\r\n                if (typeof callback === 'function') {\r\n                    callback(resp);\r\n                }\r\n            }\r\n        })\r\n    }, combobox: function (id, data, options) {\r\n        new comboboxInit(id, data, options);\r\n    }, doSearch: function (datagridID, ids, filter_type) {\r\n        if (typeof ids != 'object' || !datagridID) {\r\n            return;\r\n        }\r\n        if (datagridID.split('#').length == 1 && datagridID.split('.').length == 1) {\r\n            datagridID = '#' + datagridID;\r\n        }\r\n        var queryParams = $(datagridID).datagrid('options').queryParams;\r\n        filter_type || (filter_type = 'and');\r\n        var filterRules = [];\r\n        $.each(ids, function (id, op) {\r\n            if (typeof op != 'string') {\r\n                if (op.value != 'clear') {\r\n                    filterRules.push({field: id, op: op.op, value: op.value});\r\n                }\r\n            } else if (op != 'clear') {\r\n                var input = $('#' + id);\r\n                if (input.attr('type') == 'checkbox') {\r\n                    input = $('#' + id + ':checked');\r\n                }\r\n                if (input.length) {\r\n                    if (input.attr('field')) {\r\n                        id = input.attr('field');\r\n                    }\r\n                    op || (op = 'equal');\r\n                    var value = input.val();\r\n                    if (value + '' != '') {\r\n                        filterRules.push({field: id, op: op, value: value});\r\n                    }\r\n                }\r\n            }\r\n        });\r\n        queryParams.filterRules = filterRules;\r\n        queryParams.filter_type = filter_type;\r\n        setTimeout(function () {\r\n            $(datagridID).datagrid('load', queryParams);\r\n        }, 0);\r\n    }, show: function (options, callback) {\r\n        var dialog_key = count(BootstrapDialog_cache) + Math.random();\r\n        options.message = '<div style=\"padding: 10px;\">' + options.message + '</div>';\r\n        options.onshown = function (dialogRef) {\r\n            BootstrapDialog_cache[dialog_key] = dialogRef;\r\n            if (typeof callback === 'function') {\r\n                callback();\r\n            }\r\n        };\r\n        options.onhide = function () {\r\n            delete BootstrapDialog_cache[dialog_key];\r\n        };\r\n        BootstrapDialog.show(options);\r\n    }\r\n}\r\n\r\nfunction rand(min, max) {\r\n    var $ran = (Math.random() + '').split('.')[1];\r\n    var num = $ran;\r\n    if (min && max) {\r\n        if (max < 10) {\r\n            num = Number(num[0]);\r\n        } else {\r\n            var tmp = ''\r\n            for (var i = 0; i < (max + '').length; i++) {\r\n                tmp += num[i];\r\n            }\r\n            num = Number(tmp);\r\n        }\r\n        if (num > max) {\r\n            num = max;\r\n        } else if (num <= min) {\r\n            num = min;\r\n        }\r\n    }\r\n    return num;\r\n}\r\n\r\nfunction dialogCloseAll(callback, type) {\r\n    for (var i in BootstrapDialog_cache) {\r\n        if (BootstrapDialog_cache[i]) {\r\n            if (!type) {\r\n                BootstrapDialog_cache[i].askBeforeClose = false;\r\n            }\r\n            BootstrapDialog_cache[i].close();\r\n        }\r\n    }\r\n    BootstrapDialog_cache = {};\r\n    if (typeof callback === 'function') {\r\n        callback();\r\n    }\r\n}\r\n\r\nfunction dialogClose(callback, type) {\r\n    var id = undefined;\r\n    for (var i in BootstrapDialog_cache) {\r\n        id = i;\r\n    }\r\n    if (!type) {\r\n        BootstrapDialog_cache[id].askBeforeClose = false;\r\n    }\r\n    BootstrapDialog_cache[id].close();\r\n    delete BootstrapDialog_cache[id];\r\n    if (typeof callback === 'function') {\r\n        callback();\r\n    }\r\n}\r\n\r\nfunction dialogRefresh() {\r\n    var dialog = undefined;\r\n    var id = undefined;\r\n    for (var i in BootstrapDialog_cache) {\r\n        id = i;\r\n    }\r\n    if (BootstrapDialog_cache[id]) {\r\n        if (BootstrapDialog_cache[id].btnReload) {\r\n            console.log(BootstrapDialog_cache[id].btnReload);\r\n            $(BootstrapDialog_cache[id].btnReload).click();\r\n        }\r\n    }\r\n}\r\n\r\nfunction comboboxInit(id, data, options) {\r\n    options || (options = {});\r\n    options.valueField || (options.valueField = 'id');\r\n    options.textField || (options.textField = 'text');\r\n    options.width || (options.width = 200);\r\n    options.height || (options.height = 34);\r\n    options.delay || (options.delay = 500);\r\n    options.placeholder || (options.placeholder = '');\r\n    options.queryParams || (options.queryParams = {});\r\n    options.searchFields || (options.searchFields = options.textField);\r\n    if (typeof id != 'object') {\r\n        if (id.split('#').length == 1) {\r\n            id = 'input#' + id;\r\n        }\r\n        id = $(id);\r\n    }\r\n    if (typeof options.searchFields === 'string') {\r\n        options.searchFields = [options.searchFields];\r\n    }\r\n    if (id.attr('placeholder')) {\r\n        options.placeholder = id.attr('placeholder');\r\n    }\r\n    var $options = {\r\n        queryParams: options.queryParams,\r\n        valueField: options.valueField,\r\n        textField: options.textField,\r\n        width: options.width,\r\n        height: options.height,\r\n        mode: options.mode,\r\n        delay: options.delay,\r\n        value: options.value,\r\n        multiple: options.multiple,\r\n        onBeforeLoad: function (params) {\r\n            $(this).next().children('input').attr('placeholder', options.placeholder);\r\n            if (options.mode != 'local') {\r\n                $(this).next().children('span').append($(getLoadingHTML()));\r\n                $(this).next().children('span').children('a').hide();\r\n            }\r\n        }, onSelect: function (row) {\r\n            if (options.multiple) {\r\n                var value = [];\r\n                id.next().find('input.combo-value').each(function (index, el) {\r\n                    value.push($(el).val());\r\n                });\r\n                id.val(value.join(','));\r\n            } else {\r\n                id.val(row[options.valueField])\r\n            }\r\n            if (typeof options.onSelect === 'function') {\r\n                options.onSelect(row, this);\r\n            }\r\n        }, onLoadSuccess: function (data, el) {\r\n            setTimeout(function () {\r\n                id.next().children('span').children('a').show();\r\n                id.next().children('span').children('div').hide();\r\n            }, 300);\r\n            if (options.multiple) {\r\n                var value = [];\r\n                id.next().find('input.combo-value').each(function (index, ele) {\r\n                    value.push($(ele).val());\r\n                });\r\n                id.val(value.join(','));\r\n            }\r\n            if (typeof options.onLoadSuccess === 'function') {\r\n                options.onLoadSuccess(data, el, this);\r\n            }\r\n        }, onLoadError: function () {\r\n            id.next().children('span').children('div').hide();\r\n            id.next().children('span').children('span').hide();\r\n            id.next().children('span').append(getErrorHTML());\r\n\r\n        }, onUnselect: function (row) {\r\n            var value = [];\r\n            id.next().find('input.combo-value').each(function (index, el) {\r\n                value.push($(el).val());\r\n            });\r\n            id.val(value.join(','));\r\n            if (typeof options.onUnselect === 'function') {\r\n                options.onUnselect(row, this);\r\n            }\r\n        }, filter: function (q, row) {\r\n            var opts = $(this).combobox('options');\r\n            var qs = removeUnicode(q.toLowerCase());\r\n            if (removeUnicode(row[opts.textField]).toLowerCase().indexOf(qs) >= 0) {\r\n                return true;\r\n            }\r\n            return false;\r\n        }, onChange: function (newvalue, oldvalue) {\r\n            if (typeof options.onChange === 'function') {\r\n                options.onChange(newvalue, oldvalue, this);\r\n            }\r\n        }\r\n    };\r\n    if (typeof options.formatter === 'function') {\r\n        $options['formatter'] = options.formatter;\r\n    }\r\n    delete $options.url;\r\n    delete $options.data;\r\n    if (typeof data === 'string') {\r\n        $options.url = data;\r\n    } else {\r\n        $options.data = data;\r\n    }\r\n    id.combobox($options);\r\n}\r\n\r\nfunction init_uploader(id_fileupload, url, accept_file_types, limit_file_size, success_func) {\r\n    var input_element = null;\r\n    if (typeof id_fileupload === 'string') {\r\n        input_element = $(\"#\" + id_fileupload);\r\n    } else {\r\n        input_element = id_fileupload;\r\n    }\r\n    var container = $('<span class=\"container-input-file\"></span>');\r\n    input_element.after(container);\r\n    var content_input = $('<span class=\"btn fileinput-button\"></span>');\r\n    var id_files = $('<span id=\"files\" class=\"files\"></span>');\r\n    content_input.append($('<span class=\"btn btn-warring\">Chọn file...</span>')).append(input_element);\r\n    container.append(content_input).append(id_files);\r\n    var length = arguments.length;\r\n    var resizable = true;\r\n    if (length == 7) {\r\n        resizable = arguments[6];\r\n    }\r\n    input_element.fileupload({\r\n        url: url,\r\n        dataType: 'json',\r\n        singleFileUploads: true,\r\n        autoUpload: true,\r\n        acceptFileTypes: new RegExp(\"(\\.|\\/)(\" + accept_file_types + \")$\", \"i\"),\r\n        maxFileSize: (15 * 1024 * 1024), /* 15 MB */\r\n        disableImageResize: !resizable, /* Android(?!.*Chrome)|Opera */\r\n        previewMaxWidth: 70,\r\n        previewMaxHeight: 70,\r\n        previewCrop: true\r\n    }).on('fileuploadprocessalways', function (e, data) {\r\n        id_files.html('');\r\n        var index = 0;\r\n        file = data.files[index];\r\n        if (file.error) {\r\n            $.messager.alert('Lỗi', file.error);\r\n        } else {\r\n            statusloading();\r\n            data.context = id_files;\r\n            var node = $('<span class=\"file_upload_choise\"/>')\r\n                .text(file.name)\r\n                .append('<span style=\"font-weight:bold\">&nbsp; - ' + (file.size / 1024 / 1024).toFixed(2) + ' Mb</span>&nbsp;');\r\n            node.append('<span id=\"upload_stt\" style=\"color: red\"></span>');\r\n            node.appendTo(data.context);\r\n        }\r\n    }).on('fileuploadprogressall', function (e, data) {\r\n        var progress = parseInt(data.loaded / data.total * 100, 10);\r\n        $(\"#upload_stt\").text(progress + \"%\");\r\n    }).on('fileuploaddone', function (e, data) {\r\n        success_func(data.result);\r\n        if (data.result.files) {\r\n            var index = 0, file = data.result.files[index];\r\n            if (file.url) {\r\n                var link = $('<a>')\r\n                    .attr('target', '_blank')\r\n                    .prop('href', file.url);\r\n                if (data.context) {\r\n                    $(data.context.children()[index]).wrap(link);\r\n                }\r\n            }\r\n            if (file.error) {\r\n                $.messager.alert('Lỗi', file.error);\r\n            }\r\n        }\r\n        id_files.html('');\r\n        statusloadingclose();\r\n    }).on('fileuploadfail', function (e, data) {\r\n        id_files.html('');\r\n        $.messager.alert('Lỗi', 'Tải file không thành công');\r\n        statusloadingclose();\r\n    }).prop('disabled', !$.support.fileInput)\r\n        .parent().addClass($.support.fileInput ? undefined : 'disabled');\r\n}\r\n\r\nfunction form_edit_pass() {\r\n    $.dm_datagrid.showEditForm(\r\n        {\r\n            module: 'admin/user',\r\n            action: 'changepass',\r\n            title: 'Đổi mật khẩu',\r\n            size: size.small,\r\n            noteWarning: '',\r\n            content: function (element) {\r\n                loadForm('admin/user', 'changepass', {}, function (resp) {\r\n                    $(element).html(resp);\r\n                })\r\n            }\r\n        }\r\n    );\r\n}\r\n\r\nfunction count($ob) {\r\n    $rs = 0;\r\n    if (typeof $ob === 'string' || typeof $ob === 'array') {\r\n        $rs = $ob.length;\r\n    } else if (typeof $ob === 'number') {\r\n        var str = $ob + '';\r\n        $rs = $str.length;\r\n    } else if (typeof $ob === 'function') {\r\n    } else if (typeof $ob === 'object') {\r\n        for (var i in $ob) {\r\n            $rs++;\r\n        }\r\n    }\r\n    return $rs;\r\n}\r\nfunction openDlgThongBao(name_cache, filename, solan) {\r\n    var tb_count = getCookie(name_cache);\r\n    if (!tb_count) {\r\n        tb_count = 1;\r\n    }else{\r\n        tb_count = parseInt(tb_count);\r\n    }\r\n    if (tb_count > solan) {\r\n        return;\r\n    }\r\n    tb_count += 1;\r\n    setCookie(name_cache, tb_count, 60*60*24*30*12);\r\n    var content = '<div id=\"dlg_warning\"><div class=\"dlg-content\"></div></div>';\r\n    if (!$('body > #dlg_warning').length) {\r\n        $('body').append(content);\r\n    }\r\n    $('#dlg_warning').dialog({\r\n        title: '',\r\n        width: 550,\r\n        height: 400,\r\n        closed: false,\r\n        cache: false,\r\n        modal: true,\r\n        onOpen : function (ele) {\r\n            var img = $('<img style=\"width: 100%; height: 100%;\">');\r\n            img.attr('src',$CFG.remote.base_url+'/images/thongbao/'+filename+'?_' + Math.random().toString().split('.')[1]);\r\n            $('.window').css({\"background\":\"rgba(0,0,0,0)\",\"border\":\"0px\",\"box-shadow\":\"none\"});\r\n            var btn_close = $('<div style=\"top: 60px;right: 53px;position: absolute;font-size:17px; opacity: 0.3;\" class=\"close\"></div>');\r\n            btn_close.append('<img src=\"' + $CFG.remote.base_url + '/images/close.png\">');\r\n            $('#dlg_warning').css({\"background\":\"rgba(0,0,0,0)\",\"border\":\"0px\", \"overflow\": \"unset\"}).append(btn_close).append(img);\r\n            btn_close.click(function(){\r\n                $('#dlg_warning').dialog(\"close\");\r\n            });\r\n            $('.window-mask').click(function(){\r\n                $('#dlg_warning').dialog(\"close\");\r\n            });\r\n        }\r\n    });\r\n}\r\nfunction loadForm(module, action, params, callback) {\r\n    params || (params = {});\r\n    params.dataType || (params.dataType = 'html');\r\n    params.async || (params.async = false);\r\n    /*Truyền số người online để*/\r\n    var online = $('#tructuyen').html();\r\n    params._online = online;\r\n    var url = module;\r\n    if (action != '') {\r\n        url = url + '/' + action;\r\n    }\r\n    url = url + \"?_=\" + rand();\r\n    if(url.split('//').length === 1){\r\n\t\tif(url[0] !== '/') {\r\n\t\t\turl = '/' + url;\r\n\t\t}\r\n\t\turl = $CFG.remote.base_url + url;\r\n\t}\r\n    var note = {\r\n        title: 'Thông báo',\r\n        message: '',\r\n        buttons: [{\r\n            label: 'Đóng',\r\n            icon: 'glyphicon glyphicon-log-out',\r\n            action: function (dialog) {\r\n                dialog.close();\r\n            }\r\n        }]\r\n    };\r\n    $.ajax({\r\n        type: 'GET',\r\n        url: url,\r\n        data: params,\r\n        dataType: params.dataType,\r\n        async: params.async,\r\n        success: function (resp) {\r\n            if (typeof callback === 'function') {\r\n                callback(resp);\r\n            }\r\n            if (params.dataType == 'json') {\r\n                if (resp.errors != undefined) {\r\n                    if (count(resp.errors) > 0) {\r\n                        var html = [];\r\n                        $.each(resp.errors, function (index, item) {\r\n                            if (typeof item != 'string') {\r\n                                $.each(item, function (i1, value) {\r\n                                    html.push('<div> - ' + value + '</div>');\r\n                                })\r\n                            } else {\r\n                                html.push('<div> - ' + item + '</div>');\r\n                            }\r\n                        });\r\n                        if (html.length > 0) {\r\n                            note.message = '<div style=\"max-height:400px;overflow-y:scroll;\">' + html.join(' ') + '</div>';\r\n                            $.dm_datagrid.show(note);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        error: function (e) {\r\n            if (typeof callback === 'function') {\r\n                callback('Lỗi khi xử lý');\r\n            }\r\n        }, beforeSend: function () {\r\n            showSpinner('load_form_' + module);\r\n        }, complete: function () {\r\n            hiddenSpinner('load_form_' + module);\r\n        }\r\n    });\r\n}\r\n\r\nfunction setDialog2fullscreen(dialogRef, options) {\r\n    var header = dialogRef.getModalHeader().children('.bootstrap-dialog-header');\r\n    var body_h = $('body').height() - 50;\r\n    if (options.fullScreen) {\r\n        dialogRef.getModalDialog().addClass('full-screen');\r\n        dialogRef.getModalBody().css({'min-height': body_h})\r\n    }\r\n    $(window).resize(function () {\r\n        if (dialogRef.getModalDialog().hasClass('full-screen')) {\r\n            dialogRef.getModalBody().css({'min-height': body_h});\r\n        }\r\n    });\r\n    header.dblclick(function () {\r\n        if (dialogRef.getModalDialog().hasClass('full-screen')) {\r\n            dialogRef.getModalDialog().removeClass('full-screen');\r\n            dialogRef.getModalBody().css({'min-height': ''})\r\n        } else {\r\n            dialogRef.getModalDialog().addClass('full-screen');\r\n            dialogRef.getModalBody().css({'min-height': body_h})\r\n        }\r\n    });\r\n    if (options.closable != false && options.closable != 'false') {\r\n        header.children('.bootstrap-dialog-close-button').css('display', 'block');\r\n    }\r\n    var btnZoomOutIn = $('<div href=\"javascript: void(0);\" class=\"bootstrap-dialog-close-button btn-dialog-fullscreen\"></div>');\r\n    var zoomOut = $('<span class=\"glyphicon glyphicon-fullscreen\" title=\"Toàn màn hình\"></span>');\r\n    zoomOut.click(function () {\r\n        dialogRef.getModalDialog().addClass('full-screen');\r\n        dialogRef.getModalBody().css({'min-height': body_h})\r\n    });\r\n    var zoomIn = $('<span class=\"glyphicon glyphicon-resize-small\" title=\"Thu nhỏ\"></span>')\r\n    zoomIn.click(function () {\r\n        dialogRef.getModalDialog().removeClass('full-screen');\r\n        dialogRef.getModalBody().css({'min-height': ''});\r\n    });\r\n    btnZoomOutIn.append(zoomOut).append(zoomIn);\r\n    header.append(btnZoomOutIn);\r\n    if (typeof options.content === 'function' || options.scope != undefined) {\r\n        dialogRef.btnReload = $('<div href=\"javascript: void(0);\" class=\"bootstrap-dialog-close-button btn-dialog-fullscreen\"></div>')\r\n            .append($('<span class=\"glyphicon glyphicon-refresh\" title=\"Tải lại\"></span>'));\r\n        dialogRef.btnReload.click(function () {\r\n            if (options.scope != undefined) {\r\n                options.scope.getTemplate(options.content, function (template) {\r\n                    dialogRef.getModalBody().html(options.scope.compile(template, options.scope));\r\n                    if (typeof options.reload === 'function') {\r\n                        options.reload(dialogRef);\r\n                    }\r\n                }, true);\r\n            } else {\r\n                dialogRef.getModalBody().html($('<div style=\"margin:20px; width:100%;height:50px;\"></div>').append(getLoadingHTML()));\r\n                options.content(dialogRef.getModalBody());\r\n            }\r\n        });\r\n        header.append(dialogRef.btnReload);\r\n    }\r\n}\r\n\r\nfunction utf8Encode(string) {\r\n    string = string.replace(/\\x0d\\x0a/g, \"\\x0a\");\r\n    var output = \"\";\r\n    for (var n = 0; n < string.length; n++) {\r\n        var c = string.charCodeAt(n);\r\n        if (c < 128) {\r\n            output += String.fromCharCode(c);\r\n        } else if ((c > 127) && (c < 2048)) {\r\n            output += String.fromCharCode((c >> 6) | 192);\r\n            output += String.fromCharCode((c & 63) | 128);\r\n        } else {\r\n            output += String.fromCharCode((c >> 12) | 224);\r\n            output += String.fromCharCode(((c >> 6) & 63) | 128);\r\n            output += String.fromCharCode((c & 63) | 128);\r\n        }\r\n    }\r\n    return output;\r\n}\r\n\r\nfunction utf8Decode(input) {\r\n    var string = \"\";\r\n    var i = 0;\r\n    var c = c1 = c2 = 0;\r\n    while (i < input.length) {\r\n        c = input.charCodeAt(i);\r\n        if (c < 128) {\r\n            string += String.fromCharCode(c);\r\n            i++;\r\n        } else if ((c > 191) && (c < 224)) {\r\n            c2 = input.charCodeAt(i + 1);\r\n            string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));\r\n            i += 2;\r\n        } else {\r\n            c2 = input.charCodeAt(i + 1);\r\n            c3 = input.charCodeAt(i + 2);\r\n            string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\r\n            i += 3;\r\n        }\r\n    }\r\n    return string;\r\n}\r\n\r\nfunction base64Encode(input) {\r\n    var output = \"\";\r\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\r\n    var i = 0;\r\n    input = utf8Encode(input);\r\n    while (i < input.length) {\r\n        chr1 = input.charCodeAt(i++);\r\n        chr2 = input.charCodeAt(i++);\r\n        chr3 = input.charCodeAt(i++);\r\n        enc1 = chr1 >> 2;\r\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\r\n        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\r\n        enc4 = chr3 & 63;\r\n        if (isNaN(chr2)) {\r\n            enc3 = enc4 = 64;\r\n        } else if (isNaN(chr3)) {\r\n            enc4 = 64;\r\n        }\r\n        output = output + keyString.charAt(enc1) + keyString.charAt(enc2) + keyString.charAt(enc3) + keyString.charAt(enc4);\r\n    }\r\n    return output;\r\n}\r\n\r\nfunction base64Decode(input) {\r\n    var output = \"\";\r\n    var chr1, chr2, chr3;\r\n    var enc1, enc2, enc3, enc4;\r\n    var i = 0;\r\n    input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\r\n    while (i < input.length) {\r\n        enc1 = keyString.indexOf(input.charAt(i++));\r\n        enc2 = keyString.indexOf(input.charAt(i++));\r\n        enc3 = keyString.indexOf(input.charAt(i++));\r\n        enc4 = keyString.indexOf(input.charAt(i++));\r\n        chr1 = (enc1 << 2) | (enc2 >> 4);\r\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\r\n        chr3 = ((enc3 & 3) << 6) | enc4;\r\n        output = output + String.fromCharCode(chr1);\r\n        if (enc3 != 64) {\r\n            output = output + String.fromCharCode(chr2);\r\n        }\r\n        if (enc4 != 64) {\r\n            output = output + String.fromCharCode(chr3);\r\n        }\r\n    }\r\n    output = utf8Decode(output);\r\n    return output;\r\n}\r\n\r\nfunction arrayToJson(values) {\r\n    var json = \"\";\r\n    i = 1;\r\n    for (t in values) {\r\n        if (typeof values[t] == \"object\") {\r\n            json += \"\\\"\" + values[t]['id'] + \"\\\" : \\\"\" + values[t]['value'] + \"\\\",\";\r\n            i++;\r\n        } else {\r\n            json += \"\\\"\" + t + \"\\\" : \\\"\" + values[t] + \"\\\",\";\r\n            i++;\r\n        }\r\n    }\r\n    json = json.substring(0, json.lastIndexOf(','));\r\n    json = \"{\" + json + \"}\";\r\n    return json;\r\n}\r\n\r\nfunction checkNeeded(thisInput, ignoredIds, neededIds) {\r\n    ignoredIds || (ignoredIds = []);\r\n    neededIds || (neededIds = []);\r\n    var check = true;\r\n    if (neededIds.length > 0) {\r\n        if ($.inArray(thisInput.attr(\"id\"), neededIds) == -1) check = false;\r\n    }\r\n    if (ignoredIds.length > 0) {\r\n        if ($.inArray(thisInput.attr(\"id\"), ignoredIds) != -1) check = false;\r\n    }\r\n    return check;\r\n}\r\n\r\nfunction getSubmitForm(name, ignoredIds, neededIds) {\r\n    ignoredIds || (ignoredIds = []);\r\n    neededIds || (neededIds = []);\r\n    var form = '';\r\n    if (typeof name == 'string') {\r\n        if (name[0] == '#') {\r\n            form = $(name);\r\n        } else {\r\n            form = $(\"#\" + name);\r\n            if (!form.length) {\r\n                form = $('.' + name);\r\n            }\r\n        }\r\n    } else {\r\n        form = name;\r\n    }\r\n    var data = [];\r\n    form.find(\"input:text\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find('input[type=\"number\"]').each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds)) {\r\n            pushToArray(data, $(this));\r\n        }\r\n    });\r\n    form.find(\"select\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find(\"input:file\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find(\"input:hidden\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find(\"input:password\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find(\"textarea\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds))\r\n            pushToArray(data, $(this));\r\n    });\r\n    form.find(\"input:radio\").each(function (i) {\r\n        if (checkNeeded($(this), ignoredIds, neededIds)) {\r\n            var id = $(this).attr('id');\r\n            if (id != \"\" && id != null) {\r\n                var value = $(\"input[id='\" + id + \"']:checked\").val();\r\n                if (value == undefined) value = \"\";\r\n                value = base64Encode(value);\r\n                var item = {id: id, value: value};\r\n                data.push(item);\r\n            }\r\n        }\r\n    });\r\n    form.find(\"input:checkbox\").each(function (i) {\r\n        var id = $(this).attr('id');\r\n        if (id != \"\" && id != null) {\r\n            var tmp = \"\";\r\n            form.find(\"input[id='\" + id + \"']:checked\").each(function (i) {\r\n                tmp += $(this).val() + \",\";\r\n            });\r\n            tmp = tmp.substring(0, tmp.length - 1);\r\n            tmp = base64Encode(tmp);\r\n            var item = {id: id, value: tmp};\r\n            data.push(item);\r\n        }\r\n    });\r\n    return data;\r\n}\r\n\r\nfunction pushToArray(arr, obj) {\r\n    var id = obj.attr('id');\r\n    var css_class = obj.attr(\"class\");\r\n    var value = null;\r\n    if (id != '' && id != undefined) {\r\n        switch (css_class) {\r\n            case 'datebox-f combo-f':\r\n                value = encodeHTML($(\"#\" + id).datebox('getValue'));\r\n                break;\r\n            case 'combobox-f combo-f':\r\n                value = encodeHTML($(\"#\" + id).combobox('getValue'));\r\n                break;\r\n\r\n            case 'easyui-datebox datebox-f combo-f':\r\n                value = encodeHTML($(\"#\" + id).combobox('getValue'));\r\n                break;\r\n\r\n            case 'tree':\r\n                var nodes = $('#' + id).tree('getChecked');\r\n                var s = '';\r\n                for (var i = 0; i < nodes.length; i++) {\r\n                    if (nodes[i].id != '') {\r\n                        s += nodes[i].id + ',';\r\n                    }\r\n                }\r\n                value = s;\r\n                break;\r\n            default:\r\n                value = obj.val() + '';\r\n                if (obj.attr(\"type\") == 'number') {\r\n                    value = value.replace(',', '.');\r\n                }\r\n                value = encodeHTML(value);\r\n                break;\r\n        }\r\n        value = base64Encode(value);\r\n        var item = {id: id, value: value};\r\n        arr.push(item);\r\n    }\r\n}\r\n\r\n$.alert = function (msg) {\r\n    if (typeof msg != 'string') {\r\n        BootstrapDialog.show(msg);\r\n    } else {\r\n        BootstrapDialog.show({\r\n            title: 'Thông báo',\r\n            message: '<div class=\"col col-right col-md-12 col-sm-12\">' + msg + '</div>',\r\n            buttons: [\r\n                {\r\n                    label: 'Đóng',\r\n                    icon: 'glyphicon glyphicon-log-out',\r\n                    action: function (dialog) {\r\n                        dialog.close();\r\n                    }\r\n                }\r\n            ]\r\n        });\r\n    }\r\n}\r\n\r\nfunction encodeHTML(html) {\r\n    if (html != null)\r\n        return html.replace(/\"/g, \"\\\\'\");\r\n    else\r\n        return \"\";\r\n}\r\n\r\n$(function () {\r\n    $.fn.datebox.defaults.formatter = function (date) {\r\n        var y = date.getFullYear();\r\n        var m = date.getMonth() + 1;\r\n        var d = date.getDate();\r\n        return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;\r\n    };\r\n    $.fn.datebox.defaults.parser = function (s) {\r\n        if (!s) return new Date();\r\n        var ss = s.split('/');\r\n        var d = parseInt(ss[0], 10);\r\n        var m = parseInt(ss[1], 10);\r\n        var y = parseInt(ss[2], 10);\r\n        if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {\r\n            return new Date(y, m - 1, d);\r\n        } else {\r\n            return new Date();\r\n        }\r\n    };\r\n});\r\nvar selectedRow = null;\r\n\r\nfunction process(url, params, success_func, error_func, show_notification, show_loading) {\r\n    if (url.split('api').length == 1) {\r\n        url = url.replace($CFG.remote.base_url + '/', '');\r\n        url = url.replace('doing/', '');\r\n        url = $CFG.remote.base_url + '/doing/' + url;\r\n    }\r\n    if (show_notification == 'false' || show_notification == false) {\r\n        show_notification = false;\r\n    } else {\r\n        show_notification = true;\r\n    }\r\n    var text_status = undefined;\r\n    if (typeof show_loading === 'string' && show_loading != '') {\r\n        text_status = show_loading;\r\n    }\r\n    if (show_loading == 'false' || show_loading == false) {\r\n        show_loading = false;\r\n    } else {\r\n        show_loading = true;\r\n    }\r\n    if (show_loading) {\r\n        statusloading(null, text_status);\r\n    }\r\n    params || (params = {});\r\n    var async = params.async;\r\n    async || (async = false);\r\n    if (async != true && async != 'true') {\r\n        async = false;\r\n    } else {\r\n        async = true;\r\n    }\r\n    delete params.async;\r\n    /*Truyền số người online để*/\r\n    var online = $('#tructuyen').html();\r\n    params._online = online;\r\n    var note = {\r\n        title: 'Thông báo',\r\n        message: '',\r\n        buttons: [{\r\n            label: 'Đóng',\r\n            icon: 'glyphicon glyphicon-log-out',\r\n            action: function (dialog) {\r\n                dialog.close();\r\n            }\r\n        }]\r\n    };\r\n    return $.ajax({\r\n        type: \"POST\",\r\n        url: url,\r\n        async: async,\r\n        data: params,\r\n        timeout: 120000,\r\n        dataType: 'json',\r\n        success: function (resp) {\r\n            if (!resp) {\r\n                if (show_notification != false) {\r\n                    note.message = \"Lỗi\";\r\n                    $.dm_datagrid.show(note);\r\n                }\r\n            } else if (resp.result == 'logouted') {\r\n                var html = resp.html;\r\n                $.dm_datagrid.showAddForm(\r\n                    {\r\n                        module: $CFG.project + '/menu_adjust',\r\n                        action: 'add',\r\n                        title: 'Đăng nhập',\r\n                        size: size.wide,\r\n                        fullScreen: false,\r\n                        showButton: false,\r\n                        content: function (element, dialogRef) {\r\n                            if (resp.html) {\r\n                                $(element).html(resp.html);\r\n                            }\r\n                        }\r\n                    }\r\n                );\r\n            } else if (resp.result == 'success') {\r\n                if (resp.errors != undefined) {\r\n                    var html = [];\r\n                    $.each(resp.errors, function (index, item) {\r\n                        if (typeof item != 'string') {\r\n                            $.each(item, function (i1, value) {\r\n                                html.push('<div> - ' + value + '</div>');\r\n                            })\r\n                        } else {\r\n                            html.push('<div> - ' + item + '</div>');\r\n                        }\r\n                    });\r\n                    if (html.length > 0) {\r\n                        note.message = '<div style=\"max-height:400px;overflow-y:scroll;\">' + html.join(' ') + '</div>';\r\n                        $.dm_datagrid.show(note);\r\n                    }\r\n                }\r\n                $.messager.show({\r\n                    msg: '<i class=\"fa fa-bolt color-orange\" aria-hidden=\"true\" style=\"margin-right: 3px;font-size: 15px;\"></i> Xử lý thành công!',\r\n                    timeout: 1800,\r\n                    showType: 'slide',\r\n                    width: 150,\r\n                    height: 40\r\n                });\r\n            } else if (resp.result == 'fail') {\r\n                if (show_notification != false) {\r\n                    note.message = \"Lỗi xử lý\";\r\n                    $.dm_datagrid.show(note);\r\n                }\r\n            } else {\r\n                if (show_notification != false) {\r\n                    var html = [];\r\n                    if (resp.errors) {\r\n                        $.each(resp.errors, function (index, item) {\r\n                            html.push('<div> - ' + item + '</div>');\r\n                        })\r\n                    }\r\n                    if (html.length == 0) {\r\n                        html.push('Lỗi.');\r\n                    }\r\n                    note.message = html.join(' ');\r\n                    $.dm_datagrid.show(note);\r\n                }\r\n            }\r\n            if (typeof success_func === 'function' && resp.result != 'logouted') {\r\n                success_func(resp);\r\n            }\r\n        },\r\n        error: function () {\r\n            if (show_notification != false) {\r\n                setTimeout(function () {\r\n                    note.message = 'Không xử lý được chức năng';\r\n                    $.dm_datagrid.show(note);\r\n                });\r\n            }\r\n            if (typeof error_func === 'function') {\r\n                error_func();\r\n            }\r\n        }, beforeSend: function () {\r\n        }, complete: function () {\r\n            if (show_loading) {\r\n                statusloadingclose();\r\n            }\r\n        }\r\n    });\r\n}\r\n\r\njQuery.downloadFile = function (url, data) {\r\n    //url and data options required\r\n    if (url) {\r\n        if (data) {\r\n            var form_submit = jQuery('<form action=\"' + url + '\" method=\"post\" style=\"position: fixed; width: 5px; height: 5px; top: -100px;\"></form>');\r\n\r\n            jQuery('body').append(form_submit);\r\n            jQuery.each(data, function (key, val) {\r\n                if (typeof val === 'object') {\r\n                    val = JSON.stringify(val);\r\n                }\r\n                var frm_input = jQuery('<input type=\"hidden\" name=\"' + key + '\" value=\"\" />');\r\n                form_submit.append(frm_input);\r\n                frm_input.val(val);\r\n            });\r\n            $('<input type=\"submit\" value=\"submit\">').appendTo(form_submit);\r\n            form_submit.submit(function () {\r\n                console.log('2222222')\r\n            });\r\n            const a = form_submit.submit();\r\n            console.log(111,a);\r\n\r\n        }\r\n    }\r\n    ;\r\n};\r\n$.loading = {\r\n    show: function (title) {\r\n        var zindex = $(\"body div.window\").css(\"z-index\");\r\n        if (title == undefined) title = \"Tải dữ liệu...\";\r\n        $(\"body\").append('<div id=\"loading\"><div id=\"div-loading\" class=\"loading window\" style=\"background:white;color:black;display: block; width: 180px; z-index: 20001;\"><img src=\"theme/img/loading3.gif\" border=0 align=\"top\"/>&nbsp;' + title + '</div><div class=\"window-mask\" style=\"width: 100%; height: 100%; display: block; z-index: 20000;\"></div></div>');\r\n        $('#div-loading').center();\r\n        $('#loading').show();\r\n    },\r\n    hide: function () {\r\n        $('#div-loading').hide();\r\n        $('#div-loading').remove();\r\n        $('#loading').hide();\r\n        $('#loading').remove();\r\n    }\r\n}\r\n/*Hàm tính nhân chia số thực*/\r\nFLOAT = {\r\n    0: 10,\r\n    1: 100,\r\n    2: 1000,\r\n    3: 10000,\r\n    4: 100000,\r\n    5: 1000000,\r\n    6: 10000000,\r\n    7: 100000000,\r\n    8: 1000000000,\r\n    9: 10000000000,\r\n}\r\n$['*'] = function (a, b) {\r\n    if (isNaN(a)) {\r\n        a = 0;\r\n    }\r\n    if (isNaN(b)) {\r\n        b = 0;\r\n    }\r\n    a += '';\r\n    b += '';\r\n    if ((a + '').split('.').length == 1) {\r\n        a = a + '.0';\r\n    }\r\n    if ((b + '').split('.').length == 1) {\r\n        b = b + '.0';\r\n    }\r\n    var len = a.split('.')[1].length;\r\n    if (len < b.split('.')[1].length) {\r\n        len = b.split('.')[1].length;\r\n    }\r\n    var factor = FLOAT[len];\r\n    if (!factor) {\r\n        factor = 10000000;\r\n    }\r\n    a = Math.round(parseFloat(a) * factor);\r\n    b = Math.round(parseFloat(b) * factor);\r\n    return a * b / factor / factor;\r\n};\r\n$['/'] = function (a, b) {\r\n    if (isNaN(a)) {\r\n        a = 0;\r\n    }\r\n    if (isNaN(b)) {\r\n        b = 0;\r\n    }\r\n    a += '';\r\n    b += '';\r\n    if ((a + '').split('.').length == 1) {\r\n        a = a + '.0';\r\n    }\r\n    if ((b + '').split('.').length == 1) {\r\n        b = b + '.0';\r\n    }\r\n    var len = a.split('.')[1].length;\r\n    if (len < b.split('.')[1].length) {\r\n        len = b.split('.')[1].length;\r\n    }\r\n    var factor = FLOAT[len];\r\n    if (!factor) {\r\n        factor = 10000000;\r\n    }\r\n    a = Math.round(parseFloat(a) * factor);\r\n    b = Math.round(parseFloat(b) * factor);\r\n    return rs = a / b;\r\n};\r\n$['+'] = function (a, b) {\r\n    if (isNaN(a)) {\r\n        a = 0;\r\n    }\r\n    if (isNaN(b)) {\r\n        b = 0;\r\n    }\r\n    a += '';\r\n    b += '';\r\n    if ((a + '').split('.').length == 1) {\r\n        a = a + '.0';\r\n    }\r\n    if ((b + '').split('.').length == 1) {\r\n        b = b + '.0';\r\n    }\r\n    var len = a.split('.')[1].length;\r\n    if (len < b.split('.')[1].length) {\r\n        len = b.split('.')[1].length;\r\n    }\r\n    var factor = FLOAT[len];\r\n    if (!factor) {\r\n        factor = 10000000;\r\n    }\r\n    a = Math.round(parseFloat(a) * factor);\r\n    b = Math.round(parseFloat(b) * factor);\r\n    return (a + b) / factor;\r\n};\r\n$['-'] = function (a, b) {\r\n    if (isNaN(a)) {\r\n        a = 0;\r\n    }\r\n    if (isNaN(b)) {\r\n        b = 0;\r\n    }\r\n    a += '';\r\n    b += '';\r\n    if ((a + '').split('.').length == 1) {\r\n        a = a + '.0';\r\n    }\r\n    if ((b + '').split('.').length == 1) {\r\n        b = b + '.0';\r\n    }\r\n    var len = a.split('.')[1].length;\r\n    if (len < b.split('.')[1].length) {\r\n        len = b.split('.')[1].length;\r\n    }\r\n    var factor = FLOAT[len];\r\n    if (!factor) {\r\n        factor = 10000000;\r\n    }\r\n    a = Math.round(parseFloat(a) * factor);\r\n    b = Math.round(parseFloat(b) * factor);\r\n    return (a - b) / factor;\r\n};\r\n\r\n/*  Chuẩn hóa chuỗi số*/\r\nfunction clearnNumeric(value) {\r\n    var number = ['-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'];\r\n    var type_number = 'int';\r\n    var is_number = false;\r\n    if (typeof value === 'number') {\r\n        is_number = true;\r\n    }\r\n    value += '';\r\n    if (value.split('.') > 1) {\r\n        type_number = 'float';\r\n    }\r\n    value = value.replace(',', '.');\r\n    value = value.replace('..', '.');\r\n    value = value.replace('..', '.');\r\n    value = value.replace('--', '-');\r\n    value = value.replace('--', '-');\r\n    if (value[0] == '.') {\r\n        value = value.substring(1, value.length);\r\n    }\r\n    if (value[value.length - 1] == '-') {\r\n        value = value.substring(0, value.length - 1);\r\n    }\r\n    var rs = '';\r\n    var number_dot = 0;\r\n    for (var i = 0; i < value.length; i++) {\r\n        if (value[i] == '.') {\r\n            number_dot++;\r\n        }\r\n        if (i > 0 && i < value.length - 1 && value[i] == '-' || number_dot > 1 && value[i] == '.') {\r\n\r\n        } else if (in_array(value[i], number)) {\r\n            rs += value[i];\r\n        }\r\n    }\r\n    if (is_number) {\r\n        if (type_number == 'int') {\r\n            value = parseInt(value);\r\n        } else {\r\n            value = parseFloat(value);\r\n        }\r\n    }\r\n    return rs;\r\n}\r\n\r\nfunction absNumeric(value) { /* Trị tuỵet đối dùng hàm có sẵn trong Math sẽ không gõ được dâu chấm ở cuối nếu là số */\r\n    var type_number = 'int';\r\n    var is_number = false;\r\n    if (typeof value === 'number') {\r\n        is_number = true;\r\n    }\r\n    value += '';\r\n    if (value.split('.') > 1) {\r\n        type_number = 'float';\r\n    }\r\n    while (value[0] == '-') {\r\n        value = value.substring(1, value.length);\r\n    }\r\n    if (is_number) {\r\n        if (type_number == 'int') {\r\n            value = parseInt(value);\r\n        } else {\r\n            value = parseFloat(value);\r\n        }\r\n    }\r\n    return value;\r\n}\r\n\r\nfunction toFloat(value) {\r\n    value += '';\r\n    var arr = value.replace(',', '.').split('.');\r\n    var rs = parseInt(arr[0]);\r\n    if (arr.length == 2) {\r\n        var d = 10;\r\n        for (var i = 0; i < arr[1].length; i++) {\r\n            var v = parseInt(arr[1][i]);\r\n            rs += v / d;\r\n            d *= 10;\r\n        }\r\n    }\r\n    return rs;\r\n}\r\n\r\nfunction round(value, num, get_default) {\r\n    if (get_default == undefined) {\r\n        get_default = 0;\r\n    }\r\n    if (value + '' == 'NaN' || value == undefined) {\r\n        value = get_default;\r\n    } else {\r\n        if (num === undefined || num === \"\") {\r\n            if ($CFG) {\r\n                num = $CFG.round_number_config;\r\n            }\r\n        }\r\n        if (num === undefined || num === \"\") {\r\n            num = 3;\r\n        }\r\n        var per = 1;\r\n        for (var i = 0; i < num; i++) {\r\n            per *= 10;\r\n        }\r\n\r\n        if (typeof value != 'number') {\r\n            value = parseFloat(value);\r\n        }\r\n        value = Math.round(value * per) / per;\r\n        if (value + '' === '0') {\r\n            value = get_default;\r\n        }\r\n    }\r\n    return value;\r\n}\r\n\r\nfunction myformatter(date) {\r\n    var y = date.getFullYear();\r\n    var m = date.getMonth() + 1;\r\n    var d = date.getDate();\r\n    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;\r\n}\r\n\r\nfunction myparser(s) {\r\n    if (!s) return new Date();\r\n    if (typeof s === 'object') {\r\n        return s;\r\n    }\r\n    var ss = (s.split('/'));\r\n    var y = parseInt(ss[0], 10);\r\n    var m = parseInt(ss[1], 10);\r\n    var d = parseInt(ss[2], 10);\r\n    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {\r\n        return new Date(d, m - 1, y);\r\n    } else {\r\n        return new Date();\r\n    }\r\n}\r\n\r\nfunction dateparser(s) {\r\n    if (!s) return new Date();\r\n    var ss = (s.split('/'));\r\n    var d = parseInt(ss[0], 10);\r\n    var m = parseInt(ss[1], 10);\r\n    var y = parseInt(ss[2], 10);\r\n    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {\r\n        return new Date(y, m - 1, d);\r\n    } else {\r\n        return new Date();\r\n    }\r\n}\r\n\r\nfunction converdate(s) {\r\n    if (!s) return new Date();\r\n    var ss = (s.split('/'));\r\n    var y = parseInt(ss[0], 10);\r\n    var m = parseInt(ss[1], 10);\r\n    var d = parseInt(ss[2], 10);\r\n    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {\r\n        return new Date(y, m - 1, d);\r\n    } else {\r\n        return new Date();\r\n    }\r\n}\r\n\r\nfunction $NaN(value, end) {\r\n    end || (end = '');\r\n    if (value + '' == 'NaN') {\r\n        return end;\r\n    }\r\n    return value;\r\n}\r\n\r\nfunction $Infinity(value, ext) {\r\n    ext || (ext = 0);\r\n    if (value + '' == 'Infinity') {\r\n        value = ext;\r\n    }\r\n    return value;\r\n}\r\n\r\nfunction dateboxOnSelect(date) {\r\n    if (!date) {\r\n        date = new Date();\r\n    }\r\n    if (typeof date.getFullYear === 'function') {\r\n        var y = date.getFullYear();\r\n        var m = date.getMonth() + 1;\r\n        var d = date.getDate();\r\n        var val = (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;\r\n        $(this).val(val);\r\n        return val;\r\n    }\r\n    return date;\r\n}\r\n\r\nfunction monthNow() {\r\n    var date = new Date();\r\n    var m = date.getMonth() + 1;\r\n    return m.toString();\r\n}\r\n\r\nfunction array_del_value($array, $value) {\r\n    var res = null;\r\n    if ($.type($array) === 'array') {\r\n        res = [];\r\n    }\r\n    if ($.type($array) === 'object') {\r\n        res = {};\r\n    }\r\n    if (res != null) {\r\n        $.each($array, function (index, item) {\r\n            if (item != $value) {\r\n                if ($.type($array) === 'array') {\r\n                    res.push(item);\r\n                }\r\n                if ($.type($array) === 'object') {\r\n                    res[index] = item;\r\n                }\r\n            }\r\n        });\r\n    } else {\r\n        res = $array;\r\n    }\r\n    return res;\r\n}\r\n\r\nfunction array_del_key($array, $value) {\r\n    var res = null;\r\n    if ($.type($array) === 'array') {\r\n        res = [];\r\n    }\r\n    if ($.type($array) === 'object') {\r\n        res = {};\r\n    }\r\n    if (res != null) {\r\n        $.each($array, function (index, item) {\r\n            if (index != $value) {\r\n                if ($.type($array) === 'array') {\r\n                    res.push(item);\r\n                }\r\n                if ($.type($array) === 'object') {\r\n                    res[index] = item;\r\n                }\r\n            }\r\n        });\r\n    } else {\r\n        res = $array;\r\n    }\r\n    return res;\r\n}\r\n\r\nfunction in_array($value, $array) {\r\n    var res = false;\r\n    $.each($array, function (index, item) {\r\n        if (item == $value) {\r\n            res = true;\r\n            return true;\r\n        }\r\n    });\r\n    return res;\r\n}\r\n\r\nfunction indexOf_array($value, $array) {\r\n    var res = false;\r\n    if ($value) {\r\n        $value += '';\r\n        for (var i in $array) {\r\n            if ($value.toLowerCase().indexOf($array[i].toLowerCase()) >= 0) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\nfunction in_array_key($value, $array) {\r\n    var res = false;\r\n    $.each($array, function (index, item) {\r\n        if (index + '' == $value + '') {\r\n            res = true;\r\n            return;\r\n        }\r\n    });\r\n    return res;\r\n}\r\n\r\nfunction createLinkButton($script, $title, $iconclass, $text) {\r\n    var html = '<a id=\"linkbutton\" class=\"easyui-linkbutton l-btn l-btn-small l-btn-plain\" plain=\"true\" '\r\n        + ' onclick=\"' + $script + '\" href=\"javascript:void(0)\" group=\"\" title=\"' + $title + '\">'\r\n        + '<span class=\"l-btn-left l-btn-icon-left\">'\r\n        + '<span class=\"l-btn-text\">' + $text + '</span>'\r\n        + '<span class=\"l-btn-icon ' + $iconclass + '\"> </span>'\r\n        + '</span>'\r\n        + '</a>';\r\n    return html;\r\n}\r\n\r\nfunction formatdate(value) {\r\n    if (value) {\r\n        value = value.split(' ')[0];\r\n        var d = value.split(\"-\");\r\n        return d[2] + \"/\" + d[1] + \"/\" + d[0];\r\n    } else {\r\n        return '';\r\n    }\r\n}\r\n\r\nfunction getLoadingHTML(msg, type) {\r\n    if (!type) {\r\n        var num = Math.floor((Math.random() * 10) + 1);\r\n        if (num == 0) {\r\n            num = 1;\r\n        } else if (num > 4) {\r\n            num = num - 4;\r\n        }\r\n        type = num;\r\n    } else if (type > 4) {\r\n        type = 4;\r\n    }\r\n    msg = '<div class=\"spinner-container spinner'\r\n        + type + '\"><div id=\"time-progress-bar\" style=\"width:28px;text-align:center;padding-top:7px\"></div></div>'\r\n        + '<span style=\"position: absolute; padding: 5px;\">' + (msg ? msg : 'Đang tải ...') + '</span>';\r\n    return msg;\r\n}\r\n\r\nfunction getErrorHTML() {\r\n    html = '<div><span style=\"padding: 5px;color: #ff8700;\" title=\"Lỗi khi tải dữ liệu\" class=\"glyphicon glyphicon-warning-sign\"></span></div>';\r\n    return html;\r\n}\r\nfunction decodeHTML(encodedStr) {\r\n    return $(\"<div/>\").html(encodedStr).text();\r\n}\r\nfunction removeUnicode(str) {\r\n    if (typeof str != 'string') {\r\n        return str;\r\n    }\r\n    str = str.toLowerCase();\r\n    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, \"a\");\r\n    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, \"e\");\r\n    str = str.replace(/ì|í|ị|ỉ|ĩ/g, \"i\");\r\n    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, \"o\");\r\n    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, \"u\");\r\n    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, \"y\");\r\n    str = str.replace(/đ/g, \"d\");\r\n    str = str.replace(/!|@|%|\\^|\\*|\\(|\\)|\\+|\\=|\\<|\\>|\\?|\\/|,|\\.|\\:|\\;|\\'|\\\"|\\&|\\#|\\[|\\]|~|$|_/g, \"\");\r\n    str = str.replace(/-+-/g, \" \");\r\n    str = str.replace(/^\\-+|\\-+$/g, \" \");\r\n    return str;\r\n}\r\nfunction setCookie(c_name, value, exdays) {\r\n    exdays || (exdays = 60 * 60 * 24);\r\n    var exdate = new Date();\r\n    exdate.setDate(exdate.getDate() + exdays);\r\n    var c_value = escape(value) + ((exdays == null) ? \"\" : \"; expires=\" + exdate.toUTCString());\r\n    document.cookie = c_name + \"=\" + c_value;\r\n};\r\n\r\nfunction getCookie(c_name) {\r\n    var i, x, y, ARRcookies = document.cookie.split(\";\");\r\n    for (i = 0; i < ARRcookies.length; i++) {\r\n        x = ARRcookies[i].substr(0, ARRcookies[i].indexOf(\"=\"));\r\n        y = ARRcookies[i].substr(ARRcookies[i].indexOf(\"=\") + 1);\r\n        x = x.replace(/^\\s+|\\s+$/g, \"\");\r\n        if (x == c_name) {\r\n            return unescape(y);\r\n        }\r\n    }\r\n}\r\n\r\nfunction showSpinner(id) {\r\n    if (id === 'tmp_layout') {\r\n        dialogCloseAll();\r\n    }\r\n    if (!$CFG.spinController) {\r\n        $CFG.spinController = {}\r\n    }\r\n    $CFG.spinController[id] = true;\r\n}\r\n\r\nfunction hiddenSpinner(id) {\r\n    delete $CFG.spinController[id];\r\n}\r\n\r\nfunction setCacheProcess(id, msg) {\r\n    statusloadingCache || (statusloadingCache = {});\r\n    id || (id = Math.random());\r\n    statusloadingCache[id] = msg;\r\n    return id;\r\n}\r\n\r\nfunction delCacheProcess(delAll) {\r\n    if (delAll) {\r\n        statusloadingCache = {};\r\n    } else {\r\n        if (count(statusloadingCache) > 0) {\r\n            delete statusloadingCache[Object.keys(statusloadingCache)[Object.keys(statusloadingCache).length - 1]];\r\n        }\r\n    }\r\n}\r\n\r\nstatusloading = function (id, msg) {\r\n    setCacheProcess(id, msg);\r\n    setTimeout(function () {\r\n        var thongbao = 'Đang xử lý...';\r\n        if (typeof msg === 'string') {\r\n            thongbao = msg;\r\n        }\r\n        $('#statusloading-mash').show().find('.spinner-text').html(thongbao);\r\n    }, 0);\r\n}\r\nstatusloadingclose = function (closeAll) {\r\n    angular.element(document).ready(function () {\r\n        delCacheProcess(closeAll);\r\n        if (count(statusloadingCache) == 0) {\r\n            setTimeout(function () {\r\n                $('#statusloading-mash').hide();\r\n            }, 100);\r\n        }\r\n    });\r\n}\r\ninitStatusLoading = function () {\r\n    var html = '<div id=\"statusloading-mash\" style=\"display: none;\"><div class=\"spinner-container-loading\"><div class=\"spinner-container btn-hover spinner3\" id=\"spinner-container\"> <div id=\"time-progress-bar\"></div></div><span class=\"spinner-text\">Đang xử lý ...</span></div></div>';\r\n    var spinner = $('#statusloading-mash');\r\n    if (!spinner.length) {\r\n        spinner = $(html);\r\n        $('body').append(spinner);\r\n    }\r\n    spinner.children().click(function () {\r\n        spinner.css({display: 'none'});\r\n        statusloadingCache = {};\r\n    })\r\n}\r\n\r\nfunction number_stand(value) {\r\n    value || (value = 0);\r\n    value += '';\r\n    var tmp = value.split('.');\r\n    if (tmp.length == 1) {\r\n        value = parseInt(tmp[0]) + '';\r\n    } else {\r\n        tmp[0] = parseInt(tmp[0]);\r\n        value = tmp.join('.');\r\n    }\r\n    return value;\r\n}\r\n\r\nfunction digit_grouping(value, ext) {\r\n    $CFG || ($CFG = {});\r\n    $CFG.digit_grouping_char || ($CFG.digit_grouping_char = ',');\r\n    var phancach = $CFG.digit_grouping_char;\r\n    var thapphan = '.';\r\n    phancach || (phancach == ',')\r\n    if (phancach == '.') {\r\n        thapphan = ',';\r\n    }\r\n    if (ext == undefined) {\r\n        ext = 0;\r\n    }\r\n    if (value == '' || value + '' === '0' || isNaN(value)) {\r\n        return ext;\r\n    }\r\n    var num = 3;\r\n    value || (value = 0);\r\n    value += '';\r\n    var test_symboy = (3 / 2) + '';\r\n    var symboy = '.';\r\n    var smb_float = ',';\r\n    if (test_symboy.split('.').length > 1) {\r\n        symboy = ',';\r\n        smb_float = '.';\r\n    }\r\n    if (value.length <= 3) {\r\n        value = value.replace(smb_float, thapphan);\r\n        return value;\r\n    } else {\r\n        if (value.split(smb_float).length == 2) {\r\n            if (value.split(smb_float)[0].length <= 3 && value.split(smb_float)[1].length <= 3) {\r\n                value = value.replace(smb_float, thapphan);\r\n                return value;\r\n            }\r\n        }\r\n    }\r\n    var dau = '';\r\n    if (value.split('-').length > 1) {\r\n        dau = '-';\r\n        value = value.replace('-', '');\r\n    }\r\n    value = value.split(smb_float);\r\n    var tmp = value[0];\r\n    if (value.length == 1) {\r\n        tmp = parseInt(value[0]) + '';\r\n    }\r\n    if (tmp.length > num) {\r\n        var arrn = [];\r\n        var d = 0;\r\n        for (var i = tmp.length - 1; i >= 0; i--) {\r\n            d++;\r\n            arrn.push(tmp[i]);\r\n            if (d % num == 0 && i != 0) {\r\n                arrn.push(phancach);\r\n            }\r\n        }\r\n        value[0] = '';\r\n        for (var i = arrn.length - 1; i >= 0; i--) {\r\n            value[0] += arrn[i];\r\n        }\r\n    }\r\n    return dau + value.join(thapphan);\r\n}\r\n\r\nfunction clone(obj) {\r\n    if (typeof obj === 'object') {\r\n        return JSON.parse(JSON.stringify(obj));\r\n    }\r\n}\r\n\r\n/* Xử lý đọc số thành chữ */\r\nvar mangso = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];\r\n\r\nfunction dochangchuc(so, daydu) {\r\n    var chuoi = \"\";\r\n    chuc = Math.floor(so / 10);\r\n    donvi = so % 10;\r\n    if (chuc > 1) {\r\n        chuoi = \" \" + mangso[chuc] + \" mươi\";\r\n        if (donvi == 1) {\r\n            chuoi += \" mốt\";\r\n        }\r\n    } else if (chuc == 1) {\r\n        chuoi = \" mười\";\r\n        if (donvi == 1) {\r\n            chuoi += \" một\";\r\n        }\r\n    } else if (daydu && donvi > 0) {\r\n        chuoi = \" lẻ\";\r\n    }\r\n    if (donvi == 5 && chuc > 1) {\r\n        chuoi += \" lăm\";\r\n    } else if (donvi > 1 || (donvi == 1 && chuc == 0)) {\r\n        chuoi += \" \" + mangso[donvi];\r\n    }\r\n    return chuoi;\r\n}\r\n\r\nfunction docblock(so, daydu) {\r\n    var chuoi = \"\";\r\n    tram = Math.floor(so / 100);\r\n    so = so % 100;\r\n    if (daydu || tram > 0) {\r\n        chuoi = \" \" + mangso[tram] + \" trăm\";\r\n        chuoi += dochangchuc(so, true);\r\n    } else {\r\n        chuoi = dochangchuc(so, false);\r\n    }\r\n    return chuoi;\r\n}\r\n\r\nfunction dochangtrieu(so, daydu) {\r\n    var chuoi = \"\";\r\n    trieu = Math.floor(so / 10000);\r\n    so = so % 10000;\r\n    if (trieu > 0) {\r\n        chuoi = docblock(trieu, daydu) + \" triệu\";\r\n        daydu = true;\r\n    }\r\n    nghin = Math.floor(so / 1000);\r\n    so = so % 1000;\r\n    if (nghin > 0) {\r\n        chuoi += docblock(nghin, daydu) + \" nghìn\";\r\n        daydu = true;\r\n    }\r\n    if (so > 0) {\r\n        chuoi += docblock(so, daydu);\r\n    }\r\n    return chuoi;\r\n}\r\n\r\nfunction convert_number_to_words(so) {\r\n    if (so == 0) return mangso[0];\r\n    var chuoi = \"\", hauto = \"\", tiento = ' ', phancach = ' ';\r\n    var list = [];\r\n    if ((so + '').split('.').length > 1) {\r\n        phancach = ' phảy';\r\n        return convert_number_to_words((so + '').split('.')[0]) + phancach + convert_number_to_words((so + '').split('.')[1]);\r\n    } else if ((so + '').split('-').length > 1) {\r\n        tiento = 'Âm';\r\n        return tiento + convert_number_to_words((so + '').split('-')[1]);\r\n    } else {\r\n        do {\r\n            ty = so % 10000000;\r\n            so = Math.floor(so / 10000000);\r\n            if (so > 0) {\r\n                chuoi = dochangtrieu(ty, true) + hauto + chuoi;\r\n            } else {\r\n                chuoi = dochangtrieu(ty, false) + hauto + chuoi;\r\n            }\r\n            hauto = \" tỷ\";\r\n        } while (so > 0);\r\n        return chuoi;\r\n    }\r\n    return chuoi;\r\n}\r\n\r\nfunction getURLParameter(sParam) {\r\n    var sPageURL = window.location.search.substring(1);\r\n    var sURLVariables = sPageURL.split('&');\r\n    for (var i = 0; i < sURLVariables.length; i++) {\r\n        var sParameterName = sURLVariables[i].split('=');\r\n        if (sParameterName[0] == sParam) {\r\n            return sParameterName[1];\r\n        }\r\n    }\r\n}\r\n\r\nfunction replaceUrlParam(url, paramName, paramValue) {\r\n    if (paramValue == null) {\r\n        paramValue = '';\r\n    }\r\n    var pattern = new RegExp('\\\\b(' + paramName + '=).*?(&|#|$)');\r\n    if (url.search(pattern) >= 0) {\r\n        return url.replace(pattern, '$1' + paramValue + '$2');\r\n    }\r\n    url = url.replace(/[?#]$/, '');\r\n    return url + (url.indexOf('?') > 0 ? '&' : '?') + paramName + '=' + paramValue;\r\n}\r\n\r\nfunction exportExcel() {\r\n    var cols = []; /* Init data */\r\n    var colsData = $(\"[data-col]\");\r\n    var lengthCol = colsData.length;\r\n    for (var c = 0; c < lengthCol; c++) {\r\n        if (colsData[c].id !== 'undefined') {\r\n            var colId = colsData[c].id;\r\n            var colElement = document.getElementById(colId);\r\n            var colValue = colElement.getAttribute(\"data-col\");\r\n            var colCell = colElement.getAttribute(\"data-cells\");\r\n            cols.push({key: colCell, value: colValue});\r\n        }\r\n    }\r\n\r\n    var cellsExcel = []; /* Init data */\r\n    var cellsData = $(\"[data-name='cell']\");\r\n    var lengthCell = cellsData.length;\r\n    for (var i = 0; i < lengthCell; i++) {\r\n        if (cellsData[i].id !== 'undefined') {\r\n            var cellId = cellsData[i].id;\r\n            var cellElement = document.getElementById(cellId);\r\n            var cellValue = cellElement.innerText;\r\n            if (cellElement.type === 'text') {\r\n                cellValue = cellElement.value;\r\n            }\r\n            var cellsExcelId = cellId.replace(\"cell-insert.\", \"\");\r\n            cellsExcel.push({key: cellsExcelId, value: cellValue});\r\n        }\r\n    }\r\n\r\n    var rowsExcel = []; /* Init data */\r\n    var rowsData = $(\"[data-name='row']\");\r\n    var lengthRow = rowsData.length;\r\n    for (var j = 0; j < lengthRow; j++) {\r\n        if (rowsData[j].id !== 'undefined') {\r\n            var rowId = rowsData[j].id;\r\n            var rowElement = document.getElementById(rowId);\r\n            var rowValue = rowElement.innerText;\r\n            if (rowElement.type === 'text') {\r\n                rowValue = rowElement.value;\r\n            }\r\n            var rowsExcelId = rowId.replace(\"row-insert.\", \"\");\r\n            rowsExcel.push({key: rowsExcelId, value: rowValue});\r\n        }\r\n    }\r\n\r\n    var style = []; /* Init data style */\r\n    var styleData = $(\"[data-cells]\");\r\n    var lengthStyle = styleData.length;\r\n    for (var s = 0; s < lengthStyle; s++) {\r\n        if (styleData[s].id !== 'undefined') {\r\n            var styleId = styleData[s].id;\r\n            var styleElement = document.getElementById(styleId);\r\n            var styleCell = styleElement.getAttribute(\"data-cells\");\r\n            var styleOptions = styleElement.getAttribute(\"data-options\");\r\n            var splitOptions = styleOptions.split(\",\");\r\n\r\n            /*font*/\r\n            var font = {};\r\n            if (splitOptions.indexOf('bold') !== -1)\r\n                font['bold'] = true;\r\n            if (splitOptions.indexOf('italic') !== -1)\r\n                font['italic'] = true;\r\n            var tmp = {key: styleCell, font: font};\r\n\r\n            /*merge*/\r\n            if (splitOptions.indexOf('merge') !== -1)\r\n                tmp['merge'] = true;\r\n\r\n            /*alignment*/\r\n            if (splitOptions.indexOf('center') !== -1)\r\n                tmp['alignment'] = {'center': true};\r\n            if (splitOptions.indexOf('left') !== -1)\r\n                tmp['alignment'] = {'left': true};\r\n            if (splitOptions.indexOf('right') !== -1)\r\n                tmp['alignment'] = {'right': true};\r\n            style.push(tmp);\r\n        }\r\n    }\r\n    return {\r\n        cells: JSON.stringify(cellsExcel),\r\n        rows: JSON.stringify(rowsExcel),\r\n        style: JSON.stringify(style),\r\n        cols: JSON.stringify(cols),\r\n    };\r\n\r\n}\r\n\r\n\r\n/*Excel new*/\r\n\r\n/* attribute: data-row */\r\nfunction getDataExcelByAttribute(attribute) {\r\n    var data = [];\r\n    var selectors = $('[' + attribute + ']');\r\n    var length = selectors.length;\r\n    for (var i = 0; i < length; i++) {\r\n        var selector = selectors[i];\r\n        var cell = selector.getAttribute(attribute);\r\n        var value = selector.innerText;\r\n        if (selector.type === 'text') {\r\n            value = selector.value;\r\n        }\r\n        data.push({cell: cell, value: value})\r\n    }\r\n    return data;\r\n}\r\n\r\nfunction getDate(strDate) {\r\n    var date = new Date();\r\n    if (strDate)\r\n        date = new Date(strDate);\r\n    var y = date.getFullYear();\r\n    var m = date.getMonth() + 1;\r\n    var d = date.getDate();\r\n    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;\r\n}\r\n\r\nfunction getCurrentDate(strDate) {\r\n    var date = new Date();\r\n    if (strDate)\r\n        date = new Date(strDate);\r\n    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);\r\n    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);\r\n\r\n    var first = getDayMonthYear(firstDay);\r\n    var now = getDayMonthYear(date);\r\n    var last = getDayMonthYear(lastDay);\r\n\r\n    return {\r\n        first: first.day + '/' + first.month + '/' + first.year,\r\n        now: now.day + '/' + now.month + '/' + now.year,\r\n        last: last.day + '/' + last.month + '/' + last.year,\r\n    };\r\n}\r\n\r\nfunction getDayMonthYear(dateObject) {\r\n    var y = dateObject.getFullYear();\r\n    var m = dateObject.getMonth() + 1;\r\n    var d = dateObject.getDate();\r\n    d = (d < 10 ? ('0' + d) : d);\r\n    m = (m < 10 ? ('0' + m) : m);\r\n    return {\r\n        day: d,\r\n        month: m,\r\n        year: y\r\n    };\r\n}\r\n\r\n$.angularComplie = function (element, html, callback) { /* Biên dịch html cho popup để chạy angularjs*/\r\n    var form = '<div >' + html + '</div>';\r\n    // $(element).html(form);\r\n    angular.element($(element)).scope().$apply(function (scope) {\r\n        $(element).html(scope.compile(form, scope));\r\n        if (typeof callback === 'function') {\r\n            callback(scope);\r\n        }\r\n    });\r\n};", "angular_app = angular.module(\"angular_app\",['ngRoute','ngResource','ngCookies','ngAnimate','textAngular', 'ngStorage'])\r\n.config(['$routeProvider','$locationProvider',function ($routeProvider,$locationProvider) {\r\n    $locationProvider.html5Mode(true);\r\n    $routeProvider\r\n    .when('/dinhduong', {\r\n        template: ''+'<input type=\"hidden\" value=\"\">',\r\n        controller: function($scope) {\r\n            $scope.menu.page = 'index';\r\n            $scope.$CFG.spinController = {};\r\n            dialogCloseAll();\r\n            /*Chạy biểu tượng tổng đâì hỗ trợ xuống góc bên phải*/\r\n            angular.element(document).ready(function(){\r\n                setTimeout(function(){\r\n                    $('.content-info-help').addClass('content-info-help-running');\r\n                },200);\r\n            })\r\n        }\r\n    })\r\n    .when('/dinhduong/view/:module', {\r\n        templateUrl: function(request){\r\n            statusloading('tmp_layout');\r\n            var urls = [\r\n                $CFG.remote.base_url,\r\n                $CFG.project,\r\n                request.module,\r\n                'list.html'\r\n            ];\r\n            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];\r\n        },\r\n        controller: function($scope,$routeParams) {\r\n            $scope.menu.page = 'children';\r\n            statusloadingclose('tmp_layout');\r\n        }\r\n    })\r\n    .when('/dinhduong/view/:module/:action', {\r\n        templateUrl: function(request){\r\n            console.log(request);\r\n            statusloading('tmp_layout');\r\n            var urls = [\r\n                $CFG.remote.base_url,\r\n                'templates',\r\n                $CFG.project,\r\n                request.module,\r\n                request.action+'.html'\r\n            ];\r\n            delete request.module;\r\n            delete request.action;\r\n            var req = ['?_='+Math.random().toString().split('.')[1]];\r\n            for(var key in request) {\r\n                req.push(key+'='+request[key]);\r\n            }\r\n            return urls.join('/')+req.join('&');\r\n        },\r\n        controller: function($scope,$routeParams) {\r\n            $scope.menu.page = 'children';\r\n            statusloadingclose();\r\n        }\r\n    })\r\n    .when('/dinhduong/:module/:action', {\r\n        templateUrl: function(request){\r\n            statusloading('tmp_layout');\r\n            var urls = [\r\n                $CFG.remote.base_url,\r\n                $CFG.project,\r\n                request.module,\r\n                request.action\r\n            ];\r\n            return urls.join('/')+'/'+Math.random().toString().split('.')[1];\r\n        },\r\n        controller: function($scope,$routeParams) {\r\n            $scope.menu.page = 'children';\r\n            statusloadingclose();\r\n        }\r\n    })\r\n    .when('/dinhduong/:module', {\r\n        templateUrl: function(request){\r\n            statusloading('tmp_layout');\r\n            var urls = [\r\n                $CFG.remote.base_url,\r\n                $CFG.project,\r\n                request.module,\r\n                'list'\r\n            ];\r\n            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];\r\n        },\r\n        controller: function($scope) {\r\n            $scope.menu.page = 'children';\r\n            statusloadingclose('tmp_layout');\r\n        }\r\n    })\r\n    .when('/dinhduong/:module/:action/:id', {\r\n        templateUrl: function(request){\r\n            statusloading('tmp_layout');\r\n            var urls = [\r\n                $CFG.remote.base_url,\r\n                $CFG.project,\r\n                request.module,\r\n                request.action,\r\n                request.id\r\n            ];\r\n            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];\r\n        },\r\n        controller: function($scope,$routeParams) {\r\n            $scope.menu.page = 'children';\r\n            statusloadingclose('tmp_layout');\r\n        }\r\n    })\r\n    .otherwise({redirectTo:'/dinhduong'})\r\n}]).config(['$compileProvider', function($compileProvider){\r\n    $compileProvider.debugInfoEnabled(true);\r\n}]);", "angular_app.controller('appController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', function ($scope, $routeParams, $compile, MyCache, $filter, $cookies) {\r\n    $scope.$CFG = $CFG;\r\n    $scope.namhoc = parseInt($CFG.namhoc);\r\n    $scope.$CFG || ($scope.$CFG = {});\r\n    $scope.$CFG.round_number_config || ($scope.$CFG.round_number_config = 3);\r\n    $scope.$CFG.digit_grouping_char || ($scope.$CFG.digit_grouping_char = ',');\r\n    $scope.window = {\r\n        screen: {\r\n            width: $(window).width(),\r\n            height: $(window).height()\r\n        }\r\n    };\r\n    $(window).resize(function () {\r\n        screen_size = {\r\n            width: $(window).width(),\r\n            height: $(window).height()\r\n        };\r\n        setTimeout(function () {\r\n            $scope.$apply(function () {\r\n                $scope.window.screen = screen_size;\r\n            });\r\n        });\r\n    });\r\n\r\n    $scope.filter = function (arr, key) {\r\n        return $filter('filter')(arr, key);\r\n    };\r\n    $scope.rand = function (min, max) {\r\n        return rand(min, max);\r\n    };\r\n    $scope.compile = function (html, scope) {\r\n        if (scope) {\r\n            return $compile(html)(scope);\r\n        } else {\r\n            return $compile(html)($scope);\r\n        }\r\n    };\r\n    $scope.parseInt = function (value) {\r\n        value || (value = 0);\r\n        return parseInt(value);\r\n    };\r\n    $scope.parseFloat = function (value) {\r\n        value || (value = 0);\r\n        return parseFloat(value);\r\n    };\r\n    $scope.NaN = function (value, resp) {\r\n        if (resp == undefined) {\r\n            resp = '';\r\n        }\r\n        if (value + '' == 'NaN') {\r\n            value = resp;\r\n        }\r\n        return value;\r\n    };\r\n    $scope.digit_grouping = function (value, num) {\r\n        return digit_grouping(value, num);\r\n    };\r\n    $scope.number_stand = function (value) {\r\n        return number_stand(value);\r\n    };\r\n    $scope.round = function (value, num, get_default) {\r\n        return round(value, num, get_default);\r\n    };\r\n    $scope.safeRound = function (val) {\r\n        val = Number(val);\r\n        if (val < 0.0005) {\r\n            val = 0.001;\r\n        } else if (val < 0.005) {\r\n            val = 0.01;\r\n        } else if (val <= 0.07) {\r\n            val = round(val, 2);\r\n        } else {\r\n            val = round(val, 1);\r\n        }\r\n        return val;\r\n    };\r\n\r\n    $scope.divide = function (total, values, notsaferound) {\r\n        var all = 0;\r\n        var rs = {};\r\n        angular.forEach(values, function (val, key) {\r\n            rs[key] = 0;\r\n            all += val;\r\n        });\r\n        if (total != all) {\r\n            tong = 0;\r\n            var max_val = {point: 1, value: 0};\r\n            var min_val = {point: 1, value: 1000000};\r\n            angular.forEach(values, function (sotre, point) {\r\n                if (sotre == 0) {\r\n                    rs[point] = 0;\r\n                    return;\r\n                }\r\n                var tile = sotre / all;\r\n                var val = $['*'](tile, total);\r\n                if (!notsaferound) {\r\n                    val = $scope.safeRound(val);\r\n                } else {\r\n                    val = round(val, 3);\r\n                }\r\n                tong = $['+'](tong, val);\r\n                rs[point] = val;\r\n                if (max_val.value < val) {\r\n                    max_val = {point: point, value: val};\r\n                }\r\n                if (min_val.value > val) {\r\n                    min_val = {point: point, value: val};\r\n                }\r\n            });\r\n            if (total != tong) {\r\n                if (total - tong > 0) {\r\n                    rs[min_val.point] = $['+'](rs[min_val.point], $['-'](total, tong));\r\n                } else {\r\n                    rs[max_val.point] = $['+'](rs[max_val.point], $['-'](total, tong));\r\n                }\r\n            }\r\n        } else {\r\n            rs = values;\r\n        }\r\n        return rs;\r\n    };\r\n    /*\r\n        * Mở form hướng dẫn tắt cảnh báo lỗi khi mở tệp excel được tải trực tiếp ở form in\r\n        * */\r\n    $scope.formHelperFixErrorOpenExcel = function () {\r\n        $.dm_datagrid.showAddForm({\r\n            title: 'Thông báo',\r\n            size: size.wide,\r\n            fullScreen: true,\r\n            showButton: false,\r\n            draggable: true,\r\n            content: function (dialog) {\r\n                loadForm($CFG.remote.base_url + '/templates/dinhduong/helper-fix-error-open-excel.html', '', {async: true}, function (resp) {\r\n                    $scope.$apply(function () {\r\n                        $(dialog).html($scope.compile(resp, $scope));\r\n                    });\r\n                });\r\n            }\r\n        });\r\n    };\r\n    /*\r\n    * Chia tỉ lệ theo chiều dọc danh sách thực phẩm xuất kho nhiều giá\r\n    * */\r\n    $scope.divideH = function (vals, exps) {\r\n        var tmp = clone(exps);\r\n        var mau = {};\r\n        var i = 1;\r\n        angular.forEach(exps, function (food, food_id_price) {\r\n            mau[i] = food.quantity;\r\n            food.quantities = {};\r\n            i++;\r\n        });\r\n        angular.forEach(vals, function (val, point) {\r\n            var i = 1;\r\n            var arr = $scope.divide(val, mau);\r\n            angular.forEach(exps, function (food, food_id_price) {\r\n                if (val == 0) {\r\n                    food.quantities[point] = 0;\r\n                } else {\r\n                    food.quantities[point] = arr[i];\r\n                    i++;\r\n                }\r\n            });\r\n        });\r\n        return exps;\r\n    };\r\n    $scope.mathRound = function (value) {\r\n        return Math.round(value);\r\n    };\r\n    $scope.Infinity = function (value, ext) {\r\n        return $Infinity(value, ext);\r\n    };\r\n    $scope.count = function (arr) {\r\n        return count(arr);\r\n    };\r\n    $scope.sum = function (arr) {\r\n        var rs = 0;\r\n        for (var i in arr) {\r\n            rs += Number(arr[i]);\r\n        }\r\n        return rs;\r\n    };\r\n    $scope.num2array = function (num) {\r\n        var rs = [];\r\n        if (typeof num == 'string') {\r\n            num = Number(num);\r\n        }\r\n        if (typeof num == 'number') {\r\n            for (var i = 0; i < num; i++) {\r\n                rs.push(i + 1);\r\n            }\r\n        } else if (angular.isArray(num)) {\r\n            for (var i = num[0]; i < num[1]; i++) {\r\n                rs.push(i);\r\n            }\r\n        }\r\n        return rs;\r\n    };\r\n    $scope.convert_number_to_words = function (num) {\r\n        return convert_number_to_words(num);\r\n    };\r\n    $scope.in_array = function (value, arr) {\r\n        return in_array(value, arr);\r\n    };\r\n    $scope.objToArray = function (obj) {\r\n        if (typeof obj === 'object') {\r\n            return Object.values(obj);\r\n        }\r\n    };\r\n    setTimeout(function () {\r\n        var view_container = $('.angular-view-container');\r\n        var screen = $('body');\r\n        if (!view_container) {\r\n            return;\r\n        }\r\n        $scope.$apply(function () {\r\n            if (screen) {\r\n                $scope.screen = {\r\n                    width: screen.css('width').replace('px', ''),\r\n                    height: screen.css('height').replace('px', '')\r\n                }\r\n            }\r\n            if (view_container.length) {\r\n                $scope.view = {\r\n                    width: view_container.css('width').replace('px', ''),\r\n                    height: view_container.css('height').replace('px', '')\r\n                }\r\n            }\r\n        })\r\n    }, 1000);\r\n}])\r\n    .config(['$compileProvider', function ($compileProvider) {\r\n        $compileProvider.debugInfoEnabled(true);\r\n    }])\r\n    .filter('date2form', ['$sce', function ($sce) {\r\n        var div = document.createElement('div');\r\n        return function (date, type) {\r\n            var d = '';\r\n            if (date != undefined && date != '') {\r\n                type || (type = '/');\r\n                d = date.split(' ')[0].split('-');\r\n                var dt = [];\r\n                if (d[2]) dt.push(d[2]);\r\n                if (d[1]) dt.push(d[1]);\r\n                if (d[0]) dt.push(d[0]);\r\n                d = dt.join(type);\r\n            }\r\n            return d;\r\n        };\r\n    }])\r\n    .filter('decodeHTML', ['$sce', function ($sce) {\r\n        var div = document.createElement('div');\r\n        return function (text) {\r\n            text || (text = '');\r\n            div.innerHTML = $('<textarea />').html(text).text();\r\n            return $sce.trustAsHtml($('<textarea />').html(text).text());\r\n        };\r\n    }])\r\n    .filter('parseFloat', [function ($sce) {\r\n        return function (value) {\r\n            var val = parseFloat(value);\r\n            return val;\r\n        };\r\n    }])\r\n    .filter('round', ['$sce', function ($sce) {\r\n        return function (value, num) {\r\n            num || (num = 2);\r\n            var v = (value + '').split('.');\r\n            if (v.length == 2) {\r\n                v[1] = v[1].substring(0, num);\r\n            } else {\r\n                v.push('0');\r\n            }\r\n            return parseFloat(v[0] + '.' + v[1]);\r\n        };\r\n    }])\r\n    .factory('MyCache', function ($cacheFactory) {\r\n        return $cacheFactory('myCache');\r\n    })\r\n\r\n    .directive('menuDropdownHover', function () {\r\n        return {\r\n            link: function (scope, element) {\r\n                $(element).hover(\r\n                    function () {\r\n                        $(this).addClass('open')\r\n                    },\r\n                    function () {\r\n                        $(this).removeClass('open')\r\n                    }\r\n                )\r\n            }\r\n        };\r\n    })\r\n    .directive('iconHover', function () {\r\n        return {\r\n            link: function (scope, element) {\r\n                $(element).hover(\r\n                    function () {\r\n                    }\r\n                )\r\n\r\n            }\r\n        };\r\n    })\r\n    .directive('focusMe', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=focusMe'},\r\n            link: function (scope, element) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value === true) {\r\n                        $timeout(function () {\r\n                            element.focus();\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('meFocus', function ($timeout) {\r\n        return {\r\n            scope: {meFocus: '&meFocus'},\r\n            link: function (scope, element) {\r\n                $(element).focus(function () {\r\n                    /*console.log(element);*/\r\n                    scope.meFocus();\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('numberFormat', function ($timeout) {\r\n        return {\r\n            scope: {\r\n                numberFormat: '@numberFormat',\r\n                ngModel: '=ngModel'\r\n            }, link: function (scope, element, attrs) {\r\n                var type = scope.numberFormat;\r\n                if (!type || type != 'float') {\r\n                    type = 'int';\r\n                } else {\r\n                    type = 'float';\r\n                }\r\n                var char_sp = [0, 8];\r\n                $(element).keypress(function (e) {\r\n                    if (e.which >= 48 && e.which <= 57 || e.which == 46) {\r\n                        /*console.log(type,e.which);*/\r\n                        if (type == 'int' && e.which == 46) {\r\n                            return false;\r\n                        } else {\r\n                            var val = $(this).val();\r\n                            if (val.split('.').length > 1 && e.which == 46) {\r\n                            } else {\r\n                                return false;\r\n                            }\r\n                        }\r\n                    } else {\r\n                        if (!in_array(e.which, char_sp)) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                    if (attrs.ngModel) {\r\n                        scope.ngModel = Number($(this).val());\r\n                    }\r\n                })\r\n            }\r\n        };\r\n    })\r\n    .directive('typeNumber', function ($timeout) {\r\n        return {\r\n            scope: {\r\n                typeNumber: '@typeNumber',\r\n                ngModel: '=ngModel',\r\n                numAbs: '=numAbs',\r\n                min: '=min',\r\n                max: '=max'\r\n            }, link: function (scope, element, attrs) {\r\n                var type = scope.typeNumber;\r\n                if (!type || type != 'float') {\r\n                    type = 'int';\r\n                } else {\r\n                    type = 'float';\r\n                }\r\n                if (!attrs.title) {\r\n                    if (type == 'float') {\r\n                        $(element).attr('title', 'Định dạng phải là số');\r\n                    } else {\r\n                        $(element).attr('title', 'Chỉ chấp nhận số nguyên');\r\n                    }\r\n                }\r\n                $(element).keypress(function (e) {\r\n                    var key = e.which;\r\n                    var rs = false;\r\n                    /*\r\n                    * 46: dấu chấm;\r\n                    * 45: dấu trừ;\r\n                    * */\r\n                    if (key >= 48 && key <= 57 || key === 46 || key === 45) {\r\n                        rs = true;\r\n                        var val = $(this).val();\r\n                        if (key === 46 && (type === 'int' || type === 'float' && $(this).val().split('.').length > 1)) {\r\n                            rs = false;\r\n                        }\r\n                        if (key === 45 && (scope.numAbs || val.split('-').length > 1)) {\r\n                            rs = false;\r\n                        }\r\n                    }\r\n                    return rs;\r\n                });\r\n                $(element).keyup(function (e) {\r\n                    if (e.which === 190) {\r\n                        var val = $(this).val();\r\n                        if (val.split('.').length > 1 && val.split('.')[0] === '') {\r\n                            $(this).val(0 + val);\r\n                        }\r\n                    }\r\n                    if (e.which == 189 || e.which == 173) {\r\n                        var val = $(this).val();\r\n                        if (val.split('-').length > 1) {\r\n                            val = val.replace('-', '');\r\n                            $(this).val('-' + val);\r\n                        }\r\n                    }\r\n                    if (typeof scope.min != \"undefined\") {\r\n                        if (Number($(this).val()) < scope.min) {\r\n                            $(this).val(scope.min);\r\n                        }\r\n                    }\r\n                    if (typeof scope.max != \"undefined\") {\r\n                        if (Number($(this).val()) > scope.max) {\r\n                            $(this).val(scope.max);\r\n                        }\r\n                    }\r\n                }).blur(function () {\r\n                    scope.ngModel = Number($(this).val());\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('parseInt', function ($timeout) {\r\n        return {\r\n            scope: {\r\n                parseInt: '=parseInt'\r\n            }, link: function (scope, element, attrs) {\r\n                $(element).keyup(function (e) {\r\n                    var val = $(element).val();\r\n                    if (val) {\r\n                        val = val.replace(',', '.');\r\n                        val = val.replace('.', '');\r\n                        val || (val = 0);\r\n                        val = parseInt(val);\r\n                    } else {\r\n                        val = 0;\r\n                    }\r\n                    $(element).val(val);\r\n                    scope.trigger = val;\r\n                })\r\n            }\r\n        };\r\n    })\r\n    .directive('parseFloat', function ($timeout) {\r\n        return {\r\n            scope: {\r\n                parseInt: '=parseInt'\r\n            }, link: function (scope, element, attrs) {\r\n                $(element).keyup(function (e) {\r\n                    var val = $(element).val();\r\n                    if (val) {\r\n                        val = val.replace(',', '.');\r\n                        val = val.replace('..', '.');\r\n                        val || (val = 0);\r\n                        if (e.key != '.') {\r\n                            val = parseFloat(val);\r\n                        }\r\n                    } else {\r\n                        val = 0;\r\n                    }\r\n                    $(element).val(val);\r\n                    scope.trigger = val;\r\n                })\r\n            }\r\n        };\r\n    })\r\n    .directive('dateBox', function ($timeout) {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                trigger: '=dateBox',\r\n                boxWidth: '=boxWidth',\r\n                boxHeight: '=boxHeight',\r\n                dateBoxDisabled: '=dateBoxDisabled',\r\n                onSelect: '&onSelect',\r\n                onLoad: '&onLoad',\r\n                build: '=build'\r\n            },\r\n            link: function (scope, element, attrs) {\r\n                element.begin = true;\r\n                var options = {\r\n                    value: scope.trigger,\r\n                    formatter: myformatter,\r\n                    parser: dateparser,\r\n                    onSelect: function (date) {\r\n                        var val_new = dateboxOnSelect(date);\r\n                        var val_old = element.oldValue;\r\n                        onChange(val_new, val_old);\r\n                    }, onChange: function (date) {\r\n                    },\r\n                    width: scope.boxWidth,\r\n                    height: scope.boxHeight\r\n                };\r\n                scope.onLoad();\r\n                var el = element.datebox(options);\r\n                scope.$watch('trigger', function (value) {\r\n                    el.datebox('setValue', scope.trigger);\r\n                });\r\n                el.next().children('input:first').bind('blur', function () {\r\n                    var val_new = $(this).val();\r\n                    var val_old = element.oldValue;\r\n                    onChange(val_new, val_old);\r\n                }).bind('focus', function () {\r\n                    element.oldValue = $(this).val();\r\n                }).click(function () {\r\n                    element.oldValue = $(this).val();\r\n                });\r\n\r\n                function onChange(value_new, value_old) {\r\n                    if (value_new != value_old) {\r\n                        setTimeout(function () {\r\n                            scope.$apply(function () {\r\n                                scope.trigger = value_new;\r\n                            });\r\n                            scope.$apply(function () {\r\n                                scope.onSelect();\r\n                            });\r\n                        });\r\n\r\n                    }\r\n                }\r\n            }\r\n        };\r\n    })\r\n    .directive('vtDraggable', function ($timeout) {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                trigger: '=vtDraggable'\r\n            },\r\n            compile: function($element, $attrs) {\r\n                $($element).addClass('draggable-container');\r\n                var check = false;\r\n                $element.find('button.btn-print').each(function (index, el) {\r\n                    var click = $(el).attr('ng-click');\r\n                    click || (click = $(el).attr('onclick'));\r\n                    if (click) {\r\n                        if (click.split('Export.file').length > 1) {\r\n                            check = true;\r\n                        }\r\n                    }\r\n                });\r\n                if (check) {\r\n                    $element.append('<span class=\"btn glyphicon glyphicon-info-sign color-orange btn-over-blue\" ng-click=\"formHelperFixErrorOpenExcel()\" title=\"Hướng dẫn sửa lỗi khi mở tệp excel\"></span>');\r\n                }\r\n                $($element).draggable();\r\n                return $element;\r\n            }\r\n        };\r\n    })\r\n    .directive('comboBox', function ($timeout, $compile) {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                trigger: '=comboBox',\r\n                boxWidth: '=boxWidth',\r\n                boxHeight: '=boxHeight',\r\n                url: '@url',\r\n                data: '=data',\r\n                delay: '@delay',\r\n                mode: '@mode',\r\n                paramKey: '@paramKey',\r\n                valueField: '@valueField',\r\n                textField: '@textField',\r\n                paramValue: '=paramValue',\r\n                clearForNull: '=clearForNull',\r\n                reloadOnSelected: '=reloadOnSelected',\r\n                clearOnSelected: '=clearOnSelected',\r\n                onSelect: '&onSelect',\r\n                onLoadSuccess: '&onLoadSuccess'\r\n            }, controller: function ($scope, $element, $attrs, $transclude) {\r\n\r\n            },\r\n            link: function (scope, element, attrs, ctrls) {\r\n                var created = false;\r\n                var is_selected = false;\r\n                var queryParams = {};\r\n                scope.valueField || (scope.valueField = 'id');\r\n                scope.textField || (scope.textField = 'text');\r\n                scope.delay || (scope.delay = 700);\r\n                scope.mode || (scope.mode = 'remote');\r\n                if (scope.paramKey) {\r\n                    queryParams[scope.paramKey] = scope.paramValue;\r\n                }\r\n                var option = {\r\n                    valueField: scope.valueField,\r\n                    textField: scope.textField,\r\n                    /*panelHeight:'auto',*/\r\n                    mode: scope.mode,\r\n                    delay: scope.delay,\r\n                    onSelect: function (row, el, test) {\r\n                        is_selected = true;\r\n                    }, onLoadSuccess: function (data, el) {\r\n                        is_selected = false;\r\n                        $(element).combobox('panel').children().click(function (e) {\r\n                            element.onSelected();\r\n                        });\r\n                        $(element).next().children('input').keyup(function (e) {\r\n                            if (e.which == 13) {\r\n                                element.onSelected();\r\n                            }\r\n                        });\r\n                        scope.data = data;\r\n                        scope.onLoadSuccess();\r\n                        created = true;\r\n                    },\r\n                    queryParams: queryParams,\r\n                    width: 170,\r\n                    height: 30\r\n                };\r\n                element.onSelected = function () {\r\n                    setTimeout(function () {\r\n                        var id = $(element).combobox('getValue');\r\n                        var item = {};\r\n                        var data = $(element).combobox('getData');\r\n                        angular.forEach(data, function (fd, ind) {\r\n                            if (id + '' == fd[scope.valueField] + '') {\r\n                                item = fd;\r\n                            }\r\n                        });\r\n                        setTimeout(function () {\r\n                            scope.$apply(function () {\r\n                                scope.trigger = item;\r\n                            });\r\n                            scope.$apply(function () {\r\n                                scope.onSelect();\r\n                            });\r\n                        });\r\n                        if (scope.clearForNull) {\r\n                            $(element).combobox('clear');\r\n                        }\r\n                    });\r\n                };\r\n                if (scope.trigger) {\r\n                    option.value = scope.trigger[scope.valueField];\r\n                }\r\n                if (scope.boxWidth) {\r\n                    option.width = scope.boxWidth;\r\n                }\r\n                scope.$watch('data', function (value) {\r\n                    if (value) {\r\n                        $(element).combobox('loadData', value);\r\n                    } else {\r\n\r\n                    }\r\n                });\r\n                scope.$watch('trigger', function (value) {\r\n                    if (created) {\r\n                        if (value != undefined) {\r\n                            var data = scope.data;\r\n                            var id = value[scope.valueField];\r\n                            var index = -1;\r\n                            angular.forEach(data, function (item, ind) {\r\n                                if (value[scope.valueField] == item[scope.valueField]) {\r\n                                    index = ind;\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                });\r\n                scope.$watch('paramValue', function (value) {\r\n                    if (scope.mode != 'local') {\r\n                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {\r\n                            var queryParams = $(element).combobox('options').queryParams;\r\n                            if (scope.paramKey) {\r\n                                queryParams[scope.paramKey] = scope.paramValue;\r\n                            }\r\n                            $(element).combobox({queryParams: queryParams});\r\n                        }\r\n                    } else {\r\n                        if (in_array(typeof scope.data, ['object'])) {\r\n                            if (in_array(typeof value, ['object'])) {\r\n                                var data_new = [];\r\n                                angular.forEach(scope.data, function (item, index) {\r\n                                    if (!in_array(item[scope.valueField], value)) {\r\n                                        data_new.push(item);\r\n                                    }\r\n                                });\r\n                                $(element).combobox('loadData', data_new).combobox('clear');\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n                var url = scope.url;\r\n                // if(scope.data != undefined){\r\n                //     url = scope.data;\r\n                // }\r\n                $.dm_datagrid.combobox(element, url, option);\r\n            }\r\n        };\r\n    })\r\n    .directive('infConfigs', function ($timeout, $compile) {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                infConfigs: '=infConfigs',\r\n                infId: '@infId',\r\n                url: '@url',\r\n                resp: '=resp',\r\n                onSuccess: '&onSuccess',\r\n                onError: '&onError',\r\n                infDefault: '=infDefault',\r\n                ngTrueValue: '=ngTrueValue',\r\n                ngFalseValue: '=ngFalseValue',\r\n                onChange: '&onChange',\r\n                ngValue: '=ngValue'\r\n            }, controller: function ($scope, $element, $attrs, $transclude) {\r\n                $scope.value_old = $scope.infConfigs;\r\n            },\r\n            link: function (scope, element, attrs, ctrls) {\r\n                var ngTrueValue = true;\r\n                var ngFalseValue = false;\r\n                if (typeof attrs.ngFalseValue != 'undefined') {\r\n                    ngFalseValue = scope.ngFalseValue;\r\n                }\r\n                if (typeof attrs.ngTrueValue != 'undefined') {\r\n                    ngTrueValue = scope.ngTrueValue;\r\n                }\r\n                if (scope.infConfigs === undefined) {\r\n                    if (attrs.type === 'checkbox') {\r\n                        scope.infConfigs = ngFalseValue;\r\n                    } else {\r\n                        scope.infConfigs = scope.infDefault;\r\n                    }\r\n                }\r\n                if (!scope.url) {\r\n                    scope.url = [$CFG.remote.base_url, 'information_configs', $CFG.project].join('/');\r\n                }\r\n                if (attrs.type === 'checkbox') {\r\n                    if (scope.infConfigs === ngTrueValue) {\r\n                        $(element).prop('checked', true);\r\n                    }\r\n                } else if (attrs.type === 'radio') {\r\n                    if (scope.ngValue === scope.infConfigs) {\r\n                        $(element).prop('checked', true);\r\n                    } else {\r\n                        $(element).prop('checked', false);\r\n                    }\r\n                } else if (attrs.type === 'number') {\r\n                    console.log('set default', scope.infDefault)\r\n                    $(element).val(scope.infDefault);\r\n                } else {\r\n                    $(element).focus(function () {\r\n                        scope.value_old = scope.infConfigs;\r\n                    });\r\n                }\r\n                if (attrs.type === 'checkbox') {\r\n                    $(element).change(function () {\r\n                        var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);\r\n                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {\r\n                            scope.$apply(function () {\r\n                                scope.infConfigs = value;\r\n                            });\r\n                        }, function () {\r\n                            if (typeof scope.onError == 'function') {\r\n                                scope.$apply(function () {\r\n                                    scope.onError();\r\n                                })\r\n                            }\r\n                        }, scope.infAlert, false);\r\n                    })\r\n                } else if (attrs.type === 'radio') {\r\n                    $(element).click(function () {\r\n                        var value = scope.ngValue;\r\n                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {\r\n                            scope.$apply(function () {\r\n                                scope.infConfigs = value;\r\n                            })\r\n                        }, function () {\r\n                            if (typeof scope.onError == 'function') {\r\n                                scope.$apply(function () {\r\n                                    scope.onError();\r\n                                })\r\n                            }\r\n                        }, scope.infAlert, false);\r\n                    })\r\n                } else {\r\n                    $(element).blur(function () {\r\n                        /* directive id is array */\r\n                        var arr = scope.infId.split('.');\r\n                        var id = angular.copy(scope.infId);\r\n                        var value = angular.copy(scope.infConfigs);\r\n\r\n                        if (arr.length >= 1) {\r\n                            id = arr[0];\r\n                            for (var i = arr.length; i > 0; i--) {\r\n                                var tmp = {};\r\n                                tmp[arr[i - 1]] = value;\r\n                                value = tmp;\r\n                            }\r\n                            value = value[id];\r\n                        }\r\n\r\n                        if (scope.value_old !== scope.infConfigs) {\r\n                            process(scope.url, {async: true, id: id, value: value}, function (resp) {\r\n                                scope.$apply(function () {\r\n                                    if (typeof scope.onSuccess == 'function') {\r\n                                        scope.onSuccess();\r\n                                    }\r\n                                })\r\n                            }, function () {\r\n                                if (typeof scope.onError == 'function') {\r\n                                    scope.$apply(function () {\r\n                                        scope.onError();\r\n                                    });\r\n                                }\r\n                            }, scope.infAlert, false);\r\n                        }\r\n                    }).keyup(function () {\r\n                        console.log(111111111111)\r\n                        scope.$apply(function () {\r\n                            scope.infConfigs = $(element).val();\r\n                        });\r\n                    });\r\n                }\r\n                scope.$watch('infConfigs', function (value) {\r\n                    if (attrs.type === 'checkbox') {\r\n                        if (scope.ngTrueValue + '' === value + '') {\r\n                            $(element).prop('checked', true);\r\n                        } else {\r\n                            $(element).prop('checked', false);\r\n                        }\r\n                        scope.onSuccess();\r\n                    } else if (attrs.type === 'radio') {\r\n                        scope.onSuccess();\r\n                    } else {\r\n                        $(element).val(value);\r\n                    }\r\n                    scope.onChange();\r\n                })\r\n            }\r\n        };\r\n    })\r\n    .directive('countFbComment', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=countFbComment'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $timeout(function () {\r\n                            element.append('<div class=\"fb-comments-count\" data-href=\"' + attrs.url + value + '\"></div>');\r\n                            reloadFace();\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('helperInclude', function ($timeout) {\r\n        return {\r\n            scope: {\r\n                trigger: '=helperInclude',\r\n                url: '@url',\r\n                key: '@key'\r\n            },\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    var data = {};\r\n                    if (scope.key) {\r\n                        data[scope.key] = scope.trigger;\r\n                    }\r\n                    loadForm(scope.url, '', data, function (resp) {\r\n                        $(element).html(resp);\r\n                    })\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('addId', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=addId'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    $(element).attr('id', value);\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('classHidden', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=classHidden'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $(element).addClass('hidden');\r\n                    } else {\r\n                        $(element).removeClass('hidden');\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('classActive', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=classActive'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $(element).addClass('active');\r\n                    } else {\r\n                        $(element).removeClass('active');\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('idActive', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=idActive'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $(element).addClass('active');\r\n                    } else {\r\n                        $(element).removeClass('active');\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('viewFbComments', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=viewFbComments'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $timeout(function () {\r\n                            element.append('<div class=\"fb-comments\" data-href=\"' + attrs.url + value + '\" width=\"100%\" datanumposts=\"5\"></div>');\r\n                            reloadFace();\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('shareFb', function ($timeout) {\r\n        return {\r\n            scope: {trigger: '=shareFb'},\r\n            link: function (scope, element, attrs) {\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        $timeout(function () {\r\n                            element.append('<div class=\"fb-share-button\" data-href=\"' + attrs.url + value + '\" data-layout=\"button_count\" data-mobile-iframe=\"true\"></div>');\r\n                            reloadFace();\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        };\r\n    })\r\n    .directive('myEnter', function () {\r\n        return function (scope, element, attrs) {\r\n            element.bind(\"keydown keypress\", function (event) {\r\n                if (event.which === 13) {\r\n                    scope.$apply(function () {\r\n                        scope.$eval(attrs.myEnter);\r\n                    });\r\n                    event.preventDefault();\r\n                }\r\n            });\r\n        };\r\n    })\r\n    .directive('starHoverChangeColor', function ($timeout) {\r\n        return {\r\n            restrict: 'A',\r\n            compile: function (element, attrs) {\r\n                return function (scope, element, attrs) {\r\n                    $timeout(function () {\r\n                        if (attrs.starHoverChangeColor && taikhoan != '') {\r\n                            var p = element.parent();\r\n                            var no = parseInt(attrs.field);\r\n                            element.hover(function () {\r\n                                if (scope.monan.dabinhchon) {\r\n                                    element.attr('title', 'Bạn đã bình chọn món ăn này');\r\n                                    return;\r\n                                }\r\n                                p.children().each(function (index, el) {\r\n                                    if (index < no) {\r\n                                        $(el).addClass('star-hover');\r\n                                    } else {\r\n                                        $(el).removeClass('star-hover');\r\n                                    }\r\n                                })\r\n                            }, function () {\r\n                                p.children().removeClass('star-hover');\r\n                            })\r\n                                .click(function () {\r\n                                    if (scope.monan.dabinhchon) {\r\n                                        return;\r\n                                    }\r\n                                    var arr_data = {\r\n                                        diem: attrs.starHoverChangeColor,\r\n                                        mamn: attrs.maMn,\r\n\r\n                                    };\r\n                                    var data = JSON.stringify(arr_data);\r\n\r\n                                    $.ajax({\r\n                                        type: \"POST\",\r\n                                        url: base_url + don_vi + '/_xuly/mon_ngon/xulyBC',\r\n                                        timeout: 120000,\r\n                                        data: {data: data},\r\n                                        dataType: \"json\",\r\n                                        success: function (resp) {\r\n                                            if (resp.result == \"success\") {\r\n                                                alert(\"Bạn đã bình chọn thành công. Cảm ơn!\");\r\n                                                scope.monan.dabinhchon = true;\r\n                                                scope.$apply(scope.$eval(attrs.mnUpdate));\r\n                                            } else if (resp.result == \"fail\") {\r\n                                                alert(resp.err);\r\n                                            } else {\r\n                                                var errors = \"\";\r\n                                                $.each(resp.errors, function (i, v) {\r\n                                                    errors += \"- \" + v + \"\\n\";\r\n                                                });\r\n                                                alert('Lỗi!', errors);\r\n                                            }\r\n                                        }\r\n                                    });\r\n                                })\r\n                        } else {\r\n                            element.attr('title', 'Đăng nhập để bình chọn');\r\n                        }\r\n                    })\r\n                }\r\n            }\r\n        };\r\n    })\r\n    .directive('fileUpload', function () {\r\n        return {\r\n            scope: {\r\n                fileUpload: '=fileUpload',\r\n                url: '@url',\r\n                urlBind: '=urlBind',\r\n                format: '@format',\r\n                size: '@size',\r\n                uploadDone: '&uploadDone'\r\n            },\r\n            link: function (scope, element, attrs) {\r\n                var url = scope.url;\r\n                url || (url = scope.urlBind);\r\n                if (url) {\r\n                    scope.size || (scope.size = 2);\r\n                    scope.files || (scope.files = 'files');\r\n                    scope.format || (scope.format = 'zip|rar|7z|doc|docx|pdf|xls|xlsx|jpg|png|gif');\r\n                    element.attr('name', scope.files + '[]').attr('type', 'file');\r\n                    init_uploader(element, url, scope.format, scope.size, function (resp) {\r\n                        setTimeout(function () {\r\n                            scope.$apply(function () {\r\n                                scope.fileUpload = resp.data;\r\n                            })\r\n                        })\r\n                    });\r\n                }\r\n                scope.$watch('fileUpload', function (value) {\r\n                    if (typeof value !== 'undefined') {\r\n                        scope.uploadDone();\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    }).directive('dynamic', function () {\r\n    return {\r\n        restrict: 'A',\r\n        scope: {\r\n            dynamic: '=dynamic',\r\n            dynamicId: '@dynamicId',\r\n            url: '@url',\r\n            resp: '=resp',\r\n            onSuccess: '&onSuccess',\r\n            onError: '&onError',\r\n            ngTrueValue: '=ngTrueValue',\r\n            ngFalseValue: '=ngFalseValue',\r\n            ngValue: '=ngValue'\r\n        }, controller: function ($scope) {\r\n            $scope.value_old = $scope.dynamic;\r\n        },\r\n        link: function (scope, element, attrs) {\r\n            var ngTrueValue = true;\r\n            var ngFalseValue = false;\r\n            if (attrs.ngFalseValue !== undefined) {\r\n                ngFalseValue = scope.ngFalseValue;\r\n            }\r\n            if (attrs.ngTrueValue !== undefined) {\r\n                ngTrueValue = scope.ngTrueValue;\r\n            }\r\n            if (scope.dynamic === undefined) {\r\n                if (attrs.type === 'checkbox') {\r\n                    scope.dynamic = ngFalseValue;\r\n                } else if (attrs.type === 'radio') {\r\n\r\n                } else {\r\n                    scope.dynamic = '';\r\n                }\r\n            }\r\n            if (!scope.url) {\r\n                scope.url = [$CFG.remote.base_url, 'dynamic-variable/update'].join('/');\r\n            }\r\n            if (attrs.type === 'checkbox') {\r\n                if (scope.dynamic === ngTrueValue) {\r\n                    $(element).prop('checked', true);\r\n                }\r\n            } else if (attrs.type === 'radio') {\r\n                if (scope.ngValue === scope.dynamic) {\r\n                    $(element).prop('checked', true);\r\n                } else {\r\n                    $(element).prop('checked', false);\r\n                }\r\n            } else {\r\n                $(element).focus(function () {\r\n                    scope.value_old = scope.dynamic;\r\n                })\r\n            }\r\n            if (attrs.type === 'checkbox') {\r\n                $(element).change(function () {\r\n                    var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);\r\n                    process(scope.url, {\r\n                        async: true,\r\n                        id: scope.dynamicId,\r\n                        value: value,\r\n                        pathname: window.location.pathname\r\n                    }, function () {\r\n                        scope.$apply(function () {\r\n                            scope.dynamic = value;\r\n                        })\r\n                    }, function () {\r\n                        if (typeof scope.onError === 'function') {\r\n                            scope.$apply(function () {\r\n                                scope.onError();\r\n                            })\r\n                        }\r\n                    }, false, false);\r\n                })\r\n            } else if (attrs.type === 'radio') {\r\n                $(element).click(function () {\r\n                    var value = scope.ngValue;\r\n                    process(scope.url, {\r\n                        async: true,\r\n                        id: scope.dynamicId,\r\n                        value: value,\r\n                        pathname: window.location.pathname\r\n                    }, function () {\r\n                        scope.$apply(function () {\r\n                            scope.dynamic = value;\r\n                        })\r\n                    }, function () {\r\n                        if (typeof scope.onError === 'function') {\r\n                            scope.$apply(function () {\r\n                                scope.onError();\r\n                            })\r\n                        }\r\n                    }, false, false);\r\n                })\r\n            } else {\r\n                $(element).blur(function () {\r\n                    /* directive id is array */\r\n                    var arr = scope.dynamicId.split('.');\r\n                    var id = angular.copy(scope.dynamicId);\r\n                    var value = angular.copy(scope.dynamic);\r\n\r\n                    if (arr.length >= 1) {\r\n                        id = arr[0];\r\n                        for (var i = arr.length; i > 0; i--) {\r\n                            var tmp = {};\r\n                            tmp[arr[i - 1]] = value;\r\n                            value = tmp;\r\n                        }\r\n                        value = value[id];\r\n                    }\r\n\r\n                    if (scope.value_old !== scope.dynamic) {\r\n                        process(scope.url, {\r\n                            async: true,\r\n                            id: id,\r\n                            value: value,\r\n                            pathname: window.location.pathname\r\n                        }, function () {\r\n                            scope.$apply(function () {\r\n                                if (typeof scope.onSuccess === 'function') {\r\n                                    scope.onSuccess();\r\n                                }\r\n                            })\r\n                        }, function () {\r\n                            if (typeof scope.onError === 'function') {\r\n                                scope.$apply(function () {\r\n                                    scope.onError();\r\n                                })\r\n                            }\r\n                        }, false, false);\r\n                    }\r\n                }).keyup(function () {\r\n                    scope.$apply(function () {\r\n                        scope.dynamic = $(element).val();\r\n                    })\r\n                })\r\n            }\r\n            /*Set lại thuộc tính*/\r\n            scope.$watch('dynamic', function (value) {\r\n                if (attrs.type === 'checkbox') {\r\n                    if (scope.ngTrueValue + '' === value + '') {\r\n                        $(element).prop('checked', true);\r\n                    } else {\r\n                        $(element).prop('checked', false);\r\n                    }\r\n                    scope.onSuccess();\r\n                } else if (attrs.type === 'radio') {\r\n                    scope.onSuccess();\r\n                } else {\r\n                    $(element).val(value);\r\n                }\r\n            })\r\n        }\r\n    };\r\n})\r\n    .directive('comboBoxFood', function ($timeout, $compile) {\r\n        return {\r\n            restrict: 'A',\r\n            scope: {\r\n                trigger: '=comboBoxFood',\r\n                boxWidth: '=boxWidth',\r\n                boxHeight: '=boxHeight',\r\n                url: '@url',\r\n                data: '=data',\r\n                delay: '@delay',\r\n                mode: '@mode',\r\n                paramKey: '@paramKey',\r\n                valueField: '@valueField',\r\n                textField: '@textField',\r\n                paramValue: '=paramValue',\r\n                clearForNull: '=clearForNull',\r\n                reloadOnSelected: '=reloadOnSelected',\r\n                clearOnSelected: '=clearOnSelected',\r\n                onSelect: '&onSelect',\r\n                onLoadSuccess: '&onLoadSuccess'\r\n            }, controller: function ($scope, $element, $attrs, $transclude) {\r\n\r\n            },\r\n            link: function (scope, element, attrs, ctrls) {\r\n                var created = false;\r\n                var is_selected = false;\r\n                var queryParams = {};\r\n                scope.valueField || (scope.valueField = 'id');\r\n                scope.textField || (scope.textField = 'text');\r\n                scope.delay || (scope.delay = 700);\r\n                scope.mode || (scope.mode = 'remote');\r\n                if (scope.paramKey) {\r\n                    queryParams[scope.paramKey] = scope.paramValue;\r\n                }\r\n                var option = {\r\n                    valueField: scope.valueField,\r\n                    textField: scope.textField,\r\n                    /*panelHeight:'auto',*/\r\n                    mode: scope.mode,\r\n                    delay: scope.delay,\r\n                    onSelect: function (row, el, test) {\r\n                        is_selected = true;\r\n                    }, onLoadSuccess: function (data, el) {\r\n                        is_selected = false;\r\n                        $(element).combobox('panel').children().click(function (e) {\r\n                            element.onSelected();\r\n                        })\r\n                        $(element).next().children('input').keyup(function (e) {\r\n                            if (e.which == 13) {\r\n                                var id = $(element).combobox('getValue');\r\n                                element.onSelected(id);\r\n                            }\r\n                        });\r\n                        var kt = false;\r\n                        for (var i in data) {\r\n                            var kesearch = removeUnicode($(element).next().find('input').val());\r\n                            if (scope.trigger != undefined && typeof scope.trigger == 'object') {\r\n                                if (data[i][scope.valueField] == scope.trigger[scope.valueField] || removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {\r\n                                    kt = true;\r\n                                    break;\r\n                                }\r\n                            } else if (typeof data[i][scope.textField] == 'string') {\r\n                                if (removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {\r\n                                    kt = true;\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                        if (!kt && scope.clearForNull) {\r\n                            $(element).combobox('clear');\r\n                        }\r\n                        scope.data = data;\r\n                        scope.onLoadSuccess();\r\n                        created = true;\r\n                    },\r\n                    queryParams: queryParams,\r\n                    width: 170,\r\n                    height: 30,\r\n                    formatter: function (row) {\r\n                        var html = `<span class=\"glyphicon glyphicon` + (row.is_static == 1 ? '-ok color-green' : '-grain') + `\"></span> `;\r\n                        return ' <div title=\"' + (row.is_static == 1 ? 'Theo viện dinh dưỡng' : 'Nguồn khác') + '\">' + html + ' <span class=\"item-text\">' + row.name + '</span></div>';\r\n                    }\r\n                };\r\n                element.onSelected = function onSelected(id) {\r\n                    setTimeout(function () {\r\n                        id || (id = $(element).combobox('getValue'));\r\n                        var item = scope.data[0];\r\n                        if (scope.trigger) {\r\n                            item = scope.trigger;\r\n                        }\r\n                        angular.forEach(scope.data, function (fd, ind) {\r\n                            if (id + '' == fd[scope.valueField] + '') {\r\n                                item = fd;\r\n                            }\r\n                        })\r\n                        scope.$apply(function () {\r\n                            scope.trigger = item;\r\n                        })\r\n                    }, 400)\r\n                }\r\n                if (scope.trigger) {\r\n                    option.value = scope.trigger[scope.valueField];\r\n                }\r\n                if (scope.boxWidth) {\r\n                    option.width = scope.boxWidth;\r\n                }\r\n                scope.$watch('data', function (value) {\r\n                    value || (value = []);\r\n                    $(element).combobox('loadData', value);\r\n                });\r\n                scope.$watch('trigger', function (value) {\r\n                    if (value) {\r\n                        var data = scope.data;\r\n                        var id = value[scope.valueField];\r\n                        var index = -1;\r\n                        angular.forEach(data, function (item, ind) {\r\n                            if (value[scope.valueField] == item[scope.valueField]) {\r\n                                index = ind;\r\n                            }\r\n                        });\r\n                        if (index >= 0) {\r\n                            $(element).combobox('setValue', value[scope.textField]);\r\n                        }\r\n                        scope.onSelect();\r\n                    }\r\n                    if (scope.clearOnSelected) {\r\n                        $(element).combobox('clear');\r\n                    }\r\n                });\r\n                scope.$watch('paramValue', function (value) {\r\n                    if (scope.mode != 'local') {\r\n                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {\r\n                            var queryParams = $(element).combobox('options').queryParams;\r\n                            if (scope.paramKey) {\r\n                                queryParams[scope.paramKey] = scope.paramValue;\r\n                            }\r\n                            $(element).combobox({queryParams: queryParams});\r\n                        }\r\n                    } else {\r\n                        if (in_array(typeof scope.data, ['object'])) {\r\n                            if (in_array(typeof value, ['object'])) {\r\n                                var data_new = [];\r\n                                angular.forEach(scope.data, function (item, index) {\r\n                                    if (!in_array(item[scope.valueField], value)) {\r\n                                        data_new.push(item);\r\n                                    }\r\n                                });\r\n                                $(element).combobox('loadData', data_new).combobox('clear');\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n                $.dm_datagrid.combobox(element, scope.url, option);\r\n            }\r\n        };\r\n    })", "angular_app.controller('mainContentController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', '$templateCache', '$templateRequest', '$sce', '$localStorage', '$location',\r\n    function ($scope, $routeParams, $compile, MyCache, $filter, $cookies, $templateCache, $templateRequest, $sce, $localStorage, $location) {\r\n        /*Khai báo mẫu định mức số phần*/\r\n        $scope.dinhmuc = {\r\n            'ngucoc': {name: '<PERSON><PERSON> cốc', value: 3},\r\n            'rau': {name: '<PERSON><PERSON>', value: 7},\r\n            'traicay': {name: '<PERSON>r<PERSON><PERSON> cây', value: 2},\r\n            'damdv': {name: 'Đạm ĐV', value: 7}\r\n        };\r\n        $scope.sys = {\r\n            truong: {\r\n                lists: [],\r\n                selected: undefined\r\n            },\r\n            phong: {\r\n                lists: [],\r\n                selected: undefined\r\n            },\r\n            configs: {},\r\n            tempVer: (new Date()).getTime()\r\n        };\r\n        $scope.menu = {\r\n            select: {},\r\n            page: 'index',\r\n            templates: {\r\n                top: {\r\n                    index: 'menu_top_index.htm',\r\n                    children: 'menu_top_children.htm'\r\n                },\r\n                bottom: {\r\n                    index: 'menu_bottom_index.htm',\r\n                    children: 'menu_bottom_children.htm'\r\n                },\r\n                group: $CFG.template.base_url + '/dinhduong/menu_main-index.html'\r\n            },\r\n            data: {\r\n                top: {},\r\n                bottom: {}\r\n            }\r\n        };\r\n        $scope.sys.phong.onSelected = function (old) {\r\n            var id = 0;\r\n            if ($scope.sys.phong.selected) {\r\n                id = $scope.sys.phong.selected.id;\r\n            }\r\n            if (id != old.id) {\r\n                process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {\r\n                    async: true,\r\n                    id: id,\r\n                    project_id: 2\r\n                }, function (resp) {\r\n                    $scope.$apply(function () {\r\n                        $scope.sys.truong.lists = resp;\r\n                    });\r\n                }, function () {\r\n                    $('#select-school').combobox('clear');\r\n                }, false);\r\n            }\r\n        };\r\n        $scope.sys.truong.onLoadSuccess = function () {\r\n            if ($scope.sys.truong.old_id && count($scope.sys.truong.lists) > 0) {\r\n                angular.forEach($scope.sys.truong.lists, function (item, ind) {\r\n                    if (item.id == $scope.sys.truong.old_id) {\r\n                        setTimeout(function () {\r\n                            $('#select-school').val(item.text).next().find('input[type=\"text\"]').val(item.text);\r\n                        }, 300)\r\n                        $scope.sys.truong.old_id = undefined;\r\n                    }\r\n                });\r\n            }\r\n        };\r\n        $scope.sys.truong.onSelected = function () {\r\n            var truong = $scope.sys.truong.selected;\r\n            var id = '';\r\n            if (truong) {\r\n                id = truong.id;\r\n            }\r\n            process($CFG.remote.base_url + '/doing/admin/unit/setTruong', {\r\n                async: true,\r\n                id: id,\r\n                project_id: 2\r\n            }, function (resp) {\r\n                statusloading();\r\n                setTimeout(function () {\r\n                    location.reload();\r\n                })\r\n            }, function () {\r\n                $('#select-school').combobox('clear');\r\n            });\r\n        };\r\n        $scope.getParams = function () {\r\n            var urls = $location.path().split('/');\r\n            var index = 2;\r\n            if (urls[2] == 'view') {\r\n                index = 3;\r\n            }\r\n            var params = {};\r\n            params.module = urls[index];\r\n            params.action = urls[index + 1];\r\n            return params;\r\n        };\r\n        $scope.getGroupMenu = function ($define) {\r\n            $rs = {};\r\n            $define || ($define = $scope.getParams().module);\r\n            if ($define) {\r\n                var kt = false;\r\n                $.each($scope.menu.data.top, function (index, menus) {\r\n                    for (var i in menus.children) {\r\n                        var menu = menus.children[i];\r\n                        if ($define == menu.define) {\r\n                            kt = true;\r\n                            $rs = menus;\r\n                            $rs['module_name'] = menu.name;\r\n                            return;\r\n                        }\r\n                    }\r\n                });\r\n                if (!kt) {\r\n                    $.each($scope.menu.data.bottom, function (index, menus) {\r\n                        for (var i in menus.children) {\r\n                            var menu = menus.children[i];\r\n                            if ($define == menu.define) {\r\n                                kt = true;\r\n                                $rs = menus;\r\n                                $rs['module_name'] = menu.name;\r\n                                return;\r\n                            }\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n            return $rs;\r\n        };\r\n        $scope.selected_menu_khauphandinhduong = function () {\r\n            angular.forEach($scope.menu.data.top, function (menu, ind) {\r\n                if (menu.icon == 'khau_phan_dinh_duong') {\r\n                    $scope.menu.selected = menu;\r\n                    setTimeout(function () {\r\n                        $('li#danh_muc').click();\r\n                    })\r\n                }\r\n            })\r\n        };\r\n        $scope.setMonth_selected = function (month) {\r\n            setCookie('month_selected', month);\r\n        };\r\n        $scope.getMonth_selected = function (get_string) {\r\n            var month = getCookie('month_selected');\r\n            month || (month = (new Date().getMonth() + 1));\r\n            $scope.setMonth_selected(month);\r\n            var m = parseInt(month);\r\n            if (get_string) {\r\n                if (m < 10) {\r\n                    month = '0' + m;\r\n                } else {\r\n                    month = '' + m;\r\n                }\r\n            } else {\r\n                month = m;\r\n            }\r\n            return month;\r\n        };\r\n        /* Khởi tạo menu và cấu hình chung cho các báo cáo */\r\n        $scope._initSys = function () {\r\n            var data = window.sysConfigs;\r\n            data || (data = $templateCache.get('sysConfigs'));\r\n            if (data.rows[1]) {\r\n                $scope.menu.data.top = data.rows[1];\r\n                $scope.menu.selected = '';\r\n                $.each($scope.menu.data.top, function (index, menu) {\r\n                    $scope.menu.selected = menu;\r\n                });\r\n            }\r\n            if (data.rows[2]) {\r\n                $scope.menu.data.bottom = data.rows[2];\r\n            }\r\n            $scope.sys.configs = data.configs;\r\n            window.sysConfigs || (window.sysConfigs = data);\r\n            angular.element(document).ready(function () {\r\n                var li0 = $(\".li0\").width() + 26;\r\n                var li1 = $(\".li1\").width() + 26;\r\n                var li2 = $(\".li2\").width() + 26;\r\n                var li3 = $(\".li3\").width() + 26;\r\n                var li4 = $(\".li4\").width() + 26;\r\n                var width = (li0 + li1 + li2 + li3 + li4 + 40);\r\n                if ($(window).width() < 1025) {\r\n                    width = width - 76;\r\n                }\r\n                if (width > 50) {\r\n                    width = width + 20;\r\n                    $(\".nav-main-content\").css(\"width\", width + \"px\");\r\n                }\r\n                $('#statusloading-mash').hide();\r\n            });\r\n            $templateCache.put('menu', $scope.menu);\r\n        };\r\n        $scope.detailUserForm = function () {\r\n            $.dm_datagrid.showAddForm({\r\n                module: $CFG.project + '/' + self.module,\r\n                action: 'add',\r\n                title: 'Thông tin chi tiết tài khoản',\r\n                size: size.normal,\r\n                fullScreen: false,\r\n                showButton: false,\r\n                draggable: true,\r\n                content: function (element) {\r\n                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_detail.html', {}, function (resp) {\r\n                        $scope.$apply(function () {\r\n                            $(element).html($scope.compile(resp, $scope));\r\n                        });\r\n                    });\r\n                }\r\n            });\r\n        };\r\n        $scope.updateSelfUser = function () {\r\n            var data = {\r\n                async: true,\r\n                email: $CFG.user.email,\r\n                phone: $CFG.user.phone,\r\n                name: $CFG.user.name\r\n            };\r\n            process($CFG.remote.base_url + '/doing/admin/user/updateSelfUser', data, function (resp) {\r\n\r\n            }, function () {\r\n\r\n            });\r\n        };\r\n        $scope.onchangeSchool_point = function (point, callback) {\r\n            var data = {\r\n                async: true,\r\n                point: point\r\n            };\r\n            process($CFG.remote.base_url + '/doing/admin/user/changePoint', data, function (resp) {\r\n                if (typeof callback === 'function' && resp.result === 'success') {\r\n                    console.log('aaaaaaaaaaaaa', resp)\r\n                    callback(resp);\r\n                }\r\n            }, function () {\r\n            });\r\n        };\r\n        $scope.detailUnitForm = function () {\r\n            $.dm_datagrid.showAddForm({\r\n                module: $CFG.project + '/' + self.module,\r\n                action: 'add',\r\n                title: 'Cập nhật thông tin đơn vị',\r\n                size: size.small,\r\n                fullScreen: false,\r\n                showButton: false,\r\n                draggable: true,\r\n                content: function (element) {\r\n                    loadForm($CFG.remote.base_url + '/templates/admin/unit/popup_detail.html', '', {}, function (resp) {\r\n                        $scope.$apply(function () {\r\n                            $scope.$CFG.self_id_old = $scope.$CFG.self_id;\r\n                            $(element).html($scope.compile(resp, $scope));\r\n                        });\r\n                    });\r\n                }\r\n            });\r\n        };\r\n        $scope.updateUnitInfo = function () {\r\n            if ($scope.$CFG.self_id_old != $scope.$CFG.self_id) {\r\n                var data = {\r\n                    id: $scope.$CFG.self_id,\r\n                    async: true\r\n                };\r\n                process($CFG.remote.base_url + '/doing/admin/unit/updateInfo', data, function (resp) {\r\n                    if (resp.result == 'success') {\r\n                        dialogCloseAll();\r\n                    }\r\n                }, function () {\r\n\r\n                });\r\n            }\r\n        };\r\n        $scope._initSys();\r\n        /*\r\n        * Load template for cached\r\n        */\r\n        $scope.loadTemplates = function (urls, scope) {\r\n            if (typeof urls === 'string') {\r\n                urls = [urls];\r\n            }\r\n            for (var i in urls) {\r\n                $scope.getTemplate(urls[i]);\r\n            }\r\n            /* Bind template loaded to scope */\r\n            $scope.templateCache = $templateCache;\r\n        };\r\n        $scope.parseUrlTemp = parseUrlTemp;\r\n        $scope.getTemplate = getTemplate;\r\n\r\n        function parseUrlTemp(url) {\r\n            if (url.split('//').length === 1) {\r\n                if (url[0] !== '/') {\r\n                    url = '/' + url;\r\n                }\r\n                url = $CFG.template.base_url + url;\r\n            }\r\n            return url;\r\n        }\r\n\r\n        function getTemplate(url, callback, reload) {\r\n            var fullUrl = $scope.parseUrlTemp(url);\r\n            if (reload) {\r\n                $templateCache.remove(fullUrl);\r\n                $scope.sys.tempVer++;\r\n            }\r\n            var template = $templateCache.get(fullUrl);\r\n            if (template) {\r\n                if (typeof callback === 'function') {\r\n                    callback(template);\r\n                }\r\n            } else {\r\n                $templateRequest(fullUrl + '?_=' + $scope.sys.tempVer, false).then(function (template) {\r\n                    $templateCache.put(fullUrl, template);\r\n                    if (typeof callback === 'function') {\r\n                        callback(template);\r\n                    }\r\n                }, function () {\r\n                    if (typeof callback === 'function') {\r\n                        callback('Lỗi khi tải.');\r\n                    }\r\n                });\r\n            }\r\n            return template;\r\n        };\r\n        $scope.storage = $localStorage;\r\n    }])\r\n    .run(function ($templateCache) {\r\n        var overload = 0;\r\n\r\n        function _initSys() {\r\n            if (overload > 5) {\r\n                return;\r\n            }\r\n            process($CFG.remote.base_url + '/doing/dinhduong/index/sysConfigs', {}, function (resp) {\r\n                $templateCache.put('sysConfigs', resp);\r\n            }, function () {\r\n                overload++;\r\n                _initSys();\r\n            }, false, false, false);\r\n        }\r\n\r\n        if (!window.sysConfigs) {\r\n            _initSys();\r\n        }\r\n    });", "$.balance_money = {\r    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */\r    \tscope.balance_money = {\r            fields:[\r                { field: 'name', title: '<PERSON><PERSON><PERSON> thực phẩm', width: 200 },\r                { field: 'luong1tre_tmp', title: '<PERSON><PERSON>ợng (g)'},\r                { field: 'price_kg', type:'number', title: 'Đơn giá (đ/kg)'},\r                { field: 'price', title: 'Đơn giá theo ĐVT', type:'number'}\r            ], getValue: function(field, row){\r                return row[field.field];\r            }, formatter: function(field, value, row){\r                return value;\r            }, data: scope.datagrid.data,\r            getStyles: function(field) {\r                var styles = {};\r                var width = field.width;\r                if(width != undefined){\r                    width = width + '';\r                    if(width.split('%') == 1){\r                        width = width + 'px';\r                    }\r                    styles.width = width;\r                }\r                return styles;\r            }\r        };\r        scope.balance_moneyShow = function () {\r            dialogClose();\r            $.dm_datagrid.showAddForm({\r                title: '<PERSON><PERSON> đối tiền',\r                size: size.wide,\r                fullScreen: true,\r                scope: scope,\r                showButton: false,\r                content: $CFG.template.base_url + '/dinhduong/balance/balance_money.html',\r                onShown: function () {\r                    scope.balance_money.init();\r                }, reload: function () {\r                    scope.balance_money.init();\r                }\r            });\r        };\r        scope.balance_money.selectallfood = false;\r        scope.balance.money_min = 5;\r        scope.balance.money_minChange = function(){\r            if(scope.balance.money_min<1){\r                scope.balance.money_min = 1;\r            }\r        };\r        scope.balance_money.apply = function(){\r            scope.balance.data = scope.balance_money.data;\r            scope.balance.meals = scope.balance_money.meals;\r            scope.balance.apply();\r            dialogClose();\r        };\r        scope.balance_money.toZero = function(step) {\r            \r        };\r        scope.balance_money.init = function() {\r            scope.balance_money.data = clone(scope.balance.data);\r            scope.balance_money.meals = clone(scope.balance.meals);\r            scope.balance_money.foods = [];\r            angular.forEach(scope.balance_money.data, function(foods, meal_key){\r                angular.forEach(foods, function(food, food_id){\r                    if (scope.balance.data[meal_key][food_id].exports) {\r                        food.exports = clone(scope.balance.data[meal_key][food_id].exports);\r                    }\r                    food.tiendieuchinh = 0;\r                    food.thucmuatheodvt_old = scope.balance.data[meal_key][food_id].thucmuatheodvt;\r                    food.money_selected = false;\r                    food.foods = [];\r                });\r            });\r            angular.forEach(scope.balance_money.meals, function (meal, meal_define) {\r                var meal_key = scope.meal_defines[meal_define];\r                angular.forEach(meal.dishes, function (dish, dish_id) {\r                    angular.forEach(dish.ingredient, function (food, food_id) {\r                        if (!scope.balance_money.data[meal_key][food_id]) {\r                            scope.balance_money.data[meal_key][food_id] = clone(food);\r                            scope.balance_money.data[meal_key][food_id].foods = [];\r                            scope.balance_money.data[meal_key][food_id].luong1tre = 0;\r                            scope.balance_money.data[meal_key][food_id].thucmuatheodvt = 0;\r                            scope.balance_money.data[meal_key][food_id].thucmua1nhom = 0;\r                        }\r                        scope.balance_money.data[meal_key][food_id].luong1tre = $['+'](scope.balance_money.data[meal_key][food_id].luong1tre, food.quantity_edit);\r                        scope.balance_money.data[meal_key][food_id].foods.push(food);\r                    });\r                });\r            });\r            scope.balance_money.selectallfood = false;\r            scope.balance_money.totalCalculator();\r        };\r        /*  Tính lượng khẩu phần (calo) đạt hay chưa */\r        scope.balance_money.caloRate = function(){\r            var value = 0;\r            var calo = round(scope.sumCaloMeals(scope.balance_money.meals), 0);\r            var rate = scope.getNormSelected();\r            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {\r                value = 1;\r            } else {\r                if (calo < rate.smallest_rate) {\r                    value = 0;\r                } else {\r                    value = 2;\r                }\r            }\r\r            return value;\r        };\r        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */\r        scope.balance_money.plgRate = function(){\r            var rs = {};\r            var tile = scope.getTile_PLG(scope.balance_money.meals);\r            var tile_dat = [];\r            var tile_chuadat = [];\r            var tile_vuotqua = [];\r            if(tile.protein >= scope.selected.group.protein_min && tile.protein <= scope.selected.group.protein_max){\r                tile_dat.push({define:'protein',name: 'Chất đạm'});\r            }else if(tile.protein<scope.selected.group.protein_min) {\r                tile_chuadat.push({define:'protein',name: 'Chất đạm'})\r            }else{\r                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})\r            }\r            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){\r                tile_dat.push({define:'fat',name: 'Chất béo'});\r            }else if(tile.fat<scope.selected.group.fat_min) {\r                tile_chuadat.push({define:'fat',name: 'Chất béo'})\r            }else{\r                tile_vuotqua.push({define:'fat',name: 'Chất béo'})\r            }\r            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){\r                tile_dat.push({define:'sugar',name: 'Chất bột'});\r            }else if(tile.sugar<scope.selected.group.suga_min) {\r                tile_chuadat.push({define:'sugar',name: 'Chất bột'})\r            }else{\r                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})\r            }\r            rs = {\r                dat: tile_dat,\r                chuadat: tile_chuadat,\r                vuotqua: tile_vuotqua\r            };\r            return rs;\r        };\r        scope.balance_money.plgRateBind = function(){\r            var thanhphan = scope.balance_money.plgRate();\r            var text = '';\r            if(count(thanhphan.dat) == 3){\r                text = 'Cân đối';\r                class_color = '';\r            }else{\r                text = 'Chưa cân đối';\r                class_color = 'color-red';\r            }\r            return {text: text, 'class': class_color, thanhphan: thanhphan};\r        };\r        scope.balance_money.caloRateBind = function(){\r            var value = scope.balance_money.caloRate();\r            if(value == 1){\r                text = 'Đạt';\r                class_color = '';\r            }else if(value == 0){\r                text = 'Chưa đạt';\r                class_color = 'color-red';\r            }else{\r                text = 'Vượt quá định mức';\r                class_color = 'btn-color-blue';\r            }\r            return {text: text, 'class': class_color, value: value};\r        };\r        scope.balance_money.foodSelectAll = function() {\r            scope.balance_money.selectallfood = !scope.balance_money.selectallfood;\r            angular.forEach(scope.balance.data, function(kho,warehouse_id){\r                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);\r                angular.forEach(foods, function(food,food_id){\r                    food.money_selected = scope.balance_money.selectallfood;\r                })\r            });\r            scope.balance_money.foodSelectedChange();\r        };\r        scope.balance_money.foodClickSelected = function(meal_key_select, food){\r            var foods = [];\r            food.money_selected = !food.money_selected;\r            for(var meal_key in scope.balance_money.data){\r                fs = scope.balance_money.data[meal_key];\r                for(var food_id in fs){\r                    var food = fs[food_id];\r                    food.tiendieuchinh = 0;\r                    food.thucmuatheodvt = scope.balance.data[meal_key][food_id].thucmuatheodvt;\r                    food.thanhtien1nhom = scope.datagrid.data[meal_key][food_id].thanhtien1nhom;\r                    if(scope.row.meal_selection[meal_key].selected && food.money_selected){\r                        foods.push(food);\r                    }\r                }\r            }\r            console.log(food.name, foods);\r            scope.balance_money.foods = foods;\r            scope.balance_money.run(foods);\r            scope.balance_money.applyQuantity(foods);\r            scope.balance_money.totalCalculator();\r        };\r        scope.balance_money.isApply = function(){\r            var rs = false;\r            angular.forEach(scope.balance_money.data, function(kho,warehouse_id){\r                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);\r                angular.forEach(foods, function(food,food_id){\r                    if(food.money_selected && Number(food.tiendieuchinh)!=0){\r                        rs = true;\r                    }\r                });\r            });\r            return rs;\r        };\r        scope.balance_money.isNegative = function(){\r            var rs = true;\r            scope.balance_money.foods || (scope.balance_money.foods = []);\r            for (var food of scope.balance_money.foods) {\r                if(Number(food.thanhtien1nhom) < 0){\r                    rs = false;\r                    break;\r                }\r            }\r            return rs;\r        };\r        scope.balance_money.onChange_tiendieuchinh = function(fd){\r            var fds = [];\r            angular.forEach(scope.balance.data, function(kho,warehouse_id){\r                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);\r                angular.forEach(foods, function(food,food_id){\r                    if(food.money_selected && food.food_id != fd.food_id){\r                        food.tiendieuchinh = 0;\r                        fds.push(food);\r                    }\r                });\r            });\r            money = $['-'](scope.balance_money.tienchenhlech1tre , fd.tiendieuchinh);\r            scope.balance_money.run(fds, money);\r            scope.balance_money.applyQuantity();\r            scope.balance_money.totalCalculator();\r        };\r        scope.balance_money.autoSelectForm = function() {\r            $.dm_datagrid.showAddForm({\r                title: 'Chọn thưc phẩm muốn cố định',\r                size: size.wide,\r                fullScreen: true,\r                scope: scope,\r                showButton: false,\r                content: $CFG.template.base_url + '/dinhduong/balance/balance_money_option.html'\r            });\r        };\r        scope.balance_money.autoSelectStart = function() {\r\r        };\r        scope.balance_money.applyQuantity = function() {\r            angular.forEach(scope.balance_money.data, function(foods, meal_key){\r                angular.forEach(foods, function(food, food_id) {\r                    if (food.price && food.money_selected) {\r                        food.thanhtien1nhom = $['+'](scope.datagrid.data[meal_key][food_id].thanhtien1nhom, food.tiendieuchinh);\r                        food.thucmuatheodvt = $['/'](food.thanhtien1nhom, food.price);\r                        scope.onChange_thucmuatheodvt(food, true);\r                    }\r                });\r            });\r        };\r        scope.balance_money.totalCalculator = function(){\r            var thanhtien1nhom = 0;\r            angular.forEach(scope.balance_money.data, function(foods, meal_key){\r                angular.forEach(foods, function(food, food_id) {\r                    if(food.exports) {\r                        if (count(food.exports) == 1) {\r                            thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);\r                        } else {\r                            angular.forEach(food.exports, function(fd, food_id_price){\r                                thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));\r                            });\r                        }\r                    }else{\r                        thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);\r                    }\r                });\r            });\r            var service = scope.getTiendichvu();\r            var tongtien_dichvu = $['*'](service, scope.row.sotre);\r            var tien_bo_tro = scope.row.tien_bo_tro || 0;\r            var surplus_end = 0;\r            if (scope.surplus) {\r                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {\r                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];\r                }\r            }\r            scope.balance_money.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);\r            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, tien_bo_tro);\r            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, surplus_end);\r            console.log('thanhtien1nhom', thanhtien1nhom);\r        };\r        scope.balance_money.run = function(foods) {\r            var money = scope.row.tongtienchenhlech;\r            if(foods.length > 0){\r                if(scope.balance_money.money_min<=0){\r                    scope.balance_money.money_min = 1;\r                }\r                var tong_tien = 0;\r                angular.forEach(foods, function(food, index){\r                    tong_tien = $['+'](tong_tien , food.thanhtien1nhom);\r                });\r                var tong_tien_chia = 0;\r                var food_max = {tiendieuchinh:0};\r                var da_tru = 0;\r                var food_max = {tiendieuchinh:0};\r                angular.forEach(foods, function(food, index){\r                    var tile = food.thanhtien1nhom/tong_tien;\r                    var ext = $['*'](tile , money);\r                    var ext_round = scope.round(ext,0);\r                    food.tiendieuchinh = ext_round;\r                    if (food.tiendieuchinh )\r                    da_tru = $['+'](da_tru, ext_round);\r                    if(food_max.tiendieuchinh <= food.tiendieuchinh){\r                        food_max = food;\r                    }\r                });\r                if(money != da_tru){\r                    var con_lai = $['-'](money, da_tru);\r                    food_max.tiendieuchinh = $['+'](food_max.tiendieuchinh, con_lai);\r                }\r            }\r        };\r    }\r};\r", "$.balance = {\r    project: 'dinh<PERSON><PERSON>',\r    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */\r        scope.balance = {};\r        scope.balance.sophan = {\r            ngucoc: {name:'<PERSON><PERSON> cốc', foods:{}},\r            traicay: {name:'<PERSON><PERSON><PERSON><PERSON> cây', foods:{}},\r            rau: {name:'<PERSON><PERSON>', foods:{}},\r            damdv: {name:'<PERSON><PERSON> cốc', foods:{}},\r            khac: {name:'<PERSON><PERSON><PERSON><PERSON>', foods:{}}\r        };\r        scope.balance.templates = {\r            show: this.project + '/balance/balance.html',\r            change_money: this.project + '/balance/change_money_of_child.html',\r            keepBoundMeal: this.project + '/balance/keep_bound_meal.html'\r        };\r        // scope.loadTemplates(scope.templates, scope);\r        scope.balance.selectallfood = false;\r        scope.balance.checkautofortype = false;\r        scope.balance.keeping_recipes = true;\r        scope.balance.for_money = true;\r        scope.balance.for_quantity = true;\r        scope.balance.for_quanlity = true;\r        scope.balance.init = function() {\r            scope.balance.data = clone(scope.datagrid.data);\r            scope.balance.meals = clone(scope.selected.meals);\r            scope.balance.build_data();\r            scope.balance.totalCalculator();\r        };\r        scope.balance.build_data = function() {\r            var data = {};\r            angular.forEach(scope.balance.data, function (foods, meal_key) {\r                data[meal_key] = {};\r                angular.forEach(foods, function (food, food_id) {\r                    food.foods = [];\r                    data[meal_key][food_id] = food;\r                });\r            });\r            angular.forEach(scope.balance.meals, function (meal, meal_define) {\r                var meal_key = scope.meal_defines[meal_define];\r                angular.forEach(meal.dishes, function (dish, dish_id) {\r                    angular.forEach(dish.ingredient, function (food, food_id) {\r                        data[meal_key] || (data[meal_key] = clone(food));\r                        if (data[meal_key] == undefined) {\r                            delete dish.ingredient[food_id];\r                        } else if (data[meal_key][food_id] == undefined) {\r                            delete dish.ingredient[food_id];\r                        }else{\r                            data[meal_key][food_id].foods.push(food);\r                        }\r                    });\r                });\r            });\r            // angular.forEach(data, function (foods, wh_id) {\r            //     angular.forEach(foods, function (food, food_id) {\r            //         scope.onChange_luong1tre(food, true);\r            //     });\r            // });\r            scope.balance.data = data;\r        };\r        scope.balance.getFoodSelected = function(){\r            var rs = {};\r            scope.balance.nutritions || (scope.balance.nutritions = {});\r            angular.forEach(scope.balance.data, function(foods, meal_key){\r                angular.forEach(foods, function(food,food_id) {\r                    if(!scope.balance.nutritions[food_id]) {\r                        scope.balance.nutritions[food_id] = food.nutritions;\r                    }else{\r                        food.nutritions = scope.balance.nutritions[food_id];\r                    }\r                    if(food.selected){\r                        rs[meal_key] || (rs[meal_key] = {});\r                        rs[meal_key][food_id] || (rs[meal_key][food_id] = 0);\r                        rs[meal_key][food_id] += food.luong1tre;\r                    }\r                });\r            });\r            return rs;\r        };\r        scope.balance.getData = function(meals, is_meals) {\r            /*Đoạn này viết để lấy cấu trúc thực đơn*/\r            scope.balance.nutritions = {};\r            var keep_foods = scope.balance.getFoodSelected(); /* Danh sách thực phẩm cố định lượng*/\r            var nutritions = scope.balance.nutritions;\r            var tmps = {};\r            var tmp_foods = {};\r            var total_calo = 0;\r            meals || (meals = scope.selected.meals);\r            angular.forEach(meals, function(meal, meal_define) {\r                tmps[meal_define] = [];\r                if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;\r                tmp_foods[meal.warehouse_id] || (tmp_foods[meal.warehouse_id]= {});\r                var meal_key = 2;\r                if (meal_define == 'buasang') {\r                    meal_key = 1;\r                } else if (meal_define == 'buatoi') {\r                    meal_key = 3;\r                }\r                angular.forEach(meal.dishes, function(dish, dish_id) {\r                    var tmp_meals = [];\r                    angular.forEach(dish.ingredient, function(food, food_id) {\r                        if(!nutritions[food_id]) {\r                            nutritions[food_id] = food.nutritions;\r                        }else{\r                            food.nutritions = nutritions[food_id];\r                        }\r                        food.gam_exchange || (food.gam_exchange = 1000);\r                        var f = {name: food_id};\r                        if(food.extrude_factor){\r                            food.extrude_factor = Number(food.extrude_factor);\r                            if(food.extrude_factor >= 100) {\r                                food.extrude_factor = 50;\r                            }\r                        }\r                        f.quantity = Number(food.quantity_edit);\r                        if(f.quantity < 0) {\r                            f.quantity = 0;\r                        }\r                        if(food.price_kg) {\r                            f.price = food.price_kg;\r                        }else{\r                            f.price = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));\r                        }\r                        if(!f.price){\r                            f.price = 0;\r                        }\r                        f.protein = $['*'](food.nutritions.protein * 10 , 4.1);\r                        f.fat = $['*'](food.nutritions.fat * 10 , 9.3);\r                        f.sugar = $['*'](food.nutritions.sugar * 10 , 4.1); \r                        f.PLG = $['+']($['+'](f.protein , f.fat) , f.sugar);    /*(Kcal)*/\r                        total_calo += (\r                                f.quantity * food.nutritions.protein / 100 * 4.1 +\r                                f.quantity * food.nutritions.fat / 100 * 9.3 +\r                                f.quantity * food.nutritions.sugar / 100 * 4.1 \r                            );\r                        if(food.extrude_factor > 0){\r                            f.price = (100*f.price)/(100 - food.extrude_factor);\r                            var gam = (100*100)/(100 - food.extrude_factor);\r                        }\r                        if(in_array_key(food_id, keep_foods[meal_key])) {\r                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){\r                                if(food_id+'' == f_id+''){\r                                    f['i_' + meal_key + '_' + f_id] = 1.0;\r                                }else{\r                                    f['i_' + meal_key + '_' + f_id] = 0.0;\r                                }\r                            });\r                        }else{\r                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){\r                                f['i_' + meal_key + '_' + f_id] = 0.0;\r                            });\r                        }\r                        tmp_meals.push(f);\r                        // if(!tmp_foods[meal_key][food_id]) {\r                        //     tmp_foods[meal_key][food_id] = {\r                        //         id: food_id,\r                        //         name: food.name,\r                        //         extrude_factor: food.extrude_factor,\r                        //         gam_exchange: food.gam_exchange,\r                        //         quantity: 0,\r                        //         price: food.price,\r                        //         quantity_real: 0\r                        //     }\r                        // }\r                        // tmp_foods[meal_key][food_id].quantity = $['+'](tmp_foods[meal_key][food_id].quantity,food.quantity_edit);\r                        // tmp_foods[meal_key][food_id].quantity_real = $['+'](tmp_foods[meal_key][food_id].quantity_real,f.quantity);\r                    });\r                    dish.lower_bound = Number(dish.lower_bound);\r                    dish.upper_bound = Number(dish.upper_bound);\r                    if(!dish.lower_bound){\r                        dish.lower_bound = 5\r                    }\r                    if(!dish.upper_bound){\r                        dish.upper_bound = 500\r                    }\r                    if(dish.upper_bound < dish.lower_bound){\r                        dish.upper_bound = dish.lower_bound;\r                    }\r                    var tmp_disk = {\r                        dish: dish_id,\r                        lower_bound: dish.lower_bound/1000,\r                        upper_bound: dish.upper_bound/1000,\r                        ingredients: tmp_meals\r                    };\r                    if(count(tmp_meals)==0) {\r                        tmp_disk.lower_bound = 0;\r                    }\r                    tmps[meal_define].push(tmp_disk);\r                });\r            });\r            // console.log(tmps); return;\r            var group_id = scope.row.group_id;\r            var group_selected = scope.getGroupSelected();\r            var nutritions = scope.selected.group.nutritions;\r            var protein = (nutritions.animal_protein + nutritions.vegetable_protein) * 4.1;\r            var fat = (nutritions.animal_fat + nutritions.vegetable_fat) * 9.3;\r            var sugar = nutritions.sugar * 4.1;\r            var calo = protein+fat+sugar;\r            var asym = 0.5;\r            /*Kiểm tra và tính thêm tỉ lệ bữa sáng nếu có*/\r            var tmp_meal = {\r                buasang_min: $['/']($['*'](group_selected.meals.buasang.min + asym, nutritions.calo) , 100),\r                buasang_max: $['/']($['*'](group_selected.meals.buasang.max - asym , nutritions.calo) , 100),\r                buatrua_min: $['/']($['*'](group_selected.meals.buatrua.min + asym , nutritions.calo) , 100),\r                buatrua_max: $['/']($['*'](group_selected.meals.buatrua.max - asym , nutritions.calo) , 100),\r                buaxe_min: $['/']($['*'](group_selected.meals.buaxe.min + asym , nutritions.calo) , 100),\r                buaxe_max: $['/']($['*'](group_selected.meals.buaxe.max - asym , nutritions.calo) , 100),\r                buaphu_min: $['/']($['*'](group_selected.meals.buaphu.min + asym , nutritions.calo) , 100),\r                buaphu_max: $['/']($['*'](group_selected.meals.buaphu.max - asym , nutritions.calo) , 100)\r            };\r            asym = 0.5;\r            var buasang_norm = scope.getNormBuasang();\r            var smallest_rate = 0;\r            var biggest_rate = 0;\r            if(scope.dinhduong_calc[1]){\r                smallest_rate += buasang_norm.smallest_rate;\r                biggest_rate += buasang_norm.biggest_rate;\r            }\r            if(scope.dinhduong_calc[2]){\r                smallest_rate += scope.selected.group.smallest_rate;\r                biggest_rate += scope.selected.group.biggest_rate;\r            }\r            var tmp_norm = {\r                protein_min: $['/']($['*'](protein , smallest_rate + asym) , 100),\r                fat_min: $['/']($['*'](fat , smallest_rate + asym) , 100),\r                sugar_min: $['/']($['*'](sugar , smallest_rate + asym) , 100),\r                protein_max: $['/']($['*'](protein , biggest_rate - asym) , 100),\r                fat_max: $['/']($['*'](fat , biggest_rate - asym) , 100),\r                sugar_max: $['/']($['*'](sugar , biggest_rate - asym) , 100),\r                calo_min: 0,\r                calo_max: 0\r            };\r            if(scope.dinhduong_calc[1]){\r                tmp_norm.calo_min += nutritions.calo_rate_morning_low;\r                tmp_norm.calo_max += nutritions.calo_rate_morning;\r            }\r            if(scope.dinhduong_calc[2]){\r                tmp_norm.calo_min += nutritions.calo_rate_low;\r                tmp_norm.calo_max += nutritions.calo_rate;\r            }\r\r            var tmp_quantity = {\r                protein_min: (scope.selected.group.protein_min + asym)/100,\r                fat_min: (scope.selected.group.fat_min + asym)/100,\r                sugar_min: (scope.selected.group.sugar_min + asym)/100,\r                protein_max: (scope.selected.group.protein_max - asym)/100,\r                fat_max: (scope.selected.group.fat_max - asym)/100,\r                sugar_max: (scope.selected.group.sugar_max - asym)/100\r            };\r            var service_price = scope.getTiendichvu();\r            var data = {\r                keeping_recipes: (scope.balance.keeping_recipes?1:0),\r                menu_price: scope.row.tien1tre - service_price,\r                input_menu: tmps,\r                constraints: []\r            };\r            if(scope.balance.for_money) {}\r            if(scope.balance.for_quantity) {\r                data.constraints.push({\"name\": \"PLG\", \"lower_bound\": tmp_norm.calo_min+1, \"upper_bound\": tmp_norm.calo_max-1, \"type\": \"nominal\", denominator: \"\",\"meal\":\"all\"});\r            }\r            if(scope.balance.for_quanlity) {\r                data.constraints.push({name: 'protein', lower_bound: tmp_quantity.protein_min, upper_bound: tmp_quantity.protein_max,type:'percentage',denominator: \"PLG\",meal:\"all\"});\r                data.constraints.push({name: 'fat', lower_bound: tmp_quantity.fat_min, upper_bound: tmp_quantity.fat_max,type:'percentage',denominator: \"PLG\",meal:\"all\"});\r                data.constraints.push({name: 'sugar', lower_bound: tmp_quantity.sugar_min, upper_bound: tmp_quantity.sugar_max,type:'percentage',denominator: \"PLG\",meal:\"all\"});\r            }\r\r            if(is_meals){\r                angular.forEach(tmps, function(meal, meal_define){\r                    var kt = false;\r                    for(var i in tmps[meal_define]) {\r                        if(count(tmps[meal_define][i].ingredients)>0){\r                            kt = true;\r                            break;\r                        }\r                    }\r                    if(kt){\r                        data.constraints.push({\r                            name: 'PLG', \r                            lower_bound: tmp_meal[meal_define+'_min'], \r                            upper_bound: tmp_meal[meal_define+'_max'], \r                            type:'nominal', \r                            denominator: \"\", \r                            meal: meal_define \r                        });\r                    }\r                });\r            }\r            /*  Thêm điều kiện cố định thực phẩm    */\r            if(count(keep_foods)>0) {\r                angular.forEach(keep_foods, function(foods, meal_key){\r                    angular.forEach(foods, function(quantity, food_id){\r                        data.constraints.push({\r                            name: 'i_' + meal_key + '_' + food_id,\r                            lower_bound: quantity/1000,\r                            upper_bound: quantity/1000,\r                            type: \"nominal\",\r                            denominator: \"\",\r                            meal: \"all\"\r                        });\r                    });\r                });\r            }\r            return data;\r        };\r        scope.balance.alertData = function(meals){\r            var data = scope.balance.getData(meals,true);\r            var rand = (Math.random()+'').split('.')[1];\r            scope.balance.data_tmp = data;\r            var html = '<div><div>{{balance.data_tmp}}</div></div>';\r            $.dm_datagrid.show({\r                title: 'Menu input for processing',\r                message: '<div id=\"' + rand + '\" style=\"padding: 15px; min-height: 350px; overflow-x: auto;\"></div>'\r            }, function() {\r                setTimeout(function() {\r                    scope.$apply( function() {\r                        $('#'+rand).html(scope.compile(html));\r                    });\r                });\r            });\r        };\r        scope.balance.alertEnd = function(resp, msgs) {\r            msgs || (msgs = []);\r            var status = resp.status;\r            switch (status){\r                case 0:\r                    break;\r                case 1:\r                    break;\r                case 2:\r                    msgs.push('Tiền ăn quá thấp. Đã giảm lượng thực phẩm tối đa để tiền thiếu ít nhất');\r                    break;\r                case 3:\r                    msgs.push('Tiền ăn quá cao. Đã tăng lượng thực phẩm tối đa để tiền thừa ít nhất.');\r                    break;\r                case 4:\r                    msgs.push('Công thức (tỉ lệ thực phẩm) bị thay đổi, dẫn đến món ăn có thể không nấu được.');\r                    break;\r                default:\r                    msgs.push('Hệ thống không thể cân đối.');\r                    msgs.push('Hãy kiểm tra và chọn lại thực đơn đầu vào.');\r                    msgs.push('Hoặc lựa chọn tiện ích khác nếu có.');\r                    break;\r            }\r            /*Kiểm tra có thực phẩm nào giá bằng 0 để đưa thông báo*/\r            var tmp_foods = {};\r            angular.forEach(scope.datagrid.data, function(foods, warehouse_id){\r                angular.forEach(foods,function(food,food_id){\r                    if(food.price == 0){\r                        tmp_foods[food_id] = food;\r                    }\r                })\r            });\r            var foods = [];\r            angular.forEach(tmp_foods, function(food,food_id){\r                foods.push(food.name);\r            });\r            if(foods.length) {\r                msgs.push('Một số thực phẩm chưa có đơn giá hãy kiểm tra lại để đảm bảo việc cân đối được chính xác : '+foods.join(', '));\r            }\r            if(count(msgs)>0) {\r                $.dm_datagrid.show({\r                    size: size.small,\r                    title: 'Thông báo',\r                    message: '<div style=\"padding:15px;min-height:250px;overflow-x: auto\"> - '+msgs.join('<br/> - ')+'</div>'\r                });\r            }\r        };\r        scope.balance.processAction = function(meals) {\r            meals || (meals = scope.balance.meals);\r            var tien1tre;\r            var canh_bao = (scope.row.meal_selection[1].selected != scope.row.meal_selection[2].selected);\r            if (canh_bao) {\r                angular.forEach(scope.row.meal_selection, function (meal, meal_key) {\r                    if (!meal.selected && meal.visible) {\r                        if (count(scope.balance.data[meal_key]) == 0) {\r                            canh_bao = false;\r                        }\r                    }\r                });\r            }\r            if (canh_bao) {\r                scope.balance.meals = meals;\r                $.dm_datagrid.showAddForm({\r                    module: $CFG.project + '/' + self.module,\r                    title: 'Tiền ăn 1 trẻ',\r                    draggable: true,\r                    fullScreen: false,\r                    showButton: false,\r                    scope: scope,\r                    content: scope.balance.templates.change_money,\r                });\r            }else{\r                scope.balance.send(meals);\r            }\r        };\r        scope.balance.send = function(meals, tien1tre) {\r            meals || (meals = scope.balance.meals);\r            var data = scope.balance.getData(meals,true);\r            data.input_menu = JSON.stringify(data.input_menu);\r            data.constraints = JSON.stringify(data.constraints);\r            data.async = true;\r            if (tien1tre) {\r                data.menu_price = tien1tre;\r            }\r            var url = $CFG.balance_api;\r            process(url, data, function(resp) {\r                if(in_array(resp.status, [1])) {\r                    scope.balance.process(resp);\r                    var msgs = [];\r                    msgs.push('Thực đơn đã được cân đối.');\r                    scope.balance.alertEnd(resp, msgs);\r                } else {\r                    data = scope.balance.getData(meals);\r                    data.input_menu = JSON.stringify(data.input_menu);\r                    data.constraints = JSON.stringify(data.constraints);\r                    if (tien1tre) {\r                        data.menu_price = tien1tre;\r                    }\r                    data.async = true;\r                    process(url, data, function(resp) {\r                        var msgs = [];\r                        if(in_array(resp.status, [1,2,3])){\r                            if(in_array(resp.status, [1,2,3])){\r                                msgs.push('Thực đơn đã được cân đối..');\r                            }\r                            scope.balance.process(resp);\r                            console.log('calo theo bữa: not ok.');\r                        }else{\r                            console.log('thực đơn không cân đối được.');\r                        }\r                        scope.balance.alertEnd(resp, msgs);\r                    },null,false);\r                }\r            }, null, false);\r        };\r        scope.balance.process = function(resp) {\r            setTimeout(function () {\r                scope.$apply(function () {\r                    var keep_foods = scope.balance.getFoodSelected();\r                    var tmp_fds = {};\r                    var meals = resp.output_menu;\r                    var status = resp.status;\r                    var root_meals = scope.balance.meals;\r                    angular.forEach(meals, function(meal, meal_define) {\r                        if (count(meal) > 0 ) {\r                            if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;\r                            var meal_key = 2;\r                            if (meal_define == 'buasang') {\r                                meal_key = 1;\r                            } else if (meal_define == 'buatoi') {\r                                meal_key = 3;\r                            }\r                            angular.forEach(meal, function(dish, ind) {\r                                angular.forEach(dish.ingredients, function (food, ind1) {\r                                    if (keep_foods[meal_key]) {\r                                        if (keep_foods[meal_key][food.food_id]) {\r                                            return;\r                                        }\r                                    }\r                                    root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity = food.quantity * 1000;\r                                    root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity_edit = food.quantity * 1000;\r                                });\r                            });\r                        }\r                    });\r                    scope.balance.build_data();\r                    angular.forEach(scope.balance.data, function(foods, meal_key) {\r                        angular.forEach(foods, function (food, food_id) {\r                            food.foods = [];\r                            if (keep_foods[meal_key]) {\r                                if (keep_foods[meal_key][food.food_id]) {\r                                    return;\r                                }\r                            }\r                            food.luong1tre = 0;\r                        });\r                    });\r                    angular.forEach(root_meals, function (meal, meal_define) {\r                        var meal_key = 2;\r                        if (meal_define == 'buasang') {\r                            meal_key = 1;\r                        } else if (meal_define == 'buatoi') {\r                            meal_key = 3;\r                        }\r                        angular.forEach(meal.dishes, function(dish, ind) {\r                            angular.forEach(dish.ingredient, function (food, food_id) {\r                                scope.balance.data[scope.meal_defines[meal.define]][food_id].foods.push(food);\r                                if (keep_foods[meal_key]) {\r                                    if (keep_foods[meal_key][food.food_id]) {\r                                        return;\r                                    }\r                                }\r                                scope.balance.data[scope.meal_defines[meal.define]][food_id].luong1tre += food.quantity_edit;\r                            });\r                        });\r                    });\r                    angular.forEach(scope.balance.data, function(foods, meal_key) {\r                        angular.forEach(foods, function (food, food_id) {\r                            if (keep_foods[meal_key]) {\r                                if (keep_foods[meal_key][food.food_id]) {\r                                    return;\r                                }\r                            }\r                            scope.onChange_luong1tre(food, true);\r                        });\r                    });\r                    scope.balance.totalCalculator();\r                });\r            });\r        };\r        scope.balance.roundThucmuatheo_dvt = function(food,status){\r            var num = 0;\r            var price = food.price+'';\r            if(status==1) {\r                if(food.thucmuatheodvt <= 0.00005){\r                    food.thucmuatheodvt = 0.0001;\r                }\r                if(price.split('.').length==0){\r                    num = 0;\r                }else{\r                    price += 'aa';\r                    if(price.split('0000aa').length==2){\r                        num = 4;\r                    }else if(price.split('000aa').length==2){\r                        if(food.thucmuatheodvt<=0.0005){\r                            food.thucmuatheodvt = 0.001;\r                        }\r                        num = 3;\r                    }else if(price.split('00aa').length==2){\r                        if(food.thucmuatheodvt<=0.005){\r                            food.thucmuatheodvt = 0.01;\r                        }\r                        num = 2;\r                    }else if(price.split('0aa').length==2){\r                        if(food.thucmuatheodvt<=0.05){\r                            food.thucmuatheodvt = 0.1;\r                        }\r                        num = 1;\r                    }\r                    if(num==0 && food.thucmuatheodvt<0.5){\r                    }else{\r                        food.thucmuatheodvt = round(food.thucmuatheodvt, num);\r                    }\r                }\r            } else {\r                food.thucmuatheodvt = scope.round_thucmuatheodvt(food.thucmuatheodvt, 2);\r                if((price.replace('0aa')+'aa').split('0aa').length==1 && food.thucmuatheodvt > 0.1){\r                    food.thucmuatheodvt = round(food.thucmuatheodvt, 1);\r                }else if(price.split('0aa').length==1 && food.thucmuatheodvt > 1){\r                }\r            }\r            return food;\r        };\r\r        scope.balance.apply = function() {\r            var foods = {};\r            angular.forEach(scope.balance.data, function(foods, meal_key){\r                angular.forEach(foods, function(food, food_id){\r                    scope.datagrid.data[meal_key][food_id] = food;\r                    scope.onChange_thucmuatheodvt(food, true);\r                    if (food.luong1tre == 0) {\r                        food.luong1tre = 0.05;\r                        scope.onChange_luong1tre(food, true);\r                    }\r                });\r            });\r            angular.forEach(scope.balance.meals, function (meal, meal_define) {\r                scope.selected.meals[meal_define] = meal;\r                angular.forEach(meal.dishes, function (dish, ind) {\r                    angular.forEach(dish.ingredient, function (fd, ind1) {\r                        fd.quantity = round(fd.quantity, 3);\r                    });\r                });\r            });\r            scope.totalCalculator();\r            scope.balance.init();\r        };\r        scope.balance.toZeroMoney = function(){\r            var chenh = round(scope.balance.getTongchenh(),1);\r            if(Math.abs(chenh) == 0 || Math.abs(chenh) > 4000) { \r                return;\r            }\r            /*Tìm kiếm thực phẩm giá cao nhất mà có số 0 đăng sau lớn hơn hoặc bằng 3*/\r            var foods = {};\r            angular.forEach(scope.datagrid.data,function(fds, warehouse_id){\r                angular.forEach(fds,function(fd,food_id){\r                    var price = fd.price+'aa';\r                    if(fd.price > 0 && price.split('0000aa').length==2 || price.split('000aa').length==2){\r                        foods[food_id] = fd;\r                    }\r                })\r            });\r            var fds = [];\r            angular.forEach(foods,function(food,food_id){\r                fds.push(food);\r            });\r            if(fds.length>1){\r                /*Sắp xếp giảm dần theo số lượng thực phẩm*/\r                for(var i=0; i<fds.length-1; i++) {\r                    for(var j=i+1; j<fds.length; j++){\r                        if(fds[i].thucmuatheodvt_balance<fds[j].thucmuatheodvt_balance){\r                            var tmp = fds[i];\r                            fds[i] = fds[j];\r                            fds[j] = tmp;\r                        }\r                    }\r                }\r                /*Lấy 1 nửa ds thực phẩm thôi*/\r                foods = [];\r                for(var i=0; i<fds.length/2; i++){\r                    foods.push(fds[i]);\r                }\r                /*sắp xếp ds giảm dần theo giá*/\r                for(var i=0; i<foods.length-1; i++) {\r                    for(var j=i+1; j<foods.length; j++){\r                        \r                        if(foods[i].price<foods[j].price){\r                            var tmp = foods[i];\r                            foods[i] = foods[j];\r                            foods[j] = tmp;\r                        }\r                    }\r                }\r            }\r            if(foods[0]){\r                scope.balance.addThucmuaForFood(foods[0],chenh);\r                scope.balance.totalCalculator(); \r                var thucmua_arr = foods[0].thucmuatheodvt_balance+''.split('.');\r                if(thucmua_arr.length == 2){\r                    if(thucmua_arr[1].length>4){}\r                }\r                chenh = round(scope.balance.getTongchenh(),1);\r            }\r        };\r        scope.balance.addThucmuaForFood = function(food,chenh) {\r            var thucmua_chenh = $['/'](chenh,food.price);\r            food.thucmuatheodvt_balance = Math.abs($['+'](food.thucmuatheodvt_balance,thucmua_chenh));\r        };\r        scope.balance.addThucmuaForFood1 = function(food,chenh) {\r            for(var i=1; i<=5; i++){\r                var per = i*2;\r                var thucmua_chenh = $['/']($['/'](chenh,food.price),per);\r                food.thucmuatheodvt_balance = $['+'](food.thucmuatheodvt_balance,thucmua_chenh);\r                scope.balance.totalCalculator();\r                chenh = scope.balance.getTongchenh();\r            }\r        };\r        scope.balance.formKeepBound_meal = function(module,meals){\r            angular.forEach(meals, function(meal,meal_define){\r                angular.forEach(meal.dishes, function(dish, dish_id){\r                    if(!dish.lower_bound){\r                        dish.lower_bound = 5;\r                    }\r                    if(!dish.upper_bound){\r                        dish.upper_bound = 500;\r                    }\r                });\r            });\r            $.dm_datagrid.showAddForm(\r                {\r                    module: $CFG.project+'/'+module,\r                    action:'balance',\r                    title:'Cân đối',\r                    size: 800,\r                    fullScreen: false,\r                    showButton: false,\r                    content: function(element){\r                        $(element).html(scope.getTemplate(scope.balance.templates.keepBoundMeal));\r                    }\r                },\r                function(resp){\r                    \r                }\r            );\r        };\r        scope.balance.show = function(){\r            scope.balance.init();\r            var nutritions = scope.selected.group.nutritions;\r            if(nutritions+'' === 'null') {\r                var msg = ['Không thể cân đối vì chưa cấu hình định mức dinh dưỡng cho nhóm trẻ đang chọn.'];\r                msg.push('<a href=\"'+$CFG.remote.base_url+'/single/'+$CFG.project+'/norm\">Vào cấu hình định mức</a>');\r                $.dm_datagrid.show({\r                    title: 'Thông báo',\r                    message: '<div style=\"padding:15px;height:100px;\">'+msg.join('<br/>')+'</div>'\r                });\r                return;\r            }\r            $.dm_datagrid.showAddForm(\r                {\r                    action: 'balance',\r                    title: 'Cân đối dinh dưỡng tự động',\r                    size: size.wide,\r                    scope: scope,\r                    fullScreen: true,\r                    showButton: false,\r                    content: scope.balance.templates.show\r                }\r            );\r        };\r        scope.balance.runAuto = function(){\r            showSpinner('balance_auto');\r            /*Kiểm tra nếu có số phần nào đó bằng 0 thì chạy theo lượng như bản cũ*/\r            /*ngược lại cân đối theo số phần*/\r            var sophan = scope.balance.getCaloFor_SoPhan();\r            var kt = true;\r            for(var i in sophan){\r                if(sophan[i] == 0){\r                    kt = false;\r                    break;\r                }\r            }\r            if(kt && false){\r                scope.balance.runSophan();\r            }else{\r                scope.balance.runQuantity();\r                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                    angular.forEach(kho, function(food,food_id){\r                        if(food.luong1tre_balance+'' == 'Infinity'){\r                            food.luong1tre_balance = food.luong1tre;\r                        }else if(food.luong1tre_balance == 0){\r                            food.luong1tre_balance == 0.01;\r                        }\r                        scope.balance.onChange_luong1tre(food);\r                    })\r                })\r            }\r            hiddenSpinner('balance_auto');\r        }\r        /*Tổng hợp lượng theo số phần*/\r        scope.balance.getCaloFor_SoPhan = function(){\r            var ngucoc = 0;\r            var traicay = 0;\r            var rau = 0;\r            var damdv = 0;\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                angular.forEach(kho, function(food,index){\r                    if(warehouse_id == 2){\r                        var calo = 0;\r                        if(!scope.apdungcongthuc){\r                            calo += $['*'](scope.co_cau_chuan.protein, food.luong1tre_balance)*(food.nutritions.protein/100)\r                                + $['*'](scope.co_cau_chuan.fat, food.luong1tre_balance)*(food.nutritions.fat/100)\r                                + $['*'](scope.co_cau_chuan.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)\r                        }else{\r                            calo += $['*'](scope.co_cau.protein, food.luong1tre_balance)*(food.nutritions.protein/100)\r                                + $['*'](scope.co_cau.fat, food.luong1tre_balance)*(food.nutritions.fat/100)\r                                + $['*'](scope.co_cau.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)\r                        }\r                        if(food.ngucoc){\r                            ngucoc = $['+'](ngucoc,calo);\r                        }else if(food.traicay){\r                            traicay = $['+'](traicay,calo);\r                        }else if(food.rau){\r                            rau = $['+'](rau,calo);\r                        }else if(food.damdv){\r                            damdv = $['+'](damdv,calo);\r                        }\r                    }\r                });\r            });\r            return {ngucoc: ngucoc, traicay: traicay, rau: rau, damdv: damdv};\r        };\r\r        scope.balance.getFoodsFor_SoPhan = function(){\r            scope.balance.sophan = {\r                ngucoc: {name:'Ngũ cốc',foods:{}},\r                traicay: {name:'Trái cây',foods:{}},\r                rau: {name:'Rau',foods:{}},\r                damdv: {name:'Ngũ cốc',foods:{}},\r                khac: {name:'Khác',foods:{}}\r            };\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                angular.forEach(kho, function(food,food_id){\r                    if(scope.selected.balance_warehouse[warehouse_id]) {\r                        if(!food.selected) {\r                            if(food.ngucoc){\r                                scope.balance.sophan.ngucoc.foods[food_id] = food;\r                            }else if(food.traicay){\r                                scope.balance.sophan.traicay.foods[food_id] = food;\r                            }else if(food.rau){\r                                scope.balance.sophan.rau.foods[food_id] = food;\r                            }else if(food.damdv){\r                                scope.balance.sophan.damdv.foods[food_id] = food;\r                            }else{\r                                scope.balance.sophan.khac.foods[food_id] = food;\r                            }\r                        }\r                    }\r                });\r            });\r        };\r\r        scope.balance.onChange_luong1tre = function(food) {\r            scope.onChange_luong1tre(food, true);\r            scope.balance.totalCalculator();\r        };\r        scope.balance.onChange_thucmuatheodvt = function(food){\r            scope.onChange_thucmuatheodvt(food, true);\r            scope.balance.totalCalculator();\r        };\r        /*Tổng tiền chênh lệch 1 tre*/\r        scope.balance.getTiendicho1tre = function() {\r            var rs = 0;\r            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){\r                angular.forEach(foods, function(food,food_id){\r                    rs = $['+'](rs,food.thanhtien1nhom_balance);\r                });\r            });\r            return scope.round($['/'](rs,scope.row.sotre));\r        };\r        /*Tổng tiền chênh lệch 1 tre*/\r        scope.balance.getTiendicho_all = function() {\r            var rs = 0;\r            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){\r                angular.forEach(foods, function(food,food_id){\r                    var thanhtien = $['*'](food.thucmuatheodvt_balance,food.price);\r                    rs = $['+'](rs,thanhtien);\r                });\r            });\r            return rs;\r        };\r        scope.balance.getTongchenh = function(){\r            var service_all = $['*'](scope.getTiendichvu(),scope.row.sotre);\r            var tienan_all = $['*'](scope.row.tien1tre,scope.row.sotre);\r            var tiendicho_all = scope.balance.getTiendicho_all();\r            return $['-']($['-'](tienan_all, tiendicho_all), service_all);\r        };\r        scope.balance.getTienchenhlech = function(){\r            var tiendicho1tre = scope.balance.getTiendicho1tre();\r            var service = scope.getTiendichvu();\r            return scope.round($['-']($['-'](scope.row.tien1tre, tiendicho1tre), service));\r        };\r        scope.balance.totalCalculator = function(){\r            var thanhtien1nhom = 0;\r            angular.forEach(scope.balance.data, function(foods, meal_key){\r                angular.forEach(foods, function(food, food_id) {\r                    if(food.exports) {\r                        angular.forEach(food.exports, function(fd, food_id_price){\r                            thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));\r                        });\r                    }else{\r                        thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](food.thucmuatheodvt, food.price));\r                    }\r                });\r            });\r            var service = scope.getTiendichvu();\r            var tongtien_dichvu = $['*'](service, scope.row.sotre);\r            var tien_bo_tro = scope.row.tien_bo_tro || 0;\r            var surplus_end = 0;\r            if (scope.surplus) {\r                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {\r                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];\r                }\r            }\r            scope.balance.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);\r            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, tien_bo_tro);\r            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, surplus_end);\r        };\r        scope.balance.isApply = function(){\r            for(var meal_key in scope.balance.data){\r                for(var food_id in scope.balance.data[meal_key]) {\r                    console.log(scope.balance.data[meal_key][food_id].name, scope.balance.data[meal_key][food_id].luong1tre, scope.datagrid.data[meal_key][food_id].luong1tre)\r                    if(scope.balance.data[meal_key][food_id].luong1tre != scope.datagrid.data[meal_key][food_id].luong1tre){\r                        return false;\r                    }\r                }\r            }\r            return true;\r        };\r        \r        /*Tự động cân dối về lượng*/\r        scope.balance.runQuantity = function(){\r            var buasang_norm = scope.getNormBuasang();\r            var max_i = (scope.selected.group.biggest_rate+buasang_norm.biggest_rate - scope.selected.group.smallest_rate-buasang_norm.smallest_rate)/2;\r            var max_protein = (scope.selected.group.protein_max - scope.selected.group.protein_min)/2\r            var max_fat = (scope.selected.group.fat_max - scope.selected.group.fat_min)/2\r            var max_sugar = (scope.selected.group.sugar_max - scope.selected.group.sugar_min)/2\r            if(max_i>20){\r                max_i = 20;\r            }\r            if(scope.balance.caloRate()==1 && count(scope.balance.plgRate().dat) == 3){\r                return true;\r            }\r            for(var test=0; test<=10;test++) {\r                for(var j=0; j<=max_i;j++) {\r                    for(var i=0; i<=max_i; i++){\r                        for(var k=0;k<=max_i;k++){\r                            if(!scope.balance.whileNotPassQuantity(i,j,k)) {\r                                                                \r                            }/*else if(count(scope.balance.plgRate().dat) == 3){\r                                return true;\r                            }*/\r                        }\r                    }\r                }\r                if(count(scope.balance.plgRate().dat) != 3 && scope.balance.caloRate()==1) {\r                    for(var i=0; i<=max_protein;i++) {\r                        for(var j=0; j<=max_fat; j++){\r                            for(var k=0;k<=max_sugar;k++){\r                                if(!scope.balance.whileNotPassPLG(i,j,k)) {\r                                    \r                                }\r                            }\r                        }\r                    }\r                }\r            }\r        };\r        scope.balance.getTile_PLG_food = function(food){\r\r        };\r        \r        scope.balance.onChangeThucmua1tretheodvt = function(item){\r            var thucmua1tretheodvt = Number(item.thucmua1tretheodvt);\r            var luong1tretheodvt = thucmua1tretheodvt;\r            if(item.extrude_factor){\r                luong1tretheodvt = $['/'](thucmua1tretheodvt,(1+1/(100/item.extrude_factor - 1)));\r            }\r            item.luong1tre_balance = $['*'](luong1tretheodvt,item.gam_exchange);\r        };\r\r        scope.balance.whileNotPassPLG = function(i,j,k) {\r            var check = true;\r            var plg = scope.balance.getTile_PLG();\r            var buasang_norm = scope.getNormBuasang();\r            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;\r            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;\r            if(plg.protein<scope.selected.group.protein_min || plg.protein>scope.selected.group.protein_max){\r                var tile_canbang = (scope.selected.group.protein_min + scope.selected.group.protein_max)/2;\r                var tile_them = 0;\r                if(plg.protein<scope.selected.group.protein_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/\r                    tile_them = scope.selected.group.protein_min-plg.protein + (tile_canbang-scope.selected.group.protein_min)/2 + i;\r                }else{\r                    tile_them = -1*(plg.protein-scope.selected.group.protein_max + (tile_canbang-scope.selected.group.protein_min)/2)-i;\r                }\r                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;\r                scope.balance.proteinChange(quantity_need);\r                check = false;\r            }\r            plg = scope.balance.getTile_PLG();\r            if(plg.fat<scope.selected.group.fat_min || plg.fat>scope.selected.group.fat_max){\r                var tile_canbang = (scope.selected.group.fat_max + scope.selected.group.fat_min)/2;\r                var tile_them = 0;\r                if(plg.fat<scope.selected.group.fat_min){ /*Đạm thấp hơn nên cần bổ sung thê*/\r                    tile_them = scope.selected.group.fat_min-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/\r                }else{\r                    tile_them = -1*(plg.fat-scope.selected.group.fat_max)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/\r                }\r                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;\r                scope.balance.fatChange(quantity_need);\r                check = false;\r            }\r            plg = scope.balance.getTile_PLG();\r            if(plg.sugar<scope.selected.group.sugar_min || plg.sugar>scope.selected.group.sugar_max){\r                var tile_them = 0;\r                if(plg.sugar<scope.selected.group.sugar_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/\r                    tile_them = scope.selected.group.sugar_min-plg.sugar+k;\r                }else{\r                    tile_them = -1*(plg.sugar-scope.selected.group.sugar_max)-k;\r                }\r                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;\r                scope.balance.sugarChange(quantity_need);\r                check = false;\r            }\r            return check;\r        };\r        scope.balance.whileNotPassQuantity = function(i,j,k) {\r            var check = true;\r            var protein = scope.balance.getProtein();\r            var plg = {};\r            plg.protein = protein/(scope.selected.group.nutritions.animal_protein + scope.selected.group.nutritions.vegetable_protein)*100;\r            var buasang_norm = scope.getNormBuasang();\r            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;\r            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;\r            if(plg.protein<smallest_rate || plg.protein>biggest_rate){\r                var tile_canbang = (biggest_rate + smallest_rate)/2;\r                var tile_them = 0;\r                if(plg.protein<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/\r                    tile_them = smallest_rate-plg.protein + (tile_canbang-smallest_rate)/2 + i;\r                }else{\r                    tile_them = -1*(plg.protein-biggest_rate + (tile_canbang-smallest_rate)/2)-i;\r                }\r                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;\r                scope.balance.proteinChange(quantity_need);\r                check = false;\r            }\r            var fat = scope.balance.getFat();\r            plg.fat = fat/( scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat )*100;\r            if(plg.fat<smallest_rate || plg.fat>biggest_rate){\r                var tile_canbang = (biggest_rate + smallest_rate)/2;\r                var tile_them = 0;\r                if(plg.fat<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thê*/\r                    tile_them = smallest_rate-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/\r                }else{\r                    tile_them = -1*(plg.fat-biggest_rate)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/\r                }\r                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;\r                scope.balance.fatChange(quantity_need);\r                check = false;\r            }\r\r            var sugar = scope.balance.getSugar();\r            plg.sugar = sugar/scope.selected.group.nutritions.sugar*100;\r            if(plg.sugar<smallest_rate || plg.sugar>biggest_rate){\r                var tile_them = 0;\r                if(plg.sugar<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/\r                    tile_them = smallest_rate-plg.sugar+k;\r                }else{\r                    tile_them = -1*(plg.sugar-biggest_rate)-k;\r                }\r                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;\r                scope.balance.sugarChange(quantity_need);\r                check = false;\r            }\r            return check;\r        };\r        scope.balance.sugarChange = function(soluong){\r            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */\r            var names = ['Gạo','Đường','Kẹo','Bánh khảo chay','Bánh','Bột mì tinh (bột năng)','Bột dong lọc','Bột khoai','Trân châu sắn','Bột bán','Si rô','Mạch nha','Miến (bún tàu), Hủ tiếu khô','Mật ong','Mứt cam'];\r            var food_ids = ['1179','1178','588','612'];\r            var foods = [];\r            var gam_total = 0;\r            angular.forEach(scope.balance.data, function($foods,warehouse_id){\r                if(scope.selected.balance_warehouse[warehouse_id]){\r                    angular.forEach($foods, function(food,food_id){\r                        if(!food.selected){\r                            if((indexOf_array(food.name,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.sugar>=15*/ \r                             || food.nutritions.sugar*4>food.nutritions.fat*9 && food.nutritions.sugar>food.nutritions.protein) {\r                                foods.push(food);\r                                if(food.luong1tre_balance+'' == 'Infinity'){\r                                    food.luong1tre_balance = food.luong1tre\r                                }\r                                gam_total += food.luong1tre_balance;\r                            }\r                        }\r                    })\r                }\r            });\r            /*Sắp xếp giảm dần theo hàm lượng đạm*/\r            /*Sắp xếp giảm dần theo calo*/\r            for(var i=0;i<foods.length-1; i++){\r                for(var j=i+1;j<foods.length;j++){\r                    if(foods[i].nutritions.sugar<foods[j].nutritions.sugar){\r                        var tmp = foods[i];\r                        foods[i] = foods[j];\r                        foods[j] = tmp;\r                    }\r                }\r            }\r            var soluong_used = 0;\r            angular.forEach(foods, function(food,id){\r                var tile = food.luong1tre_balance/gam_total;\r                var need = tile*soluong;\r                var gam_need = Math.ceil(100*need/food.nutritions.sugar);\r                if(food.luong1tre_balance+gam_need>=0.5){\r                    soluong_used += Math.ceil(food.nutritions.sugar/100*gam_need);\r                    food.luong1tre_balance += gam_need;\r                    if(soluong<0){\r                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);\r                    }else{\r                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);\r                    }\r\r                    var luong1tretheodvt = food.luong1tre/food.gam_exchange;\r                    var thucmuatheodvt = luong1tretheodvt;\r                    if(food.extrude_factor){\r                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));\r                    }\r                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);\r                }\r            });\r        };\r        scope.balance.fatChange = function(soluong){\r            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */\r            var names = ['Dầu','Mỡ','Tủy xương bò','Bơ','mayonnaise'];\r            var food_ids = ['907','908','909','910','911','912','913','914','915','1288','917','916'];\r            var foods = [];\r            var gam_total = 0;\r            angular.forEach(scope.balance.data, function($foods,warehouse_id){\r                if(scope.selected.balance_warehouse[warehouse_id]){\r                    angular.forEach($foods, function(food,food_id){\r                        if(!food.selected){\r                            if((indexOf_array(food.food_id,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.fat >= 5 || food.nutritions.fat>=40*/\r                                || food.nutritions.sugar*4<food.nutritions.fat*9 && food.nutritions.fat*9>food.nutritions.protein*4){\r                                foods.push(food);\r                                if(food.luong1tre_balance+'' == 'Infinity'){\r                                    food.luong1tre_balance = food.luong1tre\r                                }\r                                gam_total += food.luong1tre_balance;\r                            }\r                        }\r                    })\r                }\r            });\r            /*Sắp xếp giảm dần theo hàm lượng đạm*/\r            /*Sắp xếp giảm dần theo calo*/\r            for(var i=0;i<foods.length-1; i++){\r                for(var j=i+1;j<foods.length;j++){\r                    if(foods[i].nutritions.fat<foods[j].nutritions.fat){\r                        var tmp = foods[i];\r                        foods[i] = foods[j];\r                        foods[j] = tmp;\r                    }\r                }\r            }\r            var soluong_used = 0;\r            angular.forEach(foods, function(food,id){\r                var tile = food.luong1tre_balance/gam_total;\r                var need = tile*soluong;\r                var gam_need = Math.ceil(100*need/food.nutritions.fat);\r\r                if(food.luong1tre_balance+gam_need>=0.5){\r                    soluong_used += Math.ceil(food.nutritions.fat/100*gam_need);\r                    food.luong1tre_balance += gam_need;\r                    if(soluong<0){\r                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);\r                    }else{\r                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);\r                    }\r\r                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);\r                    var thucmuatheodvt = luong1tretheodvt;\r                    if(food.extrude_factor){\r                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));\r                    }\r                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);\r                }\r            });\r        };\r        scope.balance.proteinChange = function(soluong){\r            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */\r            var names = ['Tôm','cá','Mắm','bò','trâu','mực','thịt','trứng'];\r            var foods = [];\r            var gam_total = 0;\r            angular.forEach(scope.balance.data, function($foods,warehouse_id){\r                if(scope.selected.balance_warehouse[warehouse_id]){\r                    angular.forEach($foods, function(food,food_id){\r                        if(!food.selected){\r                            if(indexOf_array(food.name,names) /*&& food.nutritions.protein>=5 || food.nutritions.protein >= 20*/ \r                                || food.nutritions.protein*4>food.nutritions.fat*9 && food.nutritions.sugar<food.nutritions.protein){\r                                foods.push(food);\r                                if(food.luong1tre_balance+'' == 'Infinity'){\r                                    food.luong1tre_balance = food.luong1tre\r                                }\r                                gam_total += food.luong1tre_balance;\r                            }\r                        }\r                    });\r                }\r            });\r            /*Sắp xếp giảm dần theo hàm lượng đạm*/\r            /*Sắp xếp giảm dần theo calo*/\r            for(var i=0;i<foods.length-1; i++){\r                for(var j=i+1;j<foods.length;j++){\r                    if(foods[i].nutritions.protein<foods[j].nutritions.protein){\r                        var tmp = foods[i];\r                        foods[i] = foods[j];\r                        foods[j] = tmp;\r                    }\r                }\r            }\r            var soluong_used = 0;\r            angular.forEach(foods, function(food,id){\r                var tile = food.luong1tre_balance/gam_total;\r                var need = tile*soluong;\r                var gam_need = Math.ceil(100*need/food.nutritions.protein);\r\r                if(food.luong1tre_balance+gam_need>=0.5){\r                    soluong_used += Math.ceil(food.nutritions.protein/100*gam_need);\r                    food.luong1tre_balance += gam_need;\r                    if(soluong<0){\r                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);\r                    }else{\r                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);\r                    }\r\r                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);\r                    var thucmuatheodvt = luong1tretheodvt;\r                    if(food.extrude_factor){\r                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));\r                    }\r                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);\r                }\r            });\r        };\r        scope.balance.sumCalo = function(foods){\r            var rs = 0;\r            angular.forEach(foods, function(food,food_id){\r                if(!scope.apdungcongthuc){\r                    rs += $['*'](scope.co_cau_chuan.protein, food.luong1tre_balance)*(food.nutritions.protein/100)\r                        + $['*'](scope.co_cau_chuan.fat, food.luong1tre_balance)*(food.nutritions.fat/100)\r                        + $['*'](scope.co_cau_chuan.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)\r                }else{\r                    rs += $['*'](scope.co_cau.protein, food.luong1tre_balance)*(food.nutritions.protein/100)\r                        + $['*'](scope.co_cau.fat, food.luong1tre_balance)*(food.nutritions.fat/100)\r                        + $['*'](scope.co_cau.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)\r                }\r            });\r            return rs;\r        };\r        scope.balance.getTotal_nutritions = function(){\r            var rs = {\r                dam: 0, beo:0, duong: 0, calo:0\r            };\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                if(scope.dinhduong_calc[warehouse_id]){\r                    angular.forEach(kho, function(food,food_id){\r                        rs.dam = $['+'](rs.dam,$['*'](food.luong1tre_balance,(food.nutritions.protein/100)));\r                        rs.beo = $['+'](rs.beo,$['*'](food.luong1tre_balance,(food.nutritions.fat/100)));\r                        rs.duong = $['+'](rs.duong,$['*'](food.luong1tre_balance,(food.nutritions.sugar/100)));\r                    })\r                }\r            });\r            if(!scope.apdungcongthuc){\r                rs.calo = scope.co_cau_chuan.protein * rs.dam + scope.co_cau_chuan.fat * rs.beo + scope.co_cau_chuan.sugar * rs.duong;\r            }else{\r                rs.calo = scope.co_cau.protein * rs.dam + scope.co_cau.fat * rs.beo + scope.co_cau.sugar * rs.duong;\r            }\r            return rs;\r        };\r        scope.balance.getCaloAll = function(){\r            var rs = 0;\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                angular.forEach(kho, function(food,food_id){\r                    rs += scope.getCalo(food, 'luong1tre_balance');\r                })\r            });\r            return rs;\r        };\r        /*  Tính lượng khẩu phần (calo) đạt hay chưa */\r        scope.balance.caloRate = function(){\r            var value = 0;\r            var calo = round(scope.sumCaloMeals(scope.balance.meals), 0);\r            var rate = scope.getNormSelected();\r            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {\r                value = 1;\r            } else {\r                if (calo < rate.smallest_rate) {\r                    value = 0;\r                } else {\r                    value = 2;\r                }\r            }\r\r            return value;\r        };\r        scope.balance.caloRateBind = function(){\r            var value = scope.balance.caloRate();\r            if(value == 1){\r                text = 'Đạt';\r                class_color = '';\r            }else if(value == 0){\r                text = 'Chưa đạt';\r                class_color = 'color-red';\r            }else{\r                text = 'Vượt quá định mức';\r                class_color = 'btn-color-blue';\r            }\r            return {text: text, 'class': class_color, value: value};\r        };\r        /* Tính tổng đạm*/\r        scope.balance.getProtein = function(){\r            var animal_protein = 0;\r            var vegetable_protein = 0;\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                angular.forEach(kho, function(row,index){\r                    if(warehouse_id == 2){\r                        if(row.is_meat == 1) {   /*Nếu là động vật*/\r                            animal_protein += row.nutritions.protein*row.luong1tre_balance/100;\r                        }else{\r                            vegetable_protein += row.nutritions.protein*row.luong1tre_balance/100;\r                        }\r                    }\r                });\r            });\r            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';\r            var value = ( animal_protein + vegetable_protein ) ;\r            return value;\r        };\r        /* Tính toán tỉ lệ đạt béo*/\r        scope.balance.getFat = function(){\r            var animal_fat = 0;\r            var vegetable_fat = 0;\r            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                angular.forEach(kho, function(row,index){\r                    if(warehouse_id == 2){\r                        if(row.is_meat == 1) {   /*Nếu là động vật*/\r                            animal_fat += row.nutritions.fat*row.luong1tre_balance/100;\r                        }else{\r                            vegetable_fat += row.nutritions.fat*row.luong1tre_balance/100;\r                        }\r                    }\r                });\r            });\r            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';\r            var value = ( animal_fat + vegetable_fat ) ;\r            return value;\r        };\r        /* Tính tỉ lệ từng loại thành phần dinh dưỡng*/\r        scope.balance.getSugar = function(){\r            var value = 0;\r            if(scope.selected.group.nutritions) {\r                if(!scope.selected.group || !scope.selected.group.nutritions.sugar) return value;\r                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){\r                    angular.forEach(kho, function(row,index){\r                        if(warehouse_id == 2){\r                            value += row.nutritions.sugar*row.luong1tre_balance/100;\r                        }\r                    });\r                });\r            }\r            return value;\r        };\r        \r        /*Tính tỉ lệ PLG*/\r        scope.balance.getTile_PLG = function(name){\r            var rs = {\r                protein: 0,\r                fat: 0,\r                sugar: 0\r            };\r            var tongcalo = 0;\r            angular.forEach(scope.balance.data, function(foods, warehouse_id){\r                if ( scope.row.meal_selection[warehouse_id].selected ) {\r                    angular.forEach(foods, function(food,food_id){\r                        if(!scope.apdungcongthuc){\r                            rs.protein += scope.co_cau_chuan.protein * food.luong1tre_balance*food.nutritions.protein/100;\r                            rs.fat += scope.co_cau_chuan.fat * food.luong1tre_balance*food.nutritions.fat/100;\r                            rs.sugar += scope.co_cau_chuan.sugar * food.luong1tre_balance*food.nutritions.sugar/100;\r                        }else{\r                            rs.protein += scope.co_cau.protein * food.luong1tre_balance*food.nutritions.protein/100;\r                            rs.fat += scope.co_cau.fat * food.luong1tre_balance*food.nutritions.fat/100;\r                            rs.sugar += scope.co_cau.sugar * food.luong1tre_balance*food.nutritions.sugar/100;\r                        }\r                    })\r                }\r            });\r            tongcalo = rs.protein + rs.fat + rs.sugar;\r            rs.protein = scope.round(rs.protein/tongcalo*100,1);\r            rs.fat = scope.round(rs.fat/tongcalo*100,1);\r            rs.sugar = scope.round(rs.sugar/tongcalo*100,1);\r            return rs;\r        };\r        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */\r        scope.balance.plgRate = function(){\r            var rs = {};\r            var tile = scope.getTile_PLG(scope.balance.meals);\r            var tile_dat = [];\r            var tile_chuadat = [];\r            var tile_vuotqua = [];\r            if(tile.protein>=scope.selected.group.protein_min && tile.protein<=scope.selected.group.protein_max){\r                tile_dat.push({define:'protein',name: 'Chất đạm'});\r            }else if(tile.protein<scope.selected.group.protein_min) {\r                tile_chuadat.push({define:'protein',name: 'Chất đạm'})\r            }else{\r                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})\r            }\r            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){\r                tile_dat.push({define:'fat',name: 'Chất béo'});\r            }else if(tile.fat<scope.selected.group.fat_min) {\r                tile_chuadat.push({define:'fat',name: 'Chất béo'})\r            }else{\r                tile_vuotqua.push({define:'fat',name: 'Chất béo'})\r            }\r            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){\r                tile_dat.push({define:'sugar',name: 'Chất bột'});\r            }else if(tile.sugar<scope.selected.group.suga_min) {\r                tile_chuadat.push({define:'sugar',name: 'Chất bột'})\r            }else{\r                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})\r            }\r            rs = {\r                dat: tile_dat,\r                chuadat: tile_chuadat,\r                vuotqua: tile_vuotqua\r            };\r            return rs;\r        };\r\r        scope.balance.plgRateBind = function(){\r            var thanhphan = scope.balance.plgRate();\r            var text = '';\r            if(count(thanhphan.dat) == 3){\r                text = 'Cân đối';\r                class_color = '';\r            }else{\r                text = 'Chưa cân đối';\r                class_color = 'color-red';\r            }\r            return {text: text, 'class': class_color, thanhphan: thanhphan};\r        };\r\r        scope.balance.onChange_thucmuatheodvtApply = function(food){\r            food.gam_exchange || (food.gam_exchange = 1000);\r            var thucmuatheodvt = Number(food.thucmuatheodvt);\r            food.thucmua1tretheodvt = scope.round($['/'](thucmuatheodvt, scope.row.sotre),4);\r            food.thucmua1nhom = thucmuatheodvt * food.gam_exchange / 1000;\r            food.thucan1nhom = food.thucmua1nhom;\r            if(food.extrude_factor){\r                food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);\r            }\r            food.luong1tre = round($['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000));\r            food.thucan1nhom = round(food.thucan1nhom);\r            food.thucmua1nhom = round(food.thucmua1nhom);\r            food.luong1tre_root = food.luong1tre;\r            food.thanhtien1nhom = $['*'](thucmuatheodvt, food.price);\r            scope.divide_luong1tre(food);\r        };\r        scope.balance.foodSelectAll = function() {\r            scope.balance.selectallfood = !scope.balance.selectallfood;\r            angular.forEach(scope.balance.data, function(kho,warehouse_id){\r                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);\r                angular.forEach(foods, function(food,food_id){\r                    food.selected = scope.balance.selectallfood;\r                })\r            });\r        };\r        scope.balance.luong1treOnChange = function(food){\r            food.luong1tre_balance = Number(food.luong1tre_balance);\r            var luong1tretheodvt = $['/'](food.luong1tre_balance,food.gam_exchange);\r            var thucmuatheodvt = luong1tretheodvt;\r            if(food.extrude_factor){\r                thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));\r            }\r            food.thanhtien_balance = $['*'](thucmuatheodvt,food.price);\r        };\r        scope.balance.thanhtienOnChange = function(food){\r            food.tiendicho1tre_balance = Number(food.tiendicho1tre_balance);\r            var thucmuatheodvt = $['/'](food.tiendicho1tre_balance,food.price);\r            var luong1tretheodvt = thucmuatheodvt;\r            if(food.extrude_factor){\r                luong1tretheodvt = thucmuatheodvt/(1+1/(100/food.extrude_factor - 1));\r            }\r            food.luong1tre_balance = $['*'](luong1tretheodvt, food.gam_exchange);\r        };\r        /*  Trả ra số phần quy định đã chọn */\r        scope.balance.getDinhmucSophanSelected = function() {\r            var rs = [];\r            angular.forEach(scope.selected.group.dinhmuc, function(dinhmuc,key){\r                if(dinhmuc.selected){\r                    rs.push(key);\r                }\r            });\r            return rs;\r        };\r        scope.balance.warehouseSelectedOnClick = function(meal) {\r            scope.warehouseSelectedOnClick(meal);\r            scope.balance.init();\r        };\r        scope.balance.getSelectBySophan = function(foods){\r            var rs = {};\r            var sophan = scope.balance.getDinhmucSophanSelected();\r            if(sophan.length==0){\r                return foods;\r            }\r            angular.forEach(foods, function(food,index){\r                var check_sophan = false;\r                angular.forEach(sophan, function(key,index){\r                    if(food[key]){\r                        rs[food.food_id] = food;\r                    }\r                })                \r            });\r            return rs;\r        };\r        /*  Trả ra mảng tất cả thực phẩm được chọn theo kho  */\r        scope.balance.getFoodOfWarehouse = function(warehouse_id){\r            var foods = {};\r            if(scope.selected.balance_warehouse[warehouse_id]){\r                foods = scope.balance.getSelectBySophan(scope.balance.data[warehouse_id]);\r            }\r            return foods;\r        };\r\r        scope.balance.onCheckedAllSophan = function(){\r            scope.balance.dinhmucSelectall = !scope.balance.dinhmucSelectall;\r            angular.forEach(scope.selected.group.dinhmuc, function(item,index){\r                item.selected = scope.balance.dinhmucSelectall\r            });\r        };\r        scope.balance.isCheckedAllSophan = function(){\r            var rs = [];\r            angular.forEach(scope.selected.group.dinhmuc, function(item,index){\r                if(item.selected){\r                    rs.push(true);\r                }\r            });\r            if(rs.length == count(scope.selected.group.dinhmuc)){\r                return true;\r            }else{\r                return false;\r            }\r        };\r        scope.balance.showPartPLGInfo = function(){\r            $.menu_planning.showPartPLGInfo_balance();\r        }\r    }\r};\r", "    \r\n$(document).ready(function(){ \r\n    $(document).mouseup(function (e)\r\n    {\r\n        var container = $(\".tab-main-index\");\r\n        if($(window).width()<500){\r\n            if (!container.is(e.target) && container.has(e.target).length === 0)\r\n            {\r\n                container.hide();\r\n            }\r\n        }\r\n        \r\n    });\r\n    hv();\r\n    $(window).resize(function() {\r\n        hv();\r\n    })\r\n    $(document).mouseup(function (e){\r\n        var container = $(\".item-setup-mc-new\");\r\n     \r\n        if (!container.is(e.target) && container.has(e.target).length === 0)\r\n        {\r\n            container.hide();\r\n        }\r\n    });\r\n\r\n    var url = '{{ url() }}/single/{{$config->project}}';\r\n    if(window.location.href != url){\r\n        $(\".content-qt\").hide(\"slow\");\r\n    }\r\n    if($(window).height() < 650)\r\n    {\r\n        $('.content-qt').css({\"height\":$(window).height(),\"overflow\":\"auto\"});\r\n    }\r\n    \r\n    $('#spinner-container').addClass('spinner'+rand(1,4));\r\n    \r\n    $(document).mouseup(function (e){\r\n        var container = $(\".quy-trinh\");\r\n        if (!container.is(e.target) && container.has(e.target).length === 0)\r\n        {\r\n            $(\".content-qt\").hide(\"slow\");\r\n        }\r\n    });\r\n    $('.btn-quy-trinh').click(function(){\r\n        if($(window).height() < 650){\r\n            $('.content-qt').css({\"height\":$(window).height(),\"overflow\":\"auto\"});\r\n        }\r\n        $(\".content-qt\").toggle(\"slow\", function() {});\r\n    })\r\n        \r\n    $( \"a\" ).click(function() {\r\n        $('.logo-dd-new img').css({\"display\":\"block\",\"width\":\"100%\"});\r\n        $('.main-content').css({\"z-index\":\"3\"});\r\n    });\r\n    if(window.location.href != \"http://localhost/pms/single/{{$config->project}}\"){\r\n        $('.logo-dd-new img').css({\"display\":\"block\",\"width\":\"100%\"});\r\n        $('.main-content').css({\"z-index\":\"3\"});\r\n    };\r\n    $('.bottom-hidden').click(function(){       \r\n        $(\".bottom-content\").animate({\r\n            top:($(window).height()-60)+\"px\",\r\n        },700);\r\n        setTimeout(function(){\r\n            $(\".bottom-content\").toggle();\r\n            $('.footer').css({\"display\":\"none\"});\r\n        },800)\r\n    });\r\n\r\n    $(window).scroll(function(e){\r\n        scroll1();\r\n    });\r\n    \r\n    var hienquytrinh = getCookie('hienquytrinh');\r\n    if(!hienquytrinh){\r\n        hienquytrinh = 0;\r\n    }\r\n    \r\n    if(hienquytrinh == 0){\r\n        $(\".content-qt\").hide(\"slow\");\r\n\r\n    }else{\r\n        $(\"#tudonghien12\").attr(\"checked\", true);\r\n    }\r\n\r\n    \r\n    var visitor = getCookie('visitor11234');\r\n\r\n    if(!visitor){\r\n        visitor = 0;\r\n    }\r\n    visitor = Number(visitor);\r\n    visitor++;\r\n    \r\n    setCookie('visitor11234',visitor,18000);\r\n    if(visitor<4 && false){\r\n        $('#dialog-tphcm').dialog({\r\n            title: '',\r\n            width: 650,\r\n            height: 500,\r\n            closed: false,\r\n            cache: false,\r\n            modal: true,\r\n            onOpen : function (ele) {\r\n                $(ele).show();\r\n                $('.window-shadow').css(\"display\",\"none\");\r\n                \r\n                $('.window').css({\"background\":\"rgba(0,0,0,0)\",\"border\":\"0px\",\"box-shadow\":\"none\"});\r\n                var btn_close = $('<div style=\"top: 62px;right: 44px;position: absolute;font-size:17px; opacity: 1;\" class=\"close\"><img src=\"{{ asset(\"/images/close.png\") }}\"></div>');\r\n\r\n                $('#dialog').css({\"background\":\"rgba(0,0,0,0)\",\"border\":\"0px\"}).append(btn_close);\r\n                btn_close.click(function(){\r\n                    $('#dialog').dialog(\"close\");\r\n                })\r\n                $('.window-mask').click(function(){\r\n                    $('#dialog').dialog(\"close\");\r\n                })\r\n            }\r\n        })\r\n    }\r\n});\r\nfunction bottomShow(){\r\n    $(\".bottom-content\").toggle();\r\n    $(\".bottom-content\").animate({\r\n        top:\"0px\"\r\n    },700);\r\n    $('.footer').css({\"display\":\"block\"});\r\n}\r\nfunction hideqt(){\r\n    $(\".content-qt\").hide(\"slow\");\r\n}\r\nfunction scroll1() {\r\n    var sticky = $('.menu-bc'),\r\n        scroll = $(window).scrollTop(),\r\n        Wheight = $(window).height();\r\n    if (scroll>Wheight-80) \r\n        sticky.addClass('fixed');\r\n    else sticky.removeClass('fixed');\r\n}\r\nfunction tudonghien(el){\r\n    if($(\"#tudonghien12:checked\").length){\r\n        setCookie('hienquytrinh',1,18000);\r\n    }else{\r\n        setCookie('hienquytrinh',0,18000);\r\n    }\r\n}\r\nfunction tabMain() {\r\n    if($(window).width()<500){\r\n        $('.tab-main-index').toggle();\r\n    }   \r\n}\r\nfunction hv(){\r\n    setTimeout(function(){\r\n        var navmain =  $(\".nav-main-content\").width();\r\n        var inmain = $(\".in-main\").width();\r\n        var w2out = navmain + 20 + (inmain - navmain)/2;\r\n        if($(window).width() < 420){\r\n            w2out = 5;\r\n            $('.item-setup-mc-new').css(\"top\",\"40px\");\r\n        }\r\n        if($(window).width() < 769 && $(window).width() > 420){\r\n            w2out = navmain + 40;\r\n        }\r\n        $('.item-setup-mc-new').css(\"left\", w2out+\"px\");\r\n    }, 250);\r\n}\r\nfunction mcNew() {\r\n    $('.item-setup-mc-new').toggle();\r\n}", "angular_app.controller('payoutController', ['$scope', function ($scope) {\r\n    $scope.control = 'payout';\r\n    $scope.templates = {};\r\n    $scope.fee = {};\r\n    $scope.init = function () {\r\n        var date = new Date();\r\n        var month = date.getMonth() + 1;\r\n        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {\r\n            $scope.$apply(function () {\r\n                var data = resp.data;\r\n                $scope.months = data.months;\r\n                $scope.month = month.toString();\r\n                $scope.year = data.months[0].year;\r\n\r\n                $scope.feeConfigs = data.feeConfigs;\r\n                $scope.fee.id = '';\r\n                $scope.rows = [];\r\n                $scope.selected = {};\r\n            })\r\n        });\r\n    };\r\n    $scope.getRows = function () {\r\n        process($CFG.project + '/' + $scope.control + '/list?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {\r\n            $scope.$apply(function () {\r\n                var data = resp.data.data;\r\n                $scope.rows = data.rows;\r\n                $scope.fee.beginSurplus = data.begin_surplus;\r\n                $scope.fee.count = 0;\r\n                $scope.fee.check = '';\r\n                /*Set lại là 0 khi gọi lại để đếm số thứ tự*/\r\n            })\r\n        });\r\n    };\r\n\r\n    $scope.monthChange = function () {\r\n        $scope.getRows();\r\n    };\r\n\r\n    $scope.feeChange = function () {\r\n        $scope.getRows();\r\n    };\r\n\r\n    $scope.sumCollection = function () {\r\n        var rs = 0;\r\n        if ($scope.rows) {\r\n            angular.forEach($scope.rows, function (row, index) {\r\n                if (row.fee !== \"\" && row.fee > 0) {\r\n                    rs = $['+'](rs, row.fee);\r\n                }\r\n            })\r\n        }\r\n        return rs;\r\n    };\r\n    $scope.sumExpenditure = function () {\r\n        var rs = 0;\r\n        if ($scope.rows) {\r\n            angular.forEach($scope.rows, function (row, index) {\r\n                rs = $['+'](rs, row.cost_item);\r\n            })\r\n        }\r\n        return rs;\r\n    };\r\n    $scope.getEndSurplus = function () {\r\n        var rs = 0;\r\n        $scope.fee.beginSurplus || ($scope.fee.beginSurplus = 0);\r\n        rs = $['+'](rs, $scope.fee.beginSurplus);\r\n        rs = $['+'](rs, $scope.sumCollection());\r\n        rs = $['-'](rs, $scope.sumExpenditure());\r\n        return rs;\r\n    };\r\n    $scope.rowSelect = function (row) {\r\n        if (!row) {\r\n            $scope.selected.rowAdd = true;\r\n            angular.forEach($scope.rows, function (item, index) {\r\n                item.selected = false;\r\n            });\r\n        } else {\r\n            angular.forEach($scope.rows, function (item, index) {\r\n                item.selected = false;\r\n            });\r\n            $scope.selected.rowAdd = false;\r\n            row.selected = true;\r\n        }\r\n    };\r\n\r\n    $scope.rowAdd = function () {\r\n        var tmp = $scope.rows;\r\n        var row = {\r\n            day: '',\r\n            cost_item: 0,\r\n            explain: '',\r\n            fee: 0,\r\n            is_fee: false,\r\n            number_cost: \"\",\r\n            number_receipts: \"\",\r\n        };\r\n        tmp.push(row);\r\n        $scope.rows = tmp;\r\n    };\r\n\r\n    $scope.delete = function () {\r\n        var tmp = $scope.rows;\r\n        if($scope.fee.check === '')\r\n            alert(\"Vui lòng chọn một dòng!\");\r\n        else{\r\n            tmp.splice($scope.fee.check, 1);\r\n            $scope.rows = tmp;\r\n        }\r\n    };\r\n\r\n    $scope.refresh = function (){\r\n        var msg = '<div style=\"margin-left: 42px\"><p>Bạn chắc chắn?</p><p>Hành động này sẽ cập nhật lại dữ liệu và các <span style=\"color: red\">khoản chi hiện tại sẽ bị mất</span>!</p></div>';\r\n        $.messager.confirm('Cảnh báo', msg, function(r){\r\n            if (r){\r\n                process($CFG.project + '/' + $scope.control + '/delete?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {\r\n                    $scope.getRows();\r\n                });\r\n            }\r\n        });\r\n    };\r\n\r\n    $scope.save = function () {\r\n        var data = {\r\n            month: {\r\n                month: $scope.month,\r\n                year: $scope.year\r\n            },\r\n            fee_category_id: $scope.fee.id,\r\n            rows: $scope.rows,\r\n            begin_surplus: $scope.fee.beginSurplus,\r\n            end_surplus: $scope.getEndSurplus(),\r\n            fees: $scope.sumCollection(),\r\n            spent: $scope.sumExpenditure(),\r\n            async: true\r\n        };\r\n        var urls = [$CFG.project, 'fee_report', 'saveSoquytienmat'];\r\n        process(urls.join('/'), data, function () {\r\n            $scope.getRows();\r\n            alert(\"Lưu thành công!\");\r\n        });\r\n    };\r\n\r\n    $scope.onTick = function (index) {\r\n        $scope.fee.check = index;\r\n        $scope.fee.cost = $scope.rows[index].cost_item;\r\n    };\r\n\r\n    $scope.getCount = function (fee) {\r\n        if (fee == 0)\r\n            $scope.fee.count += 1;\r\n        return $scope.fee.count;\r\n    };\r\n\r\n    /*Validate*/\r\n    $scope.dayChange = function (row) {\r\n        var days = (new Date($scope.year, $scope.month, 0)).getDate();\r\n        if (row.day <= 0)\r\n            row.day = 1;\r\n        if (row.day > days)\r\n            row.day = days;\r\n    };\r\n\r\n    $scope.moneyChange = function (row) {\r\n        if (row.cost_item <= 0)\r\n            row.cost_item = 0 - row.cost_item;\r\n    };\r\n}]);", "angular_app.controller('exportController', ['$scope', function ($scope) {\r    $scope.control = 'export';\r    $scope.templates = {\r\r    };\r    $scope.init = function () {\r        var date = new Date();\r        var month = date.getMonth() + 1;\r        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {\r            $scope.$apply(function () {\r                var data = resp.data;\r                var dateExport = [];\r                data.dateExport.forEach(function (element) {\r                    dateExport.push({\r                        'date': getDate(element.date)\r                    });\r                });\r                $scope.months = data.months;\r                $scope.month = month.toString();\r                $scope.dateExport = dateExport;\r            });\r        });\r    };\r    $scope.monthChange = function () {\r        var month = $scope.month;\r        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {\r            $scope.$apply(function () {\r                var data = resp.data;\r                var dateExport = [];\r                data.dateExport.forEach(function (element) {\r                    dateExport.push({\r                        'date': getDate(element.date)\r                    });\r                });\r                $scope.dateExport = dateExport;\r            });\r        });\r    }\r}]);", "angular_app.controller('receiptController', ['$scope', function ($scope) {\r    $scope.control = 'receipt';\r    $scope.templates = {\r        form: $CFG.project + '/' + $scope.control + '/form.html',\r        addToVote: $CFG.project + '/' + $scope.control + '/add-to-vote.html',\r        print: $CFG.project + '/' + $scope.control + '/print.html',\r    };\r    $scope.init = function () {\r        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {\r            $scope.$apply(function () {\r                $scope.date = dateboxOnSelect();\r\r                $scope.suppliers = resp.data.suppliers;\r                $scope.warehouses = resp.data.warehouses;\r                $scope.mapMeasure = resp.data.mapMeasure;\r\r                $scope.suppliers.forEach(function (supplier, index) {\r                    $scope.suppliers[index].id = supplier.id;\r                });\r\r                $scope.mapWarehouses = {};\r                $scope.warehouses.forEach(function (warehouse) {\r                    $scope.mapWarehouses[warehouse.id] = warehouse.name;\r                });\r\r                $scope.mapSuppliers = {};\r                $scope.suppliers.forEach(function (supplier) {\r                    $scope.mapSuppliers[supplier.id] = supplier.name;\r                });\r\r                /*Khởi tạo table*/\r                var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + $scope.control + '/list';\r                $.dm_datagrid.init(url, $scope.control, '', $scope.columns, $scope.options);\r                $scope.table = $('#tbl_' + $scope.control);\r\r                /*Thông tin lưu khi chọn thực phẩm*/\r                $scope.foodId = '';\r                $scope.price = 0;\r                $scope.food = {};\r                $scope.measure = '';\r            })\r        });\r    };\r\r    $scope.columns = [[\r        {\r            field: 'check', formatter: function (value, row) {\r                if ((row.vote_id === null || row.vote_id === '')  && row.is_legal !== 2)\r                    return '<input id=\"check-id-' + row.id + '\" type=\"checkbox\">';\r                return '';\r            }\r        },\r        {title: 'Điểm trường', field: 'school_point', width: 40, align: 'center'},\r        {title: 'Phiếu', field: 'vote_name', sortable: true, align: 'left', width: 40},\r        {\r            title: 'Thực phẩm', field: 'name', sortable: true, align: 'left', width: 120,\r            formatter: function (value, row) {\r                if(row.is_legal === 2)\r                    return value + ' <a title=\"Tồn\" class=\"fa fa-info color-green\"></a>';\r                return value;\r            }\r        },\r        {\r            title: 'Kho', field: 'warehouse_id', align: 'left', width: 50, formatter: function (value) {\r                return $scope.mapWarehouses[value];\r            }\r        },\r        {title: 'Số lượng', field: 'quantity', width: 90, align: 'center', editor: 'textbox'},\r        {\r            title: 'Đơn giá',\r            field: 'price',\r            width: 100,\r            editor: 'numberbox',\r            align: 'center',\r            formatter: function (value) {\r                return digit_grouping(value);\r            }\r        },\r        {\r            title: 'Nhà cung cấp', field: 'supplier_id', width: 100, sortable: true, formatter: function (value) {\r                return $scope.mapSuppliers[value];\r            }\r        },\r        {\r            title: 'Đơn vị tính', field: 'measure_id', width: 50, align: 'center', formatter: function (value) {\r                return $scope.mapMeasure[value];\r            }\r        }\r    ]];\r    $scope.options = {\r        enableFilter: [\r            {\r                field: 'school_point',\r                type: 'textbox',\r                options: {\r                    panelHeight: 'auto',\r                    onChange: function (value) {\r                        if (value === '') {\r                            $scope.table.datagrid('removeFilterRule', 'storages.school_point');\r                        } else {\r                            $scope.table.datagrid('addFilterRule', {\r                                field: 'storages.school_point',\r                                op: 'equal',\r                                value: value\r                            });\r                        }\r                        $scope.table.datagrid('doFilter');\r                    }\r                }\r            },\r            {\r                field: 'vote_name',\r                type: 'textbox',\r                options: {\r                    panelHeight: 'auto',\r                    onChange: function (value) {\r                        if (value === '') {\r                            $scope.table.datagrid('removeFilterRule', 'votes.name');\r                        } else {\r                            $scope.table.datagrid('addFilterRule', {\r                                field: 'votes.name',\r                                op: 'beginwith',\r                                value: value\r                            });\r                        }\r                        $scope.table.datagrid('doFilter');\r                    }\r                }\r            },\r            {\r                field: 'name',\r                type: 'textbox',\r                options: {\r                    panelHeight: 'auto',\r                    onChange: function (value) {\r                        if (value === '') {\r                            $scope.table.datagrid('removeFilterRule', 'foods.name');\r                        } else {\r                            $scope.table.datagrid('addFilterRule', {\r                                field: 'foods.name',\r                                op: 'beginwith',\r                                value: value\r                            });\r                        }\r                        $scope.table.datagrid('doFilter');\r                    }\r                }\r            },\r            {\r                field: 'warehouse_id',\r                type: 'combobox',\r                options: {\r                    panelHeight: 'auto',\r                    data: [{value: '', text: 'Tất cả'}, {value: 1, text: 'Kho sáng'}, {value: 2, text: 'Kho trưa'}],\r                    onChange: function (value) {\r                        if (value === '') {\r                            $scope.table.datagrid('removeFilterRule', 'warehouse_id');\r                        } else {\r                            $scope.table.datagrid('addFilterRule', {\r                                field: 'warehouse_id',\r                                op: 'equal',\r                                value: value\r                            });\r                        }\r                        $scope.table.datagrid('doFilter');\r                    }\r                }\r            },\r            {\r                field: 'measure_id',\r                type: 'label'\r            },\r            {\r                field: 'supplier_id',\r                type: 'label'\r            },\r            {\r                field: 'price',\r                type: 'label'\r            },\r            {\r                field: 'quantity',\r                type: 'label'\r            },\r            {\r                field: 'check',\r                type: 'label'\r            },\r        ],\r        view: groupview,\r        groupField: 'date',\r        groupFormatter: function (id, rows) {\r            var objectDate = new Date(rows[0].date);\r            var dateFomart = objectDate.getDate() + \"/\" + (objectDate.getMonth() + 1) + \"/\" + objectDate.getFullYear();\r            return 'Ngày nhập ' + dateFomart + ': <i>' + rows.length + '</i>';\r        },\r    };\r\r    $scope.form = function () {\r        $.dm_datagrid.showAddForm({\r                module: $CFG.project + '/' + $scope.control,\r                action: 'form',\r                title: 'Quản lý nhập kho',\r                size: size.wide,\r                fullScreen: false,\r                showButton: false,\r                scope: $scope,\r                content: $scope.templates.form,\r                onshown: function () {\r                    process($CFG.project + '/' + $scope.control + '/form', {async: true}, function (resp) {\r                        $scope.$apply(function () {\r                            var data = resp.data;\r                            $scope.mapFoods = data.mapFoods;\r                            $scope.mapFoodById = {};\r                            $scope.mapFoods.forEach(function (food) {\r                                $scope.mapFoodById[food.id] = food.name;\r                            });\r                            delete $scope.mapFoods;\r\r                            $scope.vote = data.vote;\r                            $scope.dateDisabled = false;\r                            /*Ngày hiện tại*/\r                            $scope.school = data.school;\r\r                            /*add*/\r                            $scope.supplierId = '';\r                            $scope.warehouseId = '2';\r                            $scope.quantity = 0;\r                            $scope.selected = {};\r                            $scope.rows = [];\r                        })\r                    })\r                },\r                cancel: function () {\r                    $(\"#tbl_\" + $scope.control).datagrid('reload');\r                }\r            }\r        );\r    };\r\r    /* 2. Thay đổi số phiếu*/\r    $scope.formInputChange = function () {\r        var post = {\r            'vote': $scope.vote\r        };\r        process($CFG.project + '/' + $scope.control + '/formInputChange', {post: post, async: true}, function (resp) {\r            $scope.$apply(function () {\r                var data = resp.data;\r                $scope.dateDisabled = false;\r                if (data.id !== -1)\r                    $scope.dateDisabled = true;\r\r                $scope.date = getDate(data.date);\r                if (data.rows.length > 0) {\r                    data.rows.forEach(function (row, index) {\r                        data.rows[index].quantity = parseFloat(row.quantity);\r                        data.rows[index].price = parseFloat(row.price);\r                    });\r                }\r                $scope.rows = data.rows;\r            });\r        });\r    };\r\r    /* 3. Chọn thực phẩm */\r    $scope.selectFood = function (food) {\r        var url = $CFG.remote.base_url + '/doing/dinhduong/dish/getFoodDetailById';\r        var data = {async: true, id: food.id};\r        process(url, data, function (resp) {\r            if (!resp) return;\r            else {\r                $scope.$apply(function () {\r                    angular.forEach(resp, function (food) {\r                        $scope.food = food;\r                        $scope.foodId = food.food_id;\r                        $scope.measure = food.measure_name;\r                        $scope.price = food.price;\r                    });\r                });\r            }\r        }, function () {\r        }, false);\r    };\r\r    /* 4. Lưu */\r    $scope.formAdd = function () {\r        var post = {\r            'vote': $scope.vote,\r            'date': $scope.date,\r            'food_id': $scope.foodId,\r            'warehouse_id': $scope.warehouseId,\r            'quantity': $scope.quantity,\r            'price': $scope.price,\r            'supplier': $scope.supplierId,\r            'food': $scope.food\r        };\r        process($CFG.project + '/' + $scope.control + '/formAdd', {post: post, async: true}, function (resp) {\r            if (resp.result === 'success') {\r                $scope.$apply(function () {\r                    post.id = parseInt(resp.data.id);\r                    post.warehouse_id = parseInt(post.warehouse_id);\r                    post.food_name = post.food.name;\r                    post.supplier_id = post.supplier;\r                    post.measure_id = post.food.measure_id;\r                    post.school_point = $scope.school;\r                    post.exported = false;\r                    $scope.rows.push(post);\r                });\r            }\r        });\r    };\r\r    $scope.formEdit = function (row) {\r        process($CFG.project + '/' + $scope.control + '/formEdit', {post: row}, function (resp) {\r            if (resp.result === 'success') {\r                alert('Sửa thành công');\r            }\r        });\r    };\r\r    $scope.del = function (index, row) {\r        if (!row) {\r            alert('Không tìm thấy dữ liệu.');\r            return;\r        }\r        var msg = ['<div style = \"font-size: 14px\">Chắc chắn xóa ?</div>', ' - Dữ liệu sau khi xóa sẽ không thể khôi phục.'];\r        $.messager.confirm('Xác nhận', msg.join('<br/>'), function (r) {\r            if (r) {\r                process($CFG.project + '/' + $scope.control + '/del', {ids: row.id}, function () {\r                    $scope.$apply(function () {\r                        $scope.rows.splice(index, 1);\r                    });\r                })\r            }\r        });\r    };\r    $scope.showDetail = function (storage_id) {\r        $.dm_datagrid.showAddForm(\r            {\r                module: $CFG.project + '/' + 'storage',\r                action: 'detail',\r                title: 'Chi tiết xuất kho',\r                content: function (element) {\r                    loadForm($CFG.project + '/' + 'storage', 'detail', {storage_id: storage_id}, function (resp) {\r                        $scope.$apply(function ($scope) {\r                            var form = '<div >'+resp+'</div>';\r                            $(element).html($scope.compile(form,$scope));\r                            if(typeof callback === 'function'){\r                                callback($scope);\r                            }\r                        });\r                    })\r                }\r            },\r            function (resp) {\r                if (typeof callback === 'function') {\r                    callback(resp);\r                } else {\r                    $scope.formInputChange();\r                }\r            }\r        );\r    };\r\r    $scope.selectDate = function(date){\r        $scope.date = date;\r    };\r    $scope.addToVote = function () {\r        var rows = $scope.table.datagrid('getRows');\r\r        const ADD = 'add';\r        const MANY_DATE = 'many-date';\r        const NULL = 'null';\r        const INIT = 'init';\r\r        var check = ADD;\r        var date = INIT;\r        for (var i in rows) {\r            if(rows.length < 1){\r                check = NULL;\r                break;\r            }\r            if ($('input#check-id-' + rows[i].id).prop('checked')) {\r                if(date === INIT)\r                    date = rows[i].date;\r                else {\r                    if(date !== rows[i].date){\r                        check = MANY_DATE;\r                        break;\r                    }\r                }\r            }\r        }\r        switch (check) {\r            case ADD:\r                $.dm_datagrid.showAddForm({\r                    module: $CFG.project + '/' + $scope.control,\r                    action: 'addToVote',\r                    title: 'Chọn phiếu',\r                    size: size.small,\r                    fullScreen: false,\r                    showButton: false,\r                    scope: $scope,\r                    content: $scope.templates.addToVote,\r                    onshown: function () {\r                        process($CFG.project + '/' + $scope.control + '/addToVote', {async: true, data: {date: date}}, function (resp) {\r                            $scope.$apply(function () {\r                                var data = resp.data;\r                                $scope.votes = data.votes;\r                            })\r                        })\r                    },\r                    cancel: function () {\r                        $(\"#tbl_\" + $scope.control).datagrid('reload');\r                    }\r                });\r                break;\r            case MANY_DATE:\r                alert(\"Chỉ được chọn các thực phẩm cùng ngày để thêm vào phiếu\");\r                break;\r            default:\r                alert(\"Hãy chọn một dòng\");\r                break;\r        }\r    };\r\r    $scope.saveToVote = function (voteId) {\r        if(voteId === ''){\r            alert(\"Vui lòng chọn một phiếu!\");\r        }\r        else {\r            var ids = [];\r            var rows = $scope.table.datagrid('getRows');\r            for (var i in rows) {\r                if ($('input#check-id-' + rows[i].id).prop('checked')) {\r                    ids.push(rows[i].id);\r                }\r            }\r            var post = {\r                voteId : voteId,\r                ids: ids,\r            };\r            process($CFG.project + '/' + $scope.control + '/saveToVote', {async: true, post: post}, function (resp) {\r                if(resp.result === 'success')\r                    alert('Thêm thành công!');\r            })\r        }\r    };\r\r    $scope.print = function () {\r        $scope.begin = dateboxOnSelect();\r        $scope.end = dateboxOnSelect();\r        $.dm_datagrid.showAddForm({\r            module: $CFG.project + '/' + $scope.control,\r            action: 'in',\r            title: 'In phiếu nhập kho',\r            size: size.normal,\r            fullScreen: false,\r            showButton: false,\r            scope: $scope,\r            content: $scope.templates.print,\r        });\r    }\r}]);", "window.angular_app.controller('menu_adjustController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){\r\n    $scope.project = 'dinhduong';\r\n    $scope.control = 'menu_adjust';\r\n    $scope.form_id = 'menu_adjust-total';\r\n    $scope.dialog = undefined;\r\n    $scope.controller = {\r\n        templates: {\r\n            addForm: $scope.project + '/' + $scope.control + '/add.html',\r\n            marketbillForm: $scope.project + '/' + $scope.control + '/marketbillForm.html',\r\n            editServicePrice: $scope.project + '/' + $scope.control + '/edit_service_price.html',\r\n            duplicateToMenuPlanning: $scope.project + '/' + $scope.control + '/duplicate_to_menu_planning.html',\r\n            quantityInfo: $scope.project + '/' + $scope.control + '/quantity_info.html',\r\n            nutritionInfo: $scope.project + '/' + $scope.control + '/nutrition_info.html',\r\n            changeDish: $scope.project + '/' + $scope.control + '/change_dish.html',\r\n            shareWithAdmin: $scope.project + '/' + $scope.control + '/share_with_admin.html',\r\n            addMenuPlanning: $scope.project + '/' + $scope.control + '/add_menu_planning.html',\r\n            meal: $scope.project + '/' + $scope.control + '/meal.html',\r\n            mealOfDish: $scope.project + '/' + $scope.control + '/meal_of_dish.html',\r\n            mealOfDishUnlock: $scope.project + '/' + $scope.control + '/meal_of_dish_unlock.html',\r\n            addFood: $scope.project + '/' + $scope.control + '/add_food.html',\r\n            addDish: $scope.project + '/' + $scope.control + '/add_dish.html',\r\n            separateFood: $scope.project + '/' + $scope.control + '/separate_food.html',\r\n            balance: $scope.project + '/' + $scope.control + '/balance.html',\r\n            balanceMoney: $scope.project + '/' + $scope.control + '/balance_money.html',\r\n            edit_number_children: $scope.project + '/' + $scope.control + '/edit_number_children.html',\r\n            edit_tien_bo_tro: $scope.project + '/' + $scope.control + '/edit_tien_bo_tro.html',\r\n            formCopy: $scope.project + '/' + $scope.control + '/copy.html',\r\n            printForm: $scope.project + '/' + $scope.control + '/print-form.html',\r\n        }\r\n    };\r\n    $scope.processData = processData;\r\n    $scope.getTongluong = getTongluong;\r\n    $scope.onChange_soluong = onChange_soluong;\r\n    $scope.getTongTien1Nhom = getTongTien1Nhom;\r\n    $scope.getTongTien = getTongTien;\r\n    $scope.onSelectDish = onSelectDish;\r\n    $scope.showAddDish = showAddDish;\r\n    $scope.addDishApply = addDishApply;\r\n    $scope.showAddFood = showAddFood;\r\n    $scope.onSelectAddFood = onSelectAddFood;\r\n    $scope.appendFoodToMeal = appendFoodToMeal;\r\n    $scope.showFormTotal = showFormTotal;\r\n    $scope.closeFormTotal = closeFormTotal;\r\n    $scope.showChangeDish = showChangeDish;\r\n    $scope.changeDishApply = changeDishApply;\r\n    $scope.foodSelected = foodSelected;\r\n    $scope.addFoodToTempDish = addFoodToTempDish;\r\n    $scope.formEditSotreOfPoints = formEditSotreOfPoints;\r\n    $scope.formEditTienbotroOfPoints = formEditTienbotroOfPoints;\r\n    $scope.balanceShow = balanceShow;\r\n    $scope.sumPayForGroup = sumPayForGroup;\r\n    $scope.sumPayForAll = sumPayForAll;\r\n    $scope.modifiedSotres = modifiedSotres;\r\n    $scope.applySotres = applySotres;\r\n    $scope.marketbillPrintForm = marketbillPrintForm;\r\n    $scope.warehouseSelectedOnClick = warehouseSelectedOnClick;\r\n    $scope.fn_copyForm = fn_copyForm;   /* Mở form sao chép cân đối khẩu phần trong phòng hoặc các điểm trường khác  */\r\n    $scope.fn_selectAdjustPoint = fn_selectAdjustPoint;   /* Tải thực đơn của điểm trường đã chọn */\r\n    $scope.initGrid = initGrid;   /* Khởi tạo grid  */\r\n    $scope.onChange_food_name = onChange_food_name;   /* Sửa tên thực phẩm  */\r\n    $scope.divide_pointThucmuatheodvt = divide_pointThucmuatheodvt;   /* Chia thực mua về từng điểm trường theo tỉ lệ trẻ  */\r\n    $scope.isPointTogether = isPointTogether;   /*   Kiểm tra nhiều điêm trường dùng thực đơn chung   */\r\n    $scope.onChange_thucmuatheodvts = onChange_thucmuatheodvts; /*  Lượng thực mua ở điêm trường thay đổi */\r\n    $scope.selected = {};\r\n    $scope.table = {\r\n        rows: [],\r\n        groupColNames: {\r\n            soluong: {name: 'SL (ĐVT)', title: 'Thực mua cả nhóm theo đơn vị tính'},\r\n            soluong_kg: {name: 'SL (KG)', title: 'Thực mua cả nhóm theo Kg'},\r\n            dongia: {name: 'Đơn giá', title: 'Giá thực phẩm theo đơn vị tính'},\r\n            thanhtien: {name: 'Thành tiền', title: 'Thành tiền cả nhóm nhóm'}\r\n        }\r\n    };\r\n    /*\r\n    * Kiểm tra điều kiện là đơn vị có nhiều điểm trường và lên thực đơn chung\r\n    * */\r\n    function isPointTogether() {\r\n        var rs = false;\r\n        if ($scope.school_point) {\r\n            if (count($scope.school_point.points) > 1 && $scope.school_point.together === 1) {\r\n                rs = true;\r\n            }\r\n        }\r\n        return rs;\r\n    };\r\n    /*\r\n    * Sự kiện thực mua ở điểm trường bị thay đối\r\n    * */\r\n    function onChange_thucmuatheodvts(food, point, calc) {\r\n        food.thucmuatheodvt = 0;\r\n        angular.forEach(food.thucmuatheodvts, function (val, point) {\r\n            food.thucmuatheodvt = $['+'](food.thucmuatheodvt, val);\r\n        });\r\n        $scope.onChange_thucmuatheodvt(food, !calc, true);\r\n    }\r\n    /*\r\n    * Tách thực mua theo đơn vị tính ra các điểm trường theo tỉ lệ sĩ số trẻ\r\n    * */\r\n    function divide_pointThucmuatheodvt(food, group_adjust) {\r\n        if ($scope.isPointTogether()) {\r\n            var thucmuc = Number(food.thucmuatheodvt);\r\n            food.thucmuatheodvts = {};\r\n            var tong_tre = 0;\r\n            angular.forEach(group_adjust.row.sotres, function (val, point) {\r\n                tong_tre += val;\r\n            });\r\n            var thucmua = 0;\r\n            var p_min = {point: 1, value: 10000};\r\n            var p_max = {point: 1, value: 0};\r\n            angular.forEach(group_adjust.row.sotres, function (val, point) {\r\n                if (val == 0 || thucmuc == 0) {\r\n                    food.thucmuatheodvts[point] = 0;\r\n                    return;\r\n                }\r\n                var tile = val/tong_tre;\r\n                food.thucmuatheodvts[point] = $scope.safeRound($['*'](tile, thucmuc));\r\n                thucmua = $['+'](thucmua, food.thucmuatheodvts[point]);\r\n                if (p_min.value > food.thucmuatheodvts[point]) {\r\n                    p_min.point = point;\r\n                    p_min.value = food.thucmuatheodvts[point];\r\n                }\r\n                if (p_max.value < food.thucmuatheodvts[point]) {\r\n                    p_max.point = point;\r\n                    p_max.value = food.thucmuatheodvts[point];\r\n                }\r\n            });\r\n            var so_du = $['-'](thucmuc, thucmua);\r\n            if (so_du > 0) {\r\n                food.thucmuatheodvts[p_min.point] = $['+'](food.thucmuatheodvts[p_min.point], so_du);\r\n            } else if (so_du < 0){\r\n                food.thucmuatheodvts[p_max.point] = $['+'](food.thucmuatheodvts[p_max.point], so_du);\r\n            }\r\n        }\r\n    };\r\n    /*\r\n    * Sửa tên đi chợ\r\n    * */\r\n    function onChange_food_name(food) {\r\n        for (var f of food.foods) {\r\n            f.name_old = f.name;\r\n            f.name = food.name;\r\n        }\r\n    };\r\n    /*  Khi kích vào select chọn kho */\r\n    /*  Việc chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/\r\n    function warehouseSelectedOnClick(meal) {\r\n        meal.selected = !meal.selected;\r\n        var check_not_null = true;\r\n        angular.forEach($scope.row.meal_selection, function(item, index){\r\n            if(item.selected){\r\n                check_not_null = false;\r\n            }\r\n        });\r\n        if(check_not_null){\r\n            meal.selected = true;\r\n        }else{\r\n            $scope.totalCalculator();\r\n        }\r\n    };\r\n    function fn_copyForm (points) {\r\n        $scope.copy = {\r\n            list: points,\r\n            selected: {}\r\n        };\r\n        $.dm_datagrid.showAddForm({\r\n            module: $CFG.project+'/'+self.module,\r\n            action:'',\r\n            title:'Sao chép thực đơn',\r\n            size: size.normal,\r\n            fullScreen: false,\r\n            showButton: false,\r\n            scope: $scope,\r\n            content: $scope.controller.templates.formCopy\r\n        });\r\n    };\r\n    function fn_selectAdjustPoint() {\r\n        $scope.loadDataOfDay(null, $scope.copy.selected);\r\n    }\r\n    function initGrid (month) {\r\n        var urls = [$CFG.remote.base_url,'doing',$CFG.project,$scope.control,'list'];\r\n        $.dm_datagrid.init(\r\n            urls.join('/'),\r\n            $scope.control, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/\r\n            '', /*Tiêu đề cho bảng dữ liệu*/\r\n            [\r\n                [\r\n                    { field:'ck', checkbox: true },\r\n                    { title:'Ngày', field:'date', width: 90},\r\n                    { title:'Nhóm trẻ', field:'group_names', width:250},\r\n                    { title:'Tên thực đơn theo nhóm trẻ', field:'menu_planning_names', width: 250 },\r\n                    { title:'Số trẻ', field:'set', width: 70 },\r\n                    { title:'Tiền ăn', field:'money', width: 120 },\r\n                    { title:'', field:'school_point', width: 20, formatter: function (value, row) {\r\n                            return '<div id=\"datagrid-view-' + [row.day, row.month, row.schoolyear].join('-') + '\"></div>';\r\n                        } },\r\n                    { title:'Phiếu kê chợ', field:'marketbill_unit_id', width:70, formatter: function(value, row){\r\n                            return '<div id=\"datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-') + '\"></div>';\r\n                        } }\r\n                ]\r\n            ],\r\n            {\r\n                onDblClickRow: function(rowIndex, rowData) {\r\n                    $scope.selected.date = rowData.date;\r\n                    $scope.addjustForm($scope.selected.date);\r\n                }, onSelect: function(index, row) {\r\n                    $scope.$apply(function ($scope) {\r\n                        $scope.selected.date = row.date;\r\n                    });\r\n                }, onUnselect: function (index,row) {\r\n                    $scope.$apply(function ($scope) {\r\n                        $scope.selected.date = $.menu_adjust.date;\r\n                    });\r\n                }, onLoadSuccess: function(data) {\r\n                    setTimeout(function () {\r\n                        angular.forEach(data.rows, function (row, ind) {\r\n                            var btn = '<span class=\"glyphicon glyphicon-eye-open btn-over-green\" title=\"Mở thực đơn\" style=\"height: 14px; margin: 0 3px;\" ng-click=\"addjustForm(\\'' + row.date + '\\')\"></span>';\r\n                            $('div#datagrid-view-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));\r\n                            btn = '<span class=\"glyphicon glyphicon-pencil btn-over-green\" title=\"Sửa phiếu kê chợ\" style=\"height: 14px; margin: 0 3px;\" ng-click=\"marketbillForm(\\'' + row.date + '\\')\">Sửa</span>';\r\n                            $('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));\r\n                            if ($scope.isPointTogether() && !$scope.sys.configs.phieukecho_temple_point_old) {\r\n                                btn = '<a href=\"' +\r\n                                    $CFG.remote.base_url+'/report/' + $CFG.project+'/marketbill/pointsTogether?date=' + row.date +\r\n                                    '&type=1\"\" target=\"_blank\"><span class=\"glyphicon glyphicon-print btn-over-green\" title=\"Hiển thị phiếu kê chợ\" style=\"height: 14px; margin: 0 3px;\">In</span></a>';\r\n                                $('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));\r\n                            }\r\n                        });\r\n                    });\r\n                },\r\n                queryParams: {month: month},\r\n                pageList: [31],\r\n                pageSize: 31\r\n            }\r\n        );\r\n    };\r\n    /*\r\n    * Mở pupup danh sách biểu in liên quan tới phiếu kê chợ; sổ kiểm thực 3 bước\r\n    * */\r\n    function marketbillPrintForm () {\r\n        $.dm_datagrid.showAddForm({\r\n            module: $CFG.project+'/'+self.module,\r\n            action:'change_dish',\r\n            title:'Sửa món ăn',\r\n            size: 400,\r\n            fullScreen: false,\r\n            showButton: false,\r\n            scope: $scope,\r\n            content: $scope.project + '/marketbill/list-print-preview.html',\r\n            onShown: function(element, dialogRef){\r\n\r\n            }\r\n        });\r\n    };\r\n    /*Tổng tiền từng nhóm trẻ*/\r\n    function sumPayForGroup (foods, group_id) {\r\n        var rs = 0;\r\n        angular.forEach(foods, function (food, food_id) {\r\n            rs += $scope.getTongTien1Nhom(food.groups[group_id]);\r\n        });\r\n        return rs;\r\n    };\r\n    /*Tổng tiền tất cả các nhóm*/\r\n    function sumPayForAll (foods) {\r\n        var rs = 0;\r\n        angular.forEach(foods, function (food, food_id) {\r\n            rs += $scope.getTongTien(food);\r\n        });\r\n        return rs;\r\n    };\r\n    /*Hiển thị form xem thông tin hoặc thay đổi món ăn khác*/\r\n    function showChangeDish (meal, dish_old){\r\n        $scope.selected.dish = dish_old;\r\n        $scope.selected.meal = meal;\r\n        var self = $.menu_adjust;\r\n        $scope.foodAddeds = {};\r\n        angular.forEach(dish_old.ingredient, function (food, index) {\r\n            if (!food.quantity) {\r\n                food.quantity = food.quantity_edit;\r\n            }\r\n            food.deleted = false;\r\n        });\r\n        $.dm_datagrid.showAddForm({\r\n            module: $CFG.project+'/'+self.module,\r\n            action:'change_dish',\r\n            title:'Sửa món ăn',\r\n            size: size.wide,\r\n            fullScreen: false,\r\n            showButton: false,\r\n            scope: $scope,\r\n            content: $scope.controller.templates.changeDish,\r\n            onShown: function(element, dialogRef){\r\n\r\n            }\r\n        });\r\n    };\r\n    function showFormTotal () {\r\n        $scope.selected.food = undefined;\r\n\r\n        $.dm_datagrid.showEditForm({\r\n            module: $CFG.project+'/menu_adjust',\r\n            action:'',\r\n            title:'Cân đối khẩu phần',\r\n            scope: $scope,\r\n            size: size.wide,\r\n            showButton: false,\r\n            fullScreen: true,\r\n            askBeforeClose: false,\r\n            content: $scope.project+'/'+$scope.control+'/total.html',\r\n            onShown: function (dialog) {\r\n                $scope.processData($scope.menu_adjust.group_adjusts);\r\n            }\r\n        });\r\n    };\r\n\r\n    function closeFormTotal () {\r\n        if($scope.dialog) {\r\n            $scope.dialog.close();\r\n        }else{\r\n            dialogClose();\r\n        }\r\n    };\r\n    /*Chọn thực phẩm để thêm thành món*/\r\n    function onSelectAddFood (food_select) {\r\n        if(food_select) {\r\n            var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';\r\n            var data = {async: true,id: food_select.id};\r\n            process(url, data, function(resp) {\r\n                if(!resp) return;\r\n                var food = resp[0];\r\n                $scope.$apply(function(){\r\n                    food.dish_name = food.name.replace('Gạo','Cơm').replace('gạo','cơm');\r\n                    food.quantity = 10;\r\n                    if($scope.inventory[$scope.selected.meal.warehouse_id]){\r\n                        if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id]){\r\n                            if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].value>0) {\r\n                                food.price = $scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].price;\r\n                            }\r\n                        }\r\n                    }\r\n                    $scope.selected.food_add = food;\r\n                });\r\n            },function(){}, false);\r\n        }\r\n    };\r\n    /*Tạo thực phẩm thành món mới theo tên thực phẩm*/\r\n    function appendFoodToMeal (food, meal){\r\n        var dish = {\r\n            id: 'food_id_'+food.food_id,\r\n            name: food.dish_name,\r\n            tcp: food.tcp,\r\n            ingredient: {}\r\n        }\r\n        food.quantity || (food.quantity = 0);\r\n        food.quantity = Number(food.quantity);\r\n        dish.ingredient[food.food_id] = food;\r\n        if(!meal.dishes[dish.id]){\r\n            $scope.addFoodFromDish(meal,dish);\r\n            $scope.datagrid.data = {};\r\n            angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){\r\n                angular.forEach(meal_.dishes, function(dish_,ind){\r\n                    $scope.addFoodFromDish(meal_,dish_,true);\r\n                })\r\n            });\r\n            $scope.selected.group_adjust.data = $scope.datagrid.data;\r\n            angular.forEach($scope.datagrid.data, function(foods,warehouse_id){\r\n                angular.forEach(foods, function(food,food_id){\r\n                    $scope.onChange_luong1tre(food);\r\n                });\r\n            });\r\n            $scope.totalCalculator();\r\n        }else{\r\n\r\n        }\r\n        $scope.selected.food_tmp = undefined;\r\n        $scope.selected.food_add = undefined;\r\n    }\r\n    /*  Mở form thêm thực phẩm */\r\n    function showAddFood (meal){\r\n        $scope.selected.meal = meal;\r\n        $.dm_datagrid.showAddForm({\r\n            module: $CFG.project+'/'+self.module,\r\n            action:'add_food',\r\n            title:'Thêm mới',\r\n            size: size.small,\r\n            fullScreen: false,\r\n            showButton: false,\r\n            scope: $scope,\r\n            content: $scope.controller.templates.addFood,\r\n            onShown: function(element, dialogRef){\r\n\r\n            }\r\n        });\r\n    };\r\n    function foodSelected (food_selected){\r\n        /*Chua bit làm gi*/\r\n    }\r\n    /*Mở form nhập số trẻ từng điểm trường*/\r\n    function formEditSotreOfPoints () {\r\n        $scope.tmp || ($scope.tmp = {});\r\n        $scope.tmp.thucmuaCalc = true;\r\n        $scope.tmp.sotres = {};\r\n        angular.forEach($scope.row.sotres, function (val, point) {\r\n            $scope.tmp.sotres[point] = val;\r\n        });\r\n        $.dm_datagrid.showEditForm(\r\n            {\r\n                module: $CFG.project+'/menu_adjust',\r\n                action:'edit',\r\n                title:'Chỉnh sửa',\r\n                size: 450,\r\n                showButton: false,\r\n                draggable: true,\r\n                scope: $scope,\r\n                content: $scope.controller.templates.edit_number_children,\r\n                onShown: function(element) {\r\n                }\r\n            }\r\n        );\r\n    };\r\n    /*\r\n    * Cập nhật sự thay đổi số trẻ từng điểm trường\r\n    * */\r\n    function applySotres() {\r\n        $scope.row.sotre_old = $scope.row.sotre;\r\n        $scope.row.sotre = 0;\r\n        angular.forEach($scope.tmp.sotres, function (val, point) {\r\n            $scope.row.sotre = $['+']($scope.row.sotre, val);\r\n            val = Number(val);\r\n            $scope.row.sotres[point] = val;\r\n        });\r\n        // console.log($scope.tmp.thucmuaCalc, typeof $scope.tmp.thucmuaCalc, $scope.row.sotres, $scope.tmp.sotres);\r\n        $scope.onChangeSotre($scope.row, $scope.tmp.thucmuaCalc);\r\n    }\r\n    /*\r\n    * Kiểm tra số trẻ từng điểm trường đã bị thay đổi chưa\r\n    * */\r\n    function modifiedSotres() {\r\n        var rs = false;\r\n        angular.forEach($scope.tmp.sotres, function (val, point) {\r\n            if (val != $scope.row.sotres[point]) {\r\n                rs = true;\r\n            }\r\n        });\r\n        return rs;\r\n    }\r\n    /*Mở form nhập tiền bổ trợ từng điểm trường*/\r\n    function formEditTienbotroOfPoints (){\r\n        $.dm_datagrid.showEditForm(\r\n            {\r\n                module: $CFG.project+'/menu_adjust',\r\n                action:'edit',\r\n                title:'Chỉnh sửa',\r\n                size: size.small,\r\n                showButton: false,\r\n                scope: $scope,\r\n                content: $scope.controller.templates.edit_tien_bo_tro,\r\n                onShown: function(element) {\r\n                }\r\n            }\r\n        );\r\n    };\r\n    /*Mở form cân đối tự động*/\r\n    function balanceShow (){\r\n        var self = $.menu_adjust;\r\n        $.dm_datagrid.showAddForm({\r\n                module: $CFG.project+'/'+ $scope.control,\r\n                action:'balance',\r\n                title:'Cân đối',\r\n                size: size.wide,\r\n                fullScreen: true,\r\n                showButton: false,\r\n                scope: $scope,\r\n                content: $scope.controller.templates.balance,\r\n                onShown: function(element,dialogRef){\r\n                    setTimeout(function () {\r\n                        $scope.$apply(function () {\r\n                            $.balance.init($scope);\r\n                        });\r\n                    });\r\n                }\r\n            },\r\n            function(resp){\r\n                if(typeof callback === 'function') {\r\n                    callback(resp);\r\n                }else{\r\n                    $(\"#tbl_\"+self.module).datagrid('reload');\r\n                }\r\n            }\r\n        );\r\n    };\r\n    /*Chọn thực phẩm vào món*/\r\n    function addFoodToTempDish (food_selected){\r\n        if(food_selected) {\r\n            var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';\r\n            var data = {async: true,id: food_selected.id};\r\n            process(url, data, function(resp) {\r\n                if(!resp) return;\r\n                var food = resp[0];\r\n                $scope.$apply(function(){\r\n                    var id = food.food_id;\r\n                    // food.quantity = 0;\r\n                    // $scope.menu_adjust.food = undefined;\r\n                    food.quantity = 10;\r\n                    $scope.foodAddeds[id] = food;\r\n                    // scope.selected.dish.ingredient[id] = food;\r\n                    // $('#food').combobox('clear');\r\n                    // $scope.menu_adjust.dish_selected = null;\r\n                    $scope.selected.foodAdded = undefined;\r\n                });\r\n            },function(){}, false);\r\n        }\r\n    };\r\n    /*  Mở form thêm món ăn */\r\n    function showAddDish (meal){\r\n        var meals = $scope.selected.group_adjust.meals;\r\n        var group_id = $scope.selected.group_adjust.group_id;\r\n        $scope.foodAddeds = {};\r\n        $scope.selected.dish_tmp = undefined;\r\n        $scope.selected.dish = undefined;\r\n        $scope.selected.meal = meal;\r\n        $.dm_datagrid.showAddForm(\r\n            {\r\n                module: $CFG.project+'/menu_adjust',\r\n                action:'add_dish',\r\n                title:'Thêm mới',\r\n                size: size.wide,\r\n                fullScreen: false,\r\n                showButton: false,\r\n                scope: $scope,\r\n                content: $scope.controller.templates.addDish,\r\n                onShown: function(element, dialogRef) {\r\n                    $scope.selected.dish = {};\r\n                    var arr_meals = Object.values(meals);\r\n                    $.dm_datagrid.combobox('meal', arr_meals,{\r\n                        valueField: 'define',\r\n                        textField: 'name',\r\n                        value: meal.define,\r\n                        height: 24,\r\n                        panelHeight: 'auto',\r\n                        mode: 'local',\r\n                        onSelect: function(meal, element) {\r\n                            $.menu_adjust.combobox_load_dish($scope, meal, group_id);\r\n                        },\r\n                        queryParams: {},\r\n                        width: 200\r\n                    });\r\n                }\r\n            },\r\n            function(resp){\r\n                if (typeof callback === 'function') {\r\n                    callback(resp);\r\n                } else {\r\n                    $(\"#tbl_\" + self.module).datagrid('reload');\r\n                }\r\n            }\r\n        );\r\n    };\r\n    /*Cập nhật lại món sau khi sửa vào thực đơn*/\r\n    function changeDishApply (meal, dish_old){\r\n        dish_old || (dish_old = $scope.selected.dish);\r\n        dish_old.ingredient = Object.assign(dish_old.ingredient, $scope.foodAddeds);\r\n        var dish_new = dish_old;\r\n        /*Xóa món ăn cũ đi*/\r\n        /*Xóa thực phẩm đã bị xóa trong món ăn*/\r\n        var tmp_foods = {};\r\n        angular.forEach(dish_new.ingredient, function(food, food_id){\r\n            if(food && !food.deleted) {\r\n                if (typeof food.quantity_edit ==='undefined') {\r\n                    food.quantity_edit = Number(food.quantity);\r\n                }\r\n                tmp_foods[food.food_id] = clone(food);\r\n            }\r\n        });\r\n        dish_new.ingredient = tmp_foods;\r\n        /*Thêm món ăn mới vào*/\r\n        meal || (meal = $scope.selected.meal);\r\n        meal.dishes[dish_new.id] = dish_new;\r\n        /*\r\n        * Chỗ này dữ liệu cũ để dish.id + ' ' nên bị lặp món -> cập nhật lại món bị nhảy gấp đôi lượng\r\n        * Chuẩn hóa lại ds món teo id gốc;\r\n        * */\r\n        var dishes = {};\r\n        angular.forEach(meal.dishes, function (dish, dish_id) {\r\n            dishes[dish.id] = dish;\r\n        });\r\n        meal.dishes = dishes;\r\n        $scope.datagrid.data = {};\r\n        angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){\r\n            angular.forEach(meal_.dishes, function(dish_,ind){\r\n                $scope.addFoodFromDish(meal_, dish_, true);\r\n            })\r\n        });\r\n        $scope.selected.group_adjust.data = $scope.datagrid.data;\r\n        console.log('KQ',$scope.selected.group_adjust.data);\r\n        dialogClose();\r\n        $scope.totalCalculator();\r\n    };\r\n\r\n    function addDishApply (){\r\n        var dish = $scope.selected.dish;\r\n        var meal_define = $scope.selected.meal.define;\r\n        dish.ingredient = Object.assign(dish.ingredient, $scope.foodAddeds);\r\n        /*Cập nhật món mới vào thực đơn*/\r\n        $scope.addFoodFromDish($scope.selected.group_adjust.meals[meal_define], dish);\r\n        $scope.datagrid.data = {};\r\n        angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){\r\n            angular.forEach(meal_.dishes, function(dish_,ind){\r\n                $scope.addFoodFromDish(meal_,dish_,true);\r\n            });\r\n        });\r\n        $scope.selected.group_adjust.data = $scope.datagrid.data;\r\n        angular.forEach($scope.datagrid.data, function(foods, warehouse_id){\r\n            angular.forEach(foods, function(food, food_id){\r\n                $scope.onChange_luong1tre(food);\r\n            });\r\n        });\r\n        $scope.totalCalculator();\r\n        dialogClose();\r\n    }\r\n    function onChange_soluong (group) {\r\n        group.food.thucmuatheodvt = group.soluong;\r\n        $scope.onChange_thucmuatheodvt(group.food);\r\n    }\r\n    function getTongTien(food) {\r\n        var rs = 0;\r\n        angular.forEach(food.groups, function (group, group_id) {\r\n            var tongnhom = getTongTien1Nhom(group);\r\n            rs = $['+'](rs, tongnhom);\r\n        });\r\n        return rs;\r\n    }\r\n    function getTongTien1Nhom(group) {\r\n        var rs = 0;\r\n        if(group) {\r\n            if (group.food.exports == undefined) {\r\n                rs = $['+'](rs, group.thanhtien);\r\n            } else {\r\n                angular.forEach(group.food.exports, function (exp, food_id_price) {\r\n                    rs = $['+'](rs, $['*'](exp.quantity, exp.price));\r\n                });\r\n            }\r\n        }\r\n        return rs;\r\n    }\r\n    function getTongluong(food) {\r\n        var rs = 0;\r\n        // if(console.log(food))\r\n        angular.forEach(food.groups, function (group, group_key) {\r\n            rs += parseFloat(group.soluong);\r\n        });\r\n        return rs;\r\n    }\r\n    /*\r\n    * xử lý dữ liệu gộp form\r\n    * dataAll = selected.group_adjusts : các thực đơn\r\n    * */\r\n    function processData (dataAll) {\r\n        $scope.table.rows = {};\r\n        $scope.table.groups = {};\r\n        var fds = {};\r\n        angular.forEach(dataAll, function (group, group_id) {\r\n            var group_key = group.menu_plannings[0].group_key;\r\n            $scope.table.groups[group_key] = {\r\n                name: group.name,\r\n                title: ''\r\n            };\r\n            angular.forEach(group.menu_plannings[0].data, function (kho, meal_key) {\r\n                var warehouse_id = 2;\r\n                if (meal_key == 1) {\r\n                    warehouse_id = group.menu_plannings[0].meals['buasang'].warehouse_id;\r\n                } else if (meal_key == 3) {\r\n                    warehouse_id = group.menu_plannings[0].meals['buatoi'].warehouse_id;\r\n                }\r\n                fds[meal_key] || (fds[meal_key] = {});\r\n                angular.forEach(kho, function (food, food_id) {\r\n                    fds[meal_key][food_id] || (fds[meal_key][food_id] = {\r\n                        name: food.name,\r\n                        price: food.price,\r\n                        groups: {}\r\n                    });\r\n                    fds[meal_key][food_id].groups[group_key] || (fds[meal_key][food_id].groups[group_key] = {\r\n                        soluong: food.thucmuatheodvt,\r\n                        soluong_kg: food.thucmua1nhom,\r\n                        thanhtien: food.thucmuatheodvt * food.price,\r\n                        food: food\r\n                    });\r\n                });\r\n            });\r\n        });\r\n        $scope.table.rows = fds;\r\n    };\r\n    function onSelectDish (row) {\r\n        process($CFG.remote.base_url + '/doing/dinhduong/menu_adjust/dishs', {id: row.id, async: true}, function (resp) {\r\n            if (resp) {\r\n                row = resp;\r\n                row.quantity_edit = row.quantity;\r\n                $scope.menu_planning.ignore_ids = '';\r\n                var kt = 0;\r\n                angular.forEach(row.ingredient, function (food, key) {\r\n                    food.name_edit || (food.name_edit = food.name);\r\n                    $scope.menu_planning.ignore_ids = $scope.menu_planning.ignore_ids + ',' + key;\r\n                    if (food.quantities) {\r\n                        if (count(food.quantities) == 0) {\r\n                            food.quantities = {};\r\n                        }\r\n                        if (food.quantities[$scope.row.group_id] > 0) {\r\n                            food.quantity = food.quantities[$scope.row.group_id];\r\n                        }\r\n                    }\r\n                    if (food.quantity > 0) kt++;\r\n                });\r\n                if (!kt) {\r\n                    if (!count(row.ingredient)) {\r\n                        alert('Món ăn không có thực phẩm');\r\n                    }\r\n                    if (!kt) {\r\n                        alert('Món ăn không có thực phẩm nào có lượng dành cho nhóm trẻ đang chọn.\\n Hãy kiểm tra lại lượng của món ăn theo nhóm trẻ.');\r\n                    }\r\n                }\r\n                /*Kiểm tra thực phẩm có thêm ngoài không thì đẩy lại vào*/\r\n                // if (dish) { /*Nếu là thêm mới thì không có món được truyền vào*/\r\n                //     if (dish.id === row.id) {\r\n                //         angular.forEach(dish.ingredient, function (food, food_id) {\r\n                //             food.name_edit = food.name;\r\n                //             food.deleted = false;\r\n                //             if (!row.ingredient[food_id]) {\r\n                //                 row.ingredient[food_id] = food;\r\n                //             }\r\n                //         });\r\n                //     }\r\n                // $('#dish').combobox('clear');\r\n                $scope.$apply(function () {\r\n                    $scope.selected.dish = row;\r\n                });\r\n            }\r\n        }, function () {\r\n        }, false);\r\n    }\r\n}]);", "angular_app.controller('menu_planningController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){\r\n    $scope.project = 'dinhduong';\r\n    $scope.control = 'menu_planning';\r\n    $scope.systemConfig = {};\r\n    $scope.systemConfig.module = 'menu_planning';\r\n    $scope.warehouseSelectedOnClick = warehouseSelectedOnClick;\r\n    $scope.onChange_food_name = onChange_food_name;\r\n    /*  Khi kích vào select chọn kho */\r\n    /*  <PERSON>iệc chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/\r\n    function warehouseSelectedOnClick(meal) {\r\n        meal.selected = !meal.selected;\r\n        var check_not_null = true;\r\n        angular.forEach($scope.row.meal_selection, function(item, index){\r\n            if(item.selected){\r\n                check_not_null = false;\r\n            }\r\n        });\r\n        if(check_not_null){\r\n            meal.selected = true;\r\n        }else{\r\n            $scope.totalCalculator();\r\n        }\r\n    };\r\n    /*\r\n    * <PERSON><PERSON>a tên đi chợ\r\n    * */\r\n    function onChange_food_name(food) {\r\n        for (var f of food.foods) {\r\n            f.name_old = f.name;\r\n            f.name = food.name;\r\n        }\r\n    };\r\n}]);"]}