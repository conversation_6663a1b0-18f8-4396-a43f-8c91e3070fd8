(function (app) {
    app.controller('StudentHealthDashboardController', DashboardController);

    function DashboardController($scope, $q, ACTIVE_MONTHS, AuthService, CommonService, CourseService, DiseaseService, StudentMedicalExaminationService, $uibModal) {
        var vm = this;

        $scope.sys.module.title = 'Sức khỏe học sinh';

        vm.filters = {
            keyword: '',
            month: 0,
            school_year: $CFG.active_year || (new Date()).getFullYear(),
            course_id: 0,
        };

        vm.paginate = {
            maxSize: 5,
            limit: 50,
            page: 1,
            total: 0,
            perPages: [15, 30, 50, 100],
        };

        vm.courses = [];

        vm.years = CommonService.generateSchoolYear();

        vm.months = _.map(ACTIVE_MONTHS, function (month) {
            return {id: month, title: _.padStart(month, 2, '0')};
        });

        vm.month_years = _.map(ACTIVE_MONTHS, function (month) {
            if (month >= 1 && month <=8) {
                return {id: month, title: _.padStart(month, 2, '0') + '/' + (vm.filters.school_year + 1)};
            }
            return {id: month, title: _.padStart(month, 2, '0') + '/' + vm.filters.school_year};
        });

        vm.months.unshift({id: 0, title: 'Chọn tháng'});

        vm.medicalExaminations = {};
        vm.medicalExaminationNote = {};
        vm.medicalExaminationConclusion = {};

        CourseService.fetchCourses().then(function (courses) {
            if (!AuthService.isSuperAdmin()) {
                vm.filters.course_id = _.get(courses, '[0]courses.data[0].id', -1);
            }

            if (AuthService.isSuperAdmin()) {
                courses.unshift({id: 0, name: 'Toàn trường'});
            } else {
                courses.unshift({id: -1, name: 'Chọn lớp'});
            }

            vm.courses = _.assign({}, courses);
        });

        $scope.detachedZScore = ["40","14"].includes($CFG.province);

        function fetchStudents() {
            if (!vm.filters.school_year || !vm.filters.month || vm.filters.course_id < 0) {
                return;
            }

            $q.all([
                DiseaseService.getDiseaseOfSchool(1,0,vm.filters.course_id),
                StudentMedicalExaminationService.fetchStudents(vm.paginate.page, vm.paginate.limit, vm.filters)
            ]).then(function (response) {
                vm.diseases = response[0].diseases;
                vm.default_diseases = {};
                _.forEach(response[0].default_diseases, (v) => {
                    vm.default_diseases[v['code']] = v['default_note']
                });
                vm.students = response[1].students;
                vm.paginate.total = response[1].total;
                vm.unit_level = response[0].user.level;
                vm.medicalExaminations = {};
                vm.studentDiseasesNote = {};
                vm.medicalExaminationNote = {};
                vm.medicalExaminationConclusion = {};

                _.forEach(vm.students, function (student) {
                    vm.medicalExaminations[student.id] = _.reduce(_.get(student, 'medical_examinations', []), function (obj, exam) {
                        obj[_.get(exam, 'disease_id')] = _.get(exam, 'is_positive', false);

                        return obj;
                    }, {});
                    if ($scope.detachedZScore) {

                        vm.studentDiseasesNote[student.id] = {};
                        
                        _.forEach(vm.diseases, (disease) => {
                            let note = (disease.code in student.medical_examinations) ? student.medical_examinations[disease.code].note : vm.default_diseases[disease.code];
                            vm.studentDiseasesNote[student.id][disease.id] = note;
                        })
                    } 
                    else {

                        vm.studentDiseasesNote[student.id] = _.reduce(_.get(student, 'medical_examinations', []), function (obj, exam) {
                            obj[_.get(exam, 'disease_id')] = _.get(exam, 'note', '');
    
                            return obj;
                        }, {});
                    }



                    vm.medicalExaminationNote[student.id] = student.medical_examination_note;
                    vm.medicalExaminationConclusion[student.id] = student.conclusion.toString();
                });
            });
        }

        vm.onDiseaseOfStudentChange = function (student, disease) {
            StudentMedicalExaminationService.store(
                student.id,
                disease.id,
                _.get(vm.medicalExaminations, [student.id, disease.id], false),
                vm.filters.school_year,
                vm.filters.month,
                vm.studentDiseasesNote[student.id][disease.id]
            ).then(function () {
                // TODO:
            });
        };

        vm.formChooseMonth = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'choose_month_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    // $ctrl.date = new Date();
                    $ctrl.error = false;
                    $ctrl.error_conclusion = false;

                    $ctrl.months = vm.months;
                    $ctrl.month_selected = {
                        1 : 0,
                        2 : 0,
                        3 : 0,
                        4 : 0,
                        5 : 0,
                    };

                    $ctrl.months_conclusion = {
                        1 : 0,
                        2 : 0,
                        3 : 0,
                    };

                    $ctrl.show = function (type) {
                        let arr_month = [];
                        for(let i = 1; i <= 5; i++ ) {
                            if($ctrl.month_selected[i] && !arr_month.includes($ctrl.month_selected[i])){
                                arr_month.push($ctrl.month_selected[i]);
                            }
                        }
                        let arr_month_conclusion = [];
                        for(let i = 1; i <= 3; i++ ) {
                            if($ctrl.months_conclusion[i] && !arr_month_conclusion.includes($ctrl.months_conclusion[i])){
                                arr_month_conclusion.push($ctrl.months_conclusion[i]);
                            }
                        }
                        let url = '';
                        if(type == 1 )
                            url = $CFG.remote.base_url+'/dinhduong/cando/rpt_follow_weight_height?schoolyear='+vm.filters.school_year+'&course_id='+vm.filters.course_id+'&months='+JSON.stringify(arr_month)+'&months_conclusion='+JSON.stringify(arr_month_conclusion);
                        else
                            url = $CFG.remote.base_url+'/dinhduong/cando/rpt_follow_disease?schoolyear='+vm.filters.school_year+'&course_id='+vm.filters.course_id+'&months='+JSON.stringify(arr_month)+'&months_conclusion='+JSON.stringify(arr_month_conclusion);
                        window.open(url)
                    }

                    $ctrl.changeMonth = function (time,month_selected,error) {
                        let count = 0;
                        if($ctrl[month_selected][time]) {
                            for(let i = parseInt(time)- 1; i >= 1; i--) {
                                if(!$ctrl[month_selected][i]) {
                                    count ++;
                                    $ctrl[error] = true;
                                }
                            }
                            for(let i = 1; i <= 5; i++ ) {
                                if($ctrl[month_selected][i] && $ctrl[month_selected][i+1]){
                                    let index1 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i]);
                                    let index2 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i+1]);
                                    if(index1 > index2){
                                        count ++;
                                        $ctrl[error] = true;
                                    }
                                }
                            }
                            
                        }
                        if (count == 0) $ctrl[error] = false;
                    }

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        };

        vm.onSchoolYearChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onMonthChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onCourseChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onPageChange = function () {
            vm.students = [];
            fetchStudents();
        };

        vm.onKeywordChange = function () {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onPageLimitChange = function () {
            vm.paginate.page = 1;
            vm.students = [];
            fetchStudents();
        };

        vm.reload = function reload() {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentMedicalExaminationService.generateDownloadExcelUrlForDashboard(vm.filters);
        };
        vm.downloadExcelReports = function downloadExcel(date) {
            location.href = StudentMedicalExaminationService.generateDownloadExcelUrlForDashboardReports(Object.assign(vm.filters, { date : date }));
        };

        vm.downloadExcelFollows = function(months) {
            location.href = StudentMedicalExaminationService.generateDownloadExcelUrlForDashboardFollows(Object.assign(vm.filters, { months : months }));
        }

        vm.formExportReports = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'exports_reports_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    $ctrl.date = new Date();

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.exports = function () {
                        vm.downloadExcelReports($('#txt_date_export').val());
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        };

        vm.formExportFollows = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'exports_follows_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    $ctrl.date = new Date();
                    $ctrl.month_years = vm.month_years;
                    $ctrl.month_selects = [];
                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.exports = function () {
                        var elements = $('input[name="month_year"]:checked');

                        if (elements.length == 0) {
                            return;
                        }
                        _.forEach(elements, function(element){
                            $ctrl.month_selects.push(element.value);
                        });
                        vm.downloadExcelFollows($ctrl.month_selects);
                        $ctrl.month_selects = [];
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        };

        vm.storeMedicalExaminationNote = function (student,type = 0) {
            if (vm.medicalExaminationNote[student.id] === student.medical_examination_note && type == 0) {
                return;
            }

            StudentMedicalExaminationService.storeMedicalExaminationNote(
                student.id,
                vm.medicalExaminationNote[student.id],
                vm.filters.school_year,
                vm.filters.month,
                vm.medicalExaminationConclusion[student.id],
            ).then(function () {
                student.medical_examination_note = vm.medicalExaminationNote[student.id];
                student.conclusion = vm.medicalExaminationConclusion[student.id];
            });
        }
    }

    DashboardController.$inject = [
        '$scope',
        '$q',
        'ACTIVE_MONTHS',
        'AuthService',
        'CommonService',
        'CourseService',
        'DiseaseService',
        'StudentMedicalExaminationService',
        '$uibModal'
    ];
})(window.angular_app);
