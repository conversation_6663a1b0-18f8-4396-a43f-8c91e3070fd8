{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///socket.io.js", "webpack:///webpack/bootstrap 3dcdaa84a447b9ebdc0b", "webpack:///./lib/index.js", "webpack:///./lib/url.js", "webpack:///./~/parseuri/index.js", "webpack:///./~/debug/src/browser.js", "webpack:///./~/process/browser.js", "webpack:///./~/debug/src/debug.js", "webpack:///./~/ms/index.js", "webpack:///./~/socket.io-parser/index.js", "webpack:///./~/component-emitter/index.js", "webpack:///./~/socket.io-parser/binary.js", "webpack:///./~/isarray/index.js", "webpack:///./~/socket.io-parser/is-buffer.js", "webpack:///./lib/manager.js", "webpack:///./~/engine.io-client/lib/index.js", "webpack:///./~/engine.io-client/lib/socket.js", "webpack:///./~/engine.io-client/lib/transports/index.js", "webpack:///./~/engine.io-client/lib/xmlhttprequest.js", "webpack:///./~/has-cors/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-xhr.js", "webpack:///./~/engine.io-client/lib/transports/polling.js", "webpack:///./~/engine.io-client/lib/transport.js", "webpack:///./~/engine.io-parser/lib/browser.js", "webpack:///./~/engine.io-parser/lib/keys.js", "webpack:///./~/has-binary2/index.js", "webpack:///./~/arraybuffer.slice/index.js", "webpack:///./~/after/index.js", "webpack:///./~/engine.io-parser/lib/utf8.js", "webpack:///(webpack)/buildin/module.js", "webpack:///./~/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack:///./~/blob/index.js", "webpack:///./~/parseqs/index.js", "webpack:///./~/component-inherit/index.js", "webpack:///./~/yeast/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-jsonp.js", "webpack:///./~/engine.io-client/lib/transports/websocket.js", "webpack:///./~/indexof/index.js", "webpack:///./lib/socket.js", "webpack:///./~/to-array/index.js", "webpack:///./lib/on.js", "webpack:///./~/component-bind/index.js", "webpack:///./~/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "lookup", "uri", "opts", "_typeof", "undefined", "io", "parsed", "url", "source", "path", "sameNamespace", "cache", "nsps", "newConnection", "forceNew", "multiplex", "debug", "Manager", "query", "socket", "Symbol", "iterator", "obj", "constructor", "prototype", "parser", "managers", "protocol", "connect", "Socket", "global", "loc", "location", "host", "char<PERSON>t", "test", "parseuri", "port", "ipv6", "indexOf", "href", "re", "parts", "str", "src", "b", "e", "substring", "replace", "length", "exec", "i", "authority", "ipv6uri", "process", "useColors", "window", "type", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "console", "firebug", "exception", "table", "parseInt", "RegExp", "$1", "formatArgs", "args", "namespace", "humanize", "diff", "color", "splice", "index", "lastC", "log", "Function", "apply", "arguments", "save", "namespaces", "storage", "removeItem", "load", "r", "env", "DEBUG", "localstorage", "localStorage", "chrome", "local", "colors", "formatters", "j", "v", "JSON", "stringify", "err", "message", "enable", "defaultSetTimout", "Error", "defaultClearTimeout", "runTimeout", "fun", "cachedSetTimeout", "setTimeout", "runClearTimeout", "marker", "cachedClearTimeout", "clearTimeout", "cleanUpNextTick", "draining", "currentQueue", "queue", "concat", "queueIndex", "drainQueue", "timeout", "len", "run", "<PERSON><PERSON>", "array", "noop", "nextTick", "Array", "push", "title", "browser", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "name", "binding", "cwd", "chdir", "dir", "umask", "selectColor", "hash", "charCodeAt", "Math", "abs", "createDebug", "enabled", "self", "curr", "Date", "ms", "prevTime", "prev", "coerce", "unshift", "format", "formatter", "val", "logFn", "bind", "destroy", "init", "instances", "names", "skips", "split", "substr", "instance", "disable", "stack", "parse", "String", "n", "parseFloat", "y", "d", "h", "s", "fmtShort", "round", "fmtLong", "plural", "floor", "ceil", "options", "isNaN", "Encoder", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "attachments", "nsp", "data", "payload", "tryStringify", "ERROR_PACKET", "encodeAsBinary", "callback", "writeEncoding", "bloblessData", "deconstruction", "binary", "deconstructPacket", "pack", "packet", "buffers", "removeBlobs", "Decoder", "reconstructor", "decodeString", "Number", "types", "error", "buf", "next", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "ERROR", "isArray", "BinaryReconstructor", "reconPack", "msg", "Emitter", "isBuf", "CONNECT", "DISCONNECT", "EVENT", "ACK", "encode", "encoding", "add", "base64", "takeBinaryData", "finishedReconstruction", "binData", "reconstructPacket", "mixin", "key", "addEventListener", "event", "fn", "_callbacks", "removeEventListener", "callbacks", "cb", "slice", "hasListeners", "_deconstructPacket", "placeholder", "_placeholder", "num", "newData", "_reconstructPacket", "toString", "Object", "withNativeBlob", "Blob", "withNativeFile", "File", "packetData", "_removeBlobs", "cur<PERSON><PERSON>", "containingObject", "pendingBlobs", "fileReader", "FileReader", "onload", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "arr", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "Backoff", "min", "max", "jitter", "readyState", "connecting", "lastPing", "packetBuffer", "_parser", "encoder", "decoder", "autoConnect", "open", "eio", "has", "hasOwnProperty", "emitAll", "updateSocketIds", "generateId", "engine", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "reconnecting", "attempts", "reconnect", "skipReconnect", "openSub", "onopen", "errorSub", "cleanup", "timer", "close", "onping", "onpong", "ondata", "ondecoded", "onerror", "onConnecting", "encodedPackets", "write", "processPacketQueue", "shift", "subsLength", "sub", "disconnect", "reset", "onclose", "reason", "delay", "duration", "onreconnect", "attempt", "hostname", "secure", "agent", "parseqs", "decode", "upgrade", "forceJSONP", "jsonp", "forceBase64", "enablesXDR", "timestampParam", "timestampRequests", "transports", "transportOptions", "writeBuffer", "prevBufferLen", "policyPort", "rememberUpgrade", "binaryType", "onlyBinaryUpgrades", "perMessageDeflate", "threshold", "pfx", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "forceNode", "freeGlobal", "extraHeaders", "keys", "localAddress", "upgrades", "pingInterval", "pingTimeout", "pingIntervalTimer", "pingTimeoutTimer", "clone", "o", "priorWebsocketSuccess", "Transport", "createTransport", "EIO", "transport", "sid", "requestTimeout", "protocols", "setTransport", "onDrain", "onPacket", "onError", "onClose", "probe", "onTransportOpen", "upgradeLosesBinary", "supportsBinary", "failed", "send", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "onOpen", "l", "onHandshake", "setPing", "code", "filterUpgrades", "onHeartbeat", "ping", "sendPacket", "writable", "compress", "cleanupAndClose", "waitForUpgrade", "desc", "filteredUpgrades", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "XMLHttpRequest", "XHR", "JSONP", "websocket", "hasCORS", "XDomainRequest", "join", "empty", "Polling", "Request", "method", "async", "isBinary", "create", "unload<PERSON><PERSON><PERSON>", "requests", "abort", "inherit", "request", "doWrite", "req", "sendXhr", "doPoll", "onData", "pollXhr", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "hasXDR", "onLoad", "responseText", "onreadystatechange", "contentType", "getResponseHeader", "responseType", "status", "requestsCount", "onSuccess", "fromError", "response", "attachEvent", "hasXHR2", "yeast", "doOpen", "poll", "onPause", "total", "decodePayload", "doClose", "packets", "callbackfn", "encodePayload", "schema", "b64", "description", "decodePacket", "encodeBase64Object", "encode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBase64Packet", "contentArray", "Uint8Array", "result<PERSON><PERSON><PERSON>", "byteLength", "encodeBlobAsArrayBuffer", "fr", "encodePacket", "encodeBlob", "dontSendBlobs", "blob", "tryDecode", "utf8", "strict", "map", "ary", "each", "done", "after", "eachWithIndex", "el", "base64encoder", "hasBinary", "sliceBuffer", "isAndroid", "isPhantomJS", "pong", "packetslist", "utf8encode", "encoded", "readAsDataURL", "b64data", "fromCharCode", "typed", "basic", "btoa", "utf8decode", "decodeBase64Packet", "asArray", "rest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOne", "doneCallback", "encodePayloadAsBlob", "encodePayloadAsArrayBuffer", "results", "decodePayloadAsBinary", "chr", "ret", "totalLength", "reduce", "acc", "resultArray", "bufferIndex", "for<PERSON>ach", "isString", "ab", "view", "lenStr", "binaryIdentifier", "size", "lengthAry", "bufferTail", "tailArray", "msg<PERSON><PERSON>th", "toJSON", "arraybuffer", "start", "end", "bytes", "abv", "ii", "count", "err_cb", "proxy", "bail", "__WEBPACK_AMD_DEFINE_RESULT__", "ucs2decode", "string", "value", "extra", "output", "counter", "ucs2encode", "stringFromCharCode", "checkScalarValue", "codePoint", "toUpperCase", "createByte", "encodeCodePoint", "symbol", "codePoints", "byteString", "readContinuationByte", "byteIndex", "byteCount", "continuationByte", "byteArray", "decodeSymbol", "byte1", "byte2", "byte3", "byte4", "tmp", "freeExports", "webpackPolyfill", "deprecate", "paths", "children", "chars", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "mapArrayBufferViews", "chunk", "copy", "set", "byteOffset", "BlobBuilderConstructor", "bb", "BlobBuilder", "append", "getBlob", "BlobConstructor", "WebKitBlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "blobSupported", "a", "blobSupportsArrayBufferView", "blobBuilderSupported", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "alphabet", "decoded", "now", "seed", "JSONPPolling", "___eio", "script", "rNewline", "rEscapedNewline", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "complete", "initIframe", "html", "iframeId", "area", "className", "position", "top", "left", "target", "setAttribute", "action", "submit", "WS", "usingBrowserWebSocket", "BrowserWebSocket", "WebSocket", "NodeWebSocket", "MozWebSocket", "check", "headers", "ws", "supports", "addEventListeners", "onmessage", "ev", "json", "ids", "acks", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "connected", "disconnected", "flags", "toArray", "hasBin", "events", "connect_error", "connect_timeout", "reconnect_attempt", "reconnect_failed", "reconnect_error", "subEvents", "pop", "onpacket", "rootNamespaceError", "onconnect", "onevent", "onack", "ondisconnect", "ack", "sent", "emitBuffered", "list", "factor", "pow", "rand", "random", "deviation"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,GAAAD,IAEAD,EAAA,GAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAAUL,EAAQD,EAASM,GAEhC,YErBD,SAASS,GAAQC,EAAKC,GACD,YAAf,mBAAOD,GAAP,YAAAE,EAAOF,MACTC,EAAOD,EACPA,EAAMG,QAGRF,EAAOA,KAEP,IAQIG,GARAC,EAASC,EAAIN,GACbO,EAASF,EAAOE,OAChBd,EAAKY,EAAOZ,GACZe,EAAOH,EAAOG,KACdC,EAAgBC,EAAMjB,IAAOe,IAAQE,GAAMjB,GAAIkB,KAC/CC,EAAgBX,EAAKY,UAAYZ,EAAK,0BACtB,IAAUA,EAAKa,WAAaL,CAiBhD,OAbIG,IACFG,EAAM,+BAAgCR,GACtCH,EAAKY,EAAQT,EAAQN,KAEhBS,EAAMjB,KACTsB,EAAM,yBAA0BR,GAChCG,EAAMjB,GAAMuB,EAAQT,EAAQN,IAE9BG,EAAKM,EAAMjB,IAETY,EAAOY,QAAUhB,EAAKgB,QACxBhB,EAAKgB,MAAQZ,EAAOY,OAEfb,EAAGc,OAAOb,EAAOG,KAAMP,GFR/B,GAAIC,GAA4B,kBAAXiB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,IErDnQf,EAAMhB,EAAQ,GACdkC,EAASlC,EAAQ,GACjB0B,EAAU1B,EAAQ,IAClByB,EAAQzB,EAAQ,GAAS,mBAM7BL,GAAOD,QAAUA,EAAUe,CAM3B,IAAIW,GAAQ1B,EAAQyC,WAuDpBzC,GAAQ0C,SAAWF,EAAOE,SAS1B1C,EAAQ2C,QAAU5B,EAQlBf,EAAQgC,QAAU1B,EAAQ,IAC1BN,EAAQ4C,OAAStC,EAAQ,KF8DnB,SAAUL,EAAQD,EAASM,IAEJ,SAASuC,GAAS,YGtI/C,SAASvB,GAAKN,EAAK8B,GACjB,GAAIT,GAAMrB,CAGV8B,GAAMA,GAAOD,EAAOE,SAChB,MAAQ/B,IAAKA,EAAM8B,EAAIJ,SAAW,KAAOI,EAAIE,MAG7C,gBAAoBhC,KAClB,MAAQA,EAAIiC,OAAO,KAEnBjC,EADE,MAAQA,EAAIiC,OAAO,GACfH,EAAIJ,SAAW1B,EAEf8B,EAAIE,KAAOhC,GAIhB,sBAAsBkC,KAAKlC,KAC9Be,EAAM,uBAAwBf,GAE5BA,EADE,mBAAuB8B,GACnBA,EAAIJ,SAAW,KAAO1B,EAEtB,WAAaA,GAKvBe,EAAM,WAAYf,GAClBqB,EAAMc,EAASnC,IAIZqB,EAAIe,OACH,cAAcF,KAAKb,EAAIK,UACzBL,EAAIe,KAAO,KACF,eAAeF,KAAKb,EAAIK,YACjCL,EAAIe,KAAO,QAIff,EAAIb,KAAOa,EAAIb,MAAQ,GAEvB,IAAI6B,GAAOhB,EAAIW,KAAKM,QAAQ,QAAS,EACjCN,EAAOK,EAAO,IAAMhB,EAAIW,KAAO,IAAMX,EAAIW,IAO7C,OAJAX,GAAI5B,GAAK4B,EAAIK,SAAW,MAAQM,EAAO,IAAMX,EAAIe,KAEjDf,EAAIkB,KAAOlB,EAAIK,SAAW,MAAQM,GAAQF,GAAOA,EAAIM,OAASf,EAAIe,KAAO,GAAM,IAAMf,EAAIe,MAElFf,EApET,GAAIc,GAAW7C,EAAQ,GACnByB,EAAQzB,EAAQ,GAAS,uBAM7BL,GAAOD,QAAUsB,IH6NaX,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,GItOxB,GAAAwD,GAAA,0OAEAC,GACA,iIAGAxD,GAAAD,QAAA,SAAA0D,GACA,GAAAC,GAAAD,EACAE,EAAAF,EAAAJ,QAAA,KACAO,EAAAH,EAAAJ,QAAA,IAEAM,KAAA,GAAAC,IAAA,IACAH,IAAAI,UAAA,EAAAF,GAAAF,EAAAI,UAAAF,EAAAC,GAAAE,QAAA,UAAwEL,EAAAI,UAAAD,EAAAH,EAAAM,QAOxE,KAJA,GAAApD,GAAA4C,EAAAS,KAAAP,GAAA,IACA1C,KACAkD,EAAA,GAEAA,KACAlD,EAAAyC,EAAAS,IAAAtD,EAAAsD,IAAA,EAUA,OAPAN,KAAA,GAAAC,IAAA,IACA7C,EAAAO,OAAAoC,EACA3C,EAAAgC,KAAAhC,EAAAgC,KAAAc,UAAA,EAAA9C,EAAAgC,KAAAgB,OAAA,GAAAD,QAAA,KAAwE,KACxE/C,EAAAmD,UAAAnD,EAAAmD,UAAAJ,QAAA,QAAAA,QAAA,QAAAA,QAAA,KAAkF,KAClF/C,EAAAoD,SAAA,GAGApD,IJqPM,SAAUf,EAAQD,EAASM,IK1RjC,SAAA+D,GA2CA,QAAAC,KAIA,2BAAAC,iBAAAF,SAAA,aAAAE,OAAAF,QAAAG,QAKA,mBAAAC,uBAAAC,YAAAD,UAAAC,UAAAC,cAAAC,MAAA,4BAMA,mBAAAC,oBAAAC,iBAAAD,SAAAC,gBAAAC,OAAAF,SAAAC,gBAAAC,MAAAC,kBAEA,mBAAAT,gBAAAU,UAAAV,OAAAU,QAAAC,SAAAX,OAAAU,QAAAE,WAAAZ,OAAAU,QAAAG,QAGA,mBAAAX,sBAAAC,WAAAD,UAAAC,UAAAC,cAAAC,MAAA,mBAAAS,SAAAC,OAAAC,GAAA,SAEA,mBAAAd,sBAAAC,WAAAD,UAAAC,UAAAC,cAAAC,MAAA,uBAsBA,QAAAY,GAAAC,GACA,GAAAnB,GAAAlE,KAAAkE,SASA,IAPAmB,EAAA,IAAAnB,EAAA,SACAlE,KAAAsF,WACApB,EAAA,WACAmB,EAAA,IACAnB,EAAA,WACA,IAAAtE,EAAA2F,SAAAvF,KAAAwF,MAEAtB,EAAA,CAEA,GAAAzD,GAAA,UAAAT,KAAAyF,KACAJ,GAAAK,OAAA,IAAAjF,EAAA,iBAKA,IAAAkF,GAAA,EACAC,EAAA,CACAP,GAAA,GAAA1B,QAAA,uBAAAa,GACA,OAAAA,IACAmB,IACA,OAAAnB,IAGAoB,EAAAD,MAIAN,EAAAK,OAAAE,EAAA,EAAAnF,IAUA,QAAAoF,KAGA,sBAAAhB,UACAA,QAAAgB,KACAC,SAAA3D,UAAA4D,MAAAxF,KAAAsE,QAAAgB,IAAAhB,QAAAmB,WAUA,QAAAC,GAAAC,GACA,IACA,MAAAA,EACAtG,EAAAuG,QAAAC,WAAA,SAEAxG,EAAAuG,QAAAxE,MAAAuE,EAEG,MAAAzC,KAUH,QAAA4C,KACA,GAAAC,EACA,KACAA,EAAA1G,EAAAuG,QAAAxE,MACG,MAAA8B,IAOH,OAJA6C,GAAA,mBAAArC,IAAA,OAAAA,KACAqC,EAAArC,EAAAsC,IAAAC,OAGAF,EAoBA,QAAAG,KACA,IACA,MAAAtC,QAAAuC,aACG,MAAAjD,KA3LH7D,EAAAC,EAAAD,QAAAM,EAAA,GACAN,EAAAiG,MACAjG,EAAAwF,aACAxF,EAAAqG,OACArG,EAAAyG,OACAzG,EAAAsE,YACAtE,EAAAuG,QAAA,mBAAAQ,SACA,mBAAAA,QAAAR,QACAQ,OAAAR,QAAAS,MACAH,IAMA7G,EAAAiH,QACA,sEACA,sEACA,sEACA,sEACA,sEACA,sEACA,sEACA,sEACA,sEACA,sEACA,6DAwCAjH,EAAAkH,WAAAC,EAAA,SAAAC,GACA,IACA,MAAAC,MAAAC,UAAAF,GACG,MAAAG,GACH,qCAAAA,EAAAC,UAqGAxH,EAAAyH,OAAAhB,OL+S8B9F,KAAKX,EAASM,EAAoB,KAI1D,SAAUL,EAAQD,GMzdxB,QAAA0H,KACA,SAAAC,OAAA,mCAEA,QAAAC,KACA,SAAAD,OAAA,qCAsBA,QAAAE,GAAAC,GACA,GAAAC,IAAAC,WAEA,MAAAA,YAAAF,EAAA,EAGA,KAAAC,IAAAL,IAAAK,IAAAC,WAEA,MADAD,GAAAC,WACAA,WAAAF,EAAA,EAEA,KAEA,MAAAC,GAAAD,EAAA,GACK,MAAAjE,GACL,IAEA,MAAAkE,GAAApH,KAAA,KAAAmH,EAAA,GACS,MAAAjE,GAET,MAAAkE,GAAApH,KAAAP,KAAA0H,EAAA,KAMA,QAAAG,GAAAC,GACA,GAAAC,IAAAC,aAEA,MAAAA,cAAAF,EAGA,KAAAC,IAAAP,IAAAO,IAAAC,aAEA,MADAD,GAAAC,aACAA,aAAAF,EAEA,KAEA,MAAAC,GAAAD,GACK,MAAArE,GACL,IAEA,MAAAsE,GAAAxH,KAAA,KAAAuH,GACS,MAAArE,GAGT,MAAAsE,GAAAxH,KAAAP,KAAA8H,KAYA,QAAAG,KACAC,GAAAC,IAGAD,GAAA,EACAC,EAAAvE,OACAwE,EAAAD,EAAAE,OAAAD,GAEAE,GAAA,EAEAF,EAAAxE,QACA2E,KAIA,QAAAA,KACA,IAAAL,EAAA,CAGA,GAAAM,GAAAf,EAAAQ,EACAC,IAAA,CAGA,KADA,GAAAO,GAAAL,EAAAxE,OACA6E,GAAA,CAGA,IAFAN,EAAAC,EACAA,OACAE,EAAAG,GACAN,GACAA,EAAAG,GAAAI,KAGAJ,IAAA,EACAG,EAAAL,EAAAxE,OAEAuE,EAAA,KACAD,GAAA,EACAL,EAAAW,IAiBA,QAAAG,GAAAjB,EAAAkB,GACA5I,KAAA0H,MACA1H,KAAA4I,QAYA,QAAAC,MAhKA,GAOAlB,GACAI,EARA9D,EAAApE,EAAAD,YAgBA,WACA,IAEA+H,EADA,kBAAAC,YACAA,WAEAN,EAEK,MAAA7D,GACLkE,EAAAL,EAEA,IAEAS,EADA,kBAAAC,cACAA,aAEAR,EAEK,MAAA/D,GACLsE,EAAAP,KAuDA,IAEAW,GAFAC,KACAF,GAAA,EAEAI,GAAA,CAyCArE,GAAA6E,SAAA,SAAApB,GACA,GAAArC,GAAA,GAAA0D,OAAA/C,UAAApC,OAAA,EACA,IAAAoC,UAAApC,OAAA,EACA,OAAAE,GAAA,EAAuBA,EAAAkC,UAAApC,OAAsBE,IAC7CuB,EAAAvB,EAAA,GAAAkC,UAAAlC,EAGAsE,GAAAY,KAAA,GAAAL,GAAAjB,EAAArC,IACA,IAAA+C,EAAAxE,QAAAsE,GACAT,EAAAc,IASAI,EAAAxG,UAAAuG,IAAA,WACA1I,KAAA0H,IAAA3B,MAAA,KAAA/F,KAAA4I,QAEA3E,EAAAgF,MAAA,UACAhF,EAAAiF,SAAA,EACAjF,EAAAsC,OACAtC,EAAAkF,QACAlF,EAAAmF,QAAA,GACAnF,EAAAoF,YAIApF,EAAAqF,GAAAT,EACA5E,EAAAsF,YAAAV,EACA5E,EAAAuF,KAAAX,EACA5E,EAAAwF,IAAAZ,EACA5E,EAAAyF,eAAAb,EACA5E,EAAA0F,mBAAAd,EACA5E,EAAA2F,KAAAf,EACA5E,EAAA4F,gBAAAhB,EACA5E,EAAA6F,oBAAAjB,EAEA5E,EAAA8F,UAAA,SAAAC,GAAqC,UAErC/F,EAAAgG,QAAA,SAAAD,GACA,SAAAzC,OAAA,qCAGAtD,EAAAiG,IAAA,WAA2B,WAC3BjG,EAAAkG,MAAA,SAAAC,GACA,SAAA7C,OAAA,mCAEAtD,EAAAoG,MAAA,WAA4B,WN2etB,SAAUxK,EAAQD,EAASM,GOxnBjC,QAAAoK,GAAAhF,GACA,GAAAxB,GAAAyG,EAAA,CAEA,KAAAzG,IAAAwB,GACAiF,MAAA,GAAAA,EAAAjF,EAAAkF,WAAA1G,GACAyG,GAAA,CAGA,OAAA3K,GAAAiH,OAAA4D,KAAAC,IAAAH,GAAA3K,EAAAiH,OAAAjD,QAWA,QAAA+G,GAAArF,GAIA,QAAA3D,KAEA,GAAAA,EAAAiJ,QAAA,CAEA,GAAAC,GAAAlJ,EAGAmJ,GAAA,GAAAC,MACAC,EAAAF,GAAAG,GAAAH,EACAD,GAAArF,KAAAwF,EACAH,EAAAK,KAAAD,EACAJ,EAAAC,OACAG,EAAAH,CAIA,QADAzF,GAAA,GAAA0D,OAAA/C,UAAApC,QACAE,EAAA,EAAmBA,EAAAuB,EAAAzB,OAAiBE,IACpCuB,EAAAvB,GAAAkC,UAAAlC,EAGAuB,GAAA,GAAAzF,EAAAuL,OAAA9F,EAAA,IAEA,gBAAAA,GAAA,IAEAA,EAAA+F,QAAA,KAIA,IAAAzF,GAAA,CACAN,GAAA,GAAAA,EAAA,GAAA1B,QAAA,yBAAAa,EAAA6G,GAEA,UAAA7G,EAAA,MAAAA,EACAmB,IACA,IAAA2F,GAAA1L,EAAAkH,WAAAuE,EACA,sBAAAC,GAAA,CACA,GAAAC,GAAAlG,EAAAM,EACAnB,GAAA8G,EAAA/K,KAAAsK,EAAAU,GAGAlG,EAAAK,OAAAC,EAAA,GACAA,IAEA,MAAAnB,KAIA5E,EAAAwF,WAAA7E,KAAAsK,EAAAxF,EAEA,IAAAmG,GAAA7J,EAAAkE,KAAAjG,EAAAiG,KAAAhB,QAAAgB,IAAA4F,KAAA5G,QACA2G,GAAAzF,MAAA8E,EAAAxF,IAnDA,GAAA4F,EAmEA,OAbAtJ,GAAA2D,YACA3D,EAAAiJ,QAAAhL,EAAAgL,QAAAtF,GACA3D,EAAAuC,UAAAtE,EAAAsE,YACAvC,EAAA8D,MAAA6E,EAAAhF,GACA3D,EAAA+J,UAGA,kBAAA9L,GAAA+L,MACA/L,EAAA+L,KAAAhK,GAGA/B,EAAAgM,UAAA5C,KAAArH,GAEAA,EAGA,QAAA+J,KACA,GAAA/F,GAAA/F,EAAAgM,UAAA1I,QAAAlD,KACA,OAAA2F,MAAA,IACA/F,EAAAgM,UAAAlG,OAAAC,EAAA,IACA,GAcA,QAAA0B,GAAAnB,GACAtG,EAAAqG,KAAAC,GAEAtG,EAAAiM,SACAjM,EAAAkM,QAEA,IAAAhI,GACAiI,GAAA,gBAAA7F,KAAA,IAAA6F,MAAA,UACAtD,EAAAsD,EAAAnI,MAEA,KAAAE,EAAA,EAAaA,EAAA2E,EAAS3E,IACtBiI,EAAAjI,KACAoC,EAAA6F,EAAAjI,GAAAH,QAAA,aACA,MAAAuC,EAAA,GACAtG,EAAAkM,MAAA9C,KAAA,GAAA9D,QAAA,IAAAgB,EAAA8F,OAAA,SAEApM,EAAAiM,MAAA7C,KAAA,GAAA9D,QAAA,IAAAgB,EAAA,MAIA,KAAApC,EAAA,EAAaA,EAAAlE,EAAAgM,UAAAhI,OAA8BE,IAAA,CAC3C,GAAAmI,GAAArM,EAAAgM,UAAA9H,EACAmI,GAAArB,QAAAhL,EAAAgL,QAAAqB,EAAA3G,YAUA,QAAA4G,KACAtM,EAAAyH,OAAA,IAWA,QAAAuD,GAAAZ,GACA,SAAAA,IAAApG,OAAA,GACA,QAEA,IAAAE,GAAA2E,CACA,KAAA3E,EAAA,EAAA2E,EAAA7I,EAAAkM,MAAAlI,OAAyCE,EAAA2E,EAAS3E,IAClD,GAAAlE,EAAAkM,MAAAhI,GAAAhB,KAAAkH,GACA,QAGA,KAAAlG,EAAA,EAAA2E,EAAA7I,EAAAiM,MAAAjI,OAAyCE,EAAA2E,EAAS3E,IAClD,GAAAlE,EAAAiM,MAAA/H,GAAAhB,KAAAkH,GACA,QAGA,UAWA,QAAAmB,GAAAI,GACA,MAAAA,aAAAhE,OAAAgE,EAAAY,OAAAZ,EAAAnE,QACAmE,EAvNA3L,EAAAC,EAAAD,QAAA+K,EAAAhJ,MAAAgJ,EAAA,WAAAA,EACA/K,EAAAuL,SACAvL,EAAAsM,UACAtM,EAAAyH,SACAzH,EAAAgL,UACAhL,EAAA2F,SAAArF,EAAA,GAKAN,EAAAgM,aAMAhM,EAAAiM,SACAjM,EAAAkM,SAQAlM,EAAAkH,ePw2BM,SAAUjH,EAAQD,GQ31BxB,QAAAwM,GAAA9I,GAEA,GADAA,EAAA+I,OAAA/I,KACAA,EAAAM,OAAA,MAGA,GAAAY,GAAA,wHAAAX,KACAP,EAEA,IAAAkB,EAAA,CAGA,GAAA8H,GAAAC,WAAA/H,EAAA,IACAJ,GAAAI,EAAA,UAAAD,aACA,QAAAH,GACA,YACA,WACA,UACA,SACA,QACA,MAAAkI,GAAAE,CACA,YACA,UACA,QACA,MAAAF,GAAAG,CACA,aACA,WACA,UACA,SACA,QACA,MAAAH,GAAAI,CACA,eACA,aACA,WACA,UACA,QACA,MAAAJ,GAAA9L,CACA,eACA,aACA,WACA,UACA,QACA,MAAA8L,GAAAK,CACA,oBACA,kBACA,YACA,WACA,SACA,MAAAL,EACA,SACA,UAYA,QAAAM,GAAA5B,GACA,MAAAA,IAAAyB,EACAhC,KAAAoC,MAAA7B,EAAAyB,GAAA,IAEAzB,GAAA0B,EACAjC,KAAAoC,MAAA7B,EAAA0B,GAAA,IAEA1B,GAAAxK,EACAiK,KAAAoC,MAAA7B,EAAAxK,GAAA,IAEAwK,GAAA2B,EACAlC,KAAAoC,MAAA7B,EAAA2B,GAAA,IAEA3B,EAAA,KAWA,QAAA8B,GAAA9B,GACA,MAAA+B,GAAA/B,EAAAyB,EAAA,QACAM,EAAA/B,EAAA0B,EAAA,SACAK,EAAA/B,EAAAxK,EAAA,WACAuM,EAAA/B,EAAA2B,EAAA,WACA3B,EAAA,MAOA,QAAA+B,GAAA/B,EAAAsB,EAAAtC,GACA,KAAAgB,EAAAsB,GAGA,MAAAtB,GAAA,IAAAsB,EACA7B,KAAAuC,MAAAhC,EAAAsB,GAAA,IAAAtC,EAEAS,KAAAwC,KAAAjC,EAAAsB,GAAA,IAAAtC,EAAA,IAlJA,GAAA2C,GAAA,IACAnM,EAAA,GAAAmM,EACAD,EAAA,GAAAlM,EACAiM,EAAA,GAAAC,EACAF,EAAA,OAAAC,CAgBA5M,GAAAD,QAAA,SAAA2L,EAAA2B,GACAA,OACA,IAAA9I,SAAAmH,EACA,eAAAnH,GAAAmH,EAAA3H,OAAA,EACA,MAAAwI,GAAAb,EACG,eAAAnH,GAAA+I,MAAA5B,MAAA,EACH,MAAA2B,WAAAJ,EAAAvB,GAAAqB,EAAArB,EAEA,UAAAhE,OACA,wDACAN,KAAAC,UAAAqE,MRqgCM,SAAU1L,EAAQD,EAASM,GSt7BjC,QAAAkN,MAiCA,QAAAC,GAAApL,GAGA,GAAAqB,GAAA,GAAArB,EAAAmC,IAmBA,IAhBAxE,EAAA0N,eAAArL,EAAAmC,MAAAxE,EAAA2N,aAAAtL,EAAAmC,OACAd,GAAArB,EAAAuL,YAAA,KAKAvL,EAAAwL,KAAA,MAAAxL,EAAAwL,MACAnK,GAAArB,EAAAwL,IAAA,KAIA,MAAAxL,EAAA5B,KACAiD,GAAArB,EAAA5B,IAIA,MAAA4B,EAAAyL,KAAA,CACA,GAAAC,GAAAC,EAAA3L,EAAAyL,KACA,IAAAC,KAAA,EAGA,MAAAE,EAFAvK,IAAAqK,EAOA,MADAhM,GAAA,mBAAAM,EAAAqB,GACAA,EAGA,QAAAsK,GAAAtK,GACA,IACA,MAAA2D,MAAAC,UAAA5D,GACG,MAAAG,GACH,UAcA,QAAAqK,GAAA7L,EAAA8L,GAEA,QAAAC,GAAAC,GACA,GAAAC,GAAAC,EAAAC,kBAAAH,GACAI,EAAAhB,EAAAa,EAAAI,QACAC,EAAAL,EAAAK,OAEAA,GAAAnD,QAAAiD,GACAN,EAAAQ,GAGAJ,EAAAK,YAAAvM,EAAA+L,GAUA,QAAAS,KACAzO,KAAA0O,cAAA,KAwDA,QAAAC,GAAArL,GACA,GAAAQ,GAAA,EAEApD,GACA0D,KAAAwK,OAAAtL,EAAAT,OAAA,IAGA,UAAAjD,EAAAiP,MAAAnO,EAAA0D,MACA,MAAA0K,GAAA,uBAAApO,EAAA0D,KAIA,IAAAxE,EAAA0N,eAAA5M,EAAA0D,MAAAxE,EAAA2N,aAAA7M,EAAA0D,KAAA,CAEA,IADA,GAAA2K,GAAA,GACA,MAAAzL,EAAAT,SAAAiB,KACAiL,GAAAzL,EAAAT,OAAAiB,GACAA,GAAAR,EAAAM,UAEA,GAAAmL,GAAAH,OAAAG,IAAA,MAAAzL,EAAAT,OAAAiB,GACA,SAAAyD,OAAA,sBAEA7G,GAAA8M,YAAAoB,OAAAG,GAIA,SAAAzL,EAAAT,OAAAiB,EAAA,GAEA,IADApD,EAAA+M,IAAA,KACA3J,GAAA,CACA,GAAArD,GAAA6C,EAAAT,OAAAiB,EACA,UAAArD,EAAA,KAEA,IADAC,EAAA+M,KAAAhN,EACAqD,IAAAR,EAAAM,OAAA,UAGAlD,GAAA+M,IAAA,GAIA,IAAAuB,GAAA1L,EAAAT,OAAAiB,EAAA,EACA,SAAAkL,GAAAJ,OAAAI,MAAA,CAEA,IADAtO,EAAAL,GAAA,KACAyD,GAAA,CACA,GAAArD,GAAA6C,EAAAT,OAAAiB,EACA,UAAArD,GAAAmO,OAAAnO,MAAA,GACAqD,CACA,OAGA,GADApD,EAAAL,IAAAiD,EAAAT,OAAAiB,GACAA,IAAAR,EAAAM,OAAA,MAEAlD,EAAAL,GAAAuO,OAAAlO,EAAAL,IAIA,GAAAiD,EAAAT,SAAAiB,GAAA,CACA,GAAA6J,GAAAsB,EAAA3L,EAAA0I,OAAAlI,IACAoL,EAAAvB,KAAA,IAAAjN,EAAA0D,OAAAxE,EAAAuP,OAAAC,EAAAzB,GACA,KAAAuB,EAGA,MAAAJ,GAAA,kBAFApO,GAAAgN,KAAAC,EAOA,MADAhM,GAAA,mBAAA2B,EAAA5C,GACAA,EAGA,QAAAuO,GAAA3L,GACA,IACA,MAAA2D,MAAAmF,MAAA9I,GACG,MAAAG,GACH,UA0BA,QAAA4L,GAAAf,GACAtO,KAAAsP,UAAAhB,EACAtO,KAAAuO,WAkCA,QAAAO,GAAAS,GACA,OACAnL,KAAAxE,EAAAuP,MACAzB,KAAA,iBAAA6B,GAzZA,GAAA5N,GAAAzB,EAAA,uBACAsP,EAAAtP,EAAA,GACAiO,EAAAjO,EAAA,GACAkP,EAAAlP,EAAA,IACAuP,EAAAvP,EAAA,GAQAN,GAAA0C,SAAA,EAQA1C,EAAAiP,OACA,UACA,aACA,QACA,MACA,QACA,eACA,cASAjP,EAAA8P,QAAA,EAQA9P,EAAA+P,WAAA,EAQA/P,EAAAgQ,MAAA,EAQAhQ,EAAAiQ,IAAA,EAQAjQ,EAAAuP,MAAA,EAQAvP,EAAA0N,aAAA,EAQA1N,EAAA2N,WAAA,EAQA3N,EAAAwN,UAQAxN,EAAA6O,SAUA,IAAAZ,GAAAjO,EAAAuP,MAAA,gBAYA/B,GAAAjL,UAAA2N,OAAA,SAAA7N,EAAA8L,GAGA,GAFApM,EAAA,qBAAAM,GAEArC,EAAA0N,eAAArL,EAAAmC,MAAAxE,EAAA2N,aAAAtL,EAAAmC,KACA0J,EAAA7L,EAAA8L,OACG,CACH,GAAAgC,GAAA1C,EAAApL,EACA8L,IAAAgC,MA8FAP,EAAAf,EAAAtM,WAUAsM,EAAAtM,UAAA6N,IAAA,SAAA/N,GACA,GAAAqM,EACA,oBAAArM,GACAqM,EAAAK,EAAA1M,GACArC,EAAA0N,eAAAgB,EAAAlK,MAAAxE,EAAA2N,aAAAe,EAAAlK,MACApE,KAAA0O,cAAA,GAAAW,GAAAf,GAGA,IAAAtO,KAAA0O,cAAAY,UAAA9B,aACAxN,KAAA4J,KAAA,UAAA0E,IAGAtO,KAAA4J,KAAA,UAAA0E,OAGA,KAAAmB,EAAAxN,OAAAgO,OAYA,SAAA1I,OAAA,iBAAAtF,EAXA,KAAAjC,KAAA0O,cACA,SAAAnH,OAAA,mDAEA+G,GAAAtO,KAAA0O,cAAAwB,eAAAjO,GACAqM,IACAtO,KAAA0O,cAAA,KACA1O,KAAA4J,KAAA,UAAA0E,MAmGAG,EAAAtM,UAAAuJ,QAAA,WACA1L,KAAA0O,eACA1O,KAAA0O,cAAAyB,0BA6BAd,EAAAlN,UAAA+N,eAAA,SAAAE,GAEA,GADApQ,KAAAuO,QAAAvF,KAAAoH,GACApQ,KAAAuO,QAAA3K,SAAA5D,KAAAsP,UAAA9B,YAAA,CACA,GAAAc,GAAAH,EAAAkC,kBAAArQ,KAAAsP,UAAAtP,KAAAuO,QAEA,OADAvO,MAAAmQ,yBACA7B,EAEA,aASAe,EAAAlN,UAAAgO,uBAAA,WACAnQ,KAAAsP,UAAA,KACAtP,KAAAuO,aTsjCM,SAAU1O,EAAQD,EAASM,GU/7CjC,QAAAsP,GAAAvN,GACA,GAAAA,EAAA,MAAAqO,GAAArO,GAWA,QAAAqO,GAAArO,GACA,OAAAsO,KAAAf,GAAArN,UACAF,EAAAsO,GAAAf,EAAArN,UAAAoO,EAEA,OAAAtO,GAzBApC,EAAAD,QAAA4P,EAqCAA,EAAArN,UAAAmH,GACAkG,EAAArN,UAAAqO,iBAAA,SAAAC,EAAAC,GAIA,MAHA1Q,MAAA2Q,WAAA3Q,KAAA2Q,gBACA3Q,KAAA2Q,WAAA,IAAAF,GAAAzQ,KAAA2Q,WAAA,IAAAF,QACAzH,KAAA0H,GACA1Q,MAaAwP,EAAArN,UAAAqH,KAAA,SAAAiH,EAAAC,GACA,QAAApH,KACAtJ,KAAAyJ,IAAAgH,EAAAnH,GACAoH,EAAA3K,MAAA/F,KAAAgG,WAKA,MAFAsD,GAAAoH,KACA1Q,KAAAsJ,GAAAmH,EAAAnH,GACAtJ,MAaAwP,EAAArN,UAAAsH,IACA+F,EAAArN,UAAAuH,eACA8F,EAAArN,UAAAwH,mBACA6F,EAAArN,UAAAyO,oBAAA,SAAAH,EAAAC,GAIA,GAHA1Q,KAAA2Q,WAAA3Q,KAAA2Q,eAGA,GAAA3K,UAAApC,OAEA,MADA5D,MAAA2Q,cACA3Q,IAIA,IAAA6Q,GAAA7Q,KAAA2Q,WAAA,IAAAF,EACA,KAAAI,EAAA,MAAA7Q,KAGA,OAAAgG,UAAApC,OAEA,aADA5D,MAAA2Q,WAAA,IAAAF,GACAzQ,IAKA,QADA8Q,GACAhN,EAAA,EAAiBA,EAAA+M,EAAAjN,OAAsBE,IAEvC,GADAgN,EAAAD,EAAA/M,GACAgN,IAAAJ,GAAAI,EAAAJ,OAAA,CACAG,EAAAnL,OAAA5B,EAAA,EACA,OAGA,MAAA9D,OAWAwP,EAAArN,UAAAyH,KAAA,SAAA6G,GACAzQ,KAAA2Q,WAAA3Q,KAAA2Q,cACA,IAAAtL,MAAA0L,MAAAxQ,KAAAyF,UAAA,GACA6K,EAAA7Q,KAAA2Q,WAAA,IAAAF,EAEA,IAAAI,EAAA,CACAA,IAAAE,MAAA,EACA,QAAAjN,GAAA,EAAA2E,EAAAoI,EAAAjN,OAA2CE,EAAA2E,IAAS3E,EACpD+M,EAAA/M,GAAAiC,MAAA/F,KAAAqF,GAIA,MAAArF,OAWAwP,EAAArN,UAAA4H,UAAA,SAAA0G,GAEA,MADAzQ,MAAA2Q,WAAA3Q,KAAA2Q,eACA3Q,KAAA2Q,WAAA,IAAAF,QAWAjB,EAAArN,UAAA6O,aAAA,SAAAP,GACA,QAAAzQ,KAAA+J,UAAA0G,GAAA7M,SVs9CM,SAAU/D,EAAQD,EAASM,IWvnDjC,SAAAuC,GA+BA,QAAAwO,GAAAvD,EAAAa,GACA,IAAAb,EAAA,MAAAA,EAEA,IAAA+B,EAAA/B,GAAA,CACA,GAAAwD,IAAuBC,cAAA,EAAAC,IAAA7C,EAAA3K,OAEvB,OADA2K,GAAAvF,KAAA0E,GACAwD,EACG,GAAA9B,EAAA1B,GAAA,CAEH,OADA2D,GAAA,GAAAtI,OAAA2E,EAAA9J,QACAE,EAAA,EAAmBA,EAAA4J,EAAA9J,OAAiBE,IACpCuN,EAAAvN,GAAAmN,EAAAvD,EAAA5J,GAAAyK,EAEA,OAAA8C,GACG,mBAAA3D,kBAAA3C,OAAA,CACH,GAAAsG,KACA,QAAAd,KAAA7C,GACA2D,EAAAd,GAAAU,EAAAvD,EAAA6C,GAAAhC,EAEA,OAAA8C,GAEA,MAAA3D,GAkBA,QAAA4D,GAAA5D,EAAAa,GACA,IAAAb,EAAA,MAAAA,EAEA,IAAAA,KAAAyD,aACA,MAAA5C,GAAAb,EAAA0D,IACG,IAAAhC,EAAA1B,GACH,OAAA5J,GAAA,EAAmBA,EAAA4J,EAAA9J,OAAiBE,IACpC4J,EAAA5J,GAAAwN,EAAA5D,EAAA5J,GAAAyK,OAEG,oBAAAb,GACH,OAAA6C,KAAA7C,GACAA,EAAA6C,GAAAe,EAAA5D,EAAA6C,GAAAhC,EAIA,OAAAb,GA9EA,GAAA0B,GAAAlP,EAAA,IACAuP,EAAAvP,EAAA,IACAqR,EAAAC,OAAArP,UAAAoP,SACAE,EAAA,kBAAAhP,GAAAiP,MAAA,6BAAAH,EAAAhR,KAAAkC,EAAAiP,MACAC,EAAA,kBAAAlP,GAAAmP,MAAA,6BAAAL,EAAAhR,KAAAkC,EAAAmP,KAYAhS,GAAAwO,kBAAA,SAAAE,GACA,GAAAC,MACAsD,EAAAvD,EAAAZ,KACAW,EAAAC,CAGA,OAFAD,GAAAX,KAAAuD,EAAAY,EAAAtD,GACAF,EAAAb,YAAAe,EAAA3K,QACU0K,OAAAD,EAAAE,YAmCV3O,EAAAyQ,kBAAA,SAAA/B,EAAAC,GAGA,MAFAD,GAAAZ,KAAA4D,EAAAhD,EAAAZ,KAAAa,GACAD,EAAAd,YAAAzM,OACAuN,GA+BA1O,EAAA4O,YAAA,SAAAd,EAAAK,GACA,QAAA+D,GAAA7P,EAAA8P,EAAAC,GACA,IAAA/P,EAAA,MAAAA,EAGA,IAAAwP,GAAAxP,YAAAyP,OACAC,GAAA1P,YAAA2P,MAAA,CACAK,GAGA,IAAAC,GAAA,GAAAC,WACAD,GAAAE,OAAA,WACAJ,EACAA,EAAAD,GAAA/R,KAAAqS,OAGApE,EAAAjO,KAAAqS,SAIAJ,GACAlE,EAAAE,IAIAiE,EAAAI,kBAAArQ,OACK,IAAAmN,EAAAnN,GACL,OAAA6B,GAAA,EAAqBA,EAAA7B,EAAA2B,OAAgBE,IACrCgO,EAAA7P,EAAA6B,KAAA7B,OAEK,oBAAAA,KAAAwN,EAAAxN,GACL,OAAAsO,KAAAtO,GACA6P,EAAA7P,EAAAsO,KAAAtO,GAKA,GAAAgQ,GAAA,EACAhE,EAAAP,CACAoE,GAAA7D,GACAgE,GACAlE,EAAAE,MX6nD8B1N,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,GY3wDxB,GAAA2R,MAAiBA,QAEjB1R,GAAAD,QAAAmJ,MAAAqG,SAAA,SAAAmD,GACA,wBAAAhB,EAAAhR,KAAAgS,KZmxDM,SAAU1S,EAAQD,IAEK,SAAS6C,GapwDtC,QAAAgN,GAAAxN,GACA,MAAAuQ,IAAA/P,EAAAgQ,OAAAC,SAAAzQ,IACA0Q,IAAA1Q,YAAAQ,GAAAmQ,aAAAC,EAAA5Q,IArBApC,EAAAD,QAAA6P,CAEA,IAAA+C,GAAA,kBAAA/P,GAAAgQ,QAAA,kBAAAhQ,GAAAgQ,OAAAC,SACAC,EAAA,kBAAAlQ,GAAAmQ,YAEAC,EAAA,WACA,MAAAF,IAAA,kBAAAlQ,GAAAmQ,YAAAC,OACApQ,EAAAmQ,YAAAC,OAEA,SAAA5Q,GAA2B,MAAAA,GAAA6Q,iBAAArQ,GAAAmQ,kBbuyDGrS,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,GAEhC,YcpxDD,SAAS0B,GAAShB,EAAKC,GACrB,KAAMb,eAAgB4B,IAAU,MAAO,IAAIA,GAAQhB,EAAKC,EACpDD,IAAQ,+BAAoBA,GAApB,YAAAE,EAAoBF,MAC9BC,EAAOD,EACPA,EAAMG,QAERF,EAAOA,MAEPA,EAAKO,KAAOP,EAAKO,MAAQ,aACzBpB,KAAKuB,QACLvB,KAAK+S,QACL/S,KAAKa,KAAOA,EACZb,KAAKgT,aAAanS,EAAKmS,gBAAiB,GACxChT,KAAKiT,qBAAqBpS,EAAKoS,sBAAwBC,KACvDlT,KAAKmT,kBAAkBtS,EAAKsS,mBAAqB,KACjDnT,KAAKoT,qBAAqBvS,EAAKuS,sBAAwB,KACvDpT,KAAKqT,oBAAoBxS,EAAKwS,qBAAuB,IACrDrT,KAAKsT,QAAU,GAAIC,IACjBC,IAAKxT,KAAKmT,oBACVM,IAAKzT,KAAKoT,uBACVM,OAAQ1T,KAAKqT,wBAEfrT,KAAKwI,QAAQ,MAAQ3H,EAAK2H,QAAU,IAAQ3H,EAAK2H,SACjDxI,KAAK2T,WAAa,SAClB3T,KAAKY,IAAMA,EACXZ,KAAK4T,cACL5T,KAAK6T,SAAW,KAChB7T,KAAK+P,UAAW,EAChB/P,KAAK8T,eACL,IAAIC,GAAUlT,EAAKuB,QAAUA,CAC7BpC,MAAKgU,QAAU,GAAID,GAAQ3G,QAC3BpN,KAAKiU,QAAU,GAAIF,GAAQtF,QAC3BzO,KAAKkU,YAAcrT,EAAKqT,eAAgB,EACpClU,KAAKkU,aAAalU,KAAKmU,OdqvD5B,GAAIrT,GAA4B,kBAAXiB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,IcpzDnQmS,EAAMlU,EAAQ,IACdsC,EAAStC,EAAQ,IACjBsP,EAAUtP,EAAQ,GAClBkC,EAASlC,EAAQ,GACjBoJ,EAAKpJ,EAAQ,IACbuL,EAAOvL,EAAQ,IACfyB,EAAQzB,EAAQ,GAAS,4BACzBgD,EAAUhD,EAAQ,IAClBqT,EAAUrT,EAAQ,IAMlBmU,EAAM7C,OAAOrP,UAAUmS,cAM3BzU,GAAOD,QAAUgC,EAoDjBA,EAAQO,UAAUoS,QAAU,WAC1BvU,KAAK4J,KAAK7D,MAAM/F,KAAMgG,UACtB,KAAK,GAAIyH,KAAOzN,MAAKuB,KACf8S,EAAI9T,KAAKP,KAAKuB,KAAMkM,IACtBzN,KAAKuB,KAAKkM,GAAK7D,KAAK7D,MAAM/F,KAAKuB,KAAKkM,GAAMzH,YAWhDpE,EAAQO,UAAUqS,gBAAkB,WAClC,IAAK,GAAI/G,KAAOzN,MAAKuB,KACf8S,EAAI9T,KAAKP,KAAKuB,KAAMkM,KACtBzN,KAAKuB,KAAKkM,GAAKpN,GAAKL,KAAKyU,WAAWhH,KAa1C7L,EAAQO,UAAUsS,WAAa,SAAUhH,GACvC,OAAgB,MAARA,EAAc,GAAMA,EAAM,KAAQzN,KAAK0U,OAAOrU,IAOxDmP,EAAQ5N,EAAQO,WAUhBP,EAAQO,UAAU6Q,aAAe,SAAUhM,GACzC,MAAKhB,WAAUpC,QACf5D,KAAK2U,gBAAkB3N,EAChBhH,MAFuBA,KAAK2U,eAarC/S,EAAQO,UAAU8Q,qBAAuB,SAAUjM,GACjD,MAAKhB,WAAUpC,QACf5D,KAAK4U,sBAAwB5N,EACtBhH,MAFuBA,KAAK4U,uBAarChT,EAAQO,UAAUgR,kBAAoB,SAAUnM,GAC9C,MAAKhB,WAAUpC,QACf5D,KAAK6U,mBAAqB7N,EAC1BhH,KAAKsT,SAAWtT,KAAKsT,QAAQwB,OAAO9N,GAC7BhH,MAHuBA,KAAK6U,oBAMrCjT,EAAQO,UAAUkR,oBAAsB,SAAUrM,GAChD,MAAKhB,WAAUpC,QACf5D,KAAK+U,qBAAuB/N,EAC5BhH,KAAKsT,SAAWtT,KAAKsT,QAAQ0B,UAAUhO,GAChChH,MAHuBA,KAAK+U,sBAcrCnT,EAAQO,UAAUiR,qBAAuB,SAAUpM,GACjD,MAAKhB,WAAUpC,QACf5D,KAAKiV,sBAAwBjO,EAC7BhH,KAAKsT,SAAWtT,KAAKsT,QAAQ4B,OAAOlO,GAC7BhH,MAHuBA,KAAKiV,uBAarCrT,EAAQO,UAAUqG,QAAU,SAAUxB,GACpC,MAAKhB,WAAUpC,QACf5D,KAAKmV,SAAWnO,EACThH,MAFuBA,KAAKmV,UAYrCvT,EAAQO,UAAUiT,qBAAuB,YAElCpV,KAAKqV,cAAgBrV,KAAK2U,eAA2C,IAA1B3U,KAAKsT,QAAQgC,UAE3DtV,KAAKuV,aAYT3T,EAAQO,UAAUgS,KAClBvS,EAAQO,UAAUI,QAAU,SAAUmO,EAAI7P,GAExC,GADAc,EAAM,gBAAiB3B,KAAK2T,aACvB3T,KAAK2T,WAAWzQ,QAAQ,QAAS,MAAOlD,KAE7C2B,GAAM,aAAc3B,KAAKY,KACzBZ,KAAK0U,OAASN,EAAIpU,KAAKY,IAAKZ,KAAKa,KACjC,IAAIiB,GAAS9B,KAAK0U,OACd7J,EAAO7K,IACXA,MAAK2T,WAAa,UAClB3T,KAAKwV,eAAgB,CAGrB,IAAIC,GAAUnM,EAAGxH,EAAQ,OAAQ,WAC/B+I,EAAK6K,SACLhF,GAAMA,MAIJiF,EAAWrM,EAAGxH,EAAQ,QAAS,SAAU4L,GAK3C,GAJA/L,EAAM,iBACNkJ,EAAK+K,UACL/K,EAAK8I,WAAa,SAClB9I,EAAK0J,QAAQ,gBAAiB7G,GAC1BgD,EAAI,CACN,GAAIvJ,GAAM,GAAII,OAAM,mBACpBJ,GAAIuG,KAAOA,EACXgD,EAAGvJ,OAGH0D,GAAKuK,wBAKT,KAAI,IAAUpV,KAAKmV,SAAU,CAC3B,GAAI3M,GAAUxI,KAAKmV,QACnBxT,GAAM,wCAAyC6G,EAG/C,IAAIqN,GAAQjO,WAAW,WACrBjG,EAAM,qCAAsC6G,GAC5CiN,EAAQ/J,UACR5J,EAAOgU,QACPhU,EAAO8H,KAAK,QAAS,WACrBiB,EAAK0J,QAAQ,kBAAmB/L,IAC/BA,EAEHxI,MAAK+S,KAAK/J,MACR0C,QAAS,WACP1D,aAAa6N,MAQnB,MAHA7V,MAAK+S,KAAK/J,KAAKyM,GACfzV,KAAK+S,KAAK/J,KAAK2M,GAER3V,MAST4B,EAAQO,UAAUuT,OAAS,WACzB/T,EAAM,QAGN3B,KAAK4V,UAGL5V,KAAK2T,WAAa,OAClB3T,KAAK4J,KAAK,OAGV,IAAI9H,GAAS9B,KAAK0U,MAClB1U,MAAK+S,KAAK/J,KAAKM,EAAGxH,EAAQ,OAAQ2J,EAAKzL,KAAM,YAC7CA,KAAK+S,KAAK/J,KAAKM,EAAGxH,EAAQ,OAAQ2J,EAAKzL,KAAM,YAC7CA,KAAK+S,KAAK/J,KAAKM,EAAGxH,EAAQ,OAAQ2J,EAAKzL,KAAM,YAC7CA,KAAK+S,KAAK/J,KAAKM,EAAGxH,EAAQ,QAAS2J,EAAKzL,KAAM,aAC9CA,KAAK+S,KAAK/J,KAAKM,EAAGxH,EAAQ,QAAS2J,EAAKzL,KAAM,aAC9CA,KAAK+S,KAAK/J,KAAKM,EAAGtJ,KAAKiU,QAAS,UAAWxI,EAAKzL,KAAM,gBASxD4B,EAAQO,UAAU4T,OAAS,WACzB/V,KAAK6T,SAAW,GAAI9I,MACpB/K,KAAKuU,QAAQ,SASf3S,EAAQO,UAAU6T,OAAS,WACzBhW,KAAKuU,QAAQ,OAAQ,GAAIxJ,MAAS/K,KAAK6T,WASzCjS,EAAQO,UAAU8T,OAAS,SAAUvI,GACnC1N,KAAKiU,QAAQjE,IAAItC,IASnB9L,EAAQO,UAAU+T,UAAY,SAAU5H,GACtCtO,KAAK4J,KAAK,SAAU0E,IAStB1M,EAAQO,UAAUgU,QAAU,SAAUhP,GACpCxF,EAAM,QAASwF,GACfnH,KAAKuU,QAAQ,QAASpN,IAUxBvF,EAAQO,UAAUL,OAAS,SAAU2L,EAAK5M,GAiBxC,QAASuV,MACDlT,EAAQ2H,EAAK+I,WAAY9R,IAC7B+I,EAAK+I,WAAW5K,KAAKlH,GAlBzB,GAAIA,GAAS9B,KAAKuB,KAAKkM,EACvB,KAAK3L,EAAQ,CACXA,EAAS,GAAIU,GAAOxC,KAAMyN,EAAK5M,GAC/Bb,KAAKuB,KAAKkM,GAAO3L,CACjB,IAAI+I,GAAO7K,IACX8B,GAAOwH,GAAG,aAAc8M,GACxBtU,EAAOwH,GAAG,UAAW,WACnBxH,EAAOzB,GAAKwK,EAAK4J,WAAWhH,KAG1BzN,KAAKkU,aAEPkC,IAUJ,MAAOtU,IASTF,EAAQO,UAAUuJ,QAAU,SAAU5J,GACpC,GAAI6D,GAAQzC,EAAQlD,KAAK4T,WAAY9R,IAChC6D,GAAO3F,KAAK4T,WAAWlO,OAAOC,EAAO,GACtC3F,KAAK4T,WAAWhQ,QAEpB5D,KAAK8V,SAUPlU,EAAQO,UAAUmM,OAAS,SAAUA,GACnC3M,EAAM,oBAAqB2M,EAC3B,IAAIzD,GAAO7K,IACPsO,GAAOzM,OAAyB,IAAhByM,EAAOlK,OAAYkK,EAAOb,KAAO,IAAMa,EAAOzM,OAE7DgJ,EAAKkF,SAWRlF,EAAKiJ,aAAa9K,KAAKsF,IATvBzD,EAAKkF,UAAW,EAChB/P,KAAKgU,QAAQlE,OAAOxB,EAAQ,SAAU+H,GACpC,IAAK,GAAIvS,GAAI,EAAGA,EAAIuS,EAAezS,OAAQE,IACzC+G,EAAK6J,OAAO4B,MAAMD,EAAevS,GAAIwK,EAAOpB,QAE9CrC,GAAKkF,UAAW,EAChBlF,EAAK0L,yBAcX3U,EAAQO,UAAUoU,mBAAqB,WACrC,GAAIvW,KAAK8T,aAAalQ,OAAS,IAAM5D,KAAK+P,SAAU,CAClD,GAAI1B,GAAOrO,KAAK8T,aAAa0C,OAC7BxW,MAAKsO,OAAOD,KAUhBzM,EAAQO,UAAUyT,QAAU,WAC1BjU,EAAM,UAGN,KAAK,GADD8U,GAAazW,KAAK+S,KAAKnP,OAClBE,EAAI,EAAGA,EAAI2S,EAAY3S,IAAK,CACnC,GAAI4S,GAAM1W,KAAK+S,KAAKyD,OACpBE,GAAIhL,UAGN1L,KAAK8T,gBACL9T,KAAK+P,UAAW,EAChB/P,KAAK6T,SAAW,KAEhB7T,KAAKiU,QAAQvI,WASf9J,EAAQO,UAAU2T,MAClBlU,EAAQO,UAAUwU,WAAa,WAC7BhV,EAAM,cACN3B,KAAKwV,eAAgB,EACrBxV,KAAKqV,cAAe,EAChB,YAAcrV,KAAK2T,YAGrB3T,KAAK4V,UAEP5V,KAAKsT,QAAQsD,QACb5W,KAAK2T,WAAa,SACd3T,KAAK0U,QAAQ1U,KAAK0U,OAAOoB,SAS/BlU,EAAQO,UAAU0U,QAAU,SAAUC,GACpCnV,EAAM,WAEN3B,KAAK4V,UACL5V,KAAKsT,QAAQsD,QACb5W,KAAK2T,WAAa,SAClB3T,KAAK4J,KAAK,QAASkN,GAEf9W,KAAK2U,gBAAkB3U,KAAKwV,eAC9BxV,KAAKuV,aAUT3T,EAAQO,UAAUoT,UAAY,WAC5B,GAAIvV,KAAKqV,cAAgBrV,KAAKwV,cAAe,MAAOxV,KAEpD,IAAI6K,GAAO7K,IAEX,IAAIA,KAAKsT,QAAQgC,UAAYtV,KAAK4U,sBAChCjT,EAAM,oBACN3B,KAAKsT,QAAQsD,QACb5W,KAAKuU,QAAQ,oBACbvU,KAAKqV,cAAe,MACf,CACL,GAAI0B,GAAQ/W,KAAKsT,QAAQ0D,UACzBrV,GAAM,0CAA2CoV,GAEjD/W,KAAKqV,cAAe,CACpB,IAAIQ,GAAQjO,WAAW,WACjBiD,EAAK2K,gBAET7T,EAAM,wBACNkJ,EAAK0J,QAAQ,oBAAqB1J,EAAKyI,QAAQgC,UAC/CzK,EAAK0J,QAAQ,eAAgB1J,EAAKyI,QAAQgC,UAGtCzK,EAAK2K,eAET3K,EAAKsJ,KAAK,SAAUhN,GACdA,GACFxF,EAAM,2BACNkJ,EAAKwK,cAAe,EACpBxK,EAAK0K,YACL1K,EAAK0J,QAAQ,kBAAmBpN,EAAIuG,QAEpC/L,EAAM,qBACNkJ,EAAKoM,mBAGRF,EAEH/W,MAAK+S,KAAK/J,MACR0C,QAAS,WACP1D,aAAa6N,QAYrBjU,EAAQO,UAAU8U,YAAc,WAC9B,GAAIC,GAAUlX,KAAKsT,QAAQgC,QAC3BtV,MAAKqV,cAAe,EACpBrV,KAAKsT,QAAQsD,QACb5W,KAAKwU,kBACLxU,KAAKuU,QAAQ,YAAa2C,Kd8zDtB,SAAUrX,EAAQD,EAASM,Gex3EjCL,EAAAD,QAAAM,EAAA,IAQAL,EAAAD,QAAAwC,OAAAlC,EAAA,Kfg4EM,SAAUL,EAAQD,EAASM,IgBz4EjC,SAAAuC,GA0BA,QAAAD,GAAA5B,EAAAC,GACA,KAAAb,eAAAwC,IAAA,UAAAA,GAAA5B,EAAAC,EAEAA,SAEAD,GAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAA,MAGAA,GACAA,EAAAmC,EAAAnC,GACAC,EAAAsW,SAAAvW,EAAAgC,KACA/B,EAAAuW,OAAA,UAAAxW,EAAA0B,UAAA,QAAA1B,EAAA0B,SACAzB,EAAAmC,KAAApC,EAAAoC,KACApC,EAAAiB,QAAAhB,EAAAgB,MAAAjB,EAAAiB,QACGhB,EAAA+B,OACH/B,EAAAsW,SAAApU,EAAAlC,EAAA+B,YAGA5C,KAAAoX,OAAA,MAAAvW,EAAAuW,OAAAvW,EAAAuW,OACA3U,EAAAE,UAAA,WAAAA,SAAAL,SAEAzB,EAAAsW,WAAAtW,EAAAmC,OAEAnC,EAAAmC,KAAAhD,KAAAoX,OAAA,YAGApX,KAAAqX,MAAAxW,EAAAwW,QAAA,EACArX,KAAAmX,SAAAtW,EAAAsW,WACA1U,EAAAE,kBAAAwU,SAAA,aACAnX,KAAAgD,KAAAnC,EAAAmC,OAAAP,EAAAE,mBAAAK,KACAL,SAAAK,KACAhD,KAAAoX,OAAA,QACApX,KAAA6B,MAAAhB,EAAAgB,UACA,gBAAA7B,MAAA6B,QAAA7B,KAAA6B,MAAAyV,EAAAC,OAAAvX,KAAA6B,QACA7B,KAAAwX,SAAA,IAAA3W,EAAA2W,QACAxX,KAAAoB,MAAAP,EAAAO,MAAA,cAAAuC,QAAA,cACA3D,KAAAyX,aAAA5W,EAAA4W,WACAzX,KAAA0X,OAAA,IAAA7W,EAAA6W,MACA1X,KAAA2X,cAAA9W,EAAA8W,YACA3X,KAAA4X,aAAA/W,EAAA+W,WACA5X,KAAA6X,eAAAhX,EAAAgX,gBAAA,IACA7X,KAAA8X,kBAAAjX,EAAAiX,kBACA9X,KAAA+X,WAAAlX,EAAAkX,aAAA,uBACA/X,KAAAgY,iBAAAnX,EAAAmX,qBACAhY,KAAA2T,WAAA,GACA3T,KAAAiY,eACAjY,KAAAkY,cAAA,EACAlY,KAAAmY,WAAAtX,EAAAsX,YAAA,IACAnY,KAAAoY,gBAAAvX,EAAAuX,kBAAA,EACApY,KAAAqY,WAAA,KACArY,KAAAsY,mBAAAzX,EAAAyX,mBACAtY,KAAAuY,mBAAA,IAAA1X,EAAA0X,oBAAA1X,EAAA0X,wBAEA,IAAAvY,KAAAuY,oBAAAvY,KAAAuY,sBACAvY,KAAAuY,mBAAA,MAAAvY,KAAAuY,kBAAAC,YACAxY,KAAAuY,kBAAAC,UAAA,MAIAxY,KAAAyY,IAAA5X,EAAA4X,KAAA,KACAzY,KAAAuQ,IAAA1P,EAAA0P,KAAA,KACAvQ,KAAA0Y,WAAA7X,EAAA6X,YAAA,KACA1Y,KAAA2Y,KAAA9X,EAAA8X,MAAA,KACA3Y,KAAA4Y,GAAA/X,EAAA+X,IAAA,KACA5Y,KAAA6Y,QAAAhY,EAAAgY,SAAA,KACA7Y,KAAA8Y,mBAAA/X,SAAAF,EAAAiY,oBAAAjY,EAAAiY,mBACA9Y,KAAA+Y,YAAAlY,EAAAkY,SAGA,IAAAC,GAAA,gBAAAvW,KACAuW,GAAAvW,SAAAuW,IACAnY,EAAAoY,cAAAzH,OAAA0H,KAAArY,EAAAoY,cAAArV,OAAA,IACA5D,KAAAiZ,aAAApY,EAAAoY,cAGApY,EAAAsY,eACAnZ,KAAAmZ,aAAAtY,EAAAsY,eAKAnZ,KAAAK,GAAA,KACAL,KAAAoZ,SAAA,KACApZ,KAAAqZ,aAAA,KACArZ,KAAAsZ,YAAA,KAGAtZ,KAAAuZ,kBAAA,KACAvZ,KAAAwZ,iBAAA,KAEAxZ,KAAAmU,OAsFA,QAAAsF,GAAAxX,GACA,GAAAyX,KACA,QAAA5V,KAAA7B,GACAA,EAAAqS,eAAAxQ,KACA4V,EAAA5V,GAAA7B,EAAA6B,GAGA,OAAA4V,GA/MA,GAAA3B,GAAA7X,EAAA,IACAsP,EAAAtP,EAAA,GACAyB,EAAAzB,EAAA,8BACAyF,EAAAzF,EAAA,IACAkC,EAAAlC,EAAA,IACA6C,EAAA7C,EAAA,GACAoX,EAAApX,EAAA,GAMAL,GAAAD,QAAA4C,EAyGAA,EAAAmX,uBAAA,EAMAnK,EAAAhN,EAAAL,WAQAK,EAAAF,SAAAF,EAAAE,SAOAE,WACAA,EAAAoX,UAAA1Z,EAAA,IACAsC,EAAAuV,WAAA7X,EAAA,IACAsC,EAAAJ,OAAAlC,EAAA,IAUAsC,EAAAL,UAAA0X,gBAAA,SAAA7P,GACArI,EAAA,0BAAAqI,EACA,IAAAnI,GAAA4X,EAAAzZ,KAAA6B,MAGAA,GAAAiY,IAAA1X,EAAAE,SAGAT,EAAAkY,UAAA/P,CAGA,IAAAkD,GAAAlN,KAAAgY,iBAAAhO,MAGAhK,MAAAK,KAAAwB,EAAAmY,IAAAha,KAAAK,GAEA,IAAA0Z,GAAA,GAAAhC,GAAA/N,IACAnI,QACAC,OAAA9B,KACAqX,MAAAnK,EAAAmK,OAAArX,KAAAqX,MACAF,SAAAjK,EAAAiK,UAAAnX,KAAAmX,SACAnU,KAAAkK,EAAAlK,MAAAhD,KAAAgD,KACAoU,OAAAlK,EAAAkK,QAAApX,KAAAoX,OACAhW,KAAA8L,EAAA9L,MAAApB,KAAAoB,KACAqW,WAAAvK,EAAAuK,YAAAzX,KAAAyX,WACAC,MAAAxK,EAAAwK,OAAA1X,KAAA0X,MACAC,YAAAzK,EAAAyK,aAAA3X,KAAA2X,YACAC,WAAA1K,EAAA0K,YAAA5X,KAAA4X,WACAE,kBAAA5K,EAAA4K,mBAAA9X,KAAA8X,kBACAD,eAAA3K,EAAA2K,gBAAA7X,KAAA6X,eACAM,WAAAjL,EAAAiL,YAAAnY,KAAAmY,WACAM,IAAAvL,EAAAuL,KAAAzY,KAAAyY,IACAlI,IAAArD,EAAAqD,KAAAvQ,KAAAuQ,IACAmI,WAAAxL,EAAAwL,YAAA1Y,KAAA0Y,WACAC,KAAAzL,EAAAyL,MAAA3Y,KAAA2Y,KACAC,GAAA1L,EAAA0L,IAAA5Y,KAAA4Y,GACAC,QAAA3L,EAAA2L,SAAA7Y,KAAA6Y,QACAC,mBAAA5L,EAAA4L,oBAAA9Y,KAAA8Y,mBACAP,kBAAArL,EAAAqL,mBAAAvY,KAAAuY,kBACAU,aAAA/L,EAAA+L,cAAAjZ,KAAAiZ,aACAF,UAAA7L,EAAA6L,WAAA/Y,KAAA+Y,UACAI,aAAAjM,EAAAiM,cAAAnZ,KAAAmZ,aACAc,eAAA/M,EAAA+M,gBAAAja,KAAAia,eACAC,UAAAhN,EAAAgN,WAAA,QAGA,OAAAH,IAkBAvX,EAAAL,UAAAgS,KAAA,WACA,GAAA4F,EACA,IAAA/Z,KAAAoY,iBAAA5V,EAAAmX,uBAAA3Z,KAAA+X,WAAA7U,QAAA,kBACA6W,EAAA,gBACG,QAAA/Z,KAAA+X,WAAAnU,OAAA,CAEH,GAAAiH,GAAA7K,IAIA,YAHA4H,YAAA,WACAiD,EAAAjB,KAAA,oCACK,GAGLmQ,EAAA/Z,KAAA+X,WAAA,GAEA/X,KAAA2T,WAAA,SAGA,KACAoG,EAAA/Z,KAAA6Z,gBAAAE,GACG,MAAAtW,GAGH,MAFAzD,MAAA+X,WAAAvB,YACAxW,MAAAmU,OAIA4F,EAAA5F,OACAnU,KAAAma,aAAAJ,IASAvX,EAAAL,UAAAgY,aAAA,SAAAJ,GACApY,EAAA,uBAAAoY,EAAA/P,KACA,IAAAa,GAAA7K,IAEAA,MAAA+Z,YACApY,EAAA,iCAAA3B,KAAA+Z,UAAA/P,MACAhK,KAAA+Z,UAAApQ,sBAIA3J,KAAA+Z,YAGAA,EACAzQ,GAAA,mBACAuB,EAAAuP,YAEA9Q,GAAA,kBAAAgF,GACAzD,EAAAwP,SAAA/L,KAEAhF,GAAA,iBAAA7F,GACAoH,EAAAyP,QAAA7W,KAEA6F,GAAA,mBACAuB,EAAA0P,QAAA,sBAWA/X,EAAAL,UAAAqY,MAAA,SAAAxQ,GAQA,QAAAyQ,KACA,GAAA5P,EAAAyN,mBAAA,CACA,GAAAoC,IAAA1a,KAAA2a,gBAAA9P,EAAAkP,UAAAY,cACAC,MAAAF,EAEAE,IAEAjZ,EAAA,8BAAAqI,GACA+P,EAAAc,OAAqBzW,KAAA,OAAAsJ,KAAA,WACrBqM,EAAAvQ,KAAA,kBAAA+F,GACA,IAAAqL,EACA,YAAArL,EAAAnL,MAAA,UAAAmL,EAAA7B,KAAA,CAIA,GAHA/L,EAAA,4BAAAqI,GACAa,EAAAiQ,WAAA,EACAjQ,EAAAjB,KAAA,YAAAmQ,IACAA,EAAA,MACAvX,GAAAmX,sBAAA,cAAAI,EAAA/P,KAEArI,EAAA,iCAAAkJ,EAAAkP,UAAA/P,MACAa,EAAAkP,UAAAgB,MAAA,WACAH,GACA,WAAA/P,EAAA8I,aACAhS,EAAA,iDAEAiU,IAEA/K,EAAAsP,aAAAJ,GACAA,EAAAc,OAA2BzW,KAAA,aAC3ByG,EAAAjB,KAAA,UAAAmQ,GACAA,EAAA,KACAlP,EAAAiQ,WAAA,EACAjQ,EAAAmQ,eAEO,CACPrZ,EAAA,8BAAAqI,EACA,IAAA7C,GAAA,GAAAI,OAAA,cACAJ,GAAA4S,YAAA/P,KACAa,EAAAjB,KAAA,eAAAzC,OAKA,QAAA8T,KACAL,IAGAA,GAAA,EAEAhF,IAEAmE,EAAAjE,QACAiE,EAAA,MAIA,QAAA5D,GAAAhP,GACA,GAAA2H,GAAA,GAAAvH,OAAA,gBAAAJ,EACA2H,GAAAiL,YAAA/P,KAEAiR,IAEAtZ,EAAA,mDAAAqI,EAAA7C,GAEA0D,EAAAjB,KAAA,eAAAkF,GAGA,QAAAoM,KACA/E,EAAA,oBAIA,QAAAU,KACAV,EAAA,iBAIA,QAAAgF,GAAAC,GACArB,GAAAqB,EAAApR,OAAA+P,EAAA/P,OACArI,EAAA,6BAAAyZ,EAAApR,KAAA+P,EAAA/P,MACAiR,KAKA,QAAArF,KACAmE,EAAArQ,eAAA,OAAA+Q,GACAV,EAAArQ,eAAA,QAAAyM,GACA4D,EAAArQ,eAAA,QAAAwR,GACArQ,EAAAnB,eAAA,QAAAmN,GACAhM,EAAAnB,eAAA,YAAAyR,GAhGAxZ,EAAA,yBAAAqI,EACA,IAAA+P,GAAA/Z,KAAA6Z,gBAAA7P,GAA8CwQ,MAAA,IAC9CI,GAAA,EACA/P,EAAA7K,IAEAwC,GAAAmX,uBAAA,EA8FAI,EAAAvQ,KAAA,OAAAiR,GACAV,EAAAvQ,KAAA,QAAA2M,GACA4D,EAAAvQ,KAAA,QAAA0R,GAEAlb,KAAAwJ,KAAA,QAAAqN,GACA7W,KAAAwJ,KAAA,YAAA2R,GAEApB,EAAA5F,QASA3R,EAAAL,UAAAkZ,OAAA,WASA,GARA1Z,EAAA,eACA3B,KAAA2T,WAAA,OACAnR,EAAAmX,sBAAA,cAAA3Z,KAAA+Z,UAAA/P,KACAhK,KAAA4J,KAAA,QACA5J,KAAAgb,QAIA,SAAAhb,KAAA2T,YAAA3T,KAAAwX,SAAAxX,KAAA+Z,UAAAgB,MAAA,CACApZ,EAAA,0BACA,QAAAmC,GAAA,EAAAwX,EAAAtb,KAAAoZ,SAAAxV,OAA6CE,EAAAwX,EAAOxX,IACpD9D,KAAAwa,MAAAxa,KAAAoZ,SAAAtV,MAWAtB,EAAAL,UAAAkY,SAAA,SAAA/L,GACA,eAAAtO,KAAA2T,YAAA,SAAA3T,KAAA2T,YACA,YAAA3T,KAAA2T,WAQA,OAPAhS,EAAA,uCAAA2M,EAAAlK,KAAAkK,EAAAZ,MAEA1N,KAAA4J,KAAA,SAAA0E,GAGAtO,KAAA4J,KAAA,aAEA0E,EAAAlK,MACA,WACApE,KAAAub,YAAAtU,KAAAmF,MAAAkC,EAAAZ,MACA,MAEA,YACA1N,KAAAwb,UACAxb,KAAA4J,KAAA,OACA,MAEA,aACA,GAAAzC,GAAA,GAAAI,OAAA,eACAJ,GAAAsU,KAAAnN,EAAAZ,KACA1N,KAAAsa,QAAAnT,EACA,MAEA,eACAnH,KAAA4J,KAAA,OAAA0E,EAAAZ,MACA1N,KAAA4J,KAAA,UAAA0E,EAAAZ,UAIA/L,GAAA,8CAAA3B,KAAA2T,aAWAnR,EAAAL,UAAAoZ,YAAA,SAAA7N,GACA1N,KAAA4J,KAAA,YAAA8D,GACA1N,KAAAK,GAAAqN,EAAAsM,IACAha,KAAA+Z,UAAAlY,MAAAmY,IAAAtM,EAAAsM,IACAha,KAAAoZ,SAAApZ,KAAA0b,eAAAhO,EAAA0L,UACApZ,KAAAqZ,aAAA3L,EAAA2L,aACArZ,KAAAsZ,YAAA5L,EAAA4L,YACAtZ,KAAAqb,SAEA,WAAArb,KAAA2T,aACA3T,KAAAwb,UAGAxb,KAAA0J,eAAA,YAAA1J,KAAA2b,aACA3b,KAAAsJ,GAAA,YAAAtJ,KAAA2b,eASAnZ,EAAAL,UAAAwZ,YAAA,SAAAnT,GACAR,aAAAhI,KAAAwZ,iBACA,IAAA3O,GAAA7K,IACA6K,GAAA2O,iBAAA5R,WAAA,WACA,WAAAiD,EAAA8I,YACA9I,EAAA0P,QAAA,iBACG/R,GAAAqC,EAAAwO,aAAAxO,EAAAyO,cAUH9W,EAAAL,UAAAqZ,QAAA,WACA,GAAA3Q,GAAA7K,IACAgI,cAAA6C,EAAA0O,mBACA1O,EAAA0O,kBAAA3R,WAAA,WACAjG,EAAA,mDAAAkJ,EAAAyO,aACAzO,EAAA+Q,OACA/Q,EAAA8Q,YAAA9Q,EAAAyO,cACGzO,EAAAwO,eASH7W,EAAAL,UAAAyZ,KAAA,WACA,GAAA/Q,GAAA7K,IACAA,MAAA6b,WAAA,kBACAhR,EAAAjB,KAAA,WAUApH,EAAAL,UAAAiY,QAAA,WACApa,KAAAiY,YAAAvS,OAAA,EAAA1F,KAAAkY,eAKAlY,KAAAkY,cAAA,EAEA,IAAAlY,KAAAiY,YAAArU,OACA5D,KAAA4J,KAAA,SAEA5J,KAAAgb,SAUAxY,EAAAL,UAAA6Y,MAAA,WACA,WAAAhb,KAAA2T,YAAA3T,KAAA+Z,UAAA+B,WACA9b,KAAA8a,WAAA9a,KAAAiY,YAAArU,SACAjC,EAAA,gCAAA3B,KAAAiY,YAAArU,QACA5D,KAAA+Z,UAAAc,KAAA7a,KAAAiY,aAGAjY,KAAAkY,cAAAlY,KAAAiY,YAAArU,OACA5D,KAAA4J,KAAA,WAcApH,EAAAL,UAAAmU,MACA9T,EAAAL,UAAA0Y,KAAA,SAAAtL,EAAArC,EAAAwD,GAEA,MADA1Q,MAAA6b,WAAA,UAAAtM,EAAArC,EAAAwD,GACA1Q,MAaAwC,EAAAL,UAAA0Z,WAAA,SAAAzX,EAAAsJ,EAAAR,EAAAwD,GAWA,GAVA,kBAAAhD,KACAgD,EAAAhD,EACAA,EAAA3M,QAGA,kBAAAmM,KACAwD,EAAAxD,EACAA,EAAA,MAGA,YAAAlN,KAAA2T,YAAA,WAAA3T,KAAA2T,WAAA,CAIAzG,QACAA,EAAA6O,UAAA,IAAA7O,EAAA6O,QAEA,IAAAzN,IACAlK,OACAsJ,OACAR,UAEAlN,MAAA4J,KAAA,eAAA0E,GACAtO,KAAAiY,YAAAjP,KAAAsF,GACAoC,GAAA1Q,KAAAwJ,KAAA,QAAAkH,GACA1Q,KAAAgb,UASAxY,EAAAL,UAAA2T,MAAA,WAqBA,QAAAA,KACAjL,EAAA0P,QAAA,gBACA5Y,EAAA,+CACAkJ,EAAAkP,UAAAjE,QAGA,QAAAkG,KACAnR,EAAAnB,eAAA,UAAAsS,GACAnR,EAAAnB,eAAA,eAAAsS,GACAlG,IAGA,QAAAmG,KAEApR,EAAArB,KAAA,UAAAwS,GACAnR,EAAArB,KAAA,eAAAwS,GAnCA,eAAAhc,KAAA2T,YAAA,SAAA3T,KAAA2T,WAAA,CACA3T,KAAA2T,WAAA,SAEA,IAAA9I,GAAA7K,IAEAA,MAAAiY,YAAArU,OACA5D,KAAAwJ,KAAA,mBACAxJ,KAAA8a,UACAmB,IAEAnG,MAGK9V,KAAA8a,UACLmB,IAEAnG,IAsBA,MAAA9V,OASAwC,EAAAL,UAAAmY,QAAA,SAAAnT,GACAxF,EAAA,kBAAAwF,GACA3E,EAAAmX,uBAAA,EACA3Z,KAAA4J,KAAA,QAAAzC,GACAnH,KAAAua,QAAA,kBAAApT,IASA3E,EAAAL,UAAAoY,QAAA,SAAAzD,EAAAoF,GACA,eAAAlc,KAAA2T,YAAA,SAAA3T,KAAA2T,YAAA,YAAA3T,KAAA2T,WAAA,CACAhS,EAAA,iCAAAmV,EACA,IAAAjM,GAAA7K,IAGAgI,cAAAhI,KAAAuZ,mBACAvR,aAAAhI,KAAAwZ,kBAGAxZ,KAAA+Z,UAAApQ,mBAAA,SAGA3J,KAAA+Z,UAAAjE,QAGA9V,KAAA+Z,UAAApQ,qBAGA3J,KAAA2T,WAAA,SAGA3T,KAAAK,GAAA,KAGAL,KAAA4J,KAAA,QAAAkN,EAAAoF,GAIArR,EAAAoN,eACApN,EAAAqN,cAAA,IAYA1V,EAAAL,UAAAuZ,eAAA,SAAAtC,GAEA,OADA+C,MACArY,EAAA,EAAAiD,EAAAqS,EAAAxV,OAAsCE,EAAAiD,EAAOjD,KAC7C6B,EAAA3F,KAAA+X,WAAAqB,EAAAtV,KAAAqY,EAAAnT,KAAAoQ,EAAAtV,GAEA,OAAAqY,MhB84E8B5b,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,IiBvnGjC,SAAAuC,GAuBA,QAAA2Z,GAAAvb,GACA,GAAAwb,GACAC,GAAA,EACAC,GAAA,EACA7E,GAAA,IAAA7W,EAAA6W,KAEA,IAAAjV,EAAAE,SAAA,CACA,GAAA6Z,GAAA,WAAA7Z,SAAAL,SACAU,EAAAL,SAAAK;AAGAA,IACAA,EAAAwZ,EAAA,QAGAF,EAAAzb,EAAAsW,WAAAxU,SAAAwU,UAAAnU,IAAAnC,EAAAmC,KACAuZ,EAAA1b,EAAAuW,SAAAoF,EAOA,GAJA3b,EAAA4b,QAAAH,EACAzb,EAAA6b,QAAAH,EACAF,EAAA,GAAAM,GAAA9b,GAEA,QAAAwb,KAAAxb,EAAA4W,WACA,UAAAmF,GAAA/b,EAEA,KAAA6W,EAAA,SAAAnQ,OAAA,iBACA,WAAAsV,GAAAhc,GA9CA,GAAA8b,GAAAzc,EAAA,IACA0c,EAAA1c,EAAA,IACA2c,EAAA3c,EAAA,IACA4c,EAAA5c,EAAA,GAMAN,GAAAwc,UACAxc,EAAAkd,cjBiqG8Bvc,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,IkBnrGjC,SAAAuC,GAEA,GAAAsa,GAAA7c,EAAA,GAEAL,GAAAD,QAAA,SAAAiB,GACA,GAAA4b,GAAA5b,EAAA4b,QAIAC,EAAA7b,EAAA6b,QAIA9E,EAAA/W,EAAA+W,UAGA,KACA,sBAAA+E,mBAAAF,GAAAM,GACA,UAAAJ,gBAEG,MAAAlZ,IAKH,IACA,sBAAAuZ,kBAAAN,GAAA9E,EACA,UAAAoF,gBAEG,MAAAvZ,IAEH,IAAAgZ,EACA,IACA,WAAAha,GAAA,UAAA4F,OAAA,UAAA4U,KAAA,4BACK,MAAAxZ,QlByrGyBlD,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,GmBttGxB,IACAC,EAAAD,QAAA,mBAAA+c,iBACA,uBAAAA,gBACC,MAAAxV,GAGDtH,EAAAD,SAAA,InBuuGM,SAAUC,EAAQD,EAASM,IoBtvGjC,SAAAuC,GAqBA,QAAAya,MASA,QAAAN,GAAA/b,GAKA,GAJAsc,EAAA5c,KAAAP,KAAAa,GACAb,KAAAia,eAAApZ,EAAAoZ,eACAja,KAAAiZ,aAAApY,EAAAoY,aAEAxW,EAAAE,SAAA,CACA,GAAA6Z,GAAA,WAAA7Z,SAAAL,SACAU,EAAAL,SAAAK,IAGAA,KACAA,EAAAwZ,EAAA,QAGAxc,KAAAsc,GAAAzb,EAAAsW,WAAA1U,EAAAE,SAAAwU,UACAnU,IAAAnC,EAAAmC,KACAhD,KAAAuc,GAAA1b,EAAAuW,SAAAoF,GA6FA,QAAAY,GAAAvc,GACAb,KAAAqd,OAAAxc,EAAAwc,QAAA,MACArd,KAAAY,IAAAC,EAAAD,IACAZ,KAAAsc,KAAAzb,EAAAyb,GACAtc,KAAAuc,KAAA1b,EAAA0b,GACAvc,KAAAsd,OAAA,IAAAzc,EAAAyc,MACAtd,KAAA0N,KAAA3M,SAAAF,EAAA6M,KAAA7M,EAAA6M,KAAA,KACA1N,KAAAqX,MAAAxW,EAAAwW,MACArX,KAAAud,SAAA1c,EAAA0c,SACAvd,KAAA2a,eAAA9Z,EAAA8Z,eACA3a,KAAA4X,WAAA/W,EAAA+W,WACA5X,KAAAia,eAAApZ,EAAAoZ,eAGAja,KAAAyY,IAAA5X,EAAA4X,IACAzY,KAAAuQ,IAAA1P,EAAA0P,IACAvQ,KAAA0Y,WAAA7X,EAAA6X,WACA1Y,KAAA2Y,KAAA9X,EAAA8X,KACA3Y,KAAA4Y,GAAA/X,EAAA+X,GACA5Y,KAAA6Y,QAAAhY,EAAAgY,QACA7Y,KAAA8Y,mBAAAjY,EAAAiY,mBAGA9Y,KAAAiZ,aAAApY,EAAAoY,aAEAjZ,KAAAwd,SAiPA,QAAAC,KACA,OAAA3Z,KAAAsZ,GAAAM,SACAN,EAAAM,SAAApJ,eAAAxQ,IACAsZ,EAAAM,SAAA5Z,GAAA6Z,QApZA,GAAAhB,GAAAzc,EAAA,IACAid,EAAAjd,EAAA,IACAsP,EAAAtP,EAAA,GACA0d,EAAA1d,EAAA,IACAyB,EAAAzB,EAAA,kCAMAL,GAAAD,QAAAgd,EACA/c,EAAAD,QAAAwd,UAuCAQ,EAAAhB,EAAAO,GAMAP,EAAAza,UAAAwY,gBAAA,EASAiC,EAAAza,UAAA0b,QAAA,SAAAhd,GAsBA,MArBAA,SACAA,EAAAD,IAAAZ,KAAAY,MACAC,EAAAyb,GAAAtc,KAAAsc,GACAzb,EAAA0b,GAAAvc,KAAAuc,GACA1b,EAAAwW,MAAArX,KAAAqX,QAAA,EACAxW,EAAA8Z,eAAA3a,KAAA2a,eACA9Z,EAAA+W,WAAA5X,KAAA4X,WAGA/W,EAAA4X,IAAAzY,KAAAyY,IACA5X,EAAA0P,IAAAvQ,KAAAuQ,IACA1P,EAAA6X,WAAA1Y,KAAA0Y,WACA7X,EAAA8X,KAAA3Y,KAAA2Y,KACA9X,EAAA+X,GAAA5Y,KAAA4Y,GACA/X,EAAAgY,QAAA7Y,KAAA6Y,QACAhY,EAAAiY,mBAAA9Y,KAAA8Y,mBACAjY,EAAAoZ,eAAAja,KAAAia,eAGApZ,EAAAoY,aAAAjZ,KAAAiZ,aAEA,GAAAmE,GAAAvc,IAWA+b,EAAAza,UAAA2b,QAAA,SAAApQ,EAAAgD,GACA,GAAA6M,GAAA,gBAAA7P,IAAA3M,SAAA2M,EACAqQ,EAAA/d,KAAA6d,SAA0BR,OAAA,OAAA3P,OAAA6P,aAC1B1S,EAAA7K,IACA+d,GAAAzU,GAAA,UAAAoH,GACAqN,EAAAzU,GAAA,iBAAAnC,GACA0D,EAAAyP,QAAA,iBAAAnT,KAEAnH,KAAAge,QAAAD,GASAnB,EAAAza,UAAA8b,OAAA,WACAtc,EAAA,WACA,IAAAoc,GAAA/d,KAAA6d,UACAhT,EAAA7K,IACA+d,GAAAzU,GAAA,gBAAAoE,GACA7C,EAAAqT,OAAAxQ,KAEAqQ,EAAAzU,GAAA,iBAAAnC,GACA0D,EAAAyP,QAAA,iBAAAnT,KAEAnH,KAAAme,QAAAJ,GA0CAvO,EAAA4N,EAAAjb,WAQAib,EAAAjb,UAAAqb,OAAA,WACA,GAAA3c,IAAcwW,MAAArX,KAAAqX,MAAAoF,QAAAzc,KAAAsc,GAAAI,QAAA1c,KAAAuc,GAAA3E,WAAA5X,KAAA4X,WAGd/W,GAAA4X,IAAAzY,KAAAyY,IACA5X,EAAA0P,IAAAvQ,KAAAuQ,IACA1P,EAAA6X,WAAA1Y,KAAA0Y,WACA7X,EAAA8X,KAAA3Y,KAAA2Y,KACA9X,EAAA+X,GAAA5Y,KAAA4Y,GACA/X,EAAAgY,QAAA7Y,KAAA6Y,QACAhY,EAAAiY,mBAAA9Y,KAAA8Y,kBAEA,IAAAuD,GAAArc,KAAAqc,IAAA,GAAAM,GAAA9b,GACAgK,EAAA7K,IAEA,KACA2B,EAAA,kBAAA3B,KAAAqd,OAAArd,KAAAY,KACAyb,EAAAlI,KAAAnU,KAAAqd,OAAArd,KAAAY,IAAAZ,KAAAsd,MACA,KACA,GAAAtd,KAAAiZ,aAAA,CACAoD,EAAA+B,uBAAA/B,EAAA+B,uBAAA,EACA,QAAAta,KAAA9D,MAAAiZ,aACAjZ,KAAAiZ,aAAA3E,eAAAxQ,IACAuY,EAAAgC,iBAAAva,EAAA9D,KAAAiZ,aAAAnV,KAIK,MAAAL,IAEL,YAAAzD,KAAAqd,OACA,IACArd,KAAAud,SACAlB,EAAAgC,iBAAA,2CAEAhC,EAAAgC,iBAAA,2CAEO,MAAA5a,IAGP,IACA4Y,EAAAgC,iBAAA,gBACK,MAAA5a,IAGL,mBAAA4Y,KACAA,EAAAiC,iBAAA,GAGAte,KAAAia,iBACAoC,EAAA7T,QAAAxI,KAAAia,gBAGAja,KAAAue,UACAlC,EAAAjK,OAAA,WACAvH,EAAA2T,UAEAnC,EAAAlG,QAAA,WACAtL,EAAAyP,QAAA+B,EAAAoC,gBAGApC,EAAAqC,mBAAA,WACA,OAAArC,EAAA1I,WACA,IACA,GAAAgL,GAAAtC,EAAAuC,kBAAA,eACA/T,GAAA8P,gBAAA,6BAAAgE,IACAtC,EAAAwC,aAAA,eAEW,MAAApb,IAEX,IAAA4Y,EAAA1I,aACA,MAAA0I,EAAAyC,QAAA,OAAAzC,EAAAyC,OACAjU,EAAA2T,SAIA5W,WAAA,WACAiD,EAAAyP,QAAA+B,EAAAyC,SACW,KAKXnd,EAAA,cAAA3B,KAAA0N,MACA2O,EAAAxB,KAAA7a,KAAA0N,MACG,MAAAjK,GAOH,WAHAmE,YAAA,WACAiD,EAAAyP,QAAA7W,IACK,GAILhB,EAAAgC,WACAzE,KAAA2F,MAAAyX,EAAA2B,gBACA3B,EAAAM,SAAA1d,KAAA2F,OAAA3F,OAUAod,EAAAjb,UAAA6c,UAAA,WACAhf,KAAA4J,KAAA,WACA5J,KAAA4V,WASAwH,EAAAjb,UAAA+b,OAAA,SAAAxQ,GACA1N,KAAA4J,KAAA,OAAA8D,GACA1N,KAAAgf,aASA5B,EAAAjb,UAAAmY,QAAA,SAAAnT,GACAnH,KAAA4J,KAAA,QAAAzC,GACAnH,KAAA4V,SAAA,IASAwH,EAAAjb,UAAAyT,QAAA,SAAAqJ,GACA,sBAAAjf,MAAAqc,KAAA,OAAArc,KAAAqc,IAAA,CAUA,GANArc,KAAAue,SACAve,KAAAqc,IAAAjK,OAAApS,KAAAqc,IAAAlG,QAAA+G,EAEAld,KAAAqc,IAAAqC,mBAAAxB,EAGA+B,EACA,IACAjf,KAAAqc,IAAAsB,QACK,MAAAla,IAGLhB,EAAAgC,gBACA2Y,GAAAM,SAAA1d,KAAA2F,OAGA3F,KAAAqc,IAAA,OASAe,EAAAjb,UAAAqc,OAAA,WACA,GAAA9Q,EACA,KACA,GAAAiR,EACA,KACAA,EAAA3e,KAAAqc,IAAAuC,kBAAA,gBACK,MAAAnb,IAELiK,EADA,6BAAAiR,EACA3e,KAAAqc,IAAA6C,UAAAlf,KAAAqc,IAAAoC,aAEAze,KAAAqc,IAAAoC,aAEG,MAAAhb,GACHzD,KAAAsa,QAAA7W,GAEA,MAAAiK,GACA1N,KAAAke,OAAAxQ,IAUA0P,EAAAjb,UAAAoc,OAAA,WACA,yBAAA9b,GAAAua,iBAAAhd,KAAAuc,IAAAvc,KAAA4X,YASAwF,EAAAjb,UAAAwb,MAAA,WACA3d,KAAA4V,WASAwH,EAAA2B,cAAA,EACA3B,EAAAM,YAEAjb,EAAAgC,WACAhC,EAAA0c,YACA1c,EAAA0c,YAAA,WAAA1B,GACGhb,EAAA+N,kBACH/N,EAAA+N,iBAAA,eAAAiN,GAAA,MpBowG8Bld,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,GqBvnHjC,QAAAid,GAAAtc,GACA,GAAA8W,GAAA9W,KAAA8W,WACAyH,KAAAzH,IACA3X,KAAA2a,gBAAA,GAEAf,EAAArZ,KAAAP,KAAAa,GAnCA,GAAA+Y,GAAA1Z,EAAA,IACAoX,EAAApX,EAAA,IACAkC,EAAAlC,EAAA,IACA0d,EAAA1d,EAAA,IACAmf,EAAAnf,EAAA,IACAyB,EAAAzB,EAAA,8BAMAL,GAAAD,QAAAud,CAMA,IAAAiC,GAAA,WACA,GAAAzC,GAAAzc,EAAA,IACAmc,EAAA,GAAAM,IAAgCF,SAAA,GAChC,cAAAJ,EAAAwC,eAsBAjB,GAAAT,EAAAvD,GAMAuD,EAAAhb,UAAA6H,KAAA,UASAmT,EAAAhb,UAAAmd,OAAA,WACAtf,KAAAuf,QAUApC,EAAAhb,UAAA4Y,MAAA,SAAAyE,GAKA,QAAAzE,KACApZ,EAAA,UACAkJ,EAAA8I,WAAA,SACA6L,IAPA,GAAA3U,GAAA7K,IAUA,IARAA,KAAA2T,WAAA,UAQA3T,KAAAoc,UAAApc,KAAA8b,SAAA,CACA,GAAA2D,GAAA,CAEAzf,MAAAoc,UACAza,EAAA,+CACA8d,IACAzf,KAAAwJ,KAAA,0BACA7H,EAAA,gCACA8d,GAAA1E,OAIA/a,KAAA8b,WACAna,EAAA,+CACA8d,IACAzf,KAAAwJ,KAAA,mBACA7H,EAAA,gCACA8d,GAAA1E,WAIAA,MAUAoC,EAAAhb,UAAAod,KAAA,WACA5d,EAAA,WACA3B,KAAAoc,SAAA,EACApc,KAAAie,SACAje,KAAA4J,KAAA,SASAuT,EAAAhb,UAAA+b,OAAA,SAAAxQ,GACA,GAAA7C,GAAA7K,IACA2B,GAAA,sBAAA+L,EACA,IAAAK,GAAA,SAAAO,EAAA3I,EAAA8Z,GAOA,MALA,YAAA5U,EAAA8I,YACA9I,EAAAwQ,SAIA,UAAA/M,EAAAlK,MACAyG,EAAA0P,WACA,OAIA1P,GAAAwP,SAAA/L,GAIAlM,GAAAsd,cAAAhS,EAAA1N,KAAA8B,OAAAuW,WAAAtK,GAGA,WAAA/N,KAAA2T,aAEA3T,KAAAoc,SAAA,EACApc,KAAA4J,KAAA,gBAEA,SAAA5J,KAAA2T,WACA3T,KAAAuf,OAEA5d,EAAA,uCAAA3B,KAAA2T,cAWAwJ,EAAAhb,UAAAwd,QAAA,WAGA,QAAA7J,KACAnU,EAAA,wBACAkJ,EAAAyL,QAAiBlS,KAAA,WAJjB,GAAAyG,GAAA7K,IAOA,UAAAA,KAAA2T,YACAhS,EAAA,4BACAmU,MAIAnU,EAAA,wCACA3B,KAAAwJ,KAAA,OAAAsM,KAYAqH,EAAAhb,UAAAmU,MAAA,SAAAsJ,GACA,GAAA/U,GAAA7K,IACAA,MAAA8b,UAAA,CACA,IAAA+D,GAAA,WACAhV,EAAAiR,UAAA,EACAjR,EAAAjB,KAAA,SAGAxH,GAAA0d,cAAAF,EAAA5f,KAAA2a,eAAA,SAAAjN,GACA7C,EAAAiT,QAAApQ,EAAAmS,MAUA1C,EAAAhb,UAAAvB,IAAA,WACA,GAAAiB,GAAA7B,KAAA6B,UACAke,EAAA/f,KAAAoX,OAAA,eACApU,EAAA,IAGA,IAAAhD,KAAA8X,oBACAjW,EAAA7B,KAAA6X,gBAAAwH,KAGArf,KAAA2a,gBAAA9Y,EAAAmY,MACAnY,EAAAme,IAAA,GAGAne,EAAAyV,EAAAxH,OAAAjO,GAGA7B,KAAAgD,OAAA,UAAA+c,GAAA,MAAAnR,OAAA5O,KAAAgD,OACA,SAAA+c,GAAA,KAAAnR,OAAA5O,KAAAgD,SACAA,EAAA,IAAAhD,KAAAgD,MAIAnB,EAAA+B,SACA/B,EAAA,IAAAA,EAGA,IAAAoB,GAAAjD,KAAAmX,SAAAjU,QAAA,SACA,OAAA6c,GAAA,OAAA9c,EAAA,IAAAjD,KAAAmX,SAAA,IAAAnX,KAAAmX,UAAAnU,EAAAhD,KAAAoB,KAAAS,IrBiqHM,SAAUhC,EAAQD,EAASM,GsBh4HjC,QAAA0Z,GAAA/Y,GACAb,KAAAoB,KAAAP,EAAAO,KACApB,KAAAmX,SAAAtW,EAAAsW,SACAnX,KAAAgD,KAAAnC,EAAAmC,KACAhD,KAAAoX,OAAAvW,EAAAuW,OACApX,KAAA6B,MAAAhB,EAAAgB,MACA7B,KAAA6X,eAAAhX,EAAAgX,eACA7X,KAAA8X,kBAAAjX,EAAAiX,kBACA9X,KAAA2T,WAAA,GACA3T,KAAAqX,MAAAxW,EAAAwW,QAAA,EACArX,KAAA8B,OAAAjB,EAAAiB,OACA9B,KAAA4X,WAAA/W,EAAA+W,WAGA5X,KAAAyY,IAAA5X,EAAA4X,IACAzY,KAAAuQ,IAAA1P,EAAA0P,IACAvQ,KAAA0Y,WAAA7X,EAAA6X,WACA1Y,KAAA2Y,KAAA9X,EAAA8X,KACA3Y,KAAA4Y,GAAA/X,EAAA+X,GACA5Y,KAAA6Y,QAAAhY,EAAAgY,QACA7Y,KAAA8Y,mBAAAjY,EAAAiY,mBACA9Y,KAAA+Y,UAAAlY,EAAAkY,UAGA/Y,KAAAiZ,aAAApY,EAAAoY,aACAjZ,KAAAmZ,aAAAtY,EAAAsY,aAzCA,GAAA/W,GAAAlC,EAAA,IACAsP,EAAAtP,EAAA,EAMAL,GAAAD,QAAAga,EAyCApK,EAAAoK,EAAAzX,WAUAyX,EAAAzX,UAAAmY,QAAA,SAAA/K,EAAA2M,GACA,GAAA/U,GAAA,GAAAI,OAAAgI,EAIA,OAHApI,GAAA/C,KAAA,iBACA+C,EAAA8Y,YAAA/D,EACAlc,KAAA4J,KAAA,QAAAzC,GACAnH,MASA4Z,EAAAzX,UAAAgS,KAAA,WAMA,MALA,WAAAnU,KAAA2T,YAAA,KAAA3T,KAAA2T,aACA3T,KAAA2T,WAAA,UACA3T,KAAAsf,UAGAtf,MASA4Z,EAAAzX,UAAA2T,MAAA,WAMA,MALA,YAAA9V,KAAA2T,YAAA,SAAA3T,KAAA2T,aACA3T,KAAA2f,UACA3f,KAAAua,WAGAva,MAUA4Z,EAAAzX,UAAA0Y,KAAA,SAAA+E,GACA,YAAA5f,KAAA2T,WAGA,SAAApM,OAAA,qBAFAvH,MAAAsW,MAAAsJ,IAYAhG,EAAAzX,UAAAkZ,OAAA,WACArb,KAAA2T,WAAA,OACA3T,KAAA8b,UAAA,EACA9b,KAAA4J,KAAA,SAUAgQ,EAAAzX,UAAA+b,OAAA,SAAAxQ,GACA,GAAAY,GAAAlM,EAAA8d,aAAAxS,EAAA1N,KAAA8B,OAAAuW,WACArY,MAAAqa,SAAA/L,IAOAsL,EAAAzX,UAAAkY,SAAA,SAAA/L,GACAtO,KAAA4J,KAAA,SAAA0E,IASAsL,EAAAzX,UAAAoY,QAAA,WACAva,KAAA2T,WAAA,SACA3T,KAAA4J,KAAA,WtB45HM,SAAU/J,EAAQD,EAASM,IuBvjIjC,SAAAuC,GA8HA,QAAA0d,GAAA7R,EAAAP,GAEA,GAAA3G,GAAA,IAAAxH,EAAAggB,QAAAtR,EAAAlK,MAAAkK,EAAAZ,SACA,OAAAK,GAAA3G,GAOA,QAAAgZ,GAAA9R,EAAAqM,EAAA5M,GACA,IAAA4M,EACA,MAAA/a,GAAAygB,mBAAA/R,EAAAP,EAGA,IAAAL,GAAAY,EAAAZ,KACA4S,EAAA,GAAAC,YAAA7S,GACA8S,EAAA,GAAAD,YAAA,EAAA7S,EAAA+S,WAEAD,GAAA,GAAAZ,EAAAtR,EAAAlK,KACA,QAAAN,GAAA,EAAiBA,EAAAwc,EAAA1c,OAAyBE,IAC1C0c,EAAA1c,EAAA,GAAAwc,EAAAxc,EAGA,OAAAiK,GAAAyS,EAAA1N,QAGA,QAAA4N,GAAApS,EAAAqM,EAAA5M,GACA,IAAA4M,EACA,MAAA/a,GAAAygB,mBAAA/R,EAAAP,EAGA,IAAA4S,GAAA,GAAAxO,WAKA,OAJAwO,GAAAvO,OAAA,WACA9D,EAAAZ,KAAAiT,EAAAtO,OACAzS,EAAAghB,aAAAtS,EAAAqM,GAAA,EAAA5M,IAEA4S,EAAArO,kBAAAhE,EAAAZ,MAGA,QAAAmT,GAAAvS,EAAAqM,EAAA5M,GACA,IAAA4M,EACA,MAAA/a,GAAAygB,mBAAA/R,EAAAP,EAGA,IAAA+S,EACA,MAAAJ,GAAApS,EAAAqM,EAAA5M,EAGA,IAAAnK,GAAA,GAAA2c,YAAA,EACA3c,GAAA,GAAAgc,EAAAtR,EAAAlK,KACA,IAAA2c,GAAA,GAAArP,IAAA9N,EAAAkP,OAAAxE,EAAAZ,MAEA,OAAAK,GAAAgT,GAkFA,QAAAC,GAAAtT,GACA,IACAA,EAAAuT,EAAA1J,OAAA7J,GAA8BwT,QAAA,IAC3B,MAAAzd,GACH,SAEA,MAAAiK,GAgFA,QAAAyT,GAAAC,EAAAC,EAAAC,GAWA,OAVAjP,GAAA,GAAAtJ,OAAAqY,EAAAxd,QACAoL,EAAAuS,EAAAH,EAAAxd,OAAA0d,GAEAE,EAAA,SAAA1d,EAAA2d,EAAA3Q,GACAuQ,EAAAI,EAAA,SAAA3S,EAAAS,GACA8C,EAAAvO,GAAAyL,EACAuB,EAAAhC,EAAAuD,MAIAvO,EAAA,EAAiBA,EAAAsd,EAAAxd,OAAgBE,IACjC0d,EAAA1d,EAAAsd,EAAAtd,GAAAkL,GAnWA,GAMA0S,GANAxI,EAAAhZ,EAAA,IACAyhB,EAAAzhB,EAAA,IACA0hB,EAAA1hB,EAAA,IACAqhB,EAAArhB,EAAA,IACA+gB,EAAA/gB,EAAA,GAGAuC,MAAAmQ,cACA8O,EAAAxhB,EAAA,IAUA,IAAA2hB,GAAA,mBAAAxd,YAAA,WAAAvB,KAAAuB,UAAAC,WAQAwd,EAAA,mBAAAzd,YAAA,aAAAvB,KAAAuB,UAAAC,WAMAwc,EAAAe,GAAAC,CAMAliB,GAAA0C,SAAA,CAMA,IAAAsd,GAAAhgB,EAAAggB,SACAzL,KAAA,EACA2B,MAAA,EACA8F,KAAA,EACAmG,KAAA,EACA3a,QAAA,EACAoQ,QAAA,EACA3O,KAAA,GAGAmZ,EAAA9I,EAAA0G,GAMAzY,GAAW/C,KAAA,QAAAsJ,KAAA,gBAMXgE,EAAAxR,EAAA,GAkBAN,GAAAghB,aAAA,SAAAtS,EAAAqM,EAAAsH,EAAAlU,GACA,kBAAA4M,KACA5M,EAAA4M,EACAA,GAAA,GAGA,kBAAAsH,KACAlU,EAAAkU,EACAA,EAAA,KAGA,IAAAvU,GAAA3M,SAAAuN,EAAAZ,KACA3M,OACAuN,EAAAZ,KAAAoF,QAAAxE,EAAAZ,IAEA,IAAAjL,EAAAmQ,aAAAlF,YAAAkF,aACA,MAAAwN,GAAA9R,EAAAqM,EAAA5M,EACG,IAAA2D,GAAAhE,YAAAjL,GAAAiP,KACH,MAAAmP,GAAAvS,EAAAqM,EAAA5M,EAIA,IAAAL,KAAAuC,OACA,MAAAkQ,GAAA7R,EAAAP,EAIA,IAAAmU,GAAAtC,EAAAtR,EAAAlK,KAOA,OAJArD,UAAAuN,EAAAZ,OACAwU,GAAAD,EAAAhB,EAAAnR,OAAAzD,OAAAiC,EAAAZ,OAA8DwT,QAAA,IAAgB7U,OAAAiC,EAAAZ,OAG9EK,EAAA,GAAAmU,IAmEAtiB,EAAAygB,mBAAA,SAAA/R,EAAAP,GACA,GAAA3G,GAAA,IAAAxH,EAAAggB,QAAAtR,EAAAlK,KACA,IAAAsN,GAAApD,EAAAZ,eAAAjL,GAAAiP,KAAA,CACA,GAAAiP,GAAA,GAAAxO,WAKA,OAJAwO,GAAAvO,OAAA,WACA,GAAA4N,GAAAW,EAAAtO,OAAAtG,MAAA,OACAgC,GAAA3G,EAAA4Y,IAEAW,EAAAwB,cAAA7T,EAAAZ,MAGA,GAAA0U,EACA,KACAA,EAAA/V,OAAAgW,aAAAtc,MAAA,QAAAwa,YAAAjS,EAAAZ,OACG,MAAAjK,GAIH,OAFA6e,GAAA,GAAA/B,YAAAjS,EAAAZ,MACA6U,EAAA,GAAAxZ,OAAAuZ,EAAA1e,QACAE,EAAA,EAAmBA,EAAAwe,EAAA1e,OAAkBE,IACrCye,EAAAze,GAAAwe,EAAAxe,EAEAse,GAAA/V,OAAAgW,aAAAtc,MAAA,KAAAwc,GAGA,MADAnb,IAAA3E,EAAA+f,KAAAJ,GACArU,EAAA3G,IAUAxH,EAAAsgB,aAAA,SAAAxS,EAAA2K,EAAAoK,GACA,GAAA1hB,SAAA2M,EACA,MAAAvG,EAGA,oBAAAuG,GAAA,CACA,SAAAA,EAAA7K,OAAA,GACA,MAAAjD,GAAA8iB,mBAAAhV,EAAA1B,OAAA,GAAAqM,EAGA,IAAAoK,IACA/U,EAAAsT,EAAAtT,GACAA,KAAA,GACA,MAAAvG,EAGA,IAAA/C,GAAAsJ,EAAA7K,OAAA,EAEA,OAAA+L,QAAAxK,OAAA4d,EAAA5d,GAIAsJ,EAAA9J,OAAA,GACcQ,KAAA4d,EAAA5d,GAAAsJ,OAAAhK,UAAA,KAEAU,KAAA4d,EAAA5d,IANd+C,EAUA,GAAAwb,GAAA,GAAApC,YAAA7S,GACAtJ,EAAAue,EAAA,GACAC,EAAAhB,EAAAlU,EAAA,EAIA,OAHAgE,IAAA,SAAA2G,IACAuK,EAAA,GAAAlR,IAAAkR,MAEUxe,KAAA4d,EAAA5d,GAAAsJ,KAAAkV,IAmBVhjB,EAAA8iB,mBAAA,SAAAnT,EAAA8I,GACA,GAAAjU,GAAA4d,EAAAzS,EAAA1M,OAAA,GACA,KAAA6e,EACA,OAAYtd,OAAAsJ,MAAoBuC,QAAA,EAAAvC,KAAA6B,EAAAvD,OAAA,IAGhC,IAAA0B,GAAAgU,EAAAnK,OAAAhI,EAAAvD,OAAA,GAMA,OAJA,SAAAqM,GAAA3G,IACAhE,EAAA,GAAAgE,IAAAhE,MAGUtJ,OAAAsJ,SAmBV9N,EAAAkgB,cAAA,SAAAF,EAAAjF,EAAA5M,GAoBA,QAAA8U,GAAAzb,GACA,MAAAA,GAAAxD,OAAA,IAAAwD,EAGA,QAAA0b,GAAAxU,EAAAyU,GACAnjB,EAAAghB,aAAAtS,IAAAiP,GAAA5C,GAAA,WAAAvT,GACA2b,EAAA,KAAAF,EAAAzb,MAzBA,kBAAAuT,KACA5M,EAAA4M,EACAA,EAAA,KAGA,IAAA4C,GAAAoE,EAAA/B,EAEA,OAAAjF,IAAA4C,EACA7L,IAAAoP,EACAlhB,EAAAojB,oBAAApD,EAAA7R,GAGAnO,EAAAqjB,2BAAArD,EAAA7R,GAGA6R,EAAAhc,WAcAud,GAAAvB,EAAAkD,EAAA,SAAA3b,EAAA+b,GACA,MAAAnV,GAAAmV,EAAAjG,KAAA,OAdAlP,EAAA,OA8CAnO,EAAA8f,cAAA,SAAAhS,EAAA2K,EAAAtK,GACA,mBAAAL,GACA,MAAA9N,GAAAujB,sBAAAzV,EAAA2K,EAAAtK,EAGA,mBAAAsK,KACAtK,EAAAsK,EACAA,EAAA,KAGA,IAAA/J,EACA,SAAAZ,EAEA,MAAAK,GAAA5G,EAAA,IAKA,QAFAmF,GAAAiD,EAAA3L,EAAA,GAEAE,EAAA,EAAAwX,EAAA5N,EAAA9J,OAAkCE,EAAAwX,EAAOxX,IAAA,CACzC,GAAAsf,GAAA1V,EAAA7K,OAAAiB,EAEA,UAAAsf,EAAA,CAKA,QAAAxf,OAAA0I,EAAAsC,OAAAhL,IAEA,MAAAmK,GAAA5G,EAAA,IAKA,IAFAoI,EAAA7B,EAAA1B,OAAAlI,EAAA,EAAAwI,GAEA1I,GAAA2L,EAAA3L,OAEA,MAAAmK,GAAA5G,EAAA,IAGA,IAAAoI,EAAA3L,OAAA,CAGA,GAFA0K,EAAA1O,EAAAsgB,aAAA3Q,EAAA8I,GAAA,GAEAlR,EAAA/C,OAAAkK,EAAAlK,MAAA+C,EAAAuG,OAAAY,EAAAZ,KAEA,MAAAK,GAAA5G,EAAA,IAGA,IAAAkc,GAAAtV,EAAAO,EAAAxK,EAAAwI,EAAAgP,EACA,SAAA+H,EAAA,OAIAvf,GAAAwI,EACA1I,EAAA,OA9BAA,IAAAwf,EAiCA,WAAAxf,EAEAmK,EAAA5G,EAAA,KAFA,QAqBAvH,EAAAqjB,2BAAA,SAAArD,EAAA7R,GAKA,QAAA+U,GAAAxU,EAAAyU,GACAnjB,EAAAghB,aAAAtS,GAAA,cAAAZ,GACA,MAAAqV,GAAA,KAAArV,KANA,MAAAkS,GAAAhc,WAUAud,GAAAvB,EAAAkD,EAAA,SAAA3b,EAAAkP,GACA,GAAAiN,GAAAjN,EAAAkN,OAAA,SAAAC,EAAA9iB,GACA,GAAA+H,EAMA,OAJAA,GADA,gBAAA/H,GACAA,EAAAkD,OAEAlD,EAAA+f,WAEA+C,EAAA/a,EAAA8I,WAAA3N,OAAA6E,EAAA,GACK,GAELgb,EAAA,GAAAlD,YAAA+C,GAEAI,EAAA,CA8BA,OA7BArN,GAAAsN,QAAA,SAAAjjB,GACA,GAAAkjB,GAAA,gBAAAljB,GACAmjB,EAAAnjB,CACA,IAAAkjB,EAAA,CAEA,OADAE,GAAA,GAAAvD,YAAA7f,EAAAkD,QACAE,EAAA,EAAuBA,EAAApD,EAAAkD,OAAcE,IACrCggB,EAAAhgB,GAAApD,EAAA8J,WAAA1G,EAEA+f,GAAAC,EAAAhR,OAGA8Q,EACAH,EAAAC,KAAA,EAEAD,EAAAC,KAAA,CAIA,QADAK,GAAAF,EAAApD,WAAAlP,WACAzN,EAAA,EAAqBA,EAAAigB,EAAAngB,OAAmBE,IACxC2f,EAAAC,KAAAze,SAAA8e,EAAAjgB,GAEA2f,GAAAC,KAAA,GAGA,QADAI,GAAA,GAAAvD,YAAAsD,GACA/f,EAAA,EAAqBA,EAAAggB,EAAAlgB,OAAiBE,IACtC2f,EAAAC,KAAAI,EAAAhgB,KAIAiK,EAAA0V,EAAA3Q,UApDA/E,EAAA,GAAA6E,aAAA,KA4DAhT,EAAAojB,oBAAA,SAAApD,EAAA7R,GACA,QAAA+U,GAAAxU,EAAAyU,GACAnjB,EAAAghB,aAAAtS,GAAA,cAAA4T,GACA,GAAA8B,GAAA,GAAAzD,YAAA,EAEA,IADAyD,EAAA,KACA,gBAAA9B,GAAA,CAEA,OADA4B,GAAA,GAAAvD,YAAA2B,EAAAte,QACAE,EAAA,EAAuBA,EAAAoe,EAAAte,OAAoBE,IAC3CggB,EAAAhgB,GAAAoe,EAAA1X,WAAA1G,EAEAoe,GAAA4B,EAAAhR,OACAkR,EAAA,KASA,OANAvb,GAAAyZ,YAAAtP,aACAsP,EAAAzB,WACAyB,EAAA+B,KAEAF,EAAAtb,EAAA8I,WACA2S,EAAA,GAAA3D,YAAAwD,EAAAngB,OAAA,GACAE,EAAA,EAAqBA,EAAAigB,EAAAngB,OAAmBE,IACxCogB,EAAApgB,GAAAmB,SAAA8e,EAAAjgB,GAIA,IAFAogB,EAAAH,EAAAngB,QAAA,IAEA8N,EAAA,CACA,GAAAqP,GAAA,GAAArP,IAAAsS,EAAAlR,OAAAoR,EAAApR,OAAAoP,GACAa,GAAA,KAAAhC,MAKAI,EAAAvB,EAAAkD,EAAA,SAAA3b,EAAA+b,GACA,MAAAnV,GAAA,GAAA2D,GAAAwR,OAaAtjB,EAAAujB,sBAAA,SAAAzV,EAAA2K,EAAAtK,GACA,kBAAAsK,KACAtK,EAAAsK,EACAA,EAAA,KAMA,KAHA,GAAA8L,GAAAzW,EACAa,KAEA4V,EAAA1D,WAAA,IAKA,OAJA2D,GAAA,GAAA7D,YAAA4D,GACAP,EAAA,IAAAQ,EAAA,GACAC,EAAA,GAEAvgB,EAAA,EACA,MAAAsgB,EAAAtgB,GADqBA,IAAA,CAIrB,GAAAugB,EAAAzgB,OAAA,IACA,MAAAmK,GAAA5G,EAAA,IAGAkd,IAAAD,EAAAtgB,GAGAqgB,EAAAvC,EAAAuC,EAAA,EAAAE,EAAAzgB,QACAygB,EAAApf,SAAAof,EAEA,IAAA9U,GAAAqS,EAAAuC,EAAA,EAAAE,EACA,IAAAT,EACA,IACArU,EAAAlD,OAAAgW,aAAAtc,MAAA,QAAAwa,YAAAhR,IACO,MAAA9L,GAEP,GAAA6e,GAAA,GAAA/B,YAAAhR,EACAA,GAAA,EACA,QAAAzL,GAAA,EAAuBA,EAAAwe,EAAA1e,OAAkBE,IACzCyL,GAAAlD,OAAAgW,aAAAC,EAAAxe,IAKAyK,EAAAvF,KAAAuG,GACA4U,EAAAvC,EAAAuC,EAAAE,GAGA,GAAA5E,GAAAlR,EAAA3K,MACA2K,GAAAoV,QAAA,SAAA7Q,EAAAhP,GACAiK,EAAAnO,EAAAsgB,aAAApN,EAAAuF,GAAA,GAAAvU,EAAA2b,QvB6jI8Blf,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,GwBppJxBC,EAAAD,QAAA4R,OAAA0H,MAAA,SAAAjX,GACA,GAAAsQ,MACA8B,EAAA7C,OAAArP,UAAAmS,cAEA,QAAAxQ,KAAA7B,GACAoS,EAAA9T,KAAA0B,EAAA6B,IACAyO,EAAAvJ,KAAAlF,EAGA,OAAAyO,KxBoqJM,SAAU1S,EAAQD,EAASM,IyBrrJjC,SAAAuC,GA2BA,QAAAkf,GAAA1f,GACA,IAAAA,GAAA,gBAAAA,GACA,QAGA,IAAAmN,EAAAnN,GAAA,CACA,OAAA6B,GAAA,EAAAwX,EAAArZ,EAAA2B,OAAmCE,EAAAwX,EAAOxX,IAC1C,GAAA6d,EAAA1f,EAAA6B,IACA,QAGA,UAGA,qBAAArB,GAAAgQ,QAAAhQ,EAAAgQ,OAAAC,UAAAjQ,EAAAgQ,OAAAC,SAAAzQ,IACA,kBAAAQ,GAAAmQ,aAAA3Q,YAAA2Q,cACAnB,GAAAxP,YAAAyP,OACAC,GAAA1P,YAAA2P,MAEA,QAIA,IAAA3P,EAAAqiB,QAAA,kBAAAriB,GAAAqiB,QAAA,IAAAte,UAAApC,OACA,MAAA+d,GAAA1f,EAAAqiB,UAAA,EAGA,QAAA/T,KAAAtO,GACA,GAAAuP,OAAArP,UAAAmS,eAAA/T,KAAA0B,EAAAsO,IAAAoR,EAAA1f,EAAAsO,IACA,QAIA,UAtDA,GAAAnB,GAAAlP,EAAA,IAEAqR,EAAAC,OAAArP,UAAAoP,SACAE,EAAA,kBAAAhP,GAAAiP,MAAA,6BAAAH,EAAAhR,KAAAkC,EAAAiP,MACAC,EAAA,kBAAAlP,GAAAmP,MAAA,6BAAAL,EAAAhR,KAAAkC,EAAAmP,KAMA/R,GAAAD,QAAA+hB,IzBsuJ8BphB,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,G0BnvJxBC,EAAAD,QAAA,SAAA2kB,EAAAC,EAAAC,GACA,GAAAC,GAAAH,EAAA9D,UAIA,IAHA+D,KAAA,EACAC,KAAAC,EAEAH,EAAAxT,MAA0B,MAAAwT,GAAAxT,MAAAyT,EAAAC,EAM1B,IAJAD,EAAA,IAAkBA,GAAAE,GAClBD,EAAA,IAAgBA,GAAAC,GAChBD,EAAAC,IAAoBD,EAAAC,GAEpBF,GAAAE,GAAAF,GAAAC,GAAA,IAAAC,EACA,UAAA9R,aAAA,EAKA,QAFA+R,GAAA,GAAApE,YAAAgE,GACAlS,EAAA,GAAAkO,YAAAkE,EAAAD,GACA1gB,EAAA0gB,EAAAI,EAAA,EAA6B9gB,EAAA2gB,EAAS3gB,IAAA8gB,IACtCvS,EAAAuS,GAAAD,EAAA7gB,EAEA,OAAAuO,GAAAS,S1BkwJM,SAAUjT,EAAQD,G2B3xJxB,QAAA2hB,GAAAsD,EAAA9W,EAAA+W,GAOA,QAAAC,GAAA5d,EAAAkL,GACA,GAAA0S,EAAAF,OAAA,EACA,SAAAtd,OAAA,iCAEAwd,EAAAF,MAGA1d,GACA6d,GAAA,EACAjX,EAAA5G,GAEA4G,EAAA+W,GACS,IAAAC,EAAAF,OAAAG,GACTjX,EAAA,KAAAsE,GAnBA,GAAA2S,IAAA,CAIA,OAHAF,MAAAjc,EACAkc,EAAAF,QAEA,IAAAA,EAAA9W,IAAAgX,EAoBA,QAAAlc,MA3BAhJ,EAAAD,QAAA2hB,G3B+zJM,SAAU1hB,EAAQD,EAASM,GAEhC,GAAI+kB,I4Bj0JL,SAAAplB,EAAA4C,IACC,SAAA/C,GAqBD,QAAAwlB,GAAAC,GAMA,IALA,GAGAC,GACAC,EAJAC,KACAC,EAAA,EACA3hB,EAAAuhB,EAAAvhB,OAGA2hB,EAAA3hB,GACAwhB,EAAAD,EAAA3a,WAAA+a,KACAH,GAAA,OAAAA,GAAA,OAAAG,EAAA3hB,GAEAyhB,EAAAF,EAAA3a,WAAA+a,KACA,cAAAF,GACAC,EAAAtc,OAAA,KAAAoc,IAAA,UAAAC,GAAA,QAIAC,EAAAtc,KAAAoc,GACAG,MAGAD,EAAAtc,KAAAoc,EAGA,OAAAE,GAIA,QAAAE,GAAA5c,GAKA,IAJA,GAEAwc,GAFAxhB,EAAAgF,EAAAhF,OACA+B,GAAA,EAEA2f,EAAA,KACA3f,EAAA/B,GACAwhB,EAAAxc,EAAAjD,GACAyf,EAAA,QACAA,GAAA,MACAE,GAAAG,EAAAL,IAAA,eACAA,EAAA,WAAAA,GAEAE,GAAAG,EAAAL,EAEA,OAAAE,GAGA,QAAAI,GAAAC,EAAAzE,GACA,GAAAyE,GAAA,OAAAA,GAAA,OACA,GAAAzE,EACA,KAAA3Z,OACA,oBAAAoe,EAAApU,SAAA,IAAAqU,cACA,yBAGA,UAEA,SAIA,QAAAC,GAAAF,EAAAnP,GACA,MAAAiP,GAAAE,GAAAnP,EAAA,QAGA,QAAAsP,GAAAH,EAAAzE,GACA,kBAAAyE,GACA,MAAAF,GAAAE,EAEA,IAAAI,GAAA,EAiBA,OAhBA,gBAAAJ,GACAI,EAAAN,EAAAE,GAAA,UAEA,eAAAA,IACAD,EAAAC,EAAAzE,KACAyE,EAAA,OAEAI,EAAAN,EAAAE,GAAA,WACAI,GAAAF,EAAAF,EAAA,IAEA,eAAAA,KACAI,EAAAN,EAAAE,GAAA,UACAI,GAAAF,EAAAF,EAAA,IACAI,GAAAF,EAAAF,EAAA,IAEAI,GAAAN,EAAA,GAAAE,EAAA,KAIA,QAAA1D,GAAAkD,EAAAtkB,GACAA,OAQA,KAPA,GAKA8kB,GALAzE,GAAA,IAAArgB,EAAAqgB,OAEA8E,EAAAd,EAAAC,GACAvhB,EAAAoiB,EAAApiB,OACA+B,GAAA,EAEAsgB,EAAA,KACAtgB,EAAA/B,GACA+hB,EAAAK,EAAArgB,GACAsgB,GAAAH,EAAAH,EAAAzE,EAEA,OAAA+E,GAKA,QAAAC,KACA,GAAAC,GAAAC,EACA,KAAA7e,OAAA,qBAGA,IAAA8e,GAAA,IAAAC,EAAAH,EAGA,IAFAA,IAEA,UAAAE,GACA,UAAAA,CAIA,MAAA9e,OAAA,6BAGA,QAAAgf,GAAArF,GACA,GAAAsF,GACAC,EACAC,EACAC,EACAhB,CAEA,IAAAQ,EAAAC,EACA,KAAA7e,OAAA,qBAGA,IAAA4e,GAAAC,EACA,QAQA,IAJAI,EAAA,IAAAF,EAAAH,GACAA,IAGA,QAAAK,GACA,MAAAA,EAIA,cAAAA,GAAA,CAGA,GAFAC,EAAAP,IACAP,GAAA,GAAAa,IAAA,EAAAC,EACAd,GAAA,IACA,MAAAA,EAEA,MAAApe,OAAA,6BAKA,aAAAif,GAAA,CAIA,GAHAC,EAAAP,IACAQ,EAAAR,IACAP,GAAA,GAAAa,IAAA,GAAAC,GAAA,EAAAC,EACAf,GAAA,KACA,MAAAD,GAAAC,EAAAzE,GAAAyE,EAAA,KAEA,MAAApe,OAAA,6BAKA,aAAAif,KACAC,EAAAP,IACAQ,EAAAR,IACAS,EAAAT,IACAP,GAAA,EAAAa,IAAA,GAAAC,GAAA,GACAC,GAAA,EAAAC,EACAhB,GAAA,OAAAA,GAAA,SACA,MAAAA,EAIA,MAAApe,OAAA,0BAMA,QAAAkb,GAAAwD,EAAAplB,GACAA,OACA,IAAAqgB,IAAA,IAAArgB,EAAAqgB,MAEAoF,GAAApB,EAAAe,GACAG,EAAAE,EAAA1iB,OACAuiB,EAAA,CAGA,KAFA,GACAS,GADAZ,MAEAY,EAAAL,EAAArF,OAAA,GACA8E,EAAAhd,KAAA4d,EAEA,OAAApB,GAAAQ,GAvNA,GAAAa,GAAA,gBAAAjnB,MAQAoZ,GALA,gBAAAnZ,OACAA,EAAAD,SAAAinB,GAAAhnB,EAIA,gBAAA4C,MACAuW,GAAAvW,SAAAuW,KAAA7U,SAAA6U,IACAtZ,EAAAsZ,EAKA,IAyLAsN,GACAF,EACAD,EA3LAV,EAAApZ,OAAAgW,aA6MApB,GACA7X,QAAA,QACA0G,OAAAmS,EACA1K,OAAAkL,EAUAwC,GAAA,WACA,MAAAhE,IACG1gB,KAAAX,EAAAM,EAAAN,EAAAC,KAAAkB,SAAAkkB,IAAAplB,EAAAD,QAAAqlB,KAeFjlB,Q5Bi0J6BO,KAAKX,EAASM,EAAoB,IAAIL,GAAU,WAAa,MAAOG,WAI5F,SAAUH,EAAQD,G6BnkKxBC,EAAAD,QAAA,SAAAC,GAQA,MAPAA,GAAAinB,kBACAjnB,EAAAknB,UAAA,aACAlnB,EAAAmnB,SAEAnnB,EAAAonB,YACApnB,EAAAinB,gBAAA,GAEAjnB,I7B2kKM,SAAUA,EAAQD,I8B5kKxB,WACA,YAMA,QAJAsnB,GAAA,mEAGAvmB,EAAA,GAAA4f,YAAA,KACAzc,EAAA,EAAiBA,EAAAojB,EAAAtjB,OAAkBE,IACnCnD,EAAAumB,EAAA1c,WAAA1G,KAGAlE,GAAAkQ,OAAA,SAAAyU,GACA,GACAzgB,GADA4gB,EAAA,GAAAnE,YAAAgE,GACA9b,EAAAic,EAAA9gB,OAAAqM,EAAA,EAEA,KAAAnM,EAAA,EAAeA,EAAA2E,EAAS3E,GAAA,EACxBmM,GAAAiX,EAAAxC,EAAA5gB,IAAA,GACAmM,GAAAiX,GAAA,EAAAxC,EAAA5gB,KAAA,EAAA4gB,EAAA5gB,EAAA,OACAmM,GAAAiX,GAAA,GAAAxC,EAAA5gB,EAAA,OAAA4gB,EAAA5gB,EAAA,OACAmM,GAAAiX,EAAA,GAAAxC,EAAA5gB,EAAA,GASA,OANA2E,GAAA,MACAwH,IAAAvM,UAAA,EAAAuM,EAAArM,OAAA,OACK6E,EAAA,QACLwH,IAAAvM,UAAA,EAAAuM,EAAArM,OAAA,SAGAqM,GAGArQ,EAAA2X,OAAA,SAAAtH,GACA,GACAnM,GACAqjB,EAAAC,EAAAC,EAAAC,EAFAC,EAAA,IAAAtX,EAAArM,OACA6E,EAAAwH,EAAArM,OAAAlD,EAAA,CAGA,OAAAuP,IAAArM,OAAA,KACA2jB,IACA,MAAAtX,IAAArM,OAAA,IACA2jB,IAIA,IAAAhD,GAAA,GAAA3R,aAAA2U,GACA7C,EAAA,GAAAnE,YAAAgE,EAEA,KAAAzgB,EAAA,EAAeA,EAAA2E,EAAS3E,GAAA,EACxBqjB,EAAAxmB,EAAAsP,EAAAzF,WAAA1G,IACAsjB,EAAAzmB,EAAAsP,EAAAzF,WAAA1G,EAAA,IACAujB,EAAA1mB,EAAAsP,EAAAzF,WAAA1G,EAAA,IACAwjB,EAAA3mB,EAAAsP,EAAAzF,WAAA1G,EAAA,IAEA4gB,EAAAhkB,KAAAymB,GAAA,EAAAC,GAAA,EACA1C,EAAAhkB,MAAA,GAAA0mB,IAAA,EAAAC,GAAA,EACA3C,EAAAhkB,MAAA,EAAA2mB,IAAA,KAAAC,CAGA,OAAA/C,Q9B4lKM,SAAU1kB,EAAQD,I+B5pKxB,SAAA6C,GAkDA,QAAA+kB,GAAApG,GACA,OAAAtd,GAAA,EAAiBA,EAAAsd,EAAAxd,OAAgBE,IAAA,CACjC,GAAA2jB,GAAArG,EAAAtd,EACA,IAAA2jB,EAAA3U,iBAAAF,aAAA,CACA,GAAA7D,GAAA0Y,EAAA3U,MAIA,IAAA2U,EAAAhH,aAAA1R,EAAA0R,WAAA,CACA,GAAAiH,GAAA,GAAAnH,YAAAkH,EAAAhH,WACAiH,GAAAC,IAAA,GAAApH,YAAAxR,EAAA0Y,EAAAG,WAAAH,EAAAhH,aACA1R,EAAA2Y,EAAA5U,OAGAsO,EAAAtd,GAAAiL,IAKA,QAAA8Y,GAAAzG,EAAAlU,GACAA,OAEA,IAAA4a,GAAA,GAAAC,EACAP,GAAApG,EAEA,QAAAtd,GAAA,EAAiBA,EAAAsd,EAAAxd,OAAgBE,IACjCgkB,EAAAE,OAAA5G,EAAAtd,GAGA,OAAAoJ,GAAA,KAAA4a,EAAAG,QAAA/a,EAAA9I,MAAA0jB,EAAAG,UAGA,QAAAC,GAAA9G,EAAAlU,GAEA,MADAsa,GAAApG,GACA,GAAA1P,MAAA0P,EAAAlU,OAhFA,GAAA6a,GAAAtlB,EAAAslB,aACAtlB,EAAA0lB,mBACA1lB,EAAA2lB,eACA3lB,EAAA4lB,eAMAC,EAAA,WACA,IACA,GAAAC,GAAA,GAAA7W,OAAA,MACA,YAAA6W,EAAAtE,KACG,MAAAxgB,GACH,aASA+kB,EAAAF,GAAA,WACA,IACA,GAAA9kB,GAAA,GAAAkO,OAAA,GAAA6O,aAAA,OACA,YAAA/c,EAAAygB,KACG,MAAAxgB,GACH,aAQAglB,EAAAV,GACAA,EAAA5lB,UAAA6lB,QACAD,EAAA5lB,UAAA8lB,OA6CApoB,GAAAD,QAAA,WACA,MAAA0oB,GACAE,EAAA/lB,EAAAiP,KAAAwW,EACGO,EACHZ,EAEA,Y/BkqK8BtnB,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,GgC3vKxBA,EAAAkQ,OAAA,SAAA7N,GACA,GAAAqB,GAAA,EAEA,QAAAQ,KAAA7B,GACAA,EAAAqS,eAAAxQ,KACAR,EAAAM,SAAAN,GAAA,KACAA,GAAAolB,mBAAA5kB,GAAA,IAAA4kB,mBAAAzmB,EAAA6B,IAIA,OAAAR,IAUA1D,EAAA2X,OAAA,SAAAoR,GAGA,OAFAC,MACAC,EAAAF,EAAA5c,MAAA,KACAjI,EAAA,EAAAwX,EAAAuN,EAAAjlB,OAAmCE,EAAAwX,EAAOxX,IAAA,CAC1C,GAAAglB,GAAAD,EAAA/kB,GAAAiI,MAAA,IACA6c,GAAAG,mBAAAD,EAAA,KAAAC,mBAAAD,EAAA,IAEA,MAAAF,KhC2wKM,SAAU/oB,EAAQD,GiC7yKxBC,EAAAD,QAAA,SAAA2oB,EAAA/kB,GACA,GAAAkN,GAAA,YACAA,GAAAvO,UAAAqB,EAAArB,UACAomB,EAAApmB,UAAA,GAAAuO,GACA6X,EAAApmB,UAAAD,YAAAqmB,IjCqzKM,SAAU1oB,EAAQD,GkC1zKxB,YAgBA,SAAAkQ,GAAAsB,GACA,GAAA8Q,GAAA,EAEA,GACAA,GAAA8G,EAAA5X,EAAAxN,GAAAse,EACA9Q,EAAA3G,KAAAuC,MAAAoE,EAAAxN,SACGwN,EAAA,EAEH,OAAA8Q,GAUA,QAAA3K,GAAAjU,GACA,GAAA2lB,GAAA,CAEA,KAAAnlB,EAAA,EAAaA,EAAAR,EAAAM,OAAgBE,IAC7BmlB,IAAArlB,EAAAud,EAAA7d,EAAAT,OAAAiB,GAGA,OAAAmlB,GASA,QAAA5J,KACA,GAAA6J,GAAApZ,GAAA,GAAA/E,MAEA,OAAAme,KAAAhe,GAAAie,EAAA,EAAAje,EAAAge,GACAA,EAAA,IAAApZ,EAAAqZ,KAMA,IA1DA,GAKAje,GALA8d,EAAA,mEAAAjd,MAAA,IACAnI,EAAA,GACAud,KACAgI,EAAA,EACArlB,EAAA,EAsDMA,EAAAF,EAAYE,IAAAqd,EAAA6H,EAAAllB,KAKlBub,GAAAvP,SACAuP,EAAA9H,SACA1X,EAAAD,QAAAyf,GlCi0KM,SAAUxf,EAAQD,EAASM,IAEJ,SAASuC,GmCv2KtC,QAAAya,MASA,QAAAkM,GAAAvoB,GACAsc,EAAA5c,KAAAP,KAAAa,GAEAb,KAAA6B,MAAA7B,KAAA6B,UAIAgP,IAEApO,EAAA4mB,SAAA5mB,EAAA4mB,WACAxY,EAAApO,EAAA4mB,QAIArpB,KAAA2F,MAAAkL,EAAAjN,MAGA,IAAAiH,GAAA7K,IACA6Q,GAAA7H,KAAA,SAAAuG,GACA1E,EAAAqT,OAAA3O,KAIAvP,KAAA6B,MAAAkF,EAAA/G,KAAA2F,MAGAlD,EAAAgC,UAAAhC,EAAA+N,kBACA/N,EAAA+N,iBAAA,0BACA3F,EAAAye,SAAAze,EAAAye,OAAAnT,QAAA+G,KACK,GAhEL,GAAAC,GAAAjd,EAAA,IACA0d,EAAA1d,EAAA,GAMAL,GAAAD,QAAAwpB,CAMA,IAOAvY,GAPA0Y,EAAA,MACAC,EAAA,MA0DA5L,GAAAwL,EAAAjM,GAMAiM,EAAAjnB,UAAAwY,gBAAA,EAQAyO,EAAAjnB,UAAAwd,QAAA,WACA3f,KAAAspB,SACAtpB,KAAAspB,OAAAG,WAAAC,YAAA1pB,KAAAspB,QACAtpB,KAAAspB,OAAA,MAGAtpB,KAAA2pB,OACA3pB,KAAA2pB,KAAAF,WAAAC,YAAA1pB,KAAA2pB,MACA3pB,KAAA2pB,KAAA,KACA3pB,KAAA4pB,OAAA,MAGAzM,EAAAhb,UAAAwd,QAAApf,KAAAP,OASAopB,EAAAjnB,UAAA8b,OAAA,WACA,GAAApT,GAAA7K,KACAspB,EAAA7kB,SAAAolB,cAAA,SAEA7pB,MAAAspB,SACAtpB,KAAAspB,OAAAG,WAAAC,YAAA1pB,KAAAspB,QACAtpB,KAAAspB,OAAA,MAGAA,EAAAhM,OAAA,EACAgM,EAAA/lB,IAAAvD,KAAAY,MACA0oB,EAAAnT,QAAA,SAAA1S,GACAoH,EAAAyP,QAAA,mBAAA7W,GAGA,IAAAqmB,GAAArlB,SAAAslB,qBAAA,YACAD,GACAA,EAAAL,WAAAO,aAAAV,EAAAQ,IAEArlB,SAAAwlB,MAAAxlB,SAAAylB,MAAAC,YAAAb,GAEAtpB,KAAAspB,QAEA,IAAAc,GAAA,mBAAA/lB,YAAA,SAAAvB,KAAAuB,UAAAC,UAEA8lB,IACAxiB,WAAA,WACA,GAAAgiB,GAAAnlB,SAAAolB,cAAA,SACAplB,UAAAylB,KAAAC,YAAAP,GACAnlB,SAAAylB,KAAAR,YAAAE,IACK,MAYLR,EAAAjnB,UAAA2b,QAAA,SAAApQ,EAAAgD,GA0BA,QAAA2Z,KACAC,IACA5Z,IAGA,QAAA4Z,KACA,GAAAzf,EAAA+e,OACA,IACA/e,EAAA8e,KAAAD,YAAA7e,EAAA+e,QACO,MAAAnmB,GACPoH,EAAAyP,QAAA,qCAAA7W,GAIA,IAEA,GAAA8mB,GAAA,oCAAA1f,EAAA2f,SAAA,IACAZ,GAAAnlB,SAAAolB,cAAAU,GACK,MAAA9mB,GACLmmB,EAAAnlB,SAAAolB,cAAA,UACAD,EAAA5f,KAAAa,EAAA2f,SACAZ,EAAArmB,IAAA,eAGAqmB,EAAAvpB,GAAAwK,EAAA2f,SAEA3f,EAAA8e,KAAAQ,YAAAP,GACA/e,EAAA+e,SApDA,GAAA/e,GAAA7K,IAEA,KAAAA,KAAA2pB,KAAA,CACA,GAGAC,GAHAD,EAAAllB,SAAAolB,cAAA,QACAY,EAAAhmB,SAAAolB,cAAA,YACAxpB,EAAAL,KAAAwqB,SAAA,cAAAxqB,KAAA2F,KAGAgkB,GAAAe,UAAA,WACAf,EAAAhlB,MAAAgmB,SAAA,WACAhB,EAAAhlB,MAAAimB,IAAA,UACAjB,EAAAhlB,MAAAkmB,KAAA,UACAlB,EAAAmB,OAAAzqB,EACAspB,EAAAtM,OAAA,OACAsM,EAAAoB,aAAA,0BACAN,EAAAzgB,KAAA,IACA2f,EAAAQ,YAAAM,GACAhmB,SAAAylB,KAAAC,YAAAR,GAEA3pB,KAAA2pB,OACA3pB,KAAAyqB,OAGAzqB,KAAA2pB,KAAAqB,OAAAhrB,KAAAY,MAgCA0pB,IAIA5c,IAAA/J,QAAA6lB,EAAA,QACAxpB,KAAAyqB,KAAArF,MAAA1X,EAAA/J,QAAA4lB,EAAA,MAEA,KACAvpB,KAAA2pB,KAAAsB,SACG,MAAAxnB,IAEHzD,KAAA4pB,OAAAzK,YACAnf,KAAA4pB,OAAAlL,mBAAA,WACA,aAAA7T,EAAA+e,OAAAjW,YACA0W,KAIArqB,KAAA4pB,OAAAxX,OAAAiY,KnC04K8B9pB,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,IoClnLjC,SAAAuC,GA0CA,QAAAyoB,GAAArqB,GACA,GAAA8W,GAAA9W,KAAA8W,WACAA,KACA3X,KAAA2a,gBAAA,GAEA3a,KAAAuY,kBAAA1X,EAAA0X,kBACAvY,KAAAmrB,sBAAAC,IAAAvqB,EAAAkY,UACA/Y,KAAAka,UAAArZ,EAAAqZ,UACAla,KAAAmrB,wBACAE,EAAAC,GAEA1R,EAAArZ,KAAAP,KAAAa,GAjDA,GAOAyqB,GAPA1R,EAAA1Z,EAAA,IACAkC,EAAAlC,EAAA,IACAoX,EAAApX,EAAA,IACA0d,EAAA1d,EAAA,IACAmf,EAAAnf,EAAA,IACAyB,EAAAzB,EAAA,iCACAkrB,EAAA3oB,EAAA4oB,WAAA5oB,EAAA8oB,YAEA,uBAAApnB,QACA,IACAmnB,EAAAprB,EAAA,IACG,MAAAuD,IASH,GAAA4nB,GAAAD,CACAC,IAAA,mBAAAlnB,UACAknB,EAAAC,GAOAzrB,EAAAD,QAAAsrB,EA2BAtN,EAAAsN,EAAAtR,GAQAsR,EAAA/oB,UAAA6H,KAAA,YAMAkhB,EAAA/oB,UAAAwY,gBAAA,EAQAuQ,EAAA/oB,UAAAmd,OAAA,WACA,GAAAtf,KAAAwrB,QAAA,CAKA,GAAA5qB,GAAAZ,KAAAY,MACAsZ,EAAAla,KAAAka,UACArZ,GACAwW,MAAArX,KAAAqX,MACAkB,kBAAAvY,KAAAuY,kBAIA1X,GAAA4X,IAAAzY,KAAAyY,IACA5X,EAAA0P,IAAAvQ,KAAAuQ,IACA1P,EAAA6X,WAAA1Y,KAAA0Y,WACA7X,EAAA8X,KAAA3Y,KAAA2Y,KACA9X,EAAA+X,GAAA5Y,KAAA4Y,GACA/X,EAAAgY,QAAA7Y,KAAA6Y,QACAhY,EAAAiY,mBAAA9Y,KAAA8Y,mBACA9Y,KAAAiZ,eACApY,EAAA4qB,QAAAzrB,KAAAiZ,cAEAjZ,KAAAmZ,eACAtY,EAAAsY,aAAAnZ,KAAAmZ,aAGA,KACAnZ,KAAA0rB,GAAA1rB,KAAAmrB,sBAAAjR,EAAA,GAAAmR,GAAAzqB,EAAAsZ,GAAA,GAAAmR,GAAAzqB,GAAA,GAAAyqB,GAAAzqB,EAAAsZ,EAAArZ,GACG,MAAAsG,GACH,MAAAnH,MAAA4J,KAAA,QAAAzC,GAGApG,SAAAf,KAAA0rB,GAAArT,aACArY,KAAA2a,gBAAA,GAGA3a,KAAA0rB,GAAAC,UAAA3rB,KAAA0rB,GAAAC,SAAAxd,QACAnO,KAAA2a,gBAAA,EACA3a,KAAA0rB,GAAArT,WAAA,cAEArY,KAAA0rB,GAAArT,WAAA,cAGArY,KAAA4rB,sBASAV,EAAA/oB,UAAAypB,kBAAA,WACA,GAAA/gB,GAAA7K,IAEAA,MAAA0rB,GAAAhW,OAAA,WACA7K,EAAAwQ,UAEArb,KAAA0rB,GAAA7U,QAAA,WACAhM,EAAA0P,WAEAva,KAAA0rB,GAAAG,UAAA,SAAAC,GACAjhB,EAAAqT,OAAA4N,EAAApe,OAEA1N,KAAA0rB,GAAAvV,QAAA,SAAA1S,GACAoH,EAAAyP,QAAA,kBAAA7W,KAWAynB,EAAA/oB,UAAAmU,MAAA,SAAAsJ,GA4CA,QAAA0B,KACAzW,EAAAjB,KAAA,SAIAhC,WAAA,WACAiD,EAAAiR,UAAA,EACAjR,EAAAjB,KAAA,UACK,GAnDL,GAAAiB,GAAA7K,IACAA,MAAA8b,UAAA,CAKA,QADA2D,GAAAG,EAAAhc,OACAE,EAAA,EAAAwX,EAAAmE,EAA4B3b,EAAAwX,EAAOxX,KACnC,SAAAwK,GACAlM,EAAAwe,aAAAtS,EAAAzD,EAAA8P,eAAA,SAAAjN,GACA,IAAA7C,EAAAsgB,sBAAA,CAEA,GAAAtqB,KAKA,IAJAyN,EAAApB,UACArM,EAAAkb,SAAAzN,EAAApB,QAAA6O,UAGAlR,EAAA0N,kBAAA,CACA,GAAA9P,GAAA,gBAAAiF,GAAAjL,EAAAgQ,OAAAgO,WAAA/S,KAAA9J,MACA6E,GAAAoC,EAAA0N,kBAAAC,YACA3X,EAAAkb,UAAA,IAQA,IACAlR,EAAAsgB,sBAEAtgB,EAAA6gB,GAAA7Q,KAAAnN,GAEA7C,EAAA6gB,GAAA7Q,KAAAnN,EAAA7M,GAES,MAAA4C,GACT9B,EAAA,2CAGA8d,GAAA6B,OAEK1B,EAAA9b,KAqBLonB,EAAA/oB,UAAAoY,QAAA,WACAX,EAAAzX,UAAAoY,QAAAha,KAAAP,OASAkrB,EAAA/oB,UAAAwd,QAAA,WACA,mBAAA3f,MAAA0rB,IACA1rB,KAAA0rB,GAAA5V,SAUAoV,EAAA/oB,UAAAvB,IAAA,WACA,GAAAiB,GAAA7B,KAAA6B,UACAke,EAAA/f,KAAAoX,OAAA,WACApU,EAAA,EAGAhD,MAAAgD,OAAA,QAAA+c,GAAA,MAAAnR,OAAA5O,KAAAgD,OACA,OAAA+c,GAAA,KAAAnR,OAAA5O,KAAAgD,SACAA,EAAA,IAAAhD,KAAAgD,MAIAhD,KAAA8X,oBACAjW,EAAA7B,KAAA6X,gBAAAwH,KAIArf,KAAA2a,iBACA9Y,EAAAme,IAAA,GAGAne,EAAAyV,EAAAxH,OAAAjO,GAGAA,EAAA+B,SACA/B,EAAA,IAAAA,EAGA,IAAAoB,GAAAjD,KAAAmX,SAAAjU,QAAA,SACA,OAAA6c,GAAA,OAAA9c,EAAA,IAAAjD,KAAAmX,SAAA,IAAAnX,KAAAmX,UAAAnU,EAAAhD,KAAAoB,KAAAS,GAUAqpB,EAAA/oB,UAAAqpB,MAAA,WACA,SAAAH,GAAA,gBAAAA,IAAArrB,KAAAgK,OAAAkhB,EAAA/oB,UAAA6H,SpCunL8BzJ,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,KAMlB,SAAUC,EAAQD,GqC55LxB,GAAAsD,aAEArD,GAAAD,QAAA,SAAA2S,EAAAtQ,GACA,GAAAiB,EAAA,MAAAqP,GAAArP,QAAAjB,EACA,QAAA6B,GAAA,EAAiBA,EAAAyO,EAAA3O,SAAgBE,EACjC,GAAAyO,EAAAzO,KAAA7B,EAAA,MAAA6B,EAEA,YrCo6LM,SAAUjE,EAAQD,EAASM,GAEhC,YsCv3LD,SAASsC,GAAQxB,EAAIyM,EAAK5M,GACxBb,KAAKgB,GAAKA,EACVhB,KAAKyN,IAAMA,EACXzN,KAAK+rB,KAAO/rB,KACZA,KAAKgsB,IAAM,EACXhsB,KAAKisB,QACLjsB,KAAKksB,iBACLlsB,KAAKmsB,cACLnsB,KAAKosB,WAAY,EACjBpsB,KAAKqsB,cAAe,EACpBrsB,KAAKssB,SACDzrB,GAAQA,EAAKgB,QACf7B,KAAK6B,MAAQhB,EAAKgB,OAEhB7B,KAAKgB,GAAGkT,aAAalU,KAAKmU,OtC22L/B,GAAIrT,GAA4B,kBAAXiB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,IsC36LnQG,EAASlC,EAAQ,GACjBsP,EAAUtP,EAAQ,GAClBqsB,EAAUrsB,EAAQ,IAClBoJ,EAAKpJ,EAAQ,IACbuL,EAAOvL,EAAQ,IACfyB,EAAQzB,EAAQ,GAAS,2BACzBoX,EAAUpX,EAAQ,IAClBssB,EAAStsB,EAAQ,GAMrBL,GAAOD,QAAUA,EAAU4C,CAS3B,IAAIiqB,IACFlqB,QAAS,EACTmqB,cAAe,EACfC,gBAAiB,EACjB/Y,WAAY,EACZ+C,WAAY,EACZ7H,MAAO,EACPyG,UAAW,EACXqX,kBAAmB,EACnBC,iBAAkB,EAClBC,gBAAiB,EACjBzX,aAAc,EACduG,KAAM,EACNmG,KAAM,GAOJnY,EAAO4F,EAAQrN,UAAUyH,IA6B7B4F,GAAQhN,EAAOL,WAQfK,EAAOL,UAAU4qB,UAAY,WAC3B,IAAI/sB,KAAK+S,KAAT,CAEA,GAAI/R,GAAKhB,KAAKgB,EACdhB,MAAK+S,MACHzJ,EAAGtI,EAAI,OAAQyK,EAAKzL,KAAM,WAC1BsJ,EAAGtI,EAAI,SAAUyK,EAAKzL,KAAM,aAC5BsJ,EAAGtI,EAAI,QAASyK,EAAKzL,KAAM,eAU/BwC,EAAOL,UAAUgS,KACjB3R,EAAOL,UAAUI,QAAU,WACzB,MAAIvC,MAAKosB,UAAkBpsB,MAE3BA,KAAK+sB,YACL/sB,KAAKgB,GAAGmT,OACJ,SAAWnU,KAAKgB,GAAG2S,YAAY3T,KAAK0V,SACxC1V,KAAK4J,KAAK,cACH5J,OAUTwC,EAAOL,UAAU0Y,KAAO,WACtB,GAAIxV,GAAOknB,EAAQvmB,UAGnB,OAFAX,GAAK+F,QAAQ,WACbpL,KAAK4J,KAAK7D,MAAM/F,KAAMqF,GACfrF,MAYTwC,EAAOL,UAAUyH,KAAO,SAAUkiB,GAChC,GAAIW,EAAOnY,eAAewX,GAExB,MADAliB,GAAK7D,MAAM/F,KAAMgG,WACVhG,IAGT,IAAIqF,GAAOknB,EAAQvmB,WACfsI,GACFlK,MAA6BrD,SAAtBf,KAAKssB,MAAMne,OAAuBnO,KAAKssB,MAAMne,OAASqe,EAAOnnB,IAASjD,EAAOkL,aAAelL,EAAOwN,MAC1GlC,KAAMrI,EAqBR,OAlBAiJ,GAAOpB,WACPoB,EAAOpB,QAAQ6O,UAAY/b,KAAKssB,QAAS,IAAUtsB,KAAKssB,MAAMvQ,SAG1D,kBAAsB1W,GAAKA,EAAKzB,OAAS,KAC3CjC,EAAM,iCAAkC3B,KAAKgsB,KAC7ChsB,KAAKisB,KAAKjsB,KAAKgsB,KAAO3mB,EAAK2nB,MAC3B1e,EAAOjO,GAAKL,KAAKgsB,OAGfhsB,KAAKosB,UACPpsB,KAAKsO,OAAOA,GAEZtO,KAAKmsB,WAAWnjB,KAAKsF,GAGvBtO,KAAKssB,SAEEtsB,MAUTwC,EAAOL,UAAUmM,OAAS,SAAUA,GAClCA,EAAOb,IAAMzN,KAAKyN,IAClBzN,KAAKgB,GAAGsN,OAAOA,IASjB9L,EAAOL,UAAUuT,OAAS,WAIxB,GAHA/T,EAAM,kCAGF,MAAQ3B,KAAKyN,IACf,GAAIzN,KAAK6B,MAAO,CACd,GAAIA,GAA8B,WAAtBf,EAAOd,KAAK6B,OAAqByV,EAAQxH,OAAO9P,KAAK6B,OAAS7B,KAAK6B,KAC/EF,GAAM,uCAAwCE,GAC9C7B,KAAKsO,QAAQlK,KAAMhC,EAAOsN,QAAS7N,MAAOA,QAE1C7B,MAAKsO,QAAQlK,KAAMhC,EAAOsN,WAYhClN,EAAOL,UAAU0U,QAAU,SAAUC,GACnCnV,EAAM,aAAcmV,GACpB9W,KAAKosB,WAAY,EACjBpsB,KAAKqsB,cAAe,QACbrsB,MAAKK,GACZL,KAAK4J,KAAK,aAAckN,IAU1BtU,EAAOL,UAAU8qB,SAAW,SAAU3e,GACpC,GAAIjN,GAAgBiN,EAAOb,MAAQzN,KAAKyN,IACpCyf,EAAqB5e,EAAOlK,OAAShC,EAAO+M,OAAwB,MAAfb,EAAOb,GAEhE,IAAKpM,GAAkB6rB,EAEvB,OAAQ5e,EAAOlK,MACb,IAAKhC,GAAOsN,QACV1P,KAAKmtB,WACL,MAEF,KAAK/qB,GAAOwN,MACV5P,KAAKotB,QAAQ9e,EACb,MAEF,KAAKlM,GAAOkL,aACVtN,KAAKotB,QAAQ9e,EACb,MAEF,KAAKlM,GAAOyN,IACV7P,KAAKqtB,MAAM/e,EACX,MAEF,KAAKlM,GAAOmL,WACVvN,KAAKqtB,MAAM/e,EACX,MAEF,KAAKlM,GAAOuN,WACV3P,KAAKstB,cACL,MAEF,KAAKlrB,GAAO+M,MACVnP,KAAK4J,KAAK,QAAS0E,EAAOZ,QAYhClL,EAAOL,UAAUirB,QAAU,SAAU9e,GACnC,GAAIjJ,GAAOiJ,EAAOZ,QAClB/L,GAAM,oBAAqB0D,GAEvB,MAAQiJ,EAAOjO,KACjBsB,EAAM,mCACN0D,EAAK2D,KAAKhJ,KAAKutB,IAAIjf,EAAOjO,MAGxBL,KAAKosB,UACPxiB,EAAK7D,MAAM/F,KAAMqF,GAEjBrF,KAAKksB,cAAcljB,KAAK3D,IAU5B7C,EAAOL,UAAUorB,IAAM,SAAUltB,GAC/B,GAAIwK,GAAO7K,KACPwtB,GAAO,CACX,OAAO,YAEL,IAAIA,EAAJ,CACAA,GAAO,CACP,IAAInoB,GAAOknB,EAAQvmB,UACnBrE,GAAM,iBAAkB0D,GAExBwF,EAAKyD,QACHlK,KAAMooB,EAAOnnB,GAAQjD,EAAOmL,WAAanL,EAAOyN,IAChDxP,GAAIA,EACJqN,KAAMrI,OAYZ7C,EAAOL,UAAUkrB,MAAQ,SAAU/e,GACjC,GAAIif,GAAMvtB,KAAKisB,KAAK3d,EAAOjO,GACvB,mBAAsBktB,IACxB5rB,EAAM,yBAA0B2M,EAAOjO,GAAIiO,EAAOZ,MAClD6f,EAAIxnB,MAAM/F,KAAMsO,EAAOZ,YAChB1N,MAAKisB,KAAK3d,EAAOjO,KAExBsB,EAAM,aAAc2M,EAAOjO,KAU/BmC,EAAOL,UAAUgrB,UAAY,WAC3BntB,KAAKosB,WAAY,EACjBpsB,KAAKqsB,cAAe,EACpBrsB,KAAK4J,KAAK,WACV5J,KAAKytB,gBASPjrB,EAAOL,UAAUsrB,aAAe,WAC9B,GAAI3pB,EACJ,KAAKA,EAAI,EAAGA,EAAI9D,KAAKksB,cAActoB,OAAQE,IACzC8F,EAAK7D,MAAM/F,KAAMA,KAAKksB,cAAcpoB,GAItC,KAFA9D,KAAKksB,iBAEApoB,EAAI,EAAGA,EAAI9D,KAAKmsB,WAAWvoB,OAAQE,IACtC9D,KAAKsO,OAAOtO,KAAKmsB,WAAWroB,GAE9B9D,MAAKmsB,eASP3pB,EAAOL,UAAUmrB,aAAe,WAC9B3rB,EAAM,yBAA0B3B,KAAKyN,KACrCzN,KAAK0L,UACL1L,KAAK6W,QAAQ,yBAWfrU,EAAOL,UAAUuJ,QAAU,WACzB,GAAI1L,KAAK+S,KAAM,CAEb,IAAK,GAAIjP,GAAI,EAAGA,EAAI9D,KAAK+S,KAAKnP,OAAQE,IACpC9D,KAAK+S,KAAKjP,GAAG4H,SAEf1L,MAAK+S,KAAO,KAGd/S,KAAKgB,GAAG0K,QAAQ1L,OAUlBwC,EAAOL,UAAU2T,MACjBtT,EAAOL,UAAUwU,WAAa,WAa5B,MAZI3W,MAAKosB,YACPzqB,EAAM,6BAA8B3B,KAAKyN,KACzCzN,KAAKsO,QAASlK,KAAMhC,EAAOuN,cAI7B3P,KAAK0L,UAED1L,KAAKosB,WAEPpsB,KAAK6W,QAAQ,wBAER7W,MAWTwC,EAAOL,UAAU4Z,SAAW,SAAUA,GAEpC,MADA/b,MAAKssB,MAAMvQ,SAAWA,EACf/b,MAWTwC,EAAOL,UAAUgM,OAAS,SAAUA,GAElC,MADAnO,MAAKssB,MAAMne,OAASA,EACbnO,OtCg7LH,SAAUH,EAAQD,GuCl2MxB,QAAA2sB,GAAAmB,EAAA/nB,GACA,GAAAiD,KAEAjD,MAAA,CAEA,QAAA7B,GAAA6B,GAAA,EAA4B7B,EAAA4pB,EAAA9pB,OAAiBE,IAC7C8E,EAAA9E,EAAA6B,GAAA+nB,EAAA5pB,EAGA,OAAA8E,GAXA/I,EAAAD,QAAA2sB,GvCu3MM,SAAU1sB,EAAQD,GAEvB,YwCz2MD,SAAS0J,GAAIrH,EAAK6pB,EAAIpb,GAEpB,MADAzO,GAAIqH,GAAGwiB,EAAIpb,IAEThF,QAAS,WACPzJ,EAAIyH,eAAeoiB,EAAIpb,KAf7B7Q,EAAOD,QAAU0J,GxCg5MX,SAAUzJ,EAAQD,GyCj5MxB,GAAAmR,WAWAlR,GAAAD,QAAA,SAAAqC,EAAAyO,GAEA,GADA,gBAAAA,OAAAzO,EAAAyO,IACA,kBAAAA,GAAA,SAAAnJ,OAAA,6BACA,IAAAlC,GAAA0L,EAAAxQ,KAAAyF,UAAA,EACA,mBACA,MAAA0K,GAAA3K,MAAA9D,EAAAoD,EAAAgD,OAAA0I,EAAAxQ,KAAAyF,gBzC85MM,SAAUnG,EAAQD,G0C/5MxB,QAAA2T,GAAA1S,GACAA,QACAb,KAAAgL,GAAAnK,EAAA2S,KAAA,IACAxT,KAAAyT,IAAA5S,EAAA4S,KAAA,IACAzT,KAAA2tB,OAAA9sB,EAAA8sB,QAAA,EACA3tB,KAAA0T,OAAA7S,EAAA6S,OAAA,GAAA7S,EAAA6S,QAAA,EAAA7S,EAAA6S,OAAA,EACA1T,KAAAsV,SAAA,EApBAzV,EAAAD,QAAA2T,EA8BAA,EAAApR,UAAA6U,SAAA,WACA,GAAAhM,GAAAhL,KAAAgL,GAAAP,KAAAmjB,IAAA5tB,KAAA2tB,OAAA3tB,KAAAsV,WACA,IAAAtV,KAAA0T,OAAA,CACA,GAAAma,GAAApjB,KAAAqjB,SACAC,EAAAtjB,KAAAuC,MAAA6gB,EAAA7tB,KAAA0T,OAAA1I,EACAA,GAAA,MAAAP,KAAAuC,MAAA,GAAA6gB,IAAA7iB,EAAA+iB,EAAA/iB,EAAA+iB,EAEA,SAAAtjB,KAAA+I,IAAAxI,EAAAhL,KAAAyT,MASAF,EAAApR,UAAAyU,MAAA,WACA5W,KAAAsV,SAAA,GASA/B,EAAApR,UAAA2S,OAAA,SAAAtB,GACAxT,KAAAgL,GAAAwI,GASAD,EAAApR,UAAA+S,OAAA,SAAAzB,GACAzT,KAAAyT,OASAF,EAAApR,UAAA6S,UAAA,SAAAtB,GACA1T,KAAA0T", "file": "socket.io.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar url = __webpack_require__(1);\n\tvar parser = __webpack_require__(7);\n\tvar Manager = __webpack_require__(12);\n\tvar debug = __webpack_require__(3)('socket.io-client');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = lookup;\n\t\n\t/**\n\t * Managers cache.\n\t */\n\t\n\tvar cache = exports.managers = {};\n\t\n\t/**\n\t * Looks up an existing `Manager` for multiplexing.\n\t * If the user summons:\n\t *\n\t *   `io('http://localhost/a');`\n\t *   `io('http://localhost/b');`\n\t *\n\t * We reuse the existing instance based on same scheme/port/host,\n\t * and we initialize sockets for each namespace.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction lookup(uri, opts) {\n\t  if ((typeof uri === 'undefined' ? 'undefined' : _typeof(uri)) === 'object') {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t\n\t  opts = opts || {};\n\t\n\t  var parsed = url(uri);\n\t  var source = parsed.source;\n\t  var id = parsed.id;\n\t  var path = parsed.path;\n\t  var sameNamespace = cache[id] && path in cache[id].nsps;\n\t  var newConnection = opts.forceNew || opts['force new connection'] || false === opts.multiplex || sameNamespace;\n\t\n\t  var io;\n\t\n\t  if (newConnection) {\n\t    debug('ignoring socket cache for %s', source);\n\t    io = Manager(source, opts);\n\t  } else {\n\t    if (!cache[id]) {\n\t      debug('new io instance for %s', source);\n\t      cache[id] = Manager(source, opts);\n\t    }\n\t    io = cache[id];\n\t  }\n\t  if (parsed.query && !opts.query) {\n\t    opts.query = parsed.query;\n\t  }\n\t  return io.socket(parsed.path, opts);\n\t}\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = parser.protocol;\n\t\n\t/**\n\t * `connect`.\n\t *\n\t * @param {String} uri\n\t * @api public\n\t */\n\t\n\texports.connect = lookup;\n\t\n\t/**\n\t * Expose constructors for standalone build.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Manager = __webpack_require__(12);\n\texports.Socket = __webpack_require__(37);\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {'use strict';\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parseuri = __webpack_require__(2);\n\tvar debug = __webpack_require__(3)('socket.io-client:url');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = url;\n\t\n\t/**\n\t * URL parser.\n\t *\n\t * @param {String} url\n\t * @param {Object} An object meant to mimic window.location.\n\t *                 Defaults to window.location.\n\t * @api public\n\t */\n\t\n\tfunction url(uri, loc) {\n\t  var obj = uri;\n\t\n\t  // default to window.location\n\t  loc = loc || global.location;\n\t  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\t\n\t  // relative path support\n\t  if ('string' === typeof uri) {\n\t    if ('/' === uri.charAt(0)) {\n\t      if ('/' === uri.charAt(1)) {\n\t        uri = loc.protocol + uri;\n\t      } else {\n\t        uri = loc.host + uri;\n\t      }\n\t    }\n\t\n\t    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\t      debug('protocol-less url %s', uri);\n\t      if ('undefined' !== typeof loc) {\n\t        uri = loc.protocol + '//' + uri;\n\t      } else {\n\t        uri = 'https://' + uri;\n\t      }\n\t    }\n\t\n\t    // parse\n\t    debug('parse %s', uri);\n\t    obj = parseuri(uri);\n\t  }\n\t\n\t  // make sure we treat `localhost:80` and `localhost` equally\n\t  if (!obj.port) {\n\t    if (/^(http|ws)$/.test(obj.protocol)) {\n\t      obj.port = '80';\n\t    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n\t      obj.port = '443';\n\t    }\n\t  }\n\t\n\t  obj.path = obj.path || '/';\n\t\n\t  var ipv6 = obj.host.indexOf(':') !== -1;\n\t  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\t\n\t  // define unique id\n\t  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n\t  // define href\n\t  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : ':' + obj.port);\n\t\n\t  return obj;\n\t}\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Parses an URI\r\n\t *\r\n\t * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\r\n\t * @api private\r\n\t */\r\n\t\r\n\tvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\r\n\t\r\n\tvar parts = [\r\n\t    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\r\n\t];\r\n\t\r\n\tmodule.exports = function parseuri(str) {\r\n\t    var src = str,\r\n\t        b = str.indexOf('['),\r\n\t        e = str.indexOf(']');\r\n\t\r\n\t    if (b != -1 && e != -1) {\r\n\t        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\r\n\t    }\r\n\t\r\n\t    var m = re.exec(str || ''),\r\n\t        uri = {},\r\n\t        i = 14;\r\n\t\r\n\t    while (i--) {\r\n\t        uri[parts[i]] = m[i] || '';\r\n\t    }\r\n\t\r\n\t    if (b != -1 && e != -1) {\r\n\t        uri.source = src;\r\n\t        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\r\n\t        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\r\n\t        uri.ipv6uri = true;\r\n\t    }\r\n\t\r\n\t    return uri;\r\n\t};\r\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(process) {/**\n\t * This is the web browser implementation of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = __webpack_require__(5);\n\texports.log = log;\n\texports.formatArgs = formatArgs;\n\texports.save = save;\n\texports.load = load;\n\texports.useColors = useColors;\n\texports.storage = 'undefined' != typeof chrome\n\t               && 'undefined' != typeof chrome.storage\n\t                  ? chrome.storage.local\n\t                  : localstorage();\n\t\n\t/**\n\t * Colors.\n\t */\n\t\n\texports.colors = [\n\t  '#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC',\n\t  '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF',\n\t  '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC',\n\t  '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF',\n\t  '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC',\n\t  '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033',\n\t  '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366',\n\t  '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933',\n\t  '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC',\n\t  '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF',\n\t  '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'\n\t];\n\t\n\t/**\n\t * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n\t * and the Firebug extension (any Firefox version) are known\n\t * to support \"%c\" CSS customizations.\n\t *\n\t * TODO: add a `localStorage` variable to explicitly enable/disable colors\n\t */\n\t\n\tfunction useColors() {\n\t  // NB: In an Electron preload script, document will be defined but not fully\n\t  // initialized. Since we know we're in Chrome, we'll just detect this case\n\t  // explicitly\n\t  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n\t    return true;\n\t  }\n\t\n\t  // Internet Explorer and Edge do not support colors.\n\t  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t    return false;\n\t  }\n\t\n\t  // is webkit? http://stackoverflow.com/a/16459606/376773\n\t  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t    // is firebug? http://stackoverflow.com/a/398120/376773\n\t    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t    // is firefox >= v31?\n\t    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t    // double check webkit in userAgent just in case we are in a worker\n\t    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n\t}\n\t\n\t/**\n\t * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n\t */\n\t\n\texports.formatters.j = function(v) {\n\t  try {\n\t    return JSON.stringify(v);\n\t  } catch (err) {\n\t    return '[UnexpectedJSONParseError]: ' + err.message;\n\t  }\n\t};\n\t\n\t\n\t/**\n\t * Colorize log arguments if enabled.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction formatArgs(args) {\n\t  var useColors = this.useColors;\n\t\n\t  args[0] = (useColors ? '%c' : '')\n\t    + this.namespace\n\t    + (useColors ? ' %c' : ' ')\n\t    + args[0]\n\t    + (useColors ? '%c ' : ' ')\n\t    + '+' + exports.humanize(this.diff);\n\t\n\t  if (!useColors) return;\n\t\n\t  var c = 'color: ' + this.color;\n\t  args.splice(1, 0, c, 'color: inherit')\n\t\n\t  // the final \"%c\" is somewhat tricky, because there could be other\n\t  // arguments passed either before or after the %c, so we need to\n\t  // figure out the correct index to insert the CSS into\n\t  var index = 0;\n\t  var lastC = 0;\n\t  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n\t    if ('%%' === match) return;\n\t    index++;\n\t    if ('%c' === match) {\n\t      // we only are interested in the *last* %c\n\t      // (the user may have provided their own)\n\t      lastC = index;\n\t    }\n\t  });\n\t\n\t  args.splice(lastC, 0, c);\n\t}\n\t\n\t/**\n\t * Invokes `console.log()` when available.\n\t * No-op when `console.log` is not a \"function\".\n\t *\n\t * @api public\n\t */\n\t\n\tfunction log() {\n\t  // this hackery is required for IE8/9, where\n\t  // the `console.log` function doesn't have 'apply'\n\t  return 'object' === typeof console\n\t    && console.log\n\t    && Function.prototype.apply.call(console.log, console, arguments);\n\t}\n\t\n\t/**\n\t * Save `namespaces`.\n\t *\n\t * @param {String} namespaces\n\t * @api private\n\t */\n\t\n\tfunction save(namespaces) {\n\t  try {\n\t    if (null == namespaces) {\n\t      exports.storage.removeItem('debug');\n\t    } else {\n\t      exports.storage.debug = namespaces;\n\t    }\n\t  } catch(e) {}\n\t}\n\t\n\t/**\n\t * Load `namespaces`.\n\t *\n\t * @return {String} returns the previously persisted debug modes\n\t * @api private\n\t */\n\t\n\tfunction load() {\n\t  var r;\n\t  try {\n\t    r = exports.storage.debug;\n\t  } catch(e) {}\n\t\n\t  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\t  if (!r && typeof process !== 'undefined' && 'env' in process) {\n\t    r = process.env.DEBUG;\n\t  }\n\t\n\t  return r;\n\t}\n\t\n\t/**\n\t * Enable namespaces listed in `localStorage.debug` initially.\n\t */\n\t\n\texports.enable(load());\n\t\n\t/**\n\t * Localstorage attempts to return the localstorage.\n\t *\n\t * This is necessary because safari throws\n\t * when a user disables cookies/localstorage\n\t * and you attempt to access it.\n\t *\n\t * @return {LocalStorage}\n\t * @api private\n\t */\n\t\n\tfunction localstorage() {\n\t  try {\n\t    return window.localStorage;\n\t  } catch (e) {}\n\t}\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(4)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\n\t// shim for using process in browser\n\tvar process = module.exports = {};\n\t\n\t// cached from whatever global is present so that test runners that stub it\n\t// don't break things.  But we need to wrap it in a try catch in case it is\n\t// wrapped in strict mode code which doesn't define any globals.  It's inside a\n\t// function because try/catches deoptimize in certain engines.\n\t\n\tvar cachedSetTimeout;\n\tvar cachedClearTimeout;\n\t\n\tfunction defaultSetTimout() {\n\t    throw new Error('setTimeout has not been defined');\n\t}\n\tfunction defaultClearTimeout () {\n\t    throw new Error('clearTimeout has not been defined');\n\t}\n\t(function () {\n\t    try {\n\t        if (typeof setTimeout === 'function') {\n\t            cachedSetTimeout = setTimeout;\n\t        } else {\n\t            cachedSetTimeout = defaultSetTimout;\n\t        }\n\t    } catch (e) {\n\t        cachedSetTimeout = defaultSetTimout;\n\t    }\n\t    try {\n\t        if (typeof clearTimeout === 'function') {\n\t            cachedClearTimeout = clearTimeout;\n\t        } else {\n\t            cachedClearTimeout = defaultClearTimeout;\n\t        }\n\t    } catch (e) {\n\t        cachedClearTimeout = defaultClearTimeout;\n\t    }\n\t} ())\n\tfunction runTimeout(fun) {\n\t    if (cachedSetTimeout === setTimeout) {\n\t        //normal enviroments in sane situations\n\t        return setTimeout(fun, 0);\n\t    }\n\t    // if setTimeout wasn't available but was latter defined\n\t    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n\t        cachedSetTimeout = setTimeout;\n\t        return setTimeout(fun, 0);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedSetTimeout(fun, 0);\n\t    } catch(e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n\t            return cachedSetTimeout.call(null, fun, 0);\n\t        } catch(e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n\t            return cachedSetTimeout.call(this, fun, 0);\n\t        }\n\t    }\n\t\n\t\n\t}\n\tfunction runClearTimeout(marker) {\n\t    if (cachedClearTimeout === clearTimeout) {\n\t        //normal enviroments in sane situations\n\t        return clearTimeout(marker);\n\t    }\n\t    // if clearTimeout wasn't available but was latter defined\n\t    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n\t        cachedClearTimeout = clearTimeout;\n\t        return clearTimeout(marker);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedClearTimeout(marker);\n\t    } catch (e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n\t            return cachedClearTimeout.call(null, marker);\n\t        } catch (e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n\t            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n\t            return cachedClearTimeout.call(this, marker);\n\t        }\n\t    }\n\t\n\t\n\t\n\t}\n\tvar queue = [];\n\tvar draining = false;\n\tvar currentQueue;\n\tvar queueIndex = -1;\n\t\n\tfunction cleanUpNextTick() {\n\t    if (!draining || !currentQueue) {\n\t        return;\n\t    }\n\t    draining = false;\n\t    if (currentQueue.length) {\n\t        queue = currentQueue.concat(queue);\n\t    } else {\n\t        queueIndex = -1;\n\t    }\n\t    if (queue.length) {\n\t        drainQueue();\n\t    }\n\t}\n\t\n\tfunction drainQueue() {\n\t    if (draining) {\n\t        return;\n\t    }\n\t    var timeout = runTimeout(cleanUpNextTick);\n\t    draining = true;\n\t\n\t    var len = queue.length;\n\t    while(len) {\n\t        currentQueue = queue;\n\t        queue = [];\n\t        while (++queueIndex < len) {\n\t            if (currentQueue) {\n\t                currentQueue[queueIndex].run();\n\t            }\n\t        }\n\t        queueIndex = -1;\n\t        len = queue.length;\n\t    }\n\t    currentQueue = null;\n\t    draining = false;\n\t    runClearTimeout(timeout);\n\t}\n\t\n\tprocess.nextTick = function (fun) {\n\t    var args = new Array(arguments.length - 1);\n\t    if (arguments.length > 1) {\n\t        for (var i = 1; i < arguments.length; i++) {\n\t            args[i - 1] = arguments[i];\n\t        }\n\t    }\n\t    queue.push(new Item(fun, args));\n\t    if (queue.length === 1 && !draining) {\n\t        runTimeout(drainQueue);\n\t    }\n\t};\n\t\n\t// v8 likes predictible objects\n\tfunction Item(fun, array) {\n\t    this.fun = fun;\n\t    this.array = array;\n\t}\n\tItem.prototype.run = function () {\n\t    this.fun.apply(null, this.array);\n\t};\n\tprocess.title = 'browser';\n\tprocess.browser = true;\n\tprocess.env = {};\n\tprocess.argv = [];\n\tprocess.version = ''; // empty string to avoid regexp issues\n\tprocess.versions = {};\n\t\n\tfunction noop() {}\n\t\n\tprocess.on = noop;\n\tprocess.addListener = noop;\n\tprocess.once = noop;\n\tprocess.off = noop;\n\tprocess.removeListener = noop;\n\tprocess.removeAllListeners = noop;\n\tprocess.emit = noop;\n\tprocess.prependListener = noop;\n\tprocess.prependOnceListener = noop;\n\t\n\tprocess.listeners = function (name) { return [] }\n\t\n\tprocess.binding = function (name) {\n\t    throw new Error('process.binding is not supported');\n\t};\n\t\n\tprocess.cwd = function () { return '/' };\n\tprocess.chdir = function (dir) {\n\t    throw new Error('process.chdir is not supported');\n\t};\n\tprocess.umask = function() { return 0; };\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * This is the common logic for both the Node.js and web browser\n\t * implementations of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\n\texports.coerce = coerce;\n\texports.disable = disable;\n\texports.enable = enable;\n\texports.enabled = enabled;\n\texports.humanize = __webpack_require__(6);\n\t\n\t/**\n\t * Active `debug` instances.\n\t */\n\texports.instances = [];\n\t\n\t/**\n\t * The currently active debug mode names, and names to skip.\n\t */\n\t\n\texports.names = [];\n\texports.skips = [];\n\t\n\t/**\n\t * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t *\n\t * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t */\n\t\n\texports.formatters = {};\n\t\n\t/**\n\t * Select a color.\n\t * @param {String} namespace\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction selectColor(namespace) {\n\t  var hash = 0, i;\n\t\n\t  for (i in namespace) {\n\t    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t    hash |= 0; // Convert to 32bit integer\n\t  }\n\t\n\t  return exports.colors[Math.abs(hash) % exports.colors.length];\n\t}\n\t\n\t/**\n\t * Create a debugger with the given `namespace`.\n\t *\n\t * @param {String} namespace\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tfunction createDebug(namespace) {\n\t\n\t  var prevTime;\n\t\n\t  function debug() {\n\t    // disabled?\n\t    if (!debug.enabled) return;\n\t\n\t    var self = debug;\n\t\n\t    // set `diff` timestamp\n\t    var curr = +new Date();\n\t    var ms = curr - (prevTime || curr);\n\t    self.diff = ms;\n\t    self.prev = prevTime;\n\t    self.curr = curr;\n\t    prevTime = curr;\n\t\n\t    // turn the `arguments` into a proper Array\n\t    var args = new Array(arguments.length);\n\t    for (var i = 0; i < args.length; i++) {\n\t      args[i] = arguments[i];\n\t    }\n\t\n\t    args[0] = exports.coerce(args[0]);\n\t\n\t    if ('string' !== typeof args[0]) {\n\t      // anything else let's inspect with %O\n\t      args.unshift('%O');\n\t    }\n\t\n\t    // apply any `formatters` transformations\n\t    var index = 0;\n\t    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n\t      // if we encounter an escaped % then don't increase the array index\n\t      if (match === '%%') return match;\n\t      index++;\n\t      var formatter = exports.formatters[format];\n\t      if ('function' === typeof formatter) {\n\t        var val = args[index];\n\t        match = formatter.call(self, val);\n\t\n\t        // now we need to remove `args[index]` since it's inlined in the `format`\n\t        args.splice(index, 1);\n\t        index--;\n\t      }\n\t      return match;\n\t    });\n\t\n\t    // apply env-specific formatting (colors, etc.)\n\t    exports.formatArgs.call(self, args);\n\t\n\t    var logFn = debug.log || exports.log || console.log.bind(console);\n\t    logFn.apply(self, args);\n\t  }\n\t\n\t  debug.namespace = namespace;\n\t  debug.enabled = exports.enabled(namespace);\n\t  debug.useColors = exports.useColors();\n\t  debug.color = selectColor(namespace);\n\t  debug.destroy = destroy;\n\t\n\t  // env-specific initialization logic for debug instances\n\t  if ('function' === typeof exports.init) {\n\t    exports.init(debug);\n\t  }\n\t\n\t  exports.instances.push(debug);\n\t\n\t  return debug;\n\t}\n\t\n\tfunction destroy () {\n\t  var index = exports.instances.indexOf(this);\n\t  if (index !== -1) {\n\t    exports.instances.splice(index, 1);\n\t    return true;\n\t  } else {\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Enables a debug mode by namespaces. This can include modes\n\t * separated by a colon and wildcards.\n\t *\n\t * @param {String} namespaces\n\t * @api public\n\t */\n\t\n\tfunction enable(namespaces) {\n\t  exports.save(namespaces);\n\t\n\t  exports.names = [];\n\t  exports.skips = [];\n\t\n\t  var i;\n\t  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t  var len = split.length;\n\t\n\t  for (i = 0; i < len; i++) {\n\t    if (!split[i]) continue; // ignore empty strings\n\t    namespaces = split[i].replace(/\\*/g, '.*?');\n\t    if (namespaces[0] === '-') {\n\t      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t    } else {\n\t      exports.names.push(new RegExp('^' + namespaces + '$'));\n\t    }\n\t  }\n\t\n\t  for (i = 0; i < exports.instances.length; i++) {\n\t    var instance = exports.instances[i];\n\t    instance.enabled = exports.enabled(instance.namespace);\n\t  }\n\t}\n\t\n\t/**\n\t * Disable debug output.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction disable() {\n\t  exports.enable('');\n\t}\n\t\n\t/**\n\t * Returns true if the given mode name is enabled, false otherwise.\n\t *\n\t * @param {String} name\n\t * @return {Boolean}\n\t * @api public\n\t */\n\t\n\tfunction enabled(name) {\n\t  if (name[name.length - 1] === '*') {\n\t    return true;\n\t  }\n\t  var i, len;\n\t  for (i = 0, len = exports.skips.length; i < len; i++) {\n\t    if (exports.skips[i].test(name)) {\n\t      return false;\n\t    }\n\t  }\n\t  for (i = 0, len = exports.names.length; i < len; i++) {\n\t    if (exports.names[i].test(name)) {\n\t      return true;\n\t    }\n\t  }\n\t  return false;\n\t}\n\t\n\t/**\n\t * Coerce `val`.\n\t *\n\t * @param {Mixed} val\n\t * @return {Mixed}\n\t * @api private\n\t */\n\t\n\tfunction coerce(val) {\n\t  if (val instanceof Error) return val.stack || val.message;\n\t  return val;\n\t}\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Helpers.\n\t */\n\t\n\tvar s = 1000;\n\tvar m = s * 60;\n\tvar h = m * 60;\n\tvar d = h * 24;\n\tvar y = d * 365.25;\n\t\n\t/**\n\t * Parse or format the given `val`.\n\t *\n\t * Options:\n\t *\n\t *  - `long` verbose formatting [false]\n\t *\n\t * @param {String|Number} val\n\t * @param {Object} [options]\n\t * @throws {Error} throw an error if val is not a non-empty string or a number\n\t * @return {String|Number}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(val, options) {\n\t  options = options || {};\n\t  var type = typeof val;\n\t  if (type === 'string' && val.length > 0) {\n\t    return parse(val);\n\t  } else if (type === 'number' && isNaN(val) === false) {\n\t    return options.long ? fmtLong(val) : fmtShort(val);\n\t  }\n\t  throw new Error(\n\t    'val is not a non-empty string or a valid number. val=' +\n\t      JSON.stringify(val)\n\t  );\n\t};\n\t\n\t/**\n\t * Parse the given `str` and return milliseconds.\n\t *\n\t * @param {String} str\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction parse(str) {\n\t  str = String(str);\n\t  if (str.length > 100) {\n\t    return;\n\t  }\n\t  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n\t    str\n\t  );\n\t  if (!match) {\n\t    return;\n\t  }\n\t  var n = parseFloat(match[1]);\n\t  var type = (match[2] || 'ms').toLowerCase();\n\t  switch (type) {\n\t    case 'years':\n\t    case 'year':\n\t    case 'yrs':\n\t    case 'yr':\n\t    case 'y':\n\t      return n * y;\n\t    case 'days':\n\t    case 'day':\n\t    case 'd':\n\t      return n * d;\n\t    case 'hours':\n\t    case 'hour':\n\t    case 'hrs':\n\t    case 'hr':\n\t    case 'h':\n\t      return n * h;\n\t    case 'minutes':\n\t    case 'minute':\n\t    case 'mins':\n\t    case 'min':\n\t    case 'm':\n\t      return n * m;\n\t    case 'seconds':\n\t    case 'second':\n\t    case 'secs':\n\t    case 'sec':\n\t    case 's':\n\t      return n * s;\n\t    case 'milliseconds':\n\t    case 'millisecond':\n\t    case 'msecs':\n\t    case 'msec':\n\t    case 'ms':\n\t      return n;\n\t    default:\n\t      return undefined;\n\t  }\n\t}\n\t\n\t/**\n\t * Short format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtShort(ms) {\n\t  if (ms >= d) {\n\t    return Math.round(ms / d) + 'd';\n\t  }\n\t  if (ms >= h) {\n\t    return Math.round(ms / h) + 'h';\n\t  }\n\t  if (ms >= m) {\n\t    return Math.round(ms / m) + 'm';\n\t  }\n\t  if (ms >= s) {\n\t    return Math.round(ms / s) + 's';\n\t  }\n\t  return ms + 'ms';\n\t}\n\t\n\t/**\n\t * Long format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtLong(ms) {\n\t  return plural(ms, d, 'day') ||\n\t    plural(ms, h, 'hour') ||\n\t    plural(ms, m, 'minute') ||\n\t    plural(ms, s, 'second') ||\n\t    ms + ' ms';\n\t}\n\t\n\t/**\n\t * Pluralization helper.\n\t */\n\t\n\tfunction plural(ms, n, name) {\n\t  if (ms < n) {\n\t    return;\n\t  }\n\t  if (ms < n * 1.5) {\n\t    return Math.floor(ms / n) + ' ' + name;\n\t  }\n\t  return Math.ceil(ms / n) + ' ' + name + 's';\n\t}\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar debug = __webpack_require__(3)('socket.io-parser');\n\tvar Emitter = __webpack_require__(8);\n\tvar binary = __webpack_require__(9);\n\tvar isArray = __webpack_require__(10);\n\tvar isBuf = __webpack_require__(11);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = 4;\n\t\n\t/**\n\t * Packet types.\n\t *\n\t * @api public\n\t */\n\t\n\texports.types = [\n\t  'CONNECT',\n\t  'DISCONNECT',\n\t  'EVENT',\n\t  'ACK',\n\t  'ERROR',\n\t  'BINARY_EVENT',\n\t  'BINARY_ACK'\n\t];\n\t\n\t/**\n\t * Packet type `connect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.CONNECT = 0;\n\t\n\t/**\n\t * Packet type `disconnect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.DISCONNECT = 1;\n\t\n\t/**\n\t * Packet type `event`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.EVENT = 2;\n\t\n\t/**\n\t * Packet type `ack`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ACK = 3;\n\t\n\t/**\n\t * Packet type `error`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ERROR = 4;\n\t\n\t/**\n\t * Packet type 'binary event'\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_EVENT = 5;\n\t\n\t/**\n\t * Packet type `binary ack`. For acks with binary arguments.\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_ACK = 6;\n\t\n\t/**\n\t * Encoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Encoder = Encoder;\n\t\n\t/**\n\t * Decoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Decoder = Decoder;\n\t\n\t/**\n\t * A socket.io Encoder instance\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Encoder() {}\n\t\n\tvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\t\n\t/**\n\t * Encode a packet as a single string if non-binary, or as a\n\t * buffer sequence, depending on packet type.\n\t *\n\t * @param {Object} obj - packet object\n\t * @param {Function} callback - function to handle encodings (likely engine.write)\n\t * @return Calls callback with Array of encodings\n\t * @api public\n\t */\n\t\n\tEncoder.prototype.encode = function(obj, callback){\n\t  debug('encoding packet %j', obj);\n\t\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    encodeAsBinary(obj, callback);\n\t  } else {\n\t    var encoding = encodeAsString(obj);\n\t    callback([encoding]);\n\t  }\n\t};\n\t\n\t/**\n\t * Encode packet as string.\n\t *\n\t * @param {Object} packet\n\t * @return {String} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsString(obj) {\n\t\n\t  // first is type\n\t  var str = '' + obj.type;\n\t\n\t  // attachments if we have them\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    str += obj.attachments + '-';\n\t  }\n\t\n\t  // if we have a namespace other than `/`\n\t  // we append it followed by a comma `,`\n\t  if (obj.nsp && '/' !== obj.nsp) {\n\t    str += obj.nsp + ',';\n\t  }\n\t\n\t  // immediately followed by the id\n\t  if (null != obj.id) {\n\t    str += obj.id;\n\t  }\n\t\n\t  // json data\n\t  if (null != obj.data) {\n\t    var payload = tryStringify(obj.data);\n\t    if (payload !== false) {\n\t      str += payload;\n\t    } else {\n\t      return ERROR_PACKET;\n\t    }\n\t  }\n\t\n\t  debug('encoded %j as %s', obj, str);\n\t  return str;\n\t}\n\t\n\tfunction tryStringify(str) {\n\t  try {\n\t    return JSON.stringify(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Encode packet as 'buffer sequence' by removing blobs, and\n\t * deconstructing packet into object with placeholders and\n\t * a list of buffers.\n\t *\n\t * @param {Object} packet\n\t * @return {Buffer} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsBinary(obj, callback) {\n\t\n\t  function writeEncoding(bloblessData) {\n\t    var deconstruction = binary.deconstructPacket(bloblessData);\n\t    var pack = encodeAsString(deconstruction.packet);\n\t    var buffers = deconstruction.buffers;\n\t\n\t    buffers.unshift(pack); // add packet info to beginning of data list\n\t    callback(buffers); // write all the buffers\n\t  }\n\t\n\t  binary.removeBlobs(obj, writeEncoding);\n\t}\n\t\n\t/**\n\t * A socket.io Decoder instance\n\t *\n\t * @return {Object} decoder\n\t * @api public\n\t */\n\t\n\tfunction Decoder() {\n\t  this.reconstructor = null;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter` with Decoder.\n\t */\n\t\n\tEmitter(Decoder.prototype);\n\t\n\t/**\n\t * Decodes an ecoded packet string into packet JSON.\n\t *\n\t * @param {String} obj - encoded packet\n\t * @return {Object} packet\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.add = function(obj) {\n\t  var packet;\n\t  if (typeof obj === 'string') {\n\t    packet = decodeString(obj);\n\t    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n\t      this.reconstructor = new BinaryReconstructor(packet);\n\t\n\t      // no attachments, labeled binary but no binary data to follow\n\t      if (this.reconstructor.reconPack.attachments === 0) {\n\t        this.emit('decoded', packet);\n\t      }\n\t    } else { // non-binary full packet\n\t      this.emit('decoded', packet);\n\t    }\n\t  }\n\t  else if (isBuf(obj) || obj.base64) { // raw binary data\n\t    if (!this.reconstructor) {\n\t      throw new Error('got binary data when not reconstructing a packet');\n\t    } else {\n\t      packet = this.reconstructor.takeBinaryData(obj);\n\t      if (packet) { // received final buffer\n\t        this.reconstructor = null;\n\t        this.emit('decoded', packet);\n\t      }\n\t    }\n\t  }\n\t  else {\n\t    throw new Error('Unknown type: ' + obj);\n\t  }\n\t};\n\t\n\t/**\n\t * Decode a packet String (JSON data)\n\t *\n\t * @param {String} str\n\t * @return {Object} packet\n\t * @api private\n\t */\n\t\n\tfunction decodeString(str) {\n\t  var i = 0;\n\t  // look up type\n\t  var p = {\n\t    type: Number(str.charAt(0))\n\t  };\n\t\n\t  if (null == exports.types[p.type]) {\n\t    return error('unknown packet type ' + p.type);\n\t  }\n\t\n\t  // look up attachments if type binary\n\t  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n\t    var buf = '';\n\t    while (str.charAt(++i) !== '-') {\n\t      buf += str.charAt(i);\n\t      if (i == str.length) break;\n\t    }\n\t    if (buf != Number(buf) || str.charAt(i) !== '-') {\n\t      throw new Error('Illegal attachments');\n\t    }\n\t    p.attachments = Number(buf);\n\t  }\n\t\n\t  // look up namespace (if any)\n\t  if ('/' === str.charAt(i + 1)) {\n\t    p.nsp = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (',' === c) break;\n\t      p.nsp += c;\n\t      if (i === str.length) break;\n\t    }\n\t  } else {\n\t    p.nsp = '/';\n\t  }\n\t\n\t  // look up id\n\t  var next = str.charAt(i + 1);\n\t  if ('' !== next && Number(next) == next) {\n\t    p.id = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (null == c || Number(c) != c) {\n\t        --i;\n\t        break;\n\t      }\n\t      p.id += str.charAt(i);\n\t      if (i === str.length) break;\n\t    }\n\t    p.id = Number(p.id);\n\t  }\n\t\n\t  // look up json data\n\t  if (str.charAt(++i)) {\n\t    var payload = tryParse(str.substr(i));\n\t    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n\t    if (isPayloadValid) {\n\t      p.data = payload;\n\t    } else {\n\t      return error('invalid payload');\n\t    }\n\t  }\n\t\n\t  debug('decoded %s as %j', str, p);\n\t  return p;\n\t}\n\t\n\tfunction tryParse(str) {\n\t  try {\n\t    return JSON.parse(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Deallocates a parser's resources\n\t *\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.destroy = function() {\n\t  if (this.reconstructor) {\n\t    this.reconstructor.finishedReconstruction();\n\t  }\n\t};\n\t\n\t/**\n\t * A manager of a binary event's 'buffer sequence'. Should\n\t * be constructed whenever a packet of type BINARY_EVENT is\n\t * decoded.\n\t *\n\t * @param {Object} packet\n\t * @return {BinaryReconstructor} initialized reconstructor\n\t * @api private\n\t */\n\t\n\tfunction BinaryReconstructor(packet) {\n\t  this.reconPack = packet;\n\t  this.buffers = [];\n\t}\n\t\n\t/**\n\t * Method to be called when binary data received from connection\n\t * after a BINARY_EVENT packet.\n\t *\n\t * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n\t * @return {null | Object} returns null if more binary data is expected or\n\t *   a reconstructed packet object if all buffers have been received.\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n\t  this.buffers.push(binData);\n\t  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n\t    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n\t    this.finishedReconstruction();\n\t    return packet;\n\t  }\n\t  return null;\n\t};\n\t\n\t/**\n\t * Cleans up binary packet reconstruction variables.\n\t *\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.finishedReconstruction = function() {\n\t  this.reconPack = null;\n\t  this.buffers = [];\n\t};\n\t\n\tfunction error(msg) {\n\t  return {\n\t    type: exports.ERROR,\n\t    data: 'parser error: ' + msg\n\t  };\n\t}\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\r\n\t/**\r\n\t * Expose `Emitter`.\r\n\t */\r\n\t\r\n\tif (true) {\r\n\t  module.exports = Emitter;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Initialize a new `Emitter`.\r\n\t *\r\n\t * @api public\r\n\t */\r\n\t\r\n\tfunction Emitter(obj) {\r\n\t  if (obj) return mixin(obj);\r\n\t};\r\n\t\r\n\t/**\r\n\t * Mixin the emitter properties.\r\n\t *\r\n\t * @param {Object} obj\r\n\t * @return {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\tfunction mixin(obj) {\r\n\t  for (var key in Emitter.prototype) {\r\n\t    obj[key] = Emitter.prototype[key];\r\n\t  }\r\n\t  return obj;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Listen on the given `event` with `fn`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.on =\r\n\tEmitter.prototype.addEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n\t    .push(fn);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Adds an `event` listener that will be invoked a single\r\n\t * time then automatically removed.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.once = function(event, fn){\r\n\t  function on() {\r\n\t    this.off(event, on);\r\n\t    fn.apply(this, arguments);\r\n\t  }\r\n\t\r\n\t  on.fn = fn;\r\n\t  this.on(event, on);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Remove the given callback for `event` or all\r\n\t * registered callbacks.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.off =\r\n\tEmitter.prototype.removeListener =\r\n\tEmitter.prototype.removeAllListeners =\r\n\tEmitter.prototype.removeEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  // all\r\n\t  if (0 == arguments.length) {\r\n\t    this._callbacks = {};\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // specific event\r\n\t  var callbacks = this._callbacks['$' + event];\r\n\t  if (!callbacks) return this;\r\n\t\r\n\t  // remove all handlers\r\n\t  if (1 == arguments.length) {\r\n\t    delete this._callbacks['$' + event];\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // remove specific handler\r\n\t  var cb;\r\n\t  for (var i = 0; i < callbacks.length; i++) {\r\n\t    cb = callbacks[i];\r\n\t    if (cb === fn || cb.fn === fn) {\r\n\t      callbacks.splice(i, 1);\r\n\t      break;\r\n\t    }\r\n\t  }\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Emit `event` with the given args.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Mixed} ...\r\n\t * @return {Emitter}\r\n\t */\r\n\t\r\n\tEmitter.prototype.emit = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  var args = [].slice.call(arguments, 1)\r\n\t    , callbacks = this._callbacks['$' + event];\r\n\t\r\n\t  if (callbacks) {\r\n\t    callbacks = callbacks.slice(0);\r\n\t    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n\t      callbacks[i].apply(this, args);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Return array of callbacks for `event`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Array}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.listeners = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  return this._callbacks['$' + event] || [];\r\n\t};\r\n\t\r\n\t/**\r\n\t * Check if this emitter has `event` handlers.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Boolean}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.hasListeners = function(event){\r\n\t  return !! this.listeners(event).length;\r\n\t};\r\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/*global Blob,File*/\n\t\n\t/**\n\t * Module requirements\n\t */\n\t\n\tvar isArray = __webpack_require__(10);\n\tvar isBuf = __webpack_require__(11);\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof global.Blob === 'function' || toString.call(global.Blob) === '[object BlobConstructor]';\n\tvar withNativeFile = typeof global.File === 'function' || toString.call(global.File) === '[object FileConstructor]';\n\t\n\t/**\n\t * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n\t * Anything with blobs or files should be fed through removeBlobs before coming\n\t * here.\n\t *\n\t * @param {Object} packet - socket.io event packet\n\t * @return {Object} with deconstructed packet and list of buffers\n\t * @api public\n\t */\n\t\n\texports.deconstructPacket = function(packet) {\n\t  var buffers = [];\n\t  var packetData = packet.data;\n\t  var pack = packet;\n\t  pack.data = _deconstructPacket(packetData, buffers);\n\t  pack.attachments = buffers.length; // number of binary 'attachments'\n\t  return {packet: pack, buffers: buffers};\n\t};\n\t\n\tfunction _deconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (isBuf(data)) {\n\t    var placeholder = { _placeholder: true, num: buffers.length };\n\t    buffers.push(data);\n\t    return placeholder;\n\t  } else if (isArray(data)) {\n\t    var newData = new Array(data.length);\n\t    for (var i = 0; i < data.length; i++) {\n\t      newData[i] = _deconstructPacket(data[i], buffers);\n\t    }\n\t    return newData;\n\t  } else if (typeof data === 'object' && !(data instanceof Date)) {\n\t    var newData = {};\n\t    for (var key in data) {\n\t      newData[key] = _deconstructPacket(data[key], buffers);\n\t    }\n\t    return newData;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Reconstructs a binary packet from its placeholder packet and buffers\n\t *\n\t * @param {Object} packet - event packet with placeholders\n\t * @param {Array} buffers - binary buffers to put in placeholder positions\n\t * @return {Object} reconstructed packet\n\t * @api public\n\t */\n\t\n\texports.reconstructPacket = function(packet, buffers) {\n\t  packet.data = _reconstructPacket(packet.data, buffers);\n\t  packet.attachments = undefined; // no longer useful\n\t  return packet;\n\t};\n\t\n\tfunction _reconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (data && data._placeholder) {\n\t    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n\t  } else if (isArray(data)) {\n\t    for (var i = 0; i < data.length; i++) {\n\t      data[i] = _reconstructPacket(data[i], buffers);\n\t    }\n\t  } else if (typeof data === 'object') {\n\t    for (var key in data) {\n\t      data[key] = _reconstructPacket(data[key], buffers);\n\t    }\n\t  }\n\t\n\t  return data;\n\t}\n\t\n\t/**\n\t * Asynchronously removes Blobs or Files from data via\n\t * FileReader's readAsArrayBuffer method. Used before encoding\n\t * data as msgpack. Calls callback with the blobless data.\n\t *\n\t * @param {Object} data\n\t * @param {Function} callback\n\t * @api private\n\t */\n\t\n\texports.removeBlobs = function(data, callback) {\n\t  function _removeBlobs(obj, curKey, containingObject) {\n\t    if (!obj) return obj;\n\t\n\t    // convert any blob\n\t    if ((withNativeBlob && obj instanceof Blob) ||\n\t        (withNativeFile && obj instanceof File)) {\n\t      pendingBlobs++;\n\t\n\t      // async filereader\n\t      var fileReader = new FileReader();\n\t      fileReader.onload = function() { // this.result == arraybuffer\n\t        if (containingObject) {\n\t          containingObject[curKey] = this.result;\n\t        }\n\t        else {\n\t          bloblessData = this.result;\n\t        }\n\t\n\t        // if nothing pending its callback time\n\t        if(! --pendingBlobs) {\n\t          callback(bloblessData);\n\t        }\n\t      };\n\t\n\t      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n\t    } else if (isArray(obj)) { // handle array\n\t      for (var i = 0; i < obj.length; i++) {\n\t        _removeBlobs(obj[i], i, obj);\n\t      }\n\t    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n\t      for (var key in obj) {\n\t        _removeBlobs(obj[key], key, obj);\n\t      }\n\t    }\n\t  }\n\t\n\t  var pendingBlobs = 0;\n\t  var bloblessData = data;\n\t  _removeBlobs(bloblessData);\n\t  if (!pendingBlobs) {\n\t    callback(bloblessData);\n\t  }\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports) {\n\n\tvar toString = {}.toString;\n\t\n\tmodule.exports = Array.isArray || function (arr) {\n\t  return toString.call(arr) == '[object Array]';\n\t};\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {\n\tmodule.exports = isBuf;\n\t\n\tvar withNativeBuffer = typeof global.Buffer === 'function' && typeof global.Buffer.isBuffer === 'function';\n\tvar withNativeArrayBuffer = typeof global.ArrayBuffer === 'function';\n\t\n\tvar isView = (function () {\n\t  if (withNativeArrayBuffer && typeof global.ArrayBuffer.isView === 'function') {\n\t    return global.ArrayBuffer.isView;\n\t  } else {\n\t    return function (obj) { return obj.buffer instanceof global.ArrayBuffer; };\n\t  }\n\t})();\n\t\n\t/**\n\t * Returns true if obj is a buffer or an arraybuffer.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction isBuf(obj) {\n\t  return (withNativeBuffer && global.Buffer.isBuffer(obj)) ||\n\t          (withNativeArrayBuffer && (obj instanceof global.ArrayBuffer || isView(obj)));\n\t}\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar eio = __webpack_require__(13);\n\tvar Socket = __webpack_require__(37);\n\tvar Emitter = __webpack_require__(8);\n\tvar parser = __webpack_require__(7);\n\tvar on = __webpack_require__(39);\n\tvar bind = __webpack_require__(40);\n\tvar debug = __webpack_require__(3)('socket.io-client:manager');\n\tvar indexOf = __webpack_require__(36);\n\tvar Backoff = __webpack_require__(41);\n\t\n\t/**\n\t * IE6+ hasOwnProperty\n\t */\n\t\n\tvar has = Object.prototype.hasOwnProperty;\n\t\n\t/**\n\t * Module exports\n\t */\n\t\n\tmodule.exports = Manager;\n\t\n\t/**\n\t * `Manager` constructor.\n\t *\n\t * @param {String} engine instance or engine uri/opts\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Manager(uri, opts) {\n\t  if (!(this instanceof Manager)) return new Manager(uri, opts);\n\t  if (uri && 'object' === (typeof uri === 'undefined' ? 'undefined' : _typeof(uri))) {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t  opts = opts || {};\n\t\n\t  opts.path = opts.path || '/socket.io';\n\t  this.nsps = {};\n\t  this.subs = [];\n\t  this.opts = opts;\n\t  this.reconnection(opts.reconnection !== false);\n\t  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n\t  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n\t  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n\t  this.randomizationFactor(opts.randomizationFactor || 0.5);\n\t  this.backoff = new Backoff({\n\t    min: this.reconnectionDelay(),\n\t    max: this.reconnectionDelayMax(),\n\t    jitter: this.randomizationFactor()\n\t  });\n\t  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n\t  this.readyState = 'closed';\n\t  this.uri = uri;\n\t  this.connecting = [];\n\t  this.lastPing = null;\n\t  this.encoding = false;\n\t  this.packetBuffer = [];\n\t  var _parser = opts.parser || parser;\n\t  this.encoder = new _parser.Encoder();\n\t  this.decoder = new _parser.Decoder();\n\t  this.autoConnect = opts.autoConnect !== false;\n\t  if (this.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Propagate given event to sockets and emit on `this`\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.emitAll = function () {\n\t  this.emit.apply(this, arguments);\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Update `socket.id` of all sockets\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.updateSocketIds = function () {\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].id = this.generateId(nsp);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * generate `socket.id` for the given `nsp`\n\t *\n\t * @param {String} nsp\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tManager.prototype.generateId = function (nsp) {\n\t  return (nsp === '/' ? '' : nsp + '#') + this.engine.id;\n\t};\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Manager.prototype);\n\t\n\t/**\n\t * Sets the `reconnection` config.\n\t *\n\t * @param {Boolean} true/false if it should automatically reconnect\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnection = function (v) {\n\t  if (!arguments.length) return this._reconnection;\n\t  this._reconnection = !!v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the reconnection attempts config.\n\t *\n\t * @param {Number} max reconnection attempts before giving up\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionAttempts = function (v) {\n\t  if (!arguments.length) return this._reconnectionAttempts;\n\t  this._reconnectionAttempts = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelay = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelay;\n\t  this._reconnectionDelay = v;\n\t  this.backoff && this.backoff.setMin(v);\n\t  return this;\n\t};\n\t\n\tManager.prototype.randomizationFactor = function (v) {\n\t  if (!arguments.length) return this._randomizationFactor;\n\t  this._randomizationFactor = v;\n\t  this.backoff && this.backoff.setJitter(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the maximum delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelayMax = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelayMax;\n\t  this._reconnectionDelayMax = v;\n\t  this.backoff && this.backoff.setMax(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the connection timeout. `false` to disable\n\t *\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.timeout = function (v) {\n\t  if (!arguments.length) return this._timeout;\n\t  this._timeout = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Starts trying to reconnect if reconnection is enabled and we have not\n\t * started reconnecting yet\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.maybeReconnectOnOpen = function () {\n\t  // Only try to reconnect if it's the first time we're connecting\n\t  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n\t    // keeps reconnection from firing twice for the same reconnection loop\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Sets the current transport `socket`.\n\t *\n\t * @param {Function} optional, callback\n\t * @return {Manager} self\n\t * @api public\n\t */\n\t\n\tManager.prototype.open = Manager.prototype.connect = function (fn, opts) {\n\t  debug('readyState %s', this.readyState);\n\t  if (~this.readyState.indexOf('open')) return this;\n\t\n\t  debug('opening %s', this.uri);\n\t  this.engine = eio(this.uri, this.opts);\n\t  var socket = this.engine;\n\t  var self = this;\n\t  this.readyState = 'opening';\n\t  this.skipReconnect = false;\n\t\n\t  // emit `open`\n\t  var openSub = on(socket, 'open', function () {\n\t    self.onopen();\n\t    fn && fn();\n\t  });\n\t\n\t  // emit `connect_error`\n\t  var errorSub = on(socket, 'error', function (data) {\n\t    debug('connect_error');\n\t    self.cleanup();\n\t    self.readyState = 'closed';\n\t    self.emitAll('connect_error', data);\n\t    if (fn) {\n\t      var err = new Error('Connection error');\n\t      err.data = data;\n\t      fn(err);\n\t    } else {\n\t      // Only do this if there is no fn to handle the error\n\t      self.maybeReconnectOnOpen();\n\t    }\n\t  });\n\t\n\t  // emit `connect_timeout`\n\t  if (false !== this._timeout) {\n\t    var timeout = this._timeout;\n\t    debug('connect attempt will timeout after %d', timeout);\n\t\n\t    // set timer\n\t    var timer = setTimeout(function () {\n\t      debug('connect attempt timed out after %d', timeout);\n\t      openSub.destroy();\n\t      socket.close();\n\t      socket.emit('error', 'timeout');\n\t      self.emitAll('connect_timeout', timeout);\n\t    }, timeout);\n\t\n\t    this.subs.push({\n\t      destroy: function destroy() {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t\n\t  this.subs.push(openSub);\n\t  this.subs.push(errorSub);\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport open.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onopen = function () {\n\t  debug('open');\n\t\n\t  // clear old subs\n\t  this.cleanup();\n\t\n\t  // mark as open\n\t  this.readyState = 'open';\n\t  this.emit('open');\n\t\n\t  // add new subs\n\t  var socket = this.engine;\n\t  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n\t  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n\t  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n\t  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n\t  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n\t  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n\t};\n\t\n\t/**\n\t * Called upon a ping.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onping = function () {\n\t  this.lastPing = new Date();\n\t  this.emitAll('ping');\n\t};\n\t\n\t/**\n\t * Called upon a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onpong = function () {\n\t  this.emitAll('pong', new Date() - this.lastPing);\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondata = function (data) {\n\t  this.decoder.add(data);\n\t};\n\t\n\t/**\n\t * Called when parser fully decodes a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondecoded = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon socket error.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onerror = function (err) {\n\t  debug('error', err);\n\t  this.emitAll('error', err);\n\t};\n\t\n\t/**\n\t * Creates a new socket for the given `nsp`.\n\t *\n\t * @return {Socket}\n\t * @api public\n\t */\n\t\n\tManager.prototype.socket = function (nsp, opts) {\n\t  var socket = this.nsps[nsp];\n\t  if (!socket) {\n\t    socket = new Socket(this, nsp, opts);\n\t    this.nsps[nsp] = socket;\n\t    var self = this;\n\t    socket.on('connecting', onConnecting);\n\t    socket.on('connect', function () {\n\t      socket.id = self.generateId(nsp);\n\t    });\n\t\n\t    if (this.autoConnect) {\n\t      // manually call here since connecting event is fired before listening\n\t      onConnecting();\n\t    }\n\t  }\n\t\n\t  function onConnecting() {\n\t    if (!~indexOf(self.connecting, socket)) {\n\t      self.connecting.push(socket);\n\t    }\n\t  }\n\t\n\t  return socket;\n\t};\n\t\n\t/**\n\t * Called upon a socket close.\n\t *\n\t * @param {Socket} socket\n\t */\n\t\n\tManager.prototype.destroy = function (socket) {\n\t  var index = indexOf(this.connecting, socket);\n\t  if (~index) this.connecting.splice(index, 1);\n\t  if (this.connecting.length) return;\n\t\n\t  this.close();\n\t};\n\t\n\t/**\n\t * Writes a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tManager.prototype.packet = function (packet) {\n\t  debug('writing packet %j', packet);\n\t  var self = this;\n\t  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\t\n\t  if (!self.encoding) {\n\t    // encode, then write to engine with result\n\t    self.encoding = true;\n\t    this.encoder.encode(packet, function (encodedPackets) {\n\t      for (var i = 0; i < encodedPackets.length; i++) {\n\t        self.engine.write(encodedPackets[i], packet.options);\n\t      }\n\t      self.encoding = false;\n\t      self.processPacketQueue();\n\t    });\n\t  } else {\n\t    // add packet to the queue\n\t    self.packetBuffer.push(packet);\n\t  }\n\t};\n\t\n\t/**\n\t * If packet buffer is non-empty, begins encoding the\n\t * next packet in line.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.processPacketQueue = function () {\n\t  if (this.packetBuffer.length > 0 && !this.encoding) {\n\t    var pack = this.packetBuffer.shift();\n\t    this.packet(pack);\n\t  }\n\t};\n\t\n\t/**\n\t * Clean up transport subscriptions and packet buffer.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.cleanup = function () {\n\t  debug('cleanup');\n\t\n\t  var subsLength = this.subs.length;\n\t  for (var i = 0; i < subsLength; i++) {\n\t    var sub = this.subs.shift();\n\t    sub.destroy();\n\t  }\n\t\n\t  this.packetBuffer = [];\n\t  this.encoding = false;\n\t  this.lastPing = null;\n\t\n\t  this.decoder.destroy();\n\t};\n\t\n\t/**\n\t * Close the current socket.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.close = Manager.prototype.disconnect = function () {\n\t  debug('disconnect');\n\t  this.skipReconnect = true;\n\t  this.reconnecting = false;\n\t  if ('opening' === this.readyState) {\n\t    // `onclose` will not fire because\n\t    // an open event never happened\n\t    this.cleanup();\n\t  }\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  if (this.engine) this.engine.close();\n\t};\n\t\n\t/**\n\t * Called upon engine close.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onclose = function (reason) {\n\t  debug('onclose');\n\t\n\t  this.cleanup();\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  this.emit('close', reason);\n\t\n\t  if (this._reconnection && !this.skipReconnect) {\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Attempt a reconnection.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.reconnect = function () {\n\t  if (this.reconnecting || this.skipReconnect) return this;\n\t\n\t  var self = this;\n\t\n\t  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\t    debug('reconnect failed');\n\t    this.backoff.reset();\n\t    this.emitAll('reconnect_failed');\n\t    this.reconnecting = false;\n\t  } else {\n\t    var delay = this.backoff.duration();\n\t    debug('will wait %dms before reconnect attempt', delay);\n\t\n\t    this.reconnecting = true;\n\t    var timer = setTimeout(function () {\n\t      if (self.skipReconnect) return;\n\t\n\t      debug('attempting reconnect');\n\t      self.emitAll('reconnect_attempt', self.backoff.attempts);\n\t      self.emitAll('reconnecting', self.backoff.attempts);\n\t\n\t      // check again for the case socket closed in above events\n\t      if (self.skipReconnect) return;\n\t\n\t      self.open(function (err) {\n\t        if (err) {\n\t          debug('reconnect attempt error');\n\t          self.reconnecting = false;\n\t          self.reconnect();\n\t          self.emitAll('reconnect_error', err.data);\n\t        } else {\n\t          debug('reconnect success');\n\t          self.onreconnect();\n\t        }\n\t      });\n\t    }, delay);\n\t\n\t    this.subs.push({\n\t      destroy: function destroy() {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful reconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onreconnect = function () {\n\t  var attempt = this.backoff.attempts;\n\t  this.reconnecting = false;\n\t  this.backoff.reset();\n\t  this.updateSocketIds();\n\t  this.emitAll('reconnect', attempt);\n\t};\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\tmodule.exports = __webpack_require__(14);\n\t\n\t/**\n\t * Exports parser\n\t *\n\t * @api public\n\t *\n\t */\n\tmodule.exports.parser = __webpack_require__(21);\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module dependencies.\n\t */\n\t\n\tvar transports = __webpack_require__(15);\n\tvar Emitter = __webpack_require__(8);\n\tvar debug = __webpack_require__(3)('engine.io-client:socket');\n\tvar index = __webpack_require__(36);\n\tvar parser = __webpack_require__(21);\n\tvar parseuri = __webpack_require__(2);\n\tvar parseqs = __webpack_require__(30);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Socket;\n\t\n\t/**\n\t * Socket constructor.\n\t *\n\t * @param {String|Object} uri or options\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Socket (uri, opts) {\n\t  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\t\n\t  opts = opts || {};\n\t\n\t  if (uri && 'object' === typeof uri) {\n\t    opts = uri;\n\t    uri = null;\n\t  }\n\t\n\t  if (uri) {\n\t    uri = parseuri(uri);\n\t    opts.hostname = uri.host;\n\t    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n\t    opts.port = uri.port;\n\t    if (uri.query) opts.query = uri.query;\n\t  } else if (opts.host) {\n\t    opts.hostname = parseuri(opts.host).host;\n\t  }\n\t\n\t  this.secure = null != opts.secure ? opts.secure\n\t    : (global.location && 'https:' === location.protocol);\n\t\n\t  if (opts.hostname && !opts.port) {\n\t    // if no port is specified manually, use the protocol default\n\t    opts.port = this.secure ? '443' : '80';\n\t  }\n\t\n\t  this.agent = opts.agent || false;\n\t  this.hostname = opts.hostname ||\n\t    (global.location ? location.hostname : 'localhost');\n\t  this.port = opts.port || (global.location && location.port\n\t      ? location.port\n\t      : (this.secure ? 443 : 80));\n\t  this.query = opts.query || {};\n\t  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n\t  this.upgrade = false !== opts.upgrade;\n\t  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n\t  this.forceJSONP = !!opts.forceJSONP;\n\t  this.jsonp = false !== opts.jsonp;\n\t  this.forceBase64 = !!opts.forceBase64;\n\t  this.enablesXDR = !!opts.enablesXDR;\n\t  this.timestampParam = opts.timestampParam || 't';\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.transports = opts.transports || ['polling', 'websocket'];\n\t  this.transportOptions = opts.transportOptions || {};\n\t  this.readyState = '';\n\t  this.writeBuffer = [];\n\t  this.prevBufferLen = 0;\n\t  this.policyPort = opts.policyPort || 843;\n\t  this.rememberUpgrade = opts.rememberUpgrade || false;\n\t  this.binaryType = null;\n\t  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n\t  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\t\n\t  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n\t  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n\t    this.perMessageDeflate.threshold = 1024;\n\t  }\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx || null;\n\t  this.key = opts.key || null;\n\t  this.passphrase = opts.passphrase || null;\n\t  this.cert = opts.cert || null;\n\t  this.ca = opts.ca || null;\n\t  this.ciphers = opts.ciphers || null;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n\t  this.forceNode = !!opts.forceNode;\n\t\n\t  // other options for Node.js client\n\t  var freeGlobal = typeof global === 'object' && global;\n\t  if (freeGlobal.global === freeGlobal) {\n\t    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n\t      this.extraHeaders = opts.extraHeaders;\n\t    }\n\t\n\t    if (opts.localAddress) {\n\t      this.localAddress = opts.localAddress;\n\t    }\n\t  }\n\t\n\t  // set on handshake\n\t  this.id = null;\n\t  this.upgrades = null;\n\t  this.pingInterval = null;\n\t  this.pingTimeout = null;\n\t\n\t  // set on heartbeat\n\t  this.pingIntervalTimer = null;\n\t  this.pingTimeoutTimer = null;\n\t\n\t  this.open();\n\t}\n\t\n\tSocket.priorWebsocketSuccess = false;\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.protocol = parser.protocol; // this is an int\n\t\n\t/**\n\t * Expose deps for legacy compatibility\n\t * and standalone browser access.\n\t */\n\t\n\tSocket.Socket = Socket;\n\tSocket.Transport = __webpack_require__(20);\n\tSocket.transports = __webpack_require__(15);\n\tSocket.parser = __webpack_require__(21);\n\t\n\t/**\n\t * Creates transport of the given type.\n\t *\n\t * @param {String} transport name\n\t * @return {Transport}\n\t * @api private\n\t */\n\t\n\tSocket.prototype.createTransport = function (name) {\n\t  debug('creating transport \"%s\"', name);\n\t  var query = clone(this.query);\n\t\n\t  // append engine.io protocol identifier\n\t  query.EIO = parser.protocol;\n\t\n\t  // transport name\n\t  query.transport = name;\n\t\n\t  // per-transport options\n\t  var options = this.transportOptions[name] || {};\n\t\n\t  // session id if we already have one\n\t  if (this.id) query.sid = this.id;\n\t\n\t  var transport = new transports[name]({\n\t    query: query,\n\t    socket: this,\n\t    agent: options.agent || this.agent,\n\t    hostname: options.hostname || this.hostname,\n\t    port: options.port || this.port,\n\t    secure: options.secure || this.secure,\n\t    path: options.path || this.path,\n\t    forceJSONP: options.forceJSONP || this.forceJSONP,\n\t    jsonp: options.jsonp || this.jsonp,\n\t    forceBase64: options.forceBase64 || this.forceBase64,\n\t    enablesXDR: options.enablesXDR || this.enablesXDR,\n\t    timestampRequests: options.timestampRequests || this.timestampRequests,\n\t    timestampParam: options.timestampParam || this.timestampParam,\n\t    policyPort: options.policyPort || this.policyPort,\n\t    pfx: options.pfx || this.pfx,\n\t    key: options.key || this.key,\n\t    passphrase: options.passphrase || this.passphrase,\n\t    cert: options.cert || this.cert,\n\t    ca: options.ca || this.ca,\n\t    ciphers: options.ciphers || this.ciphers,\n\t    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n\t    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n\t    extraHeaders: options.extraHeaders || this.extraHeaders,\n\t    forceNode: options.forceNode || this.forceNode,\n\t    localAddress: options.localAddress || this.localAddress,\n\t    requestTimeout: options.requestTimeout || this.requestTimeout,\n\t    protocols: options.protocols || void (0)\n\t  });\n\t\n\t  return transport;\n\t};\n\t\n\tfunction clone (obj) {\n\t  var o = {};\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      o[i] = obj[i];\n\t    }\n\t  }\n\t  return o;\n\t}\n\t\n\t/**\n\t * Initializes transport to use and starts probe.\n\t *\n\t * @api private\n\t */\n\tSocket.prototype.open = function () {\n\t  var transport;\n\t  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n\t    transport = 'websocket';\n\t  } else if (0 === this.transports.length) {\n\t    // Emit error on next tick so it can be listened to\n\t    var self = this;\n\t    setTimeout(function () {\n\t      self.emit('error', 'No transports available');\n\t    }, 0);\n\t    return;\n\t  } else {\n\t    transport = this.transports[0];\n\t  }\n\t  this.readyState = 'opening';\n\t\n\t  // Retry with the next transport if the transport is disabled (jsonp: false)\n\t  try {\n\t    transport = this.createTransport(transport);\n\t  } catch (e) {\n\t    this.transports.shift();\n\t    this.open();\n\t    return;\n\t  }\n\t\n\t  transport.open();\n\t  this.setTransport(transport);\n\t};\n\t\n\t/**\n\t * Sets the current transport. Disables the existing one (if any).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setTransport = function (transport) {\n\t  debug('setting transport %s', transport.name);\n\t  var self = this;\n\t\n\t  if (this.transport) {\n\t    debug('clearing existing transport %s', this.transport.name);\n\t    this.transport.removeAllListeners();\n\t  }\n\t\n\t  // set up transport\n\t  this.transport = transport;\n\t\n\t  // set up transport listeners\n\t  transport\n\t  .on('drain', function () {\n\t    self.onDrain();\n\t  })\n\t  .on('packet', function (packet) {\n\t    self.onPacket(packet);\n\t  })\n\t  .on('error', function (e) {\n\t    self.onError(e);\n\t  })\n\t  .on('close', function () {\n\t    self.onClose('transport close');\n\t  });\n\t};\n\t\n\t/**\n\t * Probes a transport.\n\t *\n\t * @param {String} transport name\n\t * @api private\n\t */\n\t\n\tSocket.prototype.probe = function (name) {\n\t  debug('probing transport \"%s\"', name);\n\t  var transport = this.createTransport(name, { probe: 1 });\n\t  var failed = false;\n\t  var self = this;\n\t\n\t  Socket.priorWebsocketSuccess = false;\n\t\n\t  function onTransportOpen () {\n\t    if (self.onlyBinaryUpgrades) {\n\t      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n\t      failed = failed || upgradeLosesBinary;\n\t    }\n\t    if (failed) return;\n\t\n\t    debug('probe transport \"%s\" opened', name);\n\t    transport.send([{ type: 'ping', data: 'probe' }]);\n\t    transport.once('packet', function (msg) {\n\t      if (failed) return;\n\t      if ('pong' === msg.type && 'probe' === msg.data) {\n\t        debug('probe transport \"%s\" pong', name);\n\t        self.upgrading = true;\n\t        self.emit('upgrading', transport);\n\t        if (!transport) return;\n\t        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\t\n\t        debug('pausing current transport \"%s\"', self.transport.name);\n\t        self.transport.pause(function () {\n\t          if (failed) return;\n\t          if ('closed' === self.readyState) return;\n\t          debug('changing transport and sending upgrade packet');\n\t\n\t          cleanup();\n\t\n\t          self.setTransport(transport);\n\t          transport.send([{ type: 'upgrade' }]);\n\t          self.emit('upgrade', transport);\n\t          transport = null;\n\t          self.upgrading = false;\n\t          self.flush();\n\t        });\n\t      } else {\n\t        debug('probe transport \"%s\" failed', name);\n\t        var err = new Error('probe error');\n\t        err.transport = transport.name;\n\t        self.emit('upgradeError', err);\n\t      }\n\t    });\n\t  }\n\t\n\t  function freezeTransport () {\n\t    if (failed) return;\n\t\n\t    // Any callback called by transport should be ignored since now\n\t    failed = true;\n\t\n\t    cleanup();\n\t\n\t    transport.close();\n\t    transport = null;\n\t  }\n\t\n\t  // Handle any error that happens while probing\n\t  function onerror (err) {\n\t    var error = new Error('probe error: ' + err);\n\t    error.transport = transport.name;\n\t\n\t    freezeTransport();\n\t\n\t    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\t\n\t    self.emit('upgradeError', error);\n\t  }\n\t\n\t  function onTransportClose () {\n\t    onerror('transport closed');\n\t  }\n\t\n\t  // When the socket is closed while we're probing\n\t  function onclose () {\n\t    onerror('socket closed');\n\t  }\n\t\n\t  // When the socket is upgraded while we're probing\n\t  function onupgrade (to) {\n\t    if (transport && to.name !== transport.name) {\n\t      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n\t      freezeTransport();\n\t    }\n\t  }\n\t\n\t  // Remove all listeners on the transport and on self\n\t  function cleanup () {\n\t    transport.removeListener('open', onTransportOpen);\n\t    transport.removeListener('error', onerror);\n\t    transport.removeListener('close', onTransportClose);\n\t    self.removeListener('close', onclose);\n\t    self.removeListener('upgrading', onupgrade);\n\t  }\n\t\n\t  transport.once('open', onTransportOpen);\n\t  transport.once('error', onerror);\n\t  transport.once('close', onTransportClose);\n\t\n\t  this.once('close', onclose);\n\t  this.once('upgrading', onupgrade);\n\t\n\t  transport.open();\n\t};\n\t\n\t/**\n\t * Called when connection is deemed open.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.onOpen = function () {\n\t  debug('socket open');\n\t  this.readyState = 'open';\n\t  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n\t  this.emit('open');\n\t  this.flush();\n\t\n\t  // we check for `readyState` in case an `open`\n\t  // listener already closed the socket\n\t  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\t    debug('starting upgrade probes');\n\t    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n\t      this.probe(this.upgrades[i]);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Handles a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onPacket = function (packet) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState ||\n\t      'closing' === this.readyState) {\n\t    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\t\n\t    this.emit('packet', packet);\n\t\n\t    // Socket is live - any packet counts\n\t    this.emit('heartbeat');\n\t\n\t    switch (packet.type) {\n\t      case 'open':\n\t        this.onHandshake(JSON.parse(packet.data));\n\t        break;\n\t\n\t      case 'pong':\n\t        this.setPing();\n\t        this.emit('pong');\n\t        break;\n\t\n\t      case 'error':\n\t        var err = new Error('server error');\n\t        err.code = packet.data;\n\t        this.onError(err);\n\t        break;\n\t\n\t      case 'message':\n\t        this.emit('data', packet.data);\n\t        this.emit('message', packet.data);\n\t        break;\n\t    }\n\t  } else {\n\t    debug('packet received with socket readyState \"%s\"', this.readyState);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon handshake completion.\n\t *\n\t * @param {Object} handshake obj\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHandshake = function (data) {\n\t  this.emit('handshake', data);\n\t  this.id = data.sid;\n\t  this.transport.query.sid = data.sid;\n\t  this.upgrades = this.filterUpgrades(data.upgrades);\n\t  this.pingInterval = data.pingInterval;\n\t  this.pingTimeout = data.pingTimeout;\n\t  this.onOpen();\n\t  // In case open handler closes socket\n\t  if ('closed' === this.readyState) return;\n\t  this.setPing();\n\t\n\t  // Prolong liveness of socket on heartbeat\n\t  this.removeListener('heartbeat', this.onHeartbeat);\n\t  this.on('heartbeat', this.onHeartbeat);\n\t};\n\t\n\t/**\n\t * Resets ping timeout.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHeartbeat = function (timeout) {\n\t  clearTimeout(this.pingTimeoutTimer);\n\t  var self = this;\n\t  self.pingTimeoutTimer = setTimeout(function () {\n\t    if ('closed' === self.readyState) return;\n\t    self.onClose('ping timeout');\n\t  }, timeout || (self.pingInterval + self.pingTimeout));\n\t};\n\t\n\t/**\n\t * Pings server every `this.pingInterval` and expects response\n\t * within `this.pingTimeout` or closes connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setPing = function () {\n\t  var self = this;\n\t  clearTimeout(self.pingIntervalTimer);\n\t  self.pingIntervalTimer = setTimeout(function () {\n\t    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n\t    self.ping();\n\t    self.onHeartbeat(self.pingTimeout);\n\t  }, self.pingInterval);\n\t};\n\t\n\t/**\n\t* Sends a ping packet.\n\t*\n\t* @api private\n\t*/\n\t\n\tSocket.prototype.ping = function () {\n\t  var self = this;\n\t  this.sendPacket('ping', function () {\n\t    self.emit('ping');\n\t  });\n\t};\n\t\n\t/**\n\t * Called on `drain` event\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onDrain = function () {\n\t  this.writeBuffer.splice(0, this.prevBufferLen);\n\t\n\t  // setting prevBufferLen = 0 is very important\n\t  // for example, when upgrading, upgrade packet is sent over,\n\t  // and a nonzero prevBufferLen could cause problems on `drain`\n\t  this.prevBufferLen = 0;\n\t\n\t  if (0 === this.writeBuffer.length) {\n\t    this.emit('drain');\n\t  } else {\n\t    this.flush();\n\t  }\n\t};\n\t\n\t/**\n\t * Flush write buffers.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.flush = function () {\n\t  if ('closed' !== this.readyState && this.transport.writable &&\n\t    !this.upgrading && this.writeBuffer.length) {\n\t    debug('flushing %d packets in socket', this.writeBuffer.length);\n\t    this.transport.send(this.writeBuffer);\n\t    // keep track of current length of writeBuffer\n\t    // splice writeBuffer and callbackBuffer on `drain`\n\t    this.prevBufferLen = this.writeBuffer.length;\n\t    this.emit('flush');\n\t  }\n\t};\n\t\n\t/**\n\t * Sends a message.\n\t *\n\t * @param {String} message.\n\t * @param {Function} callback function.\n\t * @param {Object} options.\n\t * @return {Socket} for chaining.\n\t * @api public\n\t */\n\t\n\tSocket.prototype.write =\n\tSocket.prototype.send = function (msg, options, fn) {\n\t  this.sendPacket('message', msg, options, fn);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {String} packet type.\n\t * @param {String} data.\n\t * @param {Object} options.\n\t * @param {Function} callback function.\n\t * @api private\n\t */\n\t\n\tSocket.prototype.sendPacket = function (type, data, options, fn) {\n\t  if ('function' === typeof data) {\n\t    fn = data;\n\t    data = undefined;\n\t  }\n\t\n\t  if ('function' === typeof options) {\n\t    fn = options;\n\t    options = null;\n\t  }\n\t\n\t  if ('closing' === this.readyState || 'closed' === this.readyState) {\n\t    return;\n\t  }\n\t\n\t  options = options || {};\n\t  options.compress = false !== options.compress;\n\t\n\t  var packet = {\n\t    type: type,\n\t    data: data,\n\t    options: options\n\t  };\n\t  this.emit('packetCreate', packet);\n\t  this.writeBuffer.push(packet);\n\t  if (fn) this.once('flush', fn);\n\t  this.flush();\n\t};\n\t\n\t/**\n\t * Closes the connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.readyState = 'closing';\n\t\n\t    var self = this;\n\t\n\t    if (this.writeBuffer.length) {\n\t      this.once('drain', function () {\n\t        if (this.upgrading) {\n\t          waitForUpgrade();\n\t        } else {\n\t          close();\n\t        }\n\t      });\n\t    } else if (this.upgrading) {\n\t      waitForUpgrade();\n\t    } else {\n\t      close();\n\t    }\n\t  }\n\t\n\t  function close () {\n\t    self.onClose('forced close');\n\t    debug('socket closing - telling transport to close');\n\t    self.transport.close();\n\t  }\n\t\n\t  function cleanupAndClose () {\n\t    self.removeListener('upgrade', cleanupAndClose);\n\t    self.removeListener('upgradeError', cleanupAndClose);\n\t    close();\n\t  }\n\t\n\t  function waitForUpgrade () {\n\t    // wait for upgrade to finish since we can't send packets while pausing a transport\n\t    self.once('upgrade', cleanupAndClose);\n\t    self.once('upgradeError', cleanupAndClose);\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport error\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onError = function (err) {\n\t  debug('socket error %j', err);\n\t  Socket.priorWebsocketSuccess = false;\n\t  this.emit('error', err);\n\t  this.onClose('transport error', err);\n\t};\n\t\n\t/**\n\t * Called upon transport close.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onClose = function (reason, desc) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\t    debug('socket close with reason: \"%s\"', reason);\n\t    var self = this;\n\t\n\t    // clear timers\n\t    clearTimeout(this.pingIntervalTimer);\n\t    clearTimeout(this.pingTimeoutTimer);\n\t\n\t    // stop event from firing again for transport\n\t    this.transport.removeAllListeners('close');\n\t\n\t    // ensure transport won't stay open\n\t    this.transport.close();\n\t\n\t    // ignore further transport communication\n\t    this.transport.removeAllListeners();\n\t\n\t    // set ready state\n\t    this.readyState = 'closed';\n\t\n\t    // clear session id\n\t    this.id = null;\n\t\n\t    // emit close event\n\t    this.emit('close', reason, desc);\n\t\n\t    // clean buffers after, so users can still\n\t    // grab the buffers on `close` event\n\t    self.writeBuffer = [];\n\t    self.prevBufferLen = 0;\n\t  }\n\t};\n\t\n\t/**\n\t * Filters upgrades, returning only those matching client transports.\n\t *\n\t * @param {Array} server upgrades\n\t * @api private\n\t *\n\t */\n\t\n\tSocket.prototype.filterUpgrades = function (upgrades) {\n\t  var filteredUpgrades = [];\n\t  for (var i = 0, j = upgrades.length; i < j; i++) {\n\t    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n\t  }\n\t  return filteredUpgrades;\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module dependencies\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(16);\n\tvar XHR = __webpack_require__(18);\n\tvar JSONP = __webpack_require__(33);\n\tvar websocket = __webpack_require__(34);\n\t\n\t/**\n\t * Export transports.\n\t */\n\t\n\texports.polling = polling;\n\texports.websocket = websocket;\n\t\n\t/**\n\t * Polling transport polymorphic constructor.\n\t * Decides on xhr vs jsonp based on feature detection.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction polling (opts) {\n\t  var xhr;\n\t  var xd = false;\n\t  var xs = false;\n\t  var jsonp = false !== opts.jsonp;\n\t\n\t  if (global.location) {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    xd = opts.hostname !== location.hostname || port !== opts.port;\n\t    xs = opts.secure !== isSSL;\n\t  }\n\t\n\t  opts.xdomain = xd;\n\t  opts.xscheme = xs;\n\t  xhr = new XMLHttpRequest(opts);\n\t\n\t  if ('open' in xhr && !opts.forceJSONP) {\n\t    return new XHR(opts);\n\t  } else {\n\t    if (!jsonp) throw new Error('JSONP disabled');\n\t    return new JSONP(opts);\n\t  }\n\t}\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {// browser shim for xmlhttprequest module\n\t\n\tvar hasCORS = __webpack_require__(17);\n\t\n\tmodule.exports = function (opts) {\n\t  var xdomain = opts.xdomain;\n\t\n\t  // scheme must be same when usign XDomainRequest\n\t  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\t  var xscheme = opts.xscheme;\n\t\n\t  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n\t  // https://github.com/Automattic/engine.io-client/pull/217\n\t  var enablesXDR = opts.enablesXDR;\n\t\n\t  // XMLHttpRequest can be disabled on IE\n\t  try {\n\t    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n\t      return new XMLHttpRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  // Use XDomainRequest for IE8 if enablesXDR is true\n\t  // because loading bar keeps flashing when using jsonp-polling\n\t  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n\t  try {\n\t    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n\t      return new XDomainRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  if (!xdomain) {\n\t    try {\n\t      return new global[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n\t    } catch (e) { }\n\t  }\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t *\n\t * Logic borrowed from Modernizr:\n\t *\n\t *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n\t */\n\t\n\ttry {\n\t  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n\t    'withCredentials' in new XMLHttpRequest();\n\t} catch (err) {\n\t  // if XMLHttp support is disabled in IE then it will throw\n\t  // when trying to create\n\t  module.exports = false;\n\t}\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module requirements.\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(16);\n\tvar Polling = __webpack_require__(19);\n\tvar Emitter = __webpack_require__(8);\n\tvar inherit = __webpack_require__(31);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling-xhr');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = XHR;\n\tmodule.exports.Request = Request;\n\t\n\t/**\n\t * Empty function\n\t */\n\t\n\tfunction empty () {}\n\t\n\t/**\n\t * XHR Polling constructor.\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction XHR (opts) {\n\t  Polling.call(this, opts);\n\t  this.requestTimeout = opts.requestTimeout;\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  if (global.location) {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    this.xd = opts.hostname !== global.location.hostname ||\n\t      port !== opts.port;\n\t    this.xs = opts.secure !== isSSL;\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(XHR, Polling);\n\t\n\t/**\n\t * XHR supports binary\n\t */\n\t\n\tXHR.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Creates a request.\n\t *\n\t * @param {String} method\n\t * @api private\n\t */\n\t\n\tXHR.prototype.request = function (opts) {\n\t  opts = opts || {};\n\t  opts.uri = this.uri();\n\t  opts.xd = this.xd;\n\t  opts.xs = this.xs;\n\t  opts.agent = this.agent || false;\n\t  opts.supportsBinary = this.supportsBinary;\n\t  opts.enablesXDR = this.enablesXDR;\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  opts.requestTimeout = this.requestTimeout;\n\t\n\t  // other options for Node.js client\n\t  opts.extraHeaders = this.extraHeaders;\n\t\n\t  return new Request(opts);\n\t};\n\t\n\t/**\n\t * Sends data.\n\t *\n\t * @param {String} data to send.\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doWrite = function (data, fn) {\n\t  var isBinary = typeof data !== 'string' && data !== undefined;\n\t  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n\t  var self = this;\n\t  req.on('success', fn);\n\t  req.on('error', function (err) {\n\t    self.onError('xhr post error', err);\n\t  });\n\t  this.sendXhr = req;\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doPoll = function () {\n\t  debug('xhr poll');\n\t  var req = this.request();\n\t  var self = this;\n\t  req.on('data', function (data) {\n\t    self.onData(data);\n\t  });\n\t  req.on('error', function (err) {\n\t    self.onError('xhr poll error', err);\n\t  });\n\t  this.pollXhr = req;\n\t};\n\t\n\t/**\n\t * Request constructor\n\t *\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Request (opts) {\n\t  this.method = opts.method || 'GET';\n\t  this.uri = opts.uri;\n\t  this.xd = !!opts.xd;\n\t  this.xs = !!opts.xs;\n\t  this.async = false !== opts.async;\n\t  this.data = undefined !== opts.data ? opts.data : null;\n\t  this.agent = opts.agent;\n\t  this.isBinary = opts.isBinary;\n\t  this.supportsBinary = opts.supportsBinary;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.requestTimeout = opts.requestTimeout;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  this.create();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Request.prototype);\n\t\n\t/**\n\t * Creates the XHR object and sends the request.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.create = function () {\n\t  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t\n\t  var xhr = this.xhr = new XMLHttpRequest(opts);\n\t  var self = this;\n\t\n\t  try {\n\t    debug('xhr open %s: %s', this.method, this.uri);\n\t    xhr.open(this.method, this.uri, this.async);\n\t    try {\n\t      if (this.extraHeaders) {\n\t        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n\t        for (var i in this.extraHeaders) {\n\t          if (this.extraHeaders.hasOwnProperty(i)) {\n\t            xhr.setRequestHeader(i, this.extraHeaders[i]);\n\t          }\n\t        }\n\t      }\n\t    } catch (e) {}\n\t\n\t    if ('POST' === this.method) {\n\t      try {\n\t        if (this.isBinary) {\n\t          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n\t        } else {\n\t          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n\t        }\n\t      } catch (e) {}\n\t    }\n\t\n\t    try {\n\t      xhr.setRequestHeader('Accept', '*/*');\n\t    } catch (e) {}\n\t\n\t    // ie6 check\n\t    if ('withCredentials' in xhr) {\n\t      xhr.withCredentials = true;\n\t    }\n\t\n\t    if (this.requestTimeout) {\n\t      xhr.timeout = this.requestTimeout;\n\t    }\n\t\n\t    if (this.hasXDR()) {\n\t      xhr.onload = function () {\n\t        self.onLoad();\n\t      };\n\t      xhr.onerror = function () {\n\t        self.onError(xhr.responseText);\n\t      };\n\t    } else {\n\t      xhr.onreadystatechange = function () {\n\t        if (xhr.readyState === 2) {\n\t          try {\n\t            var contentType = xhr.getResponseHeader('Content-Type');\n\t            if (self.supportsBinary && contentType === 'application/octet-stream') {\n\t              xhr.responseType = 'arraybuffer';\n\t            }\n\t          } catch (e) {}\n\t        }\n\t        if (4 !== xhr.readyState) return;\n\t        if (200 === xhr.status || 1223 === xhr.status) {\n\t          self.onLoad();\n\t        } else {\n\t          // make sure the `error` event handler that's user-set\n\t          // does not throw in the same tick and gets caught here\n\t          setTimeout(function () {\n\t            self.onError(xhr.status);\n\t          }, 0);\n\t        }\n\t      };\n\t    }\n\t\n\t    debug('xhr data %s', this.data);\n\t    xhr.send(this.data);\n\t  } catch (e) {\n\t    // Need to defer since .create() is called directly fhrom the constructor\n\t    // and thus the 'error' event can only be only bound *after* this exception\n\t    // occurs.  Therefore, also, we cannot throw here at all.\n\t    setTimeout(function () {\n\t      self.onError(e);\n\t    }, 0);\n\t    return;\n\t  }\n\t\n\t  if (global.document) {\n\t    this.index = Request.requestsCount++;\n\t    Request.requests[this.index] = this;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful response.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onSuccess = function () {\n\t  this.emit('success');\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Called if we have data.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onData = function (data) {\n\t  this.emit('data', data);\n\t  this.onSuccess();\n\t};\n\t\n\t/**\n\t * Called upon error.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onError = function (err) {\n\t  this.emit('error', err);\n\t  this.cleanup(true);\n\t};\n\t\n\t/**\n\t * Cleans up house.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.cleanup = function (fromError) {\n\t  if ('undefined' === typeof this.xhr || null === this.xhr) {\n\t    return;\n\t  }\n\t  // xmlhttprequest\n\t  if (this.hasXDR()) {\n\t    this.xhr.onload = this.xhr.onerror = empty;\n\t  } else {\n\t    this.xhr.onreadystatechange = empty;\n\t  }\n\t\n\t  if (fromError) {\n\t    try {\n\t      this.xhr.abort();\n\t    } catch (e) {}\n\t  }\n\t\n\t  if (global.document) {\n\t    delete Request.requests[this.index];\n\t  }\n\t\n\t  this.xhr = null;\n\t};\n\t\n\t/**\n\t * Called upon load.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onLoad = function () {\n\t  var data;\n\t  try {\n\t    var contentType;\n\t    try {\n\t      contentType = this.xhr.getResponseHeader('Content-Type');\n\t    } catch (e) {}\n\t    if (contentType === 'application/octet-stream') {\n\t      data = this.xhr.response || this.xhr.responseText;\n\t    } else {\n\t      data = this.xhr.responseText;\n\t    }\n\t  } catch (e) {\n\t    this.onError(e);\n\t  }\n\t  if (null != data) {\n\t    this.onData(data);\n\t  }\n\t};\n\t\n\t/**\n\t * Check if it has XDomainRequest.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.hasXDR = function () {\n\t  return 'undefined' !== typeof global.XDomainRequest && !this.xs && this.enablesXDR;\n\t};\n\t\n\t/**\n\t * Aborts the request.\n\t *\n\t * @api public\n\t */\n\t\n\tRequest.prototype.abort = function () {\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Aborts pending requests when unloading the window. This is needed to prevent\n\t * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n\t * emitted.\n\t */\n\t\n\tRequest.requestsCount = 0;\n\tRequest.requests = {};\n\t\n\tif (global.document) {\n\t  if (global.attachEvent) {\n\t    global.attachEvent('onunload', unloadHandler);\n\t  } else if (global.addEventListener) {\n\t    global.addEventListener('beforeunload', unloadHandler, false);\n\t  }\n\t}\n\t\n\tfunction unloadHandler () {\n\t  for (var i in Request.requests) {\n\t    if (Request.requests.hasOwnProperty(i)) {\n\t      Request.requests[i].abort();\n\t    }\n\t  }\n\t}\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(20);\n\tvar parseqs = __webpack_require__(30);\n\tvar parser = __webpack_require__(21);\n\tvar inherit = __webpack_require__(31);\n\tvar yeast = __webpack_require__(32);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Polling;\n\t\n\t/**\n\t * Is XHR2 supported?\n\t */\n\t\n\tvar hasXHR2 = (function () {\n\t  var XMLHttpRequest = __webpack_require__(16);\n\t  var xhr = new XMLHttpRequest({ xdomain: false });\n\t  return null != xhr.responseType;\n\t})();\n\t\n\t/**\n\t * Polling interface.\n\t *\n\t * @param {Object} opts\n\t * @api private\n\t */\n\t\n\tfunction Polling (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (!hasXHR2 || forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(Polling, Transport);\n\t\n\t/**\n\t * Transport name.\n\t */\n\t\n\tPolling.prototype.name = 'polling';\n\t\n\t/**\n\t * Opens the socket (triggers polling). We write a PING message to determine\n\t * when the transport is open.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doOpen = function () {\n\t  this.poll();\n\t};\n\t\n\t/**\n\t * Pauses polling.\n\t *\n\t * @param {Function} callback upon buffers are flushed and transport is paused\n\t * @api private\n\t */\n\t\n\tPolling.prototype.pause = function (onPause) {\n\t  var self = this;\n\t\n\t  this.readyState = 'pausing';\n\t\n\t  function pause () {\n\t    debug('paused');\n\t    self.readyState = 'paused';\n\t    onPause();\n\t  }\n\t\n\t  if (this.polling || !this.writable) {\n\t    var total = 0;\n\t\n\t    if (this.polling) {\n\t      debug('we are currently polling - waiting to pause');\n\t      total++;\n\t      this.once('pollComplete', function () {\n\t        debug('pre-pause polling complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t\n\t    if (!this.writable) {\n\t      debug('we are currently writing - waiting to pause');\n\t      total++;\n\t      this.once('drain', function () {\n\t        debug('pre-pause writing complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t  } else {\n\t    pause();\n\t  }\n\t};\n\t\n\t/**\n\t * Starts polling cycle.\n\t *\n\t * @api public\n\t */\n\t\n\tPolling.prototype.poll = function () {\n\t  debug('polling');\n\t  this.polling = true;\n\t  this.doPoll();\n\t  this.emit('poll');\n\t};\n\t\n\t/**\n\t * Overloads onData to detect payloads.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.onData = function (data) {\n\t  var self = this;\n\t  debug('polling got data %s', data);\n\t  var callback = function (packet, index, total) {\n\t    // if its the first message we consider the transport open\n\t    if ('opening' === self.readyState) {\n\t      self.onOpen();\n\t    }\n\t\n\t    // if its a close packet, we close the ongoing requests\n\t    if ('close' === packet.type) {\n\t      self.onClose();\n\t      return false;\n\t    }\n\t\n\t    // otherwise bypass onData and handle the message\n\t    self.onPacket(packet);\n\t  };\n\t\n\t  // decode payload\n\t  parser.decodePayload(data, this.socket.binaryType, callback);\n\t\n\t  // if an event did not trigger closing\n\t  if ('closed' !== this.readyState) {\n\t    // if we got data we're not polling\n\t    this.polling = false;\n\t    this.emit('pollComplete');\n\t\n\t    if ('open' === this.readyState) {\n\t      this.poll();\n\t    } else {\n\t      debug('ignoring poll - transport state \"%s\"', this.readyState);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * For polling, send a close packet.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doClose = function () {\n\t  var self = this;\n\t\n\t  function close () {\n\t    debug('writing close packet');\n\t    self.write([{ type: 'close' }]);\n\t  }\n\t\n\t  if ('open' === this.readyState) {\n\t    debug('transport open - closing');\n\t    close();\n\t  } else {\n\t    // in case we're trying to close while\n\t    // handshaking is in progress (GH-164)\n\t    debug('transport not open - deferring close');\n\t    this.once('open', close);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes a packets payload.\n\t *\n\t * @param {Array} data packets\n\t * @param {Function} drain callback\n\t * @api private\n\t */\n\t\n\tPolling.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t  var callbackfn = function () {\n\t    self.writable = true;\n\t    self.emit('drain');\n\t  };\n\t\n\t  parser.encodePayload(packets, this.supportsBinary, function (data) {\n\t    self.doWrite(data, callbackfn);\n\t  });\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'https' : 'http';\n\t  var port = '';\n\t\n\t  // cache busting is forced\n\t  if (false !== this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  if (!this.supportsBinary && !query.sid) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n\t     ('http' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(21);\n\tvar Emitter = __webpack_require__(8);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Transport;\n\t\n\t/**\n\t * Transport abstract constructor.\n\t *\n\t * @param {Object} options.\n\t * @api private\n\t */\n\t\n\tfunction Transport (opts) {\n\t  this.path = opts.path;\n\t  this.hostname = opts.hostname;\n\t  this.port = opts.port;\n\t  this.secure = opts.secure;\n\t  this.query = opts.query;\n\t  this.timestampParam = opts.timestampParam;\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.readyState = '';\n\t  this.agent = opts.agent || false;\n\t  this.socket = opts.socket;\n\t  this.enablesXDR = opts.enablesXDR;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t  this.forceNode = opts.forceNode;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t  this.localAddress = opts.localAddress;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Transport.prototype);\n\t\n\t/**\n\t * Emits an error.\n\t *\n\t * @param {String} str\n\t * @return {Transport} for chaining\n\t * @api public\n\t */\n\t\n\tTransport.prototype.onError = function (msg, desc) {\n\t  var err = new Error(msg);\n\t  err.type = 'TransportError';\n\t  err.description = desc;\n\t  this.emit('error', err);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Opens the transport.\n\t *\n\t * @api public\n\t */\n\t\n\tTransport.prototype.open = function () {\n\t  if ('closed' === this.readyState || '' === this.readyState) {\n\t    this.readyState = 'opening';\n\t    this.doOpen();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Closes the transport.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.doClose();\n\t    this.onClose();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends multiple packets.\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\tTransport.prototype.send = function (packets) {\n\t  if ('open' === this.readyState) {\n\t    this.write(packets);\n\t  } else {\n\t    throw new Error('Transport not open');\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon open\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onOpen = function () {\n\t  this.readyState = 'open';\n\t  this.writable = true;\n\t  this.emit('open');\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @param {String} data\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onData = function (data) {\n\t  var packet = parser.decodePacket(data, this.socket.binaryType);\n\t  this.onPacket(packet);\n\t};\n\t\n\t/**\n\t * Called with a decoded packet.\n\t */\n\t\n\tTransport.prototype.onPacket = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon close.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onClose = function () {\n\t  this.readyState = 'closed';\n\t  this.emit('close');\n\t};\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module dependencies.\n\t */\n\t\n\tvar keys = __webpack_require__(22);\n\tvar hasBinary = __webpack_require__(23);\n\tvar sliceBuffer = __webpack_require__(24);\n\tvar after = __webpack_require__(25);\n\tvar utf8 = __webpack_require__(26);\n\t\n\tvar base64encoder;\n\tif (global && global.ArrayBuffer) {\n\t  base64encoder = __webpack_require__(28);\n\t}\n\t\n\t/**\n\t * Check if we are running an android browser. That requires us to use\n\t * ArrayBuffer with polling transports...\n\t *\n\t * http://ghinda.net/jpeg-blob-ajax-android/\n\t */\n\t\n\tvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\t\n\t/**\n\t * Check if we are running in PhantomJS.\n\t * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n\t * https://github.com/ariya/phantomjs/issues/11395\n\t * @type boolean\n\t */\n\tvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\t\n\t/**\n\t * When true, avoids using Blobs to encode payloads.\n\t * @type boolean\n\t */\n\tvar dontSendBlobs = isAndroid || isPhantomJS;\n\t\n\t/**\n\t * Current protocol version.\n\t */\n\t\n\texports.protocol = 3;\n\t\n\t/**\n\t * Packet types.\n\t */\n\t\n\tvar packets = exports.packets = {\n\t    open:     0    // non-ws\n\t  , close:    1    // non-ws\n\t  , ping:     2\n\t  , pong:     3\n\t  , message:  4\n\t  , upgrade:  5\n\t  , noop:     6\n\t};\n\t\n\tvar packetslist = keys(packets);\n\t\n\t/**\n\t * Premade error packet.\n\t */\n\t\n\tvar err = { type: 'error', data: 'parser error' };\n\t\n\t/**\n\t * Create a blob api even for blob builder when vendor prefixes exist\n\t */\n\t\n\tvar Blob = __webpack_require__(29);\n\t\n\t/**\n\t * Encodes a packet.\n\t *\n\t *     <packet type id> [ <data> ]\n\t *\n\t * Example:\n\t *\n\t *     5hello world\n\t *     3\n\t *     4\n\t *\n\t * Binary is encoded in an identical principle\n\t *\n\t * @api private\n\t */\n\t\n\texports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = false;\n\t  }\n\t\n\t  if (typeof utf8encode === 'function') {\n\t    callback = utf8encode;\n\t    utf8encode = null;\n\t  }\n\t\n\t  var data = (packet.data === undefined)\n\t    ? undefined\n\t    : packet.data.buffer || packet.data;\n\t\n\t  if (global.ArrayBuffer && data instanceof ArrayBuffer) {\n\t    return encodeArrayBuffer(packet, supportsBinary, callback);\n\t  } else if (Blob && data instanceof global.Blob) {\n\t    return encodeBlob(packet, supportsBinary, callback);\n\t  }\n\t\n\t  // might be an object with { base64: true, data: dataAsBase64String }\n\t  if (data && data.base64) {\n\t    return encodeBase64Object(packet, callback);\n\t  }\n\t\n\t  // Sending data as a utf-8 string\n\t  var encoded = packets[packet.type];\n\t\n\t  // data fragment is optional\n\t  if (undefined !== packet.data) {\n\t    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n\t  }\n\t\n\t  return callback('' + encoded);\n\t\n\t};\n\t\n\tfunction encodeBase64Object(packet, callback) {\n\t  // packet data is an object { base64: true, data: dataAsBase64String }\n\t  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n\t  return callback(message);\n\t}\n\t\n\t/**\n\t * Encode packet helpers for binary types\n\t */\n\t\n\tfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var data = packet.data;\n\t  var contentArray = new Uint8Array(data);\n\t  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\t\n\t  resultBuffer[0] = packets[packet.type];\n\t  for (var i = 0; i < contentArray.length; i++) {\n\t    resultBuffer[i+1] = contentArray[i];\n\t  }\n\t\n\t  return callback(resultBuffer.buffer);\n\t}\n\t\n\tfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var fr = new FileReader();\n\t  fr.onload = function() {\n\t    packet.data = fr.result;\n\t    exports.encodePacket(packet, supportsBinary, true, callback);\n\t  };\n\t  return fr.readAsArrayBuffer(packet.data);\n\t}\n\t\n\tfunction encodeBlob(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  if (dontSendBlobs) {\n\t    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n\t  }\n\t\n\t  var length = new Uint8Array(1);\n\t  length[0] = packets[packet.type];\n\t  var blob = new Blob([length.buffer, packet.data]);\n\t\n\t  return callback(blob);\n\t}\n\t\n\t/**\n\t * Encodes a packet with binary data in a base64 string\n\t *\n\t * @param {Object} packet, has `type` and `data`\n\t * @return {String} base64 encoded message\n\t */\n\t\n\texports.encodeBase64Packet = function(packet, callback) {\n\t  var message = 'b' + exports.packets[packet.type];\n\t  if (Blob && packet.data instanceof global.Blob) {\n\t    var fr = new FileReader();\n\t    fr.onload = function() {\n\t      var b64 = fr.result.split(',')[1];\n\t      callback(message + b64);\n\t    };\n\t    return fr.readAsDataURL(packet.data);\n\t  }\n\t\n\t  var b64data;\n\t  try {\n\t    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n\t  } catch (e) {\n\t    // iPhone Safari doesn't let you apply with typed arrays\n\t    var typed = new Uint8Array(packet.data);\n\t    var basic = new Array(typed.length);\n\t    for (var i = 0; i < typed.length; i++) {\n\t      basic[i] = typed[i];\n\t    }\n\t    b64data = String.fromCharCode.apply(null, basic);\n\t  }\n\t  message += global.btoa(b64data);\n\t  return callback(message);\n\t};\n\t\n\t/**\n\t * Decodes a packet. Changes format to Blob if requested.\n\t *\n\t * @return {Object} with `type` and `data` (if any)\n\t * @api private\n\t */\n\t\n\texports.decodePacket = function (data, binaryType, utf8decode) {\n\t  if (data === undefined) {\n\t    return err;\n\t  }\n\t  // String data\n\t  if (typeof data === 'string') {\n\t    if (data.charAt(0) === 'b') {\n\t      return exports.decodeBase64Packet(data.substr(1), binaryType);\n\t    }\n\t\n\t    if (utf8decode) {\n\t      data = tryDecode(data);\n\t      if (data === false) {\n\t        return err;\n\t      }\n\t    }\n\t    var type = data.charAt(0);\n\t\n\t    if (Number(type) != type || !packetslist[type]) {\n\t      return err;\n\t    }\n\t\n\t    if (data.length > 1) {\n\t      return { type: packetslist[type], data: data.substring(1) };\n\t    } else {\n\t      return { type: packetslist[type] };\n\t    }\n\t  }\n\t\n\t  var asArray = new Uint8Array(data);\n\t  var type = asArray[0];\n\t  var rest = sliceBuffer(data, 1);\n\t  if (Blob && binaryType === 'blob') {\n\t    rest = new Blob([rest]);\n\t  }\n\t  return { type: packetslist[type], data: rest };\n\t};\n\t\n\tfunction tryDecode(data) {\n\t  try {\n\t    data = utf8.decode(data, { strict: false });\n\t  } catch (e) {\n\t    return false;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Decodes a packet encoded in a base64 string\n\t *\n\t * @param {String} base64 encoded message\n\t * @return {Object} with `type` and `data` (if any)\n\t */\n\t\n\texports.decodeBase64Packet = function(msg, binaryType) {\n\t  var type = packetslist[msg.charAt(0)];\n\t  if (!base64encoder) {\n\t    return { type: type, data: { base64: true, data: msg.substr(1) } };\n\t  }\n\t\n\t  var data = base64encoder.decode(msg.substr(1));\n\t\n\t  if (binaryType === 'blob' && Blob) {\n\t    data = new Blob([data]);\n\t  }\n\t\n\t  return { type: type, data: data };\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload).\n\t *\n\t *     <length>:data\n\t *\n\t * Example:\n\t *\n\t *     11:hello world2:hi\n\t *\n\t * If any contents are binary, they will be encoded as base64 strings. Base64\n\t * encoded strings are marked with a b before the length specifier\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\texports.encodePayload = function (packets, supportsBinary, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = null;\n\t  }\n\t\n\t  var isBinary = hasBinary(packets);\n\t\n\t  if (supportsBinary && isBinary) {\n\t    if (Blob && !dontSendBlobs) {\n\t      return exports.encodePayloadAsBlob(packets, callback);\n\t    }\n\t\n\t    return exports.encodePayloadAsArrayBuffer(packets, callback);\n\t  }\n\t\n\t  if (!packets.length) {\n\t    return callback('0:');\n\t  }\n\t\n\t  function setLengthHeader(message) {\n\t    return message.length + ':' + message;\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n\t      doneCallback(null, setLengthHeader(message));\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(results.join(''));\n\t  });\n\t};\n\t\n\t/**\n\t * Async array map using after\n\t */\n\t\n\tfunction map(ary, each, done) {\n\t  var result = new Array(ary.length);\n\t  var next = after(ary.length, done);\n\t\n\t  var eachWithIndex = function(i, el, cb) {\n\t    each(el, function(error, msg) {\n\t      result[i] = msg;\n\t      cb(error, result);\n\t    });\n\t  };\n\t\n\t  for (var i = 0; i < ary.length; i++) {\n\t    eachWithIndex(i, ary[i], next);\n\t  }\n\t}\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Possible binary contents are\n\t * decoded from their base64 representation\n\t *\n\t * @param {String} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayload = function (data, binaryType, callback) {\n\t  if (typeof data !== 'string') {\n\t    return exports.decodePayloadAsBinary(data, binaryType, callback);\n\t  }\n\t\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var packet;\n\t  if (data === '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t  var length = '', n, msg;\n\t\n\t  for (var i = 0, l = data.length; i < l; i++) {\n\t    var chr = data.charAt(i);\n\t\n\t    if (chr !== ':') {\n\t      length += chr;\n\t      continue;\n\t    }\n\t\n\t    if (length === '' || (length != (n = Number(length)))) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    msg = data.substr(i + 1, n);\n\t\n\t    if (length != msg.length) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    if (msg.length) {\n\t      packet = exports.decodePacket(msg, binaryType, false);\n\t\n\t      if (err.type === packet.type && err.data === packet.data) {\n\t        // parser error in individual packet - ignoring payload\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      var ret = callback(packet, i + n, l);\n\t      if (false === ret) return;\n\t    }\n\t\n\t    // advance cursor\n\t    i += n;\n\t    length = '';\n\t  }\n\t\n\t  if (length !== '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload) as binary.\n\t *\n\t * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n\t * 255><data>\n\t *\n\t * Example:\n\t * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n\t *\n\t * @param {Array} packets\n\t * @return {ArrayBuffer} encoded payload\n\t * @api private\n\t */\n\t\n\texports.encodePayloadAsArrayBuffer = function(packets, callback) {\n\t  if (!packets.length) {\n\t    return callback(new ArrayBuffer(0));\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(data) {\n\t      return doneCallback(null, data);\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, encodedPackets) {\n\t    var totalLength = encodedPackets.reduce(function(acc, p) {\n\t      var len;\n\t      if (typeof p === 'string'){\n\t        len = p.length;\n\t      } else {\n\t        len = p.byteLength;\n\t      }\n\t      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n\t    }, 0);\n\t\n\t    var resultArray = new Uint8Array(totalLength);\n\t\n\t    var bufferIndex = 0;\n\t    encodedPackets.forEach(function(p) {\n\t      var isString = typeof p === 'string';\n\t      var ab = p;\n\t      if (isString) {\n\t        var view = new Uint8Array(p.length);\n\t        for (var i = 0; i < p.length; i++) {\n\t          view[i] = p.charCodeAt(i);\n\t        }\n\t        ab = view.buffer;\n\t      }\n\t\n\t      if (isString) { // not true binary\n\t        resultArray[bufferIndex++] = 0;\n\t      } else { // true binary\n\t        resultArray[bufferIndex++] = 1;\n\t      }\n\t\n\t      var lenStr = ab.byteLength.toString();\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n\t      }\n\t      resultArray[bufferIndex++] = 255;\n\t\n\t      var view = new Uint8Array(ab);\n\t      for (var i = 0; i < view.length; i++) {\n\t        resultArray[bufferIndex++] = view[i];\n\t      }\n\t    });\n\t\n\t    return callback(resultArray.buffer);\n\t  });\n\t};\n\t\n\t/**\n\t * Encode as Blob\n\t */\n\t\n\texports.encodePayloadAsBlob = function(packets, callback) {\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(encoded) {\n\t      var binaryIdentifier = new Uint8Array(1);\n\t      binaryIdentifier[0] = 1;\n\t      if (typeof encoded === 'string') {\n\t        var view = new Uint8Array(encoded.length);\n\t        for (var i = 0; i < encoded.length; i++) {\n\t          view[i] = encoded.charCodeAt(i);\n\t        }\n\t        encoded = view.buffer;\n\t        binaryIdentifier[0] = 0;\n\t      }\n\t\n\t      var len = (encoded instanceof ArrayBuffer)\n\t        ? encoded.byteLength\n\t        : encoded.size;\n\t\n\t      var lenStr = len.toString();\n\t      var lengthAry = new Uint8Array(lenStr.length + 1);\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        lengthAry[i] = parseInt(lenStr[i]);\n\t      }\n\t      lengthAry[lenStr.length] = 255;\n\t\n\t      if (Blob) {\n\t        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n\t        doneCallback(null, blob);\n\t      }\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(new Blob(results));\n\t  });\n\t};\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Strings are decoded by\n\t * interpreting each byte as a key code for entries marked to start with 0. See\n\t * description of encodePayloadAsBinary\n\t *\n\t * @param {ArrayBuffer} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayloadAsBinary = function (data, binaryType, callback) {\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var bufferTail = data;\n\t  var buffers = [];\n\t\n\t  while (bufferTail.byteLength > 0) {\n\t    var tailArray = new Uint8Array(bufferTail);\n\t    var isString = tailArray[0] === 0;\n\t    var msgLength = '';\n\t\n\t    for (var i = 1; ; i++) {\n\t      if (tailArray[i] === 255) break;\n\t\n\t      // 310 = char length of Number.MAX_VALUE\n\t      if (msgLength.length > 310) {\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      msgLength += tailArray[i];\n\t    }\n\t\n\t    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n\t    msgLength = parseInt(msgLength);\n\t\n\t    var msg = sliceBuffer(bufferTail, 0, msgLength);\n\t    if (isString) {\n\t      try {\n\t        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n\t      } catch (e) {\n\t        // iPhone Safari doesn't let you apply to typed arrays\n\t        var typed = new Uint8Array(msg);\n\t        msg = '';\n\t        for (var i = 0; i < typed.length; i++) {\n\t          msg += String.fromCharCode(typed[i]);\n\t        }\n\t      }\n\t    }\n\t\n\t    buffers.push(msg);\n\t    bufferTail = sliceBuffer(bufferTail, msgLength);\n\t  }\n\t\n\t  var total = buffers.length;\n\t  buffers.forEach(function(buffer, i) {\n\t    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n\t  });\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Gets the keys for an object.\n\t *\n\t * @return {Array} keys\n\t * @api private\n\t */\n\t\n\tmodule.exports = Object.keys || function keys (obj){\n\t  var arr = [];\n\t  var has = Object.prototype.hasOwnProperty;\n\t\n\t  for (var i in obj) {\n\t    if (has.call(obj, i)) {\n\t      arr.push(i);\n\t    }\n\t  }\n\t  return arr;\n\t};\n\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/* global Blob File */\n\t\n\t/*\n\t * Module requirements.\n\t */\n\t\n\tvar isArray = __webpack_require__(10);\n\t\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof global.Blob === 'function' || toString.call(global.Blob) === '[object BlobConstructor]';\n\tvar withNativeFile = typeof global.File === 'function' || toString.call(global.File) === '[object FileConstructor]';\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = hasBinary;\n\t\n\t/**\n\t * Checks for binary data.\n\t *\n\t * Supports Buffer, ArrayBuffer, Blob and File.\n\t *\n\t * @param {Object} anything\n\t * @api public\n\t */\n\t\n\tfunction hasBinary (obj) {\n\t  if (!obj || typeof obj !== 'object') {\n\t    return false;\n\t  }\n\t\n\t  if (isArray(obj)) {\n\t    for (var i = 0, l = obj.length; i < l; i++) {\n\t      if (hasBinary(obj[i])) {\n\t        return true;\n\t      }\n\t    }\n\t    return false;\n\t  }\n\t\n\t  if ((typeof global.Buffer === 'function' && global.Buffer.isBuffer && global.Buffer.isBuffer(obj)) ||\n\t     (typeof global.ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n\t     (withNativeBlob && obj instanceof Blob) ||\n\t     (withNativeFile && obj instanceof File)\n\t    ) {\n\t    return true;\n\t  }\n\t\n\t  // see: https://github.com/Automattic/has-binary/pull/4\n\t  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n\t    return hasBinary(obj.toJSON(), true);\n\t  }\n\t\n\t  for (var key in obj) {\n\t    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n\t      return true;\n\t    }\n\t  }\n\t\n\t  return false;\n\t}\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * An abstraction for slicing an arraybuffer even when\n\t * ArrayBuffer.prototype.slice is not supported\n\t *\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(arraybuffer, start, end) {\n\t  var bytes = arraybuffer.byteLength;\n\t  start = start || 0;\n\t  end = end || bytes;\n\t\n\t  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\t\n\t  if (start < 0) { start += bytes; }\n\t  if (end < 0) { end += bytes; }\n\t  if (end > bytes) { end = bytes; }\n\t\n\t  if (start >= bytes || start >= end || bytes === 0) {\n\t    return new ArrayBuffer(0);\n\t  }\n\t\n\t  var abv = new Uint8Array(arraybuffer);\n\t  var result = new Uint8Array(end - start);\n\t  for (var i = start, ii = 0; i < end; i++, ii++) {\n\t    result[ii] = abv[i];\n\t  }\n\t  return result.buffer;\n\t};\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = after\n\t\n\tfunction after(count, callback, err_cb) {\n\t    var bail = false\n\t    err_cb = err_cb || noop\n\t    proxy.count = count\n\t\n\t    return (count === 0) ? callback() : proxy\n\t\n\t    function proxy(err, result) {\n\t        if (proxy.count <= 0) {\n\t            throw new Error('after called too many times')\n\t        }\n\t        --proxy.count\n\t\n\t        // after first error, rest are passed to err_cb\n\t        if (err) {\n\t            bail = true\n\t            callback(err)\n\t            // future error callbacks will go to error handler\n\t            callback = err_cb\n\t        } else if (proxy.count === 0 && !bail) {\n\t            callback(null, result)\n\t        }\n\t    }\n\t}\n\t\n\tfunction noop() {}\n\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;/* WEBPACK VAR INJECTION */(function(module, global) {/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\t;(function(root) {\n\t\n\t\t// Detect free variables `exports`\n\t\tvar freeExports = typeof exports == 'object' && exports;\n\t\n\t\t// Detect free variable `module`\n\t\tvar freeModule = typeof module == 'object' && module &&\n\t\t\tmodule.exports == freeExports && module;\n\t\n\t\t// Detect free variable `global`, from Node.js or Browserified code,\n\t\t// and use it as `root`\n\t\tvar freeGlobal = typeof global == 'object' && global;\n\t\tif (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {\n\t\t\troot = freeGlobal;\n\t\t}\n\t\n\t\t/*--------------------------------------------------------------------------*/\n\t\n\t\tvar stringFromCharCode = String.fromCharCode;\n\t\n\t\t// Taken from https://mths.be/punycode\n\t\tfunction ucs2decode(string) {\n\t\t\tvar output = [];\n\t\t\tvar counter = 0;\n\t\t\tvar length = string.length;\n\t\t\tvar value;\n\t\t\tvar extra;\n\t\t\twhile (counter < length) {\n\t\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\t\toutput.push(value);\n\t\t\t\t\t\tcounter--;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\toutput.push(value);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn output;\n\t\t}\n\t\n\t\t// Taken from https://mths.be/punycode\n\t\tfunction ucs2encode(array) {\n\t\t\tvar length = array.length;\n\t\t\tvar index = -1;\n\t\t\tvar value;\n\t\t\tvar output = '';\n\t\t\twhile (++index < length) {\n\t\t\t\tvalue = array[index];\n\t\t\t\tif (value > 0xFFFF) {\n\t\t\t\t\tvalue -= 0x10000;\n\t\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t\t}\n\t\t\t\toutput += stringFromCharCode(value);\n\t\t\t}\n\t\t\treturn output;\n\t\t}\n\t\n\t\tfunction checkScalarValue(codePoint, strict) {\n\t\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\t\tif (strict) {\n\t\t\t\t\tthrow Error(\n\t\t\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t\t\t' is not a scalar value'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t\t/*--------------------------------------------------------------------------*/\n\t\n\t\tfunction createByte(codePoint, shift) {\n\t\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t\t}\n\t\n\t\tfunction encodeCodePoint(codePoint, strict) {\n\t\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\t\treturn stringFromCharCode(codePoint);\n\t\t\t}\n\t\t\tvar symbol = '';\n\t\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t\t}\n\t\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\t\t\tcodePoint = 0xFFFD;\n\t\t\t\t}\n\t\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\t\tsymbol += createByte(codePoint, 6);\n\t\t\t}\n\t\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\t\tsymbol += createByte(codePoint, 6);\n\t\t\t}\n\t\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\t\treturn symbol;\n\t\t}\n\t\n\t\tfunction utf8encode(string, opts) {\n\t\t\topts = opts || {};\n\t\t\tvar strict = false !== opts.strict;\n\t\n\t\t\tvar codePoints = ucs2decode(string);\n\t\t\tvar length = codePoints.length;\n\t\t\tvar index = -1;\n\t\t\tvar codePoint;\n\t\t\tvar byteString = '';\n\t\t\twhile (++index < length) {\n\t\t\t\tcodePoint = codePoints[index];\n\t\t\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t\t\t}\n\t\t\treturn byteString;\n\t\t}\n\t\n\t\t/*--------------------------------------------------------------------------*/\n\t\n\t\tfunction readContinuationByte() {\n\t\t\tif (byteIndex >= byteCount) {\n\t\t\t\tthrow Error('Invalid byte index');\n\t\t\t}\n\t\n\t\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\t\tbyteIndex++;\n\t\n\t\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\t\treturn continuationByte & 0x3F;\n\t\t\t}\n\t\n\t\t\t// If we end up here, it’s not a continuation byte\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t\n\t\tfunction decodeSymbol(strict) {\n\t\t\tvar byte1;\n\t\t\tvar byte2;\n\t\t\tvar byte3;\n\t\t\tvar byte4;\n\t\t\tvar codePoint;\n\t\n\t\t\tif (byteIndex > byteCount) {\n\t\t\t\tthrow Error('Invalid byte index');\n\t\t\t}\n\t\n\t\t\tif (byteIndex == byteCount) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\n\t\t\t// Read first byte\n\t\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\t\tbyteIndex++;\n\t\n\t\t\t// 1-byte sequence (no continuation bytes)\n\t\t\tif ((byte1 & 0x80) == 0) {\n\t\t\t\treturn byte1;\n\t\t\t}\n\t\n\t\t\t// 2-byte sequence\n\t\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\t\tbyte2 = readContinuationByte();\n\t\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\t\tif (codePoint >= 0x80) {\n\t\t\t\t\treturn codePoint;\n\t\t\t\t} else {\n\t\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\t\tbyte2 = readContinuationByte();\n\t\t\t\tbyte3 = readContinuationByte();\n\t\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t\t\t} else {\n\t\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\t// 4-byte sequence\n\t\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\t\tbyte2 = readContinuationByte();\n\t\t\t\tbyte3 = readContinuationByte();\n\t\t\t\tbyte4 = readContinuationByte();\n\t\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\t\treturn codePoint;\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tthrow Error('Invalid UTF-8 detected');\n\t\t}\n\t\n\t\tvar byteArray;\n\t\tvar byteCount;\n\t\tvar byteIndex;\n\t\tfunction utf8decode(byteString, opts) {\n\t\t\topts = opts || {};\n\t\t\tvar strict = false !== opts.strict;\n\t\n\t\t\tbyteArray = ucs2decode(byteString);\n\t\t\tbyteCount = byteArray.length;\n\t\t\tbyteIndex = 0;\n\t\t\tvar codePoints = [];\n\t\t\tvar tmp;\n\t\t\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\t\t\tcodePoints.push(tmp);\n\t\t\t}\n\t\t\treturn ucs2encode(codePoints);\n\t\t}\n\t\n\t\t/*--------------------------------------------------------------------------*/\n\t\n\t\tvar utf8 = {\n\t\t\t'version': '2.1.2',\n\t\t\t'encode': utf8encode,\n\t\t\t'decode': utf8decode\n\t\t};\n\t\n\t\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t\t// like the following:\n\t\tif (\n\t\t\ttrue\n\t\t) {\n\t\t\t!(__WEBPACK_AMD_DEFINE_RESULT__ = function() {\n\t\t\t\treturn utf8;\n\t\t\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t\t}\telse if (freeExports && !freeExports.nodeType) {\n\t\t\tif (freeModule) { // in Node.js or RingoJS v0.8.0+\n\t\t\t\tfreeModule.exports = utf8;\n\t\t\t} else { // in Narwhal or RingoJS v0.7.0-\n\t\t\t\tvar object = {};\n\t\t\t\tvar hasOwnProperty = object.hasOwnProperty;\n\t\t\t\tfor (var key in utf8) {\n\t\t\t\t\thasOwnProperty.call(utf8, key) && (freeExports[key] = utf8[key]);\n\t\t\t\t}\n\t\t\t}\n\t\t} else { // in Rhino or a web browser\n\t\t\troot.utf8 = utf8;\n\t\t}\n\t\n\t}(this));\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(27)(module), (function() { return this; }())))\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = function(module) {\r\n\t\tif(!module.webpackPolyfill) {\r\n\t\t\tmodule.deprecate = function() {};\r\n\t\t\tmodule.paths = [];\r\n\t\t\t// module.parent = undefined by default\r\n\t\t\tmodule.children = [];\r\n\t\t\tmodule.webpackPolyfill = 1;\r\n\t\t}\r\n\t\treturn module;\r\n\t}\r\n\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports) {\n\n\t/*\n\t * base64-arraybuffer\n\t * https://github.com/niklasvh/base64-arraybuffer\n\t *\n\t * Copyright (c) 2012 Niklas von Hertzen\n\t * Licensed under the MIT license.\n\t */\n\t(function(){\n\t  \"use strict\";\n\t\n\t  var chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\t\n\t  // Use a lookup table to find the index.\n\t  var lookup = new Uint8Array(256);\n\t  for (var i = 0; i < chars.length; i++) {\n\t    lookup[chars.charCodeAt(i)] = i;\n\t  }\n\t\n\t  exports.encode = function(arraybuffer) {\n\t    var bytes = new Uint8Array(arraybuffer),\n\t    i, len = bytes.length, base64 = \"\";\n\t\n\t    for (i = 0; i < len; i+=3) {\n\t      base64 += chars[bytes[i] >> 2];\n\t      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n\t      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n\t      base64 += chars[bytes[i + 2] & 63];\n\t    }\n\t\n\t    if ((len % 3) === 2) {\n\t      base64 = base64.substring(0, base64.length - 1) + \"=\";\n\t    } else if (len % 3 === 1) {\n\t      base64 = base64.substring(0, base64.length - 2) + \"==\";\n\t    }\n\t\n\t    return base64;\n\t  };\n\t\n\t  exports.decode =  function(base64) {\n\t    var bufferLength = base64.length * 0.75,\n\t    len = base64.length, i, p = 0,\n\t    encoded1, encoded2, encoded3, encoded4;\n\t\n\t    if (base64[base64.length - 1] === \"=\") {\n\t      bufferLength--;\n\t      if (base64[base64.length - 2] === \"=\") {\n\t        bufferLength--;\n\t      }\n\t    }\n\t\n\t    var arraybuffer = new ArrayBuffer(bufferLength),\n\t    bytes = new Uint8Array(arraybuffer);\n\t\n\t    for (i = 0; i < len; i+=4) {\n\t      encoded1 = lookup[base64.charCodeAt(i)];\n\t      encoded2 = lookup[base64.charCodeAt(i+1)];\n\t      encoded3 = lookup[base64.charCodeAt(i+2)];\n\t      encoded4 = lookup[base64.charCodeAt(i+3)];\n\t\n\t      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n\t      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n\t      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n\t    }\n\t\n\t    return arraybuffer;\n\t  };\n\t})();\n\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Create a blob builder even when vendor prefixes exist\n\t */\n\t\n\tvar BlobBuilder = global.BlobBuilder\n\t  || global.WebKitBlobBuilder\n\t  || global.MSBlobBuilder\n\t  || global.MozBlobBuilder;\n\t\n\t/**\n\t * Check if Blob constructor is supported\n\t */\n\t\n\tvar blobSupported = (function() {\n\t  try {\n\t    var a = new Blob(['hi']);\n\t    return a.size === 2;\n\t  } catch(e) {\n\t    return false;\n\t  }\n\t})();\n\t\n\t/**\n\t * Check if Blob constructor supports ArrayBufferViews\n\t * Fails in Safari 6, so we need to map to ArrayBuffers there.\n\t */\n\t\n\tvar blobSupportsArrayBufferView = blobSupported && (function() {\n\t  try {\n\t    var b = new Blob([new Uint8Array([1,2])]);\n\t    return b.size === 2;\n\t  } catch(e) {\n\t    return false;\n\t  }\n\t})();\n\t\n\t/**\n\t * Check if BlobBuilder is supported\n\t */\n\t\n\tvar blobBuilderSupported = BlobBuilder\n\t  && BlobBuilder.prototype.append\n\t  && BlobBuilder.prototype.getBlob;\n\t\n\t/**\n\t * Helper function that maps ArrayBufferViews to ArrayBuffers\n\t * Used by BlobBuilder constructor and old browsers that didn't\n\t * support it in the Blob constructor.\n\t */\n\t\n\tfunction mapArrayBufferViews(ary) {\n\t  for (var i = 0; i < ary.length; i++) {\n\t    var chunk = ary[i];\n\t    if (chunk.buffer instanceof ArrayBuffer) {\n\t      var buf = chunk.buffer;\n\t\n\t      // if this is a subarray, make a copy so we only\n\t      // include the subarray region from the underlying buffer\n\t      if (chunk.byteLength !== buf.byteLength) {\n\t        var copy = new Uint8Array(chunk.byteLength);\n\t        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\n\t        buf = copy.buffer;\n\t      }\n\t\n\t      ary[i] = buf;\n\t    }\n\t  }\n\t}\n\t\n\tfunction BlobBuilderConstructor(ary, options) {\n\t  options = options || {};\n\t\n\t  var bb = new BlobBuilder();\n\t  mapArrayBufferViews(ary);\n\t\n\t  for (var i = 0; i < ary.length; i++) {\n\t    bb.append(ary[i]);\n\t  }\n\t\n\t  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\n\t};\n\t\n\tfunction BlobConstructor(ary, options) {\n\t  mapArrayBufferViews(ary);\n\t  return new Blob(ary, options || {});\n\t};\n\t\n\tmodule.exports = (function() {\n\t  if (blobSupported) {\n\t    return blobSupportsArrayBufferView ? global.Blob : BlobConstructor;\n\t  } else if (blobBuilderSupported) {\n\t    return BlobBuilderConstructor;\n\t  } else {\n\t    return undefined;\n\t  }\n\t})();\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Compiles a querystring\r\n\t * Returns string representation of the object\r\n\t *\r\n\t * @param {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\texports.encode = function (obj) {\r\n\t  var str = '';\r\n\t\r\n\t  for (var i in obj) {\r\n\t    if (obj.hasOwnProperty(i)) {\r\n\t      if (str.length) str += '&';\r\n\t      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return str;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Parses a simple querystring into an object\r\n\t *\r\n\t * @param {String} qs\r\n\t * @api private\r\n\t */\r\n\t\r\n\texports.decode = function(qs){\r\n\t  var qry = {};\r\n\t  var pairs = qs.split('&');\r\n\t  for (var i = 0, l = pairs.length; i < l; i++) {\r\n\t    var pair = pairs[i].split('=');\r\n\t    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\r\n\t  }\r\n\t  return qry;\r\n\t};\r\n\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = function(a, b){\n\t  var fn = function(){};\n\t  fn.prototype = b.prototype;\n\t  a.prototype = new fn;\n\t  a.prototype.constructor = a;\n\t};\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports) {\n\n\t'use strict';\n\t\n\tvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n\t  , length = 64\n\t  , map = {}\n\t  , seed = 0\n\t  , i = 0\n\t  , prev;\n\t\n\t/**\n\t * Return a string representing the specified number.\n\t *\n\t * @param {Number} num The number to convert.\n\t * @returns {String} The string representation of the number.\n\t * @api public\n\t */\n\tfunction encode(num) {\n\t  var encoded = '';\n\t\n\t  do {\n\t    encoded = alphabet[num % length] + encoded;\n\t    num = Math.floor(num / length);\n\t  } while (num > 0);\n\t\n\t  return encoded;\n\t}\n\t\n\t/**\n\t * Return the integer value specified by the given string.\n\t *\n\t * @param {String} str The string to convert.\n\t * @returns {Number} The integer value represented by the string.\n\t * @api public\n\t */\n\tfunction decode(str) {\n\t  var decoded = 0;\n\t\n\t  for (i = 0; i < str.length; i++) {\n\t    decoded = decoded * length + map[str.charAt(i)];\n\t  }\n\t\n\t  return decoded;\n\t}\n\t\n\t/**\n\t * Yeast: A tiny growing id generator.\n\t *\n\t * @returns {String} A unique id.\n\t * @api public\n\t */\n\tfunction yeast() {\n\t  var now = encode(+new Date());\n\t\n\t  if (now !== prev) return seed = 0, prev = now;\n\t  return now +'.'+ encode(seed++);\n\t}\n\t\n\t//\n\t// Map each character to its index.\n\t//\n\tfor (; i < length; i++) map[alphabet[i]] = i;\n\t\n\t//\n\t// Expose the `yeast`, `encode` and `decode` functions.\n\t//\n\tyeast.encode = encode;\n\tyeast.decode = decode;\n\tmodule.exports = yeast;\n\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar Polling = __webpack_require__(19);\n\tvar inherit = __webpack_require__(31);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = JSONPPolling;\n\t\n\t/**\n\t * Cached regular expressions.\n\t */\n\t\n\tvar rNewline = /\\n/g;\n\tvar rEscapedNewline = /\\\\n/g;\n\t\n\t/**\n\t * Global JSONP callbacks.\n\t */\n\t\n\tvar callbacks;\n\t\n\t/**\n\t * Noop.\n\t */\n\t\n\tfunction empty () { }\n\t\n\t/**\n\t * JSONP Polling constructor.\n\t *\n\t * @param {Object} opts.\n\t * @api public\n\t */\n\t\n\tfunction JSONPPolling (opts) {\n\t  Polling.call(this, opts);\n\t\n\t  this.query = this.query || {};\n\t\n\t  // define global callbacks array if not present\n\t  // we do this here (lazily) to avoid unneeded global pollution\n\t  if (!callbacks) {\n\t    // we need to consider multiple engines in the same page\n\t    if (!global.___eio) global.___eio = [];\n\t    callbacks = global.___eio;\n\t  }\n\t\n\t  // callback identifier\n\t  this.index = callbacks.length;\n\t\n\t  // add callback to jsonp global\n\t  var self = this;\n\t  callbacks.push(function (msg) {\n\t    self.onData(msg);\n\t  });\n\t\n\t  // append to query string\n\t  this.query.j = this.index;\n\t\n\t  // prevent spurious errors from being emitted when the window is unloaded\n\t  if (global.document && global.addEventListener) {\n\t    global.addEventListener('beforeunload', function () {\n\t      if (self.script) self.script.onerror = empty;\n\t    }, false);\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(JSONPPolling, Polling);\n\t\n\t/*\n\t * JSONP only supports binary as base64 encoded strings\n\t */\n\t\n\tJSONPPolling.prototype.supportsBinary = false;\n\t\n\t/**\n\t * Closes the socket.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doClose = function () {\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  if (this.form) {\n\t    this.form.parentNode.removeChild(this.form);\n\t    this.form = null;\n\t    this.iframe = null;\n\t  }\n\t\n\t  Polling.prototype.doClose.call(this);\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doPoll = function () {\n\t  var self = this;\n\t  var script = document.createElement('script');\n\t\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  script.async = true;\n\t  script.src = this.uri();\n\t  script.onerror = function (e) {\n\t    self.onError('jsonp poll error', e);\n\t  };\n\t\n\t  var insertAt = document.getElementsByTagName('script')[0];\n\t  if (insertAt) {\n\t    insertAt.parentNode.insertBefore(script, insertAt);\n\t  } else {\n\t    (document.head || document.body).appendChild(script);\n\t  }\n\t  this.script = script;\n\t\n\t  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\t\n\t  if (isUAgecko) {\n\t    setTimeout(function () {\n\t      var iframe = document.createElement('iframe');\n\t      document.body.appendChild(iframe);\n\t      document.body.removeChild(iframe);\n\t    }, 100);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes with a hidden iframe.\n\t *\n\t * @param {String} data to send\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doWrite = function (data, fn) {\n\t  var self = this;\n\t\n\t  if (!this.form) {\n\t    var form = document.createElement('form');\n\t    var area = document.createElement('textarea');\n\t    var id = this.iframeId = 'eio_iframe_' + this.index;\n\t    var iframe;\n\t\n\t    form.className = 'socketio';\n\t    form.style.position = 'absolute';\n\t    form.style.top = '-1000px';\n\t    form.style.left = '-1000px';\n\t    form.target = id;\n\t    form.method = 'POST';\n\t    form.setAttribute('accept-charset', 'utf-8');\n\t    area.name = 'd';\n\t    form.appendChild(area);\n\t    document.body.appendChild(form);\n\t\n\t    this.form = form;\n\t    this.area = area;\n\t  }\n\t\n\t  this.form.action = this.uri();\n\t\n\t  function complete () {\n\t    initIframe();\n\t    fn();\n\t  }\n\t\n\t  function initIframe () {\n\t    if (self.iframe) {\n\t      try {\n\t        self.form.removeChild(self.iframe);\n\t      } catch (e) {\n\t        self.onError('jsonp polling iframe removal error', e);\n\t      }\n\t    }\n\t\n\t    try {\n\t      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n\t      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n\t      iframe = document.createElement(html);\n\t    } catch (e) {\n\t      iframe = document.createElement('iframe');\n\t      iframe.name = self.iframeId;\n\t      iframe.src = 'javascript:0';\n\t    }\n\t\n\t    iframe.id = self.iframeId;\n\t\n\t    self.form.appendChild(iframe);\n\t    self.iframe = iframe;\n\t  }\n\t\n\t  initIframe();\n\t\n\t  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n\t  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n\t  data = data.replace(rEscapedNewline, '\\\\\\n');\n\t  this.area.value = data.replace(rNewline, '\\\\n');\n\t\n\t  try {\n\t    this.form.submit();\n\t  } catch (e) {}\n\t\n\t  if (this.iframe.attachEvent) {\n\t    this.iframe.onreadystatechange = function () {\n\t      if (self.iframe.readyState === 'complete') {\n\t        complete();\n\t      }\n\t    };\n\t  } else {\n\t    this.iframe.onload = complete;\n\t  }\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(20);\n\tvar parser = __webpack_require__(21);\n\tvar parseqs = __webpack_require__(30);\n\tvar inherit = __webpack_require__(31);\n\tvar yeast = __webpack_require__(32);\n\tvar debug = __webpack_require__(3)('engine.io-client:websocket');\n\tvar BrowserWebSocket = global.WebSocket || global.MozWebSocket;\n\tvar NodeWebSocket;\n\tif (typeof window === 'undefined') {\n\t  try {\n\t    NodeWebSocket = __webpack_require__(35);\n\t  } catch (e) { }\n\t}\n\t\n\t/**\n\t * Get either the `WebSocket` or `MozWebSocket` globals\n\t * in the browser or try to resolve WebSocket-compatible\n\t * interface exposed by `ws` for Node-like environment.\n\t */\n\t\n\tvar WebSocket = BrowserWebSocket;\n\tif (!WebSocket && typeof window === 'undefined') {\n\t  WebSocket = NodeWebSocket;\n\t}\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = WS;\n\t\n\t/**\n\t * WebSocket transport constructor.\n\t *\n\t * @api {Object} connection options\n\t * @api public\n\t */\n\t\n\tfunction WS (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  this.perMessageDeflate = opts.perMessageDeflate;\n\t  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n\t  this.protocols = opts.protocols;\n\t  if (!this.usingBrowserWebSocket) {\n\t    WebSocket = NodeWebSocket;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(WS, Transport);\n\t\n\t/**\n\t * Transport name.\n\t *\n\t * @api public\n\t */\n\t\n\tWS.prototype.name = 'websocket';\n\t\n\t/*\n\t * WebSockets support binary\n\t */\n\t\n\tWS.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Opens socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doOpen = function () {\n\t  if (!this.check()) {\n\t    // let probe timeout\n\t    return;\n\t  }\n\t\n\t  var uri = this.uri();\n\t  var protocols = this.protocols;\n\t  var opts = {\n\t    agent: this.agent,\n\t    perMessageDeflate: this.perMessageDeflate\n\t  };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  if (this.extraHeaders) {\n\t    opts.headers = this.extraHeaders;\n\t  }\n\t  if (this.localAddress) {\n\t    opts.localAddress = this.localAddress;\n\t  }\n\t\n\t  try {\n\t    this.ws = this.usingBrowserWebSocket ? (protocols ? new WebSocket(uri, protocols) : new WebSocket(uri)) : new WebSocket(uri, protocols, opts);\n\t  } catch (err) {\n\t    return this.emit('error', err);\n\t  }\n\t\n\t  if (this.ws.binaryType === undefined) {\n\t    this.supportsBinary = false;\n\t  }\n\t\n\t  if (this.ws.supports && this.ws.supports.binary) {\n\t    this.supportsBinary = true;\n\t    this.ws.binaryType = 'nodebuffer';\n\t  } else {\n\t    this.ws.binaryType = 'arraybuffer';\n\t  }\n\t\n\t  this.addEventListeners();\n\t};\n\t\n\t/**\n\t * Adds event listeners to the socket\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.addEventListeners = function () {\n\t  var self = this;\n\t\n\t  this.ws.onopen = function () {\n\t    self.onOpen();\n\t  };\n\t  this.ws.onclose = function () {\n\t    self.onClose();\n\t  };\n\t  this.ws.onmessage = function (ev) {\n\t    self.onData(ev.data);\n\t  };\n\t  this.ws.onerror = function (e) {\n\t    self.onError('websocket error', e);\n\t  };\n\t};\n\t\n\t/**\n\t * Writes data to socket.\n\t *\n\t * @param {Array} array of packets.\n\t * @api private\n\t */\n\t\n\tWS.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t\n\t  // encodePacket efficient as it uses WS framing\n\t  // no need for encodePayload\n\t  var total = packets.length;\n\t  for (var i = 0, l = total; i < l; i++) {\n\t    (function (packet) {\n\t      parser.encodePacket(packet, self.supportsBinary, function (data) {\n\t        if (!self.usingBrowserWebSocket) {\n\t          // always create a new object (GH-437)\n\t          var opts = {};\n\t          if (packet.options) {\n\t            opts.compress = packet.options.compress;\n\t          }\n\t\n\t          if (self.perMessageDeflate) {\n\t            var len = 'string' === typeof data ? global.Buffer.byteLength(data) : data.length;\n\t            if (len < self.perMessageDeflate.threshold) {\n\t              opts.compress = false;\n\t            }\n\t          }\n\t        }\n\t\n\t        // Sometimes the websocket has already been closed but the browser didn't\n\t        // have a chance of informing us about it yet, in that case send will\n\t        // throw an error\n\t        try {\n\t          if (self.usingBrowserWebSocket) {\n\t            // TypeError is thrown when passing the second argument on Safari\n\t            self.ws.send(data);\n\t          } else {\n\t            self.ws.send(data, opts);\n\t          }\n\t        } catch (e) {\n\t          debug('websocket closed before onclose event');\n\t        }\n\t\n\t        --total || done();\n\t      });\n\t    })(packets[i]);\n\t  }\n\t\n\t  function done () {\n\t    self.emit('flush');\n\t\n\t    // fake drain\n\t    // defer to next tick to allow Socket to clear writeBuffer\n\t    setTimeout(function () {\n\t      self.writable = true;\n\t      self.emit('drain');\n\t    }, 0);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon close\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.onClose = function () {\n\t  Transport.prototype.onClose.call(this);\n\t};\n\t\n\t/**\n\t * Closes socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doClose = function () {\n\t  if (typeof this.ws !== 'undefined') {\n\t    this.ws.close();\n\t  }\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'wss' : 'ws';\n\t  var port = '';\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n\t    ('ws' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // append timestamp to URI\n\t  if (this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  // communicate binary support capabilities\n\t  if (!this.supportsBinary) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\t\n\t/**\n\t * Feature detection for WebSocket.\n\t *\n\t * @return {Boolean} whether this transport is available.\n\t * @api public\n\t */\n\t\n\tWS.prototype.check = function () {\n\t  return !!WebSocket && !('__initialize' in WebSocket && this.name === WS.prototype.name);\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports) {\n\n\t/* (ignored) */\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports) {\n\n\t\n\tvar indexOf = [].indexOf;\n\t\n\tmodule.exports = function(arr, obj){\n\t  if (indexOf) return arr.indexOf(obj);\n\t  for (var i = 0; i < arr.length; ++i) {\n\t    if (arr[i] === obj) return i;\n\t  }\n\t  return -1;\n\t};\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(7);\n\tvar Emitter = __webpack_require__(8);\n\tvar toArray = __webpack_require__(38);\n\tvar on = __webpack_require__(39);\n\tvar bind = __webpack_require__(40);\n\tvar debug = __webpack_require__(3)('socket.io-client:socket');\n\tvar parseqs = __webpack_require__(30);\n\tvar hasBin = __webpack_require__(23);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = Socket;\n\t\n\t/**\n\t * Internal events (blacklisted).\n\t * These events can't be emitted by the user.\n\t *\n\t * @api private\n\t */\n\t\n\tvar events = {\n\t  connect: 1,\n\t  connect_error: 1,\n\t  connect_timeout: 1,\n\t  connecting: 1,\n\t  disconnect: 1,\n\t  error: 1,\n\t  reconnect: 1,\n\t  reconnect_attempt: 1,\n\t  reconnect_failed: 1,\n\t  reconnect_error: 1,\n\t  reconnecting: 1,\n\t  ping: 1,\n\t  pong: 1\n\t};\n\t\n\t/**\n\t * Shortcut to `Emitter#emit`.\n\t */\n\t\n\tvar emit = Emitter.prototype.emit;\n\t\n\t/**\n\t * `Socket` constructor.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Socket(io, nsp, opts) {\n\t  this.io = io;\n\t  this.nsp = nsp;\n\t  this.json = this; // compat\n\t  this.ids = 0;\n\t  this.acks = {};\n\t  this.receiveBuffer = [];\n\t  this.sendBuffer = [];\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  this.flags = {};\n\t  if (opts && opts.query) {\n\t    this.query = opts.query;\n\t  }\n\t  if (this.io.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Subscribe to open, close and packet events\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.subEvents = function () {\n\t  if (this.subs) return;\n\t\n\t  var io = this.io;\n\t  this.subs = [on(io, 'open', bind(this, 'onopen')), on(io, 'packet', bind(this, 'onpacket')), on(io, 'close', bind(this, 'onclose'))];\n\t};\n\t\n\t/**\n\t * \"Opens\" the socket.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.open = Socket.prototype.connect = function () {\n\t  if (this.connected) return this;\n\t\n\t  this.subEvents();\n\t  this.io.open(); // ensure open\n\t  if ('open' === this.io.readyState) this.onopen();\n\t  this.emit('connecting');\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a `message` event.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.send = function () {\n\t  var args = toArray(arguments);\n\t  args.unshift('message');\n\t  this.emit.apply(this, args);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Override `emit`.\n\t * If the event is in `events`, it's emitted normally.\n\t *\n\t * @param {String} event name\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.emit = function (ev) {\n\t  if (events.hasOwnProperty(ev)) {\n\t    emit.apply(this, arguments);\n\t    return this;\n\t  }\n\t\n\t  var args = toArray(arguments);\n\t  var packet = {\n\t    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n\t    data: args\n\t  };\n\t\n\t  packet.options = {};\n\t  packet.options.compress = !this.flags || false !== this.flags.compress;\n\t\n\t  // event ack callback\n\t  if ('function' === typeof args[args.length - 1]) {\n\t    debug('emitting packet with ack id %d', this.ids);\n\t    this.acks[this.ids] = args.pop();\n\t    packet.id = this.ids++;\n\t  }\n\t\n\t  if (this.connected) {\n\t    this.packet(packet);\n\t  } else {\n\t    this.sendBuffer.push(packet);\n\t  }\n\t\n\t  this.flags = {};\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.packet = function (packet) {\n\t  packet.nsp = this.nsp;\n\t  this.io.packet(packet);\n\t};\n\t\n\t/**\n\t * Called upon engine `open`.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onopen = function () {\n\t  debug('transport is open - connecting');\n\t\n\t  // write connect packet if necessary\n\t  if ('/' !== this.nsp) {\n\t    if (this.query) {\n\t      var query = _typeof(this.query) === 'object' ? parseqs.encode(this.query) : this.query;\n\t      debug('sending connect packet with query %s', query);\n\t      this.packet({ type: parser.CONNECT, query: query });\n\t    } else {\n\t      this.packet({ type: parser.CONNECT });\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon engine `close`.\n\t *\n\t * @param {String} reason\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onclose = function (reason) {\n\t  debug('close (%s)', reason);\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  delete this.id;\n\t  this.emit('disconnect', reason);\n\t};\n\t\n\t/**\n\t * Called with socket packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onpacket = function (packet) {\n\t  var sameNamespace = packet.nsp === this.nsp;\n\t  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\t\n\t  if (!sameNamespace && !rootNamespaceError) return;\n\t\n\t  switch (packet.type) {\n\t    case parser.CONNECT:\n\t      this.onconnect();\n\t      break;\n\t\n\t    case parser.EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.BINARY_EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.BINARY_ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.DISCONNECT:\n\t      this.ondisconnect();\n\t      break;\n\t\n\t    case parser.ERROR:\n\t      this.emit('error', packet.data);\n\t      break;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon a server event.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onevent = function (packet) {\n\t  var args = packet.data || [];\n\t  debug('emitting event %j', args);\n\t\n\t  if (null != packet.id) {\n\t    debug('attaching ack callback to event');\n\t    args.push(this.ack(packet.id));\n\t  }\n\t\n\t  if (this.connected) {\n\t    emit.apply(this, args);\n\t  } else {\n\t    this.receiveBuffer.push(args);\n\t  }\n\t};\n\t\n\t/**\n\t * Produces an ack callback to emit with an event.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ack = function (id) {\n\t  var self = this;\n\t  var sent = false;\n\t  return function () {\n\t    // prevent double callbacks\n\t    if (sent) return;\n\t    sent = true;\n\t    var args = toArray(arguments);\n\t    debug('sending ack %j', args);\n\t\n\t    self.packet({\n\t      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n\t      id: id,\n\t      data: args\n\t    });\n\t  };\n\t};\n\t\n\t/**\n\t * Called upon a server acknowlegement.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onack = function (packet) {\n\t  var ack = this.acks[packet.id];\n\t  if ('function' === typeof ack) {\n\t    debug('calling ack %s with %j', packet.id, packet.data);\n\t    ack.apply(this, packet.data);\n\t    delete this.acks[packet.id];\n\t  } else {\n\t    debug('bad ack %s', packet.id);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon server connect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onconnect = function () {\n\t  this.connected = true;\n\t  this.disconnected = false;\n\t  this.emit('connect');\n\t  this.emitBuffered();\n\t};\n\t\n\t/**\n\t * Emit buffered events (received and emitted).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.emitBuffered = function () {\n\t  var i;\n\t  for (i = 0; i < this.receiveBuffer.length; i++) {\n\t    emit.apply(this, this.receiveBuffer[i]);\n\t  }\n\t  this.receiveBuffer = [];\n\t\n\t  for (i = 0; i < this.sendBuffer.length; i++) {\n\t    this.packet(this.sendBuffer[i]);\n\t  }\n\t  this.sendBuffer = [];\n\t};\n\t\n\t/**\n\t * Called upon server disconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ondisconnect = function () {\n\t  debug('server disconnect (%s)', this.nsp);\n\t  this.destroy();\n\t  this.onclose('io server disconnect');\n\t};\n\t\n\t/**\n\t * Called upon forced client/server side disconnections,\n\t * this method ensures the manager stops tracking us and\n\t * that reconnections don't get triggered for this.\n\t *\n\t * @api private.\n\t */\n\t\n\tSocket.prototype.destroy = function () {\n\t  if (this.subs) {\n\t    // clean subscriptions to avoid reconnections\n\t    for (var i = 0; i < this.subs.length; i++) {\n\t      this.subs[i].destroy();\n\t    }\n\t    this.subs = null;\n\t  }\n\t\n\t  this.io.destroy(this);\n\t};\n\t\n\t/**\n\t * Disconnects the socket manually.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.close = Socket.prototype.disconnect = function () {\n\t  if (this.connected) {\n\t    debug('performing disconnect (%s)', this.nsp);\n\t    this.packet({ type: parser.DISCONNECT });\n\t  }\n\t\n\t  // remove socket from pool\n\t  this.destroy();\n\t\n\t  if (this.connected) {\n\t    // fire events\n\t    this.onclose('io client disconnect');\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the compress flag.\n\t *\n\t * @param {Boolean} if `true`, compresses the sending data\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.compress = function (compress) {\n\t  this.flags.compress = compress;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the binary flag\n\t *\n\t * @param {Boolean} whether the emitted data contains binary\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.binary = function (binary) {\n\t  this.flags.binary = binary;\n\t  return this;\n\t};\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = toArray\n\t\n\tfunction toArray(list, index) {\n\t    var array = []\n\t\n\t    index = index || 0\n\t\n\t    for (var i = index || 0; i < list.length; i++) {\n\t        array[i - index] = list[i]\n\t    }\n\t\n\t    return array\n\t}\n\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports) {\n\n\t\"use strict\";\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = on;\n\t\n\t/**\n\t * Helper for subscriptions.\n\t *\n\t * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n\t * @param {String} event name\n\t * @param {Function} callback\n\t * @api public\n\t */\n\t\n\tfunction on(obj, ev, fn) {\n\t  obj.on(ev, fn);\n\t  return {\n\t    destroy: function destroy() {\n\t      obj.removeListener(ev, fn);\n\t    }\n\t  };\n\t}\n\n/***/ }),\n/* 40 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Slice reference.\n\t */\n\t\n\tvar slice = [].slice;\n\t\n\t/**\n\t * Bind `obj` to `fn`.\n\t *\n\t * @param {Object} obj\n\t * @param {Function|String} fn or string\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(obj, fn){\n\t  if ('string' == typeof fn) fn = obj[fn];\n\t  if ('function' != typeof fn) throw new Error('bind() requires a function');\n\t  var args = slice.call(arguments, 2);\n\t  return function(){\n\t    return fn.apply(obj, args.concat(slice.call(arguments)));\n\t  }\n\t};\n\n\n/***/ }),\n/* 41 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Expose `Backoff`.\n\t */\n\t\n\tmodule.exports = Backoff;\n\t\n\t/**\n\t * Initialize backoff timer with `opts`.\n\t *\n\t * - `min` initial timeout in milliseconds [100]\n\t * - `max` max timeout [10000]\n\t * - `jitter` [0]\n\t * - `factor` [2]\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction Backoff(opts) {\n\t  opts = opts || {};\n\t  this.ms = opts.min || 100;\n\t  this.max = opts.max || 10000;\n\t  this.factor = opts.factor || 2;\n\t  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n\t  this.attempts = 0;\n\t}\n\t\n\t/**\n\t * Return the backoff duration.\n\t *\n\t * @return {Number}\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.duration = function(){\n\t  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n\t  if (this.jitter) {\n\t    var rand =  Math.random();\n\t    var deviation = Math.floor(rand * this.jitter * ms);\n\t    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n\t  }\n\t  return Math.min(ms, this.max) | 0;\n\t};\n\t\n\t/**\n\t * Reset the number of attempts.\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.reset = function(){\n\t  this.attempts = 0;\n\t};\n\t\n\t/**\n\t * Set the minimum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMin = function(min){\n\t  this.ms = min;\n\t};\n\t\n\t/**\n\t * Set the maximum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMax = function(max){\n\t  this.max = max;\n\t};\n\t\n\t/**\n\t * Set the jitter\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setJitter = function(jitter){\n\t  this.jitter = jitter;\n\t};\n\t\n\n\n/***/ })\n/******/ ])\n});\n;\n\n\n// WEBPACK FOOTER //\n// socket.io.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 3dcdaa84a447b9ebdc0b", "\n/**\n * Module dependencies.\n */\n\nvar url = require('./url');\nvar parser = require('socket.io-parser');\nvar Manager = require('./manager');\nvar debug = require('debug')('socket.io-client');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = lookup;\n\n/**\n * Managers cache.\n */\n\nvar cache = exports.managers = {};\n\n/**\n * Looks up an existing `Manager` for multiplexing.\n * If the user summons:\n *\n *   `io('http://localhost/a');`\n *   `io('http://localhost/b');`\n *\n * We reuse the existing instance based on same scheme/port/host,\n * and we initialize sockets for each namespace.\n *\n * @api public\n */\n\nfunction lookup (uri, opts) {\n  if (typeof uri === 'object') {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n\n  var parsed = url(uri);\n  var source = parsed.source;\n  var id = parsed.id;\n  var path = parsed.path;\n  var sameNamespace = cache[id] && path in cache[id].nsps;\n  var newConnection = opts.forceNew || opts['force new connection'] ||\n                      false === opts.multiplex || sameNamespace;\n\n  var io;\n\n  if (newConnection) {\n    debug('ignoring socket cache for %s', source);\n    io = Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      debug('new io instance for %s', source);\n      cache[id] = Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.query;\n  }\n  return io.socket(parsed.path, opts);\n}\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = parser.protocol;\n\n/**\n * `connect`.\n *\n * @param {String} uri\n * @api public\n */\n\nexports.connect = lookup;\n\n/**\n * Expose constructors for standalone build.\n *\n * @api public\n */\n\nexports.Manager = require('./manager');\nexports.Socket = require('./socket');\n\n\n\n// WEBPACK FOOTER //\n// ./lib/index.js", "\n/**\n * Module dependencies.\n */\n\nvar parseuri = require('parseuri');\nvar debug = require('debug')('socket.io-client:url');\n\n/**\n * Module exports.\n */\n\nmodule.exports = url;\n\n/**\n * URL parser.\n *\n * @param {String} url\n * @param {Object} An object meant to mimic window.location.\n *                 Defaults to window.location.\n * @api public\n */\n\nfunction url (uri, loc) {\n  var obj = uri;\n\n  // default to window.location\n  loc = loc || global.location;\n  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\n  // relative path support\n  if ('string' === typeof uri) {\n    if ('/' === uri.charAt(0)) {\n      if ('/' === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n      debug('protocol-less url %s', uri);\n      if ('undefined' !== typeof loc) {\n        uri = loc.protocol + '//' + uri;\n      } else {\n        uri = 'https://' + uri;\n      }\n    }\n\n    // parse\n    debug('parse %s', uri);\n    obj = parseuri(uri);\n  }\n\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = '80';\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = '443';\n    }\n  }\n\n  obj.path = obj.path || '/';\n\n  var ipv6 = obj.host.indexOf(':') !== -1;\n  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\n  // define unique id\n  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n  // define href\n  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\n  return obj;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/url.js", "/**\r\n * Parses an URI\r\n *\r\n * <AUTHOR> <stevenlevithan.com> (MIT license)\r\n * @api private\r\n */\r\n\r\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\r\n\r\nvar parts = [\r\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\r\n];\r\n\r\nmodule.exports = function parseuri(str) {\r\n    var src = str,\r\n        b = str.indexOf('['),\r\n        e = str.indexOf(']');\r\n\r\n    if (b != -1 && e != -1) {\r\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\r\n    }\r\n\r\n    var m = re.exec(str || ''),\r\n        uri = {},\r\n        i = 14;\r\n\r\n    while (i--) {\r\n        uri[parts[i]] = m[i] || '';\r\n    }\r\n\r\n    if (b != -1 && e != -1) {\r\n        uri.source = src;\r\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\r\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\r\n        uri.ipv6uri = true;\r\n    }\r\n\r\n    return uri;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseuri/index.js\n// module id = 2\n// module chunks = 0", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  '#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC',\n  '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF',\n  '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC',\n  '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF',\n  '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC',\n  '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033',\n  '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366',\n  '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933',\n  '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC',\n  '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF',\n  '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/debug/src/browser.js\n// module id = 3\n// module chunks = 0", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 4\n// module chunks = 0", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * Active `debug` instances.\n */\nexports.instances = [];\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  var prevTime;\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n  debug.destroy = destroy;\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  exports.instances.push(debug);\n\n  return debug;\n}\n\nfunction destroy () {\n  var index = exports.instances.indexOf(this);\n  if (index !== -1) {\n    exports.instances.splice(index, 1);\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var i;\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n\n  for (i = 0; i < exports.instances.length; i++) {\n    var instance = exports.instances[i];\n    instance.enabled = exports.enabled(instance.namespace);\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  if (name[name.length - 1] === '*') {\n    return true;\n  }\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/debug/src/debug.js\n// module id = 5\n// module chunks = 0", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/ms/index.js\n// module id = 6\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar debug = require('debug')('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar binary = require('./binary');\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = 4;\n\n/**\n * Packet types.\n *\n * @api public\n */\n\nexports.types = [\n  'CONNECT',\n  'DISCONNECT',\n  'EVENT',\n  'ACK',\n  'ERROR',\n  'BINARY_EVENT',\n  'BINARY_ACK'\n];\n\n/**\n * Packet type `connect`.\n *\n * @api public\n */\n\nexports.CONNECT = 0;\n\n/**\n * Packet type `disconnect`.\n *\n * @api public\n */\n\nexports.DISCONNECT = 1;\n\n/**\n * Packet type `event`.\n *\n * @api public\n */\n\nexports.EVENT = 2;\n\n/**\n * Packet type `ack`.\n *\n * @api public\n */\n\nexports.ACK = 3;\n\n/**\n * Packet type `error`.\n *\n * @api public\n */\n\nexports.ERROR = 4;\n\n/**\n * Packet type 'binary event'\n *\n * @api public\n */\n\nexports.BINARY_EVENT = 5;\n\n/**\n * Packet type `binary ack`. For acks with binary arguments.\n *\n * @api public\n */\n\nexports.BINARY_ACK = 6;\n\n/**\n * Encoder constructor.\n *\n * @api public\n */\n\nexports.Encoder = Encoder;\n\n/**\n * Decoder constructor.\n *\n * @api public\n */\n\nexports.Decoder = Decoder;\n\n/**\n * A socket.io Encoder instance\n *\n * @api public\n */\n\nfunction Encoder() {}\n\nvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\n/**\n * Encode a packet as a single string if non-binary, or as a\n * buffer sequence, depending on packet type.\n *\n * @param {Object} obj - packet object\n * @param {Function} callback - function to handle encodings (likely engine.write)\n * @return Calls callback with Array of encodings\n * @api public\n */\n\nEncoder.prototype.encode = function(obj, callback){\n  debug('encoding packet %j', obj);\n\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    encodeAsBinary(obj, callback);\n  } else {\n    var encoding = encodeAsString(obj);\n    callback([encoding]);\n  }\n};\n\n/**\n * Encode packet as string.\n *\n * @param {Object} packet\n * @return {String} encoded\n * @api private\n */\n\nfunction encodeAsString(obj) {\n\n  // first is type\n  var str = '' + obj.type;\n\n  // attachments if we have them\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    str += obj.attachments + '-';\n  }\n\n  // if we have a namespace other than `/`\n  // we append it followed by a comma `,`\n  if (obj.nsp && '/' !== obj.nsp) {\n    str += obj.nsp + ',';\n  }\n\n  // immediately followed by the id\n  if (null != obj.id) {\n    str += obj.id;\n  }\n\n  // json data\n  if (null != obj.data) {\n    var payload = tryStringify(obj.data);\n    if (payload !== false) {\n      str += payload;\n    } else {\n      return ERROR_PACKET;\n    }\n  }\n\n  debug('encoded %j as %s', obj, str);\n  return str;\n}\n\nfunction tryStringify(str) {\n  try {\n    return JSON.stringify(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Encode packet as 'buffer sequence' by removing blobs, and\n * deconstructing packet into object with placeholders and\n * a list of buffers.\n *\n * @param {Object} packet\n * @return {Buffer} encoded\n * @api private\n */\n\nfunction encodeAsBinary(obj, callback) {\n\n  function writeEncoding(bloblessData) {\n    var deconstruction = binary.deconstructPacket(bloblessData);\n    var pack = encodeAsString(deconstruction.packet);\n    var buffers = deconstruction.buffers;\n\n    buffers.unshift(pack); // add packet info to beginning of data list\n    callback(buffers); // write all the buffers\n  }\n\n  binary.removeBlobs(obj, writeEncoding);\n}\n\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n * @api public\n */\n\nfunction Decoder() {\n  this.reconstructor = null;\n}\n\n/**\n * Mix in `Emitter` with Decoder.\n */\n\nEmitter(Decoder.prototype);\n\n/**\n * Decodes an ecoded packet string into packet JSON.\n *\n * @param {String} obj - encoded packet\n * @return {Object} packet\n * @api public\n */\n\nDecoder.prototype.add = function(obj) {\n  var packet;\n  if (typeof obj === 'string') {\n    packet = decodeString(obj);\n    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n      this.reconstructor = new BinaryReconstructor(packet);\n\n      // no attachments, labeled binary but no binary data to follow\n      if (this.reconstructor.reconPack.attachments === 0) {\n        this.emit('decoded', packet);\n      }\n    } else { // non-binary full packet\n      this.emit('decoded', packet);\n    }\n  }\n  else if (isBuf(obj) || obj.base64) { // raw binary data\n    if (!this.reconstructor) {\n      throw new Error('got binary data when not reconstructing a packet');\n    } else {\n      packet = this.reconstructor.takeBinaryData(obj);\n      if (packet) { // received final buffer\n        this.reconstructor = null;\n        this.emit('decoded', packet);\n      }\n    }\n  }\n  else {\n    throw new Error('Unknown type: ' + obj);\n  }\n};\n\n/**\n * Decode a packet String (JSON data)\n *\n * @param {String} str\n * @return {Object} packet\n * @api private\n */\n\nfunction decodeString(str) {\n  var i = 0;\n  // look up type\n  var p = {\n    type: Number(str.charAt(0))\n  };\n\n  if (null == exports.types[p.type]) {\n    return error('unknown packet type ' + p.type);\n  }\n\n  // look up attachments if type binary\n  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n    var buf = '';\n    while (str.charAt(++i) !== '-') {\n      buf += str.charAt(i);\n      if (i == str.length) break;\n    }\n    if (buf != Number(buf) || str.charAt(i) !== '-') {\n      throw new Error('Illegal attachments');\n    }\n    p.attachments = Number(buf);\n  }\n\n  // look up namespace (if any)\n  if ('/' === str.charAt(i + 1)) {\n    p.nsp = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (',' === c) break;\n      p.nsp += c;\n      if (i === str.length) break;\n    }\n  } else {\n    p.nsp = '/';\n  }\n\n  // look up id\n  var next = str.charAt(i + 1);\n  if ('' !== next && Number(next) == next) {\n    p.id = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (null == c || Number(c) != c) {\n        --i;\n        break;\n      }\n      p.id += str.charAt(i);\n      if (i === str.length) break;\n    }\n    p.id = Number(p.id);\n  }\n\n  // look up json data\n  if (str.charAt(++i)) {\n    var payload = tryParse(str.substr(i));\n    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n    if (isPayloadValid) {\n      p.data = payload;\n    } else {\n      return error('invalid payload');\n    }\n  }\n\n  debug('decoded %s as %j', str, p);\n  return p;\n}\n\nfunction tryParse(str) {\n  try {\n    return JSON.parse(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Deallocates a parser's resources\n *\n * @api public\n */\n\nDecoder.prototype.destroy = function() {\n  if (this.reconstructor) {\n    this.reconstructor.finishedReconstruction();\n  }\n};\n\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n * @api private\n */\n\nfunction BinaryReconstructor(packet) {\n  this.reconPack = packet;\n  this.buffers = [];\n}\n\n/**\n * Method to be called when binary data received from connection\n * after a BINARY_EVENT packet.\n *\n * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n * @return {null | Object} returns null if more binary data is expected or\n *   a reconstructed packet object if all buffers have been received.\n * @api private\n */\n\nBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n  this.buffers.push(binData);\n  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n    this.finishedReconstruction();\n    return packet;\n  }\n  return null;\n};\n\n/**\n * Cleans up binary packet reconstruction variables.\n *\n * @api private\n */\n\nBinaryReconstructor.prototype.finishedReconstruction = function() {\n  this.reconPack = null;\n  this.buffers = [];\n};\n\nfunction error(msg) {\n  return {\n    type: exports.ERROR,\n    data: 'parser error: ' + msg\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/index.js\n// module id = 7\n// module chunks = 0", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  var args = [].slice.call(arguments, 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-emitter/index.js\n// module id = 8\n// module chunks = 0", "/*global Blob,File*/\n\n/**\n * Module requirements\n */\n\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof global.Blob === 'function' || toString.call(global.Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof global.File === 'function' || toString.call(global.File) === '[object FileConstructor]';\n\n/**\n * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n * Anything with blobs or files should be fed through removeBlobs before coming\n * here.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @api public\n */\n\nexports.deconstructPacket = function(packet) {\n  var buffers = [];\n  var packetData = packet.data;\n  var pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {packet: pack, buffers: buffers};\n};\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBuf(data)) {\n    var placeholder = { _placeholder: true, num: buffers.length };\n    buffers.push(data);\n    return placeholder;\n  } else if (isArray(data)) {\n    var newData = new Array(data.length);\n    for (var i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === 'object' && !(data instanceof Date)) {\n    var newData = {};\n    for (var key in data) {\n      newData[key] = _deconstructPacket(data[key], buffers);\n    }\n    return newData;\n  }\n  return data;\n}\n\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @api public\n */\n\nexports.reconstructPacket = function(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  packet.attachments = undefined; // no longer useful\n  return packet;\n};\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder) {\n    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n  } else if (isArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === 'object') {\n    for (var key in data) {\n      data[key] = _reconstructPacket(data[key], buffers);\n    }\n  }\n\n  return data;\n}\n\n/**\n * Asynchronously removes Blobs or Files from data via\n * FileReader's readAsArrayBuffer method. Used before encoding\n * data as msgpack. Calls callback with the blobless data.\n *\n * @param {Object} data\n * @param {Function} callback\n * @api private\n */\n\nexports.removeBlobs = function(data, callback) {\n  function _removeBlobs(obj, curKey, containingObject) {\n    if (!obj) return obj;\n\n    // convert any blob\n    if ((withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File)) {\n      pendingBlobs++;\n\n      // async filereader\n      var fileReader = new FileReader();\n      fileReader.onload = function() { // this.result == arraybuffer\n        if (containingObject) {\n          containingObject[curKey] = this.result;\n        }\n        else {\n          bloblessData = this.result;\n        }\n\n        // if nothing pending its callback time\n        if(! --pendingBlobs) {\n          callback(bloblessData);\n        }\n      };\n\n      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n    } else if (isArray(obj)) { // handle array\n      for (var i = 0; i < obj.length; i++) {\n        _removeBlobs(obj[i], i, obj);\n      }\n    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n      for (var key in obj) {\n        _removeBlobs(obj[key], key, obj);\n      }\n    }\n  }\n\n  var pendingBlobs = 0;\n  var bloblessData = data;\n  _removeBlobs(bloblessData);\n  if (!pendingBlobs) {\n    callback(bloblessData);\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/binary.js\n// module id = 9\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/isarray/index.js\n// module id = 10\n// module chunks = 0", "\nmodule.exports = isBuf;\n\nvar withNativeBuffer = typeof global.Buffer === 'function' && typeof global.Buffer.isBuffer === 'function';\nvar withNativeArrayBuffer = typeof global.ArrayBuffer === 'function';\n\nvar isView = (function () {\n  if (withNativeArrayBuffer && typeof global.ArrayBuffer.isView === 'function') {\n    return global.ArrayBuffer.isView;\n  } else {\n    return function (obj) { return obj.buffer instanceof global.ArrayBuffer; };\n  }\n})();\n\n/**\n * Returns true if obj is a buffer or an arraybuffer.\n *\n * @api private\n */\n\nfunction isBuf(obj) {\n  return (withNativeBuffer && global.Buffer.isBuffer(obj)) ||\n          (withNativeArrayBuffer && (obj instanceof global.ArrayBuffer || isView(obj)));\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/is-buffer.js\n// module id = 11\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar eio = require('engine.io-client');\nvar Socket = require('./socket');\nvar Emitter = require('component-emitter');\nvar parser = require('socket.io-parser');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:manager');\nvar indexOf = require('indexof');\nvar Backoff = require('backo2');\n\n/**\n * IE6+ hasOwnProperty\n */\n\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports\n */\n\nmodule.exports = Manager;\n\n/**\n * `Manager` constructor.\n *\n * @param {String} engine instance or engine uri/opts\n * @param {Object} options\n * @api public\n */\n\nfunction Manager (uri, opts) {\n  if (!(this instanceof Manager)) return new Manager(uri, opts);\n  if (uri && ('object' === typeof uri)) {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n\n  opts.path = opts.path || '/socket.io';\n  this.nsps = {};\n  this.subs = [];\n  this.opts = opts;\n  this.reconnection(opts.reconnection !== false);\n  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n  this.randomizationFactor(opts.randomizationFactor || 0.5);\n  this.backoff = new Backoff({\n    min: this.reconnectionDelay(),\n    max: this.reconnectionDelayMax(),\n    jitter: this.randomizationFactor()\n  });\n  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n  this.readyState = 'closed';\n  this.uri = uri;\n  this.connecting = [];\n  this.lastPing = null;\n  this.encoding = false;\n  this.packetBuffer = [];\n  var _parser = opts.parser || parser;\n  this.encoder = new _parser.Encoder();\n  this.decoder = new _parser.Decoder();\n  this.autoConnect = opts.autoConnect !== false;\n  if (this.autoConnect) this.open();\n}\n\n/**\n * Propagate given event to sockets and emit on `this`\n *\n * @api private\n */\n\nManager.prototype.emitAll = function () {\n  this.emit.apply(this, arguments);\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n    }\n  }\n};\n\n/**\n * Update `socket.id` of all sockets\n *\n * @api private\n */\n\nManager.prototype.updateSocketIds = function () {\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].id = this.generateId(nsp);\n    }\n  }\n};\n\n/**\n * generate `socket.id` for the given `nsp`\n *\n * @param {String} nsp\n * @return {String}\n * @api private\n */\n\nManager.prototype.generateId = function (nsp) {\n  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n};\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Manager.prototype);\n\n/**\n * Sets the `reconnection` config.\n *\n * @param {Boolean} true/false if it should automatically reconnect\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnection = function (v) {\n  if (!arguments.length) return this._reconnection;\n  this._reconnection = !!v;\n  return this;\n};\n\n/**\n * Sets the reconnection attempts config.\n *\n * @param {Number} max reconnection attempts before giving up\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionAttempts = function (v) {\n  if (!arguments.length) return this._reconnectionAttempts;\n  this._reconnectionAttempts = v;\n  return this;\n};\n\n/**\n * Sets the delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelay = function (v) {\n  if (!arguments.length) return this._reconnectionDelay;\n  this._reconnectionDelay = v;\n  this.backoff && this.backoff.setMin(v);\n  return this;\n};\n\nManager.prototype.randomizationFactor = function (v) {\n  if (!arguments.length) return this._randomizationFactor;\n  this._randomizationFactor = v;\n  this.backoff && this.backoff.setJitter(v);\n  return this;\n};\n\n/**\n * Sets the maximum delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelayMax = function (v) {\n  if (!arguments.length) return this._reconnectionDelayMax;\n  this._reconnectionDelayMax = v;\n  this.backoff && this.backoff.setMax(v);\n  return this;\n};\n\n/**\n * Sets the connection timeout. `false` to disable\n *\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.timeout = function (v) {\n  if (!arguments.length) return this._timeout;\n  this._timeout = v;\n  return this;\n};\n\n/**\n * Starts trying to reconnect if reconnection is enabled and we have not\n * started reconnecting yet\n *\n * @api private\n */\n\nManager.prototype.maybeReconnectOnOpen = function () {\n  // Only try to reconnect if it's the first time we're connecting\n  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n    // keeps reconnection from firing twice for the same reconnection loop\n    this.reconnect();\n  }\n};\n\n/**\n * Sets the current transport `socket`.\n *\n * @param {Function} optional, callback\n * @return {Manager} self\n * @api public\n */\n\nManager.prototype.open =\nManager.prototype.connect = function (fn, opts) {\n  debug('readyState %s', this.readyState);\n  if (~this.readyState.indexOf('open')) return this;\n\n  debug('opening %s', this.uri);\n  this.engine = eio(this.uri, this.opts);\n  var socket = this.engine;\n  var self = this;\n  this.readyState = 'opening';\n  this.skipReconnect = false;\n\n  // emit `open`\n  var openSub = on(socket, 'open', function () {\n    self.onopen();\n    fn && fn();\n  });\n\n  // emit `connect_error`\n  var errorSub = on(socket, 'error', function (data) {\n    debug('connect_error');\n    self.cleanup();\n    self.readyState = 'closed';\n    self.emitAll('connect_error', data);\n    if (fn) {\n      var err = new Error('Connection error');\n      err.data = data;\n      fn(err);\n    } else {\n      // Only do this if there is no fn to handle the error\n      self.maybeReconnectOnOpen();\n    }\n  });\n\n  // emit `connect_timeout`\n  if (false !== this._timeout) {\n    var timeout = this._timeout;\n    debug('connect attempt will timeout after %d', timeout);\n\n    // set timer\n    var timer = setTimeout(function () {\n      debug('connect attempt timed out after %d', timeout);\n      openSub.destroy();\n      socket.close();\n      socket.emit('error', 'timeout');\n      self.emitAll('connect_timeout', timeout);\n    }, timeout);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n\n  this.subs.push(openSub);\n  this.subs.push(errorSub);\n\n  return this;\n};\n\n/**\n * Called upon transport open.\n *\n * @api private\n */\n\nManager.prototype.onopen = function () {\n  debug('open');\n\n  // clear old subs\n  this.cleanup();\n\n  // mark as open\n  this.readyState = 'open';\n  this.emit('open');\n\n  // add new subs\n  var socket = this.engine;\n  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n};\n\n/**\n * Called upon a ping.\n *\n * @api private\n */\n\nManager.prototype.onping = function () {\n  this.lastPing = new Date();\n  this.emitAll('ping');\n};\n\n/**\n * Called upon a packet.\n *\n * @api private\n */\n\nManager.prototype.onpong = function () {\n  this.emitAll('pong', new Date() - this.lastPing);\n};\n\n/**\n * Called with data.\n *\n * @api private\n */\n\nManager.prototype.ondata = function (data) {\n  this.decoder.add(data);\n};\n\n/**\n * Called when parser fully decodes a packet.\n *\n * @api private\n */\n\nManager.prototype.ondecoded = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon socket error.\n *\n * @api private\n */\n\nManager.prototype.onerror = function (err) {\n  debug('error', err);\n  this.emitAll('error', err);\n};\n\n/**\n * Creates a new socket for the given `nsp`.\n *\n * @return {Socket}\n * @api public\n */\n\nManager.prototype.socket = function (nsp, opts) {\n  var socket = this.nsps[nsp];\n  if (!socket) {\n    socket = new Socket(this, nsp, opts);\n    this.nsps[nsp] = socket;\n    var self = this;\n    socket.on('connecting', onConnecting);\n    socket.on('connect', function () {\n      socket.id = self.generateId(nsp);\n    });\n\n    if (this.autoConnect) {\n      // manually call here since connecting event is fired before listening\n      onConnecting();\n    }\n  }\n\n  function onConnecting () {\n    if (!~indexOf(self.connecting, socket)) {\n      self.connecting.push(socket);\n    }\n  }\n\n  return socket;\n};\n\n/**\n * Called upon a socket close.\n *\n * @param {Socket} socket\n */\n\nManager.prototype.destroy = function (socket) {\n  var index = indexOf(this.connecting, socket);\n  if (~index) this.connecting.splice(index, 1);\n  if (this.connecting.length) return;\n\n  this.close();\n};\n\n/**\n * Writes a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nManager.prototype.packet = function (packet) {\n  debug('writing packet %j', packet);\n  var self = this;\n  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\n  if (!self.encoding) {\n    // encode, then write to engine with result\n    self.encoding = true;\n    this.encoder.encode(packet, function (encodedPackets) {\n      for (var i = 0; i < encodedPackets.length; i++) {\n        self.engine.write(encodedPackets[i], packet.options);\n      }\n      self.encoding = false;\n      self.processPacketQueue();\n    });\n  } else { // add packet to the queue\n    self.packetBuffer.push(packet);\n  }\n};\n\n/**\n * If packet buffer is non-empty, begins encoding the\n * next packet in line.\n *\n * @api private\n */\n\nManager.prototype.processPacketQueue = function () {\n  if (this.packetBuffer.length > 0 && !this.encoding) {\n    var pack = this.packetBuffer.shift();\n    this.packet(pack);\n  }\n};\n\n/**\n * Clean up transport subscriptions and packet buffer.\n *\n * @api private\n */\n\nManager.prototype.cleanup = function () {\n  debug('cleanup');\n\n  var subsLength = this.subs.length;\n  for (var i = 0; i < subsLength; i++) {\n    var sub = this.subs.shift();\n    sub.destroy();\n  }\n\n  this.packetBuffer = [];\n  this.encoding = false;\n  this.lastPing = null;\n\n  this.decoder.destroy();\n};\n\n/**\n * Close the current socket.\n *\n * @api private\n */\n\nManager.prototype.close =\nManager.prototype.disconnect = function () {\n  debug('disconnect');\n  this.skipReconnect = true;\n  this.reconnecting = false;\n  if ('opening' === this.readyState) {\n    // `onclose` will not fire because\n    // an open event never happened\n    this.cleanup();\n  }\n  this.backoff.reset();\n  this.readyState = 'closed';\n  if (this.engine) this.engine.close();\n};\n\n/**\n * Called upon engine close.\n *\n * @api private\n */\n\nManager.prototype.onclose = function (reason) {\n  debug('onclose');\n\n  this.cleanup();\n  this.backoff.reset();\n  this.readyState = 'closed';\n  this.emit('close', reason);\n\n  if (this._reconnection && !this.skipReconnect) {\n    this.reconnect();\n  }\n};\n\n/**\n * Attempt a reconnection.\n *\n * @api private\n */\n\nManager.prototype.reconnect = function () {\n  if (this.reconnecting || this.skipReconnect) return this;\n\n  var self = this;\n\n  if (this.backoff.attempts >= this._reconnectionAttempts) {\n    debug('reconnect failed');\n    this.backoff.reset();\n    this.emitAll('reconnect_failed');\n    this.reconnecting = false;\n  } else {\n    var delay = this.backoff.duration();\n    debug('will wait %dms before reconnect attempt', delay);\n\n    this.reconnecting = true;\n    var timer = setTimeout(function () {\n      if (self.skipReconnect) return;\n\n      debug('attempting reconnect');\n      self.emitAll('reconnect_attempt', self.backoff.attempts);\n      self.emitAll('reconnecting', self.backoff.attempts);\n\n      // check again for the case socket closed in above events\n      if (self.skipReconnect) return;\n\n      self.open(function (err) {\n        if (err) {\n          debug('reconnect attempt error');\n          self.reconnecting = false;\n          self.reconnect();\n          self.emitAll('reconnect_error', err.data);\n        } else {\n          debug('reconnect success');\n          self.onreconnect();\n        }\n      });\n    }, delay);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n};\n\n/**\n * Called upon successful reconnect.\n *\n * @api private\n */\n\nManager.prototype.onreconnect = function () {\n  var attempt = this.backoff.attempts;\n  this.reconnecting = false;\n  this.backoff.reset();\n  this.updateSocketIds();\n  this.emitAll('reconnect', attempt);\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/manager.js", "\nmodule.exports = require('./socket');\n\n/**\n * Exports parser\n *\n * @api public\n *\n */\nmodule.exports.parser = require('engine.io-parser');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/index.js\n// module id = 13\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar transports = require('./transports/index');\nvar Emitter = require('component-emitter');\nvar debug = require('debug')('engine.io-client:socket');\nvar index = require('indexof');\nvar parser = require('engine.io-parser');\nvar parseuri = require('parseuri');\nvar parseqs = require('parseqs');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Socket;\n\n/**\n * Socket constructor.\n *\n * @param {String|Object} uri or options\n * @param {Object} options\n * @api public\n */\n\nfunction Socket (uri, opts) {\n  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\n  opts = opts || {};\n\n  if (uri && 'object' === typeof uri) {\n    opts = uri;\n    uri = null;\n  }\n\n  if (uri) {\n    uri = parseuri(uri);\n    opts.hostname = uri.host;\n    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n    opts.port = uri.port;\n    if (uri.query) opts.query = uri.query;\n  } else if (opts.host) {\n    opts.hostname = parseuri(opts.host).host;\n  }\n\n  this.secure = null != opts.secure ? opts.secure\n    : (global.location && 'https:' === location.protocol);\n\n  if (opts.hostname && !opts.port) {\n    // if no port is specified manually, use the protocol default\n    opts.port = this.secure ? '443' : '80';\n  }\n\n  this.agent = opts.agent || false;\n  this.hostname = opts.hostname ||\n    (global.location ? location.hostname : 'localhost');\n  this.port = opts.port || (global.location && location.port\n      ? location.port\n      : (this.secure ? 443 : 80));\n  this.query = opts.query || {};\n  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n  this.upgrade = false !== opts.upgrade;\n  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n  this.forceJSONP = !!opts.forceJSONP;\n  this.jsonp = false !== opts.jsonp;\n  this.forceBase64 = !!opts.forceBase64;\n  this.enablesXDR = !!opts.enablesXDR;\n  this.timestampParam = opts.timestampParam || 't';\n  this.timestampRequests = opts.timestampRequests;\n  this.transports = opts.transports || ['polling', 'websocket'];\n  this.transportOptions = opts.transportOptions || {};\n  this.readyState = '';\n  this.writeBuffer = [];\n  this.prevBufferLen = 0;\n  this.policyPort = opts.policyPort || 843;\n  this.rememberUpgrade = opts.rememberUpgrade || false;\n  this.binaryType = null;\n  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\n  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n    this.perMessageDeflate.threshold = 1024;\n  }\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx || null;\n  this.key = opts.key || null;\n  this.passphrase = opts.passphrase || null;\n  this.cert = opts.cert || null;\n  this.ca = opts.ca || null;\n  this.ciphers = opts.ciphers || null;\n  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n  this.forceNode = !!opts.forceNode;\n\n  // other options for Node.js client\n  var freeGlobal = typeof global === 'object' && global;\n  if (freeGlobal.global === freeGlobal) {\n    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n      this.extraHeaders = opts.extraHeaders;\n    }\n\n    if (opts.localAddress) {\n      this.localAddress = opts.localAddress;\n    }\n  }\n\n  // set on handshake\n  this.id = null;\n  this.upgrades = null;\n  this.pingInterval = null;\n  this.pingTimeout = null;\n\n  // set on heartbeat\n  this.pingIntervalTimer = null;\n  this.pingTimeoutTimer = null;\n\n  this.open();\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nSocket.Socket = Socket;\nSocket.Transport = require('./transport');\nSocket.transports = require('./transports/index');\nSocket.parser = require('engine.io-parser');\n\n/**\n * Creates transport of the given type.\n *\n * @param {String} transport name\n * @return {Transport}\n * @api private\n */\n\nSocket.prototype.createTransport = function (name) {\n  debug('creating transport \"%s\"', name);\n  var query = clone(this.query);\n\n  // append engine.io protocol identifier\n  query.EIO = parser.protocol;\n\n  // transport name\n  query.transport = name;\n\n  // per-transport options\n  var options = this.transportOptions[name] || {};\n\n  // session id if we already have one\n  if (this.id) query.sid = this.id;\n\n  var transport = new transports[name]({\n    query: query,\n    socket: this,\n    agent: options.agent || this.agent,\n    hostname: options.hostname || this.hostname,\n    port: options.port || this.port,\n    secure: options.secure || this.secure,\n    path: options.path || this.path,\n    forceJSONP: options.forceJSONP || this.forceJSONP,\n    jsonp: options.jsonp || this.jsonp,\n    forceBase64: options.forceBase64 || this.forceBase64,\n    enablesXDR: options.enablesXDR || this.enablesXDR,\n    timestampRequests: options.timestampRequests || this.timestampRequests,\n    timestampParam: options.timestampParam || this.timestampParam,\n    policyPort: options.policyPort || this.policyPort,\n    pfx: options.pfx || this.pfx,\n    key: options.key || this.key,\n    passphrase: options.passphrase || this.passphrase,\n    cert: options.cert || this.cert,\n    ca: options.ca || this.ca,\n    ciphers: options.ciphers || this.ciphers,\n    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n    extraHeaders: options.extraHeaders || this.extraHeaders,\n    forceNode: options.forceNode || this.forceNode,\n    localAddress: options.localAddress || this.localAddress,\n    requestTimeout: options.requestTimeout || this.requestTimeout,\n    protocols: options.protocols || void (0)\n  });\n\n  return transport;\n};\n\nfunction clone (obj) {\n  var o = {};\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\n/**\n * Initializes transport to use and starts probe.\n *\n * @api private\n */\nSocket.prototype.open = function () {\n  var transport;\n  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n    transport = 'websocket';\n  } else if (0 === this.transports.length) {\n    // Emit error on next tick so it can be listened to\n    var self = this;\n    setTimeout(function () {\n      self.emit('error', 'No transports available');\n    }, 0);\n    return;\n  } else {\n    transport = this.transports[0];\n  }\n  this.readyState = 'opening';\n\n  // Retry with the next transport if the transport is disabled (jsonp: false)\n  try {\n    transport = this.createTransport(transport);\n  } catch (e) {\n    this.transports.shift();\n    this.open();\n    return;\n  }\n\n  transport.open();\n  this.setTransport(transport);\n};\n\n/**\n * Sets the current transport. Disables the existing one (if any).\n *\n * @api private\n */\n\nSocket.prototype.setTransport = function (transport) {\n  debug('setting transport %s', transport.name);\n  var self = this;\n\n  if (this.transport) {\n    debug('clearing existing transport %s', this.transport.name);\n    this.transport.removeAllListeners();\n  }\n\n  // set up transport\n  this.transport = transport;\n\n  // set up transport listeners\n  transport\n  .on('drain', function () {\n    self.onDrain();\n  })\n  .on('packet', function (packet) {\n    self.onPacket(packet);\n  })\n  .on('error', function (e) {\n    self.onError(e);\n  })\n  .on('close', function () {\n    self.onClose('transport close');\n  });\n};\n\n/**\n * Probes a transport.\n *\n * @param {String} transport name\n * @api private\n */\n\nSocket.prototype.probe = function (name) {\n  debug('probing transport \"%s\"', name);\n  var transport = this.createTransport(name, { probe: 1 });\n  var failed = false;\n  var self = this;\n\n  Socket.priorWebsocketSuccess = false;\n\n  function onTransportOpen () {\n    if (self.onlyBinaryUpgrades) {\n      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n      failed = failed || upgradeLosesBinary;\n    }\n    if (failed) return;\n\n    debug('probe transport \"%s\" opened', name);\n    transport.send([{ type: 'ping', data: 'probe' }]);\n    transport.once('packet', function (msg) {\n      if (failed) return;\n      if ('pong' === msg.type && 'probe' === msg.data) {\n        debug('probe transport \"%s\" pong', name);\n        self.upgrading = true;\n        self.emit('upgrading', transport);\n        if (!transport) return;\n        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\n        debug('pausing current transport \"%s\"', self.transport.name);\n        self.transport.pause(function () {\n          if (failed) return;\n          if ('closed' === self.readyState) return;\n          debug('changing transport and sending upgrade packet');\n\n          cleanup();\n\n          self.setTransport(transport);\n          transport.send([{ type: 'upgrade' }]);\n          self.emit('upgrade', transport);\n          transport = null;\n          self.upgrading = false;\n          self.flush();\n        });\n      } else {\n        debug('probe transport \"%s\" failed', name);\n        var err = new Error('probe error');\n        err.transport = transport.name;\n        self.emit('upgradeError', err);\n      }\n    });\n  }\n\n  function freezeTransport () {\n    if (failed) return;\n\n    // Any callback called by transport should be ignored since now\n    failed = true;\n\n    cleanup();\n\n    transport.close();\n    transport = null;\n  }\n\n  // Handle any error that happens while probing\n  function onerror (err) {\n    var error = new Error('probe error: ' + err);\n    error.transport = transport.name;\n\n    freezeTransport();\n\n    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\n    self.emit('upgradeError', error);\n  }\n\n  function onTransportClose () {\n    onerror('transport closed');\n  }\n\n  // When the socket is closed while we're probing\n  function onclose () {\n    onerror('socket closed');\n  }\n\n  // When the socket is upgraded while we're probing\n  function onupgrade (to) {\n    if (transport && to.name !== transport.name) {\n      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n      freezeTransport();\n    }\n  }\n\n  // Remove all listeners on the transport and on self\n  function cleanup () {\n    transport.removeListener('open', onTransportOpen);\n    transport.removeListener('error', onerror);\n    transport.removeListener('close', onTransportClose);\n    self.removeListener('close', onclose);\n    self.removeListener('upgrading', onupgrade);\n  }\n\n  transport.once('open', onTransportOpen);\n  transport.once('error', onerror);\n  transport.once('close', onTransportClose);\n\n  this.once('close', onclose);\n  this.once('upgrading', onupgrade);\n\n  transport.open();\n};\n\n/**\n * Called when connection is deemed open.\n *\n * @api public\n */\n\nSocket.prototype.onOpen = function () {\n  debug('socket open');\n  this.readyState = 'open';\n  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n  this.emit('open');\n  this.flush();\n\n  // we check for `readyState` in case an `open`\n  // listener already closed the socket\n  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n    debug('starting upgrade probes');\n    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n      this.probe(this.upgrades[i]);\n    }\n  }\n};\n\n/**\n * Handles a packet.\n *\n * @api private\n */\n\nSocket.prototype.onPacket = function (packet) {\n  if ('opening' === this.readyState || 'open' === this.readyState ||\n      'closing' === this.readyState) {\n    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\n    this.emit('packet', packet);\n\n    // Socket is live - any packet counts\n    this.emit('heartbeat');\n\n    switch (packet.type) {\n      case 'open':\n        this.onHandshake(JSON.parse(packet.data));\n        break;\n\n      case 'pong':\n        this.setPing();\n        this.emit('pong');\n        break;\n\n      case 'error':\n        var err = new Error('server error');\n        err.code = packet.data;\n        this.onError(err);\n        break;\n\n      case 'message':\n        this.emit('data', packet.data);\n        this.emit('message', packet.data);\n        break;\n    }\n  } else {\n    debug('packet received with socket readyState \"%s\"', this.readyState);\n  }\n};\n\n/**\n * Called upon handshake completion.\n *\n * @param {Object} handshake obj\n * @api private\n */\n\nSocket.prototype.onHandshake = function (data) {\n  this.emit('handshake', data);\n  this.id = data.sid;\n  this.transport.query.sid = data.sid;\n  this.upgrades = this.filterUpgrades(data.upgrades);\n  this.pingInterval = data.pingInterval;\n  this.pingTimeout = data.pingTimeout;\n  this.onOpen();\n  // In case open handler closes socket\n  if ('closed' === this.readyState) return;\n  this.setPing();\n\n  // Prolong liveness of socket on heartbeat\n  this.removeListener('heartbeat', this.onHeartbeat);\n  this.on('heartbeat', this.onHeartbeat);\n};\n\n/**\n * Resets ping timeout.\n *\n * @api private\n */\n\nSocket.prototype.onHeartbeat = function (timeout) {\n  clearTimeout(this.pingTimeoutTimer);\n  var self = this;\n  self.pingTimeoutTimer = setTimeout(function () {\n    if ('closed' === self.readyState) return;\n    self.onClose('ping timeout');\n  }, timeout || (self.pingInterval + self.pingTimeout));\n};\n\n/**\n * Pings server every `this.pingInterval` and expects response\n * within `this.pingTimeout` or closes connection.\n *\n * @api private\n */\n\nSocket.prototype.setPing = function () {\n  var self = this;\n  clearTimeout(self.pingIntervalTimer);\n  self.pingIntervalTimer = setTimeout(function () {\n    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n    self.ping();\n    self.onHeartbeat(self.pingTimeout);\n  }, self.pingInterval);\n};\n\n/**\n* Sends a ping packet.\n*\n* @api private\n*/\n\nSocket.prototype.ping = function () {\n  var self = this;\n  this.sendPacket('ping', function () {\n    self.emit('ping');\n  });\n};\n\n/**\n * Called on `drain` event\n *\n * @api private\n */\n\nSocket.prototype.onDrain = function () {\n  this.writeBuffer.splice(0, this.prevBufferLen);\n\n  // setting prevBufferLen = 0 is very important\n  // for example, when upgrading, upgrade packet is sent over,\n  // and a nonzero prevBufferLen could cause problems on `drain`\n  this.prevBufferLen = 0;\n\n  if (0 === this.writeBuffer.length) {\n    this.emit('drain');\n  } else {\n    this.flush();\n  }\n};\n\n/**\n * Flush write buffers.\n *\n * @api private\n */\n\nSocket.prototype.flush = function () {\n  if ('closed' !== this.readyState && this.transport.writable &&\n    !this.upgrading && this.writeBuffer.length) {\n    debug('flushing %d packets in socket', this.writeBuffer.length);\n    this.transport.send(this.writeBuffer);\n    // keep track of current length of writeBuffer\n    // splice writeBuffer and callbackBuffer on `drain`\n    this.prevBufferLen = this.writeBuffer.length;\n    this.emit('flush');\n  }\n};\n\n/**\n * Sends a message.\n *\n * @param {String} message.\n * @param {Function} callback function.\n * @param {Object} options.\n * @return {Socket} for chaining.\n * @api public\n */\n\nSocket.prototype.write =\nSocket.prototype.send = function (msg, options, fn) {\n  this.sendPacket('message', msg, options, fn);\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {String} packet type.\n * @param {String} data.\n * @param {Object} options.\n * @param {Function} callback function.\n * @api private\n */\n\nSocket.prototype.sendPacket = function (type, data, options, fn) {\n  if ('function' === typeof data) {\n    fn = data;\n    data = undefined;\n  }\n\n  if ('function' === typeof options) {\n    fn = options;\n    options = null;\n  }\n\n  if ('closing' === this.readyState || 'closed' === this.readyState) {\n    return;\n  }\n\n  options = options || {};\n  options.compress = false !== options.compress;\n\n  var packet = {\n    type: type,\n    data: data,\n    options: options\n  };\n  this.emit('packetCreate', packet);\n  this.writeBuffer.push(packet);\n  if (fn) this.once('flush', fn);\n  this.flush();\n};\n\n/**\n * Closes the connection.\n *\n * @api private\n */\n\nSocket.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.readyState = 'closing';\n\n    var self = this;\n\n    if (this.writeBuffer.length) {\n      this.once('drain', function () {\n        if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      });\n    } else if (this.upgrading) {\n      waitForUpgrade();\n    } else {\n      close();\n    }\n  }\n\n  function close () {\n    self.onClose('forced close');\n    debug('socket closing - telling transport to close');\n    self.transport.close();\n  }\n\n  function cleanupAndClose () {\n    self.removeListener('upgrade', cleanupAndClose);\n    self.removeListener('upgradeError', cleanupAndClose);\n    close();\n  }\n\n  function waitForUpgrade () {\n    // wait for upgrade to finish since we can't send packets while pausing a transport\n    self.once('upgrade', cleanupAndClose);\n    self.once('upgradeError', cleanupAndClose);\n  }\n\n  return this;\n};\n\n/**\n * Called upon transport error\n *\n * @api private\n */\n\nSocket.prototype.onError = function (err) {\n  debug('socket error %j', err);\n  Socket.priorWebsocketSuccess = false;\n  this.emit('error', err);\n  this.onClose('transport error', err);\n};\n\n/**\n * Called upon transport close.\n *\n * @api private\n */\n\nSocket.prototype.onClose = function (reason, desc) {\n  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n    debug('socket close with reason: \"%s\"', reason);\n    var self = this;\n\n    // clear timers\n    clearTimeout(this.pingIntervalTimer);\n    clearTimeout(this.pingTimeoutTimer);\n\n    // stop event from firing again for transport\n    this.transport.removeAllListeners('close');\n\n    // ensure transport won't stay open\n    this.transport.close();\n\n    // ignore further transport communication\n    this.transport.removeAllListeners();\n\n    // set ready state\n    this.readyState = 'closed';\n\n    // clear session id\n    this.id = null;\n\n    // emit close event\n    this.emit('close', reason, desc);\n\n    // clean buffers after, so users can still\n    // grab the buffers on `close` event\n    self.writeBuffer = [];\n    self.prevBufferLen = 0;\n  }\n};\n\n/**\n * Filters upgrades, returning only those matching client transports.\n *\n * @param {Array} server upgrades\n * @api private\n *\n */\n\nSocket.prototype.filterUpgrades = function (upgrades) {\n  var filteredUpgrades = [];\n  for (var i = 0, j = upgrades.length; i < j; i++) {\n    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n  }\n  return filteredUpgrades;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/socket.js\n// module id = 14\n// module chunks = 0", "/**\n * Module dependencies\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar XHR = require('./polling-xhr');\nvar JSONP = require('./polling-jsonp');\nvar websocket = require('./websocket');\n\n/**\n * Export transports.\n */\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling (opts) {\n  var xhr;\n  var xd = false;\n  var xs = false;\n  var jsonp = false !== opts.jsonp;\n\n  if (global.location) {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if ('open' in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error('JSO<PERSON> disabled');\n    return new JSONP(opts);\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/index.js\n// module id = 15\n// module chunks = 0", "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new global[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/xmlhttprequest.js\n// module id = 16\n// module chunks = 0", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-cors/index.js\n// module id = 17\n// module chunks = 0", "/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (global.location) {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = opts.hostname !== global.location.hostname ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n  debug('xhr poll');\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n    debug('xhr open %s: %s', this.method, this.uri);\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = true;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(xhr.status);\n          }, 0);\n        }\n      };\n    }\n\n    debug('xhr data %s', this.data);\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (global.document) {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (global.document) {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return 'undefined' !== typeof global.XDomainRequest && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (global.document) {\n  if (global.attachEvent) {\n    global.attachEvent('onunload', unloadHandler);\n  } else if (global.addEventListener) {\n    global.addEventListener('beforeunload', unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-xhr.js\n// module id = 18\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('xmlhttprequest-ssl');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n    debug('paused');\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n      debug('we are currently polling - waiting to pause');\n      total++;\n      this.once('pollComplete', function () {\n        debug('pre-pause polling complete');\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n      debug('we are currently writing - waiting to pause');\n      total++;\n      this.once('drain', function () {\n        debug('pre-pause writing complete');\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n  debug('polling');\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n  debug('polling got data %s', data);\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState) {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n      debug('ignoring poll - transport state \"%s\"', this.readyState);\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n    debug('writing close packet');\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n    debug('transport open - closing');\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n    debug('transport not open - deferring close');\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling.js\n// module id = 19\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar parser = require('engine.io-parser');\nvar Emitter = require('component-emitter');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Transport;\n\n/**\n * Transport abstract constructor.\n *\n * @param {Object} options.\n * @api private\n */\n\nfunction Transport (opts) {\n  this.path = opts.path;\n  this.hostname = opts.hostname;\n  this.port = opts.port;\n  this.secure = opts.secure;\n  this.query = opts.query;\n  this.timestampParam = opts.timestampParam;\n  this.timestampRequests = opts.timestampRequests;\n  this.readyState = '';\n  this.agent = opts.agent || false;\n  this.socket = opts.socket;\n  this.enablesXDR = opts.enablesXDR;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n  this.forceNode = opts.forceNode;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.localAddress = opts.localAddress;\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Transport.prototype);\n\n/**\n * Emits an error.\n *\n * @param {String} str\n * @return {Transport} for chaining\n * @api public\n */\n\nTransport.prototype.onError = function (msg, desc) {\n  var err = new Error(msg);\n  err.type = 'TransportError';\n  err.description = desc;\n  this.emit('error', err);\n  return this;\n};\n\n/**\n * Opens the transport.\n *\n * @api public\n */\n\nTransport.prototype.open = function () {\n  if ('closed' === this.readyState || '' === this.readyState) {\n    this.readyState = 'opening';\n    this.doOpen();\n  }\n\n  return this;\n};\n\n/**\n * Closes the transport.\n *\n * @api private\n */\n\nTransport.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.doClose();\n    this.onClose();\n  }\n\n  return this;\n};\n\n/**\n * Sends multiple packets.\n *\n * @param {Array} packets\n * @api private\n */\n\nTransport.prototype.send = function (packets) {\n  if ('open' === this.readyState) {\n    this.write(packets);\n  } else {\n    throw new Error('Transport not open');\n  }\n};\n\n/**\n * Called upon open\n *\n * @api private\n */\n\nTransport.prototype.onOpen = function () {\n  this.readyState = 'open';\n  this.writable = true;\n  this.emit('open');\n};\n\n/**\n * Called with data.\n *\n * @param {String} data\n * @api private\n */\n\nTransport.prototype.onData = function (data) {\n  var packet = parser.decodePacket(data, this.socket.binaryType);\n  this.onPacket(packet);\n};\n\n/**\n * Called with a decoded packet.\n */\n\nTransport.prototype.onPacket = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon close.\n *\n * @api private\n */\n\nTransport.prototype.onClose = function () {\n  this.readyState = 'closed';\n  this.emit('close');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transport.js\n// module id = 20\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar keys = require('./keys');\nvar hasBinary = require('has-binary2');\nvar sliceBuffer = require('arraybuffer.slice');\nvar after = require('after');\nvar utf8 = require('./utf8');\n\nvar base64encoder;\nif (global && global.ArrayBuffer) {\n  base64encoder = require('base64-arraybuffer');\n}\n\n/**\n * Check if we are running an android browser. That requires us to use\n * ArrayBuffer with polling transports...\n *\n * http://ghinda.net/jpeg-blob-ajax-android/\n */\n\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\n/**\n * Check if we are running in PhantomJS.\n * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n * https://github.com/ariya/phantomjs/issues/11395\n * @type boolean\n */\nvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\n/**\n * When true, avoids using Blobs to encode payloads.\n * @type boolean\n */\nvar dontSendBlobs = isAndroid || isPhantomJS;\n\n/**\n * Current protocol version.\n */\n\nexports.protocol = 3;\n\n/**\n * Packet types.\n */\n\nvar packets = exports.packets = {\n    open:     0    // non-ws\n  , close:    1    // non-ws\n  , ping:     2\n  , pong:     3\n  , message:  4\n  , upgrade:  5\n  , noop:     6\n};\n\nvar packetslist = keys(packets);\n\n/**\n * Premade error packet.\n */\n\nvar err = { type: 'error', data: 'parser error' };\n\n/**\n * Create a blob api even for blob builder when vendor prefixes exist\n */\n\nvar Blob = require('blob');\n\n/**\n * Encodes a packet.\n *\n *     <packet type id> [ <data> ]\n *\n * Example:\n *\n *     5hello world\n *     3\n *     4\n *\n * Binary is encoded in an identical principle\n *\n * @api private\n */\n\nexports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = false;\n  }\n\n  if (typeof utf8encode === 'function') {\n    callback = utf8encode;\n    utf8encode = null;\n  }\n\n  var data = (packet.data === undefined)\n    ? undefined\n    : packet.data.buffer || packet.data;\n\n  if (global.ArrayBuffer && data instanceof ArrayBuffer) {\n    return encodeArrayBuffer(packet, supportsBinary, callback);\n  } else if (Blob && data instanceof global.Blob) {\n    return encodeBlob(packet, supportsBinary, callback);\n  }\n\n  // might be an object with { base64: true, data: dataAsBase64String }\n  if (data && data.base64) {\n    return encodeBase64Object(packet, callback);\n  }\n\n  // Sending data as a utf-8 string\n  var encoded = packets[packet.type];\n\n  // data fragment is optional\n  if (undefined !== packet.data) {\n    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n  }\n\n  return callback('' + encoded);\n\n};\n\nfunction encodeBase64Object(packet, callback) {\n  // packet data is an object { base64: true, data: dataAsBase64String }\n  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n  return callback(message);\n}\n\n/**\n * Encode packet helpers for binary types\n */\n\nfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var data = packet.data;\n  var contentArray = new Uint8Array(data);\n  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\n  resultBuffer[0] = packets[packet.type];\n  for (var i = 0; i < contentArray.length; i++) {\n    resultBuffer[i+1] = contentArray[i];\n  }\n\n  return callback(resultBuffer.buffer);\n}\n\nfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var fr = new FileReader();\n  fr.onload = function() {\n    packet.data = fr.result;\n    exports.encodePacket(packet, supportsBinary, true, callback);\n  };\n  return fr.readAsArrayBuffer(packet.data);\n}\n\nfunction encodeBlob(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  if (dontSendBlobs) {\n    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n  }\n\n  var length = new Uint8Array(1);\n  length[0] = packets[packet.type];\n  var blob = new Blob([length.buffer, packet.data]);\n\n  return callback(blob);\n}\n\n/**\n * Encodes a packet with binary data in a base64 string\n *\n * @param {Object} packet, has `type` and `data`\n * @return {String} base64 encoded message\n */\n\nexports.encodeBase64Packet = function(packet, callback) {\n  var message = 'b' + exports.packets[packet.type];\n  if (Blob && packet.data instanceof global.Blob) {\n    var fr = new FileReader();\n    fr.onload = function() {\n      var b64 = fr.result.split(',')[1];\n      callback(message + b64);\n    };\n    return fr.readAsDataURL(packet.data);\n  }\n\n  var b64data;\n  try {\n    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n  } catch (e) {\n    // iPhone Safari doesn't let you apply with typed arrays\n    var typed = new Uint8Array(packet.data);\n    var basic = new Array(typed.length);\n    for (var i = 0; i < typed.length; i++) {\n      basic[i] = typed[i];\n    }\n    b64data = String.fromCharCode.apply(null, basic);\n  }\n  message += global.btoa(b64data);\n  return callback(message);\n};\n\n/**\n * Decodes a packet. Changes format to Blob if requested.\n *\n * @return {Object} with `type` and `data` (if any)\n * @api private\n */\n\nexports.decodePacket = function (data, binaryType, utf8decode) {\n  if (data === undefined) {\n    return err;\n  }\n  // String data\n  if (typeof data === 'string') {\n    if (data.charAt(0) === 'b') {\n      return exports.decodeBase64Packet(data.substr(1), binaryType);\n    }\n\n    if (utf8decode) {\n      data = tryDecode(data);\n      if (data === false) {\n        return err;\n      }\n    }\n    var type = data.charAt(0);\n\n    if (Number(type) != type || !packetslist[type]) {\n      return err;\n    }\n\n    if (data.length > 1) {\n      return { type: packetslist[type], data: data.substring(1) };\n    } else {\n      return { type: packetslist[type] };\n    }\n  }\n\n  var asArray = new Uint8Array(data);\n  var type = asArray[0];\n  var rest = sliceBuffer(data, 1);\n  if (Blob && binaryType === 'blob') {\n    rest = new Blob([rest]);\n  }\n  return { type: packetslist[type], data: rest };\n};\n\nfunction tryDecode(data) {\n  try {\n    data = utf8.decode(data, { strict: false });\n  } catch (e) {\n    return false;\n  }\n  return data;\n}\n\n/**\n * Decodes a packet encoded in a base64 string\n *\n * @param {String} base64 encoded message\n * @return {Object} with `type` and `data` (if any)\n */\n\nexports.decodeBase64Packet = function(msg, binaryType) {\n  var type = packetslist[msg.charAt(0)];\n  if (!base64encoder) {\n    return { type: type, data: { base64: true, data: msg.substr(1) } };\n  }\n\n  var data = base64encoder.decode(msg.substr(1));\n\n  if (binaryType === 'blob' && Blob) {\n    data = new Blob([data]);\n  }\n\n  return { type: type, data: data };\n};\n\n/**\n * Encodes multiple messages (payload).\n *\n *     <length>:data\n *\n * Example:\n *\n *     11:hello world2:hi\n *\n * If any contents are binary, they will be encoded as base64 strings. Base64\n * encoded strings are marked with a b before the length specifier\n *\n * @param {Array} packets\n * @api private\n */\n\nexports.encodePayload = function (packets, supportsBinary, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = null;\n  }\n\n  var isBinary = hasBinary(packets);\n\n  if (supportsBinary && isBinary) {\n    if (Blob && !dontSendBlobs) {\n      return exports.encodePayloadAsBlob(packets, callback);\n    }\n\n    return exports.encodePayloadAsArrayBuffer(packets, callback);\n  }\n\n  if (!packets.length) {\n    return callback('0:');\n  }\n\n  function setLengthHeader(message) {\n    return message.length + ':' + message;\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n      doneCallback(null, setLengthHeader(message));\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(results.join(''));\n  });\n};\n\n/**\n * Async array map using after\n */\n\nfunction map(ary, each, done) {\n  var result = new Array(ary.length);\n  var next = after(ary.length, done);\n\n  var eachWithIndex = function(i, el, cb) {\n    each(el, function(error, msg) {\n      result[i] = msg;\n      cb(error, result);\n    });\n  };\n\n  for (var i = 0; i < ary.length; i++) {\n    eachWithIndex(i, ary[i], next);\n  }\n}\n\n/*\n * Decodes data when a payload is maybe expected. Possible binary contents are\n * decoded from their base64 representation\n *\n * @param {String} data, callback method\n * @api public\n */\n\nexports.decodePayload = function (data, binaryType, callback) {\n  if (typeof data !== 'string') {\n    return exports.decodePayloadAsBinary(data, binaryType, callback);\n  }\n\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var packet;\n  if (data === '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n  var length = '', n, msg;\n\n  for (var i = 0, l = data.length; i < l; i++) {\n    var chr = data.charAt(i);\n\n    if (chr !== ':') {\n      length += chr;\n      continue;\n    }\n\n    if (length === '' || (length != (n = Number(length)))) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    msg = data.substr(i + 1, n);\n\n    if (length != msg.length) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    if (msg.length) {\n      packet = exports.decodePacket(msg, binaryType, false);\n\n      if (err.type === packet.type && err.data === packet.data) {\n        // parser error in individual packet - ignoring payload\n        return callback(err, 0, 1);\n      }\n\n      var ret = callback(packet, i + n, l);\n      if (false === ret) return;\n    }\n\n    // advance cursor\n    i += n;\n    length = '';\n  }\n\n  if (length !== '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n};\n\n/**\n * Encodes multiple messages (payload) as binary.\n *\n * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n * 255><data>\n *\n * Example:\n * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n *\n * @param {Array} packets\n * @return {ArrayBuffer} encoded payload\n * @api private\n */\n\nexports.encodePayloadAsArrayBuffer = function(packets, callback) {\n  if (!packets.length) {\n    return callback(new ArrayBuffer(0));\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(data) {\n      return doneCallback(null, data);\n    });\n  }\n\n  map(packets, encodeOne, function(err, encodedPackets) {\n    var totalLength = encodedPackets.reduce(function(acc, p) {\n      var len;\n      if (typeof p === 'string'){\n        len = p.length;\n      } else {\n        len = p.byteLength;\n      }\n      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n    }, 0);\n\n    var resultArray = new Uint8Array(totalLength);\n\n    var bufferIndex = 0;\n    encodedPackets.forEach(function(p) {\n      var isString = typeof p === 'string';\n      var ab = p;\n      if (isString) {\n        var view = new Uint8Array(p.length);\n        for (var i = 0; i < p.length; i++) {\n          view[i] = p.charCodeAt(i);\n        }\n        ab = view.buffer;\n      }\n\n      if (isString) { // not true binary\n        resultArray[bufferIndex++] = 0;\n      } else { // true binary\n        resultArray[bufferIndex++] = 1;\n      }\n\n      var lenStr = ab.byteLength.toString();\n      for (var i = 0; i < lenStr.length; i++) {\n        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n      }\n      resultArray[bufferIndex++] = 255;\n\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < view.length; i++) {\n        resultArray[bufferIndex++] = view[i];\n      }\n    });\n\n    return callback(resultArray.buffer);\n  });\n};\n\n/**\n * Encode as Blob\n */\n\nexports.encodePayloadAsBlob = function(packets, callback) {\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(encoded) {\n      var binaryIdentifier = new Uint8Array(1);\n      binaryIdentifier[0] = 1;\n      if (typeof encoded === 'string') {\n        var view = new Uint8Array(encoded.length);\n        for (var i = 0; i < encoded.length; i++) {\n          view[i] = encoded.charCodeAt(i);\n        }\n        encoded = view.buffer;\n        binaryIdentifier[0] = 0;\n      }\n\n      var len = (encoded instanceof ArrayBuffer)\n        ? encoded.byteLength\n        : encoded.size;\n\n      var lenStr = len.toString();\n      var lengthAry = new Uint8Array(lenStr.length + 1);\n      for (var i = 0; i < lenStr.length; i++) {\n        lengthAry[i] = parseInt(lenStr[i]);\n      }\n      lengthAry[lenStr.length] = 255;\n\n      if (Blob) {\n        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n        doneCallback(null, blob);\n      }\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(new Blob(results));\n  });\n};\n\n/*\n * Decodes data when a payload is maybe expected. Strings are decoded by\n * interpreting each byte as a key code for entries marked to start with 0. See\n * description of encodePayloadAsBinary\n *\n * @param {ArrayBuffer} data, callback method\n * @api public\n */\n\nexports.decodePayloadAsBinary = function (data, binaryType, callback) {\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var bufferTail = data;\n  var buffers = [];\n\n  while (bufferTail.byteLength > 0) {\n    var tailArray = new Uint8Array(bufferTail);\n    var isString = tailArray[0] === 0;\n    var msgLength = '';\n\n    for (var i = 1; ; i++) {\n      if (tailArray[i] === 255) break;\n\n      // 310 = char length of Number.MAX_VALUE\n      if (msgLength.length > 310) {\n        return callback(err, 0, 1);\n      }\n\n      msgLength += tailArray[i];\n    }\n\n    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n    msgLength = parseInt(msgLength);\n\n    var msg = sliceBuffer(bufferTail, 0, msgLength);\n    if (isString) {\n      try {\n        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n      } catch (e) {\n        // iPhone Safari doesn't let you apply to typed arrays\n        var typed = new Uint8Array(msg);\n        msg = '';\n        for (var i = 0; i < typed.length; i++) {\n          msg += String.fromCharCode(typed[i]);\n        }\n      }\n    }\n\n    buffers.push(msg);\n    bufferTail = sliceBuffer(bufferTail, msgLength);\n  }\n\n  var total = buffers.length;\n  buffers.forEach(function(buffer, i) {\n    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/browser.js\n// module id = 21\n// module chunks = 0", "\n/**\n * Gets the keys for an object.\n *\n * @return {Array} keys\n * @api private\n */\n\nmodule.exports = Object.keys || function keys (obj){\n  var arr = [];\n  var has = Object.prototype.hasOwnProperty;\n\n  for (var i in obj) {\n    if (has.call(obj, i)) {\n      arr.push(i);\n    }\n  }\n  return arr;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/keys.js\n// module id = 22\n// module chunks = 0", "/* global Blob File */\n\n/*\n * Module requirements.\n */\n\nvar isArray = require('isarray');\n\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof global.Blob === 'function' || toString.call(global.Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof global.File === 'function' || toString.call(global.File) === '[object FileConstructor]';\n\n/**\n * Module exports.\n */\n\nmodule.exports = hasBinary;\n\n/**\n * Checks for binary data.\n *\n * Supports Buffer, ArrayBuffer, Blob and File.\n *\n * @param {Object} anything\n * @api public\n */\n\nfunction hasBinary (obj) {\n  if (!obj || typeof obj !== 'object') {\n    return false;\n  }\n\n  if (isArray(obj)) {\n    for (var i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  if ((typeof global.Buffer === 'function' && global.Buffer.isBuffer && global.Buffer.isBuffer(obj)) ||\n     (typeof global.ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n     (withNativeBlob && obj instanceof Blob) ||\n     (withNativeFile && obj instanceof File)\n    ) {\n    return true;\n  }\n\n  // see: https://github.com/Automattic/has-binary/pull/4\n  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-binary2/index.js\n// module id = 23\n// module chunks = 0", "/**\n * An abstraction for slicing an arraybuffer even when\n * ArrayBuffer.prototype.slice is not supported\n *\n * @api public\n */\n\nmodule.exports = function(arraybuffer, start, end) {\n  var bytes = arraybuffer.byteLength;\n  start = start || 0;\n  end = end || bytes;\n\n  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\n  if (start < 0) { start += bytes; }\n  if (end < 0) { end += bytes; }\n  if (end > bytes) { end = bytes; }\n\n  if (start >= bytes || start >= end || bytes === 0) {\n    return new ArrayBuffer(0);\n  }\n\n  var abv = new Uint8Array(arraybuffer);\n  var result = new Uint8Array(end - start);\n  for (var i = start, ii = 0; i < end; i++, ii++) {\n    result[ii] = abv[i];\n  }\n  return result.buffer;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/arraybuffer.slice/index.js\n// module id = 24\n// module chunks = 0", "module.exports = after\n\nfunction after(count, callback, err_cb) {\n    var bail = false\n    err_cb = err_cb || noop\n    proxy.count = count\n\n    return (count === 0) ? callback() : proxy\n\n    function proxy(err, result) {\n        if (proxy.count <= 0) {\n            throw new Error('after called too many times')\n        }\n        --proxy.count\n\n        // after first error, rest are passed to err_cb\n        if (err) {\n            bail = true\n            callback(err)\n            // future error callbacks will go to error handler\n            callback = err_cb\n        } else if (proxy.count === 0 && !bail) {\n            callback(null, result)\n        }\n    }\n}\n\nfunction noop() {}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/after/index.js\n// module id = 25\n// module chunks = 0", "/*! https://mths.be/utf8js v2.1.2 by @mathias */\n;(function(root) {\n\n\t// Detect free variables `exports`\n\tvar freeExports = typeof exports == 'object' && exports;\n\n\t// Detect free variable `module`\n\tvar freeModule = typeof module == 'object' && module &&\n\t\tmodule.exports == freeExports && module;\n\n\t// Detect free variable `global`, from Node.js or Browserified code,\n\t// and use it as `root`\n\tvar freeGlobal = typeof global == 'object' && global;\n\tif (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {\n\t\troot = freeGlobal;\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tvar stringFromCharCode = String.fromCharCode;\n\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2decode(string) {\n\t\tvar output = [];\n\t\tvar counter = 0;\n\t\tvar length = string.length;\n\t\tvar value;\n\t\tvar extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2encode(array) {\n\t\tvar length = array.length;\n\t\tvar index = -1;\n\t\tvar value;\n\t\tvar output = '';\n\t\twhile (++index < length) {\n\t\t\tvalue = array[index];\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t}\n\t\treturn output;\n\t}\n\n\tfunction checkScalarValue(codePoint, strict) {\n\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\tif (strict) {\n\t\t\t\tthrow Error(\n\t\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t\t' is not a scalar value'\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\t/*--------------------------------------------------------------------------*/\n\n\tfunction createByte(codePoint, shift) {\n\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t}\n\n\tfunction encodeCodePoint(codePoint, strict) {\n\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\treturn stringFromCharCode(codePoint);\n\t\t}\n\t\tvar symbol = '';\n\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t}\n\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\t\tcodePoint = 0xFFFD;\n\t\t\t}\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\treturn symbol;\n\t}\n\n\tfunction utf8encode(string, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\n\t\tvar codePoints = ucs2decode(string);\n\t\tvar length = codePoints.length;\n\t\tvar index = -1;\n\t\tvar codePoint;\n\t\tvar byteString = '';\n\t\twhile (++index < length) {\n\t\t\tcodePoint = codePoints[index];\n\t\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t\t}\n\t\treturn byteString;\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tfunction readContinuationByte() {\n\t\tif (byteIndex >= byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\n\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\n\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\treturn continuationByte & 0x3F;\n\t\t}\n\n\t\t// If we end up here, it’s not a continuation byte\n\t\tthrow Error('Invalid continuation byte');\n\t}\n\n\tfunction decodeSymbol(strict) {\n\t\tvar byte1;\n\t\tvar byte2;\n\t\tvar byte3;\n\t\tvar byte4;\n\t\tvar codePoint;\n\n\t\tif (byteIndex > byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\n\t\tif (byteIndex == byteCount) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Read first byte\n\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\n\t\t// 1-byte sequence (no continuation bytes)\n\t\tif ((byte1 & 0x80) == 0) {\n\t\t\treturn byte1;\n\t\t}\n\n\t\t// 2-byte sequence\n\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\tif (codePoint >= 0x80) {\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\n\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\n\t\t// 4-byte sequence\n\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tbyte4 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\treturn codePoint;\n\t\t\t}\n\t\t}\n\n\t\tthrow Error('Invalid UTF-8 detected');\n\t}\n\n\tvar byteArray;\n\tvar byteCount;\n\tvar byteIndex;\n\tfunction utf8decode(byteString, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\n\t\tbyteArray = ucs2decode(byteString);\n\t\tbyteCount = byteArray.length;\n\t\tbyteIndex = 0;\n\t\tvar codePoints = [];\n\t\tvar tmp;\n\t\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\t\tcodePoints.push(tmp);\n\t\t}\n\t\treturn ucs2encode(codePoints);\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tvar utf8 = {\n\t\t'version': '2.1.2',\n\t\t'encode': utf8encode,\n\t\t'decode': utf8decode\n\t};\n\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof define == 'function' &&\n\t\ttypeof define.amd == 'object' &&\n\t\tdefine.amd\n\t) {\n\t\tdefine(function() {\n\t\t\treturn utf8;\n\t\t});\n\t}\telse if (freeExports && !freeExports.nodeType) {\n\t\tif (freeModule) { // in Node.js or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = utf8;\n\t\t} else { // in Narwhal or RingoJS v0.7.0-\n\t\t\tvar object = {};\n\t\t\tvar hasOwnProperty = object.hasOwnProperty;\n\t\t\tfor (var key in utf8) {\n\t\t\t\thasOwnProperty.call(utf8, key) && (freeExports[key] = utf8[key]);\n\t\t\t}\n\t\t}\n\t} else { // in Rhino or a web browser\n\t\troot.utf8 = utf8;\n\t}\n\n}(this));\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/utf8.js\n// module id = 26\n// module chunks = 0", "module.exports = function(module) {\r\n\tif(!module.webpackPolyfill) {\r\n\t\tmodule.deprecate = function() {};\r\n\t\tmodule.paths = [];\r\n\t\t// module.parent = undefined by default\r\n\t\tmodule.children = [];\r\n\t\tmodule.webpackPolyfill = 1;\r\n\t}\r\n\treturn module;\r\n}\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// (webpack)/buildin/module.js\n// module id = 27\n// module chunks = 0", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(){\n  \"use strict\";\n\n  var chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\n  // Use a lookup table to find the index.\n  var lookup = new Uint8Array(256);\n  for (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n  }\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = lookup[base64.charCodeAt(i)];\n      encoded2 = lookup[base64.charCodeAt(i+1)];\n      encoded3 = lookup[base64.charCodeAt(i+2)];\n      encoded4 = lookup[base64.charCodeAt(i+3)];\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/base64-arraybuffer/lib/base64-arraybuffer.js\n// module id = 28\n// module chunks = 0", "/**\n * Create a blob builder even when vendor prefixes exist\n */\n\nvar BlobBuilder = global.BlobBuilder\n  || global.WebKitBlobBuilder\n  || global.MSBlobBuilder\n  || global.MozBlobBuilder;\n\n/**\n * Check if Blob constructor is supported\n */\n\nvar blobSupported = (function() {\n  try {\n    var a = new Blob(['hi']);\n    return a.size === 2;\n  } catch(e) {\n    return false;\n  }\n})();\n\n/**\n * Check if Blob constructor supports ArrayBufferViews\n * Fails in Safari 6, so we need to map to ArrayBuffers there.\n */\n\nvar blobSupportsArrayBufferView = blobSupported && (function() {\n  try {\n    var b = new Blob([new Uint8Array([1,2])]);\n    return b.size === 2;\n  } catch(e) {\n    return false;\n  }\n})();\n\n/**\n * Check if BlobBuilder is supported\n */\n\nvar blobBuilderSupported = BlobBuilder\n  && BlobBuilder.prototype.append\n  && BlobBuilder.prototype.getBlob;\n\n/**\n * Helper function that maps ArrayBufferViews to ArrayBuffers\n * Used by BlobBuilder constructor and old browsers that didn't\n * support it in the Blob constructor.\n */\n\nfunction mapArrayBufferViews(ary) {\n  for (var i = 0; i < ary.length; i++) {\n    var chunk = ary[i];\n    if (chunk.buffer instanceof ArrayBuffer) {\n      var buf = chunk.buffer;\n\n      // if this is a subarray, make a copy so we only\n      // include the subarray region from the underlying buffer\n      if (chunk.byteLength !== buf.byteLength) {\n        var copy = new Uint8Array(chunk.byteLength);\n        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\n        buf = copy.buffer;\n      }\n\n      ary[i] = buf;\n    }\n  }\n}\n\nfunction BlobBuilderConstructor(ary, options) {\n  options = options || {};\n\n  var bb = new BlobBuilder();\n  mapArrayBufferViews(ary);\n\n  for (var i = 0; i < ary.length; i++) {\n    bb.append(ary[i]);\n  }\n\n  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\n};\n\nfunction BlobConstructor(ary, options) {\n  mapArrayBufferViews(ary);\n  return new Blob(ary, options || {});\n};\n\nmodule.exports = (function() {\n  if (blobSupported) {\n    return blobSupportsArrayBufferView ? global.Blob : BlobConstructor;\n  } else if (blobBuilderSupported) {\n    return BlobBuilderConstructor;\n  } else {\n    return undefined;\n  }\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/blob/index.js\n// module id = 29\n// module chunks = 0", "/**\r\n * Compiles a querystring\r\n * Returns string representation of the object\r\n *\r\n * @param {Object}\r\n * @api private\r\n */\r\n\r\nexports.encode = function (obj) {\r\n  var str = '';\r\n\r\n  for (var i in obj) {\r\n    if (obj.hasOwnProperty(i)) {\r\n      if (str.length) str += '&';\r\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\r\n    }\r\n  }\r\n\r\n  return str;\r\n};\r\n\r\n/**\r\n * Parses a simple querystring into an object\r\n *\r\n * @param {String} qs\r\n * @api private\r\n */\r\n\r\nexports.decode = function(qs){\r\n  var qry = {};\r\n  var pairs = qs.split('&');\r\n  for (var i = 0, l = pairs.length; i < l; i++) {\r\n    var pair = pairs[i].split('=');\r\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\r\n  }\r\n  return qry;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseqs/index.js\n// module id = 30\n// module chunks = 0", "\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-inherit/index.js\n// module id = 31\n// module chunks = 0", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/yeast/index.js\n// module id = 32\n// module chunks = 0", "\n/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    if (!global.___eio) global.___eio = [];\n    callbacks = global.___eio;\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (global.document && global.addEventListener) {\n    global.addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-jsonp.js\n// module id = 33\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\nvar BrowserWebSocket = global.WebSocket || global.MozWebSocket;\nvar NodeWebSocket;\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocket = BrowserWebSocket;\nif (!WebSocket && typeof window === 'undefined') {\n  WebSocket = NodeWebSocket;\n}\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocket = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n  var opts = {\n    agent: this.agent,\n    perMessageDeflate: this.perMessageDeflate\n  };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws = this.usingBrowserWebSocket ? (protocols ? new WebSocket(uri, protocols) : new WebSocket(uri)) : new WebSocket(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? global.Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug('websocket closed before onclose event');\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocket && !('__initialize' in WebSocket && this.name === WS.prototype.name);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/websocket.js\n// module id = 34\n// module chunks = 0", "\nvar indexOf = [].indexOf;\n\nmodule.exports = function(arr, obj){\n  if (indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/indexof/index.js\n// module id = 36\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parser = require('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar toArray = require('to-array');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:socket');\nvar parseqs = require('parseqs');\nvar hasBin = require('has-binary2');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = Socket;\n\n/**\n * Internal events (blacklisted).\n * These events can't be emitted by the user.\n *\n * @api private\n */\n\nvar events = {\n  connect: 1,\n  connect_error: 1,\n  connect_timeout: 1,\n  connecting: 1,\n  disconnect: 1,\n  error: 1,\n  reconnect: 1,\n  reconnect_attempt: 1,\n  reconnect_failed: 1,\n  reconnect_error: 1,\n  reconnecting: 1,\n  ping: 1,\n  pong: 1\n};\n\n/**\n * Shortcut to `Emitter#emit`.\n */\n\nvar emit = Emitter.prototype.emit;\n\n/**\n * `Socket` constructor.\n *\n * @api public\n */\n\nfunction Socket (io, nsp, opts) {\n  this.io = io;\n  this.nsp = nsp;\n  this.json = this; // compat\n  this.ids = 0;\n  this.acks = {};\n  this.receiveBuffer = [];\n  this.sendBuffer = [];\n  this.connected = false;\n  this.disconnected = true;\n  this.flags = {};\n  if (opts && opts.query) {\n    this.query = opts.query;\n  }\n  if (this.io.autoConnect) this.open();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Subscribe to open, close and packet events\n *\n * @api private\n */\n\nSocket.prototype.subEvents = function () {\n  if (this.subs) return;\n\n  var io = this.io;\n  this.subs = [\n    on(io, 'open', bind(this, 'onopen')),\n    on(io, 'packet', bind(this, 'onpacket')),\n    on(io, 'close', bind(this, 'onclose'))\n  ];\n};\n\n/**\n * \"Opens\" the socket.\n *\n * @api public\n */\n\nSocket.prototype.open =\nSocket.prototype.connect = function () {\n  if (this.connected) return this;\n\n  this.subEvents();\n  this.io.open(); // ensure open\n  if ('open' === this.io.readyState) this.onopen();\n  this.emit('connecting');\n  return this;\n};\n\n/**\n * Sends a `message` event.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.send = function () {\n  var args = toArray(arguments);\n  args.unshift('message');\n  this.emit.apply(this, args);\n  return this;\n};\n\n/**\n * Override `emit`.\n * If the event is in `events`, it's emitted normally.\n *\n * @param {String} event name\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.emit = function (ev) {\n  if (events.hasOwnProperty(ev)) {\n    emit.apply(this, arguments);\n    return this;\n  }\n\n  var args = toArray(arguments);\n  var packet = {\n    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n    data: args\n  };\n\n  packet.options = {};\n  packet.options.compress = !this.flags || false !== this.flags.compress;\n\n  // event ack callback\n  if ('function' === typeof args[args.length - 1]) {\n    debug('emitting packet with ack id %d', this.ids);\n    this.acks[this.ids] = args.pop();\n    packet.id = this.ids++;\n  }\n\n  if (this.connected) {\n    this.packet(packet);\n  } else {\n    this.sendBuffer.push(packet);\n  }\n\n  this.flags = {};\n\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.packet = function (packet) {\n  packet.nsp = this.nsp;\n  this.io.packet(packet);\n};\n\n/**\n * Called upon engine `open`.\n *\n * @api private\n */\n\nSocket.prototype.onopen = function () {\n  debug('transport is open - connecting');\n\n  // write connect packet if necessary\n  if ('/' !== this.nsp) {\n    if (this.query) {\n      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n      debug('sending connect packet with query %s', query);\n      this.packet({type: parser.CONNECT, query: query});\n    } else {\n      this.packet({type: parser.CONNECT});\n    }\n  }\n};\n\n/**\n * Called upon engine `close`.\n *\n * @param {String} reason\n * @api private\n */\n\nSocket.prototype.onclose = function (reason) {\n  debug('close (%s)', reason);\n  this.connected = false;\n  this.disconnected = true;\n  delete this.id;\n  this.emit('disconnect', reason);\n};\n\n/**\n * Called with socket packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onpacket = function (packet) {\n  var sameNamespace = packet.nsp === this.nsp;\n  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\n  if (!sameNamespace && !rootNamespaceError) return;\n\n  switch (packet.type) {\n    case parser.CONNECT:\n      this.onconnect();\n      break;\n\n    case parser.EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.BINARY_EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.ACK:\n      this.onack(packet);\n      break;\n\n    case parser.BINARY_ACK:\n      this.onack(packet);\n      break;\n\n    case parser.DISCONNECT:\n      this.ondisconnect();\n      break;\n\n    case parser.ERROR:\n      this.emit('error', packet.data);\n      break;\n  }\n};\n\n/**\n * Called upon a server event.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onevent = function (packet) {\n  var args = packet.data || [];\n  debug('emitting event %j', args);\n\n  if (null != packet.id) {\n    debug('attaching ack callback to event');\n    args.push(this.ack(packet.id));\n  }\n\n  if (this.connected) {\n    emit.apply(this, args);\n  } else {\n    this.receiveBuffer.push(args);\n  }\n};\n\n/**\n * Produces an ack callback to emit with an event.\n *\n * @api private\n */\n\nSocket.prototype.ack = function (id) {\n  var self = this;\n  var sent = false;\n  return function () {\n    // prevent double callbacks\n    if (sent) return;\n    sent = true;\n    var args = toArray(arguments);\n    debug('sending ack %j', args);\n\n    self.packet({\n      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n      id: id,\n      data: args\n    });\n  };\n};\n\n/**\n * Called upon a server acknowlegement.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onack = function (packet) {\n  var ack = this.acks[packet.id];\n  if ('function' === typeof ack) {\n    debug('calling ack %s with %j', packet.id, packet.data);\n    ack.apply(this, packet.data);\n    delete this.acks[packet.id];\n  } else {\n    debug('bad ack %s', packet.id);\n  }\n};\n\n/**\n * Called upon server connect.\n *\n * @api private\n */\n\nSocket.prototype.onconnect = function () {\n  this.connected = true;\n  this.disconnected = false;\n  this.emit('connect');\n  this.emitBuffered();\n};\n\n/**\n * Emit buffered events (received and emitted).\n *\n * @api private\n */\n\nSocket.prototype.emitBuffered = function () {\n  var i;\n  for (i = 0; i < this.receiveBuffer.length; i++) {\n    emit.apply(this, this.receiveBuffer[i]);\n  }\n  this.receiveBuffer = [];\n\n  for (i = 0; i < this.sendBuffer.length; i++) {\n    this.packet(this.sendBuffer[i]);\n  }\n  this.sendBuffer = [];\n};\n\n/**\n * Called upon server disconnect.\n *\n * @api private\n */\n\nSocket.prototype.ondisconnect = function () {\n  debug('server disconnect (%s)', this.nsp);\n  this.destroy();\n  this.onclose('io server disconnect');\n};\n\n/**\n * Called upon forced client/server side disconnections,\n * this method ensures the manager stops tracking us and\n * that reconnections don't get triggered for this.\n *\n * @api private.\n */\n\nSocket.prototype.destroy = function () {\n  if (this.subs) {\n    // clean subscriptions to avoid reconnections\n    for (var i = 0; i < this.subs.length; i++) {\n      this.subs[i].destroy();\n    }\n    this.subs = null;\n  }\n\n  this.io.destroy(this);\n};\n\n/**\n * Disconnects the socket manually.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.close =\nSocket.prototype.disconnect = function () {\n  if (this.connected) {\n    debug('performing disconnect (%s)', this.nsp);\n    this.packet({ type: parser.DISCONNECT });\n  }\n\n  // remove socket from pool\n  this.destroy();\n\n  if (this.connected) {\n    // fire events\n    this.onclose('io client disconnect');\n  }\n  return this;\n};\n\n/**\n * Sets the compress flag.\n *\n * @param {Boolean} if `true`, compresses the sending data\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.compress = function (compress) {\n  this.flags.compress = compress;\n  return this;\n};\n\n/**\n * Sets the binary flag\n *\n * @param {Boolean} whether the emitted data contains binary\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.binary = function (binary) {\n  this.flags.binary = binary;\n  return this;\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/socket.js", "module.exports = toArray\n\nfunction toArray(list, index) {\n    var array = []\n\n    index = index || 0\n\n    for (var i = index || 0; i < list.length; i++) {\n        array[i - index] = list[i]\n    }\n\n    return array\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/to-array/index.js\n// module id = 38\n// module chunks = 0", "\n/**\n * Module exports.\n */\n\nmodule.exports = on;\n\n/**\n * Helper for subscriptions.\n *\n * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n * @param {String} event name\n * @param {Function} callback\n * @api public\n */\n\nfunction on (obj, ev, fn) {\n  obj.on(ev, fn);\n  return {\n    destroy: function () {\n      obj.removeListener(ev, fn);\n    }\n  };\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/on.js", "/**\n * Slice reference.\n */\n\nvar slice = [].slice;\n\n/**\n * Bind `obj` to `fn`.\n *\n * @param {Object} obj\n * @param {Function|String} fn or string\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function(obj, fn){\n  if ('string' == typeof fn) fn = obj[fn];\n  if ('function' != typeof fn) throw new Error('bind() requires a function');\n  var args = slice.call(arguments, 2);\n  return function(){\n    return fn.apply(obj, args.concat(slice.call(arguments)));\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-bind/index.js\n// module id = 40\n// module chunks = 0", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/backo2/index.js\n// module id = 41\n// module chunks = 0"], "sourceRoot": ""}