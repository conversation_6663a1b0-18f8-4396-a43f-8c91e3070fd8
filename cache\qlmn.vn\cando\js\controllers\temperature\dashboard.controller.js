(function StudentTemperatureDashboardController(app) {
    app.controller('StudentTemperatureDashboardController', StudentTemperatureDashboardController);

    function StudentTemperatureDashboardController($scope, $q, ACTIVE_MONTHS, AuthService, CommonService, CourseService, DiseaseService, $uibModal) {
        var vm = this;

        $scope.sys.module.title = 'Nhiệt độ học sinh';

    }

    StudentTemperatureDashboardController.$inject = [
        '$scope',
        '$q',
        'ACTIVE_MONTHS',
        'AuthService',
        'CommonService',
        'CourseService',
        'DiseaseService',
        '$uibModal'
    ];
})(window.angular_app);
