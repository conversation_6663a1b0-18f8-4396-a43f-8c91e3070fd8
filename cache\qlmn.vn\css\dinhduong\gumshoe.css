.content-gum{
	/*padding-left: 100px;*/
}
.table-gumshoe div.col1-static{
    border-right: 1px solid #ccc;
}
.table-gumshoe tbody td > div.col1-static{
    background: white;
}
.table-gumshoe thead th > div.col1-static,
.table-gumshoe thead th{
    background: #e6e6e6;
}
.table-gumshoe thead th{
    text-align: center;
}
.table-gumshoe thead{
	z-index: 10;
}
.table-gumshoe th,
.table-gumshoe td{
	border: 1px solid #ccc !important;
}
.table-gumshoe th.col1,
.table-gumshoe td.col1{
	position: absolute;
	left: -100px;
	background: #fff;
	height: 28px;
}
.table-gumshoe tbody{
	position: absolute;
}
.table-gumshoe tr{
	position: relative;
}
.table-gumshoe{
    /*width: 1500px;*/
}
#tb_gumshoe_detail{
    /*padding-top: 32px;*/
    height: 100%;
}
.nhap-food,.xuat-food,.ton-food{
    width: 33%;
    float: left;
    border-left: 1px solid #dedede;
}
.child-type{
    border: 1px solid #dedede;
}
.td-food-gum{
    width: 200px;
    border-left: 1px solid #484848;
    border-right: 1px solid #484848;
    border-top:1px solid #dedede;
    border-bottom:1px solid #dedede;
    text-align: center;
}
.td-date-gum{
    width: 100px;
}
.child-food-gum{
    width: 100%;
    display: inline-block;
}
.warehouse-gum{
    width: 15%;
    float: left;
}
.date-gum{
    width: 30%;
    float: left;
}
.head-gum{
    width: 100%;
    display: inline-block;
    background: #e9f6e5;
    border-bottom: 1px solid #484848;
    padding: 10px;
}
.content-gum{
    width: 100%;
    height: 80%;
    background: #FFF;
    overflow-y: scroll;
}
.warehouse-gum input{
    padding-top: 5px;
    margin-left: 5px;
}
.modal-body{
    padding: 0px;
}
.content-gum{
    /*padding-left: 15px;*/
}
.title-table-gum{
    background: -webkit-linear-gradient(#fafafa, #edebeb); 
    background: -o-linear-gradient(#fafafa, #edebeb); 
    background: -moz-linear-gradient(#fafafa, #edebeb);         
    background: linear-gradient(#fafafa, #edebeb);
}
.th-food-gum{
    border: 1px solid #484848;
    text-align: center;
}
.td-date-gum{
    border-left: 1px solid #484848 !important; 
    border-right: 1px solid #484848 !important;
}
.td-date-gum-child{
    border: 1px solid #dedede !important;
    text-align: center;
    border-right:  1px solid #484848 !important;
}
.report-gum {
    width: 35%;
    float: left;
    margin-left: 15%;
}
.ul-report-gumshoe li{
    float: left;
    margin-left: 10px;
}
.child-ul-gumshoe{
    padding-left: 0px;
    background: #FFF;
}
.child-ul-gumshoe li{
    float: none;
    padding-left: 0px;
}
.tbl_container{
    text-align: unset !important; 
}
.child-ul-gumshoe{
    display: none;
    position: absolute;
    z-index: 555;
}
.li-report-gumshoe:hover .child-ul-gumshoe{
    display: block;
}
.child-ul-gumshoe button{
    border:0px;
}
.child-ul-gumshoe button:hover{
    background: #FFF;
    color: red;
}