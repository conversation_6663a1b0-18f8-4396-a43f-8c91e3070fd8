(function StudentService(window, app) {
    'use strict';

    app.service('StudentService', StudentService);

    function StudentService($http, $q, $httpParamSerializerJQLike, APP_CONFIG) {
        var self = this;

        self.fetchStudents = function fetchStudents(page, limit, filters) {
            page = page || 1;
            limit = limit || 15;
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students';
            var options = {
                params: {filters: filters, page: page, limit: limit , r : Math.random()},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime());

            return $http.get(url, options)
                .then(function (response) {
                    var students = _.map(_.get(response, 'data.students.data'), function (student) {
                        return new window.models.Student(student);
                    });

                    var total = _.get(response, 'data.total', students.length);
                    var user =  _.get(response, 'data.user', {});
                    return {
                        total: total,
                        students: students,
                        user: user,
                    };
                })
                .catch(function () {
                    return [];
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        };

        self.store = function store(month, schoolYear, students) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students';

            var data = {
                month: month,
                school_year: schoolYear,
                students: students,
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime());

            return $http.post(url, JSON.stringify(data), {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    return true;
                })
                .catch(function () {
                    return false;
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        };

        self.generateDownloadExcelUrlForDashboard = function generateDownloadExcelUrlForDashboard(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/download';
            //debugger;
            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };

        self.generateDownloadExcelUrlForDashboardSDD = function generateDownloadExcelUrlForDashboard(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/sdd/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };

        self.generateDownloadExcelUrlForDashboardSDDAllYear = function generateDownloadExcelUrlForDashboard(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/sdd/download/all';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };

        self.generateDownloadExcelUrlForDashboardWeigthAndHeigth = function generateDownloadExcelUrlForDashboardWeigthAndHeigth(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/reports/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };

        self.changeConfig = function changeConfig(key, value) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/bmi/change-config';

            var data = {
                key: key,
                value: value
            };
            return $http.post(url, JSON.stringify(data), {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    return true;
                })
                .catch(function () {
                    return false;
                })
                .finally(function () {
                });
        };

        self.saveImportBmi = async function saveImportBmi(month, schoolYear, data) {
            const url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/excel/save-import-bmi';

            const body = {
                month: month,
                school_year: schoolYear,
                data: data,
            };

           return await fetch(url, {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json',
                   'Accept': 'application/json',
                   'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
               },
               body: JSON.stringify(body)
           });
        };

        return self;
    }

    StudentService.$inject = [
        '$http',
        '$q',
        '$httpParamSerializerJQLike',
        'APP_CONFIG',
    ];

})(window, window.angular_app);
