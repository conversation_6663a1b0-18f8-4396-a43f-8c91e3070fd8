angular_app.controller('payoutController', ['$scope', function ($scope) {
    $scope.control = 'payout';
    $scope.templates = {};
    $scope.fee = {};
    $scope.init = function () {
        var date = new Date();
        var month = date.getMonth() + 1;
        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                $scope.months = data.months;
                $scope.month = month.toString();
                $scope.year = data.months[0].year;

                $scope.feeConfigs = data.feeConfigs;
                $scope.fee.id = '';
                $scope.rows = [];
                $scope.selected = {};
            })
        });
    };
    $scope.getRows = function () {
        process($CFG.project + '/' + $scope.control + '/list?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data.data;
                $scope.rows = data.rows;
                $scope.fee.beginSurplus = data.begin_surplus;
                $scope.fee.count = 0;
                $scope.fee.check = '';
                /*Set lại là 0 khi gọi lại để đếm số thứ tự*/
            })
        });
    };

    $scope.monthChange = function () {
        $scope.getRows();
    };

    $scope.feeChange = function () {
        $scope.getRows();
    };

    $scope.sumCollection = function () {
        var rs = 0;
        if ($scope.rows) {
            angular.forEach($scope.rows, function (row, index) {
                if (row.fee !== "" && row.fee > 0) {
                    rs = $['+'](rs, row.fee);
                }
            })
        }
        return rs;
    };
    $scope.sumExpenditure = function () {
        var rs = 0;
        if ($scope.rows) {
            angular.forEach($scope.rows, function (row, index) {
                rs = $['+'](rs, row.cost_item);
            })
        }
        return rs;
    };
    $scope.getEndSurplus = function () {
        var rs = 0;
        $scope.fee.beginSurplus || ($scope.fee.beginSurplus = 0);
        rs = $['+'](rs, $scope.fee.beginSurplus);
        rs = $['+'](rs, $scope.sumCollection());
        rs = $['-'](rs, $scope.sumExpenditure());
        return rs;
    };
    $scope.rowSelect = function (row) {
        if (!row) {
            $scope.selected.rowAdd = true;
            angular.forEach($scope.rows, function (item, index) {
                item.selected = false;
            });
        } else {
            angular.forEach($scope.rows, function (item, index) {
                item.selected = false;
            });
            $scope.selected.rowAdd = false;
            row.selected = true;
        }
    };

    $scope.rowAdd = function () {
        var tmp = $scope.rows;
        var row = {
            day: '',
            cost_item: 0,
            explain: '',
            fee: 0,
            is_fee: false,
            number_cost: "",
            number_receipts: "",
        };
        tmp.push(row);
        $scope.rows = tmp;
    };

    $scope.delete = function () {
        var tmp = $scope.rows;
        if($scope.fee.check === '')
            alert("Vui lòng chọn một dòng!");
        else{
            tmp.splice($scope.fee.check, 1);
            $scope.rows = tmp;
        }
    };

    $scope.refresh = function (){
        var msg = '<div style="margin-left: 42px"><p>Bạn chắc chắn?</p><p>Hành động này sẽ cập nhật lại dữ liệu và các <span style="color: red">khoản chi hiện tại sẽ bị mất</span>!</p></div>';
        $.messager.confirm('Cảnh báo', msg, function(r){
            if (r){
                process($CFG.project + '/' + $scope.control + '/delete?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {
                    $scope.getRows();
                });
            }
        });
    };

    $scope.save = function () {
        var data = {
            month: {
                month: $scope.month,
                year: $scope.year
            },
            fee_category_id: $scope.fee.id,
            rows: $scope.rows,
            begin_surplus: $scope.fee.beginSurplus,
            end_surplus: $scope.getEndSurplus(),
            fees: $scope.sumCollection(),
            spent: $scope.sumExpenditure(),
            async: true
        };
        var urls = [$CFG.project, 'fee_report', 'saveSoquytienmat'];
        process(urls.join('/'), data, function () {
            $scope.getRows();
            alert("Lưu thành công!");
        });
    };

    $scope.onTick = function (index) {
        $scope.fee.check = index;
        $scope.fee.cost = $scope.rows[index].cost_item;
    };

    $scope.getCount = function (fee) {
        if (fee == 0)
            $scope.fee.count += 1;
        return $scope.fee.count;
    };

    /*Validate*/
    $scope.dayChange = function (row) {
        var days = (new Date($scope.year, $scope.month, 0)).getDate();
        if (row.day <= 0)
            row.day = 1;
        if (row.day > days)
            row.day = days;
    };

    $scope.moneyChange = function (row) {
        if (row.cost_item <= 0)
            row.cost_item = 0 - row.cost_item;
    };
}]);