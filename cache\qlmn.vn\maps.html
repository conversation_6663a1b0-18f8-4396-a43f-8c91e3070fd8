<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
    	<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/bootstrap-dialog.min.css" >
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/system/loading.css"/>
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/common.css"/>

    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
    <script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
    <script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
    <script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
    <script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
    <script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
    <script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
    <script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
    <script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
    <script src="http://localhost:3000/js/datagrid-detailview.js"></script>
    <script src="http://localhost:3000/js/datagrid-filter.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
    <script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>
    <script src="http://localhost:3000/js/library/axios.min.js?v=654981510"></script>
    <script src="http://localhost:3000/js/library/common.js?v=852507536"></script>
    <script src="http://localhost:3000/js/system/init.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/library.js?v=1617248343"></script>
	<script type="text/javascript">
		var is_map_gokids = 0;
			</script>
</head>
<body ng-app="app" ng-controller="appController">
<div id="loading"><div class="circle"></div></div>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/system/maps/tree.css"/>
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/system/maps/maps.css"/>
    <div ng-controller="mapsController" id="maps">
        <div class="header">
            <div class="col col-sm-3">
                <span><b>Sơ đồ chức năng</b></span>
            </div>
            <div class="col col-sm-6">
                <div class="form-inline">
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" class="form-control" id="keyword" name="keyword" placeholder="Từ khóa">
                            <div class="input-group-addon" id="search"><i class="fa fa-search"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="context">
			<div id="div_map_popup" style="float: right; margin-right:20px; z-index:-1; padding-top:15px;"></div>
            <div class="module" ng-repeat="one in trees">
                <h4>
                    <b title="Mã: {{one.id}}, Level: 1, Order: {{one.order_id}}"><i class="fa fa-circle">&nbsp;</i>
                        <a href="{{ one.url ? $domain + '/' + one.url : '#'}}" ng-bind="one.name" ng-if="one.url" target="_blank"></a>
                        <span ng-bind="one.name" ng-if="!one.url"></span>
                        <span class="tools">
                            <span ng-if="one.status">
                                <i class="fa fa-lightbulb-o clrOrange fs15" ng-click="openMapPopup(one)"></i>
                            </span>
							<span ng-if="!one.status && isAdmin">
                                <i class="fa fa-lightbulb-o" ng-click="openMapPopup(one)"></i>
                            </span>
                            <span ng-if="isAdmin">
                                <i class="fa fa-pencil mL5" ng-click="edit(one)"></i>
                                <i class="fa fa-trash-o mL5 clrRed" ng-click="del(one.id, one.order_id)"></i>
                            </span>
                        </span>
                    </b>
                </h4>
                <ul class="ui-tree">
                    <li ng-repeat="two in one.children">
                        <b title="Mã: {{two.id}}, Level: 2, Order: {{two.order_id}}">
                            <a href="{{ two.url ? $domain + '/' + two.url : '#'}}" ng-bind="two.name" ng-if="two.url" target="_blank"></a>
                            <span ng-bind="two.name" ng-if="!two.url"></span>
                            <span class="tools">
                            <span ng-if="two.status">
                                <i class="fa fa-lightbulb-o clrOrange fs15" ng-click="openMapPopup(two)"></i>
                            </span>
							<span ng-if="!two.status && isAdmin">
                                <i class="fa fa-lightbulb-o" ng-click="openMapPopup(two)"></i>
                            </span>
                            <span ng-if="isAdmin">
                                <i class="fa fa-pencil mL5" ng-click="edit(two)"></i>
                                <i class="fa fa-trash-o mL5 clrRed" ng-click="del(two.id, two.order_id)"></i>
                            </span>
                        </span>
                        </b>
                        <ul>
                            <li ng-repeat="three in two.children">
                                <a title="Mã: {{three.id}}, Level: 3, Order: {{three.order_id}}" href="{{ three.url ? $domain + '/' + three.url : '#'}}" ng-bind="three.name" ng-if="three.url" target="_blank"></a>
                                <span ng-bind="three.name" ng-if="!three.url" title="Mã: {{three.id}}, Level: 3, Order: {{three.order_id}}"></span>
                                <span class="tools">
                                    <span ng-if="three.status">
                                        <i class="fa fa-lightbulb-o clrOrange fs15" ng-click="openMapPopup(three)"></i>
                                    </span>
									<span ng-if="!three.status && isAdmin">
										<i class="fa fa-lightbulb-o" ng-click="openMapPopup(three)"></i>
									</span>
                                    <span ng-if="isAdmin">
                                        <i class="fa fa-pencil mL5" ng-click="edit(three)"></i>
                                        <i class="fa fa-trash-o mL5 clrRed" ng-click="del(three.id, three.order_id)"></i>
                                    </span>
                                </span>
                                <ul>
                                    <li ng-repeat="four in three.children">
                                        <a title="Mã: {{four.id}}, Level: 4, Order: {{four.order_id}}" href="{{ four.url ? $domain + '/' + four.url : '#'}}" ng-bind="four.name" ng-if="four.url" target="_blank"></a>
                                        <span ng-bind="four.name" ng-if="!four.url" title="Mã: {{four.id}}, Level: 4, Order: {{four.order_id}}"></span>
                                        <span class="tools">
                                            <span ng-if="four.status">
                                                <i class="fa fa-lightbulb-o clrOrange fs15" ng-click="openMapPopup(four)"></i>
                                            </span>
											<span ng-if="!four.status && isAdmin">
												<i class="fa fa-lightbulb-o" ng-click="openMapPopup(four)"></i>
											</span>
                                            <span ng-if="isAdmin">
                                                <i class="fa fa-pencil mL5" ng-click="edit(four)"></i>
                                                <i class="fa fa-trash-o mL5 clrRed" ng-click="del(four.id, four.order_id)"></i>
                                            </span>
                                            <ul>
                                                <li ng-repeat="five in four.children" title="Mã: {{five.id}}, Level: 5, Order: {{five.order_id}}">
                                                    <a href="{{ five.url ? $domain + '/' + five.url : '#'}}" ng-bind="five.name" ng-if="five.url" target="_blank"></a>
                                                    <span ng-bind="five.name" ng-if="!five.url"></span>
                                                    <span class="tools">
                                                        <span ng-if="five.status">
                                                            <i class="fa fa-lightbulb-o clrOrange fs15" ng-click="openMapPopup(five)"></i>
                                                        </span>
														<span ng-if="!five.status && isAdmin">
															<i class="fa fa-lightbulb-o" ng-click="openMapPopup(five)"></i>
														</span>
                                                        <span ng-if="isAdmin">
                                                            <i class="fa fa-pencil mL5" ng-click="edit(five)"></i>
                                                            <i class="fa fa-trash-o mL5 clrRed" ng-click="del(five.id, five.order_id)"></i>
                                                        </span>
                                                    </span>
                                                </li>
                                                <li class="tools" ng-if="isAdmin">
                                                    <i class="fa fa-plus" ng-click="add(four.id)"></i>
                                                </li>
                                            </ul>
                                        </span>
                                    </li>
                                    <li class="tools" ng-if="isAdmin">
                                        <i class="fa fa-plus" ng-click="add(three.id)"></i>
                                    </li>
                                </ul>
                            </li>
                            <li class="tools" ng-if="isAdmin">
                                <i class="fa fa-plus" ng-click="add(two.id)"></i>
                            </li>
                        </ul>
                    </li>
                    <li class="tools" ng-if="isAdmin">
                         <i class="fa fa-plus" ng-click="add(one.id)"></i>
                    </li>
                </ul>
            </div>
            <div class="module tools" ng-if="isAdmin">
                <i class="fa fa-plus" ng-click="add()"></i>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="http://localhost:3000/js/library/jquery.mark.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/library/dialog.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/system/maps/mark.js?v=486870927"></script>
    <script type="text/javascript" src="http://localhost:3000/js/system/maps/maps.js?v=1493632145"></script>

</body>
</html>
