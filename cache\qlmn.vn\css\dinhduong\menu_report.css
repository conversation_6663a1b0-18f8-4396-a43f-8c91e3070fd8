.tbl_container{
	padding-top: 40px;
}
.tbl_container label{
	font-size: 15px;padding-top: 3px; 
}
.date-gum {
	text-align: center !important;height: 40px;padding-top: 6px;
}
.function-kh-ct {
	background: #efefef;
}
.tbl_container .noidung {
	text-align: left !important;
}
.noidung {
	width: 100%;
	margin:auto;
	/*margin-top: 10px;*/
}

.tr-noidung,.select-class{
	/*padding: 8px 0px;*/
}
hr{
	margin: 0;
}
.tr-noidung{
	font-size: 14px;
	color: #333333;
}
.tr-noidung a{
	float: right;
	color: #6eaa2f;
}
.tr-noidung a:hover{
	color: #ff7b05;
}
.glyphicon-print{
	color: lightgrey;
	padding-right: 5px;
}
.tr-noidung a:hover{
	text-decoration: none;
}
.tdkhoi:hover{
	/*background: #e6eef7;*/
}
.icon-in span:hover{
	color: #ff7b05;
}
.tr-noidung:hover{
	/*background: #edf0f7;*/
}
.select-class{
	background: white;
}
.select-class{
	padding-right: 0;
}
.select-class select{
	width: auto;
	border: 1px solid #b1afaf;
}
.tr-nd-select{
	padding-top: 0;
	margin-bottom: 1px;
	font-size: 12px;
	/*margin-top: 10px;*/
}



.tk-next a { cursor: pointer;}
/*.tk-next a{
	width: 6px;
	overflow: hidden;
}
.tk-next img:hover{
	margin-left: -6px;
}*/
	.td-date{
		width: 110px;
		padding-left: 10px;
	}
	.td-w-60{
		width: 60%;
	}
	.td-w-40{
		width: 40%;
	}
	.td-w-30{
		width: 30%;
	}
	.btn-calo-week{
		color: #4778e6;
	}
	.btn-calo-week:hover {
		color: red;
		cursor: pointer;
	}
	.btn-calo-week:hover span{
		color: red;
		cursor: pointer;
	}
	.btn-calo-week span{
		color: #4778e6;
	}
	.bg-green{
		background: #68822d;
		color: #FFF;
	}
	.nhomtuoikho-m{
		width: 100%;
		display: inline-block;
		padding: 5px;
	}
	#disable{
		color: #d3d3d3;
	}
	#disable span{
		color: #d3d3d3;
	}
	#disable:hover{
		
		cursor: default;
	}
	#disable:hover span{
		cursor: default;
	}
	.content-menu-report .tdkhoi{
		height: 35px;
	    background: -webkit-linear-gradient(#ffffff, #e8eaf1);
	    text-align: center;
	    padding-top: 8px;
	    color: #ff7b05;
	    font-weight: bold;
	    font-size: 16px;
	    width: 100%;
	    position: absolute;
	    top: 0;
	}
	.content-menu-report .ndkhoi{
		height: 100%;
		overflow-y: auto;
		padding: 5px 10px;
	}
	.content-menu-report .motkhoi{
		height: 100%;
	    position: relative;
	    padding-top: 60px;
	    padding-bottom: 7px;
		background: #f8faff;
		border: 1px solid #dae2ea;
	}
	.content-menu-report .menu-report-phieukecho,
	.content-menu-report .menu-report-sotinhtienan,
	.content-menu-report .menu-report-calotuan{
		padding: 0;
		height: 100%;
	}
.content-menu-report {
	height: 95%;
}