const express = require('express');
const router = express.Router();
const moment = require('moment');

// Mock data cho admin
const mockLockModules = [
  {
    id: 341632,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 1,
    name: "<PERSON><PERSON> đo học sinh",
    controller: "cando",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:18:12",
    updated_at_vn: "06/08/2025, 18:18:12"
  },
  {
    id: 341634,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON> lý kho",
    controller: "storage",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:38:24",
    updated_at_vn: "06/08/2025, 18:38:24"
  },
  {
    id: 341636,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 2,
    name: "<PERSON><PERSON> đ<PERSON><PERSON> kh<PERSON>u phần",
    controller: "menu_adjust",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:38:24",
    updated_at_vn: "06/08/2025, 18:38:24"
  },
  {
    id: 341638,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 2,
    name: "Sổ tính tiền ăn",
    controller: "cash_book",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:38:24",
    updated_at_vn: "06/08/2025, 18:38:24"
  },
  {
    id: 341640,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 2,
    name: "Quản lý học sinh",
    controller: "student_management",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:38:24",
    updated_at_vn: "06/08/2025, 18:38:24"
  },
  {
    id: 341642,
    unit_id: 51461,
    schoolyear: 2024,
    project_id: 2,
    name: "Điểm danh học sinh",
    controller: "student_attendance",
    action: "all",
    status: 1,
    updated_at: "2025-08-06 18:38:24",
    updated_at_vn: "06/08/2025, 18:38:24"
  }
];

const mockUsers = [
  {
    id: 1,
    username: "admin",
    name: "Quản trị viên",
    email: "<EMAIL>",
    phone: "0123456789",
    unit_id: 51461,
    level: 1,
    status: 1,
    created_at: "2024-01-01 08:00:00",
    updated_at: "2025-08-06 18:00:00"
  },
  {
    id: 2,
    username: "teacher01",
    name: "Cô Nguyễn Thị A",
    email: "<EMAIL>",
    phone: "0987654321",
    unit_id: 51461,
    level: 3,
    status: 1,
    created_at: "2024-01-15 08:00:00",
    updated_at: "2025-08-06 17:30:00"
  },
  {
    id: 3,
    username: "nutritionist",
    name: "Cô Trần Thị B",
    email: "<EMAIL>",
    phone: "0912345678",
    unit_id: 51461,
    level: 4,
    status: 1,
    created_at: "2024-02-01 08:00:00",
    updated_at: "2025-08-06 16:45:00"
  }
];

const mockPermissionGroups = [
  {
    id: 1,
    name: "Quản trị viên",
    description: "Toàn quyền hệ thống",
    permissions: ["admin.*", "dinhduong.*", "cando.*", "report.*"],
    created_at: "2024-01-01 08:00:00"
  },
  {
    id: 2,
    name: "Giáo viên",
    description: "Quyền cơ bản cho giáo viên",
    permissions: ["dinhduong.view", "cando.view", "student.view"],
    created_at: "2024-01-01 08:00:00"
  },
  {
    id: 3,
    name: "Nhân viên dinh dưỡng",
    description: "Quyền quản lý dinh dưỡng",
    permissions: ["dinhduong.*", "storage.*", "menu.*"],
    created_at: "2024-01-01 08:00:00"
  }
];

// API Routes

// Lock Module APIs
router.post('/lock_module/list', (req, res) => {
  const { page = 1, rows = 30 } = req.body;
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = mockLockModules.slice(start, end);
  
  res.json(paginatedData);
});

router.post('/lock_module/changeStatus', (req, res) => {
  const { id, status } = req.body;
  const moduleIndex = mockLockModules.findIndex(m => m.id == id);
  
  if (moduleIndex !== -1) {
    mockLockModules[moduleIndex].status = parseInt(status);
    mockLockModules[moduleIndex].updated_at = moment().format('YYYY-MM-DD HH:mm:ss');
    mockLockModules[moduleIndex].updated_at_vn = moment().format('DD/MM/YYYY, HH:mm:ss');
    
    res.json({
      success: true,
      message: "Cập nhật trạng thái thành công",
      data: mockLockModules[moduleIndex]
    });
  } else {
    res.status(404).json({
      success: false,
      message: "Không tìm thấy module"
    });
  }
});

// User APIs
router.post('/user/list', (req, res) => {
  const { page = 1, rows = 30, search = '' } = req.body;
  let filteredUsers = mockUsers;
  
  if (search) {
    filteredUsers = mockUsers.filter(user => 
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.username.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredUsers.slice(start, end);
  
  res.json({
    total: filteredUsers.length,
    rows: paginatedData
  });
});

router.post('/user/create', (req, res) => {
  const { username, name, email, phone, level } = req.body;
  
  const newUser = {
    id: mockUsers.length + 1,
    username,
    name,
    email,
    phone,
    unit_id: 51461,
    level: parseInt(level),
    status: 1,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockUsers.push(newUser);
  
  res.json({
    success: true,
    message: "Tạo người dùng thành công",
    data: newUser
  });
});

router.post('/user/update', (req, res) => {
  const { id, username, name, email, phone, level } = req.body;
  const userIndex = mockUsers.findIndex(u => u.id == id);
  
  if (userIndex !== -1) {
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      username,
      name,
      email,
      phone,
      level: parseInt(level),
      updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
    };
    
    res.json({
      success: true,
      message: "Cập nhật người dùng thành công",
      data: mockUsers[userIndex]
    });
  } else {
    res.status(404).json({
      success: false,
      message: "Không tìm thấy người dùng"
    });
  }
});

router.post('/user/delete', (req, res) => {
  const { id } = req.body;
  const userIndex = mockUsers.findIndex(u => u.id == id);
  
  if (userIndex !== -1) {
    mockUsers[userIndex].status = 0;
    mockUsers[userIndex].updated_at = moment().format('YYYY-MM-DD HH:mm:ss');
    
    res.json({
      success: true,
      message: "Xóa người dùng thành công"
    });
  } else {
    res.status(404).json({
      success: false,
      message: "Không tìm thấy người dùng"
    });
  }
});

// Permission Group APIs
router.post('/user_permission_group/list', (req, res) => {
  const { page = 1, rows = 30 } = req.body;
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = mockPermissionGroups.slice(start, end);
  
  res.json({
    total: mockPermissionGroups.length,
    rows: paginatedData
  });
});

router.post('/user_permission_group/create', (req, res) => {
  const { name, description, permissions } = req.body;
  
  const newGroup = {
    id: mockPermissionGroups.length + 1,
    name,
    description,
    permissions: permissions || [],
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockPermissionGroups.push(newGroup);
  
  res.json({
    success: true,
    message: "Tạo nhóm quyền thành công",
    data: newGroup
  });
});

// Unit Staff APIs
router.post('/unit_staff/list', (req, res) => {
  const staffData = mockUsers.map(user => ({
    ...user,
    position: user.level === 1 ? "Quản trị viên" : 
              user.level === 3 ? "Giáo viên" : 
              user.level === 4 ? "Nhân viên dinh dưỡng" : "Nhân viên"
  }));
  
  res.json({
    total: staffData.length,
    rows: staffData
  });
});

// User Log APIs
router.post('/user_log/list', (req, res) => {
  const mockLogs = [
    {
      id: 1,
      user_id: 1,
      username: "admin",
      action: "login",
      description: "Đăng nhập hệ thống",
      ip_address: "*************",
      created_at: moment().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss')
    },
    {
      id: 2,
      user_id: 2,
      username: "teacher01",
      action: "view_student",
      description: "Xem danh sách học sinh",
      ip_address: "*************",
      created_at: moment().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss')
    },
    {
      id: 3,
      user_id: 3,
      username: "nutritionist",
      action: "update_menu",
      description: "Cập nhật thực đơn",
      ip_address: "*************",
      created_at: moment().subtract(3, 'hours').format('YYYY-MM-DD HH:mm:ss')
    }
  ];
  
  res.json({
    total: mockLogs.length,
    rows: mockLogs
  });
});

module.exports = router;
