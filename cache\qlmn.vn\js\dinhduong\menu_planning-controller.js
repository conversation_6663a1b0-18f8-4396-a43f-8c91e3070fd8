angular_app.controller('menu_planningController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){
    $scope.project = 'dinhduong';
    $scope.control = 'menu_planning';
    $scope.systemConfig = {};
    $scope.systemConfig.module = 'menu_planning';
    $scope.warehouseSelectedOnClick = warehouseSelectedOnClick;
    $scope.onChange_food_name = onChange_food_name;
    /*  Khi kích vào select chọn kho */
    /*  <PERSON>i<PERSON><PERSON> chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/
    function warehouseSelectedOnClick(meal) {
        meal.selected = !meal.selected;
        var check_not_null = true;
        angular.forEach($scope.row.meal_selection, function(item, index){
            if(item.selected){
                check_not_null = false;
            }
        });
        if(check_not_null){
            meal.selected = true;
        }else{
            $scope.totalCalculator();
        }
    };
    /*
    * Sửa tên đi chợ
    * */
    function onChange_food_name(food) {
        for (var f of food.foods) {
            f.name_old = f.name;
            f.name = food.name;
        }
    };
    $scope.allowChangePriceWarehouse = (meal_key, food_id) => {
        var warehouse_id = $scope.getMenu_infomationByMeal_key(meal_key).warehouse_id;
        if(typeof $scope.inventory[warehouse_id] != 'undefined') {
            if(typeof $scope.inventory[warehouse_id][food_id] != 'undefined') {
                return $CFG.province == 40 || $CFG.username=='gvmamnon'?true:false;
            }
        }
        return true;
    };
    $scope.showChangeMeal = (meal, dish_old) => {
        $scope.selected.dish = dish_old;
        $scope.selected.meal = meal;
        $scope.selected.warehouse_id = meal.warehouse_id;
        angular.forEach(dish_old.ingredient, function (food, index) {
            if (!food.quantity) {
                food.quantity = food.quantity_edit;
            }
            food.deleted = false;
        });
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/menu_planning',
            action:'',
            title:'Chuyển bữa cho món ăn',
            size: size.small,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.project + '/menu_planning/change_meal_confirm.html?v_=2',
            onShown: function(element, dialogRef){
                $scope.selected.meal_define_old = meal.define;
                $scope.selected.meal_define = meal.define;
            }
        });
    };
    $scope.acceptChangeMeal = function() {
        if($scope.isChangeMeal && $scope.selected.meal_define != $scope.selected.meal_define_old) {
            delete $scope.selected.meals[$scope.selected.meal.define].dishes[$scope.selected.dish.id];
            var selected_dishes = $scope.selected.dish;
            angular.forEach(selected_dishes.ingredient, (food, food_id)=>{
                food.meal_define = $scope.selected.meal_define;
                selected_dishes.ingredient[food_id] = food;
                if ($scope.selected.meal_define_old=='buasang' && $scope.selected.meal_define!='buasang') {
                    $scope.datagrid.data[2] = $scope.datagrid.data[2] || {};
                    if($scope.datagrid.data[1][food_id]!=undefined && $scope.datagrid.data[2][food_id]==undefined) {
                        $scope.datagrid.data[2][food_id] = $scope.datagrid.data[1][food_id];
                        delete $scope.datagrid.data[1][food_id];
                    }
                }else if($scope.selected.meal_define_old!='buasang' && $scope.selected.meal_define=='buasang') {
                    $scope.datagrid.data[1] = $scope.datagrid.data[1] || {};
                    if($scope.datagrid.data[2][food_id]!=undefined && $scope.datagrid.data[1][food_id]==undefined) {
                        $scope.datagrid.data[1][food_id] = $scope.datagrid.data[2][food_id];
                        delete $scope.datagrid.data[2][food_id];
                    }
                }
            });
            $scope.selected.meals[$scope.selected.meal_define].dishes = Object.assign({},$scope.selected.meals[$scope.selected.meal_define].dishes);
            $scope.selected.meals[$scope.selected.meal_define].dishes[$scope.selected.dish.id] = selected_dishes;
            $scope.selected.meal = $scope.selected.meals[$scope.selected.meal_define];
        }
        dialogClose();
    };
    $scope.changeFoodForm = (food)=>{
        $scope.selected_food = {
            food_old: food
        };
        $.dm_datagrid.showAddForm({
            title:'Chọn thực phẩm',
            size: size.small,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.project + '/' + $scope.control + '/change_food.html?_=',
            onShown: function(element, dialogRef){

            }
        });
    };
    $scope.onSelectedFoodChange = (food)=>{
        if(food) {
            var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
            var data = {async: true,id: food.id, date: $scope.selected.date};
            process(url, data, function(resp) {
                if(!resp) return;
                $scope.selected_food.food_new = resp[0];
                $scope.$apply();
            }, function(){}, false);
        }
    };
    $scope.acceptChangeFood = ()=>{
        if ($scope.selected_food.food_old && $scope.selected_food.food_new){
            var food = $scope.selected_food.food_new;
            var old_food_id = $scope.selected_food.food_old.food_id;
            angular.forEach($scope.datagrid.data, (foods, wh)=>{
                if (!foods[food.food_id] && foods[old_food_id]){
                    var f = foods[old_food_id];
                    f.id = food.id;
                    f.food_id = food.id;
                    f.name = food.name;
                    f.nutritions = food.nutritions;
                    angular.forEach(f.foods, (fd)=>{
                        fd.id = food.id;
                        fd.food_id = food.id;
                        fd.name = food.name;
                        fd.nutritions = food.nutritions;
                    });
                }
                var data = {};
                angular.forEach(foods, (fd)=>{
                    data[fd.food_id] = fd;
                });
                $scope.datagrid.data[wh] = data;
            });
            angular.forEach($scope.menu_planning.meals, (meal)=>{
                angular.forEach(meal.dishes, (dish)=>{
                    var fds = {};
                    angular.forEach(dish.ingredient, (fd, food_id)=>{
                        fds[fd.food_id] = fd;
                    });
                    dish.ingredient = fds;
                });
            });
            $scope.totalCalculator();
            dialogClose();
        }
    };
}]);