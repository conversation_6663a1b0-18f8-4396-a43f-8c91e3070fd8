<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/common.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/main-report.css?_=815296795" />
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
	<script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
	<link rel="stylesheet" href="http://localhost:3000/cando/assets/plugins/sweetalert/sweetalert.css">
	<style type="text/css">
		*{
			font-family: Nunito_Regular;
			font-size: 13 !important;
		}
		@media  print {
			body > .panel {
				display: none !important;
			}
		}
		.btn-hidden {
			display: none !important;
		}
		.read-only {
			pointer-events: none;
		}
	</style>
	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
	<script src="http://localhost:3000/cando/assets/plugins/sweetalert/sweetalert.min.js"></script>
	<script type="text/javascript">
		var allowed_provinces = '35_79';
		var allowed_province_arr = allowed_provinces.split('_');
		$CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'dinhduong',
        	round_number_config: "",
        	digit_grouping_char: "",
        	username: 'd.hd.mnquangvt',
			fullname: $.parseHTML(``)?$.parseHTML(``)[0].nodeValue:'',
			course_id: '',
        	address: ``,
        	name: $.parseHTML(`Trường MN QuangVT`)?$.parseHTML(`Trường MN QuangVT`)[0].nodeValue:'',
        	parentname: $.parseHTML(`PGD Vietec Partner`)?$.parseHTML(`PGD Vietec Partner`)[0].nodeValue:'',
        	spinController: {},
			province: '97',
			district: '97_974',
			administrator: parseInt('0'),
			unit_id: parseInt('51461'),
            cpu_id: parseInt('0'),
            cpu_sync: parseInt('1'),
			level: '4',
			school_level: parseInt('0'),
			is_payment_gate_on: parseInt('0'),
			school_point: +'1',
            school_points: +'1',
            school_point_together: parseInt('0'),
			api_base_url_ura: 'https://ura.edu.vn/api/v1/',
			is_show_plg_rank: parseInt('0'),
			is_show_supplier_sign: parseInt('1'),
			is_sync_csdl_nganh: parseInt('0'),
			allowed_provinces: allowed_province_arr,
			help_base_url: "https://storage.ura.edu.vn"
      	};
      	// var test = $CFG.parentname.split('&ograve;')
	</script>
	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>

	<script src="http://localhost:3000/js/common.js?_=566671267"></script>
	<script type="text/javascript">
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
		angular_app_report = angular_app;
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=376195648749"></script>
	<script src="http://localhost:3000/js/dinhduong/main-angular-report.js?_=512934037"></script>
	<script type="text/javascript" src="http://localhost:3000/js/library.js?v=532755395"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController">
		        <div ng-controller="unitReportController" id="unitReportController" class="report-container" style="padding:5px;">
        <div>
            <span>Chọn thời điểm :</span> <select name="" ng-model="semester" ng-change="loadData()" id="">
                <option value="1">Đầu năm</option>
                <option value="2">Giữa năm</option>
                <option value="3">Cuối năm</option>
            </select>
            <button ng-if="school_level != 4" ng-click="saveForParent()">
                Lưu dữ liệu
            </button>
            <button ng-if="school_level != 4" ng-click="getDataFromUnit()">
                Tổng hợp dữ liệu
            </button>
            <button ng-click="export()">
                Xuất Excel
            </button>
        </div>
        <table class="table" width="100%" style="margin-top: 15px;">
            <tr class="table-tr font-bold text-center">
                <td rowspan="{{ school_level != 4 ? 2:1 }}">Dòng excel</td>
                <td rowspan="{{ school_level != 4 ? 2:1 }}" style="width:5%">TT</td>
                <td rowspan="{{ school_level != 4 ? 2:1 }}" style="width:50%">Nội dung</td>
                <td rowspan="{{ school_level != 4 ? 2:1 }}" style="width:10%">Đơn vị tính</td>
                <td rowspan="{{ school_level != 4 ? 2:1 }}" style="width:10%">Mã số</td>
                <td rowspan="{{ school_level != 4 ? 2:1 }}" style="width:10%">Tổng số</td>
                <td style="width:15%" colspan="2" ng-if="school_level != 4">Trong đó</td>
            </tr>
            <tr class="table-tr font-bold text-center" ng-if="school_level != 4">
                <td style="width:7.5%">Công lập</td>
                <td style="width:7.5%">Tư thục</td>
            </tr>
            <tr class="table-tr italic text-center">
                <td>0</td>
                <td>1</td>
                <td>2</td>
                <td>3</td>
                <td>4</td>
                <td>5</td>
                <td ng-if="school_level != 4">6</td>
                <td ng-if="school_level != 4">7</td>
            </tr>
                <tr ng-repeat="report in default_reports" class="table-tr">
                    <td class="text-center font-bold">{{ report['excel_row'] }}</td>
                    <td class="text-center font-bold">{{ report['rpt_stt_code'] }}</td>
                    <td class="{{ report['is_bold'] == 1 ? 'font-bold' : '' }} ">{{ report['rpt_title'] }}</td>
                    <td class="text-center">{{ report['measure_id'] }}</td>
                    <td></td>
                    <td>
                        <p ng-if=" parseInt(report['is_ratio']) == 0 ">
                            
                            <input ng-if="(school_level == 4 && parseInt(report['rpt_level']) == 4) || (school_level < 4 && parseInt(report['rpt_level']) < 4)" style="width:100%" 
                            ng-keypress="allowOnlyNumbers($event)" ng-blur="changeInput(report, reports[report['excel_row']])" 
                            ng-focus="changeValueOld(reports[report['excel_row']])"  type="text" class="print-hidden" name="" id="" 
                            ng-model="reports[report['excel_row']]">
                            <span ng-if="(school_level == 4 && parseInt(report['rpt_level']) < 4) || (school_level < 4 && parseInt(report['rpt_level']) == 4)" ng-bind="reports[report['excel_row']]"></span>
                        </p>
                        <p ng-if="parseInt(report['is_ratio']) == 1">
                            <span ng-bind="returnFormula(report, 5)"></span>
                        </p>
                    </td>
                    <td ng-if="school_level != 4">
                        <p ng-if=" parseInt(report['is_ratio']) == 0 ">
                            <input ng-if="parseInt(report['rpt_level']) < 4" style="width:100%" 
                            ng-keypress="allowOnlyNumbers($event)" 
                            ng-blur="changeInput(report, school_type_reports[report['excel_row']]['rpt_public'],'rpt_public')" 
                            ng-focus="changeValueOld(school_type_reports[report['excel_row']]['rpt_public'])"  type="text" class="print-hidden" name="" id="" 
                             ng-model="school_type_reports[report['excel_row']]['rpt_public']">
                            <span ng-if="parseInt(report['rpt_level']) == 4" ng-bind="school_type_reports[report['excel_row']]['rpt_public']"></span>
                        </p>
                        <p ng-if="parseInt(report['is_ratio']) == 1">
                            <span ng-bind="returnFormula(report, 6)"></span>
                        </p>
                    </td>
                    <td ng-if="school_level != 4">
                        <p ng-if=" parseInt(report['is_ratio']) == 0 ">
                            <input ng-if="parseInt(report['rpt_level']) < 4" style="width:100%" 
                            ng-keypress="allowOnlyNumbers($event)" 
                            ng-blur="changeInput(report, school_type_reports[report['excel_row']]['rpt_private'],'rpt_private')" 
                            ng-focus="changeValueOld(school_type_reports[report['excel_row']]['rpt_private'])"  type="text" class="print-hidden" name="" id="" 
                            ng-model="school_type_reports[report['excel_row']]['rpt_private']">
                            <span ng-if="parseInt(report['rpt_level']) == 4" ng-bind="school_type_reports[report['excel_row']]['rpt_private']"></span>
                        </p>
                        <p ng-if="parseInt(report['is_ratio']) == 1">
                            <span ng-bind="returnFormula(report, 7)"></span>
                        </p>
                    </td>
                </tr>
        </table>
    </div>
    <script src="http://localhost:3000/js/dinhduong/unit_report.js?_=325888139"></script>
    <style type="text/css"> 
        .table-tr td {
            vertical-align: middle !important; 
            border: .5pt solid black !important;
        }
        .italic {
            font-style: italic;
        }
        .font-bold {
            font-weight: 700;
        }
        input.print-hidden, .print-hidden input[type="text"]{
            border: 1px dotted !important;
            color: #000;
        }
            </style>
	</div>
</body>
</html>
