<div ng-controller="StudentVaccinationDashboardController as vm" class="position-relative w-100 h-100 mh-100">
    <div class="row">
        <div class="col-md-12 d-flex align-items-center">
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-options="year.id as year.title for year in vm.years"
                        ng-model="vm.filters.school_year"
                        ng-change="vm.onSchoolYearChange()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-options="month.id as month.title for month in vm.months"
                        ng-model="vm.filters.month"
                        ng-change="vm.onMonthChange()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-change="vm.onCourseChange()"
                        ng-model="vm.filters.course_id">
                    <option ng-repeat="grade in vm.courses"
                            ng-value="grade.id" ng-if="!grade.courses">
                        {{grade.name}}
                    </option>
                    <optgroup ng-repeat="grade in vm.courses" label="{{grade.name}}" ng-if="grade.courses">
                        <option ng-repeat="course in grade.courses.data" ng-value="course.id">{{course.name}}</option>
                    </optgroup>
                </select>
            </div>
            <button class="btn btn-secondary btn-sm"
                    ng-click="vm.reload()"
                    ng-if="vm.students">
                <i class="btn-icon btn-reload mdi mdi-reload"></i>
                Tải lại dữ liệu
            </button>
			<a class="has-arrow waves-effect waves-dark ml-auto" href="cando/view/student_temperature/dashboard">
				<i class="mdi mdi-temperature-celsius" style="color:orange;"></i>  Nhiệt độ của học sinh
			</a>
        </div>
    </div>

    <div id="student-health" class="h-100 table-responsive mt-2 table-bmi table-bmi-dashboard table-student-health" style="position: relative;">
        <form name="vm.studentBmiForm">
            <table id="student-health-table" class="jsgrid-table table table-striped table-hover">
                <thead style="position: absolute;z-index: 1;">
                <tr style="position: relative;height: 56px">
                    <th class="table-bmi-dashboard--stt">
                        <div class="absolute th-fixed">STT</div>
                    </th>
                    <th class="table-bmi-dashboard--detail"><div class="absolute tr2-cell-fixed"></div></th>
                    <th class="table-bmi-dashboard--full-name">
                        <div class="absolute th-fixed" style="padding-top: 14px">
                            <div class="div_search">
                                <input type="text" class="form-control"
                                       placeholder="Họ tên"
                                       ng-model-options="{ debounce: 1000 }"
                                       ng-model="vm.filters.keyword"
                                       ng-change="vm.onKeywordChange()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </th>
                    <th class="table-bmi-dashboard--student-id">
                        <div class="absolute th-fixed">Mã HS</div>
                    </th>
                    <th class="table-bmi-dashboard--birthday">Ngày sinh</th>
                    <th class="table-student-health--gender">Giới Tính</th>
                    <th class="table-student-health--disease" ng-repeat="vaccination in vm.vaccinations">{{ vaccination.vaccination_name }}</th>
                </tr>
                </thead>
                <tbody style="position: absolute;z-index: 0;margin-top: 56px">
                <tr ng-repeat="student in vm.students" style="position: relative;">
                    <td class="table-bmi-dashboard--stt text-center">
                        <div class="absolute td-fixed">
                            {{(vm.paginate.page - 1) * vm.paginate.limit + $index + 1}}
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--detail text-center">
                        <div class="absolute td-fixed" style="margin-top: -36px; padding-top: 1.5%;">
                            <a href="javascript:;" ng-click="vm.onViewModal(student)" data-toggle="modal"
                            data-target="#myModal">
                                <i class="mdi mdi-eye"></i>
                            </a>
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--full-name">
                        <div class="absolute td-fixed">
                            {{ student.last_name }} {{ student.first_name }}
                        </div>
                    </td>
                    <th class="table-bmi-dashboard--student-id text-center">
                        <div class="absolute td-fixed">{{student.id}}</div>
                    </th>
                    <th class="table-bmi-dashboard--birthday">
                        {{ student.birthday | date: 'dd/MM/yyyy'}}
                    </th>
                    <td class="table-student-health--gender text-center">
                        <span ng-if="student.gender === 1">Nam</span>
                        <span ng-if="student.gender !== 1">Nữ</span>
                    </td>
                    <td style="padding: 5px;" ng-repeat="vaccination in student.vaccinations" class="table-student-health--disease text-center">
                        <input type="checkbox"
                               ng-model="vaccination.value"
                               ng-change="vm.onVaccinationOfStudentChange(student, vaccination)"
                               ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock">
                        <textarea style="margin-top: 5px" class="form-control" type="text"
                        ng-model="vaccination.note" 
                        ng-blur="student['old_'+vaccination.id] != vaccination.note ? vm.onVaccinationOfStudentChange(student, vaccination) : ''"
                        ng-focus = "student['old_'+vaccination.id] = vaccination.note"
                        ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock ">
                        </textarea>
                    </td>
                </tr>
                </tbody>
            </table>
        </form>
    </div>

    <div class="d-flex justify-content-between mt-2">
        <div class="w-50 d-flex">
            <select class="form-control form-control-sm w-auto mr-2"
                    ng-model="vm.paginate.limit"
                    ng-change="vm.onPageLimitChange()"
                    ng-options="item as item for item in vm.paginate.perPages">
            </select>
            <ul uib-pagination
                class="pagination-sm"
                ng-if="vm.paginate.total > vm.paginate.limit"
                max-size="vm.paginate.maxSize"
                total-items="vm.paginate.total"
                items-per-page="vm.paginate.limit"
                ng-model="vm.paginate.page"
                ng-change="vm.onPageChange()"
                boundary-link-numbers="true"
                rotate="false"
                previous-text="&lsaquo;"
                next-text="&rsaquo;"
            ></ul>
        </div>
    </div>
    <div id="myModal" class="modal fade myModal" role="dialog">
        <div class="modal-dialog" style="max-width: 1300px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close print-hidden" data-dismiss="modal">&times;</button>
                    <div>
                        <h3>THEO DÕI TIÊM CHỦNG</h3></div>
                        <div class="row">
                            <div class="col-lg-3 col-md-3">
                                <p>Tên: <span class="tl_name">{{ student.last_name }} {{ student.first_name }}</span></p>
                            </div>
                            <div class="col-lg-3 col-md-3">
                                <p>Ngày sinh: <span class="tl_name">{{ student.birthday | date: 'dd/MM/yyyy'}}</span></p>
                            </div>
                            <div class="col-lg-3 col-md-3">
                                <p>Lớp: <span class="tl_name">{{ vm.getCourseName(student.course_id) }}</span></p>
                            </div>
                            <div class="col-lg-2 col-md-2">
                                <p>Năm học: <span class="tl_name">{{vm.filters.school_year}} - {{vm.filters.school_year+1}}</span></p>
                            </div>
                        </div>
                    </div>
                <div class="modal-body">
                    <div class="content_danhgia">
                        <div class="card">
                            <div class="card-body">
                                <div id="exampleSorting" class="jsgrid table_cando table_chitiet" style="position: relative; width: 100%;">

                                    <div class="">
                                        <table class="table color-table table-bordered text-center">
                                            <thead>
                                                <tr style="background-color: #bce2f4">
                                                    <th rowspan="2">STT</th>
                                                    <th rowspan="2">Loại vacxin</th>
                                                    <th rowspan="2">Thời gian</th>
                                                    <th colspan="2">Tình trạng tiêm / uống vacxin</th>
                                                    <th rowspan="2">Ghi chú</th>
                                                </tr>
                                                <tr style="background-color: #bce2f4">
                                                    <th>Có</th>
                                                    <th>Không</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <tr ng-repeat-start="(key, vaccination) in histories"></tr>
                                                <tr>
                                                    <td class="stt" rowspan="{{ vaccination.histories.length > 0 ? vaccination.histories.length + 1 : 1 }}">{{ key+1 }}</td>
                                                    <td class="text-left"><b>{{ vaccination.vaccination_name }}</b></td>
                                                    <td colspan="4"></td>
                                                </tr>
                                                <tr ng-repeat = "(index, item) in vaccination.histories">
                                                    <td class="text-left"> Mũi {{ index + 1 }} </td>
                                                    <td> Tháng {{ item.month }}/ {{ (item.month < 9 ? item.school_year + 1 : item.school_year ) }} </td>
                                                    <td>{{ item.value == 1 ? 'x' : '' }}</td>
                                                    <td>{{ item.value == 0 ? 'x' : '' }}</td>
                                                    <td> {{ item.note }} </td>
                                                </tr>
                                            <tr ng-repeat-end></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="jsgrid-pager-container" style="display: none;"></div>
                                    <div class="jsgrid-load-shader" style="display: none; position: absolute; inset: 0px; z-index: 1000;"></div>
                                    <div class="jsgrid-load-panel" style="display: none; position: absolute; top: 50%; left: 50%; z-index: 1000;">
                                        Please, wait...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $('#student-health').scroll(function() {
        var table = $('#student-health-table');
        
        var top = table.position().top;
        table.find('thead').css({top: top * -1});

        var left = table.position().left * -1-4;
        $('.table-bmi-dashboard--stt .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--detail .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--full-name .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--student-id .absolute').css({'margin-left': left});
    })
</script>

<style>
    
    .cando_selects:not(:first-child) {
        margin-top : 20px;
    }
    b {
        font-weight: bold !important;
    }
    .stt {
        vertical-align: inherit !important;
    }
</style>