<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" ng-controller="appController">
<head>
    <base href="http://localhost:3000/single/">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- Tell the browser to be responsive to screen width -->
    <meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
		<title>PMS - C<PERSON> đo học sinh</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
    <!-- Fonts -->
    <link href="../cando/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/bootstrap-dialog.min.css" rel="stylesheet">
    <!-- chartist CSS -->
    <link href="../cando/assets/plugins/chartist-js/dist/chartist.min.css" rel="stylesheet">
    <link href="../cando/assets/plugins/chartist-js/dist/chartist-init.css" rel="stylesheet">
    <link href="../cando/assets/plugins/chartist-plugin-tooltip-master/dist/chartist-plugin-tooltip.css" rel="stylesheet">
    <!--This page css - Morris CSS -->
    <link href="../cando/assets/plugins/c3-master/c3.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../cando/css/style.css" rel="stylesheet">
    <link href="../cando/css/style2.css?v=31105195" rel="stylesheet">
    <!-- You can change the theme colors from here -->
    <link href="../cando/css/colors/blue.css" id="theme" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
    <link href="../css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="http://localhost:3000/cando/assets/plugins/sweetalert/sweetalert.css">
    <link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />

    <script type="">
        var allowed_provinces = '35_79';
		var allowed_province_arr = allowed_provinces.split('_');
        $CFG = {
            remote: {base_url: window.location.origin},
            project: 'cando',
			is_gokids: '',
            unit_id: '51461',
			username: 'd.hd.mnquangvt',
			user_id: '190299',
			level: '4',
            province: '97',
			district: '97_974',
			base_http_url: 'http://localhost:3000',
            is_principal: parseInt('1'),
            administrator: parseInt('0'),
            metaData: {
                ages: [],
                weights: [],
                heights: [],
                labels: []
            },
			'active_year': 2024,
            grades: [],
			'no_bmi_sdd_weight_rule': 0,
            'no_bmi_under60_month_rule': 0,
            'no_is_not_weight_height': 0,
            'get_weight_height_not_normal': 0,
            'show_modal_report_multi_month': 0,
            'hidden_student_off_month': 0,
            'cando_dshs_allow_hack_bmi': 0,
            is_sync_csdl_nganh: parseInt('0'),
			allowed_provinces: allowed_province_arr,
        }
    </script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery.min.js"></script>


    <script type="text/javascript" src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/load-image.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/canvas-to-blob.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.1.6.4/angular-sanitize.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular/ngStorage.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/textAngular.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ui-bootstrap4@3.0.4/dist/ui-bootstrap.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ui-bootstrap4@3.0.4/dist/ui-bootstrap-tpls.js"></script>
    <script src="https://morgul.github.io/ui-bootstrap4/ui-bootstrap-tpls-3.0.6.js"></script>
</head>
<body class="fix-header fix-sidebar card-no-border mini-sidebar"
      ng-controller="mainContentController" id="mainContentController">
    <div class="main-wrapper">
    <header class="topbar">
        <nav class="navbar top-navbar navbar-expand-md navbar-light">
            <!-- ============================================================== -->
            <!-- Logo -->
            <!-- ============================================================== -->
            <div class="navbar-header">
                <a class="navbar-brand" href="candohocsinh.html">
                    <!-- Logo icon --><b>
                        <!--You can put here icon as well // <i class="wi wi-sunset"></i> //-->
                        <!-- Dark Logo icon -->
                        <img src="../cando/assets/images/images_cando/icon_logo.png" alt="homepage" class="dark-logo">
                        <!-- Light Logo icon -->
                        <img src="../cando/assets/images/images_cando/icon_logo.png" alt="homepage" class="light-logo">
                    </b>
                    <!--End Logo icon -->
                    <!-- Logo text -->
                    <span class="mini-hide">
                         <!-- dark Logo text -->
                         <img src="../cando/assets/images/images_cando/text_logo.png" alt="homepage" class="dark-logo">
                        <!-- Light Logo text -->
                         <img src="../cando/assets/images/images_cando/text_logo.png" class="light-logo" alt="homepage">
                    </span>
                </a>
            </div>
            <!-- ============================================================== -->
            <!-- End Logo -->
            <!-- ============================================================== -->
            <div class="navbar-collapse">
                <!-- ============================================================== -->
                <!-- toggle and nav items -->
                <!-- ============================================================== -->
                <ul class="navbar-nav mr-auto mt-md-0">
                    <!-- This is  -->
                    <li class="nav-item"> <a class="nav-link nav-toggler hidden-md-up text-muted waves-effect waves-dark" href="javascript:void(0)"><i class="mdi mdi-menu"></i></a> </li>
                    <li class="nav-item">
                        <a class="nav-link sidebartoggler hidden-sm-down text-muted waves-effect waves-dark" ng-click="setDashboard('mini_sidebar', !dashboard.mini_sidebar)" href="javascript:void(0)">
                            <i class="ti-menu"></i>
                        </a>
                    </li>
                    <!-- ============================================================== -->
                    <!-- Search -->
                    <!-- ============================================================== -->
                    <li class="icon_school">
                        <img src="../cando/assets/images/images_cando/school.png">
                    </li>
                                        <!-- ============================================================== -->
                    <!-- Messages -->
                    <!-- ============================================================== -->
                    <li class="name_school">
                        <p>Trường MN QuangVT</p>
                    </li>
                    <!-- ============================================================== -->
                    <!-- End Messages -->
                    <!-- ============================================================== -->
                    
                                        <style type="text/css">
                        .light-blue.phonggd {
                            display: inherit;
                            align-items: center;
                        }
                        .sophong-container {
                            border: 1px solid #ccc;
                            border-radius: 19px;
                            padding: 3px 4px;
                            background: #50a7c1;
                        }

                        .sophong-container > span.light-blue > span.textbox.combo {
                            border-radius: 17px;
                        }

                        .in-main-content.in-main {
                            margin-top: 15% !important;
                        }
                        .textbox {
                            position: relative;
                            border: 1px solid #ddd;
                            background-color: #fff;
                            vertical-align: middle;
                            display: inline-block;
                            overflow: hidden;
                            white-space: nowrap;
                            margin: 0;
                            padding: 0;
                            -moz-border-radius: 0px 0px 0px 0px;
                            -webkit-border-radius: 0px 0px 0px 0px;
                            border-radius: 0px 0px 0px 0px;
                        }
                        .textbox-addon {
                            position: absolute;
                            top: 0;
                        }
                        .textbox .textbox-prompt {
                            font-size: 12px;
                            color: #aaa;
                        }
                        .textbox .textbox-text {
                            font-size: 12px;
                            border: 0;
                            margin: 0;
                            padding: 4px;
                            white-space: normal;
                            vertical-align: top;
                            outline-style: none;
                            resize: none;
                            -moz-border-radius: 0px 0px 0px 0px;
                            -webkit-border-radius: 0px 0px 0px 0px;
                            border-radius: 0px 0px 0px 0px;
                        }
                        .combo-arrow {
                            background: url(/build/build/css/images/combo_arrow.png) no-repeat center center;
                        }
                        .combo-arrow {
                            width: 18px;
                            height: 20px;
                            overflow: hidden;
                            display: inline-block;
                            vertical-align: top;
                            cursor: pointer;
                            opacity: 0.6;
                            filter: alpha(opacity=60);
                        }

                        .textbox-icon {
                            display: inline-block;
                            width: 18px;
                            height: 20px;
                            overflow: hidden;
                            vertical-align: top;
                            background-position: center center;
                            cursor: pointer;
                            opacity: 0.6;
                            filter: alpha(opacity=60);
                            text-decoration: none;
                            outline-style: none;
                        }
                        .panel {
                            overflow: hidden;
                            text-align: left;
                            margin: 0;
                            margin-bottom: 20px;
                            background-color: #fff;
                            border: 1px solid transparent;
                            border-radius: 4px;
                            -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
                            box-shadow: 0 1px 1px rgba(0,0,0,.05);
                        }
                        .panel-body {
                            background: rgba(0,0,0,0);
                            padding: 3px;
                            color: #444;
                            font-size: 12px;
                            border: 1px solid #ddd;
                            overflow: auto;
                        }
                        .panel-body > .combobox-item {
                            cursor: pointer;
                            margin-top: 1px;
                            padding: 3px;
                        }
                        .combobox-item-hover {
                          background-color: #E6E6E6;
                          color: #444;
                        }
                        .combobox-item-selected {
                            background-color: #CCE6FF;
                            color: #000;
                        }
                        .spinner-container {
                            position: relative;
                            float: left;
                            width: 30px;
                            height: 30px;
                            margin-top: 0px;
                            padding: 0px;
                            border: none;
                            border-radius: 5px;
                        }
                        .spinner-container {
                            min-width: 24px;
                            min-height: 24px;
                        }

                        .spinner-container:before {
                            content: 'Loading…';
                            position: absolute;
                            top: 3px;
                            left: 3px;
                            width: 24px;
                            height: 24px;
                        }
                        .spinner-container:not(:required):before {
                            content: '';
                            border-radius: 50%;
                            border-top: 2px solid #03ade0;
                            border-right: 2px solid transparent;
                            animation: spinner .6s linear infinite;
                            -webkit-animation: spinner .6s linear infinite;
                        }
                        @keyframes  spinner {
                            from {transform:rotate(0deg);}
                            to {transform:rotate(360deg);}
                        }
                        .textbox-addon span {
                            display: none;
                        }

                    </style>
                </ul>
                <!-- ============================================================== -->
                <!-- User profile and search -->
                <!-- ============================================================== -->
                <ul class="navbar-nav my-lg-0">
					<li class="light-blue">
						<div id="div_ura_notification" style="margin-top:  4px; "></div>
					</li>
                    						<li class="nav-item dropdown">
							<a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                <img title="Hướng dẫn" src="http://localhost:3000/css/dinhduong/images/support.gif" style="margin-top:10px; width:20px;">
                            </a>
							<ul class="dropdown-menu dropdown-menu-right mailbox scale-up">
															<li>
									<a target="_blank" style="padding-left:10px;"
									   href="http://localhost:3000/cando/assets/files/HDSD-CANDO.pdf?t=99">
									   Hướng dẫn sử dụng Cân Đo
									</a>
								</li>
														</ul>
						</li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-muted text-muted waves-effect waves-dark" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-cubes"></i>
                                <b>Phân hệ</b>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-right mailbox scale-up">
                                                                                                                                                    <li class="item-home">
                                            <a href="/single/cando" target="_blank">
                                                <img width="18px" height="18px" class="swivel-img" src="http://localhost:3000/images/item_cando.png">
                                                C&acirc;n đo
                                            </a>
                                        </li>
                                                                                                                                                                                                                                                                            <li class="item-home">
                                            <a href="http://localhost:3000/dinhduong" target="_blank">
                                                <img width="18px" height="18px" class="swivel-img" src="http://localhost:3000/images/item_ctbt.png">
                                                C&ocirc;ng t&aacute;c b&aacute;n tr&uacute;
                                            </a>
                                        </li>
                                                                                                                                                                                                                                        </ul>
                        </li>
                                        <!-- ============================================================== -->
                    <!-- ============================================================== -->
                    <!-- Profile -->
                    <!-- ============================================================== -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-muted waves-effect waves-dark" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img src="../cando/assets/images/users/avatar.png" alt="user" class="profile-pic" />
                        </a>
                        <div class="dropdown-menu dropdown-menu-right scale-up">
                            <ul class="dropdown-user">
                                <li><a href="#"><i class="ti-user"></i> Tài khoản: {{sys.user.username}}</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="#" onclick="loadForm($CFG.remote.base_url + '/logout','',{async: true}, () => {location.reload()})"><i class="fa fa-power-off"></i> Đăng xuất</a></li>

                                <li class="divider"></li>
                                <li>
                                    <a href="#version" tabindex="-1">
                                    <i class="ace-icon fa fa-info-circle" style="color: #000000;"></i>
                                    Phiên bản 2535ad23.70
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <!-- ============================================================== -->
                </ul>
            </div>
        </nav>
    </header>
    <aside class="left-sidebar" >
        <!-- Sidebar scroll-->
        <div class="scroll-sidebar" style="overflow-x: visible;">
            <!-- User profile -->
            <div class="user-profile bg-image" style="background: url(../cando/assets/images/images_cando/bg_cando.png) no-repeat;">
                <div class="namhoc"> Năm học 2024 - 2025
                </div>
            </div>
            <!-- End User profile text-->
            <!-- Sidebar navigation-->
            <nav class="sidebar-nav active">
                <ul id="sidebarnav">
						<li>
							<a class="has-arrow waves-effect waves-dark" href="cando/view/student_bmi/dashboard" aria-expanded="false">
								<i class="mdi mdi-gauge"></i><span class="hide-menu">Cân đo học sinh</span>
							</a>
						</li>
						<li>
							<a class="has-arrow waves-effect waves-dark" href="cando/view/student_health/dashboard">
								<i class="mdi mdi-gauge"></i><span class="hide-menu">Sức khỏe học sinh</span>
							</a>
						</li>
                                                    <li>
                                <a class="has-arrow waves-effect waves-dark" href="cando/view/student_vaccination/dashboard">
                                    <i class="mdi mdi-needle"></i><span class="hide-menu">Tiêm chủng </span>
                                </a>
                            </li>
                        					                        <li>
                            <a class="has-arrow waves-effect waves-dark" href="cando/view/settings/disease">
                                <i class="mdi mdi-medical-bag"></i>
                                <span class="hide-menu">Quản lý nhóm bệnh</span>
                            </a>
                        </li>
                    					
						<li class="nav-devider"></li>
						<li class="nav-small-cap">BÁO CÁO</li>
						<li >
							<a class="has-arrow waves-effect waves-dark" href="cando/view/reports/bmi_for_weight" aria-expanded="false"><i class="mdi mdi-weight"></i><span class="hide-menu">Kết quả cân nặng</span></a>
						</li>
						<li>
							<a class="has-arrow waves-effect waves-dark" href="cando/view/reports/bmi_for_height" aria-expanded="false"><i class="mdi mdi-ruler"></i><span class="hide-menu">Kết quả chiều cao</span></a>
						</li>
						<li >
							<a class="has-arrow waves-effect waves-dark" href="cando/view/reports/weight_height_yearly" aria-expanded="false"><i class="mdi mdi-widgets"></i><span class="hide-menu">Kết quả cân đo cả năm</span></a>
						</li>
													<!--li>
								<a class="has-arrow waves-effect waves-dark" href="cando/view/reports/weight_by_grade" aria-expanded="false"><i class="mdi mdi-chart-areaspline"></i><span class="hide-menu">Thể lực trẻ theo khối</span></a>
							</li>
							<li>
								<a class="has-arrow waves-effect waves-dark" href="cando/view/reports/weight_by_course" aria-expanded="false"><i class="mdi mdi-chart-areaspline"></i><span class="hide-menu">Thể lực trẻ theo lớp</span></a>
							</li-->
											
                </ul>
            </nav>
            <!-- End Sidebar navigation -->
        </div>
        <!-- End Sidebar scroll-->
        <!-- Bottom points-->
        <div class="sidebar-footer">
        </div>
            <!-- End Bottom points-->
    </aside>
    <div class="page-wrapper">
        <!-- ============================================================== -->
        <!-- Container fluid  -->
        <!-- ============================================================== -->
        <div class="container-fluid">
            <!-- ============================================================== -->
            <!-- Bread crumb and right sidebar toggle -->
            <!-- ============================================================== -->
            <div class="row page-titles">
                <div class="col-md-5 col-8 align-self-center">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0)">PMS</a></li>
                        <li class="breadcrumb-item"><a href="javascript:void(0)">Cân đo</a></li>
                        <li class="breadcrumb-item active">{{sys.module.title}}</li>
                    </ol>
                </div>
                <div class="col-md-7 col-4 align-self-center">
                </div>
            </div>
            <!-- ============================================================== -->
            <!-- End Bread crumb and right sidebar toggle -->
            <!-- Start Page Content -->
            <!-- ============================================================== -->
            <div class="main-content-view">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body angular-view-container" ng-view="">
                            Nội dung hiển thị
                        </div>
                    </div>
                </div>
            </div>
            <!-- Row -->
        </div>
    </div>
    <div class="fix-main">
        <div class="header">
        </div>
        <div class="content" style="">
            <div class="main-content">
                <div class="angular-view-container" ng-view="">
                    <div style="width: 270px; margin: auto;margin-top: 250px; color: orange; font-size: 30px;">
                        Đang xử lý 23456...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .hidden-item-home{
        opacity: 0.5;
    }
    .item-home{
        padding: 3px 15px;
    }
</style>
<!-- Hiện báo đang xử lý -->
<style>
    #app-loading-mash{
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: #37474f;
        z-index: 999999;
    }
    #app-loading-mash > .app-loading{
        width: 100%;
        height: 100%;
        z-index: 9998;
    }
    #app-loading-mash > .app-loading > .loading-content{
        position: absolute;
        width: 1px;
        height: 1px;
        top: 45%;
        left: 50%;
    }
    /* KEYFRAMES */
    @keyframes  spin {
        from {
            transform: rotate(0);
        }
        to{
            transform: rotate(359deg);
        }
    }

    .spinner-box {
        width: 200px;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: transparent;
        position: absolute;
        top: -100px;
        left: -100px;
    }
    #statusloading-mash {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(1, 20, 30, 0.3);
        z-index: 99999;
    }
    /* ALTERNATING ORBITS */

    .circle-border {
        width: 100px;
        height: 100px;
        padding: 3px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: rgb(63,249,220);
        background: linear-gradient(0deg, rgba(63,249,220,0.1) 33%, rgba(63,249,220,1) 100%);
        animation: spin .8s linear 0s infinite;
    }

    .circle-core {
        width: 100%;
        height: 100%;
        background-color: #37474f;
        border-radius: 50%;
    }
    .logo-content{
        width: 60px;
        height: 60px;
        padding: 3px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url("../cando/assets/images/images_cando/icon_logo.png");
        background-size: 74px;
        position: absolute;
    }
    /* loading spinner of module process */
    .lds-ellipsis {
        display: inline-block;
        position: relative;
        width: 64px;
        height: 64px;
        margin-top: -50px;
    }
    .lds-ellipsis div {
        position: absolute;
        top: 27px;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        background: #000761;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
        opacity: 0.7;
    }
    .lds-ellipsis div:nth-child(1) {
        left: 6px;
        animation: lds-ellipsis1 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(2) {
        left: 6px;
        animation: lds-ellipsis2 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(3) {
        left: 26px;
        animation: lds-ellipsis2 0.6s infinite;
    }
    .lds-ellipsis div:nth-child(4) {
        left: 45px;
        animation: lds-ellipsis3 0.6s infinite;
    }
    @keyframes  lds-ellipsis1 {
        0% {
            transform: scale(0);
        }
        100% {
            transform: scale(1);
        }
    }
    @keyframes  lds-ellipsis3 {
        0% {
            transform: scale(1);
        }
        100% {
            transform: scale(0);
        }
    }
    @keyframes  lds-ellipsis2 {
        0% {
            transform: translate(0, 0);
        }
        100% {
            transform: translate(19px, 0);
        }
    }
</style>
<div id="statusloading-mash">
    <div class="lds-ellipsis"><div></div><div></div><div></div><div></div></div>
</div>
<div id="app-loading-mash" style="display: unset;">
    <div class="app-loading">
        <div class="loading-content">
            <div class="spinner-box">
                <div class="circle-border">
                    <div class="circle-core"></div>
                </div>
                <div class="logo-content"></div>
            </div>
        </div>
    </div>
</div>
<!-- Bootstrap tether Core JavaScript -->
<script src="../cando/assets/plugins/popper/popper.min.js"></script>
<script src="../cando/assets/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>

<!-- slimscrollbar scrollbar JavaScript -->
<script src="../cando/js/jquery.slimscroll.js"></script>
<!--Wave Effects -->
<script src="../cando/js/waves.js"></script>
<!--Menu sidebar -->
<script src="../cando/js/sidebarmenu.js"></script>
<script src="../cando/js/left_menu.js"></script>
<!--stickey kit -->
<script src="../cando/assets/plugins/sticky-kit-master/dist/sticky-kit.min.js"></script>
<script src="../cando/assets/plugins/sparkline/jquery.sparkline.min.js"></script>
<!--Custom JavaScript -->
<script src="../cando/js/custom.min.js"></script>
<!-- ============================================================== -->
<!-- This page plugins -->
<!-- ============================================================== -->
<!-- chartist chart -->
<script src="../cando/assets/plugins/chartist-js/dist/chartist.min.js"></script>
<script src="../cando/assets/plugins/chartist-plugin-tooltip-master/dist/chartist-plugin-tooltip.min.js"></script>
<!--c3 JavaScript -->
<script src="../cando/assets/plugins/d3/d3.min.js"></script>
<script src="../cando/assets/plugins/c3-master/c3.min.js"></script>
<script src="http://localhost:3000/cando/assets/plugins/sweetalert/sweetalert.min.js"></script>
<!-- Chart JS -->
<!-- ============================================================== -->
<!-- Style switcher -->
<!-- ============================================================== -->
<script src="../cando/assets/plugins/styleswitcher/jQuery.style.switcher.js"></script>
    <script type="text/javascript" src="../js/common.js?=1734041808"></script>
    <script type="text/javascript" src="../js/lodash.min.js"></script>
    <script type="text/javascript" src="../cando/js/init-angular.js"></script>
    <script type="text/javascript" src="../cando/js/my-angular.js"></script>
    <script type="text/javascript" src="../cando/js/main-angular.js"></script>

    <script type="text/javascript" src="../cando/js/models/student.model.js"></script>

    <script type="text/javascript" src="../cando/js/app.constant.js?=1834835125"></script>
    <script type="text/javascript" src="../cando/js/directives/only_number_input.directive.js"></script>
    <script type="text/javascript" src="../cando/js/services/auth.service.js?=988873458"></script>
    <script type="text/javascript" src="../cando/js/services/common.service.js?=1202171034"></script>
    <script type="text/javascript" src="../cando/js/services/course.service.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/services/student_bmi.service.js?=774365179"></script>
    <script type="text/javascript" src="../cando/js/services/student.service.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/services/bmi.service.js?=291152402"></script>
    <script type="text/javascript" src="../cando/js/services/disease.service.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/services/student-medical-examination.service.js?=797487493"></script>
    <script type="text/javascript" src="../cando/js/services/student-vaccination.service.js?=139099510"></script>


    <script type="text/javascript" src="../cando/js/controllers/dashboard.controller.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/controllers/reports/bmi_for_weight.controller.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/controllers/reports/bmi_for_height.controller.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/controllers/reports/weight_height_yearly_history.controller.js?=457146834"></script>
    <script type="text/javascript" src="../cando/js/controllers/reports/weight_by_grade.controller.js?=833209093"></script>
    <script type="text/javascript" src="../cando/js/controllers/reports/weight_by_course.controller.js?=**********"></script>

    <script type="text/javascript" src="../cando/js/student_bmi/studentBmiServices.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/student_bmi/detailController.js?=**********"></script>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/modules/export-data.js"></script>

    <script type="text/javascript" src="../cando/js/controllers/health/dashboard.controller.js?=93133402"></script>
    <script type="text/javascript" src="../cando/js/controllers/settings/disease.controller.js?=**********"></script>
    <script type="text/javascript" src="../cando/js/controllers/temperature/dashboard.controller.js?=256740372"></script>
    <script type="text/javascript" src="../cando/js/controllers/vaccination/dashboard.controller.js?=**********"></script>
	<script type="text/javascript" src="http://localhost:3000/cando/js/notification.js?**********"></script>

    <script type="text/javascript">
        $('.hidden-item-home').click(function (event) {
            event.preventDefault();
        });
    </script>
</body>
</html>
