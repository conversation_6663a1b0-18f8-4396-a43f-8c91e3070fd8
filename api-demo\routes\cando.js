const express = require('express');
const router = express.Router();
const moment = require('moment');

// Mock data cho cân đo
const mockDiseases = [
  {
    id: 1,
    name: "<PERSON><PERSON> khoa",
    code: "NHI",
    unit_id: 2539,
    origin_id: 0,
    created_at: "2020-04-17 19:42:24",
    updated_at: "2020-04-17 19:42:24",
    deleted_at: null,
    grade_ids: null,
    default_note: "a,Tuần hoàn:\r\nb, <PERSON><PERSON> Hấp:\r\nc, Tiêu hóa:\r\nd, Thận - Tiết niệu:\r\nđ, Thần kinh - Tâm thần:\r\ne, Khám lâm sàng khác:"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    code: "MAT",
    unit_id: 2539,
    origin_id: 0,
    created_at: "2020-04-17 19:43:05",
    updated_at: "2020-04-17 19:43:05",
    deleted_at: null,
    grade_ids: null,
    default_note: "a, <PERSON><PERSON><PERSON> qu<PERSON> khám thị lực :\r\n- Không kính:\r\nMắt phải :\r\nMắt trái :\r\n- Có kính :\r\nMắt phải :\r\nMắt trái :\r\nb, Bệnh (nếu có) :"
  },
  {
    id: 3,
    name: "Tai mũi họng",
    code: "TMH",
    unit_id: 2539,
    origin_id: 0,
    created_at: "2020-04-17 19:55:29",
    updated_at: "2020-04-17 19:55:29",
    deleted_at: null,
    grade_ids: null,
    default_note: "a, Kết quả khám thính lực:\r\n- Tai trái :\r\nNói thường :\r\nNói thầm :\r\n- Tai phải :\r\nNói thường :\r\nNói thầm :\r\nb, Bệnh (nếu có) :"
  },
  {
    id: 4,
    name: "Răng hàm mặt",
    code: "RHM",
    unit_id: 2539,
    origin_id: 0,
    created_at: "2020-04-17 20:01:48",
    updated_at: "2020-04-17 20:01:48",
    deleted_at: null,
    grade_ids: null,
    default_note: "a, Kết quả khám :\r\n- Hàm trên :\r\n- Hàm dưới :\r\nb, Bệnh (nếu có) :"
  },
  {
    id: 5,
    name: "Cơ xương khớp",
    code: "CXK",
    unit_id: 2539,
    origin_id: 0,
    created_at: "2020-04-17 20:02:05",
    updated_at: "2020-04-17 20:02:05",
    deleted_at: null,
    grade_ids: null,
    default_note: "a, Kết quả khám :\r\n- Bình thường :\r\n- Cong cột sống :\r\n- Vẹo cột sống :\r\nb, Bệnh (nếu có) :"
  }
];

const mockDefaultDiseases = [
  { id: 8, name: "Cảm cúm", code: "CC", grade_ids: null },
  { id: 9, name: "Cận thị", code: "CT", grade_ids: null },
  { id: 10, name: "Cận thị bẩm sinh", code: "CTBS", grade_ids: null },
  { id: 14, name: "Hen phế quản", code: "HPQ", grade_ids: null },
  { id: 15, name: "Ho", code: "HO", grade_ids: null },
  { id: 20, name: "Sâu răng", code: "SR", grade_ids: null },
  { id: 21, name: "Sốt", code: "SOT", grade_ids: null },
  { id: 24, name: "Suy dinh dưỡng", code: "SDD", grade_ids: null }
];

const mockStudents = [
  {
    id: 1,
    student_code: "HS001",
    full_name: "Nguyễn Văn A",
    birth_date: "2020-05-15",
    gender: "Nam",
    grade_id: 1,
    grade_name: "Nhóm Chồi",
    parent_name: "Nguyễn Thị B",
    parent_phone: "0123456789",
    address: "123 Đường ABC, Quận 1, TP.HCM",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 2,
    student_code: "HS002",
    full_name: "Trần Thị C",
    birth_date: "2020-03-20",
    gender: "Nữ",
    grade_id: 1,
    grade_name: "Nhóm Chồi",
    parent_name: "Trần Văn D",
    parent_phone: "0987654321",
    address: "456 Đường DEF, Quận 2, TP.HCM",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 3,
    student_code: "HS003",
    full_name: "Lê Minh E",
    birth_date: "2019-08-10",
    gender: "Nam",
    grade_id: 2,
    grade_name: "Nhóm Lá",
    parent_name: "Lê Thị F",
    parent_phone: "0912345678",
    address: "789 Đường GHI, Quận 3, TP.HCM",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  }
];

const mockMeasurements = [
  {
    id: 1,
    student_id: 1,
    measurement_date: "2025-08-06",
    height: 95.5,
    weight: 14.2,
    head_circumference: 48.5,
    chest_circumference: 52.0,
    bmi: 15.6,
    nutrition_status: "Bình thường",
    growth_status: "Phát triển tốt",
    notes: "Trẻ phát triển bình thường",
    measured_by: "Cô Y tá A",
    created_at: "2025-08-06 08:00:00"
  },
  {
    id: 2,
    student_id: 1,
    measurement_date: "2025-07-06",
    height: 94.0,
    weight: 13.8,
    head_circumference: 48.2,
    chest_circumference: 51.5,
    bmi: 15.6,
    nutrition_status: "Bình thường",
    growth_status: "Phát triển tốt",
    notes: "Tăng trưởng ổn định",
    measured_by: "Cô Y tá A",
    created_at: "2025-07-06 08:00:00"
  },
  {
    id: 3,
    student_id: 2,
    measurement_date: "2025-08-06",
    height: 93.2,
    weight: 13.5,
    head_circumference: 47.8,
    chest_circumference: 50.8,
    bmi: 15.5,
    nutrition_status: "Bình thường",
    growth_status: "Phát triển tốt",
    notes: "Trẻ khỏe mạnh",
    measured_by: "Cô Y tá A",
    created_at: "2025-08-06 08:00:00"
  }
];

const mockHealthRecords = [
  {
    id: 1,
    student_id: 1,
    examination_date: "2025-08-06",
    diseases: [
      {
        disease_id: 20,
        disease_name: "Sâu răng",
        disease_code: "SR",
        status: "Có",
        notes: "Sâu răng nhẹ ở răng hàm"
      }
    ],
    general_health: "Tốt",
    recommendations: "Vệ sinh răng miệng thường xuyên",
    next_checkup: "2025-11-06",
    examined_by: "Bác sĩ Nguyễn Văn X",
    created_at: "2025-08-06 09:00:00"
  },
  {
    id: 2,
    student_id: 2,
    examination_date: "2025-08-06",
    diseases: [],
    general_health: "Rất tốt",
    recommendations: "Duy trì chế độ ăn uống và vận động",
    next_checkup: "2025-11-06",
    examined_by: "Bác sĩ Nguyễn Văn X",
    created_at: "2025-08-06 09:00:00"
  }
];

// API Routes

// Diseases APIs
router.get('/api/diseases', (req, res) => {
  res.json({
    diseases: mockDiseases,
    default_diseases: mockDefaultDiseases,
    admin_diseases: mockDefaultDiseases,
    is_admin: 0,
    user: {
      name: "Trường MN QuangVT",
      level: 4
    },
    result: "success"
  });
});

router.post('/diseases/list', (req, res) => {
  const { page = 1, rows = 30, search = '' } = req.body;
  let filteredDiseases = [...mockDiseases, ...mockDefaultDiseases];
  
  if (search) {
    filteredDiseases = filteredDiseases.filter(disease => 
      disease.name.toLowerCase().includes(search.toLowerCase()) ||
      disease.code.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredDiseases.slice(start, end);
  
  res.json({
    total: filteredDiseases.length,
    rows: paginatedData
  });
});

router.post('/diseases/create', (req, res) => {
  const { name, code, default_note } = req.body;
  
  const newDisease = {
    id: Math.max(...mockDiseases.map(d => d.id)) + 1,
    name,
    code,
    unit_id: 2539,
    origin_id: 0,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    updated_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    deleted_at: null,
    grade_ids: null,
    default_note: default_note || null
  };
  
  mockDiseases.push(newDisease);
  
  res.json({
    success: true,
    message: "Tạo bệnh tật thành công",
    data: newDisease
  });
});

// Student APIs
router.post('/students/list', (req, res) => {
  const { page = 1, rows = 30, search = '', grade_id = '' } = req.body;
  let filteredStudents = mockStudents;
  
  if (search) {
    filteredStudents = filteredStudents.filter(student => 
      student.full_name.toLowerCase().includes(search.toLowerCase()) ||
      student.student_code.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  if (grade_id) {
    filteredStudents = filteredStudents.filter(student => student.grade_id == grade_id);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredStudents.slice(start, end);
  
  res.json({
    total: filteredStudents.length,
    rows: paginatedData
  });
});

router.post('/students/create', (req, res) => {
  const { student_code, full_name, birth_date, gender, grade_id, parent_name, parent_phone, address } = req.body;
  
  const newStudent = {
    id: mockStudents.length + 1,
    student_code,
    full_name,
    birth_date,
    gender,
    grade_id: parseInt(grade_id),
    grade_name: "Nhóm mới",
    parent_name,
    parent_phone,
    address,
    status: 1,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockStudents.push(newStudent);
  
  res.json({
    success: true,
    message: "Tạo học sinh thành công",
    data: newStudent
  });
});

// Measurements APIs
router.post('/measurements/list', (req, res) => {
  const { student_id, date_from, date_to, page = 1, rows = 30 } = req.body;
  let filteredMeasurements = mockMeasurements;
  
  if (student_id) {
    filteredMeasurements = filteredMeasurements.filter(m => m.student_id == student_id);
  }
  
  if (date_from && date_to) {
    filteredMeasurements = filteredMeasurements.filter(m => 
      m.measurement_date >= date_from && m.measurement_date <= date_to
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredMeasurements.slice(start, end);
  
  // Thêm thông tin học sinh
  const enrichedData = paginatedData.map(measurement => {
    const student = mockStudents.find(s => s.id === measurement.student_id);
    return {
      ...measurement,
      student_name: student ? student.full_name : 'N/A',
      student_code: student ? student.student_code : 'N/A'
    };
  });
  
  res.json({
    total: filteredMeasurements.length,
    rows: enrichedData
  });
});

router.post('/measurements/create', (req, res) => {
  const { 
    student_id, 
    measurement_date, 
    height, 
    weight, 
    head_circumference, 
    chest_circumference,
    notes,
    measured_by 
  } = req.body;
  
  // Tính BMI
  const heightInM = parseFloat(height) / 100;
  const bmi = parseFloat(weight) / (heightInM * heightInM);
  
  // Đánh giá tình trạng dinh dưỡng (đơn giản)
  let nutrition_status = "Bình thường";
  if (bmi < 14) nutrition_status = "Thiếu cân";
  else if (bmi > 17) nutrition_status = "Thừa cân";
  
  const newMeasurement = {
    id: mockMeasurements.length + 1,
    student_id: parseInt(student_id),
    measurement_date,
    height: parseFloat(height),
    weight: parseFloat(weight),
    head_circumference: parseFloat(head_circumference),
    chest_circumference: parseFloat(chest_circumference),
    bmi: Math.round(bmi * 10) / 10,
    nutrition_status,
    growth_status: "Phát triển tốt",
    notes: notes || "",
    measured_by: measured_by || "Y tá",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockMeasurements.push(newMeasurement);
  
  res.json({
    success: true,
    message: "Thêm số đo thành công",
    data: newMeasurement
  });
});

// Health Records APIs
router.post('/health_records/list', (req, res) => {
  const { student_id, date_from, date_to, page = 1, rows = 30 } = req.body;
  let filteredRecords = mockHealthRecords;
  
  if (student_id) {
    filteredRecords = filteredRecords.filter(r => r.student_id == student_id);
  }
  
  if (date_from && date_to) {
    filteredRecords = filteredRecords.filter(r => 
      r.examination_date >= date_from && r.examination_date <= date_to
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredRecords.slice(start, end);
  
  // Thêm thông tin học sinh
  const enrichedData = paginatedData.map(record => {
    const student = mockStudents.find(s => s.id === record.student_id);
    return {
      ...record,
      student_name: student ? student.full_name : 'N/A',
      student_code: student ? student.student_code : 'N/A'
    };
  });
  
  res.json({
    total: filteredRecords.length,
    rows: enrichedData
  });
});

router.post('/health_records/create', (req, res) => {
  const { 
    student_id, 
    examination_date, 
    diseases, 
    general_health, 
    recommendations,
    examined_by 
  } = req.body;
  
  const newRecord = {
    id: mockHealthRecords.length + 1,
    student_id: parseInt(student_id),
    examination_date,
    diseases: diseases || [],
    general_health: general_health || "Tốt",
    recommendations: recommendations || "",
    next_checkup: moment(examination_date).add(3, 'months').format('YYYY-MM-DD'),
    examined_by: examined_by || "Bác sĩ",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockHealthRecords.push(newRecord);
  
  res.json({
    success: true,
    message: "Tạo hồ sơ sức khỏe thành công",
    data: newRecord
  });
});

// Growth Chart API
router.post('/growth_chart', (req, res) => {
  const { student_id } = req.body;
  
  const studentMeasurements = mockMeasurements
    .filter(m => m.student_id == student_id)
    .sort((a, b) => new Date(a.measurement_date) - new Date(b.measurement_date));
  
  const chartData = {
    student_id,
    measurements: studentMeasurements,
    growth_trend: {
      height: "Tăng trưởng bình thường",
      weight: "Tăng trưởng bình thường",
      bmi: "Ổn định"
    },
    recommendations: [
      "Duy trì chế độ ăn uống cân bằng",
      "Tăng cường hoạt động thể chất",
      "Theo dõi định kỳ"
    ]
  };
  
  res.json({
    success: true,
    data: chartData
  });
});

module.exports = router;
