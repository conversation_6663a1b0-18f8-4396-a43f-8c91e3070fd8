(function AuthService(app, $CFG) {
    'use strict';

    app.service('AuthService', [authService]);

    var SUPER_ADMIN = 1;

    function authService() {
        function isSuperAdmin() {
            var cd_admin = false;
            if (_.get($CFG, 'user.superadmin', 0) === SUPER_ADMIN || _.get($CFG, 'user.course_id', 0)==0) {
                cd_admin = true;
            }
            return cd_admin;
        }

        return {
            isSuperAdmin: isSuperAdmin,
        };
    }

})(window.angular_app, window.$CFG);
