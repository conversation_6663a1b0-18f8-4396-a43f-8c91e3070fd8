var dialog = (function () {
    return {
        init: function ($scope, option, url) {
            url = url || '';
            option = option || {};
            option.title = option.title || 'Dialog';
            option.message = option.message || $('<div></div>').append(dialog.getLoading());
            option.type = option.type || BootstrapDialog.TYPE_DEFAULT;
            option.size = option.size || BootstrapDialog.SIZE_NORMAL;
            option.closeByBackdrop = option.closeByBackdrop || false;
            option.onshown = option.onshown || function (dialogRef) {
                if (url) {
                    $('<div class="dialog"></div>').load(url, function (html) {
                        $scope.$apply(function () {
                            dialogRef.getModalBody().html($scope.compile(html, $scope));
                        });
                    });
                }
            };
            BootstrapDialog.show(option);
        },
        getLoading: function () {
            return 'Đang tải ...';
        }
    }
})();