
<div ng-controller="DashboardController as vm" class="position-relative w-100 h-100 mh-100">
    <div class="modal fade" id="excelModal" tabindex="-1" role="dialog" aria-labelledby="excelModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document" style="max-width: 1800px">
            <div class="modal-content" style="margin-top: 250px;">
                <div class="modal-header">
                    <span class="modal-title text-capitalize" id="excelModalLabel"
                          style="font-size: 20px; font-weight: bold;">Thêm từ Excel</span>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="overflow: scroll; max-height: 750px;">
                    <div style="display: flex; justify-content: space-between;" class="mb-2">
                        <div style="display: flex; justify-content: flex-start;" class="mb-2">
                            <div class="form-group m-0 mr-2">
                                <select class="form-control custom-select" ng-options="year.id as year.title for year in vm.years"
                                        ng-model="vm.filters.school_year" ng-change="vm.onSchoolYearChanged()">
                                </select>
                            </div>
                            <div class="form-group m-0 mr-2">
                                <select class="form-control custom-select" ng-options="month.id as month.title for month in vm.months"
                                        ng-model="vm.filters.month" ng-change="vm.onMonthChanged()">
                                </select>
                            </div>
                        </div>

                        <div class="f-flex">
<!--                            <button class="btn btn-success btn-sm" ng-click="downloadImportBmiTemplate()">-->
<!--                                <i class="fa fa-download"></i>-->
<!--                                <span class="text-capitalize text-bold">Tải mẫu Excel Cho</span>-->
<!--                            </button>-->

                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">Tải Mẫu Excel Cho:</span>
                                </div>

                                <select class="form-control custom-select" ng-change="vm.onCourseChanged($event)"
                                        ng-model="vm.filters.course_id">
                                    <option ng-repeat="grade in vm.courses" ng-value="grade.id" ng-if="!grade.courses">
                                        {{grade.name}}
                                    </option>
                                    <optgroup ng-repeat="grade in vm.courses" label="{{grade.name}}" ng-if="grade.courses">
                                        <option ng-repeat="course in grade.courses.data" ng-value="course.id">{{course.name}}</option>
                                    </optgroup>
                                </select>

                                <div class="input-group-prepend">
                                    <span class="input-group-text">Sắp xếp theo mã học sinh</span>
                                </div>

                                <div style="position: relative; width: 30px; background: #e9ecef; border: 1px solid #ced4da; border-left: none;">
                                    <input type="checkbox"
                                           style="position: absolute; left: 50%; top: 50%;  transform: translate(-50%, -50%); opacity: 100%;"
                                           ng-model="sortByStudentId">
                                </div>

                                <div class="input-group-append">
                                    <button class="btn btn-outline-info" type="button" ng-click="downloadImportBmiTemplate()">
                                        <i class="fa fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div style="display: flex; justify-content: space-between;" class="mb-2">
                        <div>
                            <label for="excelFileInput" class="file-input-custom" style=""  ng-click="onClickFileUploadInput($event)">
                                <span class="text-capitalize">Chọn tệp</span>
                                <input type="file" id="excelFileInput" accept=".xls,.xlsx"
                                       style="display: none;" onchange="angular.element(this).scope().uploadFileExcel()"/>
                            </label>

                            <div ng-if="fileSelected">
                                <span class="text-info">{{fileSelected.name}}</span>
                                <strong class="text-danger">{{(fileSelected.size / 1000).toFixed(1)}} KB</strong>
                            </div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div>
                        <div ng-if="importBmiData.length > 0" class="mb-2">
                            <button class="btn btn-success" ng-click="saveImportBmiData()">Thêm dữ liệu</button>
                        </div>
                        <table class="table table-bordered" >
                            <thead style="background-color: #bce2f4">
                                <tr >
                                    <th>STT</th>
                                    <th>Mã Học Sinh</th>
                                    <th>Họ tên</th>
                                    <th>Giới tính</th>
                                    <th>Ngày sinh</th>
                                    <th>Tháng tuổi cân đo</th>
                                    <th>CÂN NẶNG (KG)</th>
                                    <th>CHIỀU CAO (CM)</th>
                                    <th>Đánh giá chung</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="data in importBmiData">
                                    <td class="td-w-2" ng-bind="$index + 1"></td>
                                    <td>
                                        <span>{{data.student_id}}</span>
                                    </td>
                                    <td>
                                        <span>{{data.fullname}}</span>
                                    </td>
                                    <td>
                                        <span>{{data.gender_label}}</span>
                                    </td>
                                    <td>
                                        <span>{{data.birthdate}}</span>
                                    </td>
                                    <td>
                                        <span>{{data.month_old}}</span>
                                    </td>
                                    <td>
                                        <input type="number" ng-model="data.weight" class="w-100">
                                        <div class="text-danger small">
                                            {{importBmiValidateErrors['data.' + $index + '.weight'][0]}}
                                        </div>
                                    </td>
                                    <td>
                                        <input type="number" ng-model="data.height"  class="w-100">
                                        <div class="text-danger small">
                                            {{importBmiValidateErrors['data.' + $index + '.height'][0]}}
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" ng-model="data.conclusion_text"  class="w-100">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
<!--                    <button class="btn btn-primary" ng-click="uploadFileExcel()">Tải lên</button>-->
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12" style="display: flex;">
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select" ng-options="year.id as year.title for year in vm.years"
                    ng-model="vm.filters.school_year" ng-change="vm.onSchoolYearChanged()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select" ng-options="month.id as month.title for month in vm.months"
                    ng-model="vm.filters.month" ng-change="vm.onMonthChanged()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select" ng-change="vm.onCourseChanged()"
                    ng-model="vm.filters.course_id">
                    <option ng-repeat="grade in vm.courses" ng-value="grade.id" ng-if="!grade.courses">
                        {{grade.name}}
                    </option>
                    <optgroup ng-repeat="grade in vm.courses" label="{{grade.name}}" ng-if="grade.courses">
                        <option ng-repeat="course in grade.courses.data" ng-value="course.id">{{course.name}}</option>
                    </optgroup>
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <div style="height: 30px;">
					<button class="btn ml-auto dropdown-toggle" type="button" id="dropdownExcel"
						data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="background:none; margin-top:-5px;">
						<i class="mdi mdi-settings" style="font-size:16px; color:blue;"> Tiện ích</i>
					</button>
					<div class="dropdown-menu" aria-labelledby="dropdownExcel" style="max-height:400px; overflow-y:auto;">
						<div style="padding-left:5px;">
							<input type="checkbox" id="malnutritionLastMonth" class="filled-in chk-col-blue-grey" ng-model="vm.filters.malnutrition_last_month" ng-change="vm.onMalnutritionChanged()">
							<label for="malnutritionLastMonth" style="width:400px;">Gợi ý học sinh SDD, BP, TC...tháng trước</label>
						</div>
						<div style="padding-left:5px;" ng-if="$CFG.administrator || vm.filters.no_bmi_sdd_weight_rule">
							<input type="checkbox" id="noBmiSddWeightRule" class="filled-in chk-col-blue-grey" ng-model="vm.filters.no_bmi_sdd_weight_rule" ng-change="vm.onSddBmiRuleChanged()">
							<label for="noBmiSddWeightRule" style="width:400px;">Ko cần chấm BMI với trẻ dưới 60 tháng tuổi có cân nặng SDD, SDD+</label>
						</div>
                        <div style="padding-left:5px; margin-top:10px;" ng-if="($CFG.administrator || vm.filters.no_bmi_under60_month_rule) && !detachedZScore">
                            <input type="checkbox" id="noBmiUnder60MonthRule" class="filled-in chk-col-blue-grey" ng-model="vm.filters.no_bmi_under60_month_rule" ng-change="vm.onUnder60MonthBmiRuleChanged()" ng-disabled="!vm.filters.no_bmi_under60_month_rule && !(['40','97'].includes($CFG.province))">
                            <label for="noBmiUnder60MonthRule" style="width:400px;">Chấm chỉ số Z-Score với tất cả trẻ dưới 60 tháng tuổi</label>
                        </div>
                        <div style="padding-left:5px; margin-top:10px;">
                            <input type="checkbox" id="noIsNotWeightHeight" class="filled-in chk-col-blue-grey" ng-model="vm.filters.no_is_not_weight_height" ng-change="vm.onIsNotWeightHeightRuleChanged()">
                            <label for="noIsNotWeightHeight" style="width:400px;">Chỉ tính sĩ số theo số HS được nhập đầy đủ cân nặng, chiều cao (trong báo cáo)</label>
                        </div>
                        <div style="padding-left:5px;margin-top:20px;" ng-if="$CFG.administrator">
							<input type="checkbox" id="WHNormal" class="filled-in chk-col-blue-grey"  ng-model="vm.filters.get_weight_height_not_normal" ng-change="vm.onWHNormalChanged()">
							<label for="WHNormal" style="width:450px;">Lấy cân nặng, chiều cao trong DS Cân đo SDD, TC, BP</label>
						</div>
                        <div style="padding-left:5px;margin-top:10px;" ng-if="$CFG.administrator">
							<input type="checkbox" id="SMRMM" class="filled-in chk-col-blue-grey"  ng-model="vm.filters.show_modal_report_multi_month" ng-change="vm.onSMRMMChanged()">
							<label for="SMRMM" style="width:450px;">Hiển thị menu BC-TH cân nặng - chiều cao nhiều tháng</label>
						</div>
                        <!--p style="color:orange; padding:10px;">(Vui lòng nhấn F5 để cập nhật tiện ích nếu bạn thay đổi các giá trị này!)</p-->

                        <div style="padding:5px; border: 1px solid #ccc; text-align:center;" ng-if="$CFG.is_sync_csdl_nganh && $CFG.is_principal && !vm.filters.course_id && vm.filters.month">
                            <button type="button" class="btn btn-success" ng-click="vm.convertToCpuCando()" style="width:400px;">
                                <i class="mdi mdi-content-save-all"></i> Chuyển dữ liệu sang bảng chờ đồng bộ CSDL ngành
                            </button><br/>
                            <a href="{{$CFG.remote.base_url}}/report/csdl_nganh_v2/list_cando" target="_blank"><button type="button" class="btn btn-secondary" style="width:400px; margin-top:5px;">[ THÔNG TIN ĐỒNG BỘ CSDL NGÀNH ]</button>
                                </a>
                        </div>

                        <div style="padding-left:5px;margin-top:10px;" ng-if="$CFG.administrator">
							<input type="checkbox" id="hidden_month" class="filled-in chk-col-blue-grey"  ng-model="vm.filters.hidden_student_off_month" ng-change="vm.onHiddenStudentOffMonthChanged()">
							<label for="hidden_month" style="width:450px;">Ẩn học sinh nghỉ cả tháng (áp dụng nếu đơn vị sử dụng thu chi)</label>
						</div>
					</div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-around;" class="ml-auto">
                <button type="button" class="btn btn-success btn-sm mr-2" id="openModalBtn" ng-if="vm.filters.month == 7 && (vm.filters.unitId == '23314' || vm.filters.unitId == '19132')" ng-click="vm.copyDataFromMonth6()">
                    <i class="fa fa-copy"></i>
                    Copy dữ liệu tháng 6
                </button>
                <button type="button" class="btn btn-success btn-sm mr-2" id="openModalBtn">
                    <i class="fa fa-upload"></i>
                    Thêm từ Excel
                </button>
                <button class="btn btn-secondary btn-sm ml-auto" ng-click="vm.reload()" ng-if="vm.students">
                    <i class="btn-icon btn-reload mdi mdi-reload"></i>
                    Tải lại dữ liệu
                </button>
            </div>

        </div>
    </div>

    <div id="student-bmi" class="h-100 table-responsive mt-2 table-bmi table-bmi-dashboard" style="position: relative;">
        <form name="vm.studentBmiForm">
        <table id="student-bmi-table" class="jsgrid-table table table-striped table-hover">
            <thead style="position: absolute;z-index: 1;">
                <!--tr style="position: relative;height: 32px">
                    <th colspan="{{vm.filters.course_id>0?6:7}}" class="table-bmi-dashboard--info {{vm.filters.course_id>0?' class':' school'}}">
                        <div class="absolute tr1-cell-fixed">Thông tin học sinh</div>
                    </th>
                    <th colspan="4">Thông tin cân đo</th>
                    <th colspan="3">Gợi ý đánh giá Dư cân, Béo phì</th>
                    <th rowspan="2" class="table-bmi-dashboard--note">Giải pháp thực hiện</th>
                    <th rowspan="2" class="table-bmi-dashboard--note-after">Kết quả sau giải pháp</th>
                </tr-->
                <tr style="position: relative;height: 32px">
                    <th class="table-bmi-dashboard--stt"><div class="absolute tr2-cell-fixed">STT</div></th>
                    <th class="table-bmi-dashboard--detail"><div class="absolute tr2-cell-fixed"></div></th>
                    <th class="table-bmi-dashboard--full-name">
                        <div class="absolute tr2-cell-fixed" style="padding: 15px 3px;">
                            <div class="div_search">
                                <input type="text" class="form-control"
                                       placeholder="Họ tên hoặc Mã HS"
                                       ng-model-options="{ debounce: 1000 }"
                                       ng-model="vm.filters.keyword"
                                       ng-change="vm.onKeywordChanged()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </th>
                    <th class="table-bmi-dashboard--student-id">
                        <div class="absolute tr2-cell-fixed">Mã HS</div>
                    </th>
                    <th class="table-bmi-dashboard--birthday">Ngày sinh</th>
                    <th class="table-bmi-dashboard--birthday" style="min-width:150px;" ng-if="!vm.filters.course_id">Tên lớp</th>
                    <th class="table-bmi-dashboard--month-old">Số tháng</th>
                    <th class="table-bmi-dashboard--gender">Nữ</th>
                    <th class="table-bmi-dashboard--weight">Cân nặng<br/>(kg)</th>
                    <th class="table-bmi-dashboard--weight-result">KQ</th>
                    <th class="table-bmi-dashboard--height">Chiều cao<br/>(cm)</th>
                    <th class="table-bmi-dashboard--height-result">KQ</th>
                    <th class="table-bmi-dashboard--bmi">CS BMI <span ng-if="!detachedZScore"><br> (Z-Score)</span></th>
                    <th class="table-bmi-dashboard--bmi" ng-if="detachedZScore">Z-Score</th>
                    <th class="table-bmi-dashboard--bmi-result">KQ</th>
                    <th class="table-bmi-dashboard--bmi-rate">Đánh giá chung</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--blood-pressure" >Huyết áp <br/>(/mmHG)</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--beat-heart">Nhịp tim <br/>(lần/phút)</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">Mắt trái không kính <br/>(/10)</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">Mắt phải không kính <br/>(/10)</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">Mắt trái có kính <br/>(/10)</th>
                    <th ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">Mắt phải có kính <br/>(/10)</th>
					<th class="table-bmi-dashboard--note" ng-if = "!vm.showBC">Giải pháp thực hiện</th>
                    <th class="table-bmi-dashboard--note-after" ng-if = "!vm.showBC">Kết quả sau giải pháp</th>
                    <th class="table-bmi-dashboard--bmi" ng-if="$CFG.administrator==2 || $CFG.cando_dshs_allow_hack_bmi" style="width:250px !important;"><div style="width: 100%;" title="Để trống thì hệ thống sẽ lấy giá trị gốc của phần mềm tính ">Sửa kết quả BMI của học sinh</div></th>
                </tr>
            </thead>
            <tbody style="position: absolute;z-index: 0;margin-top: 55px">
                <tr ng-repeat="student in vm.students" style="position: relative;" title="Tháng trước: {{student.last_month_str}}; Cân nặng:{{student.last_month_weight}} kg; Chiều cao:{{student.last_month_height}} cm">
                    <td class="table-bmi-dashboard--stt text-center">
                        <div class="absolute tbody-cell-fixed">
                            {{(vm.paginate.page - 1) * vm.paginate.limit + $index + 1}}
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--detail text-center">
                        <div class="absolute tbody-cell-fixed" style="margin-top: -5px;height: 34px; padding-top: 5px">
                            <a href="javascript:;" ng-click="vm.onViewModal(student)" data-toggle="modal"
                            data-target="#myModal">
                                <i class="mdi mdi-eye"></i>
                            </a>
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--full-name">
                        <div class="absolute tbody-cell-fixed">
                            {{student.last_name}} {{student.first_name}}
                        </div>
                    </td>
                    <th class="table-bmi-dashboard--student-id text-center">
                        <div class="absolute tbody-cell-fixed">{{student.id}}</div>
                    </th>
                    <td class="table-bmi-dashboard--birthday text-center" ng-style="{color:(!student.birthday_valid?'red':'')}">
                        {{student.birthday | date: 'dd/MM/yyyy'}}
                    </td>
                    <td class="table-bmi-dashboard--birthday text-center" style="min-width:150px;" ng-if="!vm.filters.course_id">
                        {{student.course_name}}
                    </td>
                    <td class="table-bmi-dashboard--month-old text-center">
                        {{student.month_old}}
                    </td>
                    <td class="table-bmi-dashboard--gender text-center">
                        <i class="mdi mdi-check" ng-if="student.isFemale()"></i>
                    </td>
                    <td class="table-bmi-dashboard--weight">
                        <div class="custom-input-group" ng-class="{'is-invalid': vm.isInvalid('student_weight_' + $index), 'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input name="student_weight_{{$index}}"
                                   class="form-control"
                                   ng-model="student.weight"
                                   ng-pattern="/^[0-9]+(\.[0-9]{1,2})?$/"
                                   ng-change="vm.onWeightOrHeightChanged(student)"
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="student.old_weight != student.weight ? vm.save() : ''"
                                   ng-focus="student.old_weight = student.weight"
                                   ng-style="{color: (student.weight>0 && student.weight<student.last_month_weight)? 'orange': ''}"/>
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--weight-result text-center">
                        {{vm.bmiResultToCode(student, 'weight')}}
                    </td>
                    <td class="table-bmi-dashboard--height">
                        <div class="custom-input-group" ng-class="{'is-invalid': vm.isInvalid('student_height_' + $index), 'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input name="student_height_{{$index}}"
                                   class="form-control"
                                   ng-model="student.height"
                                   ng-pattern="/^[0-9]+(\.[0-9]{1,2})?$/"
                                   ng-change="vm.onWeightOrHeightChanged(student)"
                                   ng-disabled="vm.unit_level==2 ||vm.unit_level==3 || student.is_lock"
                                   ng-blur="student.old_height != student.height ? vm.save() : ''"
                                   ng-focus="student.old_height = student.height"
                                   ng-style="{color: (student.height>0 && student.height<student.last_month_height)? 'orange': ''}"/>
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--height-result text-center">{{vm.bmiResultToCode(student, 'height')}}</td>
                    <td class="table-bmi-dashboard--bmi text-center" title="{{detachedZScore ?'Chỉ số BMI' :(student.month_old > 60 ? 'Chỉ số BMI' : student.month_old >=24 ? 'Chỉ số Z-Score' : '') }}" ng-bind = "((detachedZScore && student.month_old > 60) || !detachedZScore ) && !(student.month_old < 60 && vm.filters.no_bmi_sdd_weight_rule && ['SDD','SDD+'].includes(vm.bmiResultToCode(student, 'weight'))) ? student.bmi : ''">{{student.bmi}}</td>
                    <td class="table-bmi-dashboard--bmi text-center" ng-if="detachedZScore" title="Chỉ số Z-Score" ng-bind="(student.month_old <= 60) ? student.z_score : ''">{{student.z_score}}</td>
                    <td class="table-bmi-dashboard--bmi-result text-center">{{vm.bmiResultToCode(student, 'bmi')}}</td>

                    <td class="table-bmi-dashboard--bmi-rate">
						<div class="custom-input-group {{(vm.unit_level==2 ||vm.unit_level==3)?' disabled':''}}">
                            <input type="text" name="student_conclusion_text_{{$index}}" style="max-width: 100% !important;" class="form-control" ng-model="student.conclusion_text" ng-change="vm.changed=true" ng-disabled="vm.unit_level==2 ||vm.unit_level==3 || student.is_lock" ng-blur="vm.old_conclusion_text != student.conclusion_text ? vm.save() : ''" ng-focus="vm.old_conclusion_text = student.conclusion_text"/>
                        </div>
					</td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--blood-pressure">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_blood_pressure_min_{{$index}}"
                                   class="form-control"
                                   ng-model="student.blood_pressure_min"
                                   
                                   ng-disabled="vm.unit_level==2 ||vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('blood_pressure_min',student)"
                                   ng-focus="student.old_blood_pressure_min = student.blood_pressure_min" />
                                   
                        </div>
                    </td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--beat-heart">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_beat_heart_{{$index}}"
                                   class="form-control"
                                   ng-model="student.beat_heart"
                                   
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('beat_heart',student)"
                                   ng-focus="student.old_beat_heart = student.beat_heart" />
                        </div>
                    </td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_eye_sight_no_glasses_left_{{$index}}"
                                   class="form-control"
                                   ng-model="student.eye_sight_no_glasses_left"
                                   
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('eye_sight_no_glasses_left',student)"
                                   ng-focus="student.old_eye_sight_no_glasses_left = student.eye_sight_no_glasses_left" />
                        </div>
                    </td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_eye_sight_no_glasses_right_{{$index}}"
                                   class="form-control"
                                   ng-model="student.eye_sight_no_glasses_right"
                                   
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('eye_sight_no_glasses_right',student)"
                                   ng-focus="student.old_eye_sight_no_glasses_right = student.eye_sight_no_glasses_right" />
                        </div>
                    </td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_eye_sight_has_glasses_left_{{$index}}"
                                   class="form-control"
                                   ng-model="student.eye_sight_has_glasses_left"
                                   
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('eye_sight_has_glasses_left',student)"
                                   ng-focus="student.old_eye_sight_has_glasses_left = student.eye_sight_has_glasses_left" />
                        </div>
                    </td>
                    <td ng-if = "vm.filters.month == 9 && vm.showBC" class="table-bmi-dashboard--eye-sight">
                        <div class="custom-input-group" ng-class="{'disabled': vm.unit_level==2 ||vm.unit_level==3}">
                            <input 
                                   type="text"
                                   name="student_eye_sight_has_glasses_right_{{$index}}"
                                   class="form-control"
                                   ng-model="student.eye_sight_has_glasses_right"
                                   
                                   ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                                   ng-blur="vm.save_tmp('eye_sight_has_glasses_right',student)"
                                   ng-focus="student.old_eye_sight_has_glasses_right = student.eye_sight_has_glasses_right" />
                        </div>
                    </td>
                    <td ng-if = "!vm.showBC" class="table-bmi-dashboard--note">
                        <div class="custom-input-group {{(vm.unit_level==2 ||vm.unit_level==3)?' disabled':''}}">
                            <input type="text" name="student_note_{{$index}}" style="max-width: 100% !important;" class="form-control" ng-model="student.note" ng-change="vm.onNoteChanged(student)" ng-disabled="vm.unit_level==2 ||vm.unit_level==3"/>
                        </div>
                    </td>
                    <td ng-if = "!vm.showBC" class="table-bmi-dashboard--note-after">
                        <div class="custom-input-group {{(vm.unit_level==2 ||vm.unit_level==3)?' disabled':''}}">
                            <input type="text" name="student_note_after_{{$index}}" style="max-width: 100% !important;" class="form-control" ng-model="student.note_after" ng-change="vm.changed=true" ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"/>
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--bmi-result text-center" ng-if="$CFG.administrator==2 || $CFG.cando_dshs_allow_hack_bmi">
                        <select class="form-control form-control-sm" ng-model="student.bmi_result_hack" style="width:215px"
                        ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                        ng-change="vm.save_bmi_result_hack('bmi_result_hack', student)" ng-options="hack_item.id as hack_item.title for hack_item in vm.bmi_result_hacks">
                        </select>
                        <!--input name="student_bmi_result_hack_{{$index}}" ng-change="vm.save_bmi_result_hack('bmi_result_hack', student)" style="width:150px; color: red;" ng-model="student.bmi_result_hack"-->
                    </td>
                </tr>
            </tbody>
            </table>
        </form>
    </div>

    <div class="d-flex justify-content-between mt-2">
        <div class="w-50 d-flex">
            <select class="form-control form-control-sm w-auto mr-2" ng-model="vm.paginate.limit"
                ng-change="vm.reload()" ng-options="item as item for item in vm.paginate.perPages">
            </select>
            <ul uib-pagination class="pagination-sm" ng-if="vm.paginate.total > vm.paginate.limit"
                max-size="vm.paginate.maxSize" total-items="vm.paginate.total" items-per-page="vm.paginate.limit"
                ng-model="vm.paginate.page" ng-change="vm.pageChanged()" boundary-link-numbers="true" rotate="false"
                previous-text="&lsaquo;" next-text="&rsaquo;"></ul>
        </div>

        <div class="w-50 align-items-end text-right">
            <button type="button" class="btn btn-success" ng-click="vm.save()"
                ng-disabled="!vm.changed || vm.studentBmiForm.$invalid">
                <i class="mdi mdi-content-save-all"></i> Lưu
            </button>
            <a type="button" class="btn btn-success" ng-if="vm.filters.month" ng-disabled="!(vm.students.length > 0)" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_info_course?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}" >
                Xem
            </a>
            <button class="btn btn btn-info dropdown-toggle waves-effect waves-light" type="button" id="dropdownExcel"
                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                ng-disabled="!(vm.students.length > 0)">
                <i class="mdi mdi-download"></i> Xuất danh sách
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownExcel">
                <a class="dropdown-item" href="#" ng-click="vm.downloadExcel()">Danh sách cân đo</a>
                <!--a class="dropdown-item" href="#" ng-click="vm.downloadExcelSDD()">DS trẻ SDD, BP, TC</a-->
                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_status?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&detach=1" ng-if="vm.filters.course_id" ng-click="vm.printRptStatus()">Tình trạng dinh dưỡng của trẻ</a>

                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_status?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&detach=1&type=-1" ng-if="vm.filters.course_id" ng-click="vm.printRptStatus()">Tình trạng dinh dưỡng của trẻ (SDD, TC, BP)</a>

                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_status?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&type=1" ng-if="vm.filters.month == 9 && vm.filters.course_id" >Tình trạng dinh dưỡng của trẻ (+huyết áp, nhịp tim, ...) </a>

                <a class="dropdown-item" href="#" ng-if="!vm.filters.course_id" ng-click="vm.formExportWeigthAndHeigth()"> BC-TH cân nặng - chiều cao</a>
                <a class="dropdown-item" href="#" ng-if="!vm.filters.course_id && vm.filters.show_modal_report_multi_month" ng-click="vm.showModalReportWH()"> BC-TH cân nặng - chiều cao chọn tháng</a> 
                <a class="dropdown-item" href="#" ng-if="!vm.filters.course_id" ng-click="vm.downloadExcelSDDAllYear()">DS trẻ SDD, BP, TC cả năm</a>

                <a class="dropdown-item" ng-if="vm.showBC && !vm.filters.course_id" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_status_toantruong?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&type=0" ng-if="vm.filters.course_id" ng-click="vm.printRptStatus()">Tình trạng dinh dưỡng của trẻ toàn trường</a>

                <a class="dropdown-item" ng-if="vm.showBC && !vm.filters.course_id" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_status_toantruong?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&type=1" ng-if="vm.filters.course_id" ng-click="vm.printRptStatus()">Tình trạng dinh dưỡng của trẻ SDD, BP, TC toàn trường</a>

                <a class="dropdown-item" href="#" ng-if="vm.filters.course_id" ng-click="vm.formChooseMonth()">Theo dõi thể lực, dinh dưỡng & Kết quả khám sức khỏe chuyên khoa</a>
                <a class="dropdown-item" href="#" ng-if="vm.filters.course_id && vm.checkProvince(['14'])" ng-click="vm.formChooseMonthSonLa()">Theo dõi cân đo khám sức khỏe theo học kỳ</a>
            </div>
        </div>
    </div>
    
    <div id="myModal" class="modal fade myModal" role="dialog">
        <div class="modal-dialog" style="max-width: 1000px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close print-hidden" data-dismiss="modal">&times;</button>
                    <div>
                        <h3>THÔNG TIN CHIỀU CAO - CÂN NẶNG HỌC SINH</h3></div>
                        <div class="row">
                            <table style="width:100%;">
                                <tr>
                                    <td style="width:30%;padding-left: 20px;">
                                        <p>Tên: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.first_name}} {{student.last_name}}</span></p>
                                    </td>
                                    <td style="width:30%">
                                        <p>Ngày sinh: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.birth_date | date: 'dd/MM/yyyy'}}</span></p>
                                    </td>
                                    <td style="width:30%">
                                        <p>Lớp: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.course.name}}</span></p>
                                    </td>
                                    <td style="width:10%; padding-right: 20px;">
                                        <button style="right: 0px; float: right; position: relative; top:-20px" class="btn btn-secondary btn-sm ml-auto ng-scope print-hidden" id="printButton">
                                            <i class="btn-icon btn-reload mdi mdi-printer"></i>
                                            In
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                  </div>
                <div class="modal-body">
                    <div id="exampleSorting" class="jsgrid table_cando table_chitiet" style="position: relative; width: 100%;">

                        <div class="">
                            <div style="margin-bottom: 10px;" ng-if="!['01'].includes($CFG.province)">
                                <a class="btn btn-secondary btn-sm ml-auto ng-scope print-hidden"  href="javascript:;" ng-click="vm.onViewModal(vm.currentStudent, '1')" data-toggle="modal" data-target="#myModal1">
                                        <i class="mdi mdi-eye"></i> Xem thông tin 5 năm
                                </a>
                                <a class="btn btn-secondary btn-sm ml-auto ng-scope print-hidden"  href="javascript:;" data-toggle="modal" data-target="#myModal{{vm.currentStudent.group_age_id == 1 ? 'NT' : 'MG'}}">
                                    <i class="mdi mdi-eye"></i> Mẫu theo dõi thể lực theo thông tư 13
                                </a>
                            </div>
                            <table class="table color-table table-bordered text-center">
                                <thead>
                                <tr style="background-color: #bce2f4">
                                    <th>STT</th>
                                    <th>Tháng đo</th>
                                    <th>Tháng tuổi</th>
                                    <th>Cân nặng (kg)</th>
                                    <th>Chiều cao (cm)</th>
                                    <th>BMI</th>
                                    <th>Kết quả</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="(key,value) in student.histories | filter:hasHistory">
                                    <td>{{key+1}}</td>
                                    <td>{{value.month > 9 ? value.month : '0'+ value.month}}/{{ value.year}}</td>
                                    <td>{{value.month_old}}</td>
                                    <td>{{value.weight}} ({{vm.bmiResultToCode(value, 'weight')}})</td>
                                    <td>{{value.height}} ({{vm.bmiResultToCode(value, 'height')}})</td>
                                    <td>{{value.bmi}}</td>
                                    <td class="text-left">{{value.conclusion_text}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="jsgrid-pager-container" style="display: none;"></div>
                        <div class="jsgrid-load-shader" style="display: none; position: absolute; inset: 0px; z-index: 1000;"></div>
                        <div class="jsgrid-load-panel" style="display: none; position: absolute; top: 50%; left: 50%; z-index: 1000;">
                            Please, wait...
                        </div>
                    </div>
                
                    <!-- ĐÁNH GIÁ CHIỀU CAO -->
                
                    <div class="content_danhgia">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <table style="width:100%;">
                                        <td style="width:50%;">
                                            <div class="col-xs-12 col-xs-offset-6"><p class="chart_title"><span class="tl_danhgia pl-2">BIỂU ĐỒ CÂN NẶNG THEO ĐỘ TUỔI CỦA TRẺ</span>
                                            </p></div>
                                            <div class="col-xs-12 col-xs-offset-6">
                                                <div class="chart_cannang">
                                                    <div class="col-lg-12">
                                                        <div class="ct-animation-chart" id="weight-chart" style="height: 400px;"></div>
                                                    </div>
                                                    <hr>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="width:50%;">
                                            <div class="col-xs-12 col-xs-offset-6"><p class="chart_title"><span class="tl_danhgia pl-2">BIỂU ĐỒ CHIỀU CAO THEO ĐỘ TUỔI CỦA TRẺ</span>
                                            </p></div>
                                            <div class="col-xs-12 col-xs-offset-6">
                                                <div class="chart_cannang">
                                                    <div class="col-lg-12">
                                                        <div class="ct-animation-chart" id="height-chart" style="height: 400px;"></div>
                                                    </div>
                                                    <hr>
                                                </div>
                                            </div>
                                        </td>
                                    </div>
                                </table>
                                <div class="row"  style="width:100%;">
                                    <table style="width:100%;">
                                        <tr>
                                            <td style="width:50%;">
                                                <div class="text_note">
                                                    <div class="col-xs-12 col-xs-offset-6 text-right">
                                                        <span class="tl_danhgia pl-2 mr-5">GHI CHÚ CÂN NẶNG</span>
                                                    </div>
                                                    <div class="col-xs-12 col-xs-offset-6">
                                                        <div class="mr-3">
                                                            <div class="list_note text-right">
                                                                <!-- <div><span>Béo phì (BP)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle thua3 p-2"></i></div> -->
                                                                <div><span>Vượt chuẩn</span><i
                                                                    class="mdi mdi-checkbox-blank-circle thua2 p-2"></i></div>
                                                                <div><span>Bình thường (BT)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle bt p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng nhẹ cân (SDD)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy2 p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng nhẹ cân mức độ nặng (SDD+)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy3 p-2"></i></div>
                                                                <div><span>Chỉ số của Học Sinh</span><i
                                                                    class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text_note" style="margin-top: 10px">
                                                    <div class="col-xs-12 col-xs-offset-6 text-right">
                                                        <span class="tl_danhgia pl-2 mr-5">GHI CHÚ CHIỀU CAO</span>
                                                    </div>
                                                    <div class="col-xs-12 col-xs-offset-6">
                                                        <div class="mr-3">
                                                            <div class="list_note text-right">
                                                                <div><span>Cao hơn so với tuổi (VC)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle thua2 p-2"></i></div>
                                                                <div><span>Bình thường (BT)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle bt p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng thấp còi (SDD)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy2 p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng thấp còi mức độ nặng (SDD+)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy3 p-2"></i></div>
                                                                <div><span>Chỉ số của Học Sinh</span><i
                                                                    class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text_note" style="margin-top: 10px">
                                                    <div class="col-xs-12 col-xs-offset-6 text-right">
                                                        <span class="tl_danhgia pl-2 mr-5">GHI CHÚ BMI</span>
                                                    </div>
                                                    <div class="col-xs-12 col-xs-offset-6">
                                                        <div class="mr-3">
                                                            <div class="list_note text-right">
                                                                <div><span>Béo phì (BP)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle thua3 p-2"></i></div>
                                                                <div><span>Thừa cân (TC)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle thua2 p-2"></i></div>
                                                                <div><span>Bình thường (BT)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle bt p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng gầy còm (SDD)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy2 p-2"></i></div>
                                                                <div><span>Suy dinh dưỡng gầy còm mức độ nặng (SDD+)</span><i
                                                                    class="mdi mdi-checkbox-blank-circle suy3 p-2"></i></div>
                                                                <div><span>Chỉ số của Học Sinh</span><i
                                                                    class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="width:50%;vertical-align:top;">
                                                <div class="col-xs-12 col-xs-offset-6"><span class="tl_danhgia pl-2">BIỂU ĐỒ CHỈ SỐ BMI THEO ĐỘ TUỔI CỦA TRẺ</span>
                                                </div>
                                                <div class="col-xs-12 col-xs-offset-6">
                                                    <div class="chart_cannang">
                                                        <div class="col-lg-12">
                                                            <div class="ct-animation-chart" id="bmi-chart" style="height: 400px;"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- HET ĐÁNH GIÁ CHIỀU CAO -->
                </div>
            </div>
        </div>
    </div>
    <div id="myModal1" class="modal fade myModal1" role="dialog">
        <div class="modal-dialog" style="max-width: 1000px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close print-hidden" data-dismiss="modal">&times;</button>
                    <div>
                        <h3>THÔNG TIN CHIỀU CAO - CÂN NẶNG HỌC SINH</h3></div>
                        <div class="row">
                            <table style="width:100%;">
                                <tr>
                                    <td style="width:33%;padding-left: 20px;">
                                        <p>Tên: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.first_name}} {{student.last_name}}</span></p>
                                    </td>
                                    <td style="width:33%">
                                        <p>Ngày sinh: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.birth_date | date: 'dd/MM/yyyy'}}</span></p>
                                    </td>
                                    <td style="width:33%">
                                        <p>Lớp: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.course.name}}</span></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                  </div>
                <div class="modal-body">
                    <div id="exampleSorting" class="jsgrid table_cando table_chitiet" style="position: relative; width: 100%;">

                        <div class="">
                            <table class="table color-table table-bordered text-center">
                                <thead>
                                <tr style="background-color: #bce2f4">
                                    <th>STT</th>
                                    <th>Tháng đo</th>
                                    <th>Tháng tuổi</th>
                                    <th>Cân nặng (kg)</th>
                                    <th>Chiều cao (cm)</th>
                                    <th>BMI</th>
                                    <th>Z-Score</th>
                                    <th>Kết quả</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="(key,value) in studentAll.histories | filter:hasHistory">
                                    <td>{{key+1}}</td>
                                    <td>{{value.month > 9 ? value.month : '0'+ value.month}}/{{ value.year}}</td>
                                    <td>{{value.month_old}}</td>
                                    <td>{{value.weight}}</td>
                                    <td>{{value.height}}</td>
                                    <td>{{ value.month_old > 60 ?  value.bmi : ''}}</td>
                                    <td>{{ value.month_old <= 60 &&  value.month_old >= 24 ?  value.z_score : ''}}</td>
                                    <td class="text-left">{{value.conclusion_text}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="jsgrid-pager-container" style="display: none;"></div>
                        <div class="jsgrid-load-shader" style="display: none; position: absolute; inset: 0px; z-index: 1000;"></div>
                        <div class="jsgrid-load-panel" style="display: none; position: absolute; top: 50%; left: 50%; z-index: 1000;">
                            Please, wait...
                        </div>
                    </div>
                
                    <div class="hr"></div>
                
                    <!-- ĐÁNH GIÁ CHIỀU CAO -->
                
                    <div class="content_danhgia">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-xs-offset-12">
                                        <div class="col-xs-12 col-xs-offset-12"><p class="chart_title"><span class="tl_danhgia pl-2">BIỂU ĐỒ CÂN NẶNG THEO ĐỘ TUỔI CỦA TRẺ</span>
                                        </p></div>
                                        <div class="col-xs-12 col-xs-offset-12">
                                            <div class="chart_cannang">
                                                <div class="col-lg-12">
                                                    <div class="ct-animation-chart" id="weight-chart1" style="height: 400px;"></div>
                                                </div>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-12 col-md-12 col-xs-offset-12">
                                        <div class="col-xs-12 col-xs-offset-12"><p class="chart_title"><span class="tl_danhgia pl-2">BIỂU ĐỒ CHIỀU CAO THEO ĐỘ TUỔI CỦA TRẺ</span>
                                        </p></div>
                                        <div class="col-xs-12 col-xs-offset-12">
                                            <div class="chart_cannang">
                                                <div class="col-lg-12">
                                                    <div class="ct-animation-chart" id="height-chart1" style="height: 400px;"></div>
                                                </div>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-xs-offset-12">
                                        <div class="col-xs-12 col-xs-offset-6"><p class="chart_title"><span class="tl_danhgia pl-2">BIỂU ĐỒ ĐÁNH GIÁ CHỈ SỐ BMI THEO ĐỘ TUỔI CỦA TRẺ</span>
                                        </div>
                                        </p>
                                        <div class="col-xs-12 col-xs-offset-6">
                                            <div class="chart_cannang">
                                                <div class="col-lg-12">
                                                    <div class="ct-animation-chart" id="bmi-chart1" style="height: 400px;"></div>
                                                </div>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 pr-5 col-md-6 col-xs-offset-6">
                                        <div class="text_note">
                                            <div class="col-xs-12 col-xs-offset-6 text-right">
                                                <span class="tl_danhgia pl-2 mr-5">GHI CHÚ CÂN NẶNG</span>
                                            </div>
                                            <div class="col-xs-12 col-xs-offset-6">
                                                <div class="mr-3">
                                                    <div class="list_note text-right">
                                                        <!-- <div><span>Béo phì (BP)</span><i
                                                            class="mdi mdi-checkbox-blank-circle thua3 p-2"></i></div> -->
                                                        <div><span>Vượt chuẩn</span><i
                                                            class="mdi mdi-checkbox-blank-circle thua2 p-2"></i></div>
                                                        <div><span>Bình thường (BT)</span><i
                                                            class="mdi mdi-checkbox-blank-circle bt p-2"></i></div>
                                                        <div><span>Suy dinh dưỡng nhẹ cân (SDD)</span><i
                                                            class="mdi mdi-checkbox-blank-circle suy2 p-2"></i></div>
                                                        <div><span>Suy dinh dưỡng nhẹ cân mức độ nặng (SDD+)</span><i
                                                            class="mdi mdi-checkbox-blank-circle suy3 p-2"></i></div>
                                                        <div><span>Chỉ số của Học Sinh</span><i
                                                            class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 pr-5 col-md-6 col-xs-offset-6">
                                        <div class="text_note">
                                            <div class="col-xs-12 col-xs-offset-6 mb-3">
                                                <span class="tl_danhgia pl-2 mr-5">GHI CHÚ CHIỀU CAO</span>
                                            </div>
                                            <div class="col-xs-12 col-xs-offset-6">
                                                <div class="mr-3">
                                                    <div class="">
                                                        <div><i class="mdi mdi-checkbox-blank-circle thua2 p-2"></i><span>Cao hơn so với tuổi (VC)</span></div>
                                                        <div><i class="mdi mdi-checkbox-blank-circle bt p-2"></i><span>Bình thường (BT)</span></div>
                                                        <div><i class="mdi mdi-checkbox-blank-circle suy2 p-2"></i><span>Suy dinh dưỡng thấp còi (SDD)</span></div>
                                                        <div><i class="mdi mdi-checkbox-blank-circle suy3 p-2"></i><span>Suy dinh dưỡng thấp còi mức độ nặng (SDD+)</span></div>
                                                        <div><i class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i><span>Chỉ số của Học Sinh</span></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 pr-5 col-md-6 col-xs-offset-6">
                                        <div class="text_note" style="margin-top: 10px">
                                            <div class="col-xs-12 col-xs-offset-6 text-right">
                                                <span class="tl_danhgia pl-2 mr-5">GHI CHÚ BMI</span>
                                            </div>
                                            <div class="col-xs-12 col-xs-offset-6">
                                                <div class="mr-3">
                                                    <div class="list_note text-right">
                                                        <div><span>Béo phì (BP)</span><i
                                                            class="mdi mdi-checkbox-blank-circle thua3 p-2"></i></div>
                                                        <div><span>Thừa cân (TC)</span><i
                                                            class="mdi mdi-checkbox-blank-circle thua2 p-2"></i></div>
                                                        <div><span>Bình thường (BT)</span><i
                                                            class="mdi mdi-checkbox-blank-circle bt p-2"></i></div>
                                                        <div><span>Suy dinh dưỡng gầy còm (SDD)</span><i
                                                            class="mdi mdi-checkbox-blank-circle suy2 p-2"></i></div>
                                                        <div><span>Suy dinh dưỡng gầy còm mức độ nặng (SDD+)</span><i
                                                            class="mdi mdi-checkbox-blank-circle suy3 p-2"></i></div>
                                                        <div><span>Chỉ số của Học Sinh</span><i
                                                            class="mdi mdi-checkbox-blank-circle hocsinh p-2"></i></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- HET ĐÁNH GIÁ CHIỀU CAO -->
                </div>
            </div>
        </div>
    </div>
    <div id="myModalNT" class="modal fade myModalNT" role="dialog">
        <div class="modal-dialog" style="max-width: 1000px">
            <div class="modal-content">
                <div class="modal-header" style="padding: 1rem;border-top-left-radius: .3rem;border-top-right-radius: .3rem;">
                    <button type="button" class="close print-hidden" data-dismiss="modal">&times;</button>
                    <div>
                        <h3 style="font-size: 21px; font-family: sans-serif;">THEO DÕI THỂ LỰC HỌC SINH</h3></div>
                        <div class="row">
                            <div class="col-lg-5 col-md-5">
                                <p>Cơ sở GDMN: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{$CFG.schoolName}}</span></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4">
                                <p>Lớp: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.course.name}}</span></p>
                            </div>
                            <div class="col-lg-3 col-md-3">
                                <p>Năm học: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{vm.filters.school_year}} - {{vm.filters.school_year+1}}</span></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4">
                                <p>Tên: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.first_name}} {{student.last_name}}</span></p>
                            </div>
                            <div class="col-lg-3 col-md-3">
                                <p>Ngày sinh: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.birth_date | date: 'dd/MM/yyyy'}}</span></p>
                            </div>
                            <div class="col-lg-5 col-md-5">
                                <button style="right: 20px; float: right; position: relative;" class="btn btn-secondary btn-sm ml-auto ng-scope print-hidden" id="printButtonNT">
                                    <i class="btn-icon btn-reload mdi mdi-printer"></i>
                                    In
                                </button>
                            </div>
                        </div>
                    </div>
                <div class="modal-body">
                    <div class="content_danhgia">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 pr-5 col-md-6 col-xs-offset-6 border p-3" ng-repeat="(key,value) in student.histories | filter:hasHistory">
                                        <div class="col-xs-12 col-xs-offset-6"><p class="chart_title"><span class="tl_danhgia pl-2">Tháng {{ value.month }} năm {{ (parseInt(value.year) )}}</span>
                                        </p></div>
                                        <div class="col-xs-12 col-xs-offset-6">
                                           Chiều cao : <b>{{ value.height ? value.height  + ' cm' : 'Không đo' }}</b>
                                        </div>
                                        <div class="col-xs-12 col-xs-offset-6">
                                            Cân nặng : <b>{{ value.weight ? value.weight  + ' kg' : 'Không đo' }}</b>
                                        </div>
                                        <div class="col-xs-12 col-xs-offset-6">
                                            <p><b style="color: palevioletred;">Đánh giá tình trạng DD :</b></p>
                                            <div class="row">
                                                <table style="width:100%; margin-left:15px;">
                                                    <tr>
                                                        <td style="width:30%">
                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'BT'}">- Bình thường</span>
                                                        </td>
                                                        <td style="width:10%">
                                                            <i class="fa fa-check-square-o icon-checkbox color-red ng-scope" ng-if="vm.bmiResultToCode(value,'conclusion') == 'BT'"></i>

                                                            <i class="fa fa-square-o icon-checkbox color-green ng-scope" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'BT')" style=""></i>
                                                        </td>
                                                        <td style="width:50%">
                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'SDD'}">- Suy DD thể gầy còm</span>
                                                        </td>
                                                        <td style="width:10%">
                                                            <i class="fa fa-check-square-o icon-checkbox color-red ng-scope" ng-if="vm.bmiResultToCode(value,'conclusion') == 'SDD'"></i>

                                                            <i class="fa fa-square-o icon-checkbox color-green ng-scope" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'SDD')" style=""></i>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'TC'}">- Thừa cân</span>
                                                        </td>
                                                        <td>
                                                            <i class="fa fa-check-square-o icon-checkbox color-red ng-scope" ng-if="vm.bmiResultToCode(value,'conclusion') == 'TC'"></i>

                                                            <i class="fa fa-square-o icon-checkbox color-green ng-scope" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'TC')" style=""></i>
                                                        </td>
                                                        <td>
                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'SDD+'}">- Suy DD thể gầy còm nặng</span>
                                                        </td>
                                                        <td>
                                                            <i class="fa fa-check-square-o icon-checkbox color-red ng-scope" ng-if="vm.bmiResultToCode(value,'conclusion') == 'SDD+'"></i>

                                                            <i class="fa fa-square-o icon-checkbox color-green ng-scope" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'SDD+')" style=""></i>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'BP'}">- Béo phì</span>
                                                        </td>
                                                        <td>
                                                            <i class="fa fa-check-square-o icon-checkbox color-red ng-scope" ng-if="vm.bmiResultToCode(value,'conclusion') == 'BP'"></i>

                                                            <i class="fa fa-square-o icon-checkbox color-green ng-scope" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'BP')" style=""></i>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="myModalMG" class="modal fade myModalMG" role="dialog">
        <div class="modal-dialog" style="max-width: 1000px">
            <div class="modal-content">
                <div class="modal-header" style="padding: 1rem; border-top-left-radius: .3rem;border-top-right-radius: .3rem;">
                    <button type="button" class="close print-hidden" data-dismiss="modal">&times;</button>
                    <div>
                        <h3 style="font-size: 21px; font-family: sans-serif;">THEO DÕI THỂ LỰC HỌC SINH</h3></div>
                        <table style="width:100%; margin-left:15px;">
                            <tr>
                                <td>
                                    Cơ sở GDMN: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{$CFG.schoolName}}</span>
                                </td>
                                <td>
                                    <button style="right: 20px; float: right; position: relative;" class="btn btn-secondary btn-sm ml-auto ng-scope print-hidden" id="printButtonMG">
                                    <i class="btn-icon btn-reload mdi mdi-printer"></i>
                                    In
                                </button></td>
                            </tr>
                            <tr>
                                <td>
                                    Lớp: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.course.name}}</span>
                                </td>
                                <td>
                                    Năm học: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{vm.filters.school_year}} - {{vm.filters.school_year+1}}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Tên: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.first_name}} {{student.last_name}}</span>
                                </td>
                                <td>
                                    Ngày sinh: <span class="tl_name" style="color: #69aa46; font-size: 20px; font-weight: bold;">{{student.birth_date | date: 'dd/MM/yyyy'}}
                                </td>
                            </tr>
                        </table>
                    </div>
                <div class="modal-body" style="padding: 0px;">
                    <div class="content_danhgia">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-12 pr-5 col-md-12 col-xs-offset-12 " ng-repeat="(key,value) in student.histories | filter:hasHistory">
                                        <p ng-if="key==3" style="break-after: page;"></p>
                                        <div class="row">
                                            <table style="width:100%;">
                                                <td style="width:33%; border: 1px solid #dee2e6 !important; padding:10px !important;">
                                                    <p>
                                                        <span class="tl_danhgia pl-2" style="border-left: 5px solid #26c6da; color: #26c6da; font-weight: bold; margin-bottom: 15px; padding-left:5px !important;">Lần {{ key + 1 }}</span><br/>
                                                         <span class="t-bold pl-2" style="font-weight:bold;">Tháng {{ value.month }} năm {{ (parseInt(value.year) )}}</span>
                                                    </p>
                                                </td>
                                                <td style="width:67%; border: 1px solid #dee2e6 !important; padding:10px !important;">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <p><b style="color: palevioletred;">Thể lực :</b></p>
                                                            <p>Chiều cao : <b>{{ value.height ? value.height  + ' cm' : 'Không đo' }}</b> ; Cân nặng : <b>{{ value.weight ? value.weight  + ' kg' : 'Không đo' }}</b> ; BMI : <b>{{value.bmi}}</b></p>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <p><b style="color: palevioletred;">Tình trạng dinh dưỡng :</b></p>
                                                            <div class="row">
                                                                <table style="width:100%; margin-left:15px;">
                                                                    <tr>
                                                                        <td style="width:30%">
                                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'BT'}">- Bình thường</span>
                                                                        </td>
                                                                        <td style="width:10%">
                                                                            <input type="checkbox" checked ng-if="vm.bmiResultToCode(value,'conclusion') == 'BT'" style="opacity:1 !important; left:auto;"/>

                                                                            <input type="checkbox" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'BT')" style="opacity:1 !important; left:auto;" />
                                                                        </td>
                                                                        <td style="width:50%">
                                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'SDD'}">- Suy DD thể gầy còm</span>
                                                                        </td>
                                                                        <td style="width:10%">
                                                                            <input type="checkbox" checked ng-if="vm.bmiResultToCode(value,'conclusion') == 'SDD'" style="opacity:1 !important; left:auto;"/>

                                                                            <input type="checkbox" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'SDD')" style="opacity:1 !important; left:auto;" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'TC'}">- Thừa cân</span>
                                                                        </td>
                                                                        <td>
                                                                            <input type="checkbox" checked ng-if="vm.bmiResultToCode(value,'conclusion') == 'TC'" style="opacity:1 !important; left:auto;"/>

                                                                            <input type="checkbox" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'TC')" style="opacity:1 !important; left:auto;" />
                                                                        </td>
                                                                        <td>
                                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'SDD+'}">- Suy DD thể gầy còm nặng</span>
                                                                        </td>
                                                                        <td>
                                                                            <input type="checkbox" checked ng-if="vm.bmiResultToCode(value,'conclusion') == 'SDD+'" style="opacity:1 !important; left:auto;"/>

                                                                            <input type="checkbox" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'SDD+')" style="opacity:1 !important; left:auto;" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <span ng-class="{'t-bold' : vm.bmiResultToCode(value,'conclusion') == 'BP'}">- Béo phì</span>
                                                                        </td>
                                                                        <td>
                                                                            <input type="checkbox" checked ng-if="vm.bmiResultToCode(value,'conclusion') == 'BP'" style="opacity:1 !important; left:auto;"/>

                                                                            <input type="checkbox" ng-if="!(vm.bmiResultToCode(value,'conclusion') == 'BP')" style="opacity:1 !important; left:auto;" />
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </div>
                                                        <ng-template ng-if="key == 0">
                                                            <div class="col-md-12">
                                                                <p><b style="color: palevioletred;">Huyết áp :</b> <b>{{ value.blood_pressure_min ? value.blood_pressure_min  + '/ mmHg' : 'Chưa đo' }}</b></p>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <p><b style="color: palevioletred;">Nhịp tim :</b> <b>{{ value.beat_heart ? value.beat_heart  + ' lần / phút' : 'Chưa đo' }}</b></p>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <p><b style="color: palevioletred;">Thị lực :</b></p>
                                                                <p>- Không kính : Mắt phải : <b>{{ value.eye_sight_no_glasses_right ? value.eye_sight_no_glasses_right : 'Chưa đo' }}</b> ; Mắt trái : <b>{{ value.eye_sight_no_glasses_left ? value.eye_sight_no_glasses_left : 'Chưa đo' }}</b></p>
                                                                <p>- Có kính : Mắt phải : <b>{{ value.eye_sight_has_glasses_right ? value.eye_sight_has_glasses_right : 'Chưa đo' }}</b> ; Mắt trái : <b>{{ value.eye_sight_has_glasses_left ? value.eye_sight_has_glasses_left : 'Chưa đo' }}</b></p>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                </td>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    @media print {
        .print-hidden{
            display: none;
            height: 0;
        }
        

        html, body {
            height:100%; 
            margin: 0 !important; 
            padding: 0 !important;
            overflow: hidden;
        }
    }
    .cando_selects:not(:first-child) {
        margin-top : 20px;
    }
    b, .t-bold {
        font-weight: bold !important
    }
    .checkbox {
        position: relative !important;
        left: 0px !important;
        top: 0px !important;
        opacity: 100 !important;
    }

    .file-input-custom {
        display: inline-block;
        position: relative;
        font-size: 16px;
        color: #333;
        padding: 10px;
        cursor: pointer;
        border: 2px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
    }
    /* Style for hiding the default file input */
    .custom-file-input input {
        /*display: none;*/
    }
</style>
<!-- <div id="tbl_{{ $config->module }}"></div> -->
<!-- <script src="{{ asset('js/'.$options->module.'.js?_=').rand() }}"></script> -->
<script type="text/ng-template" id="exports_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xuất báo cáo cân đo học sinh</h3>
    </div>
    <form name="$ctrl.exportForm" ng-submit="$ctrl.exports()" autocomplete="off" spellcheck="false">
        <div class="modal-body">
            <p class="input-group">
                <input type="text" class="form-control" id="txt_date_export" readonly="readonly" uib-datepicker-popup="dd/MM/yyyy" ng-model="$ctrl.date" is-open="$ctrl.opened" close-text="Close"/>
                <span class="input-group-btn">
                    <button type="button" class="btn btn-secondary p-2" ng-click="$ctrl.open()" style="padding: 0.8rem !important;">
                      <i class="fa-svg-icon">
                        <svg width="1792" height="1792" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M192 1664h288v-288h-288v288zm352 0h320v-288h-320v288zm-352-352h288v-320h-288v320zm352 0h320v-320h-320v320zm-352-384h288v-288h-288v288zm736 736h320v-288h-320v288zm-384-736h320v-288h-320v288zm768 736h288v-288h-288v288zm-384-352h320v-320h-320v320zm-352-864v-288q0-13-9.5-22.5t-22.5-9.5h-64q-13 0-22.5 9.5t-9.5 22.5v288q0 13 9.5 22.5t22.5 9.5h64q13 0 22.5-9.5t9.5-22.5zm736 864h288v-320h-288v320zm-384-384h320v-288h-320v288zm384 0h288v-288h-288v288zm32-480v-288q0-13-9.5-22.5t-22.5-9.5h-64q-13 0-22.5 9.5t-9.5 22.5v288q0 13 9.5 22.5t22.5 9.5h64q13 0 22.5-9.5t9.5-22.5zm384-64v1280q0 52-38 90t-90 38h-1408q-52 0-90-38t-38-90v-1280q0-52 38-90t90-38h128v-96q0-66 47-113t113-47h64q66 0 113 47t47 113v96h384v-96q0-66 47-113t113-47h64q66 0 113 47t47 113v96h128q52 0 90 38t38 90z"/></svg>
                      </i>
                    </button>
                </span>
            </p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit" ng-disabled="$ctrl.exportForm.$invalid">Xuất</button>
        </div>
    </form>
</script>
<script type="text/ng-template" id="choose_month_modal_content_2.html">
    <div class="modal-header">
        <h3 class="modal-title">Xem báo cáo theo dõi cân đo theo kì (mẫu tách trang)</h3>
    </div>
    <div class="modal-body">
        <div class="row" style="margin-top : 20px">
            <div class="col-md-12 " style="border-right: thin solid #ccc">
                <div class="row cando_selects" style="padding-left: 10px">
                    <div class="col-md-8">
                    <select class="form-control custom-select" ng-model = "$ctrl.month">
                        <option value="0">Chọn kì học</option>
                        <option value="9">Kì I</option>
                        <option value="1">Kì II</option>
                    </select>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary" style="" ng-click="$ctrl.show()" ng-disabled="!$ctrl.month">Xuất báo cáo</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
    </div>
</script>
<script type="text/ng-template" id="choose_month_modal_content_1.html">
    <div class="modal-header">
        <h3 class="modal-title">Xem BC-TH cân nặng - chiều cao</h3>
    </div>
    <div class="modal-body">
        <div class="row" style="margin-top : 20px">
            <div class="col-md-12 " style="border-right: thin solid #ccc">
                <div class="row cando_selects" ng-repeat="x in [].constructor(5) track by $index" style="padding-left: 10px">
                    <div class="col-ms-4" style="line-height: 38px">Lần {{ $index + 1 }}</div>
                    <div class="col-md-8">
                        <select class="form-control custom-select" ng-change = "$ctrl.changeMonth($index + 1,'month_selected','error')" ng-model = "$ctrl.month_selected[$index + 1]" ng-options="month.id as month.title for month in $ctrl.months">
                    </select>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="row" >
            <div class="col-md-12" ng-if="$ctrl.error" style=" color :red; text-align: center;">
                Lựa chọn tháng không hợp lệ
            </div>
            <div class="col-md-12" ng-if="!$ctrl.error"></div>
        </div>
    </div>

    <div class="modal-footer">
        <button class="btn btn-primary" style="" ng-click="$ctrl.show(1)" ng-disabled="$ctrl.error">Xuất báo cáo</button>
        <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
    </div>
</script>
<script type="text/ng-template" id="choose_month_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xem báo cáo theo dõi thể lực</h3>
    </div>
    <!-- <form name="$ctrl.exportForm" ng-submit="$ctrl.exports()" autocomplete="off" spellcheck="false"> -->
        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>Tháng theo dõi thể lực của trẻ</strong>
                </div>
                <div class="col-md-6">
                    <strong>Tháng khám sức chuyên khoa</strong>
                </div>
            </div>
            
            <div class="row" style="margin-top : 20px">
                <div class="col-md-6 " style="border-right: thin solid #ccc">
                    <div class="row cando_selects" ng-repeat="x in [].constructor(5) track by $index" style="padding-left: 10px">
                        <div class="col-ms-4" style="line-height: 38px">Lần {{ $index + 1 }}</div>
                        <div class="col-md-8">
                            <select class="form-control custom-select" ng-change = "$ctrl.changeMonth($index + 1,'month_selected','error')" ng-model = "$ctrl.month_selected[$index + 1]" ng-options="month.id as month.title for month in $ctrl.months">
                        </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row cando_selects" ng-repeat="x in [].constructor(3) track by $index" style="padding-left: 10px">
                        <div class="col-ms-4" style="line-height: 38px">Lần {{ $index + 1 }}</div>
                        <div class="col-md-8">
                            <select class="form-control custom-select" ng-change = "$ctrl.changeMonth($index + 1,'months_conclusion','error_conclusion')" ng-model = "$ctrl.months_conclusion[$index + 1]" ng-options="month.id as month.title for month in $ctrl.months">
                        </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-6" ng-if="$ctrl.error" style=" color :red; text-align: center;">
                    Lựa chọn tháng không hợp lệ
                </div>
                <div class="col-md-6" ng-if="!$ctrl.error"></div>
                <div class="col-md-6" ng-if="$ctrl.error_conclusion" style=" color :red; text-align: center;">
                    Lựa chọn tháng không hợp lệ
                </div>
            </div>
            
        </div>
        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary" style="width:100% !important" ng-click="$ctrl.show(1)" ng-disabled="$ctrl.error">Xem</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-primary" style="width:100% !important" ng-click="$ctrl.show(2)" ng-disabled="$ctrl.error_conclusion">Xem</button>
                </div>
            </div>
            
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
        </div>
    <!-- </form> -->
</script>
<script src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
<script type="text/javascript">
    $('#student-bmi').scroll(function() {
        var bmi_table = $('#student-bmi-table');
        
        var top = bmi_table.position().top;
        bmi_table.find('thead').css({top: top * -1});

        var left = bmi_table.position().left * -1-4;
        $('.table-bmi-dashboard--info .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--stt .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--detail .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--full-name .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--student-id .absolute').css({'margin-left': left});
    });

    document.getElementById("printButton").onclick = function () {
        if ($('.myModal').is(':visible')) {
            var modalId = $(event.target).closest('.myModal').attr('id');
            $('body').css('visibility', 'hidden');
            $("#" + modalId).css('visibility', 'visible');
            $('#' + modalId).removeClass('myModal');
            window.print();
            $('body').css('visibility', 'visible');
            $('#' + modalId).addClass('myModal');
        } else {
            window.print();
        }
    }

    document.getElementById("printButtonNT").onclick = function () {
        $('#printButtonNT').css('visibility', 'hidden');
        $('.close').css('opacity', '0');
        var content = document.getElementById('myModalNT').innerHTML;
        var printWindow = window.open('', '');
        printWindow.document.write('<html><head><style>body{font-family:"Roboto", sans-serif !important;}</style></head><body>');
        printWindow.document.write(content);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
        $('#printButtonNT').css('visibility', 'visible');
        $('.close').css('opacity', '0.5');
    }

    document.getElementById("printButtonMG").onclick = function () {
        $('#printButtonMG').css('visibility', 'hidden');
        $('.close').css('opacity', '0');
        var content = document.getElementById('myModalMG').innerHTML;
        var printWindow = window.open('', '');
        printWindow.document.write('<html><head><style>body{font-family:"Roboto", sans-serif !important;}</style></head><body>');
        printWindow.document.write(content);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
        $('#printButtonMG').css('visibility', 'visible');
        $('.close').css('opacity', '0.5');
    }

    $("#myModal1 .close").click(function(e) {
        $("#myModal").css("overflow-y","auto");
    })
    $("#myModalNT .close").click(function(e) {
        $("#myModal").css("overflow-y","auto");
    })
    $("#myModalMG .close").click(function(e) {
        $("#myModal").css("overflow-y","auto");
    })
</script>
<script>
    // Wait for the document to be ready
    $(document).ready(function () {
        // Attach a click event to the button
        $("#openModalBtn").click(function () {
            // Show the modal
            $("#excelModal").modal("show");
        });

        $('#excelFileInput').on('change', function(event) {
            // vm.uploadFileExcel()
        });
    });

    // Function to handle the Excel upload (assuming you have it)
    function uploadExcel() {
        // Add your code for Excel upload here
        console.log("Uploading Excel...");
    }
</script>
