$.unit_food_detail = {
    module: 'unit_food_detail',
    nutritions: null,
    foodtypes: null,
    measures: {},
    init: function(measures) {
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        self.measures = measures;
        var fields = [
            { field:'ck', checkbox: true }
        ];
        if($CFG.is_vin) {
            fields.push({ title:'Mã chung', field:'vin_together', width:80, sortable:true });
            fields.push({ title:'Mã hàng', field:'vin_id', width:80, sortable:true });
        }
        fields.push({ title:'Tên thực phẩm', field:'name', width:250, sortable:true });
        fields.push({ title:'Mã TP', field:'food_id', width:60, sortable:true, formatter: function(value,row){
                var html = '';
				style = (row.parent_id>0)? 'style="color:orange;"': '';
                if(row.is_static) {
                    html = '<span title="TP của viện dinh dưỡng">' + row.food_id + ' <span class="glyphicon glyphicon-ok color-green" '+style+'></span></span>';
                } else {
                    html = '<span title="TP tự thêm">' + row.food_id + ' <span class="glyphicon glyphicon-grain" '+style+' title="ParentID: '+row.parent_id+'"></span></span>';
                }
                return html;
            } });
        fields.push({ title:'Đơn giá', field:'price', width:70, sortable:true, formatter: function(value,row){
            var html = value;
            if(row.is_priceByDay) {
                html = `<span class="clrOrange" title="Đơn giá theo ngày">${value}</span>`;
            }
            return html;
        } });
        fields.push({ title:'ĐVT', field:'measure_id', width:50, sortable:true, formatter: function(value,row){
            $.each($.unit_food_detail.measures,function(index,item) {
                if(item.id == value) {
                    value = item.name;
                    return;
                }
            })
            return value;
        } });
        fields.push({ title:'Quy đổi gam (hoặc ml)', field:'gam_exchange', width:100, sortable:true });
        fields.push({ title:'Động vật', field:'is_meat', width:80, sortable:true, formatter: function(value,row){
            var html = '';
            if(value) {
                html = '<input type="checkbox" checked disabled>';
            }else{
                html = '<input type="checkbox" disabled>';
            }
            return html;
        } });
        fields.push({ title:'TP khô', field:'is_dry', width:60, sortable:true, formatter: function(value,row){
            var html = '';
            if(value) {
                html = '<input type="checkbox" checked disabled>';
            }else{
                html = '<input type="checkbox" disabled>';
            }
            return html;
        } });
		fields.push({ title:'P', field:'protein', width:50, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
		fields.push({ title:'L', field:'fat', width:50, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
		fields.push({ title:'G', field:'sugar', width:50, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
		fields.push({ title:'Calo', field:'calo', width:70, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
        fields.push({ title:'Canxi', field:'canxi', width:50, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
        fields.push({ title:'B1', field:'b1', width:50, sortable:true, formatter: function(value,row){
            var html = value;
            return html;
        } });
        /*fields.push({ title:'Rau quả', field:'is_veget', width:80, sortable:true, formatter: function(value,row){
            var html = '';
            if(value) {
                html = '<input type="checkbox" checked disabled>';
            }else{
                html = '<input type="checkbox" disabled>';
            }
            return html;
        } });*/
        //fields.push({ title:'Tồn tối thiểu', field:'min_inventory', width:100, sortable:true });
        fields.push({ title:'HS thải bỏ (%)', field:'extrude_factor', width:80, sortable:true });
        //fields.push({ title:'Hệ số thải bỏ (g)', field:'extrude_factor_g', width:100, sortable:true });
        fields.push({ title:'Giá theo ngày', field:'price_by_day', width:100, sortable:true, formatter: function(value,row){
            var color = '';
            if(row.has_priceByDay) {
                color = 'color:orange;'
            }
            var html = `<div style="text-align:center;`+color+`"><i class="fa fa-eye mcur" onclick="$.unit_food_detail.showListPriceDay(${row.food_id},'${row.food_name}')"></i></div>`;
            return html;
        } });
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [fields],
            {
                view: detailview,
                detailFormatter:function(index,row){
                    return '<div class="detail-view-content"></div>';
                },onExpandRow: function(index,row){
                    var nutritions_data = row.nutritions;
                    if(!$.unit_food_detail.nutritions) {
                        process($CFG.project+'/'+self.module+'/nutritions',{},function(resp){
                            $.unit_food_detail.nutritions = resp;
                            $.unit_food_detail.buildDetailView(index, row, nutritions_data, $.unit_food_detail.nutritions);
                        },null,false);
                    }else{
                        $.unit_food_detail.buildDetailView(index, row, nutritions_data, $.unit_food_detail.nutritions);
                    }
                },onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }
			}
        );
        $.unit_food_detail.initAngular();
    },initAngular: function(){
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                scope.sys = window.sysConfigs;
                if(scope.sys.configs.tptruong_show_canxib1 == 1){
					$('#tbl_unit_food_detail').datagrid('showColumn','calo');
                    $('#tbl_unit_food_detail').datagrid('showColumn','canxi');
                    $('#tbl_unit_food_detail').datagrid('showColumn','b1');
					$('#tbl_unit_food_detail').datagrid('showColumn','protein');
					$('#tbl_unit_food_detail').datagrid('showColumn','fat');
					$('#tbl_unit_food_detail').datagrid('showColumn','sugar');
                }else{
					$('#tbl_unit_food_detail').datagrid('hideColumn','calo');
                    $('#tbl_unit_food_detail').datagrid('hideColumn','canxi');
                    $('#tbl_unit_food_detail').datagrid('hideColumn','b1');
					$('#tbl_unit_food_detail').datagrid('hideColumn','protein');
					$('#tbl_unit_food_detail').datagrid('hideColumn','fat');
					$('#tbl_unit_food_detail').datagrid('hideColumn','sugar');
                }
                $.unit_food_detail.scope = scope;
                //cấu hình lọc thực phẩm theo vi chất
                scope.micronutrients_config = [{
                    label: 'P (Đạm) = 0',
                    value: 'protein'
                },{
                    label: 'L (Béo) = 0',
                },{
                    label: 'G (Đường) = 0',
                    value: 'sugar'
                },{
                    label: 'Canxi = 0',
                    value: 'canxi'
                },{
                    label: 'B1 = 0',
                    value: 'b1'
                }];
                scope.unit_food_detail || (scope.unit_food_detail = {});
                scope.unit_food_detail.row = {};
                scope.unit_food_detail.keysearch_name_time = 0;
                scope.unit_food_detail.onChangeKeysearchName = function(keysearch_name){
                    if(scope.unit_food_detail.keysearch_name_time == 0){
                        scope.unit_food_detail.keysearch_name_time = 1;
                        setTimeout(function(){
                            if(scope.unit_food_detail.keysearch_name_time == 1){
                                $.unit_food_detail.doSearch();
                                scope.unit_food_detail.keysearch_name_time = 0;
                            }
                        }, 700);
                    }
                };
                scope.hidden_canxi_b1 = function(hidden){
                    if(hidden == 0) {
						$('#tbl_unit_food_detail').datagrid('showColumn','calo');
                        $('#tbl_unit_food_detail').datagrid('showColumn','canxi');
                        $('#tbl_unit_food_detail').datagrid('showColumn','b1');
						$('#tbl_unit_food_detail').datagrid('showColumn','protein');
						$('#tbl_unit_food_detail').datagrid('showColumn','fat');
						$('#tbl_unit_food_detail').datagrid('showColumn','sugar');
                    }else{
						$('#tbl_unit_food_detail').datagrid('hideColumn','calo');
                        $('#tbl_unit_food_detail').datagrid('hideColumn','canxi');
                        $('#tbl_unit_food_detail').datagrid('hideColumn','b1');
						$('#tbl_unit_food_detail').datagrid('hideColumn','protein');
						$('#tbl_unit_food_detail').datagrid('hideColumn','fat');
						$('#tbl_unit_food_detail').datagrid('hideColumn','sugar');
                    }
                    // console.log(hidden);
                };
                scope.unit_food_detail.onChangePT = function(){
                    if(scope.unit_food_detail.row.gam_exchange != ""){
                        scope.unit_food_detail.row.extrude_factor_g = (scope.unit_food_detail.row.extrude_factor*scope.unit_food_detail.row.gam_exchange)/100;
                    }else{
                        alert("Cột quy đổi ra gam không được để trống ");
                    }
                };
                scope.unit_food_detail.onChangeG = function(){
                    if(scope.unit_food_detail.row.gam_exchange != ""){
                        scope.unit_food_detail.row.extrude_factor = (100*scope.unit_food_detail.row.extrude_factor_g)/scope.unit_food_detail.row.gam_exchange;
                    }else{
                        alert("Cột quy đổi ra gam không được để trống ");
                    }
                };
                scope.unit_food_detail.onChangeEG = function(){
                    scope.unit_food_detail.row.extrude_factor_g = (scope.unit_food_detail.row.extrude_factor*scope.unit_food_detail.row.gam_exchange)/100;
                };
                scope.unit_food_detail.onchangeStatus = function(status) {
                    var queryParams = $('#tbl_unit_food_detail').datagrid('options').queryParams;
                    queryParams.status = status;
                    $('#tbl_unit_food_detail').datagrid({"load": queryParams});
                };
                scope.unit_food_detail.onchangeStatic = function (is_static) {
                    var queryParams = $('#tbl_unit_food_detail').datagrid('options').queryParams;
                    queryParams.is_static = is_static;
                    $('#tbl_unit_food_detail').datagrid({'load': queryParams});
                };
                // lọc thực phẩm theo vi chất dinh dưỡng
                scope.unit_food_detail.micronutrients = {};
                scope.unit_food_detail.onTickMicronutrients = function (micronutrients) {
                    if(scope.unit_food_detail.micronutrients.hasOwnProperty(micronutrients))
                        delete scope.unit_food_detail.micronutrients[micronutrients];
                    else
                        scope.unit_food_detail.micronutrients[micronutrients] = micronutrients;

                    var arr = Object.keys(scope.unit_food_detail.micronutrients).map(function(key) {
                        return scope.unit_food_detail.micronutrients[key];
                    });

                    var queryParams = $('#tbl_unit_food_detail').datagrid('options').queryParams;
                    queryParams.micronutrients = arr;
                    $('#tbl_unit_food_detail').datagrid({'load': queryParams});

                };
                scope.unit_food_detail.handleDeactive = function(status) {
                    var self = this;
                    var ids = [];
                    var rows_selected = {};
                    $.each($('#tbl_unit_food_detail').datagrid('getSelections'), function(index,row){
                        ids.push(row.id);
                        rows_selected[row.id] = row;
                    });
                    if(ids.length == 0) {
                        $.messager.alert('Thông báo','Hãy chọn một dòng!');
                        return;
                    }
                    var msg = '<div style = "font-size: 14px"> Bạn có chắc chắn muốn ngừng kích hoạt các thực phẩm đã chọn?</div>';
                    if (status) {
                        msg = '<div style = "font-size: 14px"> Bạn có chắc chắn muốn kích hoạt hoạt các thực phẩm đã chọn?</div>';
                    }
                    $.messager.confirm('Xác nhận', msg, function(r) {
                        if (r) {
                            var url = $CFG.project + '/unit_food_detail/deactive';
                            process(url, {ids: ids.join(','), status: status}, function (resp) {
                                if (resp.result == 'success') {
                                    $('#tbl_unit_food_detail').datagrid('reload');
                                }
                            }, function () {
                                // TO DO
                            }, false);
                        }
                    });
                };
                scope.unit_food_detail.handleDeactiveAll = function(status) {
                    var captchaForm = $CFG.dialog_captcha('restore');
                    var msg = '<div style = "font-size: 14px"> - Bạn có chắc chắn muốn ngừng kích hoạt tất cả các thực phẩm?</div>' + captchaForm;
                    if(status)
                        msg = '<div style = "font-size: 14px"> - Bạn có chắc chắn muốn kích hoạt tất cả các thực phẩm?</div>' + captchaForm;

                    $.messager.confirm('Xác nhận', msg, function(r){
                        if (r){
                            var captcha = $('input[name="restore_captcha"]').val();
                            var url = $CFG.project + '/unit_food_detail/deactiveAll';

                            data = {
                                captcha: captcha,
                                status: status,
                            };

                            process(url, {captcha:captcha, status: status}, function(resp){
                                if (resp.result == 'success') {
                                    $('#tbl_unit_food_detail').datagrid('reload');
                                } else {
                                    scope.unit_food_detail.handleDeactiveAll(status);
                                    $.messager.alert('Thông báo',resp.errors);
                                }
                            },function(){
                                // TO DO
                            },false);
                        }
                    });
                }
                scope.unit_food_detail.handleDeactiveNoUse = function(status) {
                    var captchaForm = $CFG.dialog_captcha('restore');
                    var msg = '<div style = "font-size: 14px"> - Bạn có chắc chắn muốn bỏ kích hoạt tất cả các thực phẩm chưa từng được sử dụng trong Nhập kho, Món ăn, Thực đơn mẫu, CĐKP ko?</div>' + captchaForm;
                    if(status) {
                        msg = '<div style = "font-size: 14px"> - Bạn có chắc chắn muốn kích hoạt tất cả các thực phẩm chưa từng được sử dụng trong Nhập kho, Món ăn, Thực đơn mẫu, CĐKP ko?</div>' + captchaForm;
                    }
    
                    $.messager.confirm('Xác nhận', msg, function(r){
                        if (r){
                            var captcha = $('input[name="restore_captcha"]').val();
                            var url = $CFG.project + '/unit_food_detail/deactiveNoUse';
    
                            data = {
                                captcha: captcha,
                                status: status,
                            };
    
                            process(url, {captcha:captcha, status: status}, function(resp){
                                if (resp.result == 'success') {
                                    $('#tbl_unit_food_detail').datagrid('reload');
                                } else {
                                    scope.unit_food_detail.handleDeactiveNoUse(status);
                                    $.messager.alert('Thông báo',resp.errors);
                                }
                            },function(){
                                // TO DO
                            },false);
                        }
                    });
                }
                scope.unit_food_detail.savePriceByDay = function() {
                    var data = {
                        food_id: scope.selected_row.id,
                        name: scope.selected_row.name,
                        day: scope.day,
                        price: scope.price
                    };
                    var url = $CFG.project+'/unit_food_detail/savePriceByDay';
                    process(url, data, function(resp){
                        if (resp.result == 'success' && resp.data) {
                            if (resp.type == 'create') {
                                scope.data.push(resp.data);
                            } else {
                                angular.forEach(scope.data, function(row, idx) {
                                    if(row.food_id == resp.data.food_id && row.day == resp.data.day) {
                                        scope.data[idx].price = resp.data.price;
                                    }
                                });
                            }
                            scope.day = null;
                            scope.price = null;
                            $('#tbl_unit_food_detail').datagrid('reload');
                        }
                    });
                }
                scope.unit_food_detail.delPriceByDay = function(id) {
                    var url = $CFG.project+'/unit_food_detail/delPriceByDay';
                    process(url,{id: id},function(resp){
                        if (resp.result == 'success') {
                            $("#tbl_unit_food_detail").datagrid('reload');
                            angular.forEach(scope.data, function(row, idx) {
                                if(row.id == id) {
                                    scope.data.splice(idx, 1);
                                }
                            });
                        }
                    },function(){
                        // TO DO
                    },false); 
                }

                scope.unit_food_detail.changePriceByDayStatus = function(id) {
                    var url = $CFG.project+'/unit_food_detail/changePriceByDayStatus';
                    var status = 1;
                    if (!$('#price_by_day_id_'+id).prop('checked')) {
                        status = 0;
                    }
                    process(url,{id: id, status: status},function(resp){
                        if (resp.result == 'success') {
                            $("#tbl_unit_food_detail").datagrid('reload');
                        }
                    },function(){
                        // TO DO
                    },false); 
                }

                scope.unit_food_detail.addExcelForm = function(type) {
                    scope.random = Math.random();
                    scope.upload_captcha = null;
                    scope.currentForm = type;

                    if (scope.currentForm == 'foodPriceByDay') {
                        title = 'Tải lên file mẫu Giá TP theo ngày';
                    } else if (scope.currentForm == 'foodsWithPrice') {
                        title = 'Tải lên file các thực phẩm có đơn giá';
                    }

                    $.dm_datagrid.showAddForm(
                        {
                            title: title,
                            draggable: true,
                            size: size.small,
                            showButton: false,
                            scope: scope,
                            content: function(element){
                                loadForm($CFG.project+'/unit_food_detail','addExcelForm', {}, function(resp){
                                    $.unit_food_detail.angular(element,resp,function(scope){
                                    });
                                })
                            }
                        }
                    );
                }

                scope.unit_food_detail.downloadFoodsWithPrice = function() {
                    downloadExcelFile($CFG.remote.base_url+'/doing/'+$CFG.project+'/unit_food_detail/downloadExcelFoodsWithPrice', {});
                }

                scope.unit_food_detail.upload = function() {
                    var files = $('.upload-excell').prop('files')[0];
                    if(files == undefined) {
                        $.messager.alert('Lỗi', 'Bạn chưa chọn tệp excel!');
                        return;
                    }
                    var captcha = scope.upload_captcha;
                    var form_data = new FormData();
                    form_data.append('files', files);
                    form_data.append('captcha', captcha);

                    let url = '';

                    if (scope.currentForm == 'foodPriceByDay') {
                        url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/unit_food_detail/addExcelPriceByDay';
                    } else if (scope.currentForm == 'foodsWithPrice') {
                        url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/unit_food_detail/uploadExcelFoodsWithPrice';
                    }

                    $.post({
                        url: url,
                        data: form_data,
                        contentType: false,
                        processData: false,
                        success: function (resp) {
                            if(resp.result == 'success') {
                                $("#tbl_unit_food_detail").datagrid('reload');
                                dialogClose();
                            } else {
                                $.messager.alert('Lỗi', resp.errors);
                            }
                        },
                        error: function (resp) {
                            $.messager.alert('Lỗi', 'Đã có lỗi xảy ra. Vui lòng thử lại!');
                            $('.upload-excell').val('');
                        },
                        complete: function(){
                            scope.$apply( function() {
                                scope.upload_captcha = null;
                                scope.random = Math.random();
                            });
                        }
                    });
                }
            });
        });
    }, cleanSearch: function(){
        angular.element($("#header-search")).scope().$apply(function(scope){
            scope.keysearch_name = '';
            scope.keysearch_foodtype_id = '';
            $('input#foodtype_id').combobox('clear');
            var queryParams = $('#tbl_unit_food_detail').datagrid('options').queryParams;
            if(queryParams.filterRules) {
                delete queryParams.filterRules;
                delete queryParams.filter_type;
                $('#tbl_unit_food_detail').datagrid('load',queryParams);
            }
        });
    }, buildDetailView: function(index, row, nutritions_data, nutritions_define){
        console.log(index, row, nutritions_data, nutritions_define);
        var ddv = $('#tbl_unit_food_detail').datagrid('getRowDetail',index).find('div.detail-view-content');
        if($(ddv).attr('opened')) {
            return;
        }
        $(ddv).attr('opened','true');
        var html = [
            `<div class="form-group">
                <div class="col-md-6">
                    <label class="col-md-4 control-label">Nhà cung cấp</label>
                    <div class="col-md-8">
                        <input class="form-control" style="" value="`+row.supplier+`" disabled="">
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="col-md-4 control-label">Mã bao bì</label>
                    <div class="col-md-8">
                        <input class="form-control" style="" value="`+row.packaging+`" disabled="">
                    </div>
                </div>
            </div>`
        ];
        $.each(nutritions_define, function(i,nutr){
            if(!nutritions_data){
                nutritions_data = {}
            }
            html.push(
                `
                <div class="col-md-3">
                    <label class="col-md-6 control-label">`+nutr.name+`</label>
                    <div class="col-md-6">
                        <input class="form-control" disabled="true" style="max-width: 100px" value="`+(nutritions_data[nutr.define] || '')+`">
                    </div>
                </div>
                `);
        });
        html = html.join('');
        ddv.append(html);
        $('#tbl_unit_food_detail').datagrid('fixDetailRowHeight',index);
    }, restorDefault: function() { 
    	var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = ids.length >= 2 ? $CFG.dialog_captcha('restore') : '';
        var msg = '<div style = "font-size: 14px"> - Hành động này có thể làm mất các dữ liệu đã thay đổi.<br/> - Chắc chắn khôi phục dữ liệu gốc ?</div>' + captchaForm;

        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="restore_captcha"]').val();
                var url = $CFG.project+'/'+self.module+'/restore_default';
                process(url,{ids: ids.join(','), captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.unit_food_detail.restorDefault();
                        $.messager.alert('Thông báo',resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    },  showAddForm: function(callback) { 
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'add',
                title:'Thêm mới',
                showButton: true,
                size: size.wide,
                // content: function(element){
                //  loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
                //      $(element).html(resp);
                //  })
                // }
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
                        $.unit_food_detail.angular(element,resp,function(){
                            
                        });
                    })
                }
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    },showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
					// content: function(element){
					// 	loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
					// 		$(element).html(resp);
					// 	})
					// }
                    content: function(element){
                        process($CFG.project+'/'+self.module+'/editForm', {id: row.id}, function(resp){
                            $.unit_food_detail.angular(element,resp.html,function($scope){
                                // console.log(resp.row);
                                $scope.unit_food_detail.row = resp.row;
                            });
                        },false,false)
                    }
				},
				function(resp){
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Phải chọn một dòng!');
	    }
       
    },showListPriceDay: function(food_id, food_name) {
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                title:'Bảng giá thực phẩm theo ngày',
                draggable: true,
                size: size.wide,
                showButton: false,
                content: function(element){
                    process($CFG.project+'/'+self.module+'/listPriceByDay', {food_id: food_id}, function(resp){
                        $.unit_food_detail.angular(element,resp.html,function($scope){
                            $scope.data = resp.data;
                            $scope.selected_row = {id: food_id, name: food_name};
                        });
                    },false,false)
                }
            },
            function(resp){
                
            }
        );
    },doSearch: function() {
        var self = this;
        $.dm_datagrid.doSearch('tbl_'+self.module,{name:'contains',foodtype_id:"equal"},'and');
    },angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        });
    } 
}
