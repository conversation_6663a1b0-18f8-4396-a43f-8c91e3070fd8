$(function () {
    angular_app.controller('detail_bmiController', ['$scope', '$localStorage','studentBmiServices','$routeParams',
        function($scope, $localStorage, studentBmiServices, $routeParams) {
            $scope.storage = $localStorage;
            $scope.sys.module.title = 'Cân đo học sinh';
            $scope.api_url = $scope.$CFG.remote.base_url;
            $scope.student = {};

            $scope.bmiData = {
                id: 'bmi-chart',
                x_title: 'Tháng',
                y_title: 'Chỉ số BMI',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.heightData = {
                id: 'height-chart',
                x_title: 'Tháng',
                y_title: 'Chiều cao (cm)',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.weightData = {
                id: 'weight-chart',
                x_title: 'Tháng',
                y_title: '<PERSON><PERSON> nặng (kg)',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.getStudentById = function(){
                return studentBmiServices.getStudentById($scope, function (student) {

                    $scope.student = student;

                    //draw chart by BMI
                    $scope.bmiData.xAxis = $scope.generateXAxis($scope.student);
                    $scope.bmiData.series_data = $scope.generateSeriesData($CFG.metaData.ages, $scope.student, $scope.bmiData.xAxis, 'bmi');
                    $scope.bmiData.yAxis = $scope.generateYAxis($scope.bmiData.series_data.SDD3.data, $scope.bmiData.series_data.TC3.data, $scope.student.histories);
                    $scope.generateSeriesDataTC3($scope.bmiData.series_data, $scope.bmiData.yAxis);
                    $scope.chart($scope.bmiData);

                    //draw chart by height
                    $scope.heightData.xAxis = $scope.generateXAxis($scope.student);
                    $scope.heightData.series_data = $scope.generateSeriesData($CFG.metaData.heights, $scope.student, $scope.heightData.xAxis, 'height');
                    $scope.heightData.yAxis = $scope.generateYAxisByHeight($scope.heightData.series_data.SDD3.data, $scope.heightData.series_data.TC3.data, $scope.student.histories);
                    $scope.generateSeriesDataTC3($scope.heightData.series_data, $scope.heightData.yAxis);
                    $scope.chart($scope.heightData);

                    //draw chart by weight
                    $scope.weightData.xAxis = $scope.generateXAxis($scope.student);
                    $scope.weightData.series_data = $scope.generateSeriesData($CFG.metaData.weights, $scope.student, $scope.weightData.xAxis, 'weight');
                    $scope.weightData.yAxis = $scope.generateYAxisByWeight($scope.weightData.series_data.SDD3.data, $scope.weightData.series_data.TC3.data, $scope.student.histories);
                    $scope.generateSeriesDataTC3($scope.weightData.series_data, $scope.weightData.yAxis);
                    $scope.chart($scope.weightData);
                });
            };
            $scope.init = function () {
                $scope.getStudentById();
            };

            $scope.chart = function (data) {
                Highcharts.chart(data.id, {
                    chart: {
                        type: 'area',
                    },
                    title: '',
                    xAxis: {
                        title: {
                            text: data.x_title
                        },
                        categories: data.xAxis,
                        gridLineWidth: 0.5,
                    },
                    yAxis: {
                        title: {
                            text: data.y_title
                        },
                        gridLineWidth: 0.5,
                        tickPositioner: function () {
                            var positions = [],
                                tick = (data.yAxis.min < 0) ? 0 : data.yAxis.min,
                                increment = data.yAxis.tickIncrement || 5;

                            if (data.yAxis.min !== null && data.yAxis.max !== null) {
                                for (tick; tick <= data.yAxis.max; tick += increment) {
                                    positions.push(tick);
                                }
                            }
                            return positions;
                        }
                    },
                    legend: {enabled: false},
                    tooltip: {
                        enabled: false,
                    },
                    exporting: false,
                    credits: false,
                    plotOptions: {
                        area: {
                            // stacking: 'percent',
                            lineColor: '#ffffff',
                            lineWidth: 1,
                            marker: {
                                enabled: false,
                                symbol: 'circle',
                                radius: 0,
                                states: {
                                    hover: {
                                        enabled: false
                                    }
                                }
                            }
                        },
                        series: {
                            lineWidth: 0,
                            states: {
                                inactive: {
                                    opacity: 1
                                }
                            }
                        }
                    },

                    series: [
                        data.series_data.TTC3_moreC3,
                        data.series_data.TC3,
                        data.series_data.TC2,
                        // data.series_data.BT,
                        data.series_data.SDD2,
                        data.series_data.SDD3,
                        data.series_data.student,

                    ]
                });
            };

            $scope.generateXAxis = function (student) {
                return _.map(student.histories, 'month_old');
            };

            $scope.hasHistory = function (item) {
                return item.weight || item.height || item.bmi || item.conclusion_text;
            };

            $scope.generateSeriesData = function(bmi, student, xAxis, chart_type){
                let arr_bmi = _.filter(bmi, function (o) {
                    return (o.gender == student.gender && (o.month >= xAxis[0] && o.month <= xAxis[xAxis.length-1]));
                });
                let data = {
                    SDD3: {name: 'SDD 3', index: 6, data:[]},
                    SDD2: {name: 'SDD 2', index: 5, data:[]},
                    // BT: {name: 'BT', data:[]},
                    TC2: {name: 'TC 2', index: 4,  data:[]},
                    TC3: {name: 'TC 3', index: 3, data:[]},
                    TTC3_moreC3: {name: '', index: 2, data:[]},
                    student: {name: 'student', type: 'scatter', data:[]},
                };

                //generate chart data of SDD & TC
                _.each(arr_bmi, function (value, key) {
                    data.SDD3.data.push(value.SD3neg);
                    data.SDD2.data.push(value.SD2neg);
                    // data.BT.data.push(value.SD1);
                    if(chart_type == 'bmi'){
                        data.TC2.data.push(value.SD1);
                        data.TC3.data.push(value.SD2);
                    }else{
                        data.TC2.data.push(value.SD2);
                        data.TC3.data.push(value.SD3);
                    }
                });

                //sort asc by bmi
                data.SDD3.data.sort(function(a, b){return a - b});
                data.SDD2.data.sort(function(a, b){return a - b});
                // data.BT.data.sort(function(a, b){return a - b});
                data.TC2.data.sort(function(a, b){return a - b});
                data.TC3.data.sort(function(a, b){return a - b});

                data.student.data = $scope.generateStudentData(student, chart_type);

                return data;
            };

            //generate student data
            $scope.generateStudentData = function (student, chart_type) {
                let student_data = [];
                if(chart_type == 'bmi'){
                    _.each(student.histories, function (o) {
                        student_data[o.month] = o.bmi;
                    });
                }
                if(chart_type == 'height'){
                    _.each(student.histories, function (o) {
                        student_data[o.month] = o.height;
                    });
                }
                if(chart_type == 'weight'){
                    _.each(student.histories, function (o) {
                        student_data[o.month] = o.weight;
                    });
                }
                let arr_data = [];
                for(let i=9; i<=18; i++){
                    if(student_data[i] == undefined && i > 12){
                        if(student_data[i%12] == undefined && (i%12 != 7) && (i%12 != 8)){
                            arr_data.push(null);
                        } else {
                            arr_data.push(student_data[i % 12] || null);
                        }
                    }else{
                        arr_data.push(student_data[i]||null);
                    }
                }
                return _.valuesIn(arr_data);
            };

            $scope.generateYAxis = function (SDD3, TC3, histories) {
                let min_bmi_student = _.minBy(histories, function(o) { return o.bmi; });
                let yAxis = {min:0, max:0};
                let min_yAxis = (min_bmi_student.bmi < SDD3[0])?min_bmi_student.bmi:SDD3[0];
                yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                let max_bmi_student = _.maxBy(histories, function(o) { return o.bmi; });
                let max_yAxis = (max_bmi_student.bmi > TC3[TC3.length-1])?max_bmi_student.bmi:TC3[TC3.length-1];

                yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                return yAxis;
            };

            //generate yAxis by height
            $scope.generateYAxisByHeight = function (SDD3, TC3, histories) {
                let yAxis = {min:0, max:0, tickIncrement: 10};
                let min_bmi_student = _.minBy(histories, function(o) { return o.height; });

                let min_yAxis = (min_bmi_student.height < SDD3[0])?min_bmi_student.height:SDD3[0];
                yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                let max_bmi_student = _.maxBy(histories, function(o) { return o.height; });
                let max_yAxis = (max_bmi_student.height > TC3[TC3.length-1])?max_bmi_student.height:TC3[TC3.length-1];
                yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;

                return yAxis;
            };

            //generate yAxis by weight
            $scope.generateYAxisByWeight = function (SDD3, TC3, histories) {
                let min_bmi_student = _.minBy(histories, function(o) { return o.weight; });
                let yAxis = {min:0, max:0};
                let min_yAxis = (min_bmi_student.weight < SDD3[0])?min_bmi_student.weight:SDD3[0];
                yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                let max_bmi_student = _.maxBy(histories, function(o) { return o.weight; });
                let max_yAxis = (max_bmi_student.weight > TC3[TC3.length-1])?max_bmi_student.weight:TC3[TC3.length-1];
                yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;

                return yAxis;
            };

            $scope.generateSeriesDataTC3 = function (seriesData, yAxis) {
                let tc3Data = [];
                for(let i = 0; i<=9; i++){
                    tc3Data.push(yAxis.max) ;
                }
                seriesData.TTC3_moreC3.data = tc3Data;
            }

        }]
    );
});