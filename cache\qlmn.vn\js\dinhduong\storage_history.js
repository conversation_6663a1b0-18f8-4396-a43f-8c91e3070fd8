$.storage_history = {
    module: 'storage_history',
    id: '', /*Mã project*/
    measures: null,
    warehouses: null,
    init: function(id,project) {
        var self = this;
        self.id = id;
        /*<PERSON><PERSON>i đ<PERSON>n vị t<PERSON>h và danh sach kho trước*/
        process($CFG.project+'/'+self.module+'/special_categories',{},function(resp){
            self.measures = resp.data.measures;
            self.warehouses = resp.data.warehouses;
            self.exports = resp.data.exports;
            self.datagrid();
        },null,false);
        $.storage_history.initAngular();
    },initAngular: function(){
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                $.storage_history.scope = scope;
                scope.storage_history || (scope.storage_history = {});
                scope.storage_history.opt_searching_warehouse = false;
                scope.storage_history.opt_searching_date = false;

                scope.storage_history.warehouseHistorySearch = function () {
                    var date_start = scope.storage_history.start;
                    var date_end = scope.storage_history.end;
                    var keysearch = scope.storage_history.keysearch;
                    var queryParams = $('#tbl_storage_history').datagrid('options').queryParams;

                    var pattern =/^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/;
                    if(pattern.test(date_start) && pattern.test(date_end))
                    {
                        queryParams.date_start = date_start;
                        queryParams.date_end = date_end;
                    }
                    else
                    {
                        queryParams.date_start = '';
                        queryParams.date_end = '';
                    }
                    queryParams.keysearch = keysearch;
                    $('#tbl_storage_history').datagrid({'load': queryParams});
                };
            });
        });
    }, datagrid: function(){
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
            urls.join('/'),
            self.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    { title:'Tên thực phẩm', field:'name',  width:250, sortable:true },
                    { title:'Mã thực phẩm', field:'food_id',  width:100, sortable:true },
                    { title:'Kho lưu', field:'warehouse_id',  width:100, sortable:true, formatter: function(value,row){
                        $.each(self.warehouses,function(index,item){
                            if(item.id == value){
                                value = item.name;
                                return;
                            }
                        })
                        return value;
                    },editor:{
                        type:'combobox',
                        options:{
                            valueField:'id',
                            textField:'name',
                            data: $.storage_history.warehouses,
                            required:true
                        }
                    } },
                    // { title:'Đơn giá', field:'price', width:100,  editor: 'numberbox' , sortable:true},
                    { title:'Đơn giá', field:'price', width:100,sortable:true, editor: 'numberbox', align: 'center', formatter: function(value,row,index){
                            value = digit_grouping(value);
                            return value;
                        } 
                    },
                    { title:'Số lượng', field:'soluong', width:100, align: 'center', editor: 'numberbox' , sortable:true ,formatter : function(value,row){
                        if ((row.type) == 2){
                            return '-'+value;
                        } else {
                            return value;
                        }
                    }},
                    { title:'Đơn vị tính', field:'measure_id',  width:100, formatter: function(value,row){
                        $.each(self.measures,function(index,item){
                            if(item.id == value){
                                value = item.name;
                                return;
                            }
                        })
                        return value;
                    } },
                    { title:'Ngày', field:'date', width:100,  editor: 'numberbox' , sortable:true},
                    { title:'Ghi chú', field:'note', width:100,  editor: 'numberbox' , sortable:true, formatter: function(value,row){
                        return value;
                    }},

                ],
],
{
    view: groupview,
        groupField: 'date',
    selectOnCheck: 'false',
    checkOnSelect: 'false',
    ctrlSelect: 'false',
    singleSelect: 'false',
    groupFormatter: function(id,rows){
        return rows[0].date + ': <i>' + rows.length+'</i>';
},onSelect: function(index,row) {
    if(row.quantity_used==0) {
        $('#tbl_storage').datagrid('beginEdit',index);
        // row.editing = true;
        $('#export_print').prop('disabled',false).css('opacity','1');
    }
},onBeforeEdit:function(index,row){
    row.editing = true;
    $(this).datagrid('refreshRow', index);
},onDblClickRow: function(index,row){

},onAfterEdit:function(index,row){
    row.editing = false;
    $(this).datagrid('refreshRow', index);
    $('#export_print').prop('disabled',true).css('opacity','0.3');
},
    onCancelEdit:function(index,row){
        row.editing = false;
        $(this).datagrid('refreshRow', index);
        $('#export_print').prop('disabled',true).css('opacity','0.3');
    },onLoadSuccess:function(data){
    $('#export_print').prop('disabled',true).css('opacity','0.3');
	setTimeout(function(){
		if($CFG.is_gokids==1) {
			$('.datagrid-view').height($('.datagrid-view').height() - 30);
			$('.datagrid-body').height($('.datagrid-body').height() - 30);
		}
	},1000);
},rowStyler:function(index,row){
    if ((row.type) == 1){
        return 'background-color:#fbf3c7;';
    }
    if ((row.type) == 2){
        return 'background-color:white;';
    }
}
}
);
}, cancelEditor:function(index){
    $('#tbl_storage').datagrid('cancelEdit', index);
}, saverow: function(index){
    var self = this;
    var item = {};
    var ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'warehouse_id'
    });
    item.warehouse_id = $(ed.target).combobox('getValue');
    ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'price'
    });
    item.price = $(ed.target).combobox('getText');
    ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'quantity'
    });
    item.quantity = $(ed.target).combobox('getText');
    var data = $('#tbl_storage').datagrid('getData');
    if(!data.rows){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var row = data.rows[index];
    if(!row){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    item.id = row.id;
    var action = $CFG.project+'/storage/edit';
    process(action, {data: item}, function(resp){
        if(resp.result == 'success') {
            $('#tbl_storage').datagrid('endEdit', index);
            // $.storage.cancelEditor(index);
        }
    });
}, addAction: function(id){
    if(!id) return;
    if($('.btn-add.add-new').css('opacity')+'' != '1'){
        return;
    }
    var self = this;
    var data = arrayToJson(getSubmitForm(id,true));
    var action = $CFG.project+'/storage/add';
    process(action, {data:data}, function(resp){
        if(resp.result == 'success') {
            $("#tbl_"+self.module).datagrid('reload');
            $('#ma_thuc_pham').combobox('clear');
            $('.btn-add.add-new').css('opacity',0);
        }
    });
}, doSearch: function(){
    setTimeout(function(){
        var scope = $.storage.scope;
        if(!scope){
            return;
        }
        var opt_search = {};
        if(scope.storage.opt_searching_warehouse){
            opt_search.warehouse_id = {value: $.storage.keysearch_warehouse_id,op:'equal'};
        }
        if(scope.storage.opt_searching_date){
            opt_search.date = {value: $.storage.keysearch_date,op:'equal'};
        }
        if(count(opt_search)>0){
            $.dm_datagrid.doSearch('tbl_storage',opt_search,'and');
        }else{
            $.dm_datagrid.doSearch('tbl_storage',{},'and');
        }
    },300)
}, del: function(index){ // XÓA
    var self = this;
    var data = $('#tbl_storage').datagrid('getData');
    if(!data.rows){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var row = data.rows[index];
    if(!row){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>'];
    msg.push(' - Dữ liệu sau khi xóa sẽ không thể khôi phục.');
    $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
        if (r){
            process($CFG.project+'/'+self.module+'/del',{ids: row.id},function(resp){
                $("#tbl_"+self.module).datagrid('reload');
            })
        }
    });
    setTimeout(function(){
        $('#tbl_storage').datagrid('cancelEdit', index);

    },0);
},  exportFormAction :  function (id){
    var self = this;
    var urls_export = [$CFG.remote.base_url,$CFG.project,'storage','exportExcelPhieuNhapKho'];
    $('#export-dialog').dialog({
        title: 'Thông báo',
        width: 400,
        height: 200,
        closed: false,
        cache: false,
        modal: true ,
        onOpen : function (ele) {
            $(ele).show();
            var selectedrow = $("#tbl_storage").datagrid("getSelected");
            var row = selectedrow ;
            if(!row){
                alert('Không tìm thấy dữ liệu.');
            }
            var f_date  = row.date;
            var ss = f_date.split('/');
            var selected_date = parseInt(ss[2],10)+"-"+parseInt(ss[1],10)+"-"+parseInt(ss[0],10);
            var date;
            var type;
            $("#ngay_xuat").datebox({
                width:100,
                onSelect: function(date){
                    selected_date = date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
                }
            });
            $('#ngay_xuat').datebox('setValue',f_date);
            $('#btn_export').click(function(){
                var nguoi_lap_phieu  = $('#nguoi_lap_phieu').val();
                var nguoi_giao_hang  = $('#nguoi_giao_hang').val();
                var thu_kho  = $('#thu_kho').val();
                var ke_toan_truong  = $('#ke_toan_truong').val();
                if(typeof selected_date == 'undefined') {
                    alert('Vui lòng chọn ngày xuất') ;
                    return;
                }
                date  = selected_date;
                type  = $('#export_type').val();
                process($CFG.project+'/'+self.module+'/update_print_info',
                    {type:2,nguoi_lap_phieu:nguoi_lap_phieu,nguoi_giao_hang:nguoi_giao_hang,thu_kho:thu_kho,ke_toan_truong:ke_toan_truong},
                    function(data){
                        location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                    },
                    function(data){
                        location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                    },
                    false);
            });
            $.getScript($CFG.remote.base_url+'/js/jQuery-printPage-plugin/jquery.printPage.js').done(function(){
                $('#btn_preview').click(function(){
                    if(typeof selected_date == 'undefined') {
                        alert('Vui lòng chọn ngày xuất') ;
                        return;
                    }
                    date  = selected_date;
                    type  = $('#export_type').val();

                    $('#btn_preview').printPage({
                        url: urls_export.join('/')+'?type='+type+'&date='+date+'&preview=1',
                        attr: "href",
                        message:"Phiếu nhập kho đang được tạo ..."
                    });
                });
            });
        }
    });
}
}







