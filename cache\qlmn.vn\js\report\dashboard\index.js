angular_app_report.controller('dashboardController', ['$scope', function ($scope) {
    $scope.filter = {
        schoolyear: schoolyear,
        month: $CFG.level == 4 ? '' : (new Date()).getMonth() + 1
    }
    $scope.data = {};
    $scope.chart = {};
    $scope.units = {};
    $scope.unitLimit = 40;
    $scope.showChart = true;
    $scope.titlesForCombo = {};
    resetDataChart();

    function resetDataChart() {
        $scope.titlesForCombo = {};
        $scope.data = {};
        $scope.months = _.map([9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8], function (month) {
            let title = _.padStart(month, 2, '0') + '/' + (month >= 9 ? $scope.filter.schoolyear : parseInt($scope.filter.schoolyear) + 1);
            if (!$scope.filter.month && $CFG.level == 4) {
                $scope.titlesForCombo[title] = 0;
            }
            return { id: month, title: title };
        });
        if ($CFG.level == 4) {
            $scope.months.unshift({ id: '', title: 'Tất cả' });
        }
    }


    $scope.init = function () {
        $scope.typeChart = $scope.filter.type == 'attend' && $CFG.level == 4 ? 'line' : 'bar';
        // if ($scope.filter.key && $scope.filter.type) {
            resetDataChart();
            process('report/dashboard/index', $scope.filter, function (resp) {
                if ($CFG.level < 4 && resp.units) {
                    $scope.units = resp.units;
                    ['cdkp_money', 'cdkp_quantity', 'cdkp_student', 'attend_attended', 'attend_total_kp', 'attend_total_cp', 'fee_amount_paid', 'fee_amount_rule', 'fee_student_paid', 'fee_student_rule', 'fee_student_paid_ctt', 'fee_amount_paid_ctt'].forEach(rpt_key => {
                        $scope.data[rpt_key] = {};
                        Object.values(resp.units).map((v, key )=> {
                                $scope.data[rpt_key][v] = 0;
                                $scope.titlesForCombo[v] = 0;
                        })
                    })
                }
                if (resp.data) {
                    if ($scope.filter.month && $CFG.level == 4) {
                        $scope.titlesForCombo = {};
                    }
                    Object.keys(resp.data).map(key => {
                        if ($CFG.level == 4) {
                            $scope.data[key] = {};
                            resp.data[key].map(v => {
                                if (!$scope.filter.month) {
                                    $scope.data[key][_.padStart(v.rpt_month, 2, '0') + '/' + v.rpt_year] = v.rpt_value;
                                }
                                else {
                                    $scope.titlesForCombo[v.rpt_date.split('-').reverse().join('/')] = 0;
                                    $scope.data[key][v.rpt_date.split('-').reverse().join('/')] = v.rpt_value;
                                }
                            })
                        }
                        else {
                            Object.keys(resp.data[key]).forEach(rpt_key => {
                                if(rpt_key in $scope.data) {
                                    $scope.data[rpt_key][resp.units[key]] = resp.data[key][rpt_key]['rpt_value'];
                                }
                            })
                        }
                    })
                }
                Object.keys($scope.data).forEach(v => {
                    $scope.data[v] = sortObjectKeyDate($scope.data[v]);
                })

                $scope.titlesForCombo = sortObjectKeyDate($scope.titlesForCombo);
                // if (!($CFG.level < 4 && Object.values($scope.units).length > $scope.unitLimit)) {
                    $scope.showChart = true;
                    setTimeout(bindChart($scope.data['cdkp_money'] ?? {}, 'bar', 'Tổng tiền CĐKP', 'chart_cdkp_money', 'money'), 200);
                    setTimeout(bindChart($scope.data['cdkp_quantity'] ?? {}, 'line', 'Tổng khối lượng thực phẩm', 'chart_cdkp_quantity', 'quantity'), 200);
                    bindChartAttend();
                    bindChartFeeAmount();
                    bindChartFeeStudent();
                // }
                // else {
                //     $scope.showChart = false;
                //     $scope.data = Object.values($scope.data);
                //     if ($scope.chart) {
                //         $scope.chart.destroy();
                //     }
                // }
            })
        // }
    }

    $scope.onMonthChanged = function () {
        $scope.init();
    }

    function sortObjectKeyDate (data) {
        return Object.keys(data).sort((a, b) => {
            let arrA = a.split('/');
            let arrB = b.split('/');
            if (arrA[arrA.length - 1] > arrB[arrB.length - 1]) {
                return 1;
            }
            if (arrA[arrA.length - 2] > arrB[arrB.length - 2]) {
                return 1;
            }
            if (arrA.length == 3 && arrB.length == 3 && arrA[arrA.length - 3] > arrB[arrB.length - 3]) {
                return 1;
            }
            return -1;
        }).reduce(
            (obj, key) => { 
                obj[key] = data[key]; 
                return obj;
            }, 
            {}
        );
    }

    $scope.init();

    function bindChartFeeStudent() {
        const fields = {
            'fee_student_rule' : "Tổng học sinh dự kiến thu",
            'fee_student_paid' : "Tổng tiền đã đóng",
            'fee_student_paid_ctt' : "Tổng GD qua CTT",
        };
        let dataFeeStudent = {};
        Object.keys(fields).forEach(v => {
            dataFeeStudent[v] = {};
            Object.keys($scope.titlesForCombo).map(title => {
                dataFeeStudent[v][title] = 0;
                if (v in $scope.data) {
                    dataFeeStudent[v][title] = $scope.data[v][title] ?? 0
                }
            })
        })
        const datasets = [
            {
                label: fields['fee_student_rule'],
                data: Object.values(dataFeeStudent['fee_student_rule']),
                borderColor: '#00FF7F',
                order: 1
            },
            {
                label: fields['fee_student_paid'],
                data: Object.values(dataFeeStudent['fee_student_paid']),
                borderColor: '#1E90FF',
                type: 'line',
                order: 0
            },
            {
                label: fields['fee_student_paid_ctt'],
                data: Object.values(dataFeeStudent['fee_student_paid_ctt']),
                borderColor: '#EE82EE',
                type: 'line',
                order: 2
            },
        ];
        setTimeout(bindChart($scope.titlesForCombo, 'line', '', 'chart_fee_student', 'fee', true, datasets), 200);
    }

    function bindChartFeeAmount() {
        const fields = {
            'fee_amount_rule' : "Tổng tiền dự kiến thu",
            'fee_amount_paid' : "Tổng tiền đã đóng",
            'fee_amount_paid_ctt' : "Tổng tiền qua CTT",
        };
        let dataFeeAmount = {};
        Object.keys(fields).forEach(v => {
            dataFeeAmount[v] = {};
            Object.keys($scope.titlesForCombo).map(title => {
                dataFeeAmount[v][title] = 0;
                if (v in $scope.data) {
                    dataFeeAmount[v][title] = $scope.data[v][title] ?? 0
                }
            })
        })
        const datasets = [
            {
                label: fields['fee_amount_rule'],
                data: Object.values(dataFeeAmount['fee_amount_rule']),
                borderColor: '#00FF7F',
                order: 1
            },
            {
                label: fields['fee_amount_paid'],
                data: Object.values(dataFeeAmount['fee_amount_paid']),
                borderColor: '#1E90FF',
                type: 'line',
                order: 0
            },
            {
                label: fields['fee_amount_paid_ctt'],
                data: Object.values(dataFeeAmount['fee_amount_paid_ctt']),
                borderColor: '#EE82EE',
                type: 'line',
                order: 2
            },
        ];
        setTimeout(bindChart($scope.titlesForCombo, 'line', '', 'chart_fee_amount', 'money', true, datasets), 200);
    }

    function bindChartAttend() {
        let dataAttended = {};
        let dataAttendKP = {};
        let dataAttendCP = {};
        Object.keys($scope.titlesForCombo).map(title => {
            dataAttended[title] = 0;
            dataAttendKP[title] = 0;
            dataAttendCP[title] = 0;
            if ($scope.data['attend_attended']) {
                dataAttended[title] = $scope.data['attend_attended'][title] ?? 0;
            }
            if ($scope.data['attend_total_kp']) {
                dataAttendKP[title] = $scope.data['attend_total_kp'][title] ?? 0;
            }
            if ($scope.data['attend_total_cp']) {
                dataAttendCP[title] = $scope.data['attend_total_cp'][title] ?? 0;
            }
        })
        const datasets = [
            {
                label: 'Số trẻ đi học',
                data: dataAttended,
                borderColor: '#00FF7F',
                // backgroundColor: '#00FF7F',
                borderWidth: 3,
                order: 1
            },
            {
                label: 'Số trẻ nghỉ KP',
                data: dataAttendKP,
                borderColor: '#1E90FF',
                type: 'line',
                order: 0
            },
            {
                label: 'Số trẻ nghỉ CP',
                data: dataAttendCP,
                borderColor: '#EE82EE',
                type: 'line',
                order: 2
            },
        ];
        setTimeout(bindChart($scope.titlesForCombo, 'bar', '', 'chart_attend', 'attend', true, datasets), 200);
    }

    function bindChart (data, type, title, elementId, key, isComboChart = false, datasets) {
        var ctx = document.getElementById(elementId).getContext('2d');
        var selectedOptionText = title;
        if ($scope.chart[elementId]) {
            $scope.chart[elementId].destroy();
        }
        if (!isComboChart) {
            datasets = [{
                label: selectedOptionText,
                data: Object.values(data),
                borderWidth: 1
            }];
        }
        $scope.chart[elementId] = new Chart(ctx, {
            type: type, // loại biểu đồ, bạn có thể thay đổi thành 'line', 'pie', 'bar.
            data: {
                labels: Object.keys(data),
                datasets: datasets
            },
            options: {
                scales: {
                    // x: {
                    //     beginAtZero: true,
                    //     ticks: {
                    //         maxRotation: 90,
                    //         minRotation: 45
                    //     }
                    // },
                    // y: {
                    //     beginAtZero: true
                    // }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
        
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    if (key == 'money') {
                                        label += new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(context.parsed.y);
                                    }
                                    else {
                                        label += context.parsed.y
                                        if (key == 'student') {
                                            label += ' suất';
                                        }

                                    }
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }
}]);