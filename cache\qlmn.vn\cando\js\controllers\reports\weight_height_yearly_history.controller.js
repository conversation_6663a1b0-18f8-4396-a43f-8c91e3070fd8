(function StudentBmiYearlyController(app) {
    'use strict';

    app.controller('B<PERSON><PERSON>ear<PERSON><PERSON>ontroller', StudentBmiYearlyController);

    function StudentBmiYearlyController(
        $scope,
        ACTIVE_MONTHS,
        CommonService,
        CourseService,
        StudentBmiService,
        AuthService
    ) {
        var vm = this;

        $scope.sys.module.title = 'Kết quả cân đo cả năm';

        vm.filters = {
            'school_year': $CFG.active_year || (new Date()).getFullYear(),
            'course_id': 0,
        };

        vm.ACTIVE_MONTHS = ACTIVE_MONTHS;
        vm.students = [];
        vm.schoolYears = CommonService.generateSchoolYear();

        CourseService.fetchCourses().then(function (courses) {
            vm.courses = courses;

            if (!AuthService.isSuperAdmin()) {
                vm.filters.course_id = _.get(courses, '[0]courses.data[0].id', 0);
                vm.onCourseChanged();
            }
        });

        function fetchStudents() {
            vm.students = [];

            if (!vm.filters.school_year || !vm.filters.course_id) {
                return;
            }

            StudentBmiService.fetchStudents(vm.filters).then(function (students) {
                vm.students = students;
            });
        }

        function getBmiHistoryByMonth(studentIndex, month) {
            var histories = _.get(vm.students, [studentIndex, 'bmi_histories', 'data']);

            return _.find(histories, {'month': month});
        }

        vm.onSchoolYearChanged = function onSchoolYearChanged() {
            fetchStudents();
        };

        vm.onCourseChanged = function onCourseChanged() {
            fetchStudents();
        };

        vm.getWeightOfMonth = function getWeightOfMonth(studentIndex, month) {
            return _.get(getBmiHistoryByMonth(studentIndex, month), 'weight', '');
        };

        vm.getHeightOfMonth = function getHeightOfMonth(studentIndex, month) {
            return _.get(getBmiHistoryByMonth(studentIndex, month), 'height', '');
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentBmiService.generateDownloadExcelUrlForBmiYearlyHistory(vm.filters);
        };
    }

    StudentBmiYearlyController.$inject = [
        '$scope',
        'ACTIVE_MONTHS',
        'CommonService',
        'CourseService',
        'StudentBmiService',
        'AuthService',
    ];
})(window.angular_app);
