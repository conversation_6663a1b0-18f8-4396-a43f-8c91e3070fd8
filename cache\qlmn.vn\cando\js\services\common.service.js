(function CommonService(app) {
    'use strict';

    app.service('CommonService', [commonService]);

    function commonService() {
        function generateSchoolYear() {
            var schoolYears = [];
            var MAX_VIEW_YEAR_RANGE = 2;
            var currentSchoolYear = $CFG.active_year;

            for (var year = currentSchoolYear - MAX_VIEW_YEAR_RANGE; year <= currentSchoolYear + MAX_VIEW_YEAR_RANGE; year++) {
                schoolYears.push({
                    id: year,
                    title: year + ' - ' + (+year + 1),
                });
            }

            return schoolYears;
        }

        return {
            generateSchoolYear: generateSchoolYear,
        };
    }

})(window.angular_app);
