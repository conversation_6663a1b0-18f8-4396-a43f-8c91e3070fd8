$.menu_adjust = {
    project: 'dinhduong',
    module: 'menu_adjust',
    groups: [],
    init: function() {
        var month = new Date().getMonth() + 1;
        process('dinhduong/menu_adjust/special_categories',{month: month, async: true}, function(resp){
            $.menu_adjust.service_price = resp.service_price;
            $.menu_adjust.groups = resp.groups;
            $.menu_adjust.warehouses = resp.warehouses;
            $.menu_adjust.measures = resp.measures;
            $.menu_informations = resp.menu_informations;
            $.menu_adjust.months = resp.months;
            $.menu_adjust.school_point = resp.school_point;
            $.menu_adjust.date = resp.date;
            $.menu_adjust.month = month;
            $.menu_adjust.initAngular();
        },null,false);
    }, initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('#menu_adjustController')).scope().$apply(function(scope) {
                scope.mauKhongAnSang = localStorage.getItem('mauKhongAnSang') === 'true' ? true : false; // Tiện ích hiện mẫu ăn sáng của Phòng GD Huế
                scope.enableMauKhongAnSang = scope.mauKhongAnSang && ($CFG.level == 3) ? true : false;

                scope.meal_defines = {
                    buasang: 1,
                    buatrua: 2,
                    buaxe: 2,
                    buaphu: 2,
                    buatoi: 3
                };
                scope.meal_title = {
                    1: 'Bữa sáng',
                    2: 'Bữa chính',
                    3: 'Bữa tối'
                };
                scope.systemConfig = window.sysConfigs;
                scope.configs = scope.systemConfig.configs;
                if(scope.configs.editFoodNamePermission == undefined) {
                    scope.configs.editFoodNamePermission = 1;
                }
                scope.systemConfig.module = self.module;
                scope.groups = $.menu_adjust.groups;
                scope.menu_adjust = {
                    warehouses: $.menu_adjust.warehouses,
                    groups: $.menu_adjust.groups,
                    months: $.menu_adjust.months,
                    group_adjusts: {},
                    market_before: 1,
                    showDelete: false,
                    keysearch_name : ''
                };

                scope.warehousesRemovedKey = null;

                if (scope.enableMauKhongAnSang) {
                    scope.warehouses = {};

                    for (let key in $.menu_adjust.warehouses) {
                        if ($.menu_adjust.warehouses[key].name == 'Kho sáng') {
                            scope.warehousesRemovedKey = key;
                        } else {
                            scope.warehouses[key] = $.menu_adjust.warehouses[key];
                        }
                    }
                    
                    scope.menu_informations = $.menu_informations.filter((item) => {
                        return item.define != 'buasang';
                    });
                } else {
                    scope.warehouses = $.menu_adjust.warehouses;
                    scope.menu_informations = $.menu_informations;
                }

                // scope.onChangeKeysearchName = function(keysearch_name){
                //     if(scope.keysearch_name_time == 0){
                //         scope.keysearch_name_time = 1;
                //         setTimeout(function(){
                //             if(scope.keysearch_name_time == 1){
                //                 $.doSearch();
                //                 scope.keysearch_name_time = 0;
                //             }
                //             console.log(keysearch_name);
                //         }, 700);
                //     }
                // };

                scope.school_point = $.menu_adjust.school_point;
                scope.service_price = $.menu_adjust.service_price;
                scope.measures = $.menu_adjust.measures;
                scope.mapMeasures = {};
                scope.dinhduong_calc = {
                    1: false, 2: true
                };
                scope.apdungcongthuc = true;
                scope.tong = {
                    tien: 0,
                    animal_protein: 0,
                    vegetable_protein: 0,
                    animal_fat: 0,
                    vegetable_fat: 0,
                    sugar: 0,
                    calo: 0,
                    calo_congthuc: 0,
                    canxi: 0,
                    b1: 0
                };
                scope.tong_chinh = {
                    tien: 0,
                    animal_protein: 0,
                    vegetable_protein: 0,
                    animal_fat: 0,
                    vegetable_fat: 0,
                    sugar: 0,
                    calo: 0
                };
                scope.row = {
                    sotre: 10,
                    tien1tre: (scope.configs.cdkp_tien1tre_default || 15000)
                };
                scope.warehouse = {1:true,2:true};
                scope.selected = {
                    group: $.menu_adjust.groups[0],
                    warehouse: 1,
                    month: '',
                    balance_warehouse: {},
                    date: $.menu_adjust.date,
                };
                if ($CFG.school_point_together == 0 && $CFG.school_points > 1 && $CFG.admin == 1){
                    scope.selected.point = $CFG.school_point;
                }
                scope.datagrid = {
                    data: {}
                };
                scope.menu_adjust.fix_export = true;
                scope.prices = {};
                scope.menu_adjust.selected = {food : undefined};
                scope.menu_adjust.ignore_food_ids = '';
                scope.food_detail = false;
                scope.detailByMeal = {};
                scope.detailByDish = {};
                scope.library = {};
                scope.point_selected = undefined;
                scope.lengthSelectDish = 0;
                /*Lấy tháng mặc định*/

                angular.forEach(scope.measures, function (measure) {
                    scope.mapMeasures[measure.id] = measure;
                });

                angular.forEach(scope.menu_adjust.months,function(month,index){
                    if(month.id == $.menu_adjust.month) {
                        scope.selected.month = month;
                        return;
                    }
                });
                angular.forEach(scope.menu_adjust.warehouses, function(item,index){
                    scope.selected.balance_warehouse[index] = index == 2;
                });
                scope.initGrid(scope.selected.month);
                scope.menu_adjust.page = 1;
                scope.selectPoint = (point)=>{
                    scope.point_selected = point;
                };
                scope.getTile_PLG = function(meals){
                    if (!meals) {
                        meals = {};
                        if (scope.selected.group_adjust) {
                            meals = scope.selected.group_adjust.meals
                        }
                    }
                    var name = 'tong';
                    var rs = {protein: 0, fat: 0, sugar: 0};
                    angular.forEach(meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                if ( scope.selected.group_adjust.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                                    food.nutritions || (food.nutritions = {});
                                    food.nutritions.protein || (food.nutritions.protein = 0);
                                    food.nutritions.fat || (food.nutritions.fat = 0);
                                    food.nutritions.sugar || (food.nutritions.sugar = 0);
                                    food.nutritions.calo || (food.nutritions.calo = 0);

                                    rs.protein += scope.co_cau(null, scope.menu_adjust.date).protein * food.quantity_edit * food.nutritions.protein / 100;
                                    rs.fat += scope.co_cau(null, scope.menu_adjust.date).fat * food.quantity_edit * food.nutritions.fat / 100;
                                    rs.sugar += scope.co_cau(null, scope.menu_adjust.date).sugar * food.quantity_edit * food.nutritions.sugar / 100;
                                }
                            });
                        });
                    });
                    var tongcalo = rs.protein + rs.fat + rs.sugar;
                    rs.protein = scope.round(rs.protein / tongcalo * 100, 1);
                    rs.fat = scope.round(rs.fat / tongcalo * 100, 1);
                    rs.sugar = scope.round(rs.sugar / tongcalo * 100, 1);
                    return rs;
                };
                /*
                * Tính cơ cấu chuẩn P:L:G
                * */
                scope.getPLGTotal = function (nutritions, cocau_type) {
                    let rs = 0;
                    if(cocau_type==undefined) {
                        cocau_type = null;
                    }
                    if (nutritions) {
                        rs += scope.co_cau(cocau_type, scope.menu_adjust.date).protein * $['+'](nutritions.animal_protein, nutritions.vegetable_protein);
                        rs += scope.co_cau(cocau_type, scope.menu_adjust.date).fat * $['+'](nutritions.animal_fat, nutritions.vegetable_fat);
                        rs += $['*'](scope.co_cau(cocau_type, scope.menu_adjust.date).sugar, nutritions.sugar);
                    }
                    return rs;
                };
                scope.duplicateToMenu_planning = function(){
                    $.dm_datagrid.showEditForm(
                        {
                            module: $CFG.project+'/menu_adjust',
                            action:'edit',
                            title:'Lưu thực đơn vào danh sách thực đơn mẫu',
                            size: size.small,
                            showButton: false,
                            scope: scope,
                            content: scope.controller.templates.duplicateToMenuPlanning
                        }
                    );
                };
                /*Tạo thực đơn mẫu mới*/
                scope.addMenu_planningAction = function(menu_planning){
                    var row = {};
                    angular.forEach(scope.row, function(value, index){
                        row[index] = value;
                    });
                    row.mathucdon = menu_planning.name;
                    row.group_id = menu_planning.group_id;
                    row.group_key = scope.menu_adjust.groups[menu_planning.group_id].group_key;
                    var data = {
                        meals: JSON.stringify(scope.selected.group_adjust.meals),
                        data: JSON.stringify(scope.datagrid.data),
                        row: row,
                        services: scope.row.services,
                        rate: scope.caloRate()
                    };
                    data.async = true;
                    var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/menu_planning/add';
                    process(url, data, function(resp){
                        if(resp.result == "success"){
                            alert('Đã tạo thành công thực đơn');
                        }
                    });
                };
                /* Hàm mở form sao chép thực đơn về thư viện mẫu*/
                scope.library.formCopyToLibrary = function(){
                    scope.library.row = {};
                    angular.forEach(scope.row, function(value, index){
                        scope.library.row[index] = value;
                    });
                    scope.library.row.group_id = undefined;
                    scope.library.groups = scope.storage.combobox;
                    if (typeof scope.library.groups === 'string') {
                        scope.library.groups = JSON.parse(scope.library.groups);
                    }
                    scope.library.to_groups = scope.menu_adjust.groups;
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            title: 'Thư viện thực đơn mẫu',
                            draggable: false,
                            fullScreen: false,
                            showButton: false,
                            size: size.small,
                            scope: scope,
                            content: scope.controller.templates.shareWithAdmin,
                            onShown: function(element){
                                if (typeof scope.storage.combobox === 'undefined') {
                                    process($CFG.remote.base_url + '/doing/' + $CFG.project + '/menu_planning/groups', {async: true}, function (res) {
                                        if (res.result) {
                                            scope.$apply(function () {
                                                scope.storage.combobox = res.data.groups;
                                            });
                                        }
                                    });
                                }
                            }
                        }
                    );
                };
                scope.library.copyToAdmin = function() {
                    scope.library.row.group_key = scope.selected.group['group_key'];
                    scope.library.row.tienchenhlech1tre = round(scope.library.row.tongtienchenhlech/scope.library.row.sotre,3);
                    var data = {
                        meals: JSON.stringify(scope.selected.group_adjust.meals),
                        data: JSON.stringify(scope.datagrid.data),
                        row: scope.library.row,
                        services: scope.row.services,
                        rate: scope.caloRate(),
                        type: 'add'
                    };
                    data.async = true;
                    var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/menu_planning/libraries';
                    process(url, data, function(resp){
                        if(resp.result == 'success') {
                            dialogClose();
                            alert('Sao chép thành công');
                        }
                    });
                };
                scope.onChange_price_export = function(food, exp) {
                    food.price = exp.price;
                    scope.totalCalculator(scope.datagrid.data, true);
                };
                scope.price2price_kg = function(food) {

                };
                scope.onChange_price = function(data, food_change){
                    food_change.gam_exchange || (food_change.gam_exchange = 1000);
                    food_change.price_kg = scope.round($['/']($['*'](food_change.price,1000),food_change.gam_exchange));
                    angular.forEach(food_change.foods, function (fd, index) {
                        fd.price_kg = food_change.price_kg;
                        fd.price = food_change.price;
                    });
                    food_change.thanhtien1nhom = scope.round($['*'](food_change.price, food_change.thucmuatheodvt));
                    food_change.tiendicho1tre = scope.round($['*'](food_change.thucmua1tretheodvt , food_change.price));
                    scope.totalCalculator(scope.datagrid.data);
                };
                scope.onChange_price_kg = function(data, food_change){
                    food_change.gam_exchange || (food_change.gam_exchange = 1000);
                    food_change.price = $['*'](food_change.price_kg,$['/'](food_change.gam_exchange, 1000));
                    angular.forEach(food_change.foods,function(fd,index){
                        fd.price_kg = food_change.price_kg;
                        fd.price = food_change.price;
                    });
                    food_change.thanhtien1nhom = scope.round($['*'](food_change.price, food_change.thucmuatheodvt));
                    food_change.tiendicho1tre = scope.round($['*'](food_change.thucmua1tretheodvt , food_change.price));
                    scope.totalCalculator(scope.datagrid.data);
                };
                scope.onChangeCanxiB1 = function(data, food_change){
                    angular.forEach(food_change.foods,function(food){
                        food.nutritions.canxi = food_change.nutritions.canxi;
                        food.nutritions.b1 = food_change.nutritions.b1;
                    });
                    scope.totalCalculator(scope.datagrid.data);
                };
                scope.onBlur_luong1tre = function(food) {
                    scope.onChange_thucmuatheodvt(food);
                };
                scope.onBlur_extrude_factor = function(food){
                    food.extrude_factor = Number(food.extrude_factor);
                    scope.extrudeFactorBlur(food);
                    if( food.extrude_factor != food.extrude_factor_root ) {
                        if(scope.sys.configs.cdkp_auto_update_to_tp_truong){
                            scope.updateExtrude_factor(food);
                        }else{
                            $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Bạn vừa thay đổi hệ số thải bỏ. Có muốn cập nhật hệ số mới vào thực phẩm trường?<br/></div>', function(r){
                                if (r){
                                    scope.updateExtrude_factor(food);
                                }
                            });
                        }
                    }
                };
                scope.updateExtrude_factor = function(food){
                    var params = {
                        id: food.food_id,
                        value: food.extrude_factor
                    };
                    process('dinhduong/menu_planning/updateExtrude_factor',params,function(resp){
                        food.extrude_factor_root = food.extrude_factor;
                    },null,false);
                };

                /* Cập nhật HSTB, DVT, Quy đổi gam về TP trường */
                scope.updateUnitFoodDetail = function(food, key){
                    var new_value = food[key];
                    var params = {
                        id: food.food_id,
                        key: key,
                        value: new_value
                    };
                    process('dinhduong/menu_planning/updateUnitFoodDetail', params, function(resp){
                        food[key+'_root'] = food[key];
                    },null,false);
                };

                scope.onChange_extrude_factor = function(food){
                    scope.onChange_thucmuatheodvt(food);
                };
                scope.onChange_gam_exchange = function(food){
                    scope.onChange_thucmuatheodvt(food);
                    scope.onChange_price(scope.datagrid.data,food);
                };
                scope.gam_exchangeBlur = function(food){
                    scope.gam_exchanges[food.food_id] = Number(food.gam_exchange);
                    angular.forEach(scope.menu_adjust.group_adjusts, function (group, i1) {
                        angular.forEach(group.menu_plannings[0].data, function (kho, i2) {
                            angular.forEach(kho, function (fd, food_id) {
                                if(fd.food_id === food.food_id){
                                    fd.gam_exchange = scope.gam_exchanges[food.food_id];
                                    angular.forEach(fd.foods, function (fd_meal, i3) {
                                        fd_meal.gam_exchange = scope.gam_exchanges[food.food_id];
                                    });
                                }
                            });
                        });
                    });
                    // Kiểm tra dữ liệu thay đổi & bật popup
                    console.log('curr='+food.gam_exchange, 'root='+food.gam_exchange_root);
                    if(food.gam_exchange != food.gam_exchange_root) {
                        if(scope.sys.configs.cdkp_auto_update_to_tp_truong){
                            scope.updateUnitFoodDetail(food, 'gam_exchange');
                        }else{
                            $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Bạn vừa thay đổi quy đổi gam. Có muốn cập nhật giá trị mới vào thực phẩm gốc?<br/></div>', function(r){
                                if (r){
                                    scope.updateUnitFoodDetail(food, 'gam_exchange');
                                }
                            });
                        }
                    }
                };
                scope.extrudeFactorBlur = function(food){
                    angular.forEach(scope.menu_adjust.group_adjusts, function (group) {
                        angular.forEach(group.menu_plannings[0].data, function (kho) {
                            angular.forEach(kho, function (fd) {
                                if(fd.food_id === food.food_id){
                                    fd.extrude_factor = Number(food.extrude_factor);
                                    angular.forEach(fd.foods, function (fd_meal) {
                                        fd_meal.extrude_factor = Number(food.extrude_factor);
                                    });
                                }
                            });
                        });
                    });
                };
                scope.onChange_luong1tre = function(food,ignore_cal,callback){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var luong1tre = Number(food.luong1tre);
                    food.luong1tretheodvt = $['/'](luong1tre, food.gam_exchange);
                    food.thucan1nhom = $['/']($['*'](luong1tre,scope.row.sotre),1000);
                    food.thucmua1nhom = food.thucan1nhom;
                    if(food.extrude_factor > 0){
                        food.thucmua1nhom = $['/']($['*'](100,food.thucan1nhom),$['-'](100,food.extrude_factor));
                    }
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucmuatheodvt = round($['*']($['/'](food.thucmua1nhom,food.gam_exchange),1000),5);
                    food.thucmuatheodvt_view = food.thucmuatheodvt;
                    food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt,scope.row.sotre);
                    scope.foodRounds(food,'luong1tre');
                    if(typeof callback === 'function'){
                        callback(food);
                    }
                    food.thanhtien1nhom = $['*'](food.price, food.thucmuatheodvt);
                    scope.divide_luong1tre(food);
                    scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                    if(!ignore_cal){
                        scope.totalCalculator();
                    }
                };
                /* Check sử dụng 1 kho */
                scope.warehouseOnlyOne = function() {
                    var rs = true;
                    var whs = {};
                    if(scope.selected.group_adjust){
                        angular.forEach(scope.selected.group_adjust.meals, function(meal, define) {
                            if(count(meal.dishes)>0) {
                                whs[meal.warehouse_id] = meal.warehouse_id;
                            }
                        });
                        if(count(whs) > 1){
                            rs = false;
                        }
                    }
                    return rs;
                };
                scope.onBlur_thucan1nhom = function(food){
                    scope.onChange_thucmuatheodvt(food);
                };
                scope.onChange_thucan1nhom = function(food){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucan1nhom = Number(food.thucan1nhom);
                    food.thucmua1nhom = thucan1nhom;

                    if(food.extrude_factor){
                        food.thucmua1nhom = $['/']($['*'](100 , food.thucan1nhom),$['-'](100 , food.extrude_factor));
                    }
                    food.thucmuatheodvt = $['*']($['/'](food.thucmua1nhom,food.gam_exchange),1000);
                    food.thucmuatheodvt_view = food.thucmuatheodvt;

                    food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt, scope.row.sotre);
                    food.thucmua1nhom = food.thucmuatheodvt * food.gam_exchange / 1000;
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food,'thucan1nhom');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                    scope.totalCalculator();
                };
                scope.round_thucmuatheodvt = function(value){
                    if(value<0.005){
                        value = 0.01;
                    }else if(value <0.05){
                        value = 0.1;
                    }else{
                        value = scope.round(value,1);
                    }
                    return value;
                };

                scope.delFoodVT = function(food_id){
                    var tam = {};
                    angular.forEach(scope.selected.dish.ingredient, function(dish,key){
                        if(food_id != key){
                            tam[key] = dish;
                        }
                    });
                    scope.selected.dish.ingredient = tam;
                };

                scope.onChange_thucmua1tre = function(food){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1tre = Number(food.thucmua1tre);
                    food.thucmua1nhom = $['/']($['*'](thucmua1tre, scope.row.sotre), 1000);
                    food.thucmuatheodvt = $['*']($['/'](food.thucmua1nhom,food.gam_exchange),1000);
                    food.thucmuatheodvt_view = food.thucmuatheodvt;
                    food.thucmua1tretheodvt = scope.round($['/'](food.thucmuatheodvt, scope.row.sotre),4);
                    food.thucan1nhom = food.thucmua1nhom;
                    if(food.extrude_factor){
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food,'thucmua1tre');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                    scope.totalCalculator();
                };

                /*Thay đổi thực mua một nhóm theo kg*/
                scope.onChange_thucmua1nhom = function(food){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1nhom = Number(food.thucmua1nhom);
                    food.thucmua1tre = $['*']($['/'](thucmua1nhom, scope.row.sotre), 1000);
                    food.thucmuatheodvt = $['*']($['/'](thucmua1nhom,food.gam_exchange),1000);
                    food.thucmuatheodvt_view = food.thucmuatheodvt;
                    food.thucmua1tretheodvt = scope.round($['/'](food.thucmuatheodvt, scope.row.sotre),4);
                    food.thucan1nhom = food.thucmua1nhom;
                    if(food.extrude_factor){
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food,'thucmua1nhom');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                    scope.totalCalculator();
                };
                scope.onChange_tiendicho1tre = function(food){
                    // food.gam_exchange || (food.gam_exchange = 1000);
                    // food.thucmua1tretheodvt = $['/'](food.tiendicho1tre, food.price);
                    // food.thucmuatheodvt = scope.round_thucmuatheodvt($['*'](food.thucmua1tretheodvt, scope.row.sotre));
                    // scope.onChange_thucmuatheodvt(food);
                };
                scope.onChange_thucmua1tretheodvt = function(food){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1tretheodvt = Number(food.thucmua1tretheodvt);
                    food.thucmuatheodvt = $['*'](thucmua1tretheodvt, scope.row.sotre);
                    food.thucmuatheodvt_view = food.thucmuatheodvt;
                    food.thucmua1nhom = $['/']($['*'](food.thucmuatheodvt , food.gam_exchange) , 1000);
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucan1nhom = food.thucmua1nhom;
                    if(food.extrude_factor){
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food,'thucmua1tretheodvt');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.totalCalculator();
                };
                /*Thay đổi thành tiền một nhóm theo đơn vị tính*/
                scope.onChange_thanhtien1nhom = function(food,ignore_cal){
                    if(food.price>0){
                        food.thucmuatheodvt = $['/'](food.thanhtien1nhom,food.price);
                        food.thucmuatheodvt_view = round(food.thucmuatheodvt);
                        food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt, scope.row.sotre);
                        food.thucmua1nhom = $['/']($['*'](food.thucmuatheodvt , food.gam_exchange) , 1000);
                        food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                        food.thucan1nhom = food.thucmua1nhom;
                        if(food.extrude_factor){
                            food.thucan1nhom = $['-'](food.thucmua1nhom, $['*']($['/'](food.thucmua1nhom,100),food.extrude_factor));
                        }
                        food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                        food.tiendicho1tre = $['/'](food.thanhtien1nhom, scope.row.sotre);
                        scope.foodRounds(food,'thucmuatheodvt');
                        scope.divide_luong1tre(food);
                        if(!ignore_cal){
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }
                };
                /*Thay đổi thực mua một nhóm theo đơn vị tính*/
                scope.onChange_thucmuatheodvt = function(food, calc, dividepoint){
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmuatheodvt  = Number(food.thucmuatheodvt);
                    food.thucmua1tretheodvt = scope.round($['/'](thucmuatheodvt, scope.row.sotre), 8);
                    food.thucmua1nhom = thucmuatheodvt * food.gam_exchange / 1000;
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucan1nhom = food.thucmua1nhom;
                    if(food.extrude_factor){
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
                    }
                    food.luong1tre = round($['/']($['*'](food.thucan1nhom, 1000), scope.row.sotre), 5);
                    food.luong1tre_root = food.luong1tre;
                    // food.tiendicho1tre = $['/']($['*'](thucmuatheodvt, food.price), scope.row.sotre);
                    scope.foodRounds(food,'thucmuatheodvt');
                    food.thanhtien1nhom = $['*'](thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    if (!dividepoint) {
                        scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                    }
                    if (!calc) {
                        scope.totalCalculator();
                    }
                };
                scope.keyUpFood = function(e, index, warehouse_id, name){
                    var id_input = '';
                    if(e.which == 13 || e.which == 40){    /*Enter*/ /*ArrowDown*/
                        for(++index; index<=count(scope.datagrid.data[warehouse_id]); ++index) {
                            id_input = [name, warehouse_id, index].join('_');
                            if($('#' + id_input + ' input.text-edit').length) {
                                $('#' + id_input + ' input.text-edit').focus();
                                break;
                            }
                        }
                    }else if(e.which == 38){  /*ArrowUp*/
                        for(--index; index>=0; --index) {
                            id_input = [name, warehouse_id, index].join('_');
                            if($('#' + id_input + ' input.text-edit').length) {
                                $('#' + id_input + ' input.text-edit').focus();
                                break;
                            }
                        }
                    }
                };

                scope.roundBuy = function(){
                    angular.forEach(scope.datagrid.data, function(foods,key){
                        angular.forEach(foods, function(food,key){
                            if (scope.isPointTogether()) {
                                if (!scope.roundOption.forTotal) {
                                    food.thucmuatheodvt = 0;
                                    angular.forEach(food.thucmuatheodvts, function (val, point) {
                                        if (val > 0) {
                                            food.thucmuatheodvts[point] = scope.safeRound(val, scope.roundOption.type);
                                            food.thucmuatheodvt = $['+'](food.thucmuatheodvt, food.thucmuatheodvts[point]);
                                        }
                                    });
                                    scope.onChange_thucmuatheodvt(food, true, true);
                                } else {
                                    food.thucmuatheodvt = scope.safeRound(food.thucmuatheodvt);
                                    scope.onChange_thucmuatheodvt(food, true);
                                }
                            } else {
                                food.thucmuatheodvt = scope.safeRound(food.thucmuatheodvt);
                                scope.onChange_thucmuatheodvt(food, true);
                            }
                            scope.divide_luong1tre(food);
                            scope.foodRounds(food,'thucmuatheodvt');
                        });
                    });
                    scope.totalCalculator();
                };

                scope.foodRounds = function(food,ignores){
                    if(typeof ignores === 'string'){
                        ignores = [ignores];
                    }
                    // food.thucmuatheodvt_view = food.thucmuatheodvt;
                    var keys = [
                        /*'luong1tre',*/
                        'gam_exchange',
                        'price','price_kg',
                        'luong1tretheodvt',
                        'thucan1nhom',
                        'thucmua1nhom',
                        'thucmua1tre',
                        'thucmuatheodvt_view',
                        'tiendicho1tre'
                    ];
                    angular.forEach(keys, function(key,index){
                        if(!in_array(key,ignores)){
                            food[key] = scope.round(food[key],5);
                        }
                    });
                    if(!in_array('thucmua1tretheodvt',ignores)){
                        food['thucmua1tretheodvt'] = scope.round(food['thucmua1tretheodvt'],5);
                    }
                };
                scope.getTien_an_sang = function(){
                    var rs = 0;
                    if (scope.datagrid.data[1]) {
                        angular.forEach(scope.datagrid.data[1], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.getTien_an_chinh = function(){
                    var rs = 0;
                    if (scope.datagrid.data[2]) {
                        angular.forEach(scope.datagrid.data[2], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.getTien_an_toi = function(){
                    var rs = 0;
                    if (scope.datagrid.data[3]) {
                        angular.forEach(scope.datagrid.data[3], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.divide_luong1tre = function(item, root_quantity_change){    /*  Chia tỉ lệ thay đổi của lượng 1 trẻ cho các thự phẩm từng món ăn liên quan */
                    if (count(item.foods) == 1) {
                        angular.forEach(item.foods, function (fd, ind) {
                            fd.quantity_edit = item.luong1tre;
                        });
                    } else {
                        /*  Tính tổng lượng 1 trẻ chuẩn theo món ăn */
                        var tong_chuan = 0;
                        angular.forEach(item.foods, function (food, index) {
                            food.quantity = Number(food.quantity);
                            food.quantity_edit = Number(food.quantity_edit)
                            food.quantity_edit = Math.abs(food.quantity_edit);
                            tong_chuan = $['+'](tong_chuan, food.quantity);
                        });
                        var tong_them = $['-'](item.luong1tre, tong_chuan);
                        /* tính tỉ lệ thực phẩm từng */
                        var tong_phan = 0;
                        angular.forEach(item.foods, function (food, index) {
                            var tile = food.quantity / tong_chuan;
                            var soluong = tile * tong_them;
                            soluong = scope.round(soluong, 5);
                            food.quantity_edit = $['+'](food.quantity, soluong);
                            tong_phan = $['+'](tong_phan, soluong);
                        });
                        /*cộng nốt số dư của các lần làm tròn vào thực phẩm đầu tiên*/
                        if (tong_phan != tong_them) {
                            if (item.foods[0]) {
                                item.foods[0].quantity_edit = $['+'](item.foods[0].quantity_edit, $['-'](tong_them, tong_phan));
                                if (item.foods[0].quantity_edit < 0) {
                                    item.foods[0].quantity_edit = 0;
                                }
                            }
                        }
                    }
                };
                scope.getCalo = function(food, field) {
                    field || (field='luong1tre');
                    var rs = 0;
                    rs += scope.co_cau(null, scope.menu_adjust.date).protein * (food[field]*food.nutritions.protein/100)
                        + scope.co_cau(null, scope.menu_adjust.date).fat * (food[field]*food.nutritions.fat/100)
                        + scope.co_cau(null, scope.menu_adjust.date).sugar * (food[field]*food.nutritions.sugar/100);
                    return rs;
                };

                // Kiểm tra & cảnh báo lượng 1 trẻ chưa chính xác?
                scope.check_error_quantity_edit = function (food){
                    if(food.quantity_edit!=food.quantity && food.gam_exchange>0 && food.quantity_edit>0){
                        var luong1tre = food.thucmua1tretheodvt*food.gam_exchange/1000;
                        if(luong1tre==food.quantity && food.quantity/food.quantity_edit==2) {
                            return true;
                        }
                    }
                    return false;
                }

                /*
                * Tính toán bảng khẩu phần dinh dưỡng
                * */
                scope.calcNutrition = function (meals) { /* Tính toán dữ liệu để đưa ra bảng dinh dưỡng */
                    meals || (meals = scope.selected.group_adjust.meals);
                    var group_adjust = scope.selected.group_adjust;
                    var rs = {
                        tien: 0,
                        animal_protein: 0,
                        vegetable_protein: 0,
                        animal_fat: 0,
                        vegetable_fat: 0,
                        sugar: 0,
                        calo: 0,
                        canxi: 0,
                        b1: 0
                    };
                    group_adjust || (group_adjust = scope.selected.group_adjust);
                    angular.forEach(meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                if ( group_adjust.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                                    food.nutritions || (food.nutritions = {});
                                    food.nutritions.protein || (food.nutritions.protein = 0);
                                    food.nutritions.fat || (food.nutritions.fat = 0);
                                    food.nutritions.sugar || (food.nutritions.sugar = 0);
                                    food.nutritions.calo || (food.nutritions.calo = 0);
                                    food.nutritions.canxi || (food.nutritions.canxi = 0);
                                    food.nutritions.b1 || (food.nutritions.b1 = 0);
                                    if (food.is_meat + '' == '1' || food.is_meat + '' == 'true') {
                                        food.is_meat = true;
                                    } else {
                                        food.is_meat = false;
                                    }
                                    //Kiểm tra & xử lý t/h lượng 1 trẻ lưu chưa chính xác
                                    if(scope.sys.configs.tdm_check_error_quantity_edit){
                                        if(scope.check_error_quantity_edit(food)){
                                            food.quantity_edit = food.quantity;
                                        }
                                    }
                                    if (food.is_meat) { /*Nếu là động vật*/
                                        rs.animal_protein += $['*'](food.nutritions.protein, food.quantity_edit) / 100;
                                        rs.animal_fat += $['*'](food.nutritions.fat, food.quantity_edit) / 100;
                                    } else {
                                        rs.vegetable_protein += $['*'](food.nutritions.protein, food.quantity_edit) / 100;
                                        rs.vegetable_fat += $['*'](food.nutritions.fat, food.quantity_edit) / 100;
                                    }
                                    rs.sugar += $['*'](food.nutritions.sugar, food.quantity_edit) / 100;
                                    rs.canxi += $['*'](food.nutritions.canxi, food.quantity_edit) / 100;
                                    rs.b1 += $['*'](food.nutritions.b1, food.quantity_edit) / 100;
                                    rs.calo += scope.getCalo(food, 'quantity_edit');
                                }
                            });
                        });
                    });
                    group_adjust.tong = rs;
                    return rs;
                };
                /*
                * Tính toán tổng tiềngetChenhlech1treSchoolpoint
                * */
                scope.totalCalculator = function(data, not_inventoriesDivi){ /* Tính toàn dữ liệu để đưa ra bảng dinh dưỡng */
                    scope.row.thanhtien1nhom = 0;
                    scope.row.tien1tres = {};
                    scope.row.thanhtien1nhoms = {};
                    data || (data = scope.datagrid.data);
                    /*  Tính theo tp xuất kho */
                    if (!not_inventoriesDivi) {
                        scope.inventoriesDivision();
                    }
                    angular.forEach(data, function(kho, warehouse_id) {
                        angular.forEach(kho, function(food,index) {
                            food.price || (food.price = 0);
                            food.thanhtien1nhom = 0;
                            food.tien1tres = {};
                            // food.thanhtien1nhoms = {};
                            if(food.exports) {
                                angular.forEach(food.exports, function(fd, food_id_price){
                                    food.thanhtien1nhom = $['+'](food.thanhtien1nhom, $['*'](fd.quantity, fd.price));
                                    if (count(scope.row.sotres)>1) {
                                        if (!!fd.quantities) {
                                            fd.quantities = scope.divide(fd.quantity, food.thucmuatheodvts, true);
                                        }
                                        angular.forEach(fd.quantities, function (val, point) {
                                            scope.row.thanhtien1nhoms[point] || (scope.row.thanhtien1nhoms[point] = 0);
                                            scope.row.thanhtien1nhoms[point] = $['+'](scope.row.thanhtien1nhoms[point], $['*'](fd.price, val));
                                            
                                            food.tien1tres[point] = food.tien1tres[point] || 0;
                                            food.tien1tres[point] = $['+'](food.tien1tres[point], $['*'](fd.price, val));

                                            scope.row.tien1tres[point] = scope.row.tien1tres[point] || 0;
                                            scope.row.tien1tres[point] = $['+'](scope.row.tien1tres[point], $['*'](fd.price, val));
                                        });
                                    }
                                });
                            }else{
                                food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                                if (count(scope.row.sotres)>1) {
                                    if (!food.thucmuatheodvts) {
                                        food.thucmuatheodvts = scope.divide(food.thucmuatheodvt, scope.row.sotres, true);
                                    }
                                    angular.forEach(food.thucmuatheodvts, function (val, point) {
                                        scope.row.thanhtien1nhoms[point] || (scope.row.thanhtien1nhoms[point] = 0);
                                        scope.row.thanhtien1nhoms[point] = $['+'](scope.row.thanhtien1nhoms[point], $['*'](food.price, val));
                                        food.tien1tres[point] = food.tien1tres[point] || 0;
                                        food.tien1tres[point] = $['+'](food.tien1tres[point], $['*'](food.price, val));

                                        scope.row.tien1tres[point] = scope.row.tien1tres[point] || 0;
                                        scope.row.tien1tres[point] = $['+'](scope.row.tien1tres[point], $['*'](food.price, val));
                                    });
                                }
                            }
                            if(scope.row.meal_selection[warehouse_id].selected || !scope.sys.configs.cdkp_tien1tre_by_meal){
                                scope.row.thanhtien1nhom = $['+'](scope.row.thanhtien1nhom, food.thanhtien1nhom);
                            }
                        });
                    });
                    // scope.row.thanhtien1nhom = $['+'](scope.row.thanhtien1nhom, scope.surplus.end[scope.selected.group_adjust.group_id]);
                    var service = scope.getTiendichvu();
                    scope.row.tongtien_dichvu = $['*'](service, scope.row.sotre);
                    var tien_bo_tro = scope.row.tien_bo_tro || 0;
                    scope.row.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), scope.row.thanhtien1nhom),scope.row.tongtien_dichvu);
                    scope.row.tongtienchenhlech = $['+'](scope.row.tongtienchenhlech, tien_bo_tro);
                    if (scope.selected.group_adjust) {
                        if (scope.configs.calc_chenh_lech_dau_ngay) {
							scope.surplus.end || (scope.surplus.end = {});
                            scope.row.tongtienchenhlech = $['+'](scope.row.tongtienchenhlech, scope.surplus.end[scope.selected.group_adjust.group_id]);
                        }
                        scope.tong = scope.calcNutrition(scope.selected.group_adjust.meals);
                    }
                    scope.getTotalMeal();
                };
                scope.arrayWrap = function (collection) {
                    if (typeof collection === 'object') {
                        collection = _.reduce(collection, function (arr, item) {
                            arr.push(item);

                            return arr;
                        }, []);

                        collection = _.orderBy(collection, ['addedAt'], 'asc');

                        return collection;
                    }

                    return collection;
                };
                scope.addFoodFromDish = function(meal, dish, is_change) {
                    var $tmp_foods = {};
                    if(!scope.selected.menu_adjust){
                        scope.selected.menu_adjust = {}
                    }
                    if(!scope.selected.menu_adjust.nutritions) {
                        scope.selected.menu_adjust.nutritions = {};
                    }
                    var nutritions = scope.selected.menu_adjust.nutritions;
                    if(meal != undefined && dish != undefined && scope != undefined) {
                        var meal_key = 2;
                        if (meal.define == 'buatoi') {
                            meal_key = 3;
                        } else if (meal.define == 'buasang') {
                            meal_key = 1;
                        }
                        var warehouse_id = scope.getMenu_infomation(meal.define).warehouse_id;
                        var foods = {};
                        angular.forEach(dish.ingredient, function(food, food_id){
                            if (food) {
                                food.quantities || (food.quantities = {});
                                if(typeof food.quantities == 'array') {
                                    food.quantities = {};
                                    food.quantities[scope.row.group_id] = food.quantity;
                                }
                                foods[food.food_id] = food;
                            }
                        });
                        dish.ingredient = foods;
                        angular.forEach(dish.ingredient, function(food, fd_id){
                            var food_id = food.food_id;
                            scope.inventory[warehouse_id] || (scope.inventory[warehouse_id] = {});
                            if(scope.inventory[warehouse_id][food_id]) {
                                food.is_market = 0;
                                if(!food.price) {
                                    food.price = scope.inventory[warehouse_id][food_id].price;
                                }
                            }else{
                                food.is_market = 1;
                                scope.prices[food_id] || (scope.prices[food_id] = 0);
                                if(scope.prices[food_id] == 0){
                                    if(food.price){
                                        scope.prices[food_id] = food.price;
                                    }
                                }
                                food.price = scope.prices[food_id];
                            }
                            scope.datagrid.data[meal_key] || (scope.datagrid.data[meal_key] = {});
                            food.gam_exchange || (food.gam_exchange = 1000);
                            if (typeof food.quantity_edit === 'undefined') {
                                food.quantity_edit = Number(food.quantity);
                            }
                            food.food_id || (food.food_id = food_id);
                            food.price_kg = $['/']($['*'](food.price,1000),food.gam_exchange);
                            nutritions[food_id] || (nutritions[food_id] = food.nutritions);
                            if(!scope.datagrid.data[meal_key][food_id]) {
                                var row = clone(food);
                                row.nutritions = nutritions[food_id];
                                row.luong1tre = 0;
                                row.foods = [];
                                /*Cập nhật giá theo nhập kho nếu trong kho còn*/
                                if(scope.inventory[warehouse_id]){
                                    if(scope.inventory[warehouse_id][food_id]){
                                        if(scope.inventory[warehouse_id][food_id]>0) {
                                            row.price = scope.inventory[warehouse_id][food_id].price;
                                        }
                                    }
                                }
                                row.price_kg = row.price*1000/row.gam_exchange;
                                scope.datagrid.data[meal_key][food_id] = row;
                            }
                            //Cập nhật đơn vị tính theo kho
                            if (scope.inventory[warehouse_id]) {
                                if (scope.inventory[warehouse_id][food_id] && scope.inventory[warehouse_id][food_id].inventory > 0) {
                                    food.measure_id = scope.inventory[warehouse_id][food_id].measure_id;
                                    food.measure_name = scope.getMeasure(food.measure_id).name;
                                }
                            }
                            scope.datagrid.data[meal_key][food_id].foods.push(food);
                            $tmp_foods[food.food_id] = scope.datagrid.data[meal_key][food_id];
                        });
                        var dishes = {};
                        angular.forEach(meal.dishes, function(ds,ds_id){
                            dishes[ds.id] = ds;
                        });
                        dishes[dish.id] = dish;
                        meal.dishes = dishes;
                        angular.forEach(scope.datagrid.data, function (foods, meal_key) {
                            angular.forEach(foods, function (food, food_id) {
                                //Cập nhật đơn vị tính theo kho
                                if (scope.inventory[warehouse_id]) {
                                    if (scope.inventory[warehouse_id][food_id] && scope.inventory[warehouse_id][food_id].inventory > 0) {
                                        food.measure_id = scope.inventory[warehouse_id][food_id].measure_id;
                                        food.measure_name = scope.getMeasure(food.measure_id).name;
                                    }
                                }
                                food.luong1tre = 0;
                                angular.forEach(food.foods, function (fd, ind) {
                                    food.luong1tre = $['+'](food.luong1tre, fd.quantity_edit);
                                });
                                food.luong1tre = scope.round(food.luong1tre, 8);
                                scope.onChange_luong1tre(food, true);
                            });
                        });
                    }
                    return $tmp_foods;
                };
                /* Kiểm tra đạt cả 2 quyết định 777 và hn */
                scope.ktDat2TT = function(nutrition_name){
                    var rs = {
                        level: 0,
                        title: ''
                    };
                    if(scope.selected.group) {
                        if(scope.selected.group.is_tt28_old) {
                            rs.title = 'Khuyến nghị: ' + scope.selected.group.tt28_old[nutrition_name+'_min']+' - '+scope.selected.group.tt28_old[nutrition_name+'_max'];
                            var tiles = scope.getTile_PLG();
                            if(tiles[nutrition_name]) {
                                var tile = tiles[nutrition_name];
                                var tile_old = scope.selected.group.tt28_old;
                                var tile_new = scope.selected.group;
                                var min = tile_old[nutrition_name+'_min'];
                                var min_under = min;
                                if(min < tile_new[nutrition_name+'_min']) {
                                    min = tile_new[nutrition_name+'_min'];
                                }else{
                                    min_under = tile_new[nutrition_name+'_min'];
                                }
                                var max = tile_old[nutrition_name+'_max'];
                                var max_over = max;
                                if(max > tile_new[nutrition_name+'_max']) {
                                    max = tile_new[nutrition_name+'_max'];
                                }else{
                                    max_over = tile_new[nutrition_name+'_max'];
                                }
                                if( tile >= tile_new[nutrition_name+'_min'] &&
                                    tile <= tile_new[nutrition_name+'_max'] &&
                                    tile >= tile_old[nutrition_name+'_min'] &&
                                    tile <= tile_old[nutrition_name+'_max']
                                ){
                                    rs.level = 0;
                                }else if(tile >= tile_new[nutrition_name+'_min'] && tile <= tile_new[nutrition_name+'_max']){
                                    rs.level = 1;
                                }else if(tile >= tile_old[nutrition_name+'_min'] && tile <= tile_old[nutrition_name+'_max']){
                                    rs.level = 2;
                                }else{
                                    rs.level = 0;
                                }
                            }
                        }
                    }
                    return rs;
                };
                scope.ktDat2TTMain = function(){
                    var rs = {
                        level: 0,
                        title: 'Xem chi tiết thông tin về chất'
                    }
                    var plg = [];
                    var nutrition = scope.ktDat2TT('protein');
                    if(nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    nutrition = scope.ktDat2TT('fat');
                    if(nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    nutrition = scope.ktDat2TT('sugar');
                    if(nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    if(plg.length > 0){
                        rs.title = 'TỈ lệ ' + plg.join(', ') + ' chưa đáp ứng theo khung SGD Hà Nội.',
                            rs.level = 1;
                    }
                    return rs;
                };
                scope.delFood = function(kho,id){
                    if(!kho) {
                        kho = scope.selected.warehouse;
                    }
                    var tmp = [];
                    var kt = false;
                    angular.forEach(scope.datagrid.data[kho], function(item,index){
                        if(id != item.id) {
                            tmp.push(item);
                        }else{
                            kt = true;
                            delete item;
                        }
                    })
                    if(kt) {
                        scope.datagrid.data[kho] = tmp;
                        if(scope.menu_adjust.editable.id == id) {
                            scope.menu_adjust.editable.id = null;
                        }
                    }
                };
                scope.delAllFoodInWarehouse = function(kho) {
                    if(!kho) {
                        kho = scope.selected.warehouse;
                    }
                    delete scope.datagrid.data[kho];
                    scope.datagrid.data[kho] = [];
                };
                scope.delAllFood = function() {
                    scope.datagrid.data = [];
                };
                scope.applyFoodPrice = function(el){ /*Thay đổi kiểu lưu giá thực phẩm chung hay riên từng mẫu thực đơn*/
                    process('dinhduong/menu_adjust/applyfoodprice',{value: el.value},function(resp){
                        if(resp.result != 'success'){
                            if(el.value == 0) {
                                el.value = 1;
                            }else{
                                el.value = 0;
                            }
                        }
                    },function(){
                        if(el.value == 0) {
                            el.value = 1;
                        }else{
                            el.value = 0;
                        }
                    });
                };
                /* Tải lại bảng danh sách */
                scope.reloadDatagrid = function(){
                    var queryParams = $('#tbl_'+self.module).datagrid('options').queryParams;
                    queryParams.month = scope.selected.month.id;
                    queryParams.keysearch_name = scope.selected.keysearch_name;
                    console.log(scope.selected.keysearch_name)
                    scope.setMonth_selected(queryParams.month);
                    queryParams.showDelete = scope.menu_adjust.showDelete;
                    $('#tbl_'+self.module).datagrid('load',queryParams);
                };

                // Tiện ích hiện mẫu ăn sáng của Phòng GD Huế
                scope.changeMauKhongAnSang = function() {
                    localStorage.setItem('mauKhongAnSang', scope.mauKhongAnSang);

                    window.location.reload();
                };

                /*select tháng thay đổi trên form danh sách*/
                scope.onDaySelect = function(isMarektbillEdit, point){
                    scope.loadDataOfDay(isMarektbillEdit, point);
                };
                scope.getDateFull = function(date){
                    var row_selected = $('#tbl_menu_adjust').datagrid('getSelected');
                    if(row_selected != null){
                        date = row_selected.date;
                    }else{
                        var day = (new Date()).getDate();
                        date = day+"/"+date;
                    }
                    return date;
                };
                scope.getTiendichvu = function(group_id){
                    var rs = 0;
                    if(scope.row.services) {
                        angular.forEach(scope.row.services, function(service, ind){
                            rs += Number(service.price);
                        });
                    }
                    return rs;
                };
                scope.addService = function(){
                    scope.row.services || (scope.row.services = []);
                    scope.row.services.push({
                        name: '',
                        price: 0
                    });
                };
                scope.delService = function(index){
                    var rs = [];
                    angular.forEach(scope.row.services, function(service, ind){
                        if(ind != index){
                            rs.push(service);
                        }
                    });
                    scope.row.services = rs;
                };
                scope.onChangeSotres = function (point) {
                    if (scope.row.sotres) {
                        if (scope.row.sotres_old[point] != scope.row.sotres[point]) {
                            scope.row.sotre = 0;
                            for (var i in scope.row.sotres) {
                                scope.row.sotre = $['+'](scope.row.sotre, scope.row.sotres[i]);
                            }
                            scope.onChangeSotre(scope.row);
                        }
                    }
                };
                scope.onChangeTienbotros = function (point) {
                    if (scope.row.tien_bo_tros) {
                        if (scope.row.tien_bo_tros_old[point] != scope.row.tien_bo_tros[point]) {
                            scope.row.tien_bo_tro = 0;
                            for (var i in scope.row.tien_bo_tros) {
                                scope.row.tien_bo_tro = $['+'](scope.row.tien_bo_tro, scope.row.tien_bo_tros[i]);
                            }
                            scope.onChangeTienbotro(scope.row);
                        }
                    }
                };
                scope.editServicePrice = function(group_id){
                    $.dm_datagrid.showEditForm(
                        {
                            module: $CFG.project+'/menu_adjust',
                            action:'edit',
                            title:'Chỉnh sửa',
                            size: size.small,
                            showButton: false,
                            scope: scope,
                            content: scope.controller.templates.editServicePrice,
                            onShown: function(element){
                                // scope.$apply(function(){
                                //     $(element).html(scope.getTemplate());
                                // });
                            }
                        }
                    );
                };
                scope.getTiendicho1tre = function(){
                    var rs = 0;
                    angular.forEach(scope.datagrid.data, function(kho,index){
                        angular.forEach(kho, function(food,index){
                            rs = $['+'](rs,food.tiendicho1tre);
                        });
                    });

                    return scope.round(rs,0);
                };
                scope.getTienchenhlech = function(){
                    var tiendicho1tre = scope.getTiendicho1tre();
                    var tiendichvu = scope.getTiendichvu(scope.selected.group.id);
                    var tienchenhlech = $['-']($['-'](scope.row.tien1tre, tiendicho1tre),tiendichvu);

                    return tienchenhlech;
                };
                scope.getTongtienchenhlech = function(){
                    return $['*'](scope.getTienchenhlech(),scope.row.sotre);
                };
                scope.getMeasure = function(id){
                    return scope.mapMeasures[id] || {name: null};
                };
                /*  Xử lý tácg thực phẩm xuất kho*/
                scope.checkForExport = function(){
                    // scope.menu_adjust.group_adjusts[group_id].menu_plannings.push
                    /*làm mới thực phẩm kho*/
                    angular.forEach(scope.inventory, function(foods,warehouse_id){
                        angular.forEach(foods, function(food,food_id){
                            angular.forEach(food.foods, function(fd, food_key){
                                fd.ton = fd.inventory;
                            })
                        })
                    })
                    /*Xử lý ds thực đơn*/
                    angular.forEach(scope.menu_adjust.group_adjusts, function(adjust,group_id){
                        angular.forEach(adjust.menu_plannings, function(menu_planning,ind){
                            angular.forEach(menu_planning.data,function(foods,warehouse_id){
                                if(!scope.inventory[warehouse_id]) return;
                                var inventories = scope.inventory[warehouse_id];
                                angular.forEach(foods, function(food,food_id){
                                    food.exports = {}
                                    if(inventories[food_id]) {
                                        angular.forEach(inventories[food_id].foods, function(fdi, price){
                                            if(fdi.ton > 0){
                                                if(fdi.ton >= food.thucmuatheodvt){
                                                    food.exports[price] = food.thucmuatheodvt;
                                                    fdi.ton = $['-'](fdi.ton,food.thucmuatheodvt);
                                                }else{

                                                }
                                            }
                                        })
                                    }
                                })
                            })
                        })
                    })
                };
                /*  Thêm thực đơn vào cân đối */
                scope.addMenu_planning = function(menu_planning) {
                    var data = {};
                    scope.datagrid.data = {};
                    var row = {};
                    row.mathucdon = menu_planning.name;
                    row.sotre = parseInt(menu_planning.data.together.sotre);
                    row.sotre_old = row.sotre;
                    row.tien1tre = parseFloat(menu_planning.data.together.tien1tre);
                    row.tienchenhlech1tre = parseInt(menu_planning.data.together.tienchenhlech1tre);
                    row.tongtienchenhlech = parseInt(menu_planning.data.together.tongtienchenhlech);
                    row.services = menu_planning.row.services;
                    row.meal_selection = menu_planning.data.together.meal_selection;
                    if (!row.meal_selection) {
                        row.meal_selection = {
                            1: {selected: false, name: 'Ăn sáng', visible: true},
                            2: {selected: true, name: 'Ăn chính', visible: true},
                            3: {selected: false, name: 'Ăn tối', visible: false}
                        }
                    }
                    if($CFG.school_points > 0){
                        if(count(scope.school_point.points)>1 && scope.school_point.together) {
                            if(!row.sotres){
                                row.sotres = {
                                    1: row.sotre
                                }
                            }
                            if(!row.tien_bo_tros){
                                row.tien_bo_tros = {
                                    1: row.tien_bo_tro
                                }
                            }
                            row.sotre = 0;
                            row.tien_bo_tro = 0;
                            angular.forEach(scope.school_point.points, function(point,index){
                                if(!row.tien_bo_tros[index+1]){
                                    row.tien_bo_tros[index+1] = 0;
                                }
                                row.tien_bo_tro = $['+'](row.tien_bo_tro, row.tien_bo_tros[index+1]);
                                if(!row.sotres[index+1]){
                                    row.sotres[index+1] = 0;
                                }
                                row.sotre = $['+'](row.sotre, row.sotres[index+1] || 0);
                            });
                        }
                    }
                    var group_adjust = {
                        id: menu_planning.id,
                        group_id: menu_planning.group_id,
                        group_key: menu_planning.group_key,
                        data: data,
                        row: row,
                        meals: menu_planning.data.meals,
                        nutritions: {}
                    };
                    scope.addGroup_adjust(group_adjust);
                    scope.menu_planningSelected(group_adjust);
                    scope.selected.group_adjust = group_adjust;
                    var warehouse_diffrent = false;
                    menu_planning.row.data || (menu_planning.row.data = {});
                    angular.forEach(scope.menu_informations, function(item, index) {
                        group_adjust.meals || (group_adjust.meals = {});
                        group_adjust.meals[item.define] || (group_adjust.meals[item.define] = clone(item));
                        if (item.define == 'buasang' && count(group_adjust.meals['buasang'].dishes) > 0 && !menu_planning.row.data[1]) {
                            warehouse_diffrent = true;
                        }
                        group_adjust.meals[item.define].warehouse_id = item.warehouse_id;
                    });
                    if(count(menu_planning.row.data) == 0 || warehouse_diffrent) {
                        angular.forEach(menu_planning.data.meals,function(meal,meal_define){
                            angular.forEach(meal.dishes, function(dish,dish_id){
                                scope.addFoodFromDish(meal, dish);
                            });
                        });
                        data = scope.datagrid.data;
                        angular.forEach(data, function(kho, warehouse_id){
                            angular.forEach(kho, function(food){
                                scope.onChange_luong1tre(food,true);
                            });
                        });
                    }else{
                        var warning = [];
                        angular.forEach(menu_planning.row.data, function(kho, meal_key){
                            data[meal_key] = {};
                            angular.forEach(kho, function(food, food_id){
                                if(scope.inventory[meal_key]) {
                                    if(scope.inventory[meal_key][food.food_id]) {
                                        food.is_market = 0;
                                    } else {
                                        food.is_market = 1;
                                    }
                                }
                                if(!food.thucmuatheodvt_view){
                                    food.thucmuatheodvt_view = food.thucmuatheodvt;
                                }
                                food.foods = [];
                                var food_tmp = clone(food);
                                scope.onChange_thucmuatheodvt(food_tmp, true, true);
                                if(Math.abs($['-'](food_tmp.luong1tre, food.luong1tre))>0.1) {
                                    warning.push(food.name);
                                    food.luong1treDif = true;
                                    food.luong1tre = food_tmp.luong1tre;
                                }
                                data[meal_key][food_id] = food;
                                food.supplier = '';
                                group_adjust.nutritions[food_id] || (group_adjust.nutritions[food_id] = food.nutritions);
                                if (!food.thucmuctheodvts) {
                                    scope.divide_pointThucmuatheodvt(food, group_adjust);
                                }
                            });
                        });
                        if(warning.length > 0)
                        setTimeout(function () {
                            alert('Một số thực phẩm bị thay đổi calo vì "Hệ số quy đổi" hoặc "Hệ số thải bỏ" đã bị thay đổi: ' + warning.join(';'));
                        });
                        scope.extrude_factor || (scope.extrude_factor = {});
                        angular.forEach(menu_planning.data.meals, function(meal, meal_define){
                            var warehouse_id = meal.warehouse_id;
                            var wh = data[warehouse_id];
                            if(wh){
                                angular.forEach(meal.dishes, function(dish, dish_id){
                                    angular.forEach(dish.ingredient, function(food,food_id){
                                        food.supplier = '';
                                        group_adjust.nutritions[food_id] || (group_adjust.nutritions[food_id] = food.nutritions);
                                        food.nutritions = group_adjust.nutritions[food_id];
                                        if(wh[food_id]){
                                            wh[food_id].gam_exchange || (wh[food_id].gam_exchange=1000);
                                            if(!wh[food_id].name_edit){
                                                wh[food_id].name_edit = food.name;
                                            }
                                            if(wh[food_id].price == 0){
                                                wh[food_id].price = food.price;
                                            }else{
                                                food.price = wh[food_id].price;
                                            }
                                            wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price,1000),wh[food_id].gam_exchange));
                                            wh[food_id].is_meat = food.is_meat;
                                            wh[food_id].is_veget = food.is_veget;
                                            wh[food_id].is_dry = food.is_dry;
                                            wh[food_id].nutritions = group_adjust.nutritions[food_id];
                                            wh[food_id].extrude_factor_root = wh[food_id].extrude_factor;
                                            wh[food_id].gam_exchange_root = wh[food_id].gam_exchange;
                                            food.price_kg = wh[food_id].price_kg;
                                            food.gam_exchange = wh[food_id].gam_exchange;
                                            wh[food_id].foods.push(food);
                                        }
                                    });
                                });
                            }
                        });
                        if(warning.length > 0) {
                            angular.forEach(data, function (foods) {
                                angular.forEach(foods, function (food) {
                                    if (food.luong1treDif) {
                                        scope.onChange_luong1tre(food, true);
                                    }
                                });
                            });
                        }
                    }
                    scope.selected.meals = scope.selected.group_adjust.meals;
                    scope.totalCalculator();
                };
                scope.addGroup_adjust = function(group_adjust){
                    var menu_planning_id = group_adjust.id;
                    var group_id = group_adjust.group_key;
                    scope.menu_adjust.group_adjusts || (scope.menu_adjust.group_adjusts = {});
                    scope.menu_adjust.group_adjusts[group_id] || (scope.menu_adjust.group_adjusts[group_id] = {
                        name: scope.getGroup(group_id).name,
                        menu_plannings: []
                    });
                    /*Tính toán trường hợp nhiều điểm trường nhưng đi chợ chung*/
                    if(typeof group_adjust.row.sotres != 'undefined' && count(group_adjust.row.sotres) > 1){
                        group_adjust.row.sotre = 0;
                        angular.forEach(group_adjust.row.sotres, function(point,index){
                            group_adjust.row.sotre = $['+'](group_adjust.row.sotre, parseInt(point) || 0);
                        });
                    }
                    scope.menu_adjust.group_adjusts[group_id].menu_plannings.push(group_adjust);
                };
                scope.menu_adjust.foodSelected = function(dish_selected) {
                    scope.menu_adjust.ignore_ids = '';
                    var id = dish_selected.food_id;
                    scope.selected.dish.ingredient[id] = dish_selected;
                    angular.forEach(scope.selected.dish.ingredient, function(dish,key){
                        scope.menu_adjust.ignore_ids = scope.menu_adjust.ignore_ids + ',' + key;
                    })
                };
                /* Tính toán tỉ lệ đạt của đạm*/
                scope.getTile_dat_protein = function(){
                    var name='tong';
                    if(!scope.selected.group_adjust || !scope.selected.group_adjust.group_id) return '-';
                    var nutritions = scope.getGroup(scope.selected.group_adjust.group_id).nutritions;
                    var value = ( scope[name].animal_protein + scope[name].vegetable_protein )
                        / ( nutritions.animal_protein + nutritions.vegetable_protein)*100;
                    return value;
                };
                /* Tính toán tỉ lệ đạt béo*/
                scope.getTile_dat_fat = function(){
                    var name='tong';
                    if(!scope.selected.group_adjust || !scope.selected.group_adjust.group_id) return '-';
                    var nutritions = scope.getGroup(scope.selected.group_adjust.group_id).nutritions;
                    var value = ( scope[name].animal_fat + scope[name].vegetable_fat )
                        / ( nutritions.animal_fat + nutritions.vegetable_fat )*100;

                    return value;
                };
                /* Tính toán tỉ lệ đạt đường*/
                scope.getTile_dat_sugar = function(){
                    var name ='tong';
                    if(!scope.selected.group_adjust || !scope.selected.group_adjust.group_id) return '-';
                    var nutritions = scope.getGroup(scope.selected.group_adjust.group_id).nutritions;
                    var value = scope[name].sugar / nutritions.sugar * 100;
                    return value;
                };
                /* Tính toán tỉ lệ đạt calo*/
                scope.getTile_dat_calo  = function(){
                    var name ='tong';
                    if(!scope.selected.group_adjust || !scope.selected.group_adjust.group_id) return '-';
                    var nutritions = scope.getGroup(scope.selected.group_adjust.group_id).nutritions;
                    var value = scope[name].calo / nutritions.calo * 100;
                    return round(value,0);
                };
                scope.getTile_dat = function(){
                    var name ='tong';
                    var value = 0;
                    if(!scope.selected.group_adjust || !scope.selected.group_adjust.group_id) return '-';
                    var group = scope.getGroup(scope.selected.group_adjust.group_id);
                    if(name == 'protein') {
                        value = ( scope.tong_chinh.animal_protein + scope.tong_chinh.vegetable_protein ) / ( group.nutritions.animal_protein + group.nutritions.vegetable_protein);
                    }
                    if(name == 'fat') {
                        value = ( scope.tong_chinh.animal_fat + scope.tong_chinh.vegetable_fat ) / ( group.nutritions.animal_fat + group.nutritions.vegetable_fat );
                    }
                    return value;
                };
                scope.getTiletungloai = function(name){
                    var value = 0;
                    if(scope.selected.group){
                        if(scope.selected.group.nutritions) {
                            if(!scope.selected.group.nutritions[name]) {
                                return value;
                            }
                            var tongloai = scope.tong[name];
                            if(name == 'calo'){
                                tongloai = scope.tong.calo;
                            }
                            value = scope.tong[name]/scope.selected.group.nutritions[name];
                        }
                    }
                    return value*100;
                };
                scope.getTile_dongthucvat = function(name){
                    var value = 0;
                    if(!scope.selected.group) return '-';
                    if(name == 'animal_protein') {
                        value = (scope.tong.animal_protein/(scope.tong.animal_protein+scope.tong.vegetable_protein))*100
                    }
                    if(name == 'vegetable_protein') {
                        value = (scope.tong.vegetable_protein/(scope.tong.animal_protein+scope.tong.vegetable_protein))*100;
                    }
                    if(name == 'animal_fat') {
                        value = (scope.tong.animal_fat/(scope.tong.animal_fat+scope.tong.vegetable_fat))*100;
                    }
                    if(name == 'vegetable_fat') {
                        value = (scope.tong.vegetable_fat/(scope.tong.animal_fat+scope.tong.vegetable_fat))*100;
                    }
                    return value;
                };
                scope.getNormBuasang = function(){
                    var rs = {biggest_rate:0,smallest_rate:0};
                    if(scope.row.meal_selection[1].selected) {
                        if(scope.selected.group_adjust){
                            if(scope.selected.group_adjust.meals) {
                                if(scope.selected.group_adjust.meals.buasang) {
                                    if(count(scope.selected.group_adjust.meals.buasang.dishes)>0) {
                                        if(scope.selected.group){
                                            var chenh = scope.selected.group.biggest_rate - scope.selected.group.smallest_rate;
                                            if(scope.selected.group.group_children == 0) {
                                                rs.biggest_rate = 80 - scope.selected.group.biggest_rate;
                                            }else{
                                                rs.biggest_rate = 70 - scope.selected.group.biggest_rate;
                                            }
                                            if(rs.biggest_rate < 0){
                                                rs.biggest_rate = 0;
                                            }else{
                                                if(rs.biggest_rate>=15){
                                                    rs.smallest_rate = 10;
                                                }else if(rs.biggest_rate>=10) {
                                                    rs.smallest_rate = 5;
                                                }else{
                                                    rs.smallest_rate = 0;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    return rs;
                };
                scope.getNormBuachinh = function(){
                    var rs = {biggest_rate:0,smallest_rate:0}
                    if(scope.row.meal_selection[2].selected) {
                        if(scope.selected.group){
                            rs.biggest_rate = scope.selected.group.biggest_rate;
                            rs.smallest_rate = scope.selected.group.smallest_rate;
                        }
                    }
                    return rs;
                };
                scope.menu_adjust.printPreview = function(el){
                    var urls_export = [$CFG.remote.base_url,'report',$CFG.project,'menu_adjust','exportPrint'];
                    var btn = '<a id="btn_preview" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span></a>';
                    $('#export-dialog-printer-menu_adjust #btn_preview').printPage({
                        url: urls_export.join('/')+'?date='+scope.menu_adjust.date+'&group_id='+scope.selected.group_adjust.group_id+'&apdungcongthuc='+scope.apdungcongthuc+'&warehouse_ids='+scope.menu_adjust.getWarehouse_ids().join(','),
                        attr: "href",
                        message:"Phiếu xuất kho đang được tạo ..."
                    });
                };
                scope.getWarehouse_ids = function(){
                    var rs = [];
                    angular.forEach(scope.row.meal_selection, function(meal, meal_define){
                        if(meal.selected && meal.visible) {
                            rs.push(meal_define);
                        }
                    });
                    return rs;
                };
                scope.openDialogPrint = function() {
                    $.dm_datagrid.showEditForm({
                        module: $CFG.project+'/menu_adjust',
                        action:'edit',
                        title:'Chỉnh sửa',
                        size: 350,
                        showButton: false,
                        scope: scope,
                        content: scope.controller.templates.printForm,
                        onShown: function(element) {
                        }
                    });
                    return;
                    var urls_export = [$CFG.remote.base_url,'report',$CFG.project,'menu_adjust','exportPrint'];
                    var btn = $('<a id="btn_preview" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span></a>');
                    $('#export-dialog-printer-menu_adjust').dialog({
                        title: 'Tỉ lệ chất đạt được',
                        width: 350,
                        height: 280,
                        closed: false,
                        cache: false,
                        modal: true ,
                        onOpen : function (ele) {
                            $(ele).show();
                            $('#export-dialog-printer-menu_adjust .btn-content-form').html('').append(btn);
                            btn.printPage({
                                url: urls_export.join('/')+'?date='+scope.menu_adjust.date+'&group_id='+scope.selected.group_adjust.group_id+'&apdungcongthuc='+(scope.apdungcongthuc?1:0)+'&warehouse_ids='+scope.menu_adjust.getWarehouse_ids().join(','),
                                attr: "href",
                                message:"Phiếu xuất kho đang được tạo ..."
                            });
                        }
                    });
                };
                scope.showMealsCaloInfo = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: '',
                            title:'Thông tin món ăn',
                            draggable: true,
                            fullScreen: false,
                            scope: scope,
                            showButton: false,
                            content: scope.controller.templates.quantityInfo,
                            onShown: function(){
                                scope.totalCalculator();
                            }
                        }
                    );
                };
                scope.showNutritionInfo = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: '',
                            title:'Thông tin món ăn',
                            draggable: true,
                            fullScreen: false,
                            showButton: false,
                            scope: scope,
                            content: scope.controller.templates.nutritionInfo
                        }
                    );
                };
                scope.showAdd_Menu_planning = function(){
                    var dialog = null;
                    scope.selected.group_add_point = scope.selected.point;
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/menu_adjust',
                            action: 'add',
                            title: 'Thêm thực đơn cho nhóm trẻ',
                            fullScreen: false,
                            showButton: false,
                            scope: scope,
                            content: scope.controller.templates.addMenuPlanning,
                            onShow: function() {
                                scope.selected.group_add = undefined;
                                scope.selected.group_add_mn = undefined;
                                scope.menu_adjust.group_addChange();
                            }
                        }
                    );
                };
                scope.selectedMenu_planning = function(menu_planning, menu_planning_name){
                    if(menu_planning){
                        console.log(11111)
                        process('dinhduong/menu_adjust/getMenu_planning',{id: menu_planning.id, async: true},function(resp){
                            if(resp.result == 'success') {
                                scope.$apply(function(){
                                    var mn_planning = resp.data;
                                    mn_planning.name = resp.data.row.name;
                                    mn_planning.group_id = resp.data.row.group_id;
                                    mn_planning.group_key = scope.selected.group_add.id;
                                    mn_planning.id = resp.data.row.id;
                                    if (mn_planning.data.together.meal_selection) {
                                        angular.forEach(mn_planning.data.together.meal_selection, function (m, ind) {
                                            m.selected = m.selected === 'true'?true:false;
                                            m.visible = m.visible === 'true'?true:false;
                                        });
                                    }
                                    scope.addMenu_planning(resp.data);
                                    scope.selected.group_add = undefined;
                                    scope.selected.group_add_mn = undefined;
                                    // dialogClose(null, true);
                                });
                            }
                        })
                    }
                    if(menu_planning_name){
                        var mn_planning = {
                            data: {
                                meals: {},
                                together: {
                                    group_id: scope.selected.group_add.id,
                                    id: 'addself_'+scope.selected.group_add.id,
                                    mathucdon: menu_planning_name,
                                    nutrition_fixed: 0,
                                    services: [],
                                    sotre: 10,
                                    tien1tre: (scope.configs.cdkp_tien1tre_default || 15000)
                                }
                            },
                            row: {
                                data: {},
                                meals: {},
                                group_id: scope.selected.group_add.id,
                                id: 'addself_'+scope.selected.group_add.id,
                                mathucdon: menu_planning_name,
                                services: [],
                                student_numbers: 10,
                                nutrition_fixed: 0,
                                name: menu_planning_name
                            }
                        };
                        mn_planning.name = mn_planning.row.name;
                        mn_planning.group_id = mn_planning.row.group_id;
                        mn_planning.group_key = scope.selected.group_add.id;
                        mn_planning.id = mn_planning.row.id;
                        mn_planning.row.services = [];
                        if(scope.services){
                            mn_planning.row.services = scope.services[scope.selected.group_add.id].services;
                        }
                        scope.addMenu_planning(mn_planning);
                        angular.forEach(scope.menu_informations, function(menu_info, ind){
                            mn_planning.row.meals[menu_info.define] = {
                                define: menu_info.define,
                                name: menu_info.name,
                                warehouse_id: menu_info.warehouse_id,
                                dishes: {}
                            };
                            mn_planning.data.meals[menu_info.define] = mn_planning.row.meals[menu_info.define];
                        });
                        //dialogClose(null, true);
                    }
                };

                scope.delDish = function(meal, dish) {
                    /*Đánh dấu thực phẩm phải xóa*/
                    angular.forEach(dish.ingredient, function(food,food_id){
                        food.delete = true;
                    });
                    /* Xóa thực phẩm trong kho theo món ăn*/
                    var tmp_foods = {};
                    var meal_key = 2;
                    if (meal.define == 'buatoi') {
                        meal_key = 3;
                    } else if (meal.define == 'buasang') {
                        meal_key = 1;
                    }
                    angular.forEach(scope.datagrid.data[meal_key], function(food,food_id){
                        var fds = [];
                        angular.forEach(food.foods, function(fd,index){
                            if(fd.delete != true){
                                fds.push(fd);
                            }else{
                                food.luong1tre -= fd.quantity_edit;
                            }
                        });
                        food.foods = fds;
                        if(food.luong1tre >= 0 && fds.length>0){
                            tmp_foods[food_id] = food;
                        }
                    });
                    scope.datagrid.data[meal_key] = tmp_foods;
                    angular.forEach(meal.dishes, function(ds,ds_id){
                        if(ds.id == dish.id){
                            delete meal.dishes[ds_id];
                        }
                    });
                    /* Xóa món ăn*/
                    scope.totalCalculator(scope.datagrid.data);
                    scope.menu_planningSelected();
                };
                scope.initFormDish = function(){
                    scope._temp = {
                        random: Math.random().toString().split('.')[1]
                    }
                };
                scope.initFormAddFood = function(){
                    scope.menu_adjust.selected || (scope.menu_adjust.selected = {});
                    scope.menu_adjust.selected.food_ids = [];
                    scope.menu_adjust.selected.food = undefined;
                };

                scope.onChangeTienbotro = function(){
                    scope.totalCalculator();
                };
                scope.onChangeSotre = function(row, divilthucmua) {
                    row.sotre_old || (row.sotre_old = row.sotre);
                    var sotre_old = row.sotre_old;
                    if(row.sotre+'' == 'Infinity'){
                        row.sotre = 0;
                    }
                    if(row.sotre == 0){
                        return;
                    }
                    var sotre = row.sotre;
                    angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                        var inventory = scope.inventory[warehouse_id];
                        if(!inventory) {
                            inventory = {};
                        }
                        angular.forEach(kho, function(food, index){
                            /* Nếu cố định lượng 1 trẻ hoặc thực phẩm là thực phẩm kho */
                            if(scope.menu_adjust.market_before || (inventory[food.food_id] && !scope.menu_adjust.fix_export)){
                                scope.onChange_luong1tre(food, true);
                            }else{ /* Nếu cố định theo thực mua */
                                scope.onChange_thucmuatheodvt(food, true, true);
                            }
                            if (divilthucmua) {
                                scope.divide_pointThucmuatheodvt(food, scope.selected.group_adjust);
                            }
                        });
                    });
                    scope.totalCalculator();
                    row.sotre_old = row.sotre;
                };
                scope.onChange_tien1tre = function(){
                    scope.totalCalculator();
                };

                scope.onApplyPrice = function () {
                    if (scope.mapPrice && false) {
                        scope.applyPrice(scope.mapPrice);
                    } else {
                        process($CFG.project + '/unit_food_detail/getMapPrice', {}, function (resp) {
                            if(resp.map){
                                scope.mapPrice = resp.map;
                                scope.applyPrice(scope.mapPrice);
                                $.messager.alert('Thông báo', 'Đã hoàn thành!');
                            }
                        });
                    }
                };

                scope.applyPrice = function (map) {
                    var mapStorage = {};
                    angular.forEach(scope.inventory, function (foods) {
                        angular.forEach(foods, function (food, foodId) {
                            mapStorage[foodId] = true;
                        });
                    });
                    var exist = (scope.selected.group_adjust || {}).meals || -1;
                    if (exist !== -1) {
                        angular.forEach(scope.selected.group_adjust.meals, function (meal) {
                            angular.forEach(meal.dishes, function (dish) {
                                angular.forEach(dish.ingredient, function (food, id) {
                                    if (map[id] && !mapStorage[id]) {
                                        food.price = map[id];
                                        food.price_kg = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                                        food.thanhtien1nhom = scope.round($['*'](food.price, food.thucmuatheodvt));
                                        food.tiendicho1tre = scope.round($['*'](food.thucmua1tretheodvt, food.price));
                                    }
                                });
                            });
                        });
                        angular.forEach(scope.selected.group_adjust.data, function (warehouses) {
                            angular.forEach(warehouses, function (food, id) {
                                if (map[id] && !mapStorage[id]) {
                                    food.price = map[id];
                                    food.price_kg = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                                    food.thanhtien1nhom = scope.round($['*'](food.price, food.thucmuatheodvt));
                                    food.tiendicho1tre = scope.round($['*'](food.thucmua1tretheodvt, food.price));
                                }
                            });
                        });
                        scope.datagrid.data = scope.selected.group_adjust.data;
                    }
                    scope.totalCalculator();
                };

                function mapMenuPlanning(collection) {
                    return _.map(collection, function (menu) {
                        menu.meals = _.reduce(menu.meals, function (meals, meal, index) {
                            var minTime = _.chain(meal.dishes)
                                .map('addedAt')
                                .compact()
                                .min()
                                .value() || (new Date).getTime();

                            meal.dishes = _.reduceRight(meal.dishes, function (dishes, dish, index) {
                                if (dish.addedAt === undefined) {
                                    dish.addedAt = minTime -= 1000;
                                }

                                dishes[index] = dish;

                                return dishes;
                            }, {});

                            meals[index] = meal;

                            return meals;
                        }, {});

                        return menu;
                    });
                }

                scope.buildDataForForm = function(resp) {
                    scope.services = resp.services;
                    scope.isLock = resp.isLock;
                    scope.user = resp.user;
                    scope.inventory = resp.inventory;
                    scope.inventory || (scope.inventory = {});
                    scope.surplus = resp.surplus;
                    scope.surplus || (scope.surplus = {});
                    scope.isUnlockNutrition = resp.is_unlock_nutrition;
                    scope.gam_exchanges = {};
                    if(resp.menu_plannings){
                        scope.menu_plannings = resp.menu_plannings;
                        scope.prices || (scope.prices = {});
                        scope.extrude_factor = {};
                        scope.menu_adjust.group_adjusts = {};
                        var menu_plannings = mapMenuPlanning(resp.menu_plannings);
                        scope.menu_adjust.market_before = Number(resp.market_before);
                        angular.forEach(menu_plannings, function(group_adjust,ind1) {
                            var warehouse_diffrent = false;
                            if (scope.menu_adjust.groups[group_adjust.group_id] == undefined) {
                                var msg = [' <b>Có vấn đề về dữ liệu:</b> ', 'Không tìm thấy nhóm trẻ theo thực đơn vừa mở'];
                                msg.push('Có thể nhóm trẻ đã bị xóa hoặc đang để ẩn');
                                msg.push('Hãy kiểm tra lại trong "Cấu hình" -> "Thông tin thực đơn"');
                                msg.push('Hoặc nếu nhóm trẻ này là không cần thiết thì có thể bỏ qua cảnh báo này');
                                $.dm_datagrid.show({title: '<span class="glyphicon glyphicon-warning-sign color-orange" style="margin: 0 3px;"></span> Cảnh báo', message: msg.join('<br/> - ')});
                                return;
                            }
                            group_adjust.group_key || (group_adjust.group_key = scope.menu_adjust.groups[group_adjust.group_id].id);
                            angular.forEach(scope.menu_informations,function(item,index) {
                                if(group_adjust.meals[item.define].warehouse_id != item.warehouse_id) {
                                    if(count(group_adjust.meals[item.define].dishes) > 0) {
                                        warehouse_diffrent = true;
                                    }
                                }
                                var isLevel = ['2', '3'].includes($CFG.level);
                                if (item.define === 'buasang') {
                                    if (group_adjust.meals[item.define].warehouse_id != item.warehouse_id && !isLevel) {
                                        $.dm_datagrid.show({title: 'Thông báo', message: 'Dữ liệu thực đơn "' + group_adjust.row.mathucdon + '" đang để bữa sáng sử dụng kho khác với cấu hình kho. <br> Để dữ liệu khớp cần lưu lại thực đơn.'});
                                    }
                                }
                                group_adjust.meals[item.define].warehouse_id = item.warehouse_id;
                            });
                            scope.row = group_adjust.row;
                            if (!scope.row.meal_selection) {
                                scope.row.meal_selection = {
                                    1: {selected: false, name: 'Ăn sáng', visible: true},
                                    2: {selected: true, name: 'Ăn chính', visible: true},
                                    3: {selected: false, name: 'Ăn tối', visible: false}
                                }
                            }
                            scope.row.tien_bo_tro || (scope.row.tien_bo_tro = 0);
                            if(scope.school_point.together && count(scope.school_point.points) > 1) {
                                if(count(scope.school_point.points) >= 1) {
                                    scope.row.sotres || (scope.row.sotres = {1: scope.row.sotre});
                                    scope.row.tien_bo_tros || (scope.row.tien_bo_tros = {1: scope.row.tien_bo_tro});
                                    var sotres = {};
                                    var tien_bo_tros = {};
                                    var sotre = 0;
                                    var tien_bo_tro = 0;
                                    angular.forEach(scope.school_point.points, function (val, ind) {
                                        if (scope.row.sotres[val]) {
                                            sotres[val] = scope.row.sotres[val];
                                        } else {
                                            sotres[val] = 0;
                                        }
                                        sotre += sotres[val];
                                        if (scope.row.tien_bo_tros[val]) {
                                            tien_bo_tros[val] = scope.row.tien_bo_tros[val];
                                        } else {
                                            tien_bo_tros[val] = 0;
                                        }
                                        tien_bo_tro += tien_bo_tros[val];
                                    });
                                    if (sotre == 0) {
                                        sotres[1] = scope.row.sotre;
                                    }
                                    if (tien_bo_tro == 0) {
                                        tien_bo_tros[1] = scope.row.tien_bo_tro;
                                    }
                                    scope.row.sotres = sotres;
                                    scope.row.tien_bo_tros = tien_bo_tros;
                                }
                            }else{
                                delete scope.row.sotres;
                                delete scope.row.tien_bo_tros;
                            }
                            scope.row.services = group_adjust.services;
                            scope.datagrid.data = {};
                            group_adjust.nutritions = {};
                            scope.addGroup_adjust(group_adjust);
                            if(ind1 == 0) {
                                scope.selected.group_adjust = group_adjust;
                            }
                            var data = {};
                            angular.forEach(group_adjust.data, function(foods, meal_key) {
                                data[meal_key] || (data[meal_key] = {});
                                var warehouse_id = 2;
                                if (meal_key == 1) {
                                    warehouse_id = group_adjust.meals['buasang'].warehouse_id;
                                } else if (meal_key == 3) {
                                    warehouse_id = group_adjust.meals['buatoi'].warehouse_id;
                                }
                                angular.forEach(foods, function(food, fd_id){
                                    food.supplier = '';
                                    food_id = food.food_id;
                                    scope.gam_exchanges[fd_id] || (scope.gam_exchanges[fd_id] = food.gam_exchange);
                                    if(scope.inventory[warehouse_id]) {
                                        if(scope.inventory[warehouse_id][food.food_id]) {
                                            food.gam_exchange = scope.inventory[warehouse_id][food.food_id].gam_exchange;
                                            if(scope.inventory[warehouse_id][food.food_id].foods[food.food_id + '_' + food.price]) {
                                                food.measure_id = scope.inventory[warehouse_id][food.food_id].foods[food.food_id + '_' + food.price].measure_id;
                                            }else {
                                                food.gam_exchange = scope.inventory[warehouse_id][food.food_id].gam_exchange;
                                                food.measure_id = scope.inventory[warehouse_id][food.food_id].measure_id;
                                            }
                                            food.price = scope.inventory[warehouse_id][food.food_id].price;
                                            food.is_market = 0;
                                        }
                                    }else {
                                        food.gam_exchange = scope.gam_exchanges[fd_id];
                                        food.is_market = 1;
                                    }
                                    group_adjust.nutritions[food_id] || (group_adjust.nutritions[food_id] = food.nutritions);
                                    food.nutritions = group_adjust.nutritions[food_id];
                                    food_id || (food_id = food.food_id);
                                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                                    food.foods = [];
                                    if(!data[meal_key][food_id]) {
                                        data[meal_key][food_id] = food;
                                    }else{
                                        data[meal_key][food_id].luong1tre += food.luong1tre;
                                    }
                                })
                            });
                            angular.forEach(group_adjust.meals, function(meal, meal_define) {
                                meal.name = scope.getMenu_infomation(meal_define).name;
                                var warehouse_id = meal.warehouse_id;
                                var meal_key = scope.meal_defines[meal_define];
                                var wh = data[meal_key];
                                if(wh){
                                    angular.forEach(meal.dishes, function(dish, dish_id){
                                        if(!dish) {
                                            dish = meal.dishes[dish_id] = {};
                                        }
                                        var key = meal_define+'_'+dish_id;
                                        angular.forEach(dish.ingredient, function(food,fd_id){
                                            food.supplier = '';
                                            food_id = food.id;
                                            scope.gam_exchanges[fd_id] || (scope.gam_exchanges[fd_id] = food.gam_exchange);
                                            food.gam_exchange = scope.gam_exchanges[fd_id];
                                            group_adjust.nutritions[food_id] || (group_adjust.nutritions[food_id] = food.nutritions);
                                            food.nutritions = group_adjust.nutritions[food_id];
                                            if(wh[food_id]){
                                                if(!wh[food_id].gam_exchange) {
                                                    wh[food_id].gam_exchange = scope.gam_exchanges[fd_id];
                                                }else{
                                                    scope.gam_exchanges[fd_id] = wh[food_id].gam_exchange;
                                                }
                                                if(!wh[food_id].name_edit){
                                                    wh[food_id].name_edit = food.name;
                                                }
                                                if(wh[food_id].price == 0){
                                                    wh[food_id].price = food.price;
                                                }else{
                                                    food.price = wh[food_id].price;
                                                }
                                                wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price,1000),wh[food_id].gam_exchange));
                                                wh[food_id].is_meat = food.is_meat;
                                                wh[food_id].is_veget = food.is_veget;
                                                wh[food_id].is_dry = food.is_dry;
                                                wh[food_id].nutritions = group_adjust.nutritions[food_id];
                                                wh[food_id].extrude_factor_root = wh[food_id].extrude_factor;
                                                wh[food_id].gam_exchange_root = wh[food_id].gam_exchange;
                                                food.price_kg = wh[food_id].price_kg;
                                                food.extrude_factor = wh[food_id].extrude_factor;
                                                food.gam_exchange = scope.gam_exchanges[fd_id];
                                                food.meal_define = meal_define;
                                                food.dish_id = dish_id;
                                                wh[food_id].foods.push(food);
                                                scope.onChange_price(null,food);
                                            }
                                        });
                                    });
                                }
                            });
                            group_adjust.data = data;
                            angular.forEach(group_adjust.data, function(foods, meal_key) {
                                angular.forEach(foods, function(food, food_id) {
                                    food.supplier = '';
                                    if (!food.thucmuatheodvts) {
                                        scope.divide_pointThucmuatheodvt(food, group_adjust);
                                    }
                                    scope.onChange_thucmuatheodvt(food, true, true);
                                });
                            });
                        });
                        if(scope.selected.group_adjust) {
                            scope.menu_planningSelected(scope.selected.group_adjust);
                            angular.forEach(scope.selected.group_adjust.meals, function(meal, define) {
                                if(count(meal.dishes)>0) {
                                    if (define == 'buasang') {
                                        scope.dinhduong_calc[1] = true;
                                    }
                                }
                            });
                            scope.totalCalculator();
                        }
                        // scope.menu_adjust.form_type = 1;
                    }else{
                        if (scope.school_point.together==0 && $CFG.admin==1 && count(scope.school_point.points)>1) {
                        }else{
                            if(resp.other_points){
                                if(count(resp.other_points)>0){
                                    scope.fn_copyForm(resp.other_points);
                                }
                            }
                        }
                        scope.menu_adjust.form_type = 0;
                        scope.menu_adjust.market_before = 1;
                    }
                    /*  sắp xếp lại thứ tự nhóm trẻ */
                };
                scope.orderMenu_adjusts = function(){
                    scope.tmp_menu_adjusts = scope.menu_adjust.group_adjusts;
                    if(count(scope.menu_adjust.group_adjusts)>1){
                        var adjusts = [];
                        angular.forEach(scope.menu_adjust.group_adjusts, function(adjust, ind){
                            adjusts.push(adjust);
                        });
                        /*  sắp xếp */
                        for(var i=0; i < adjusts.length-1;i++){
                            for(var j=i+1; j<adjusts.length;j++){
                                grp1 = scope.menu_adjust.groups[adjusts[i].menu_plannings[0].group_id];
                                grp2 = scope.menu_adjust.groups[adjusts[j].menu_plannings[0].group_id];
                                if (!grp1 || !grp2) {
                                    $.messager.alert('Thông báo', 'Có vấn đề về nhóm ăn. Hãy kiểm tra lại xem có đang để ẩn không?');
                                }
                                if(grp1.group_id > grp2.group_id) {
                                    var tmp = adjusts[i];
                                    adjusts[i] = adjusts[j];
                                    adjusts[j] = tmp;
                                }
                            }
                        }
                        scope.menu_adjust.group_adjusts = adjusts;
                    }
                };
                scope.inventoriesDivisionOfFood = function(food, inventory){
                    if(inventory) {
                        var quantity_need = food.thucmuatheodvt;
                        var ept = {};
                        var prices = [];
                        angular.forEach(inventory.foods, function(invt, food_id_price) {
                            food_id_price = food.food_id + '_' + invt.price;
                            if (quantity_need <= 0 || invt.inventory_have == 0) {
                                return;
                            }
                            ept[food_id_price] = ept[food_id_price] || {
                                quantity: 0,
                                price: invt.price
                            };
                            if(quantity_need <= invt.inventory_have) {
                                ept[food_id_price].quantity = $['+'](ept[food_id_price].quantity, quantity_need);
                                invt.inventory_have = $['-'](invt.inventory_have, quantity_need);
                                quantity_need = 0;
                            }else{
                                ept[food_id_price].quantity = $['+'](ept[food_id_price].quantity, invt.inventory_have);
                                quantity_need = $['-'](quantity_need, invt.inventory_have);
                                invt.inventory_have = 0;
                            }
                            prices.push(invt.price);
                        });
                        if(quantity_need > 0) {
                            if(count(ept)==0 && inventory.is_storage != 1){

                            }else{
                                ept[food.food_id + '_editable'] = {
                                    quantity: quantity_need,
                                    price: food.price,
                                    editable: true
                                };
                                prices.push(food.price);
                            }
                        }
                        if(count(ept)>0) {
                            food.exports = ept;
                            if (count(ept) == 1) {
                                food.price = ept[Object.keys(ept)[0]].price;
                            }
                        }
                        inventory.inventory_export = $['+'](inventory.inventory_export, food.thucmuatheodvt);
                    }
                };
                scope.getInventoryOfFood = function(meal_key, food_id) {
                    var rs = 0;
                    var warehouse_id = scope.getMenu_infomationByMeal_key(meal_key).warehouse_id;
                    if (scope.inventory[warehouse_id]) {
                        if (scope.inventory[warehouse_id][food_id]) {
                            rs = scope.inventory[warehouse_id][food_id].inventory;
                            rs = $['-'](rs,  scope.inventory[warehouse_id][food_id].inventory_export);
                        }
                    }
                    return rs;
                };
                scope.inventoriesDivision = function(){
                    scope.orderMenu_adjusts();
                    /* Khôi phục mảng tồn kho về ban đầu */
                    angular.forEach(scope.inventory, function(inventories, warehouse_id){
                        angular.forEach(inventories, function(inventory, food_id){
                            inventory.inventory = 0;
                            angular.forEach(inventory.foods, function(food, food_id_price){
                                inventory.inventory = $['+'](inventory.inventory,food.inventory);
                                food.inventory_have = food.inventory;
                            });
                            inventory.inventory_export = 0;
                        });
                    });
                    /* Sắp xếp lại thứ tự theo mã nhóm trẻ (mã nhóm ăn tạo không theo thứ tự)*/
                    var menu_adjusts = [];
                    angular.forEach(scope.menu_adjust.group_adjusts, function(group_adjust, ind){
                        menu_adjusts.push(group_adjust);
                    });
                    for(var i=0; i<menu_adjusts.length-1; i++) {
                        for(var j=i+1; j<menu_adjusts.length; j++) {
                            if(menu_adjusts[i].group_key > menu_adjusts[j].group_key) {
                                var tmp = menu_adjusts[i];
                                menu_adjusts[i] = menu_adjusts[j];
                                menu_adjusts[j] = tmp;
                            }
                        }
                    }
                    angular.forEach(menu_adjusts, function(group_adjust, ind){
                        if(group_adjust.menu_plannings[0]){
                            angular.forEach(group_adjust.menu_plannings[0].data, function(foods, meal_key) {
                                var warehouse_id = scope.getMenu_infomationByMeal_key( meal_key ).warehouse_id;
                                var inventories = scope.inventory[warehouse_id];
                                angular.forEach(foods, function(food, food_id){
                                    delete food.exports;
                                    if(inventories != undefined){
                                        if(inventories[food.food_id] != undefined) {
                                            scope.inventoriesDivisionOfFood(food, inventories[food.food_id]);
                                            if (count(food.exports)>0) {
                                                angular.forEach(food.exports, function(exp, food_id_price){
                                                    exp.quantities = scope.divide(exp.quantity, food.thucmuatheodvts, true);
                                                });
                                            }
                                        }
                                    }
                                })
                            })
                        }
                    });
                    if(scope.tmp_menu_adjusts){
                        scope.menu_adjust.group_adjusts = scope.tmp_menu_adjusts;
                    }
                };
                scope.getMenu_info = function(define){
                    var rs = {};
                    angular.forEach(scope.menu_informations, function(menu_info, ind){
                        if(menu_info.define == define){
                            rs = menu_info;
                            return;
                        }
                    });
                    return rs;
                };

                /*Thêm ngày vào tháng năm đã chọn trên danh sách lọc*/
                scope.sumCaloMeals = function(meals) {
                    var rs = 0;
                    if(scope.selected.group_adjust){
                        meals || (meals = scope.selected.group_adjust.meals);
                        angular.forEach(meals, function(meal, meal_define) {
                            if ( scope.selected.group_adjust.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                                rs += scope.sumCaloMeal(meal);
                            }
                        });
                    }
                    return rs;
                };
                scope.sumCaloMeal = function(meal){
                    var rs = 0;
                    if(meal){
                        if(meal.dishes){
                            angular.forEach(meal.dishes, function(dish,dish_id){
                                if(dish+'' != 'null') {
                                    rs += scope.sumCaloDish(dish.ingredient);
                                }
                            });
                        }
                    }
                    return rs;
                };
                scope.ratioKCalo = function (mealId) {
                    if (scope.tong.calo > 0) {
                        return round(scope.sumCaloMeal(scope.selected.group_adjust.meals[mealId]) / scope.tong.calo * 100, 2);
                    }
                    return '';
                };
                scope.getGroupSelected = function(){
                    if(!scope.selected.group){
                        for(var i in scope.menu_adjust.groups){
                            scope.selected.group = scope.menu_adjust.groups[i];
                            break;
                        }
                    }
                    return scope.selected.group;
                };

                scope.caloRate = function(){
                    var value = 0;
                    var calo = round(scope.sumCaloMeals(), 0);
                    var rate = scope.getNormSelected();
                    if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                        value = 1;
                    } else {
                        if (calo < rate.smallest_rate) {
                            value = 0;
                        } else {
                            value = 2;
                        }
                    }
                    return value;
                };
                scope.isHasMorning = function () {
                    var rs = false;
                    if(scope.datagrid.data) {
                        if (count(scope.datagrid.data[1]) > 0) {
                            if (scope.dinhduong_calc[1]) {
                                rs = true;
                            }
                        }
                    }
                    return rs;
                };
                scope.isHasAfternoon = function () {
                    var rs = false;
                    if(scope.datagrid.data) {
                        if (count(scope.datagrid.data[2]) > 0) {
                            if (scope.dinhduong_calc[2]) {
                                rs = true;
                            }
                        }
                    }
                    return rs;
                };
                scope.getNormSelected = function() {
                    var rs = {biggest_rate: 0, smallest_rate: 0};
                    if (scope.row.meal_selection) {
                        if (scope.row.meal_selection[1].selected) {
                            if (scope.selected.group) {
                                rs.smallest_rate += scope.selected.group.nutritions.calo_rate_morning_low;
                                rs.biggest_rate += scope.selected.group.nutritions.calo_rate_morning;
                            }
                        }
                        if (scope.row.meal_selection[2].selected) {
                            if (scope.selected.group) {
                                rs.smallest_rate += scope.selected.group.nutritions.calo_rate_low;
                                rs.biggest_rate += scope.selected.group.nutritions.calo_rate;
                            }
                        }
                    }
                    return rs;
                };
                /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */
                scope.menu_adjust.plgRate = function(meals) {
                    var tile = scope.getTile_PLG(meals);
                    var tile_dat = [];
                    var tile_chuadat = [];
                    var tile_vuotqua = [];
                    var group = scope.selected.group;
                    if(group && tile){
                        if(tile.protein>=group.protein_min && tile.protein<=group.protein_max){
                            tile_dat.push({define:'protein',name: 'Chất đạm'});
                        }else if(tile.protein<scope.selected.group.protein_min) {
                            tile_chuadat.push({define:'protein',name: 'Chất đạm'})
                        }else{
                            tile_vuotqua.push({define:'protein',name: 'Chất đạm'})
                        }
                        if(tile.fat>=group.fat_min && tile.fat<=group.fat_max){
                            tile_dat.push({define:'fat',name: 'Chất béo'});
                        }else if(tile.fat<group.fat_min) {
                            tile_chuadat.push({define:'fat',name: 'Chất béo'})
                        }else{
                            tile_vuotqua.push({define:'fat',name: 'Chất béo'})
                        }
                        if(tile.sugar>=group.sugar_min && tile.sugar<=group.sugar_max){
                            tile_dat.push({define:'sugar',name: 'Chất bột'});
                        }else if(tile.sugar<group.sugar_min) {
                            tile_chuadat.push({define:'sugar',name: 'Chất bột'})
                        }else{
                            tile_vuotqua.push({define:'sugar',name: 'Chất bột'})
                        }
                    }
                    return {
                        dat: tile_dat,
                        chuadat: tile_chuadat,
                        vuotqua: tile_vuotqua
                    }
                };
                scope.menu_adjust.plgRateBind = function(){
                    var thanhphan = scope.menu_adjust.plgRate();
                    var text = '';
                    if(thanhphan.dat.length == 3){
                        text = 'Cân đối';
                        class_color = '';
                    }else{
                        text = 'Chưa cân đối';
                        class_color = 'color-red';
                    }
                    return {text: text, 'class': class_color, thanhphan: thanhphan};
                };
                scope.caloRateBind = function(){
                    var value = scope.caloRate();
                    if(value == 1){
                        text = 'Đạt';
                        class_color = '';
                    }else if(value == 0){
                        text = 'Chưa đạt';
                        class_color = 'color-red';
                    }else{
                        text = 'Vượt quá định mức';
                        class_color = 'color-green';
                    }
                    return {text: text, 'class': class_color, value: value};
                };
                scope.sumQuantityDish = function(foods,key){
                    var rs = 0;
                    key || (key = 'quantity_edit');
                    if(foods != undefined) {
                        foods = Object.assign(angular.copy(foods), scope.foodAddeds);
                        angular.forEach(foods, function (food, food_id) {
                            food[key] || (food[key] = food.quantity);
                            rs = $['+'](rs, food[key]);
                        });
                    }
                    return rs;
                };
                scope.sumCaloFood = function(food,key) {
                    var rs = 0;
                    key || (key = 'quantity_edit');
                    if (food) {
                        food[key] || (food[key] = 0);
                        if (food.nutritions) {
                            food.nutritions.protein || (food.nutritions.protein = 0);
                            food.nutritions.fat || (food.nutritions.fat = 0);
                            food.nutritions.sugar || (food.nutritions.sugar = 0);
                            rs += $['*'](scope.co_cau(null, scope.menu_adjust.date).protein, food[key]) * (food.nutritions.protein / 100)
                                + $['*'](scope.co_cau(null, scope.menu_adjust.date).fat, food[key]) * (food.nutritions.fat / 100)
                                + $['*'](scope.co_cau(null, scope.menu_adjust.date).sugar, food[key]) * (food.nutritions.sugar / 100);
                        }
                    }
                    return rs;
                };
                scope.sumCaloFoods = function(foods, key, notAll) {
                    var rs = 0;
                    angular.forEach(foods, function(food,food_id){
                        if(!notAll || (notAll && !food.deleted)) {
                            rs = $['+'](rs, scope.sumCaloFood(food, key));
                        }
                    });
                    return rs;
                };
                scope.sumCaloDish = function(foods, key, notAll){
                    var rs = 0;
                    rs = scope.sumCaloFoods(foods, key, notAll);
                    return rs;
                };
                /*Trả ra danh sách nhóm trẻ không có các nhóm đã thêm rồi*/
                scope.getGroups = function(){
                    var groups = {};
                    var ignore_ids = [];
                    angular.forEach(scope.menu_adjust.group_adjusts,function(item,index){
                        ignore_ids.push(index);
                    });
                    angular.forEach(scope.menu_adjust.groups, function(group,id){
                        if(!in_array(id,ignore_ids)) {
                            groups[id] = group;
                        }
                    });
                    return groups;
                };
                scope.getMenu_infomation = function (menu_define) {
                    var rs = {};
                    for (var i in scope.menu_informations) {
                        if (menu_define == scope.menu_informations[i].define) {
                            rs = scope.menu_informations[i];
                            break;
                        }
                    }
                    return rs;
                };
                scope.getMenu_infomationByMeal_key = function(meal_key) {
                    var rs = {};
                    var meal_define = 'buasang';
                    for (var i in scope.meal_defines) {
                        if (scope.meal_defines[i] == meal_key) {
                            meal_define = i;
                            break;
                        }
                    }
                    for (var i in scope.menu_informations) {
                        if (meal_define == scope.menu_informations[i].define) {
                            rs = scope.menu_informations[i];
                            break;
                        }
                    }
                    return rs;
                };
                scope.getColorForTableCalo = function(meal){
                    if(!scope.selected.group_adjust) return;
                    meal || (meal = {});
                    var group = scope.getGroup(scope.selected.group_adjust.group_id);
                    var value = scope.sumCaloMeal(meal)/group.nutritions.calo*100;
                    var m = scope.getMenu_infomation(meal.define);
                    var cl = "";
                    var title = "";
                    if(m.norm_min<=value && m.norm_max>=value){
                        title = "Đạt";
                    }else if(m.norm_min>value) {
                        cl = 'color-red';
                        title = "Chưa đạt";
                    }else{
                        title = "Vượt quá định mức";
                        cl = 'color-green';
                    }
                    return {'class':cl, 'title':title};
                };
                /*Chọn lại nhóm trẻ ở form thêm nhóm trẻ sẽ tải lại danh sách thực đơn mẫu*/
                scope.menu_adjust.group_addChange = function(group) {
                    // console.log(group)
                    group || (group = scope.selected.group_add);
                    if(!group) return;
                    var menu_planning_ids = [];
                    angular.forEach(scope.menu_adjust.group_adjusts, function(group_adjust,index){
                        angular.forEach(group_adjust.menu_plannings, function(menu_planning, id){
                            menu_planning_ids.push(menu_planning.id);
                        })
                    });
                    //document.getElementById('_easyui_textbox_input2').value  = null;
                    if(group.menu_planning) return;
                    var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/menu_adjust/menu_planning_of_group';
                    var data = {group_id: group.id, menu_planning_ids: menu_planning_ids, async: true};
                    if (scope.school_point.together==0 && $CFG.admin==1 && count(scope.school_point.points)>1){
                        data.point = scope.selected.group_add_point;
                    }
                    process(url, data,function(resp) {
                        scope.$apply(function(){
                            group.menu_plannings = resp;
                        })
                    },null,false);
                    
                };
                scope.getGroup = function(id) {
                    var rs = {};
                    if(id) {
                        angular.forEach(scope.menu_adjust.groups, function(group,index){
                            if(group.id == id) {
                                rs = group;
                                return;
                            }
                        })
                    }else{
                        if(scope.selected.group_adjust){
                            rs = scope.selected.group_adjust.group;
                        }
                    }
                    return rs;
                };
                /*Chọn nhóm tuổi để thao tác với thực đơn đã chọn*/
                scope.menu_planningSelected = function(menu_planning, calculate){
                    menu_planning || (menu_planning = scope.selected.group_adjust);
                    if(!menu_planning){
                        return;
                    }
                    scope.selected.group_adjust || (scope.selected.group_adjust = {});
                    scope.datagrid.data = {};
                    scope.prices || (scope.prices = {});
                    scope.selected.group_adjust = menu_planning;
                    scope.datagrid.data = menu_planning.data;
                    scope.row = menu_planning.row;
                    scope.row.group_id = menu_planning.group_id;
                    if (!scope.row.meal_selection) {
                        scope.row.meal_selection = {
                            1: {selected: false, name: 'Ăn sáng', visible: true},
                            2: {selected: true, name: 'Ăn chính', visible: true},
                            3: {selected: false, name: 'Ăn tối', visible: false}
                        }
                    }
                    /*Chọn lại nhóm trẻ để tính thành phàn dinh dưỡng*/
                    scope.selected.group = scope.getGroup(menu_planning.group_id);
                    if(calculate){
                        scope.totalCalculator();
                    }
                    scope.selected.meals = menu_planning.meals;
                    var cleanall = true;
                    angular.forEach(scope.selected.meals, function (meal, ind) {
                        if (count(meal.dishes) > 0 ) {
                            cleanall = false;
                        }
                    });
                    if (cleanall) {
                        scope.datagrid.data = {};
                    }
                };
                scope.chuyen_thuc_pham = function(dish_end){
                    var fds = {};
                    angular.forEach(scope.selected.group_adjust.meals, function(meal,ind1){
                        angular.forEach(meal.dishes, function(dish,ind2){
                            if(dish_end.id != dish.id){
                                dish_end.ingredient || (dish_end.ingredient = {});
                                if(count(dish_end.ingredient)==0){
                                    dish_end.ingredient = {}
                                }
                                angular.forEach(dish.ingredient,function(food,food_id){
                                    if(food.selected){
                                        if(!dish_end.ingredient[food_id]){
                                            dish_end.ingredient[food_id] = food;
                                        }else{
                                            dish_end.ingredient[food_id].quantity_edit = $['+'](dish_end.ingredient[food_id].quantity_edit,food.quantity_edit);
                                        }
                                        delete dish.ingredient[food_id];
                                        food.selected = false;
                                    }
                                })
                            }
                        })
                    })
                };
                scope.formMove_foods = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/menu_adjust',
                            action:'add',
                            title:'Tách thực phẩm',
                            size: size.wide,
                            fullScreen: true,
                            showButton: false,
                            content: function(element,dialogRef){
                                angular.forEach(scope.selected.group_adjust.meals, function(meal,ind1){
                                    angular.forEach(meal.dishes, function(dish,ind2){
                                        angular.forEach(dish.ingredient,function(food,food_id){
                                            food.quantity_move = food.quantity_edit;
                                            food.selected = false;
                                        })
                                    })
                                });
                                setTimeout(function(){
                                    scope.$apply(function(){
                                        $(element).html(scope.getTemplate(scope.controller.templates.separateFood));
                                    })
                                })
                            }
                        }
                    );
                };
                scope.marketbillForm = function(date) {
                    if(!date) {
                        date = scope.selected.date;
                    }
                    scope.selected.date = date;
                    if(!date) {
                        date = dateboxOnSelect(new Date());
                        scope.selected.date = date;
                    }
                    $.dm_datagrid.showAddForm(
                        {
                            title:'SỬA DỮ LIỆU PHIẾU KÊ CHỢ (Lưu ý: Chức năng này hỗ trợ sửa lại phiếu kê chợ, ko có tác dụng sửa hay cập nhật Cân đối khẩu phần)',
                            size: size.wide,
                            fullScreen: true,
                            showButton: false,
                            scope: scope,
                            content: scope.controller.templates.addForm,
                            onShown: function(){
                                scope.loadDataOfDay(1, scope.selected.point);
                            }, cancel: function () {
                                scope.isMarektbillEdit = false;
                            }
                        }
                    );
                };
                scope.loadDataOfDay = function (marketbill, point) {
                    process('dinhduong/menu_adjust/adjustofday',{
                        day: scope.selected.date, async:true,
                        marketbill: marketbill, point: point,
                        point_together: scope.selected.point
                    }, function(resp){
                        if (marketbill) {
                            scope.isMarektbillEdit = true;
                        } else {
                            scope.isMarektbillEdit = false;
                        }
                        scope.menu_adjust.form_type = resp.menu_plannings ?  1 : 0;
                        if (scope.school_point.together==0 && $CFG.admin==1 && count(scope.school_point.points)>1){}else{
                            if(!resp.menu_plannings && resp.other_points && count(resp.other_points)>0) {
                                scope.fn_copyForm(resp.other_points);
                            }
                        }
                        if(scope.menu_plannings && !resp.menu_plannings && resp.price_by_day) {
                            angular.forEach(scope.menu_plannings, function(group_adjust, key) {
                                angular.forEach(group_adjust.data, function(kho, key1) {
                                    angular.forEach(kho, function(food, key2) {
                                        if(resp.price_by_day[food.food_id]) {
                                            food.price = resp.price_by_day[food.food_id].price;
                                            food.is_priceByDay = true
                                        }
                                        kho[key2] = food;
                                    });
                                    group_adjust.data[key1] = kho;
                                });
                                scope.menu_plannings[key] = group_adjust;
                            });
                            resp.menu_plannings = scope.menu_plannings;
                        }
                        scope.buildDataForForm(resp);
                        scope.$apply();
                    });
                };
                scope.getChenhlech1treSchoolpoint = ()=>{
                    var rs = {};
                    scope.row || (scope.row={});
                    if (scope.row.thanhtien1nhoms && scope.row.sotres) {
                        for (var point in scope.row.thanhtien1nhoms) {
                            rs[point] = 0;
                            if (scope.row.sotres[point] > 0) {
                                rs[point] = round(scope.row.tien1tre - scope.getTiendichvu() - scope.row.thanhtien1nhoms[point] / scope.row.sotres[point]);
                            }
                        }
                    }
                    return rs;
                };
                scope.getChenhlechNhomSchoolpoint = ()=>{
                    var rs = {};
                    scope.row || (scope.row={});
                    if (scope.row.thanhtien1nhoms && scope.row.sotres) {
                        for (var point in scope.row.thanhtien1nhoms) {
                            rs[point] = 0;
                            if (scope.row.sotres[point] > 0) {
                                rs[point] = round(scope.row.sotres[point]*(scope.row.tien1tre - scope.getTiendichvu()) - scope.row.thanhtien1nhoms[point]);
                            }
                        }
                    }
                    return rs;
                };
                scope.delMarketbill = function(date) {
                    if(!date) {
                        date = scope.selected.date;
                    }
                    if(!date) {
                        date = dateboxOnSelect(new Date());
                    }
                    scope.selected.date = date;
                };
                scope.handleSidebar = function() {
                    scope.collefthidden  = !scope.collefthidden;
                }
                scope.addjustForm = function(date, only_tpc) {
                    if(!date) {
                        date = scope.selected.date;
                    }
                    if(!date) {
                        date = dateboxOnSelect(new Date());
                    }
                    scope.selected.date = date;
                    scope.collefthidden = false;
					var adjustTemplate = scope.controller.templates.addForm;
					var adjustTitle = 'Điều chỉnh thực đơn';
					var popupSize = size.wide;
					var fullScreen = true;
					if(only_tpc==1) {
						adjustTemplate = scope.controller.templates.editFormTPC;
						adjustTitle = 'Điều chỉnh thực đơn: Tích/bỏ tích TPC trong các món ăn';
						popupSize = 620;
						fullScreen = false;
					}
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/menu_adjust',
                            action:'add',
                            title:adjustTitle,
                            size: popupSize,
                            fullScreen: fullScreen,
                            showButton: false,
                            scope: scope,
                            content: adjustTemplate,
                            onShown: function(){
                                process('dinhduong/menu_adjust/adjustofday',{day: scope.selected.date, async:true, point_together: scope.selected.point}, function(resp){
                                    scope.$apply(function(){
                                        if(resp.menu_plannings) {
                                            scope.menu_adjust.form_type = 1;
                                        }

                                        if (scope.enableMauKhongAnSang) {
                                            for (const menu of resp.menu_plannings) {
                                                for (const key1 in menu.row.meal_selection) {
                                                    if (menu.row.meal_selection[key1].name.trim() == 'Ăn sáng') {
                                                        menu.row.meal_selection[key1].selected = false;
                                                    }
                                                }
                                            }
                                        }

                                        scope.buildDataForForm(resp);
                                    });
                                }, function(){
                                    scope.$apply(function(){
                                        scope.menu_adjust.cleanAllData();
                                    });
                                    dialogClose();
                                });
                            }, reload: function () {
                                process('dinhduong/menu_adjust/adjustofday',{day: date, async:true}, function(resp){
                                    scope.$apply(function(){
                                        if(resp.menu_plannings) {
                                            scope.menu_adjust.form_type = 1;
                                        }
                                        scope.buildDataForForm(resp);
                                    });
                                }, function(){
                                    scope.$apply(function(){
                                        scope.menu_adjust.cleanAllData();
                                    });
                                    dialogClose();
                                });
                            }
                        }
                    );
                };
                scope.onChange_warehouse = function(warehouse_id){
                    var kt = false;
                    angular.forEach(scope.dinhduong_calc, function(value,wid){
                        if(value){
                            kt = true;
                        }
                    });
                    if(!kt){
                        scope.dinhduong_calc[warehouse_id] = true;
                    }else{
                        scope.totalCalculator();
                        var urls_export = [$CFG.remote.base_url,'report',$CFG.project,'menu_adjust','exportPrint'];
                        var btn = $('<a id="btn_preview" title="Xem và in" title="In trực tiếp"><span class="glyphicon glyphicon-print fa-2x"></span></a>');
                        $('#btn-content-form-print-now').html('').append(btn);
                        btn.printPage({
                            url: urls_export.join('/')+'?date='+scope.menu_adjust.date+'&group_id='+scope.selected.group_adjust.group_id+'&apdungcongthuc='+(scope.apdungcongthuc?1:0)+'&warehouse_ids='+scope.menu_adjust.getWarehouse_ids().join(','),
                            attr: "href",
                            message:"Phiếu xuất kho đang được tạo ..."
                        });
                    }
                };
                scope.menu_adjust.save = function(type){
					if(type!='only_tpc') {
						type = '';
                        var sumNotEqual = document.getElementsByName('sum_not_equal_school_point');
                        if(sumNotEqual.length>0) {
                            $.messager.confirm('Thông báo', '<span style="font-size:13px;">Vui lòng kiểm tra lại các dòng thực phẩm có cột Tổng <span style="color:orange; font-weight:bold;">màu cam</span>, do cộng các điểm không bằng Tổng <span class="glyphicon glyphicon-info-sign color-orange ng-scope" style="font-size:11px;"></span></span>', function (r) {
                                //To do
                            });
                            return false;
                        }
					}
                    var data = {
                        data: scope.getDataForm(),
                        //form: arrayToJson(getSubmitForm('frm-add-menu_adjust',true)),
                        isMarektbillEdit: scope.isMarektbillEdit,
                        market_before: scope.menu_adjust.market_before,
                        date: $('#date').val(),
						type: type,
                        async: true
                    };
                    if (scope.school_point.together==0 && $CFG.admin==1 && count(scope.school_point.points)>1){
                        data.point = scope.selected.point;
                    }
                    var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/menu_adjust/update';
                    process(url, data, function(resp){
                        if(resp.result == "success"){
                            if (scope.school_point.together==0 && scope.$CFG.admin==1 && count(scope.school_point.points)>1) {
                                $.messager.confirm('Thông báo', 'Có muốn tiếp tục chỉnh sửa?', function (r) {
                                    if (r) {
                                    }else{
                                        dialogCloseAll();
                                    }
                                });
                            } else {
                                dialogCloseAll();
                            }
                            $('#tbl_menu_adjust').datagrid('reload');
                        }
                    });
                };
                scope.getDataForm = function(){
                    var datas = [];
                    angular.forEach(scope.menu_adjust.group_adjusts,function(group_adjust, group_id){
                        angular.forEach(group_adjust.menu_plannings, function(menu_planning, index){
                            var data = clone(menu_planning.data);
                            angular.forEach(data, function (foods) {
                                angular.forEach(foods, function (food) {
                                    food.foods = [];
                                });
                            });
                            datas.push({
                                id: menu_planning.id,
                                group_id: menu_planning.group_id,
                                group_key: menu_planning.group_key,
                                data: data,
                                row: menu_planning.row,
                                menu_informations: menu_planning.menu_informations,
                                meals: menu_planning.meals,
                                services: menu_planning.row.services
                            });
                        });
                    });
                    return JSON.stringify(datas);
                };
                /*Xóa nhóm tuổi đã chọn*/
                scope.delMenu_planning = function(item, index, mpl) {
                    var rs = [];
                    angular.forEach(item.menu_plannings, function(menu_planning, i){
                        if (menu_planning.group_key!=mpl.group_key) {
                            rs.push(menu_planning);
                        }
                    });
                    if(rs.length == 0){
                        delete scope.menu_adjust.group_adjusts[mpl.group_key];
                    }else{
                        item.menu_plannings = rs;
                    }
                    if(count(scope.menu_adjust.group_adjusts) == 0){
                        scope.menu_adjust.cleanAllData();
                    }else{
                        if(scope.selected.group_adjust.group_id == mpl.group_id && scope.selected.group_adjust.id == mpl.id) {
                            /*Nếu xóa nhóm trẻ đang được chọn*/
                            /*Chọn lại nhóm trên đầu để hiển thị*/
                            for(var group_id in scope.menu_adjust.group_adjusts){
                                var group_adjust = scope.menu_adjust.group_adjusts[group_id];
                                for(var i in group_adjust.menu_plannings){
                                    var menu_planning = group_adjust.menu_plannings[i];
                                    scope.menu_planningSelected(menu_planning);
                                    break;
                                }
                            }
                        }
                    }
                    scope.totalCalculator();
                };
                scope.sumNotEqual = function(food){
                    var inPoint = 0;
                    if(food.thucmuatheodvts && $CFG.school_points>1 && $CFG.school_point_together){
                        angular.forEach(food.thucmuatheodvts, function (value) {
                            inPoint = $['+'](inPoint, value);
                        });
                    }else {
                        return false;
                    }
                    return (food.thucmuatheodvt != inPoint);
                };
                scope.menu_adjust.getNameDishesOfMeals = function(meals){
                    var rs = [];
                    angular.forEach(meals, function(meal,meal_define){
                        var dish_name = [];
                        angular.forEach(meal.dishes, function(dish,dish_id){
                            dish || (meal.dishes[dish_id] = {})
                            dish_name.push(dish.name);
                        })
                        rs.push(meal.name+' ('+dish_name.join(', ')+')');
                    });
                    return rs;
                };
                scope.menu_adjust.getNameFoodsOfDish = function(dish){
                    var rs = [];
                    angular.forEach(dish.ingredient, function(food,food_id){
                        if (food) {
                            rs.push(food.name);
                        }
                    });
                    return rs;
                };
                scope.menu_adjust.cleanAllData = function(){
                    scope.menu_adjust.row = {};
                    scope.menu_adjust.group_adjusts = {};
                    scope.selected.group_adjust = {};
                    angular.forEach(scope.datagrid.data, function(item, kho){
                        scope.datagrid.data[kho] = {};
                    });
                    scope.totalCalculator();
                };

                /* Meal detail */
                scope.detailByMeal.quantityChange = function (meal, dish, food, quantityOld) {
                    var warehouseId = meal['warehouse_id'];
                    var meal_define = meal['define'];
                    var dish_id = dish.id;
                    var quantityChange = food.quantity_edit - quantityOld;
                    scope.datagrid.data[warehouseId][food.food_id].luong1tre =
                        $['+'](scope.datagrid.data[warehouseId][food.food_id].luong1tre, quantityChange);
                    var luong1treTotal = scope.datagrid.data[warehouseId][food.food_id].luong1tre; //chú ý quantity_edit là luong1tre
                    var luong1treDishNow = food.quantity_edit;
                    var quantity = food.quantity;
                    var totalQuantity = 0;
                    angular.forEach(scope.datagrid.data[warehouseId][food.food_id].foods, function (food) {
                        food.quantity = Number(food.quantity);
                        totalQuantity = $['+'](totalQuantity, food.quantity);
                    });
                    var totalQuantityOther = $['-'](totalQuantity, quantity);
                    food.quantity = round($['/']($['*'](luong1treDishNow, totalQuantityOther), $['-'](luong1treTotal, luong1treDishNow)), 4);
                    angular.forEach(scope.datagrid.data[warehouseId][food.food_id].foods, function (foodItem) {
                        if(foodItem.meal_define == meal_define && foodItem.dish_id == dish_id){
                            foodItem.quantity = food.quantity;
                        }
                    });
                    scope.onChange_luong1tre(scope.datagrid.data[warehouseId][food.food_id],true,false);
                    scope.totalCalculator();
                    scope.getTotalMeal();
                };
                scope.detailByMeal.nutritionChange = function(){
                    scope.row.nutrition_fixed = 1;   /*Trường hợp người dùng tự sửa lại lượng chất trong trực phẩm*/
                    scope.totalCalculator();
                };
                scope.detailByMeal.formShow = function(type) {
                    scope.rowStart = 7;
                    scope.rowNew = -1;
                    var meals = scope.selected.group_adjust.meals;
                    var data = {};
                    var nutritions = scope.selected.group_adjust.nutritions;
                    var first = 0;
                    var title = 'Thông tin thực phẩm theo bữa';
                    var template = scope.controller.templates.meal;
                    if(type){
                        template = scope.controller.templates.mealOfDish;
                        title = 'Thông tin thực phẩm từng món';
                    }
                    if (scope.isUnlockNutrition)
                        template = scope.controller.templates.mealOfDishUnlock;

                    angular.forEach(meals, function(meal,meal_define){
                        data[meal_define] = {
                            name: meal.name,
                            define: meal.define,
                            warehouse_id: meal.warehouse_id,
                            foods: {},
                            money: 0,
                        };
                        if(count(meal.dishes)>0 && first == 0){
                            meal.is_header = true;
                            first++;
                        }
                        angular.forEach(meal.dishes, function(dish, dish_id){
                            angular.forEach(dish.ingredient, function(food,fd_id){
                                if (food.food_id) {
                                    nutritions[food.food_id] || (nutritions[food.food_id] = food.nutritions);
                                    nutritions[food.food_id].protein || (nutritions[food.food_id].protein=0);
                                    nutritions[food.food_id].fat || (nutritions[food.food_id].fat=0);
                                    nutritions[food.food_id].sugar || (nutritions[food.food_id].sugar=0);
                                    food.nutritions = nutritions[food.food_id];
                                    if (!data[meal_define].foods[food.food_id])
                                        data[meal_define].foods[food.food_id] = {
                                            food_id: food.food_id,
                                            name: food.name,
                                            price: food.price,
                                            quantity: food.quantity,
                                            nutritions: nutritions[food.food_id],
                                            foods: [],
                                            quantity_edit: 0,
                                            eat_a_child: 0
                                        };
                                    data[meal_define].foods[food.food_id].quantity_edit = $['+'](data[meal_define].foods[food.food_id].quantity_edit,food.quantity_edit);

                                    var eatAChild = food.quantity_edit / food.gam_exchange;
                                    if (food.extrude_factor !== undefined)
                                        eatAChild = eatAChild / (1 - food.extrude_factor / 100);
                                    data[meal_define].foods[food.food_id].eat_a_child = $['+'](data[meal_define].foods[food.food_id].eat_a_child, eatAChild);
                                    data[meal_define].foods[food.food_id].foods.push(food);
                                    data[meal_define].money += data[meal_define].foods[food.food_id].eat_a_child * scope.row.sotre * food.price;
                                }
                            })
                        })
                    });
                    scope.detailByMeal.meals = data;
                    scope.getTotalMeal();
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project+'/'+self.module,
                        action:'formMealDetail',
                        title: title,
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        scope: scope,
                        content: template,
                    });
                };

                scope.countRowNew = function(){
                    scope.rowNew += 1;
                };
                scope.getRealBuyPerChild = function(food){
                    var eatAChild = food.quantity_edit / food.gam_exchange;
                    if (food.extrude_factor !== undefined)
                        eatAChild = eatAChild / (1 - food.extrude_factor / 100);
                    return eatAChild;
                };
                scope.totalMoneyMeal = {};
                scope.totalCaloMeal = {};
                scope.getTotalMeal = function () {
                    scope.totalMoneyMeal = {};
                    scope.totalCaloMeal = {};
                    if(scope.selected.group_adjust){
                        angular.forEach(scope.selected.group_adjust.meals, function (meal, meal_define) {
                            scope.totalMoneyMeal[meal_define] = scope.totalMoneyMeal[meal_define] || 0;
                            scope.totalCaloMeal[meal_define] = scope.totalCaloMeal[meal_define] || 0;
                            angular.forEach(meal.dishes, function (dish) {
                                scope.totalCaloMeal[meal_define] += scope.detailByMeal.getPLG(dish.ingredient,'plg');
                                angular.forEach(dish.ingredient, function (food) {
                                    if (food.food_id) {
                                        var eatAChild = food.quantity_edit / food.gam_exchange;
                                        if (food.extrude_factor !== undefined)
                                            eatAChild = eatAChild / (1 - food.extrude_factor / 100);
                                        scope.totalMoneyMeal[meal_define] += eatAChild * food.price;
                                    }
                                })
                            })
                        });
                    }
                };
                scope.detailByMeal.exportExcel_tptheobua = function () {
                    var cells_food    = [];
                    var bold_cells    = [];
                    var style_center  = [];
                    var style_left    = [];
                    var merge_cells   = [];
                    var mpcellexports = document.getElementsByName('mpcellexport');
                    var len           = mpcellexports.length;
                    var cell_excel    = [];
                    var mpcellexports_food = document.getElementsByName('mpcellexport_food');
                    var len_food           = mpcellexports_food.length;
                    var start = 7;
                    $('.meal-detail-by-dish tbody tr').each(function(el){
                        $(this).find('[name="mpcellexport_food"]').each(function(){
                            var id = $(this).attr('id');
                            $(this).attr('id',id+start);
                        });

                        $(this).find('.cellmerge').each(function(){
                            var id = $(this).attr('cellmerge').split(':');
                            $(this).attr('cellmerge',id[0]+start+':'+id[1]+start);
                        });
                        start++;
                    })

                    angular.forEach(mpcellexports, function(cell,ind){
                        var el = $(cell);
                        if(el.attr('col') && el.attr('row')) {
                            var cell_id = el.attr('col');
                            if(el.attr('row')) {
                                cell_id += el.attr('row')+'';
                            }
                            if (cell.type=='text') {
                                cell_value = cell.value;
                            } else {
                                cell_value = cell.innerText;
                            }

                            if(cell_id){
                                cell_excel.push({key:cell_id, value:cell_value});
                            }
                        }
                    })

                    for (var i = 0; i < len_food; i++) {
                        if (mpcellexports_food[i].id != 'undefined') {
                            var cell_id_food = mpcellexports_food[i].id;
                            var cell_element = mpcellexports_food[i];
                            if (cell_element.type=='text') {
                                cell_value = cell_element.value;
                            } else if(cell_element.tagName=='SELECT') {
                                cell_value = cell_element.options[cell_element.selectedIndex].text;
                            }else{
                                cell_value = cell_element.innerText;
                            }
                            cell_id_food = cell_id_food.replace("mpexport.", "");
                            cells_food.push({key: cell_id_food, value: cell_value});
                            if ($(cell_element).hasClass('font-bold')) {
                                bold_cells.push(cell_id_food);
                            }
                            if ($(cell_element).hasClass('style-center')) {
                                style_center.push(cell_id_food);
                            }
                            if ($(cell_element).hasClass('style-left')) {
                                style_left.push(cell_id_food);
                            }
                            if ($(cell_element).hasClass('cellmerge')) {
                                merge_cells.push(cell_element.getAttribute("cellmerge"));
                            }
                        }
                    }

                    var json_data   = {
                        so_tre: scope.row.sotre,
                        cell_excel : cell_excel,
                        cells_food: JSON.stringify(cells_food),
                        rowadd: start,
                        row_title: document.getElementsByClassName("rowExport_title").length,
                        merge_cells: JSON.stringify(merge_cells),
                        bold_cells: JSON.stringify(bold_cells),
                        style_left: JSON.stringify(style_left),
                        style_center: JSON.stringify(style_center),
                    };
                    var loadUrl = $CFG.remote.base_url + '/' + $CFG.project + '/menu_adjust/exportPrint_theobua';
                    var downloadUrl = $CFG.remote.base_url + '/' + $CFG.project + '/menu_adjust/excelPrint_theobua';

                    process(loadUrl, json_data, function (resp) {
                        window.location.href = downloadUrl + '?filename=' + resp.filename;
                    });
                };
                scope.detailByMeal.exportFile = function (path, filename = 'excel', callback) {
                    var cells = PHPExcel.getData('excel-cell', 'excel-format');
                    var rows = PHPExcel.getData('excel-row', 'excel-format');
                    var ranges = PHPExcel.getData('excel-range', 'excel-format', false);
                    var merges = PHPExcel.getData('excel-merge', false, false);
                    if (typeof callback === 'function') {
                        callback();
                    }
                    var data = {
                        path: path,
                        filename: filename,
                        rowStart: scope.rowStart,
                        rowNew: scope.rowNew + 1,
                        cells: cells,
                        rows: rows,
                        ranges: ranges,
                        merges: merges
                    };
                    PHPExcel.export(data);
                };
                scope.detailByMeal.getPLG = function(foods) {
                    var rs = 0;
                    if(foods){
                        angular.forEach(foods, function(food,food_id){
                            rs += scope.co_cau(null, scope.menu_adjust.date).protein * (food.quantity_edit*food.nutritions.protein/100)
                                + scope.co_cau(null, scope.menu_adjust.date).fat * (food.quantity_edit*food.nutritions.fat/100)
                                + scope.co_cau(null, scope.menu_adjust.date).sugar * (food.quantity_edit*food.nutritions.sugar/100);
                        });
                    }
                    return rs;
                };
                scope.getCaloOfNutrition = function(nutrition) {
                    var rs = 0;
                    if(nutrition){
                        rs += scope.co_cau(null, scope.menu_adjust.date).protein * nutrition.protein
                            + scope.co_cau(null, scope.menu_adjust.date).fat * nutrition.fat
                            + scope.co_cau(null, scope.menu_adjust.date).sugar * nutrition.sugar;
                    }
                    return rs;
                };
                scope.detailByMeal.getCalo = function(foods) {
                    var rs = 0;
                    if(foods){
                        angular.forEach(foods, function(food,food_id){
                            rs += scope.co_cau(null, scope.menu_adjust.date).protein * food.nutritions.protein
                                + scope.co_cau(null, scope.menu_adjust.date).fat * food.nutritions.fat
                                + scope.co_cau(null, scope.menu_adjust.date).sugar * food.nutritions.sugar;
                        });
                    }
                    return rs;
                };
                scope.detailByMeal.sumPLG = function(foods,plg_name) {
                    var rs = 0;
                    angular.forEach(foods, function(food,food_id){
                        if (food) {
                            food.nutritions[plg_name] || (food.nutritions[plg_name]=0);
                            rs = $['+'](rs,food.nutritions[plg_name]);
                        }
                    });
                    return rs;
                };
                scope.detailByMeal.sumCaloAll = function(){
                    var rs = 0;
                    angular.forEach(scope.detailByMeal.meals, function(meal,meal_define){
                        angular.forEach(meal.foods, function(food,food_id){
                            rs += scope.getCalo(food,'quantity_edit');
                        })
                    });
                    return rs;
                };
                scope.deleteDish = function(food, meal_key) {
                    var r = confirm('Bạn có chắc chắn xóa?');
                    if (!r) return;
                    var food_id = food.food_id;
                    angular.forEach(food.foods, function (fd, fd_id) {
                        fd.delete_from_list = true;
                    });
                    delete scope.datagrid.data[meal_key][food_id];
                    angular.forEach(scope.selected.group_adjust.meals, function (meal, meal_define) {
                        var dishes = {};
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            var foods = {};
                            angular.forEach(dish.ingredient, function (fd, fd_id) {
                                if (!fd.delete_from_list) {
                                    foods[fd_id] = fd;
                                    delete fd.delete_from_list;
                                }
                            });
                            if (count(foods) > 0) {
                                dish.ingredient = foods;
                                dishes[dish_id] = dish;
                            }
                        });
                        meal.dishes = dishes;
                    });
                    scope.totalCalculator();
                };

                scope.onChange_measure = function(food) {
                    var i, j, k, g;
                    process('dinhduong/menu_adjust/updateMeasureId',{measure_id: food.measure_id, food_id: food.food_id, async:true}, function(resp){
                        if (resp.result == "success") {
                            for (i in scope.menu_adjust.group_adjusts) {
                                for (j in scope.menu_adjust.group_adjusts[i].menu_plannings[0].data) {
                                    var foods = scope.menu_adjust.group_adjusts[i].menu_plannings[0].data[j];
                                    for (k in foods) {
                                        if (foods[k].food_id && foods[k].food_id == food.food_id) {
                                            foods[k].measure_id = food.measure_id;
                                            for (g in foods[k].foods) {
                                                foods[k].foods[g].measure_id = food.measure_id;
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    })
                };

                /* Init balance nutritions*/
                $.balance.init(scope);
                $.balance_money.init(scope);
            });
        },0);
    }, balanceShow: function(scope){

    }, combobox_load_dish: function(scope, meal, group_id, dish, is_change){
        return;
        var dish_input = $('<input id="dish" name="dish"></div>');
        $('div#input-dish-content').html('').append(dish_input);
        var disk_ids = [];
        angular.forEach(meal.dishes, function (item, dish_id) {
            if (!dish || dish.id != dish_id) {
                disk_ids.push(dish_id);
            }
        })
        var cmb_option = {
            valueField: 'id',
            textField: 'name',
            value:[],
            height: 24,
            panelHeight: 'auto',
            mode: 'remote',
            onSelect: function(row, element) {
                // Get dish detail.
                process($CFG.remote.base_url + '/doing/dinhduong/menu_adjust/dishs', {id: row.id, async: true}, function (resp) {
                    if (resp) {
                        row = resp;
                        row.quantity_edit = row.quantity;
                        scope.menu_planning.ignore_ids = '';
                        var kt = 0;
                        angular.forEach(row.ingredient, function (food, key) {
                            food.name_edit || (food.name_edit = food.name);
                            scope.menu_planning.ignore_ids = scope.menu_planning.ignore_ids + ',' + key;
                            if (food.quantities) {
                                if (count(food.quantities)==0) {
                                    food.quantities = {};
                                }
                                if (food.quantities[scope.row.group_id] > 0) {
                                    food.quantity = food.quantities[scope.row.group_id];
                                }
                            }
                            if (food.quantity>0) kt++;
                        });
                        if (!kt) {
                            if (!count(row.ingredient)) {
                                alert('Món ăn không có thực phẩm');
                            }
                            if (!kt) {
                                alert('Món ăn không có thực phẩm nào có lượng dành cho nhóm trẻ đang chọn.\n Hãy kiểm tra lại lượng của món ăn theo nhóm trẻ.');
                            }
                        }
                        /*Kiểm tra thực phẩm có thêm ngoài không thì đẩy lại vào*/
                        if (dish) { /*Nếu là thêm mới thì không có món được truyền vào*/
                            if (dish.id === row.id) {
                                angular.forEach(dish.ingredient, function (food, food_id) {
                                    food.name_edit = food.name;
                                    food.deleted = false;
                                    if (!row.ingredient[food_id]) {
                                        row.ingredient[food_id] = food;
                                    }
                                });
                            }
                        }
                        if (scope.selected.dish.id != row.id) {
                            $('#dish').combobox('clear');
                            scope.$apply(function () {
                                scope.selected.dish = row;
                            });
                        }
                    }
                }, function () {
                }, false);

            },onChange: function(newval,oldval,el){

            },onLoadSuccess: function(data,el){
                if(dish){
                    var kt = false;
                    $.each(data,function(ind,dsh){
                        if(dsh.id+'' == dish.id+''){
                            kt = true;
                        }
                    });
                    if(!kt){
                        dish_input.combobox('clear');
                    }
                }
            },
            queryParams: {group_id: group_id, disk_ids: disk_ids},
            width: 200
        };
        if(dish) {
            cmb_option.value = dish.id;
            cmb_option.queryParams.q = dish.name;
        }else{
            scope.selected.dish = {};
        }
        $.dm_datagrid.combobox(dish_input, $CFG.remote.base_url+'/doing/dinhduong/menu_planning/dishs',cmb_option);
    }, showChangeDish: function(scope,meal_selected,dish_old,callback){

    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        // var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.date);
            // rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }

        var captcha = $CFG.dialog_captcha('delete_menu_adjust');

        $.messager.confirm('Xác nhận', '<div style="font-size: 14px;" class="text-danger">Nếu xóa Cân đối khẩu phần, bạn sẽ không thể khôi phục được dữ liệu của Cân đối khẩu phần này! Hãy chắc chắn bạn muốn xóa?</div>' + captcha, function(r){
            if (r){
                var params = {
                    captcha: $('[name="delete_menu_adjust_captcha"]').val(),
                };
                $.dm_datagrid.del($CFG.project+'/'+self.module,ids,function(resp){
                    if (resp.result === 'success' && count(resp.errors) == 0) {
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                }, params)
            }
        });
    }, restore: function () {
        var self = this;
        var ids = [];
        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
            ids.push(row.date);
        });
        if (ids.length == 0) {
            $.messager.alert('Thông báo', 'Hãy chọn một dòng!');
            return;
        }

        var captcha = ids.length < $CFG.record_delete_show_captcha ? '' : $CFG.dialog_captcha('delete_menu_adjust');

        $.messager.confirm('Xác nhận', '<div style="font-size: 14px;" class="text-danger">Xác nhận khôi phục?</div>' + captcha, function (r) {
            if (r) {
                process($CFG.project + '/' + self.module + '/restore', {
                    ids: ids,
                    captcha: $('[name="delete_menu_adjust_captcha"]').val(),
                    async: true
                }, function (resp) {
                    if (resp.result === 'success' && count(resp.errors) == 0) {
                        $.messager.alert('Thông báo', resp.msg);
                        $("#tbl_" + self.module).datagrid('reload');
                    }
                })
            }
        });
    }
    ,showPartPLGInfo:function(scope){ /* Hiển thị bảng chi tiết calo từng bữa ăn */
        // var urls_export = [$CFG.remote.base_url,$CFG.project,'storage_export','exportExcelPhieuXuatKho'];
        $('#export-dialog-plg-view').dialog({
            title: 'Tỉ lệ chất đạt dược',
            width: 350,
            height: 280,
            closed: false,
            cache: false,
            modal: true ,
            onOpen : function (ele) {
                $(ele).show();
            }
        });
    }, angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
        angular.element($('#menu_adjustController')).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        });

    },getDataForm: function(element){
        var datas = [];
        return JSON.stringify(datas);
    },
    doSearch: function(){
        var self = this;
        $.dm_datagrid.doSearch('tbl_'+self.module,{name:"contains",group_ids:"comma_contains",area_ids:"comma_contains",category_ids:"comma_contains"},'and');
    },
    angularBalance_money: function(scope){
        scope.balance_money = {
            fields:[
                { field: 'name', title: 'Tên thực phẩm', width: 200 },
                { field: 'luong1tre_tmp', title: 'Lượng (g)'},
                { field: 'price_kg', type:'number', title: 'Đơn giá (đ/kg)'},
                { field: 'price', title: 'Đơn giá theo ĐVT', type:'number'}
            ], getValue: function(field, row){
                return row[field.field];
            }, formatter: function(field, value, row){
                return value;
            }, data: scope.datagrid.data,
            getStyles: function(field) {
                // if(!field) return;
                var styles = {};
                var width = field.width;
                if(width != undefined){
                    width = width + '';
                    if(width.split('%') == 1){
                        width = width + 'px';
                    }
                    styles.width = width;
                }
                return styles;
            }
        }
        scope.balance.money_selectallfood = false;
        scope.balance.money_min = 5;
        scope.balance.money_minChange = function(){
            if(scope.balance.money_min<1){
                scope.balance.money_min = 1;
            }
            scope.balance_money.foodSelectedChange();
        }
        scope.balance_money.init = function(){
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,index){
                    food.money_selectd = false;
                    food.money = 0;
                });
            });
        }

        scope.balance_money.foodSelectAll = function() {
            scope.balance.money_selectallfood = !scope.balance.money_selectallfood;
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    food.money_selectd = scope.balance.money_selectallfood;
                })
            });
            scope.balance_money.foodSelectedChange();
        }
        scope.balance_money.foodSelectedChange = function(item){
            var foods = [];
            if(item){
                item.money_selectd = !item.money_selectd;
            }
            for(var warehouse_id in scope.balance.data){
                fs = scope.balance.data[warehouse_id];
                // angular.forEach(fs,function(food,food_id){
                for(var food_id in fs){
                    var food = fs[food_id];
                    food.thanhtien_balance = Number(scope.round(food.thanhtien));
                    food.luong1tre_balance = Number(food.luong1tre);
                    // scope.balance.thanhtienOnChange(food);
                    if(scope.selected.balance_warehouse[warehouse_id] && food.money_selectd){
                        foods.push(food);
                    }
                }
            }
            if(foods.length>0){
                scope.service_price[scope.selected.group.id] || (scope.service_price[scope.selected.group.id]=0);
                var money = scope.row.tienchenhlech - scope.service_price[scope.selected.group.id];
                if(round(money,1) != 0) {
                    scope.balance_money.run(foods, money);
                }
            }
        }
        scope.balance_money.run = function(foods, money){
            if(foods.length>0){
                if(scope.balance.money_min<=0){
                    scope.balance.money_min = 1;
                }
                var tong_tien = 0;
                angular.forEach(foods, function(food,index){
                    tong_tien += Number(food.thanhtien);
                });
                var tien_du = 0;
                var food_max_thanhtien = foods[0];
                angular.forEach(foods, function(food,index){
                    var tile = Number(food.thanhtien)/tong_tien;
                    var ext = tile * money;
                    var ext_round = Number(round(ext));
                    tien_du += ext - ext_round;
                    if(food.thanhtien > 0){
                        if(food.thanhtien + ext_round >= scope.balance.money_min){
                            food.thanhtien_balance = Number(food.thanhtien) + ext_round;
                        }else{
                            if(food.thanhtien>scope.balance.money_min){
                                food.thanhtien_balance = scope.balance.money_min;
                                tien_du += ext_round - (food.thanhtien - food.thanhtien_balance);
                            }
                        }
                        if(food.thanhtien > food_max_thanhtien.thanhtien){
                            food_max_thanhtien = food;
                        }
                        scope.balance.thanhtienOnChange(food);
                    }
                });
                var money_balance = scope.balance.getTienchenhlech()-scope.service_price[scope.selected.group.id];
                if(food_max_thanhtien.thanhtien_balance + money_balance>scope.balance.money_min){
                    food_max_thanhtien.thanhtien_balance += money_balance;
                }else{
                    if(food_max_thanhtien.thanhtien>scope.balance.money_min){
                        food_max_thanhtien.thanhtien_balance = scope.balance.money_min;
                    }
                }
                scope.balance.thanhtienOnChange(food_max_thanhtien);
            }
        }
    }
};
