angular_app_report.controller('KeHoachNamHocController', ['$scope', '$compile', function ($scope) {
    $scope.data = {};
    $scope.semester = "";
    $scope.unitSignTypes = {
        schoolAdmin: 'sign_bang<PERSON><PERSON><PERSON>',
        secretary: 'sign_giaovien',
    }
    $scope.unitSigns = {}
    $scope.module = 'ke_hoach_nam_hoc';
    $scope.courseId = data.courseId;

    $scope.sign = async function (type) {
        const response = await saveUnitSignConfig({
            module: $scope.module,
            type: type,
            courseId:  $scope.courseId,
        });

        if (response.success) {
            await fetchUnitSignConfig({module: $scope.module});

            alert(response.message);

            return;
        }

        alert(response.message);
    }

    $scope.saveReport = async function () {
        var reportValues = document.querySelectorAll(`[name='report-value']`);
        var postData = {};
        reportValues.forEach(element => {
            postData[element.id] = element.value
        })

        process('dinhduong/class_gr_quality/KeHoachNamHoc', {
            courseId: $scope.courseId,
            data: postData
        })

    }

    $scope.export = async function () {
        
            $('.overlay').css('visibility', 'visible');
            var formData = new FormData();
    
            formData.append('courseId', $scope.courseId);
            
            $.ajax({
                url: $CFG.remote.base_url + '/doing/dinhduong/class_gr_quality/KeHoachNamHocExport',
                type: 'POST',
                xhrFields: {
                    responseType: 'blob'
                },
                data: formData,
                processData: false,
                contentType: false,
                success: function(response, status, xhr) {
                    var filename = ""; 
                    var disposition = xhr.getResponseHeader('Content-Disposition');
            
                    if (disposition && disposition.indexOf('attachment') !== -1) {
                        var regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                        var matches = regex.exec(disposition);
                        if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
                    }
            
                    var blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
                    
                    if (typeof window.navigator.msSaveBlob !== 'undefined') {
                        window.navigator.msSaveBlob(blob, filename);
                    } else {
                        var link = document.createElement('a');
                        var url = window.URL.createObjectURL(blob);
                        link.href = url;
                        link.download = filename || 'download.docx';
                        document.body.appendChild(link);
                        link.click();
                        
                        setTimeout(function() {
                            document.body.removeChild(link);
                            window.URL.revokeObjectURL(url);
                        }, 100);
                    }
                }, 
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus + ': ' + errorThrown);
                }, 
                complete: function() {
                    $('.overlay').css('visibility', 'hidden');
                }
            });
    }

    const fetchUnitSignConfig = async function (
        {
            module,
            projectId = null,
            groupId = null,
            courseId = null,
            date = null,
            month = null
        }) {
        const response = await findUnitSignConfig({module, projectId, groupId, courseId, date, month});

        if (response.success) {
            let signData = JSON.parse(response.data['sign_data']);

            Object.keys(signData).forEach((key) => {
                $scope.$apply(function () {
                    $scope.unitSigns[key] = {
                        url: buildUnitSignImageUrl($CFG.remote.base_url, $CFG.unit_id, signData[key]['filename']),
                        title: signData[key]['time'],
                        fullname: signData[key]['fullname'],
                    }
                });
            });
        }
    }

    fetchUnitSignConfig({module: $scope.module, courseId: $scope.courseId}).then(r => console.log(r));
}]);
