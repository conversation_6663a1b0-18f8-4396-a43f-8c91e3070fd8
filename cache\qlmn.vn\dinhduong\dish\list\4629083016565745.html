<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_dish_detail">
		<div class="title-kh-ct">

			<div id="header-title" class="header-title" tbl-menu-in-group="dish"></div>
			<div class="function-kh-ct" style="margin-top: 32px">
				<div class="header-search" id="header-search">
                    <span class="icon-searching glyphicon" ng-show="keysearch_group || keysearch_area || keysearch_category || keysearch_name" onclick="$.dish.cleanSearch()">
                        <span class="glyphicon glyphicon-filter hover-pointer" title="Bỏ tìm kiếm" >
                            <span class="glyphicon glyphicon-remove"></span>
                        </span>
                    </span>
                    
                    <input id="name" field="dishes.name" style="width:150px !important;" class="form-control" placeholder="Tên hoặc mã món ăn" ng-model="keysearch_name" ng-change="dish.onChangeKeysearchName(keysearch_name)" ng-model-options="{debounce: 1000}">
                    <input style="margin-top: 10px;" id="group_ids" field="dishes.group_ids" placeholder="Độ tuổi" ng-model="keysearch_group">
                    <input style="margin-top: 10px;" id="area_ids" field="dishes.area_ids" placeholder="Vùng miền" ng-model="keysearch_area">
                    <input style="margin-top: 10px;" id="category_ids" field="dishes.category_ids" placeholder="Danh mục" ng-model="keysearch_category">
                    <!-- <div class="combobox-class" style="margin-left: 3px;">
                        <input id="malophientai" field="students.malophientai" placeholder="Chọn lớp">
                    </div> -->
                   <!--  <button onclick="$.dish.doSearch()" class="btn btn-default btn-timkiem" style="margin-left: 3px;">
                        <span class="glyphicon glyphicon-search"></span>Tìm kiếm
                    </button> -->
                    <div class="btn-group" style="position: absolute;right: 4px;">
                        <button type="button" onclick="$.dish.showAddForm()" class="btn btn-primary action">
                            <span class="glyphicon glyphicon-plus"></span>Thêm mới
                        </button>
                        <button type="button" class="btn btn-primary action" onclick="$.dish.showEditForm()">
                            <span class="glyphicon glyphicon-edit"></span>Sửa
                        </button>
                        <button type="button" class="btn btn-primary action" onclick="$.dish.share2()">
                            <span class="glyphicon glyphicon-share-alt"></span>Chia sẻ
                        </button>
                        <a href="http://localhost:3000/single/dinhduong/dish_storage/list">
                            <button type="button" class="btn btn-primary action">
                            <span class="glyphicon glyphicon-share-alt"></span>Thư viện món ăn chia sẻ
                        </button>
                        </a>
                      <!--   <button type="button" onclick="$.dish.showShareForm()" class="btn btn-primary">
                            <span class="glyphicon glyphicon-plus"></span>Thư viện món ăn
                        </button> -->
                        <button type="button" class="btn btn-primary action" onclick="$.dish.del()">
                            <span class="glyphicon glyphicon-remove-circle"></span>Xóa
                        </button>
                        <button type="button" id="show_restore" onclick="$.dish.restore()" class="btn btn-primary hidden">
                            <span class="fa fa-undo"></span>Khôi phục
                        </button>
                        <div class="dropdown dropdown-setting">
                            <button type="button" class="btn btn-link dropdown-toggle ng-pristine ng-untouched ng-valid ng-empty"  data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-cog fa-spin icon-setting"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li>
                                    <input type="checkbox" title="Hiện món ăn đã xóa" onclick="change_status(this)" ng-click="reloadDatagrid()" class="ng-pristine ng-untouched ng-valid ng-empty"> Hiện món ăn đã xóa
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                </div>
			</div>
			<div class="support-video">
                <a target="_blank" href="https://www.youtube.com/watch?v=tQf7g5baFgQ">
                    <img src="http://localhost:3000/images/icon_hotro1.gif">
                </a>
            </div>
		</div>
		
	</div>
	<div id="tbl_dish"></div>
    <input hidden id="status" value="1">
</div>
<script src="http://localhost:3000/js/dinhduong/dish.js?_=505267725"></script>
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/dish.css?_=1">
<script type="text/javascript">
    $(function(){
        $.dish.init();
    });
	var url = $CFG.remote.base_url +"/doing/dinhduong/dish/";
    function change_status(ele)
	{
		if(!ele.checked)
		{
            $("#status").val(1);
            $('.action').removeClass("hidden");
			document.getElementById('show_restore').classList.add("hidden");
		}
		else{
            $("#status").val(0);
            $('.action').addClass("hidden");
			document.getElementById('show_restore').classList.remove("hidden");
        }
	}
</script>
<style type="text/css">
    .combo{
        margin-top: 5px;
        margin-left: 5px;
    }
    .btn-timkiem{
        height: 25px;
        margin-top: 5px;
        padding: 1px 7px;
    }
    #vung_mien,#danh_muc_mon{

    }
    #name{
        height: 25px;
        margin-top: 5px;
        border-radius: 0px;
        width: 180px; 
        margin-right: 3px;
    }
    .form-control{
        padding: 2px 6px;
    }
    .support-video{
        width: 120px;
        position: fixed;
        bottom: 4px;
        right: 0px;
        z-index: 6666;
    }
</style>

