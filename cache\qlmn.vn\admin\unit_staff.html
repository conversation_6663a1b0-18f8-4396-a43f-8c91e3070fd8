<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<base href="http://localhost:3000/admin/">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/uploader/css/jquery.fileupload.css">

	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />

	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />

	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/admin/style.css?_=1316003930" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/common.css?_=1805234255" />

	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
    <script type="text/javascript">
		var allowed_provinces = '35_79';
		var allowed_province_arr = allowed_provinces.split('_');
		$CFG = {
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'admin',
        	spinController: {},
			username: 'd.hd.mnquangvt',
			administrator: parseInt('0'),
			record_delete_show_captcha: +'2',
			dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
            is_sync_csdl_nganh: parseInt('0'),
			allowed_provinces: allowed_province_arr,
      	};
	</script>
    <script src="http://localhost:3000/js/jquery-3.3.1.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>

	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>

	<script src="http://localhost:3000/js/uploader/js/vendor/jquery.ui.widget.js"></script>
	<script src="http://localhost:3000/js/uploader/js/load-image.min.js"></script>
	<script src="http://localhost:3000/js/uploader/js/canvas-to-blob.min.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.iframe-transport.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload-process.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload-image.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload-audio.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload-video.js"></script>
	<script src="http://localhost:3000/js/uploader/js/jquery.fileupload-validate.js"></script>

	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>

	<script src="http://localhost:3000/js/common.js?_=1230102318"></script>
	<script >
		function change_unit_id_auto() {
			process($CFG.remote.base_url+'/doing/admin/user/change_unit_id_auto',{},function(){
				$.messager.show({
                        title:'Thông báo',
                        msg:'Xử lý thành công!',
                        timeout:2000,
                        showType:'slide'
                    });
			});
		}
		function update_method(){
      		var project = $("#project").val();
      		var module = $("#module").val();
      		process($CFG.remote.base_url+'/doing/admin/action_define/update_method',{p:project,m:module},function(){
				$.messager.show({
                        title:'Thông báo',
                        msg:'Xử lý thành công!',
                        timeout:2000,
                        showType:'slide'
                    });
			});
      	}
      	$(function(){
      		$(".light-blue.dropdown").hover(
	            function(){ $(this).addClass('open') },
	            function(){ $(this).removeClass('open') }
	        );
      	});
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=519176949"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="container" style="width:100%;">
		<!-- <div class="row"> -->
			<div class="main-menu" style="width: 100%; height: 35px;">
				<div role="navigation" class="navbar-buttons navbar-header">
		        	<ul class="nav ace-nav">
		        		<li class="light-blue"><a href="http://localhost:3000/single/dinhduong"> 
		        			<i class="ace-icon fa fa-home"></i>Trang chủ </a>
		        		</li>
		        		<li class="light-blue" style="display: none;"><a href="javascript: void(0)" onclick="change_unit_id_auto()"> 
		        			<i class="ace-icon fa fa-home"></i>Sửa bảng đơn vị </a>
		        		</li>
	        					        					        							        		<li class="light-blue"><a href="http://localhost:3000/admin/lock_module"> Mở/kh&oacute;a chức năng </a></li>
			        						        		<li class="light-blue"><a href="http://localhost:3000/admin/user_permission_group"> Nh&oacute;m quyền </a></li>
			        						        		<li class="light-blue"><a href="http://localhost:3000/admin/unit_staff"> Quản l&yacute; nh&acirc;n sự </a></li>
			        						        		<li class="light-blue"><a href="http://localhost:3000/admin/user"> Người d&ugrave;ng </a></li>
			        						        		<li class="light-blue"><a href="http://localhost:3000/admin/user_log"> Lịch sử </a></li>
			        						        				        			        						        <!--li class="light-blue">
		        			<a href="http://localhost:3000/admin/lock_module">
		        				Mở/khóa chức năng 
		        			</a>
		        		</li-->
				        <li class="light-blue">
		        			<a href="http://localhost:3000/logout">
		        				<i class="ace-icon fa fa-power-off" style="color: #ae1111;" ></i>
		        				Thoát 
		        			</a>
		        		</li>
		        	</ul>
		        </div>
			</div>
			<div class="view-content">
				
	<div class="tbl-container-header header-kh-ct" id="tb_unit_staff_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="unit_staff"></div>
			<div class="function-kh-ct">
				<label>Quản lý nhân sự</label>
				<div class="btn__group">
				  	<button type="button" onclick="$.unit_staff.showAddForm()" class="btn btn-link">
				  		<span class="glyphicon glyphicon-plus"></span>Thêm mới
				  	</button>
					  <button type="button" onclick="$.unit_staff.importFromExcel()" class="btn btn-link mL10">
						<span class="glyphicon glyphicon-file"></span>Thêm từ excel
					</button>
										<div class="dropdown">
						<button class="btn btn-default btn-print mL20 dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<span class="bootstrap-dialog-button-icon glyphicon glyphicon-print mg-right-5"></span> Báo cáo
						</button>
						<div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
						  	<a  href="{{$CFG.remote.base_url+'/admin/unit_staff/listFull'}}" class="dropdown-item" target="_blank"><span>Sơ yếu lý lịch</span></a>
							<a 	href="{{$CFG.remote.base_url+'/dinhduong/unit_seminar/list'}}" class="dropdown-item" target="_blank"><span>Kết quả hội giảng</span></a> 
						  	<a  href="{{$CFG.remote.base_url+'/dinhduong/unit_rank/list'}}" class="dropdown-item" target="_blank"><span>Kết quả đánh giá xếp loại</span></a>
						  	<a  href="{{$CFG.remote.base_url+'/dinhduong/unit_attend/list'}}" class="dropdown-item" target="_blank"><span>Tổng hợp dự giờ các hoạt động</span></a>
						  	<a  href="{{$CFG.remote.base_url+'/dinhduong/school_report/staffReport'}}" class="dropdown-item" target="_blank"><span>Theo dõi tình hình đội ngũ CBQL, GV</span></a>
						  	<a  href="{{$CFG.remote.base_url+'/dinhduong/school_report/inspectReport'}}" class="dropdown-item" target="_blank"><span>Kết quả thanh tra, kiểm tra các cấp</span></a>
						  	<a class="dropdown-item" href="{{$CFG.remote.base_url+'/admin/unit_staff/listFollow'}}" target="_blank"><span>Sổ theo dõi thi đua</span></a>
						</div>
					</div>
										<div class="dropdown" ng-if="$CFG.is_sync_csdl_nganh">
						<button class="btn btn-default btn-print mL20 dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<span class="glyphicon glyphicon-retweet clrBlue"></span> Tiện ích
						</button> 
						<div class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="margin-left: -100px; text-align:center; width: 300px;">
							<a href="http://localhost:3000/report/csdl_nganh_v2/list_staff" target="_blank"><button type="button" class="btn btn-info" >[ THÔNG TIN ĐỒNG BỘ CSDL NGÀNH ]</button>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="tbl_unit_staff"></div>
<script src="http://localhost:3000/js/admin/unit_staff.js?_=1843734987"></script>
<script type="text/javascript">
    $(function(){
    	$CFG.school_points = 1;
        $.unit_staff.init();
    });
</script>
<style>
	.btn__group{
		display: flex;
		align-items: baseline;
		float: right;
		margin-right: 10%;
	}
	a.btn.btn-default.btn-print.mL20 {
		height: 30px;
		border-radius: 10%;
	}
	.dropdown-menu {
		position: absolute;
		top: 100%;
		left: 0;
		display: none;
		min-width: 10rem;
		padding: 0.5rem 0;
		margin: 0;
		font-size: 1rem;
		color: #212529;
		text-align: left;
		background-color: #fff;
		background-clip: padding-box;
		border: 1px solid rgba(0, 0, 0, 0.15);
		border-radius: 0.25rem;
		left: 1rem;
	}

		.dropdown-item {
		display: block;
		width: 100%;
		padding: 0.25rem 1.5rem;
		clear: both;
		font-weight: 400;
		font-size: 14px;
		color: #212529;
		text-align: inherit;
		white-space: nowrap;
		background-color: transparent;
		border: 0;
	}

		.dropdown-item:hover,
		.dropdown-item:focus {
		color: #16181b;
		text-decoration: none;
		background-color: #f8f9fa;
	}
</style>
			</div>
		<!-- </div> -->
	</div>
	<style>
		.datagrid-body, .container {
			width: 100% !important;
		}
	</style>
	<script>
		$('body').on('click', 'button.captcha-refresh-btn', function () {
			var img = $(this).prev();
			$(this).prev().prop('src', img.prop('src').toString().replace(/t=(.*?)&/, 't=' + (new Date()).getTime() + '&'));
		});
	</script>
</body>
</html>
