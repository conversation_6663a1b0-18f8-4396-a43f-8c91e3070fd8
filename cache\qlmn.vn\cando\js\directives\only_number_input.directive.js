(function OnlyNumberInputDirective(app) {
    app.directive('onlyNumberInput', function OnlyNumberInputDirectiveController() {
        return {
            scope: {
                ngModel: '=',
            },
            link: function ($scope, $element) {
                var KEY_0 = 48;
                var KEY_9 = 57;
                var KEY_BACK_SPACE = 8;
                var KEY_ENTER = 13;
                var KEY_PERIOD = 190;
                var KEY_DECIMAL = 110;
                var KEY_DELETE = 46;

                var ALLOWED_KEYS = [
                    KEY_BACK_SPACE,
                    KEY_ENTER,
                    KEY_PERIOD,
                    KEY_DECIMAL,
                    KEY_DELETE,
                ];

                angular.element($element).on('keypress', function (event) {
                    var key = event.which;

                    if ((key >= KEY_0 && key <= KEY_9) || ALLOWED_KEYS.indexOf(key) >= 0) {
                        return true;
                    }

                    event.preventDefault();
                    return false;
                });
            },
        };
    });
})(window.angular_app);
