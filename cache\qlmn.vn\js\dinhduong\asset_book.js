angular_app_report.controller('assetBookController', ['$scope', '$compile', function ($scope) {

    $scope.blocAgeId = blocAgeId;
    $scope.courseId = courseId;
    $scope.semester = '1';
    $scope.phongId = '0';
    $scope.level = level;
    $scope.truongId = '0';
    $scope.lstTruongs = [];

    $scope.changeBlocAge = function () {
        if ($scope.level < 4) return 
        if (!parseInt($scope.blocAgeId)) {
            alert('Hãy chọn nhóm lớp!');
            return;
        }
        $scope.loadData();
    }

    $scope.init = function () {
        if (parseInt($scope.courseId)) {
            alert("<PERSON><PERSON><PERSON> khoản không có quyền thực hiện chức năng này!");
            return;
        }
        $scope.loadData();
    }

    $scope.phongChange = function () {
        if ($scope.phongId == '0') {
            alert("Vui lòng chọn phòng");
            $scope.lstTruongs = [];
            return;
        }

        process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
            async: true,
            id: $scope.phongId,
            project_id: 2
        }, function (resp) {
            $scope.$apply(function () {
                $scope.lstTruongs = resp;
            });
        }, function () {
        }, false);
    }

    $scope.loadData = function () {
        if (parseInt($scope.courseId) || !parseInt($scope.blocAgeId)) {
            return;
        }
        if ($scope.level == 2 && !parseInt($scope.phongId)) {
            return;
        }
        process($CFG.project + '/school_report/assetBook', {bloc_age_id: $scope.blocAgeId, phong_id: $scope.phongId, truong_id: $scope.truongId}, function (resp) {
            $scope.assets = resp.assets;
            Object.keys($scope.assets).forEach(key => {
                $scope.assets[key].forEach(item => {
                    if (item['date_add_new_first_semester'])
                        item['date_add_new_first_semester'] = new Date(item['date_add_new_first_semester'])
                    if (item['date_add_new_final_semester'])
                        item['date_add_new_final_semester'] = new Date(item['date_add_new_final_semester'])
                })
            })
            $scope.types = resp.types;
            $scope.user = resp.user;
            if ($scope.assets.length == 0) {
                // alert('Độ tuổi không được hỗ trợ');
            }
        })
    }

    $scope.saveData = function () {
        let assets = Object.values($scope.assets).reduce((rs, items) => {
            let tmp = JSON.parse(JSON.stringify(items)).map(item => {
                if (item['date_add_new_first_semester'])
                    item['date_add_new_first_semester'] = $scope.formatDate(item['date_add_new_first_semester']);
                if (item['date_add_new_final_semester'])
                    item['date_add_new_final_semester'] = $scope.formatDate(item['date_add_new_final_semester']);
                return item;
            })
            rs = rs.concat(tmp);
            return rs;
        }, []);
        process($CFG.project + '/school_report/saveAssetBook', {assets: assets, bloc_age_id: $scope.blocAgeId}, function (resp) {
        })
    }

    $scope.init();

    $scope.formatDate = function (item, showView = false) {
        if (!item || item.length == 0) {
            return "";
        }
        const date = new Date(item);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Thêm 1 vì tháng trong JavaScript bắt đầu từ 0
        const day = String(date.getDate()).padStart(2, '0');

        return showView ? `${day}/${month}/${year}` : `${year}-${month}-${day}`;
    };

    $scope.export = function () {
        downloadFileCommon($CFG.remote.base_url + '/doing/' + $CFG.project + '/school_report/exportAssetBook', {semester : $scope.semester, bloc_age_id: $scope.blocAgeId});
    }
}]);