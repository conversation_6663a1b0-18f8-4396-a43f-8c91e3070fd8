<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_storage_inventory_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="storage_inventory"></div>
			
			<div class="function-kh-ct " style="padding-top: 10px;line-height: 40px">
				<div style="width: 200px; float: left;">
					<label>Chọn kho : </label>
                    <select ng-options= "item as item.name for item in storage_inventory.warehouses track by item.id" ng-model="storage_inventory.warehouse" id="warehouse_id" ng-change="storage_inventory.warehouseChange(storage_inventory.warehouse.id)" style="height: 25px;">
                        <option value="">Tất cả</option>
                    </select>
				</div>
                		    	<div style="width: 130px;float: left;">
               		<div class="pull-left" style="padding-right: 5px;"><span style="color:red;font-weight: bold">Lưu ý : </span> </div>
	                <div style="float:left;border:black 1px solid;background-color: rgb(242, 213, 155) ;line-height: 11px;width:14px;margin-top:13px">&nbsp;</div>&nbsp;&nbsp;Sắp hết
               	</div>
		    	<div style="width: 200px; float: left;">
					<input ng-model="storage_inventory.keysearch_ten"
						   class="form-control search-input-warehouse"
						   placeholder="Tên thực phẩm hoặc mã thực phẩm"
						   ng-change="storage_inventory.onChangeKeysearchName(storage_inventory.keysearch_ten)">
                </div>
                <!-- <ul class="nav navbar-nav" style="float: left;">
					<li class="dropdown">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
							<span class="glyphicon glyphicon-print"></span> 
							Xử lý tồn cuối năm <span class="caret"></span>
						</a>
						<ul class="dropdown-menu">
							<li>
								
							</li>
							<li>
								<button type="button" ng-click="storage_inventory.undoMoveInventoriesToNextYear()" class="btn btn-primary" title="Thu hồi số tồn đã chuyển về lại kho cũ">
							  		<span class="glyphicon glyphicon-plus"></span> Thu hồi tồn đã chuyển từ năm (2023 - 2024)
							  	</button>
							</li>
						</ul>
					</li>
				</ul> -->
				<div class="dropdown dropdown-setting">
                    <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
                        <i class="fa fa-cog fa-spin icon-setting"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right" style="min-width: 140px !important">
                    	<li><a class="pLR5" href="" ng-click="storage_inventory.exportBrokenFoodForm()" title="Xuất hủy thực phẩm hỏng, mốc, hết hạn" ng-if="1"><i class="glyphicon glyphicon-ban-circle clrRed mR5"></i>Xuất hủy TP</a></li>
						<li role="separator" class="divider"></li>
						<li><a class="pLR5" href="" ng-click="storage_inventory.exportBrokenFoodListForm()" title="Danh sách thực phẩm xuất hủy"><i class="glyphicon glyphicon-print clrBlue mR5"></i>DS TP đã xuất hủy</a></li>
                    </ul>
                </div>
                <div class="btn-group">
                	<button type="button" ng-click="storage_inventory.showConfirm()" class="btn btn-primary" title="Chuyển hết tồn sang năm học tiếp theo" ng-disabled="0">
				  		<span class="glyphicon glyphicon-plus"></span> Chuyển tồn sang năm học (2023 - 2024)
				  	</button>
                	<button type="button" ng-click="storage_inventory.exportInventoriesFinalYearList()" class="btn btn-primary" title="Xóa hết xuất kho trả nhà cung cấp">
				  		<span class="glyphicon glyphicon-plus"></span> Danh sách trả NCC
				  	</button>
				  	<button type="button" ng-click="storage_inventory.exportInventoriesFinalYearForm()" class="btn btn-primary" title="Xuất hết số lượng đang tồn để trả nhà cung cấp" ng-disabled="0">
				  		<span class="glyphicon glyphicon-plus"></span> Xuất kho trả NCC
				  	</button>
				</div>
			</div>
		</div>
	</div>
	<div id="tbl_storage_inventory"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/storage_inventory.js"></script>
<script type="text/javascript">
	$.storage_inventory.init();
</script>
<style type="text/css">
.container-price{
	display: inline-flex;
	padding: 1px 5px;
	border: 1px solid #bbb;
	background: #fff;
	border-radius: 3px;
	padding-right: 0px;
	height: 25px;
}
.container-price > input{
	width: 70px;
	padding: 0px;
	border: unset;
}
.container-price > label{
	border-left: 1px solid #ccc;
	padding: 2px 6px;
	margin: 2px;
	color: #969696;
}
#frm-storage{
	margin-top: 10px;
	margin-bottom: 10px;
}
.btn-add{
	height: 25px;
	padding: 3px 12px;
	background: #69adde;
	margin-bottom: 1px;
	outline: none;
	color: #FFF;
	opacity: 0.3; 
}
.btn-add span{
	color: #FFF !important;
}
.dropdown-setting .dropdown-menu>li>a {
	padding: 3px 5px;
	color: blue !important;
}
.dropdown-setting .dropdown-menu .divider {
	margin: 5px 0;
}

a.pLR5:hover {
    background: #eaeaea !important;
}
.search-input-warehouse{
	height: 27px;
	font-size: 11px;
	margin-top: 5px;
}
</style>

