(function (window, app) {
    'use strict';

    app.service('StudentMedicalExaminationService', StudentMedicalExaminationService);

    function StudentMedicalExaminationService($http, $q, $httpParamSerializerJQLike, APP_CONFIG) {
        var self = this;

        self.fetchStudents = function fetchStudents(page, limit, filters) {
            page = page || 1;
            limit = limit || 15;
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations';
            var options = {
                params: {filters: filters, page: page, limit: limit},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime());

            return $http.get(url, options)
                .then(function (response) {
                    var students = _.get(response, 'data.students', []);
                    var total = _.get(response, 'data.total', 0);

                    return {
                        total: total,
                        students: students,
                    };
                })
                .catch(function () {
                    return {
                        total: 0,
                        students: [],
                    };
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        };

        self.store = function store(studentId, diseaseId, isPositive, schoolYear, month, note = '') {
            statusloading((new Date()).getTime());
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations';

            var data = {
                student_id: studentId,
                disease_id: diseaseId,
                is_positive: isPositive,
                school_year: schoolYear,
                month: month,
                note: note
            };

            return $http.post(url, data, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    return true;
                })
                .catch(function () {
                    return false;
                })
                .finally(function () {
                    statusloadingclose();
                });
        };

        self.generateDownloadExcelUrlForDashboard = function (filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations/download';
            if (filters.course_id == 0) {
                var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations/download/all';
            }

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };

        self.generateDownloadExcelUrlForDashboardReports = function (filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations/download/reports';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        };
        self.generateDownloadExcelUrlForDashboardFollows = function (filters) {
                    var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-medical-examinations/download/follows';

                    return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
                };

        self.storeMedicalExaminationNote = function (studentId, note, schoolYear, month, conclusion) {
            statusloading((new Date()).getTime());
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/students/medical-note/store';

            var data = {
                student_id: studentId,
                note: note,
                school_year: schoolYear,
                month: month,
                conclusion : conclusion
            };

            return $http.post(url, data, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    return true;
                })
                .catch(function () {
                    return false;
                })
                .finally(function () {
                    statusloadingclose();
                });
        };

        return self;
    }

    StudentMedicalExaminationService.$inject = [
        '$http',
        '$q',
        '$httpParamSerializerJQLike',
        'APP_CONFIG',
    ];

})(window, window.angular_app);
