(function AppConstant(app, $CFG) {
    'use strict';

    app.constant('APP_CONFIG', {
        'API': {
            'ENDPOINT': _.trimEnd($CFG.remote.base_url, '/'),
        },
    });

    app.constant('ACTIVE_MONTHS', [9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8]);

    app.constant('BMI_LABELS', {
        'BMI_FOR_WEIGHT': {
            'OVERWEIGHT': 36,
            'OBESITY': 37,
            'NORMAL': 38,
            'THINNESS': 39,
            'SEVERE_THINNESS': 40,
        },
        'BMI_FOR_HEIGHT': {
            'OVER_HEIGHT2': 31,
            'OVER_HEIGHT1': 32,
            'NORMAL': 33,
            'THINNESS': 34,
            'SEVERE_THINNESS': 35,
        },
        'BMI_FOR_AGE': {
            'OVERWEIGHT': 1,
            'OBESITY': 2,
            'NORMAL': 3,
            'THINNESS': 4,
            'SEVERE_THINNESS': 5,
        },
    });
})(window.angular_app, window.$CFG);
