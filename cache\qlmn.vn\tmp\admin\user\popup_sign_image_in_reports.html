<style>
    .selected_digital_signs {

    }
</style>

<form class="form-horizontal" role="form" style="display: inline-block; width: 100%;"
      xmlns="http://www.w3.org/1999/html">
    <div class="form-group col-md-12">
        <div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
            <div style="font-weight: bold; border-bottom:1px dotted green;"><b>KÝ ẢNH</b></div>
            <div style="padding-top: 5px;">
                <label>Chọn báo cáo:</label>
                <select class="form-control" name="" id="rpt_code"><option value="">Chọn báo cáo</option></select>
            </div>
            <div style="padding-top: 5px;">
                <label>Chọn vị trí ký:</label>
                <select class="form-control" name="" id="rpt_sign_key"><option value="">Chọn vị trí</option></select>
            </div>
            <div style="padding-top: 5px;" id="cb_school_point">
                <label>Chọn điểm trường:</label>
                <select class="form-control" name="" id="school_point"><option value="">Chọn điểm trường</option><option ng-repeat="i in [].constructor($CFG.school_points) track by $index+1" ng-value="$index+1">{{$index+1}}</option></select>
            </div>
            <div style="padding-top: 5px;">
                <label>Ngày bắt đầu:</label>
                <input type="date" class="form-control" id="date_from">
            </div>
            <div style="padding-top: 5px;">
                <label>Ngày kết thúc:</label>
                <input type="date" class="form-control" id="date_to">
            </div>

            <div style="padding-top: 5px;">
                <button id="btn_save_sign" class="btn btn-success btn-sm">Thực hiện ký</button>
            </div>
        </div>
    </div>
</form>

<script>
    var reports = [];
    var positions = [];
    var reportEle = $("#rpt_code");
    var positionEle = $("#rpt_sign_key");
    var btnSave = $("#btn_save_sign");
    var schoolPoint = $('#school_point');
    var dateFrom = $("#date_from");
    var dateTo = $("#date_to");
    const UpdateUnitSign = (() => {
        'use strict';

        const API_URL = {
            get: $CFG.remote.base_url +  '/doing/admin/user/getReports',
            save: $CFG.remote.base_url +  '/doing/admin/user/saveSignReports',
        }

        const getReports  = () => {
            fetch(API_URL.get, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(resp => {
                reports = resp.data;
                bindReports();
            });
        }

        const bindReports = () => {
            reportEle.html("");
            reportEle.append(
                $("<option>", {value: ""}).text("Chọn báo cáo")
            );
            Object.values(reports).map(item => {
                reportEle.append(
                    $("<option>", {value: item.rpt_code}).text(item.name)
                );
            })
        }

        reportEle.on('change', function () {
            $('#school_point').val("");
            bindPositions();
        })

        positionEle.on('change', function (e) {
            
            const reportSelected = reportEle.val();
            const value = e.target.value
            if (reportSelected in reports && value in reports[reportSelected].keys) {
                if (reports[reportSelected].keys[value]['rpt_sub_code']) {
                    $('#cb_school_point').show()
                } else {
                    $('#cb_school_point').hide()
                }
            }
            $('#school_point').val("");
        })

        const bindPositions = () => {
            const reportSelected = reportEle.val();
            positionEle.html("");
            positionEle.append(
                $("<option>", {value: '', selected: true}).text('Chọn vị trí')
            );
            if (reportSelected in reports) {
                Object.values(reports[reportSelected].keys).map(item => {
                    positionEle.append(
                        $("<option>", {value: item.rpt_sign_key}).text(item.rpt_title_label)
                    );
                })
            }
        }

        const checkDateSameMonth = function (date1, date2) {
            const d1 = new Date(date1);
            const d2 = new Date(date2);

            // Kiểm tra năm và tháng
            const sameYear = d1.getFullYear() === d2.getFullYear();
            const sameMonth = d1.getMonth() === d2.getMonth();
            const date1BeforeDate2 = d1 <= d2;

            // Trả về kết quả
            return sameYear && sameMonth && date1BeforeDate2;
        }

        btnSave.on('click', function () {
            const params = {
                'rpt_code' : reportEle.val(),
                'rpt_sign_key': positionEle.val(),
                'school_point': $('#school_point').val(),
                'date_from': dateFrom.val(),
                'date_to': dateTo.val(),
            }
            if (!params.rpt_code || !params.rpt_sign_key || !params.date_from || !params.date_to) {
                $.messager.alert('Cảnh báo', 'Hãy nhập đầy đủ thông tin!', 'warning');
                return;
            }
            if (!checkDateSameMonth(params.date_from, params.date_to)) {
                $.messager.alert('Cảnh báo', 'Ngày bắt đầu và ngày kết thúc phải trong cùng 1 tháng!', 'warning');
                return;
            }
            $.messager.confirm('Xác nhận', 'Bạn có chắc chắn muốn ký?', function (r) {
                if (r) {
                    fetch(API_URL.save, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Access-Control-Allow-Origin': '*',
                            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                        },
                        body: JSON.stringify(params)
                    })
                    .then(response => response.json())
                    .then(resp => {
                        if (resp.success) {
                            $.messager.alert('Thông báo', resp.message);
                        }
                        else {
                            $.messager.alert('Cảnh báo', resp.message, 'error');
                        }
                    });
                }
            })
        })

        const init = async () => {
            getReports();
        }

        return {
            init
        }
    })();
    UpdateUnitSign.init();
</script>