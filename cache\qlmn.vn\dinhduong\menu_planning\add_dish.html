<form class="form-horizontal layout-accordion" role="form">
	<div class="form-group add-dish-header" ng-init="initFormDish()" style="display: flex;align-items: center;">
		<label class="control-label valid mR5 mL10">Chọn bữa:</label>
		<div id="input-meal">
			<input id="meal" name="dish">
		</div>
		<label class="control-label valid mR5 mL15">Chọn món:</label>
		<div id="input-dish">
			<input id="dish" name="dish" placeholder="Tên món ăn">
		</div>
		<label class="valid mL15 mB0" style="cursor:pointer;">
			<input type="checkbox" ng-model="from_meal_share" class="mR5 mT5" ng-change="reloadDishes()">
			<span style="float: right;line-height: 17px;">Tìm trong thư viện<br>món ăn chia sẻ</span>
		</label>
		<label class="valid mL15 mB0" style="cursor:pointer;" title="Cập nhập về danh sách món ăn của trường" ng-show="from_meal_share">
			<input type="checkbox" ng-model="copy_meal" class="mR5 mT5">
			<span style="float: right;line-height: 17px;">Cập nhập về<br>DS món ăn</span>
		</label>
		<button ng-click="addMealForm()" class="btn btn-primary mL15" ng-show="!from_meal_share" style="padding: 6px 10px">
			<span class="glyphicon glyphicon-plus"></span>
			Món ăn mới
		</button>
		<span class="mL20">
			<a href="http://localhost:3000/single/dinhduong/dish" target="_blank" style=" top: 0; right: -30;" title="Quản lý món ăn">
				<img src="http://localhost:3000/css/dinhduong/images/quan_ly_mon_an.png" width="24">
			</a>
			<button ng-click="addDishApply()"> Áp dụng </button>
		</span>
	</div>
	<div class="form-group content-add-dish">
		<div class="col-md-12 no-padding-left no-padding-right" ng-if="selected.dish.name">
			<div class="form-group col-md-12 col-sm-12">
				<div class="col-md-12">
					<div class="form-group">
						<div class="col-md-2"><label class="control-label no-padding-left" style="text-align: left;">Thông tin món : </label></div>
						<div class="col-md-4"><input style="padding-left: 10px;" type="text" ng-model="selected.dish.name"></div>
					</div>
					<div class="form-group">
						<div class="col-md-2"><label class="control-label no-padding-left" style="text-align: left;">Mô tả : </label></div>
						<div class="col-md-10">
							<textarea readonly="true" ng-value="selected.dish.description" class="col-md-12"></textarea>
						</div>
					</div>
					<div class="form-group">
						<div class="col-md-2"><label class="control-label no-padding-left" style="text-align: left;">Cách nấu : </label></div>
						<div class="col-md-10">
							<textarea readonly="true" style="min-height: 100px;" ng-value="selected.dish.recipe" class="col-md-12"></textarea>
						</div>
						
					</div>
				</div>
				<div class="col-md-12">
					<div class="form-group">
						<div class="col-md-2">
							<label class="control-label no-padding-left" style="text-align: left;">Chọn thực phẩm</label>
						</div>
						<div class="col-md-10">
							<!-- <input combo-box="menu_planning.dish_selected" value-field="food_id" text-field="name" on-select="foodSelected(menu_planning.dish_selected,menu_planning.ignore_ids)" type="text" param-key="ids" param-value="menu_planning.ignore_ids" url="{{$CFG.remote.base_url}}/doing/dinhduong/menu_planning/foods" reload-on-selected="true"> -->

							<input id="food{{_temp.random}}" combo-box-food="menu_planning.dish_selected" data="cache.foods" url="{{$CFG.remote.base_url}}/doing/dinhduong/menu_planning/foods" mode="remote" on-select="foodSelected(menu_planning.dish_selected)" value-field="id" text-field="name" >
						</div>						
					</div>
				</div>
			</div>
			<div class="form-group col-md-12" style="margin-bottom: 0;">
				<table class="table food-lists food-lists-of-dish" ng-init="candoi.data=datagrid.data" style="margin-bottom: 0;">
					<thead>
						<tr class="row-head row-head-title">
							<th class="col-head col-1" style="width: 40px;">STT</th>
							<th class="col-head col-1" style="">Tên thực phẩm</th>
							<th class="col-head col-1" style="width: 150px;">Số lượng 1 trẻ (g)</th>
							<th class="col-head col-1" style="width: 100px;">Calo/1 trẻ</th>
							<th class="col-head col-1" style="width: 100px;">Calo/100(g)</th>
							<th class="col-head col-1" rowspan="2" style="width: 100px;" title="Thực phẩm chính">TPC</th>
							<th class="col-head col-1" style="width: 100px;">Xóa</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="(index,food) in selected.dish.ingredient">
							<td ng-bind="$index+1" align="center"></td>
							<td ng-bind="food.name" style="text-align: left;"></td>
							<td align="right">
								<input style="width: 100%;" type-number="float" ng-change="menu_planning.onChangeQ(food)" ng-model="food.quantity">
							</td>
							<td ng-bind="round(sumCaloFood(food,'quantity'))" align="right" ></td>
							<td ng-bind="food.nutritions.calo" align="right"></td>
							<td align="right">
								<input type="checkbox" ng-model="food.tpc" ng-true-value="1" ng-false-value="0">
							</td>
							<td><i class="glyphicon glyphicon-trash btn-color-blue del-food" ng-click="delFood(food.food_id, 'old')" title="Xóa thực phẩm"></i></td>
						</tr>
						<tr ng-repeat="(index, foodAdded) in foodAdded">
							<td ng-bind="$index+1+count(selected.dish.ingredient)" align="center"></td>
							<td ng-bind="foodAdded.name" style="text-align: left;"></td>
							<td align="right">
								<input style="width: 100%;" type-number="float" ng-change="menu_planning.onChangeQ(foodAdded)" ng-model="foodAdded.quantity">
							</td>
							<td ng-bind="round(sumCaloFood(foodAdded, 'quantity'))" align="right"></td>
							<td ng-bind="foodAdded.nutritions.calo" align="right"></td>
							<td align="right">
								<input type="checkbox" ng-model="foodAdded.tpc" ng-true-value="1" ng-false-value="0">
							</td>
							<td>
								<i class="glyphicon glyphicon-trash btn-color-blue del-food" ng-click="delFood(foodAdded.food_id, 'new')" title="Xóa thực phẩm"></i>
							</td>
						</tr>
						<tr style="font-weight: bold;border-bottom: 1px solid #ccc;">
							<td colspan="2" align="right">Tổng</td>
							<td align="right"></td>
							<td align="right" ng-bind="round(sumCaloDish(selected.dish.ingredient,'quantity'))" ></td>
							<td></td>
						</tr>
						<tr style="font-weight: bold;border-bottom: 1px solid #ccc;">
							<td colspan="2" align="right">Định mức Calo một ngày cho "<text ng-bind="selected.group.name"></text>"</td>
							<td></td>
							<td align="right" ng-bind="selected.group.nutritions.calo"></td>
							<td ng-bind=""></td>
						</tr>
						<tr style="font-weight: bold;border-bottom: 1px solid #ccc;">
							<td colspan="2" align="right">Tỉ lệ Calo theo định mức (%)</td>
							<td></td>
							<td align="right" ng-bind="round(sumCaloDish(selected.dish.ingredient,'quantity')/selected.group.nutritions.calo*100)"></td>
							<td ng-bind=""></td>
						</tr>
					</tbody>
					<tfoot>
						
					</tfoot>
				</table>
			</div>
		</div>
	</div>
</form>
<div style="clear: both;"></div>
<style type="text/css">
	.add-dish-header{
		background: #e9f5e5;
		padding: 10px;
	}
	.content-add-dish{

	}
	.del-food{
		color: red;
	}
	.del-food:hover{
		cursor: pointer;
	}
	#input-meal .textbox.combo {
		width: 100px !important;
	}
	#input-dish .textbox.combo {
		width: 150px !important;
	}
</style>
