$.menu_information = {
    module: 'menu_information',
    init: function() {
    	var self = this;
        self.reload();
        setTimeout(function(){
            angular.element($("#tbl_menu_information")).scope().$apply(function(scope){
                scope.onChangeBuasang = function(row){
					$.messager.confirm('Xác nhận', '<div style = "font-size: 14px; color:red;">H<PERSON>y lưu ý CĐKP, STTA đã lưu trước đây nếu xóa đi làm lại sẽ áp dụng theo kho mới!!! Bạn có chắc chắn muốn thay đổi kho cho <PERSON> sán<PERSON>? </div>', function(r){
						if (r){
							process($CFG.project+'/'+self.module+'/saveWFMorning',{async: true, id: row.warehouse_id},function(resp){
								if(resp.result == 'success') {
									$.menu_information.reload();
								}
							},function(){
							});
						}else{
							$.menu_information.reload();
						}
					});
                }
            });
        });
    },
    reload: function(){
        var self = this;
        process($CFG.project+'/'+self.module+'/list',{async: true},function(resp){
            setTimeout(function(){
                angular.element($("#tbl_menu_information")).scope().$apply(function(scope){
                    scope.menu_informations = resp;
                    scope.editAction = $.menu_information.editAction;
                });
            })
        },function(){

        },false);
    },
    editAction: function(rows){
        var self = this;
        process($CFG.project+'/menu_information/edit',{rows: rows, async: true},function(resp) {
            if(resp.result == 'success'){
                $.menu_information.reload();
            }
        });
    },configPage: function(){
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        var header = $('#tb_'+self.module);
        var body = $('#tbl_'+self.module);
        var parent_h = parseInt(body.parent().height());
        var header_h = parseInt(header.outerHeight());
        var parent_padding_top_h = parseInt(body.parent().css('padding-top').replace('px',''));
        var parent_padding_bottom_h = parseInt(body.parent().css('padding-bottom').replace('px',''));
        var h =  parent_h - header_h;
        body.css({width: '100%',height: h});
    },restorDefault: function(){
        var self = this;
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px"> - Hành động này có thể làm mất các dữ liệu đã thay đổi.<br/> - Chắc chắn khôi phục dữ liệu gốc ?</div>', function(r){
            if (r){
                var url = $CFG.project+'/'+self.module+'/restore_default';
                process(url,{},function(resp){
                    self.reload();
                })
            }
        });
    }

}
