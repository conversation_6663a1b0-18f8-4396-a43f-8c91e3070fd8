﻿/*
Notificatiuons Popover TODO

-Load notifications from JSON
- on load set new count to number from DB
- after clicking to ura-open notificatuions popover, reset/clear the unread counter to 0
- 
*/

function getUrlParam(parameter) {
    var vars = {};
    var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
        vars[key] = value;
    });
    if(typeof(vars[parameter]) != "undefined"){
        return vars[parameter];
    }
    return '';
}
/* Danh sach thong bao cap nhat */
var items = {};
/* Thu tu, index cua thong bao dang duoc mo */
var item_img_index = 0;
/* Id cua the div hoac the chua thong form thong bao */
var div_notice_id = 'div_ura_notification';
var unit_level = $CFG.level;
var user_id = $CFG.user_id;
var username = $CFG.username;
/* Ma lop cua thong bao he thong */
var code = 'cando'; // Can Do Vietec
var base_api = $CFG.base_http_url+'/api/notification/info?'
if (typeof($CFG.is_gokids) != "undefined" && $CFG.is_gokids=='1'){
	var code = 'cando_gke'; // Can Do - GKE
}

var province = $CFG.province;
var district = $CFG.district;
var is_principal = $CFG.is_principal;

/* So luong thong bao chua doc */
var bubble_number = 0;
/* Gioi han cua thong bao */
var limit = 1000;
/* Mo rong thong bao ra full man hinh */
var is_show_full_notify = 0;
function setUpNotification() {
	/* Doc thong bao tu API da duoc thiet lap san */
	$.ajax({
        url: base_api,
        dataType: 'json',
        data: {unit_level:unit_level, user_id:user_id, code:code, province: province, district:district, is_principal:is_principal, limit:limit},
        method: 'get',
        crossDomain: true,
        async: true,
        success: function (response) {
			items = response;
			if(typeof response.data !== 'undefined') {
				items = response.data;
			}
			for(var i=0; i<items.length; i++) {
				var item = items[i];
				if(!item.is_read) {
					bubble_number += 1;
				}
			}
			loadNotification(items, bubble_number);
        }
    });
	function loadNotification(items, bubble) {
		var noticeHtml = '';
		noticeHtml += '<div class="ura-notice-contain-popover"><style>' + getPopoverStyle(bubble) + '</style>' +
		'<ul class="ura-notificationsbtn ura-noti-nav ura-noti-navbar-nav ura-noti-navbar-right" style="margin-right:10px;">'+
		'<li id="ura-notificationsli">'+
			'<a id="notifications" href="#" id="drop3" style="display: inline-block" role="button" class="ura-dropdown-toggle ura-noti-link" data-toggle="dropdown" onclick="checkOpenPopover()">'+
		  '<span class="ura-noti-manager-bubble" style="opacity: 1; width: 100%" title="Thông báo từ hệ thống">' + setTextNumberNotify(bubble) + '</span></a>'+
			'<div id="ura-notification-container" class="ura-noti-dropdown-menu" role="menu" aria-labelledby="drop3" style="background-color:#f1f1f1;">'+
				'<section class="ura-noti-panel" style="padding-top: 0px;">'+
					'<header class="ura-noti-panel-heading" style="text-align: center;">'+
						'<div style="text-align: center; padding-bottom: 5px;"><label id="ura-div-expand-msg" style="font-weight:bold; color:green; cursor:pointer; float:left;" onclick="openFullNotifyPage()">[Mở rộng]</label><strong style="color:#000; text-align:center;">THÔNG BÁO</strong> <label style="float:right; color:red; cursor:pointer; font-weight:bold;" onclick="checkOpenPopover()">[x] Đóng</label></div>'+
					'<div style="text-align: right; padding-right:10px;border-top: 1px solid #ccc;padding-top: 5px; color:#000 !important">Tìm kiếm: <input type="text" id="txt_search_notification" placeholder="Nhập nội dung & nhấn enter" onchange="searchNotification()" style="width: 200px;"/></div>'+	
					'</header>'+
					'<div id="ura-notification-list" class="ura-list-group ura-list-group-alt">';
					if(items.length>0) {
						for(var i=0; i<items.length; i++) {
							var item = items[i];
							var date = new Date(item.created * 1000);
							var read_font_weight = '';
							if(item.is_read) {
								read_font_weight = 'font-weight:normal;';
							}
							var description = item.subject;
							var show_image_style = 'display:none;';
							if(is_show_full_notify) {
								description = item.summary;
								show_image_style = 'display:block;'
							}
							var created_date_str = ('0' + date.getDate()).slice(-2) + '/' + ('0' + (date.getMonth() + 1)).slice(-2) + '/' + date.getFullYear() + ' ' + ('0' + date.getHours()).slice(-2) + ':' + ('0' + date.getMinutes()).slice(-2);
							noticeHtml += '<div style=""><div id="div_box_msg_id'+item.id+'" class="ura-noti-manager-list-item ura-noti-manager-list-item-error"><div class="ura-activity-item"><div class="ura-activity"> <a href="#" onclick="openNotification('+i+')" id="alink_msg_id'+item.id+'" class="ura-noti-link" style="padding-left: 0px;font-family: sans-serif;'+read_font_weight+'">' + description + '</a> <span>' + created_date_str + '</span> </div>';
							if(item.attachments.length>0) {
								noticeHtml += '<p class="ura-show-full-img-notify" style="text-align:center; padding:10px; cursor:pointer;'+show_image_style+'"  ondblclick="openNotification('+i+')" title="Click đúp vào ảnh để xem chi tiết nội dung & hình ảnh"><img id="ura_first_img_itemid'+item.id+'" src="" width="100%"/></p>';
							}
							noticeHtml += '</div></div></div>';
						}
					}else{
						noticeHtml += '<p style="text-align:center; margin: 10px 0 10px;">Chưa có thông báo<p>';
					}
					noticeHtml += '</div>'+
				'</section>'+
			'<div class="ura-full-text-hint" style="float:left; color:orange; display:none; padding: 10px;">Click vào tiêu đề hoặc click đúp vào ảnh xem chi tiết hơn!</div><div id="ura-search-number-result" style="text-align:right; margin:10px; font-weight:bold; color:#000 !important">Số bản ghi: '+items.length+' </div></div>'+
		'</li></ul></div>';
		$('#'+div_notice_id).append(noticeHtml);
		setStyleNumberNotify(bubble);
		$('.ura-noti-manager-bubble').css({
			top: $CFG.is_gokids ? '-8px' : '6px'
		});
	}
}
function setTextNumberNotify(bubble){
	return bubble === 0 ? 'Thông báo cập nhật' : 'Thông báo cập nhật ' + '('.concat(bubble, ')');
}
function setStyleNumberNotify(bubble){
	$('#notifications').css({
		width: bubble === 0 ? '150px' : '150px'
	});
}
/* Mo 1 thong bao tu danh sach thong bao */
function openNotification(item_index) {
	var msg = items[item_index];
	if (!msg) return;
	if (!msg.id) return;
	var width = 800;
	item_img_index = 0;
	var img_hint = '';
	var h = window.innerHeight;
	var first_src = '';
	if(msg.attachments.length>0) {
		img_hint = '(Ảnh '+(item_img_index+1)+'/'+msg.attachments.length+')';
		first_src = msg.attachments[0].url;
	}
	var div = $('<div class="ura-contain-thong-bao"><div class="ura-dialog-thong-bao" style="position: relative;"><style>' + getOpenNoticeStyle() + '</style>' +
		'<div class="ura-tb-dialog-content" style="max-height:'+(h-150)+'px'+'; overflow-y: auto; padding-bottom: 20px;"><p><label style="float:left; padding-left:10px; font-weight:bold; color:#353535;font-family: sans-serif;padding-top: 5px;">NỘI DUNG THÔNG BÁO <span id="img_hint">'+img_hint+'</span>:</label><label class="ura-btn-dialog-close" title="Đóng"><span>&#8855;</span> Đóng</label></p>' +
		'<p style="width: 100%; margin-top: 30px;text-align: left; padding: 10px; color:#353535;font-family: sans-serif;font-size:15px; white-space:pre-line;">'+msg.summary+'</p>'+
		'<img id="img_notification_show" style="width: 90%; cursor:pointer;" src="' + first_src + '" onclick="changeImage('+item_index+')" title="Click vào ảnh để xem thêm ảnh tiếp theo nếu thông báo có nhiều ảnh"></div></div></div>');
	$('#'+div_notice_id).append(div);
	$('.ura-btn-dialog-close').click(function () {
		div.remove();
	});
	// Kiem tra & cap nhat trang thai da doc, chua doc
	if(!msg.is_read) {
		/* Doc thong bao tu API da duoc thiet lap san */
		$.ajax({
			url: base_api,
			dataType: 'json',
			data: {user:username, user_id:user_id, code:code, msg_id:msg.id, is_read:1},
			method: 'get',
			crossDomain: true,
			async: true,
			success: function (response) {
				items[item_index].is_read = 1;
				$('#alink_msg_id'+msg.id).css('font-weight', 'normal');
				bubble_number -= 1;
				$('.ura-noti-manager-bubble').text(setTextNumberNotify(bubble_number));
				setStyleNumberNotify(bubble_number);
			}
		});
	}
}
// Tang giam, thay doi anh tren thong bao pop up
function changeImage(item_index){
	var msg = items[item_index];
	if (msg.attachments.length>0){
		item_img_index ++;
		if (item_img_index > (msg.attachments.length-1)) {
			item_img_index = 0;
		}
		$('#img_hint').text('(Ảnh '+(item_img_index+1)+'/'+msg.attachments.length+')');
		$('#img_notification_show').attr("src", msg.attachments[item_img_index].url);
	}
	
}
/* Kiem tra dong/mo notification popover */
function checkOpenPopover(){
	if ($('#ura-notification-container').css('display')=='none') {
		$("#ura-notification-container").css("display", "block");
	}else{
		$("#ura-notification-container").css("display", "none");
	}
}
/* Mo rong ra full man hinh trang */
function openFullNotifyPage() {
	/* Dang o trang thai thu nho */
	if(is_show_full_notify==0) {
		var w = window.innerWidth;
		var h = window.innerHeight;
		$('#ura-notification-container').css({"width":'800px', 'height':(h-100)+'px', 'margin-left':'50px'});
		$('#ura-notification-list').css({'width':'798px','height':(h-200)+'px', 'max-height':(h-200)+'px'});
		$('.ura-show-full-img-notify').css('display', 'block');
		$('#ura-div-expand-msg').text('[Thu nhỏ]');
		$('.ura-full-text-hint').css('display', 'block');
		/*Set lai gia tri duoc mo rong*/
		is_show_full_notify=1;
	}else{
		$('#ura-notification-container').css({'width':'300px',"height":'500px'});
		$('#ura-notification-list').css({'width':'298px',"height":'400px', 'max-height':'400px'});
		$('.ura-show-full-img-notify').css('display', 'none');
		$('.ura-full-text-hint').css('display', 'none');
		$('#ura-div-expand-msg').text('[Mở rộng]');
		/*Set lai gia tri duoc thu nho lai*/
		is_show_full_notify=0;
	}
	if(items.length>0) {
		for(var i=0; i<items.length; i++) {
			var item = items[i];
			if(is_show_full_notify==1) {
				$('#alink_msg_id'+item.id).text(item.summary);
				if(item.attachments.length>0) {
					$('#ura_first_img_itemid'+item.id).attr('src', item.attachments[0].url);
				}
			}else{
				$('#alink_msg_id'+item.id).text(item.subject);
				$('#ura_first_img_itemid'+item.id).attr('src', '');
			}
		}
	}
}

/* Tim kiem noi dung thong bao qua noi dung */
function searchNotification() {
	var search = $('#txt_search_notification').val();
	search = search.toLowerCase();
	var search_count = 0;
	if(items.length>0) {
		for(var i=0; i<items.length; i++) {
			var item = items[i];
			var sumary_vn = item.summary.toLowerCase();
			var sumary_en = item.summary_en.toLowerCase();
			if(sumary_vn.indexOf(search) > -1 || sumary_en.indexOf(search) > -1) {
				$('#div_box_msg_id'+item.id).css('display', 'block');
				search_count += 1;
			}else{
				$('#div_box_msg_id'+item.id).css('display', 'none');
			}
		}
	}
	$('#ura-search-number-result').text('Số bản ghi: '+search_count);
}

/* Lay style mo popup thong bao */
function getOpenNoticeStyle() {
    var style = `
        .ura-dialog-thong-bao{
            margin-top: 20px;
        }
        .ura-dialog-thong-bao .ura-btn-dialog-close{
            position: absolute;
            color: #344144;
            font-weight: bold;
            cursor: pointer;
            font-size: 20px;
            right: 15px;
            top: 0px;
        }
        .ura-dialog-thong-bao .note-text-auto{
            position: absolute;
            font-style: italic;
            color: orange;
        }
        .ura-dialog-thong-bao .ura-btn-dialog-close>span{
            font-size: 25px;
        }
        .ura-dialog-thong-bao .ura-btn-dialog-close:hover{
            color: orange;
        }
        .ura-contain-thong-bao{
            position: fixed !important;
            height: 100% !important;
            width: 100% !important;
            background: rgba(100,100,100,0.6) !important;
            box-shadow: unset !important;
            padding: 0px !important;
            text-align: center;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        .window-shadow{
            background: rgba(10,10,10,0.5);
        }
        .ura-dialog-thong-bao .ura-tb-dialog-content{
            position: relative;
            display: inline-block;
            width: auto;
			background: #e9f5e5;
			border-radius: 5px;
			border: 5px solid #e9f5e5;
			margin:10px;
        }
        .ura-dialog-thong-bao .input-checkbox{
            display: block;
            margin-top: -18px;
            cursor: pointer;
        }
        .ura-dialog-thong-bao .input-checkbox:hover{
            color: orange;
        }
    `;
    return style;
}

/* Lay style mo popup thong bao */
function getPopoverStyle(bubble) {
	var style = `
		.starter-template {
			padding: 40px 15px;
			text-align: center;
		}
		a.ura-noti-link {
			color: #428bca;
			background-color:transparent !important;
		}
		a.ura-noti-link:hover {
			/*color: #D65C4F;
			text-decoration: none;*/
		}
		#ura-notification-list {
			width: 298px;
			max-height: 400px;
			overflow-y: scroll;
			background-color:#fff;
		}
		.ura-noti-dropdown-menu > .ura-noti-panel {
			border: none;
			margin: -5px 0;
		}
		.ura-noti-panel-heading {
			background-color: #f1f1f1;
			border-bottom: 1px solid #dedede;
			padding: 5px;
		}
		.ura-activity-item i {
			float: left;
			margin-top: 3px;
			font-size: 16px;
		}
		div.ura-activity {
			margin-left: 15px;
			padding-right:10px;
		}
		div.ura-activity-item {
			padding: 7px 0px;
		}
		#ura-notification-list div.ura-activity-item {
			border: 1px solid #ccc;
			margin: 10px;
			border-radius: 10px;
		}
		#ura-notification-list div.ura-activity-item a {
			font-weight: 600;
		}
		div.ura-activity span {
			display: block;
			color: #999;
			font-size: 11px;
			line-height: 16px;
		}
		#notifications i.fa {
			font-size: 17px;
		}
		.noty_type_error * {
			font-weight: normal !important;
		}
		.noty_type_error a {
			font-weight: bold !important;
		}
		.noty_bar.noty_type_error a, .noty_bar.noty_type_error i {
			color: #fff
		}
		.noty_bar.noty_type_information a {
			color: #fff;
			font-weight: bold;
		}
		.noty_type_error div.ura-activity span {
			color: #fff
		}
		.noty_type_information div.ura-activity span {
			color: #fefefe
		}
		.no-notification {
			padding: 10px 5px;
			text-align: center;
		}
		.noty-manager-wrapper {
			position: relative;
			display: inline-block !important;
		}
		.ura-noti-manager-bubble {
			position: absolute;
			background-color: #fb6b5b;
			color: #fff;
			padding: 5px 0px 15px 0px !important;
			font-size: 12px;
			line-height: 12px;
			text-align: center;
			white-space: nowrap;
			vertical-align: baseline;
			cursor: pointer;
			width: 20px;
			height: 16px;
			/*font-weight: bold;*/

			border-radius: 10px;
			-moz-border-radius: 10px;
			-webkit-border-radius: 10px;
			box-shadow:1px 1px 1px rgba(0,0,0,.1);
			opacity: 0;
		}
		.ura-noti-manager-bubble:hover {
			color: blue;
		}
		.ura-noti-dropdown-menu {
			position: absolute;
			top: 100%;
			left: 0;
			z-index: 1000;
			display: none;
			float: left;
			min-width: 160px;
			padding: 5px 0;
			margin: 2px 0 0;
			font-size: 14px;
			text-align: left;
			list-style: none;
			background-color: #fff;
			-webkit-background-clip: padding-box;
			background-clip: padding-box;
			border: 1px solid #ccc;
			border: 1px solid rgba(0,0,0,.15);
			border-radius: 4px;
			-webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
			box-shadow: 0 6px 12px rgba(0,0,0,.175)
		}

		.ura-noti-dropdown-menu>li>a {
			display: block;
			padding: 3px 20px;
			clear: both;
			font-weight: 400;
			line-height: 1.42857143;
			color: #333;
			white-space: nowrap
		}

		.ura-noti-dropdown-menu>li>a:focus,.ura-noti-dropdown-menu>li>a:hover {
			/*color: #262626;
			text-decoration: none;
			background-color: #f5f5f5*/
		}

		.ura-open>.ura-noti-dropdown-menu {
			display: block
		}

		.ura-open>a {
			outline: 0
		}

		@media (min-width: 768px) {
			.ura-noti-navbar-right .ura-noti-dropdown-menu {
				right:0;
				left: auto
			}

			.ura-noti-navbar-right .ura-noti-dropdown-menu-left {
				right: auto;
				left: 0
			}
		}

		.ura-noti-nav {
			padding-left: 0;
			margin-bottom: 0;
			list-style: none
		}

		.ura-noti-nav>li {
			position: relative;
			display: block
		}

		.ura-noti-nav>li>a {
			position: relative;
			display: block;
			padding: 10px 15px 0px 5px
		}

		.ura-noti-nav>li>a:focus,.ura-noti-nav>li>a:hover {
			text-decoration: none;
			/*background-color: #eee*/
		}

		.ura-noti-nav>li.disabled>a {
			color: #777
		}

		.ura-noti-nav>li.disabled>a:focus,.ura-noti-nav>li.disabled>a:hover {
			color: #777;
			text-decoration: none;
			cursor: not-allowed;
			background-color: transparent
		}

		.ura-noti-nav .ura-open>a,.ura-noti-nav .ura-open>a:focus,.ura-noti-nav .ura-open>a:hover {
			border-color: #337ab7
		}

		.ura-noti-navbar-nav>li>a {
			padding-top: 10px;
			padding-bottom: 10px;
			line-height: 20px
		}

		.ura-noti-navbar-nav>li>.ura-noti-dropdown-menu {
			margin-top: 0;
			border-top-left-radius: 0;
			border-top-right-radius: 0
		}

		.ura-navbar-btn {
			margin-top: 8px;
			margin-bottom: 8px
		}

		.ura-navbar-btn.btn-sm {
			margin-top: 10px;
			margin-bottom: 10px
		}

		.ura-navbar-btn.btn-xs {
			margin-top: 14px;
			margin-bottom: 14px
		}

		.ura-navbar-text {
			float:left;
			margin-top: 15px;
			margin-bottom: 15px
		}
		.ura-navbar-left {
			float:left!important
		}

		.ura-noti-navbar-right {
			float: right!important;
			margin-right: -15px
		}
		@-webkit-keyframes my {
			0% { color: #000; }
			100% { color: #blue;  }
		}
		@-moz-keyframes my {
			0% { color: #000; }
			100% { color: #blue;  }
		}
		@-o-keyframes my {
			0% { color: #000; }
			100% { color: #blue;  }
		}
		@keyframes my {
			0% { color: #000; }
			100% { color: #blue;  }
		}
	`;
	if(bubble>0) {
		style += `
				.ura-noti-manager-bubble {
				-webkit-animation: my 700ms infinite;
				-moz-animation: my 700ms infinite;
				-o-animation: my 700ms infinite;
				animation: my 700ms infinite;
			}
		`;
	}
	return style;
}

$(document).ready(function() {
	setTimeout(function() {
		setUpNotification();
	});
});