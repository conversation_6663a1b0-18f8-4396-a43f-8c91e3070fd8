((angular_app)=>{
    var scope = null;
    angular_app.controller('menu_adjustController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){
        scope = $scope;
        $scope.project = 'dinhduong';
        $scope.control = 'menu_adjust';
        $scope.form_id = 'menu_adjust-total';
        $scope.dialog = undefined;
        $scope.controller = {
            templates: {
                addForm: $scope.project + '/' + $scope.control + '/add.html?_=7',
				editFormTPC: $scope.project + '/' + $scope.control + '/edit_tpc.html?_=',
                marketbillForm: $scope.project + '/' + $scope.control + '/marketbillForm.html?_=',
                editServicePrice: $scope.project + '/' + $scope.control + '/edit_service_price.html?_=',
                duplicateToMenuPlanning: $scope.project + '/' + $scope.control + '/duplicate_to_menu_planning.html?_=',
                quantityInfo: $scope.project + '/' + $scope.control + '/quantity_info.html?_=',
                nutritionInfo: $scope.project + '/' + $scope.control + '/nutrition_info.html?_=',
                changeDish: $scope.project + '/' + $scope.control + '/change_dish.html?_=',
				changeDishTPC: $scope.project + '/' + $scope.control + '/change_dish_tpc.html?_=',
                shareWithAdmin: $scope.project + '/' + $scope.control + '/share_with_admin.html?_=',
                addMenuPlanning: $scope.project + '/' + $scope.control + '/add_menu_planning.html?_=',
                meal: $scope.project + '/' + $scope.control + '/meal.html?_=',
                mealOfDish: $scope.project + '/' + $scope.control + '/meal_of_dish.html?_=2',
                mealOfDishUnlock: $scope.project + '/' + $scope.control + '/meal_of_dish_unlock.html?_=',
                addFood: $scope.project + '/' + $scope.control + '/add_food.html?_=',
                addDish: $scope.project + '/' + $scope.control + '/add_dish.html?_=',
                separateFood: $scope.project + '/' + $scope.control + '/separate_food.html?_=',
                balance: $scope.project + '/' + $scope.control + '/balance.html?_=1',
                balanceMoney: $scope.project + '/' + $scope.control + '/balance_money.html?_=1',
                edit_number_children: $scope.project + '/' + $scope.control + '/edit_number_children.html?_=',
                edit_tien_bo_tro: $scope.project + '/' + $scope.control + '/edit_tien_bo_tro.html?_=',
                formCopy: $scope.project + '/' + $scope.control + '/copy.html?_=',
                printForm: $scope.project + '/' + $scope.control + '/print-form.html?_=',
                roundForm: $scope.project + '/' + $scope.control + '/round.html?_=',
                foodDetail: $scope.project + '/' + $scope.control + '/food_detail.html?_=',
                total_group_meal: $scope.project + '/' + $scope.control + '_total/total-group-meal.html?_=',
                table_checklist: $CFG.template.base_url + '/' + $scope.project + '/' +$scope.control + '/table-checklist.html?_=3',
                table_balance_info: $CFG.template.base_url + '/' + $scope.project + '/' +$scope.control + '/table-balance-info.html?_=7',
            }
        };

        $scope.selected = {};
        $scope.table = {
            rows: [],
            groupColNames: {
                soluong: {name: 'SL (ĐVT)', title: 'Thực mua cả nhóm theo đơn vị tính'},
                soluong_kg: {name: 'SL (KG)', title: 'Thực mua cả nhóm theo Kg'},
                dongia: {name: 'Đơn giá', title: 'Giá thực phẩm theo đơn vị tính'},
                thanhtien: {name: 'Thành tiền', title: 'Thành tiền cả nhóm nhóm'}
            }
        };
        $scope.totalGroupMealForm = ()=>{
            $.dm_datagrid.showAddForm({
                title:'Tổng hợp thực phẩm theo bữa',
                size: size.wide,
                fullScreen: true,
                showButton: false,
                draggable: true,
                scope: $scope,
                content: $scope.controller.templates.total_group_meal + '?_=' + refresh(),
                onShown: (element, dialogRef) => {
                }
            });
        };
        $scope.roundOptionForm = ()=>{
            $scope.roundOption = {
                data: clone($scope.datagrid.data),
                selected: true,
                option: 1,
                totalRound: 0
            };
            angular.forEach($scope.roundOption.data, function (foods) {
                angular.forEach(foods, function (food) {
                    food.selected = true;
                    food.option = 1;
                    food.totalRound = 0;
                });
            });
            $scope.roundOptionCalc();
            $.dm_datagrid.showAddForm({
                title:'Làm tròn thực mua',
                size: size.wide,
                fullScreen: true,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.roundForm + '?_=' + refresh(),
                onShown: (element, dialogRef) => {
                    element.getModalBody().css({'overflow': 'hidden'});
                }
            });
        };
        $scope.roundBuyApply = () => {
            angular.forEach($scope.roundOption.data, function(foods, meal_key){
                for(var food_index in foods) {
                    var food = foods[food_index];
                    if (food.selected) {
                        if (food.totalRound) {
                            food.thucmuatheodvt = food.thucmuatheodvt_change;
                            food.thucmuatheodvts = food.thucmuatheodvt_changes;
                            $scope.datagrid.data[meal_key][food_index].thucmuatheodvt = food.thucmuatheodvt;
                            $scope.datagrid.data[meal_key][food_index].thucmuatheodvts = food.thucmuatheodvts;
                            $scope.onChange_thucmuatheodvt($scope.datagrid.data[meal_key][food_index], true);
                        } else {
                            $scope.datagrid.data[meal_key][food_index].thucmuatheodvt = food.thucmuatheodvt_change;
                            $scope.datagrid.data[meal_key][food_index].thucmuatheodvts = food.thucmuatheodvt_changes;
                            $scope.onChange_thucmuatheodvt($scope.datagrid.data[meal_key][food_index], true, true);
                        }
                    }
                }
            });
            $scope.totalCalculator();
            dialogClose();
        };
        /*
        * For more than one school point
        * Changed checkbox all for round to total col
        * */
        $scope.roundTotalChanged = () => {
            angular.forEach($scope.roundOption.data, function (foods) {
                angular.forEach(foods, function (food) {
                    food.totalRound = $scope.roundOption.totalRound;
                });
            });
            $scope.roundOptionCalc();
        };
        /*
        * Changed checkbox all for food select
        * */
        $scope.roundSelectChanged = () => {
            angular.forEach($scope.roundOption.data, function (foods) {
                angular.forEach(foods, function (food) {
                    food.selected = $scope.roundOption.selected;
                });
            });
            $scope.roundOptionCalc();
        };
        /*
        * Changed checkbox all for option calculate type
        * */
        $scope.roundOptionChanged = () => {
            angular.forEach($scope.roundOption.data, function (foods) {
                angular.forEach(foods, function (food) {
                    food.option = $scope.roundOption.option;
                });
            });
            $scope.roundOptionCalc();
        };
        $scope.roundOptionCalc = () => {
            angular.forEach($scope.roundOption.data, function(foods){
                angular.forEach(foods, function(food){
                    if ($scope.isPointTogether()) {
                        if (!food.totalRound) {
                            food.thucmuatheodvt_change = 0;
                            food.thucmuatheodvt_changes = {};
                            angular.forEach(food.thucmuatheodvts, function (val, point) {
                                if (val > 0) {
                                    food.thucmuatheodvt_changes[point] = $scope.safeRound(val, food.option);
                                    food.thucmuatheodvt_change = $['+'](food.thucmuatheodvt_change, food.thucmuatheodvt_changes[point]);
                                }
                            });
                        } else {
                            food.thucmuatheodvt_change = $scope.safeRound(food.thucmuatheodvt, food.option);
                        }
                    } else {
                        food.thucmuatheodvt_change = $scope.safeRound(food.thucmuatheodvt, food.option);
                    }
                });
            });
        };
        /*
        * Kiểm tra điều kiện là đơn vị có nhiều điểm trường và lên thực đơn chung
        * */
        $scope.isPointTogether = () => {
            var rs = false;
            if ($scope.school_point) {
                if (count($scope.school_point.points) > 1 && $scope.school_point.together === 1) {
                    rs = true;
                }
            }
            return rs;
        };
        $scope.showAllSchoolPoint = () => {
            var rs = false;
            if ($scope.school_point) {
                if (count($scope.school_point.points) > 1 && $scope.school_point.together === 1 && $scope.sys.configs.showAllSchoolPoint) {
                    rs = true;
                }
            }
            return rs;
        };
        /*
        * Sự kiện thực mua ở điểm trường bị thay đối
        * */
        $scope.onChange_thucmuatheodvts = (food, point, calc) => {
            food.thucmuatheodvt = 0;
            angular.forEach(food.thucmuatheodvts, function (val, point) {
                food.thucmuatheodvt = $['+'](food.thucmuatheodvt, val);
            });
            $scope.onChange_thucmuatheodvt(food, !calc, true);
        };
        /*
        * Tách thực mua theo đơn vị tính ra các điểm trường theo tỉ lệ sĩ số trẻ
        * */
        $scope.divide_pointThucmuatheodvt = (food, group_adjust) => {
            if ($scope.isPointTogether()) {
                food.thucmuatheodvts = {};
                var totalQuantity = +food.thucmuatheodvt;
                var schoolPoints = Object.keys(group_adjust.row.sotres);
                var totalChild = 0;
                var quantityUsed = 0;
                var numberRound = $scope.getNumberRound(totalQuantity);
                var endPoint = 1;
                schoolPoints.forEach(function (point) {
                    totalChild += +group_adjust.row.sotres[point];
                    endPoint = point;
                });
                schoolPoints.forEach(function (point) {
                    var childPoint = group_adjust.row.sotres[point];
                    if (childPoint == 0 || totalQuantity == 0) {
                        food.thucmuatheodvts[point] = 0;
                        return;
                    }
                    if (point === endPoint) {
                        food.thucmuatheodvts[point] = round(totalQuantity - quantityUsed, numberRound);
                    } else {
                        food.thucmuatheodvts[point] = round(totalQuantity * childPoint / totalChild, numberRound);
                        quantityUsed += food.thucmuatheodvts[point];
                    }
                });
            }
        };

        $scope.getNumberRound = function (number) {
            if ($scope.sys.configs.cdkp_round_quantity_by_point) {
                return $scope.sys.configs.cdkp_round_quantity_by_point;
            }
            var decimalSplit = (number + '').split('.');
            var result = 1;
            if (decimalSplit[1]) {
                result = decimalSplit[1].length + 1;
            }
            return result;
        };

        /*
        * Sửa tên đi chợ
        * */
        $scope.onChange_food_name = (food) => {
            for (var f of food.foods) {
                f.name_old = f.name;
                f.name = food.name;
            }
        };
        /*  Khi kích vào select chọn kho */
        /*  Việc chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/
        $scope.warehouseSelectedOnClick = (meal) => {
            meal.selected = !meal.selected;
            var check_not_null = true;
            angular.forEach($scope.row.meal_selection, function(item, index){
                if(item.selected){
                    check_not_null = false;
                }
            });
            if(check_not_null){
                meal.selected = true;
            }else{
                $scope.totalCalculator();
            }
        };
        $scope.fn_copyForm = (points) => {
            $scope.copy = {
                list: points,
                selected: {}
            };
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'',
                title:'Sao chép thực đơn',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.formCopy
            });
        };
        $scope.fn_selectAdjustPoint = () => {
            $scope.loadDataOfDay(null, $scope.copy.selected);
        }
        $scope.initGrid = (month) => {
            var urls = [$CFG.remote.base_url,'doing',$CFG.project,$scope.control,'list'];
            $.dm_datagrid.init(
                urls.join('/'),
                $scope.control, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
                '', /*Tiêu đề cho bảng dữ liệu*/
                [
                    [
                        { field:'ck', checkbox: true },
                        { title:'Ngày', field:'date', width: 90},
                        { title:'Nhóm trẻ', field:'group_names', width:250},
                        { title:'Tên thực đơn theo nhóm trẻ', field:'menu_planning_names', width: 250 },
                        { title:'Số trẻ', field:'set', width: 70 },
                        { title:'Tiền ăn', field:'money', width: 120, formatter: function(value, row) {
                            var html = '<span>'+value+'</span>';
                            if(row.stta) {
                                html += '<i class="fa fa-lock clrGreen mL5" style="font-size: 17px" title="Đã tạo sổ tính tiền ăn cho CĐKP này!"></i>';
                            }
                            return html;
                        } },
                        { title:'Sửa CĐKP', field:'school_point', width: 70, formatter: function (value, row) {
                                return '<div id="datagrid-view-' + [row.day, row.month, row.schoolyear].join('-') + '"></div>';
                            }},
                        { title:'Tạo CĐKP lúc', field:'created_at', width: 90 , formatter: function (value) {
                                var dateTime = new Date(value);
                                return moment(dateTime).format('DD/MM/YYYY, HH:mm:ss');
                            }},
                        { title:'Cập nhật lúc', field:'updated_at', width: 90 , formatter: function (value) {
                                var dateTime = new Date(value);
                                return moment(dateTime).format('DD/MM/YYYY, HH:mm:ss');
                            }},
						{ title:'TPC', field:'edit_tpc_cdkp', width: 40, formatter: function (value, row) {
                                return '<div style="text-align:center;" title="Xem hoặc tích/bỏ tích TPC khi đã có STTA" id="datagrid-tpc-cdkp-' + [row.day, row.month, row.schoolyear].join('-') + '"></div>';
                            }},
                        /*{ title:'<span style="cursor:pointer;" onclick="editPKC()" title="Click vào đây để mở chức năng sửa PKC">Sửa PKC</span>', field:'marketbill_unit_id', width:70,hidden: $CFG.administrator == 0  , formatter: function(value, row){
                                return '<div class="datagrid-marketbill-editable" id="datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-') + '" style="display:none;"></div>';
                            }},*/
                        { title:'Chia T/A chín', field:'so_chia_tachin', width: 80, formatter: function (value, row) {
                            return '<div style="text-align:center;" title="Mở sổ chia thức ăn chín" id="datagrid-so-chia-tachin-' + [row.day, row.month, row.schoolyear].join('-') + '" style="display:none;"></div>';
                        }},
                    ]
                ],
                {
                    onDblClickRow: function(rowIndex, rowData) {
                        $scope.selected.date = rowData.date;
                        $scope.addjustForm($scope.selected.date);
                    }, onSelect: function(index, row) {
                        $scope.$apply(function ($scope) {
                            $scope.selected.date = row.date;
                        });
                    }, onUnselect: function (index,row) {
                        $scope.$apply(function ($scope) {
                            $scope.selected.date = $.menu_adjust.date;
                        });
                    }, onLoadSuccess: function(data) {
                        setTimeout(function () {
                            angular.forEach(data.rows, function (row, ind) {
                                var btn = '<span class="glyphicon glyphicon glyphicon-pencil btn-over-green" title="Mở thực đơn" style="height: 14px; margin: 0 3px;" ng-click="addjustForm(\'' + row.date + '\')">Sửa</span>';
                                $('div#datagrid-view-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
								if($scope.sys.configs.cdkp_edit_tpc_exist_stta == 1){
									btn = '<span class="fa fa-eye mcur ng-scope" title="Chỉ tích/bỏ tích TPC" style="height: 14px; margin: 0 3px;" ng-click="addjustForm(\'' + row.date + '\', 1)"></span>';
									$('div#datagrid-tpc-cdkp-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
								}
                                //btn = '<span class="glyphicon glyphicon-eye-open btn-over-green" title="Xem/Sửa phiếu kê chợ" style="height: 14px; margin: 0 3px;" ng-click="marketbillForm(\'' + row.date + '\')"><span style="font-size:11px; font-family:sans-serif !important;">Sửa PKC</span></span>';
                                //$('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                                if ($scope.isPointTogether() && !$scope.sys.configs.phieukecho_temple_point_old) {
                                    btn = '<a href="' +
                                        $CFG.remote.base_url+'/report/' + $CFG.project+'/marketbill/pointsTogether?date=' + row.date +
                                        '&type=1"" target="_blank"><span class="glyphicon glyphicon-print btn-over-green" title="Hiển thị phiếu kê chợ" style="height: 14px; margin: 0 3px;">In</span></a>';
                                    $('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                                }
                                if ($scope.sys.configs.cdkp_show_scta_chin) {
                                    btn = '<a href="' +
                                        $CFG.remote.base_url+'/' + $CFG.project+'/course_attendance/exportChiaBuaTheoNgay?date=' + row.date+
                                        '" target="_blank"><span class="glyphicon glyphicon-print btn-over-green" title="Sổ chia thức ăn chín" style="height: 14px; margin: 0 3px;">Xem</span></a>';
                                        $('div#datagrid-so-chia-tachin-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                                }
                            });
							if($scope.sys.configs.cdkp_edit_tpc_exist_stta == 1){
								$('#tbl_menu_adjust').datagrid('showColumn','edit_tpc_cdkp');
							}else{
								$('#tbl_menu_adjust').datagrid('hideColumn','edit_tpc_cdkp');
							}
                            if($scope.sys.configs.cdkp_show_scta_chin == 1){
                                $('#tbl_menu_adjust').datagrid('showColumn','so_chia_tachin');
                            }else{
                                $('#tbl_menu_adjust').datagrid('hideColumn','so_chia_tachin');
                            }
                            if($scope.sys.configs.cdkp_hide_created_at === 1){
                                $('#tbl_menu_adjust').datagrid('hideColumn','created_at');
                                $('#tbl_menu_adjust').datagrid('hideColumn','updated_at');
                            }else{
                                $('#tbl_menu_adjust').datagrid('showColumn','created_at');
                                $('#tbl_menu_adjust').datagrid('showColumn','updated_at');
                            }
                        });
                    },
                    queryParams: {month: month},
                    pageList: [31],
                    pageSize: 31
                }
            );
        };

        /*
        * Mở pupup danh sách biểu in liên quan tới phiếu kê chợ; sổ kiểm thực 3 bước
        * */
        $scope.marketbillPrintForm = () => {
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'change_dish',
                title:'Sửa món ăn',
                size: 400,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.project + '/marketbill/list-print-preview.html',
                onShown: function(element, dialogRef){

                }
            });
        };
        /*Tổng tiền từng nhóm trẻ*/
        $scope.sumPayForGroup = (foods, group_id) => {
            var rs = 0;
            angular.forEach(foods, function (food, food_id) {
                rs += $scope.getTongTien1Nhom(food.groups[group_id]);
            });
            return rs;
        };
        /*Tổng tiền tất cả các nhóm*/
        $scope.sumPayForAll = (foods) => {
            var rs = 0;
            angular.forEach(foods, function (food, food_id) {
                rs += $scope.getTongTien(food);
            });
            return rs;
        };
        /*Hiển thị form xem thông tin hoặc thay đổi món ăn khác*/
        $scope.showChangeDish = (meal, dish_old, only_tpc) => {
            $scope.selected.dish = dish_old;
            $scope.selected.meal = meal;
            var self = $.menu_adjust;
            $scope.foodAddeds = {};
            angular.forEach(dish_old.ingredient, function (food, index) {
                if (!food.quantity) {
                    food.quantity = food.quantity_edit;
                }
                food.deleted = false;
            });
			var dishTitle = 'Sửa món ăn';
			var dishTemplate = $scope.controller.templates.changeDish;
			if(only_tpc==1) {
				dishTitle = 'Sửa món ăn: Thực phẩm chính (TPC)';
				dishTemplate = $scope.controller.templates.changeDishTPC;
			}
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'change_dish',
                title:dishTitle,
                size: size.wide,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: dishTemplate,
                onShown: function(element, dialogRef){

                }
            });
        };
        $scope.showChangeMeal = (meal, dish_old) => {
            var self = $.menu_adjust;
            $scope.selected.dish = dish_old;
            $scope.selected.meal = meal;
            $scope.selected.warehouse_id = meal.warehouse_id;
            angular.forEach(dish_old.ingredient, function (food, index) {
                if (!food.quantity) {
                    food.quantity = food.quantity_edit;
                }
                food.deleted = false;
            });
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'',
                title:'Chuyển bữa cho món ăn',
                size: size.small,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.project + '/' + $scope.control + '/change_meal_confirm.html?_v=2',
                onShown: function(element, dialogRef){
                    $scope.selected.meal_define_old = meal.define;
                    $scope.selected.meal_define = meal.define;
                }
            });
        };
        $scope.acceptChangeMeal = function() {
            if($scope.isChangeMeal && $scope.selected.meal_define != $scope.selected.meal_define_old) {
                delete $scope.selected.group_adjust.meals[$scope.selected.meal.define].dishes[$scope.selected.dish.id];
                var selected_dishes = $scope.selected.dish;
                angular.forEach(selected_dishes.ingredient, (food, food_id)=>{
                    food.meal_define = $scope.selected.meal_define;
                    selected_dishes.ingredient[food_id] = food;
                    if ($scope.selected.meal_define_old=='buasang' && $scope.selected.meal_define!='buasang') {
                        $scope.selected.group_adjust.data[1] = $scope.selected.group_adjust.data[1] || {};
                        if($scope.selected.group_adjust.data[1][food_id]!=undefined && $scope.selected.group_adjust.data[2][food_id]==undefined) {
                            $scope.selected.group_adjust.data[2][food_id] = $scope.selected.group_adjust.data[1][food_id];
                            delete $scope.selected.group_adjust.data[1][food_id];
                        }
                    }else if($scope.selected.meal_define_old!='buasang' && $scope.selected.meal_define=='buasang') {
                        $scope.selected.group_adjust.data[2] = $scope.selected.group_adjust.data[2] || {};
                        if($scope.selected.group_adjust.data[2][food_id]!=undefined && $scope.selected.group_adjust.data[1][food_id]==undefined) {
                            $scope.selected.group_adjust.data[1][food_id] = $scope.selected.group_adjust.data[2][food_id];
                            delete $scope.selected.group_adjust.data[2][food_id];
                        }
                    }
                });
                $scope.selected.group_adjust.meals[$scope.selected.meal_define].dishes = Object.assign({},$scope.selected.group_adjust.meals[$scope.selected.meal_define].dishes);
                $scope.selected.group_adjust.meals[$scope.selected.meal_define].dishes[$scope.selected.dish.id] = selected_dishes;
                $scope.selected.meal = $scope.selected.group_adjust.meals[$scope.selected.meal_define];
            }
            dialogClose();
        }
        $scope.changeFoodForm = (food)=>{
            $scope.selected_food = {
                food_old: food
            };
            $.dm_datagrid.showAddForm({
                title:'Chọn thực phẩm',
                size: size.small,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.project + '/' + $scope.control + '/change_food.html?_=',
                onShown: function(element, dialogRef){

                }
            });
        };
        $scope.foodDetail = (food) => {
            $scope.selected.food = food;
            var self = $.menu_adjust;
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'food_detail',
                title:'Thông tin thực phẩm',
                size: size.wide,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.foodDetail,
                onShown: function(element, dialogRef){

                }
            });
        };
        $scope.showFormTotal = () => {
            $scope.selected.food = undefined;

            $.dm_datagrid.showEditForm({
                module: $CFG.project+'/menu_adjust',
                action:'',
                title:'Cân đối khẩu phần',
                scope: $scope,
                size: size.wide,
                showButton: false,
                fullScreen: true,
                askBeforeClose: false,
                content: $scope.project+'/'+$scope.control+'/total.html',
                onShown: function (dialog) {
                    $scope.processData($scope.menu_adjust.group_adjusts);
                }
            });
        };

        $scope.closeFormTotal = () => {
            if($scope.dialog) {
                $scope.dialog.close();
            }else{
                dialogClose();
            }
        };
        $scope.onSelectedFoodChange = (food)=>{
            if(food) {
                var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
                var data = {async: true,id: food.id, date: $scope.selected.date};
                process(url, data, function(resp) {
                    if(!resp) return;
                    $scope.selected_food.food_new = resp[0];
                    $scope.$apply();
                }, function(){}, false);
            }
        };
        $scope.acceptChangeFood = ()=>{
            if ($scope.selected_food.food_old && $scope.selected_food.food_new){
                var food = $scope.selected_food.food_new;
                var old_food_id = $scope.selected_food.food_old.food_id;
                angular.forEach($scope.menu_adjust.group_adjusts, (group)=>{

                    angular.forEach(group.menu_plannings[0].data, (foods, wh)=>{
                        if (!foods[food.food_id] && foods[old_food_id]){
                            var f = foods[old_food_id];
                            f.id = food.id;
                            f.food_id = food.id;
                            f.name = food.name;
                            f.nutritions = food.nutritions;
                            angular.forEach(f.foods, (fd)=>{
                                fd.id = food.id;
                                fd.food_id = food.id;
                                fd.name = food.name;
                                fd.nutritions = food.nutritions;
                            });
                        }
                        var data = {};
                        angular.forEach(foods, (fd)=>{
                            data[fd.food_id] = fd;
                        });
                        group.menu_plannings[0].data[wh] = data;
                    });

                    angular.forEach(group.menu_plannings[0].meals, (meal)=>{
                        angular.forEach(meal.dishes, (dish)=>{
                            var fds = {};
                            angular.forEach(dish.ingredient, (fd, food_id)=>{
                                fds[fd.food_id] = fd;
                            });
                            dish.ingredient = fds;
                        });
                    });
                    $scope.totalCalculator(group.menu_plannings[0].data, true);
                });
                scope.inventoriesDivision();
                dialogClose();
            }
        };
        /*Chọn thực phẩm để thêm thành món*/
        $scope.onSelectAddFood = (food_select) => {
            if(food_select) {
                var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
                var data = {async: true,id: food_select.id, date: $scope.selected.date};
                process(url, data, function(resp) {
                    if(!resp) return;
                    var food = resp[0];
                    $scope.$apply(function(){
                        food.dish_name = food.name.replace('Gạo','Cơm').replace('gạo','cơm');
                        food.quantity = 10;
                        if($scope.inventory[$scope.selected.meal.warehouse_id]){
                            if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id]){
                                if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].value>0) {
                                    food.price = $scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].price;
                                }
                            }
                        }
                        $scope.selected.food_add = food;
                    });
                },function(){}, false);
            }
        };
        /*Tạo thực phẩm thành món mới theo tên thực phẩm*/
        $scope.appendFoodToMeal = (food, meal) => {
            var dish = {
                id: 'food_id_'+food.food_id,
                name: food.dish_name,
                tcp: food.tcp,
                ingredient: {},
                addedAt: (new Date).getTime(),
            }
            food.quantity || (food.quantity = 0);
            food.quantity = Number(food.quantity);
            dish.ingredient[food.food_id] = food;
            if(!meal.dishes[dish.id]){
                $scope.addFoodFromDish(meal,dish);
                $scope.datagrid.data = {};
                angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
                    angular.forEach(meal_.dishes, function(dish_,ind){
                        $scope.addFoodFromDish(meal_,dish_,true);
                    })
                });
                $scope.selected.group_adjust.data = $scope.datagrid.data;
                angular.forEach($scope.datagrid.data, function(foods,warehouse_id){
                    angular.forEach(foods, function(food,food_id){
                        $scope.onChange_luong1tre(food);
                    });
                });
                $scope.totalCalculator();
            }else{

            }
            $scope.selected.food_tmp = undefined;
            $scope.selected.food_add = undefined;
        }
        /*  Mở form thêm thực phẩm */
        $scope.showAddFood = (meal) => {
            $scope.selected.meal = meal;
            $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+self.module,
                action:'add_food',
                title:'Thêm mới',
                size: size.small,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.addFood,
                onShown: function(element, dialogRef){

                }
            });
        };
        $scope.foodSelected = (food_selected) => {
            /*Chua bit làm gi*/
        }
        /*Mở form nhập số trẻ từng điểm trường*/
        $scope.formEditSotreOfPoints = () => {
            $scope.tmp || ($scope.tmp = {});
            $scope.tmp.thucmuaCalc = true;
            $scope.tmp.sotres = {};
            angular.forEach($scope.row.sotres, function (val, point) {
                $scope.tmp.sotres[point] = val;
            });
            $.dm_datagrid.showEditForm(
                {
                    module: $CFG.project+'/menu_adjust',
                    action:'edit',
                    title:'Chỉnh sửa',
                    size: 450,
                    showButton: false,
                    draggable: true,
                    scope: $scope,
                    content: $scope.controller.templates.edit_number_children,
                    onShown: function(element) {
                    }
                }
            );
        };
        /*
        * Cập nhật sự thay đổi số trẻ từng điểm trường
        * */
        $scope.applySotres = () => {
            $scope.row.sotre_old = $scope.row.sotre;
            $scope.row.sotre = 0;
            angular.forEach($scope.tmp.sotres, function (val, point) {
                $scope.row.sotre = $['+']($scope.row.sotre, val);
                val = +val;
                $scope.row.sotres[point] = val;
            });
            // console.log($scope.tmp.thucmuaCalc, typeof $scope.tmp.thucmuaCalc, $scope.row.sotres, $scope.tmp.sotres);
            $scope.onChangeSotre($scope.row, $scope.tmp.thucmuaCalc);
        }
        $scope.calculateColspan = function() {
            var tmp = count($scope.school_point.points)+1;
            angular.forEach($scope.row.sotres, function(v,k) {
                if(v == 0) tmp--;
            });
            return tmp;
        }
        /*
        * Kiểm tra số trẻ từng điểm trường đã bị thay đổi chưa
        * */
        $scope.modifiedSotres = () => {
            var rs = false;
            angular.forEach($scope.tmp.sotres, function (val, point) {
                if (val != $scope.row.sotres[point]) {
                    rs = true;
                }
            });
            return rs;
        }
        /*Mở form nhập tiền bổ trợ từng điểm trường*/
        $scope.formEditTienbotroOfPoints = () => {
            $.dm_datagrid.showEditForm(
                {
                    module: $CFG.project+'/menu_adjust',
                    action:'edit',
                    title:'Chỉnh sửa',
                    size: size.small,
                    showButton: false,
                    scope: $scope,
                    content: $scope.controller.templates.edit_tien_bo_tro,
                    onShown: function(element) {
                    }
                }
            );
        };
        /*Mở form cân đối tự động*/
        $scope.balanceShow = () => {
            var self = $.menu_adjust;
            $.dm_datagrid.showAddForm({
                    module: $CFG.project+'/'+ $scope.control,
                    action:'balance',
                    title:'Cân đối',
                    size: size.wide,
                    fullScreen: true,
                    showButton: false,
                    scope: $scope,
                    content: $scope.controller.templates.balance,
                    onShown: function(element,dialogRef){
                        setTimeout(function () {
                            $scope.$apply(function () {
                                $.balance.init($scope);
                            });
                        });
                    }
                },
                function(resp){
                    if(typeof callback === 'function') {
                        callback(resp);
                    }else{
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                }
            );
        };
        /*Chọn thực phẩm vào món*/
        $scope.addFoodToTempDish = (food_selected) => {
            if(food_selected) {
                var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
                var data = {async: true,id: food_selected.id};
                process(url, data, function(resp) {
                    if(!resp) return;
                    var food = resp[0];
                    $scope.$apply(function(){
                        var id = food.food_id;
                        // food.quantity = 0;
                        // $scope.menu_adjust.food = undefined;
                        food.quantity = 10;
                        $scope.foodAddeds[id] = food;
                        // scope.selected.dish.ingredient[id] = food;
                        // $('#food').combobox('clear');
                        // $scope.menu_adjust.dish_selected = null;
                        $scope.selected.foodAdded = undefined;
                    });
                },function(){}, false);
            }
        };
        /*  Mở form thêm món ăn */
        $scope.showAddDish = (meal) => {
            var meals = $scope.selected.group_adjust.meals;
            var group_id = $scope.selected.group_adjust.group_id;
            $scope.foodAddeds = {};
            $scope.selected.dish_tmp = undefined;
            $scope.selected.dish = undefined;
            $scope.selected.meal = meal;
            $.dm_datagrid.showAddForm(
                {
                    module: $CFG.project+'/menu_adjust',
                    action:'add_dish',
                    title:'Thêm mới',
                    size: size.wide,
                    fullScreen: false,
                    showButton: false,
                    scope: $scope,
                    content: $scope.controller.templates.addDish,
                    onShown: function(element, dialogRef) {
                        $scope.selected.dish = {};
                        var arr_meals = Object.values(meals);
                        $.dm_datagrid.combobox('meal', arr_meals,{
                            valueField: 'define',
                            textField: 'name',
                            value: meal.define,
                            height: 24,
                            panelHeight: 'auto',
                            mode: 'local',
                            onSelect: function(meal, element) {
                                $.menu_adjust.combobox_load_dish($scope, meal, group_id);
                            },
                            queryParams: {},
                            width: 200
                        });
                    }
                },
                function(resp){
                    if (typeof callback === 'function') {
                        callback(resp);
                    } else {
                        $("#tbl_" + self.module).datagrid('reload');
                    }
                }
            );
        };
        /*Cập nhật lại món sau khi sửa vào thực đơn*/
        $scope.changeDishApply = (meal, dish_old) => {
            dish_old || (dish_old = $scope.selected.dish);
            dish_old.ingredient = Object.assign(dish_old.ingredient, $scope.foodAddeds);
            var dish_new = dish_old;
            /*Xóa món ăn cũ đi*/
            /*Xóa thực phẩm đã bị xóa trong món ăn*/
            var tmp_foods = {};
            angular.forEach(dish_new.ingredient, function(food, food_id){
                if(food && !food.deleted) {
                    if (typeof food.quantity_edit ==='undefined') {
                        food.quantity_edit = Number(food.quantity);
                    }
                    tmp_foods[food.food_id] = clone(food);
                }
            });
            dish_new.ingredient = tmp_foods;
			/* Cap nhat lai addedAt cho mon an? */
			if($scope.sys.configs.cdkp_re_sorting_dish) {
				dish_new.addedAt = (new Date()).getTime();
			}
            /*Thêm món ăn mới vào*/
            meal || (meal = $scope.selected.meal);
            meal.dishes[dish_new.id] = dish_new;
            /*
            * Chỗ này dữ liệu cũ để dish.id + ' ' nên bị lặp món -> cập nhật lại món bị nhảy gấp đôi lượng
            * Chuẩn hóa lại ds món teo id gốc;
            * */
            var dishes = {};
            angular.forEach(meal.dishes, function (dish, dish_id) {
                dishes[dish.id] = dish;
            });
            meal.dishes = dishes;
            $scope.datagrid.data = {};
            angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
                angular.forEach(meal_.dishes, function(dish_,ind){
                    $scope.addFoodFromDish(meal_, dish_, true);
                })
            });
            $scope.selected.group_adjust.data = $scope.datagrid.data;
            dialogClose();
            $scope.totalCalculator();
        };
        $scope.emptyThucmuatheodvt = ()=>{
            $.messager.confirm('Cảnh báo', 'Thao tác này sẽ gán tất cả lượng của thực phẩm về 0',(r)=>{
                if (r){
                    angular.forEach($scope.datagrid.data, (foods)=>{
                        angular.forEach(foods, (food)=>{
                            food.thucmuatheodvt = 0;
                            $scope.onChange_thucmuatheodvt(food);
                            setTimeout(()=>{
                                $scope.$apply();
                            });
                        });
                    });
                }
                console.log(r, a)
            });
        };
        $scope.addMealForm = function() {
            $.dm_datagrid.showAddForm(
                {
                    module: $CFG.project + '/dish/',
                    action: 'add',
                    title: 'Thêm mới món ăn',
                    showButton: false,
                    fullScreen: true,
                    size: size.wide,
                    content: function (element) {
                        loadForm($CFG.project + '/dish','add', {}, function (resp) {
                            var form = '<div >'+resp+'</div>';
                            angular.element($('#menu_adjustController')).scope().$apply(function(scope){
                                $(element).html(scope.compile(form,scope));
                                scope.dish = {
                                    foods: {},
                                    row: {},
                                    selected: {},
                                };
                                scope.cache || (scope.cache = {});
                                scope.cache.foods = undefined;
                                scope.dish.selected.food_ids = [];
                                scope.dish.number_child = 100;
                                scope.dish.number_childs = {};
                                process('dinhduong/dish/getCat','',function(resp){
                                    scope.dish.groups = resp.groups;
                                    scope.dish.categories = resp.categories;
                                    scope.dish.areas = resp.areas;
                                    angular.forEach(scope.dish.groups, function (index, value) {
                                        scope.dish.number_childs[value.id] = 100;
                                    });
                                    scope.dish.group_radio = scope.dish.groups[Object.keys(scope.dish.groups)[0]].id;
                                },false,false);
                                scope.dish.checkGroupSelected = function(){
                                    var tam = 0;
                                    angular.forEach(scope.dish.groups,function(group,index){
                                        if(group.selected == true){
                                            tam ++;
                                        }
                                    });
                                    return tam;
                                };
                                scope.dish.onSelectFood = function(food) {
                                    if(!scope.dish.foods[food.id]) {
                                        var url = $CFG.remote.base_url+'/doing/dinhduong/dish/getFoodDetailById';
                                        var data = {async: true,id: food.id};
                                        process(url, data, function(resp){
                                            if(!resp) return;
                                            scope.$apply(function(){
                                                scope.dish.selected.food_ids = [];
                                                angular.forEach(scope.dish.foods, function(item, food_id){
                                                    scope.dish.selected.food_ids.push(food_id);
                                                });
                                                angular.forEach(resp, function(item, ind){
                                                    scope.dish.selected.food_ids.push(item.food_id);
                                                    scope.dish.foods[item.food_id] = item;
                                                })
                                            });
                                        },function(){}, false);
                                    }else{
                                        scope.dish.selected.food_ids = [];
                                        angular.forEach(scope.dish.foods, function(item, food_id){
                                            scope.dish.selected.food_ids.push(food_id);
                                        });
                                        angular.forEach(resp, function(item, ind){
                                            scope.dish.selected.food_ids.push(item.food_id);
                                            scope.dish.foods[item.food_id] = item;
                                        })
                                    }
                                };
                                scope.dish.onChangeRadio = function(group_id){
                                    console.log(scope.dish.foods);
                                    angular.forEach(scope.dish.foods, function(food, food_id){
                                        food.quantity = food.quantities[group_id];
                                        food.eat_a_group = scope.rounds(food.quantities[group_id]*scope.dish.number_child/1000);
                                        food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                                        food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                                        food.quantity_for_measure = scope.rounds(food.quantities[group_id]/food.gam_exchange);
                                        food.calo_for_one = scope.sumCaloFood(food);
                                    });
                                    scope.dish.number_childs || (scope.dish.number_childs = {});
                                    if (scope.dish.number_childs[group_id]) {
                                        scope.dish.number_child = scope.dish.number_childs[group_id];
                                    } else {
                                        scope.dish.number_child = 100;
                                        scope.dish.number_childs[group_id] = 100;
                                    }
                                };
                                scope.sumCaloFood = function(food,key) {
                                    key || (key = 'quantity');
                                    var rs = $['*'](scope.co_cau(null).protein,food[key])*(food.nutritions.protein/100)
                                        + $['*'](scope.co_cau(null).fat,food[key])*(food.nutritions.fat/100)
                                        + $['*'](scope.co_cau(null).sugar,food[key])*(food.nutritions.sugar/100);
                                    return round(rs,2);
                                };
                                scope.dish.delAllFood = function(){
                                    scope.dish.foods = {};
                                };
                                scope.dish.delFood = function(id){
                                    var tam = {};
                                    var food_ids = [];
                                    scope.dish.selected.food_ids =[];
                                    $.each(scope.dish.foods,function(food_id,food){
                                        if(food_id != id){
                                            tam[food_id] = food; 
                                            food_ids.push(food_id);
                                        }
                                    });
                                    scope.dish.selected.food_ids = food_ids;
                                    scope.dish.foods = tam;
                                };
                                scope.dish.onChangeOneG = function(item,group_id){
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        if(item.tam == false){
                                            angular.forEach(scope.dish.groups,function(group,index){
                                                if(group.selected == true){
                                                    item.quantities[group.id] = item.quantity;
                                                }
                                            })
                                        }
                                        item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                        item.calo_for_one = scope.sumCaloFood(item);
                
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeOneGArr = function(item,group_id,foods){
                                    scope.dish.group_radio = group_id;
                                    if(!item.tam){
                                        angular.forEach(item.quantities, function(value, grp_id){
                                            if(group_id != grp_id){
                                                item.quantities[grp_id] = item.quantities[scope.dish.group_radio];
                                            }
                                        })
                                    }
                                    if(scope.dish.group_radio == undefined){
                                        item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                        item.calo_for_one = scope.sumCaloFood(item);
                                    }else if(foods){
                                        angular.forEach(foods, function(food, food_id){
                                            food.quantity = food.quantities[scope.dish.group_radio];
                                            food.eat_a_group = scope.rounds(food.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                            food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                                            food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                                            food.quantity_for_measure = scope.rounds(food.quantities[scope.dish.group_radio]/food.gam_exchange);
                                            food.calo_for_one = scope.sumCaloFood(food);
                                        })
                                    }else{
                                        item.quantity = item.quantities[scope.dish.group_radio];
                                        item.eat_a_group = scope.rounds(item.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                        item.quantity_for_measure = scope.rounds(item.quantities[scope.dish.group_radio]/item.gam_exchange);
                                        item.calo_for_one = scope.sumCaloFood(item);
                                    }
                                };
                                scope.dish.onChangeEAG = function(item){
                                    var group_id = scope.dish.group_radio;
                                    var check_group = scope.dish.checkGroupSelected();
                                     
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        if(check_group != 0 && group_id != undefined){
                                            if(item.quantities == undefined){
                                                item.quantities = [];
                                            }
                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                            item.quantity = item.quantities[group_id]
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }else{
                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeBAG = function(item){
                                    var group_id = scope.dish.group_radio;
                                    var check_group = scope.dish.checkGroupSelected();
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        if(check_group !=0 && group_id != undefined){
                                            if(item.quantities == undefined){
                                                item.quantities = [];
                                            }
                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                            item.quantity = item.quantities[group_id];
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }else{
                                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) * 100;
                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }
                
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeBAD = function(item){
                                    var group_id = scope.dish.group_radio;
                                    var check_group = scope.dish.checkGroupSelected();
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        if(check_group !=0 && group_id != undefined){
                                            if(item.quantities == undefined){
                                                item.quantities = [];
                                            }
                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                            item.quantity = item.quantities[group_id]
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }else{
                                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) + 100;
                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }
                
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeOneDVT = function(item){
                                    var group_id = scope.dish.group_radio;
                                    var check_group = scope.dish.checkGroupSelected();
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        if(check_group !=0 && group_id != undefined){
                                            if(item.quantities == undefined){
                                                item.quantities = [];
                                            }
                                            item.quantities[group_id] = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.quantity = item.quantities[group_id];
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }else{
                                            item.quantity = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                                            item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                            item.calo_for_one = scope.sumCaloFood(item);
                                        }
                
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeEF = function(item){
                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                        item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                        item.calo_for_one = scope.sumCaloFood(item);
                                    }else{
                                        alert("Vui lòng nhập số lượng trẻ");
                                    }
                                };
                                scope.dish.onChangeNC = function(){
                                    angular.forEach(scope.dish.foods,function(food,index){
                                        scope.dish.onChangeOneG(food);
                                    });
                                    if (scope.dish.group_radio) {
                                        scope.dish.number_childs || (scope.dish.number_childs = {});
                                        scope.dish.number_childs[scope.dish.group_radio] = scope.dish.number_child;
                                    }else{
                                        for (var i in scope.dish.number_childs) {
                                            scope.dish.number_childs[i] = scope.dish.number_child;
                                        }
                                    }
                                };
                                scope.dish.quantityFocus = function(food,group_id){
                                    food.tam = false;
                                    food.quantities || (food.quantities = {})
                                    angular.forEach(scope.dish.groups,function(group,index){
                                        if(group.selected == true){
                                            if(food.quantities[group.id]){
                                                food.tam = true;
                                            }else{
                                                food.quantities[group.id] = 0;
                                            }
                                        }
                                    });
                                    scope.dish.onChangeOneGArr(food,group_id,scope.dish.foods);
                                };
                                scope.rounds = function(value) {
                                    if(typeof value != 'number') {
                                        value = parseFloat(value);
                                    }
                                    value = Math.round(value*1000)/1000;
                                    return value;
                                };
                                scope.dish.uploadDone = function(image){
                                    if(image){
                                        $('.image-last').show();
                                        $('.image-first').hide();
                                        if(count(image.errors)>0){
                                            var html = [];
                                            angular.forEach(image.errors, function(value,index){
                                                html.push(value);
                                            });
                                            $.dm_datagrid.show({title: 'Thông báo', message: html.join('<br/>')});
                                        }
                                    }
                                };
                                scope.dish.sum_calo = function(){
                                    var group_id = scope.dish.group_radio;
                                    var check_group = scope.dish.checkGroupSelected();
                                    // console.log(group_id,check_group);
                                    var sum_calo = 0;
                                    angular.forEach(scope.dish.foods,function(food,idex){
                                        if(food.nutritions.calo == undefined){
                                            food.nutritions.calo = 0;
                                        }
                                        food.quantities || (food.quantities={});
                                        food.quantity || (food.quantity=0);
                                        food.quantities[group_id] || (food.quantities[group_id] = 0);
                                        sum_calo += scope.sumCaloFood(food);
                                    });
                                    return round(sum_calo,2);
                                };
                                scope.keyUpFood = function(e,food_id,name,group_id){
                                    var input = '';
                                    newfoods = [];
                                    index = 0;
                                    angular.forEach(scope.dish.foods,function(food,f_id){
                                        newfoods[index] = food;
                                        index ++;
                                    })
                                    angular.forEach(newfoods,function(newfood,index){
                                        if(newfood.food_id == food_id){
                                            if(e.which == 13 || e.which == 40){
                                                if(index < count(newfoods)-1){
                                                    index = index+1;
                                                    input = [name,newfoods[index].food_id].join('_');
                                                }
                                            }else if(e.which == 38){
                                                if(index>0){
                                                    index = index-1;
                                                    input = [name,newfoods[index].food_id].join('_');
                                                }
                                            }
                                        }
                                    })
                                    if(input != ''){
                                        $('input#'+input).focus();
                                    }
                                };
                            });
                        })
                    },
                    buttons: [{
                        id: 'btn-save',
                        icon: 'glyphicon glyphicon-floppy-disk',
                        label: 'Lưu',
                        cssClass: 'btn-primary',
                        action: function (dialogRef) {
                            var data = {};
                            angular.forEach(scope.dish.foods,function(food,index){
                                food['number_child'] = scope.dish.number_child;
                                food['sum_calo'] = scope.dish.sum_calo();
                            });
                            data.foods = JSON.stringify(scope.dish.foods);
                            data.number_childs = scope.dish.number_childs;
                            let formData = getSubmitForm('frm-dish-add', true);
                            let area = scope.dish.areas.filter(item => item.selected == true).map(item => item.id).toString();
                            let category = scope.dish.categories.filter(item => item.selected == true).map(item => item.id).toString();
                            let group = Object.values(scope.dish.groups).filter(item => item.selected == true).map(item => item.id).toString();
                            formData.push({id: 'area', value: area})
                            formData.push({id: 'category', value: category})
                            formData.push({id: 'group', value: group})

                            data.together = arrayToJson(formData)
                            var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/dish/add';
                            process(url, data, function (resp) {
                                if (resp.result == "success") {
                                    alert("Bạn đã thêm món ăn thành công");
                                    dialogRef.close();
                                    scope.reloadDishes();
                                }
                            });
                        }
                    }]
                },
                
                function(resp){
                    if(typeof callback === 'function') {
                        callback(resp);
                    }else{
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                }
            );
        }
        $scope.reloadDishes = function() {
            $('#dish').combobox('clear');
            $('#dish').combobox('reload', $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/dishs'+($scope.from_meal_share?'?meal_share=1':''));
            $scope.selected.dish = undefined;
        }
        $scope.addDishApply = () => {
            if(count($scope.selected.dish) == 0) return;
            if($scope.copy_meal && $scope.from_meal_share) {
                process('dinhduong/dish_storage/share', {dish_info: JSON.stringify($scope.selected.dish)}, function () {
                }, false);
            }
            var dish = Object.assign({}, $scope.selected.dish, {addedAt: (new Date()).getTime()});
            var meal_define = $scope.selected.meal.define;
            dish.ingredient = Object.assign(dish.ingredient, $scope.foodAddeds);
            /*Cập nhật món mới vào thực đơn*/
            $scope.addFoodFromDish($scope.selected.group_adjust.meals[meal_define], dish);
            $scope.datagrid.data = {};
            angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
                angular.forEach(meal_.dishes, function(dish_,ind){
                    $scope.addFoodFromDish(meal_,dish_,true);
                });
            });
            $scope.selected.group_adjust.data = $scope.datagrid.data;
            angular.forEach($scope.datagrid.data, function(foods, warehouse_id){
                angular.forEach(foods, function(food, food_id){
                    $scope.onChange_luong1tre(food);
                });
            });
            $scope.totalCalculator();
            $scope.selected.dish = undefined;
        };
        $scope.onChangeGroup = (group_id)=>{
            if ($scope.row.group_id != group_id){
                $scope.menu_planningSelected($scope.menu_adjust.group_adjusts[group_id].menu_plannings[0], true);
            }
        };
        $scope.onChange_soluong = (group) => {
            group.food.thucmuatheodvt = group.soluong;
            $scope.onChange_thucmuatheodvt(group.food);
        };
        $scope.getTongTien = (food) => {
            var rs = 0;
            angular.forEach(food.groups, function (group, group_id) {
                var tongnhom = $scope.getTongTien1Nhom(group);
                rs = $['+'](rs, tongnhom);
            });
            return rs;
        };
        $scope.getTongTien1Nhom = (group) => {
            var rs = 0;
            if(group) {
                if (group.food.exports == undefined) {
                    rs = $['+'](rs, group.food.thanhtien1nhom);
                } else {
                    angular.forEach(group.food.exports, function (exp, food_id_price) {
                        rs = $['+'](rs, $['*'](exp.quantity, exp.price));
                    });
                }
            }
            return rs;
        };
        $scope.getTongluong = (food) => {
            var rs = 0;
            // if(console.log(food))
            angular.forEach(food.groups, function (group, group_key) {
                rs += parseFloat(group.soluong);
            });
            return rs;
        };
        /*
        * xử lý dữ liệu gộp form
        * dataAll = selected.group_adjusts : các thực đơn
        * */
        $scope.processData = (dataAll) => {
            $scope.table.rows = {};
            $scope.table.groups = {};
            var fds = {};
            angular.forEach(dataAll, function (group, group_id) {
                var group_key = group.menu_plannings[0].group_key;
                $scope.table.groups[group_key] = {
                    name: group.name,
                    title: ''
                };
                angular.forEach(group.menu_plannings[0].data, function (kho, meal_key) {
                    var warehouse_id = 2;
                    if (meal_key == 1) {
                        warehouse_id = group.menu_plannings[0].meals['buasang'].warehouse_id;
                    } else if (meal_key == 3) {
                        warehouse_id = group.menu_plannings[0].meals['buatoi'].warehouse_id;
                    }
                    fds[meal_key] || (fds[meal_key] = {});
                    angular.forEach(kho, function (food, food_id) {
                        fds[meal_key][food_id] || (fds[meal_key][food_id] = {
                            name: food.name,
                            price: food.price,
                            groups: {}
                        });
                        fds[meal_key][food_id].groups[group_key] || (fds[meal_key][food_id].groups[group_key] = {
                            soluong: food.thucmuatheodvt,
                            soluong_kg: food.thucmua1nhom,
                            thanhtien: food.thucmuatheodvt * food.price,
                            food: food,
                            price: food.price,
                        });
                    });
                });
            });
            $scope.table.rows = fds;
        };
        $scope.onSelectDish = (row) => {
            process($CFG.remote.base_url + '/doing/dinhduong/menu_adjust/dishs', {id: row.id, async: true}, function (resp) {
                if (resp) {
                    row = resp;
                    row.quantity_edit = row.quantity;
                    $scope.menu_adjust.ignore_ids = '';
                    var kt = 0;
                    angular.forEach(row.ingredient, function (food, key) {
                        food.name_edit || (food.name_edit = food.name);
                        $scope.menu_adjust.ignore_ids = $scope.menu_adjust.ignore_ids + ',' + key;
                        if (food.quantities) {
                            if (count(food.quantities) == 0) {
                                food.quantities = {};
                            }
                            if (food.quantities[$scope.row.group_id] > 0) {
                                food.quantity = food.quantities[$scope.row.group_id];
                            }
                        }
                        if (food.quantity > 0) kt++;
                    });
                    if (!kt) {
                        if (!count(row.ingredient)) {
                            alert('Món ăn không có thực phẩm');
                        }
                        if (!kt) {
                            alert('Món ăn không có thực phẩm nào có lượng dành cho nhóm trẻ đang chọn.\n Hãy kiểm tra lại lượng của món ăn theo nhóm trẻ.');
                        }
                    }
                    /*Kiểm tra thực phẩm có thêm ngoài không thì đẩy lại vào*/
                    // if (dish) { /*Nếu là thêm mới thì không có món được truyền vào*/
                    //     if (dish.id === row.id) {
                    //         angular.forEach(dish.ingredient, function (food, food_id) {
                    //             food.name_edit = food.name;
                    //             food.deleted = false;
                    //             if (!row.ingredient[food_id]) {
                    //                 row.ingredient[food_id] = food;
                    //             }
                    //         });
                    //     }
                    // $('#dish').combobox('clear');
                    $scope.$apply(function () {
                        $scope.selected.dish = row;
                        $scope.foodAddeds = {};
                    });
                }
            }, function () {
            }, false);
        }
    }]);
})(angular_app);