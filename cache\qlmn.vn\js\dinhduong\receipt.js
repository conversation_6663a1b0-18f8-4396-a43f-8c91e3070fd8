angular_app.controller('receiptController', ['$scope', function ($scope) {
    $scope.control = 'receipt';
    $scope.templates = {
        form: $CFG.project + '/' + $scope.control + '/form.html',
        addToVote: $CFG.project + '/' + $scope.control + '/add-to-vote.html',
        print: $CFG.project + '/' + $scope.control + '/print.html',
    };
    $scope.init = function () {
        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {
            $scope.$apply(function () {
                $scope.date = dateboxOnSelect();

                $scope.suppliers = resp.data.suppliers;
                $scope.warehouses = resp.data.warehouses;
                $scope.mapMeasure = resp.data.mapMeasure;

                $scope.suppliers.forEach(function (supplier, index) {
                    $scope.suppliers[index].id = supplier.id;
                });

                $scope.mapWarehouses = {};
                $scope.warehouses.forEach(function (warehouse) {
                    $scope.mapWarehouses[warehouse.id] = warehouse.name;
                });

                $scope.mapSuppliers = {};
                $scope.suppliers.forEach(function (supplier) {
                    $scope.mapSuppliers[supplier.id] = supplier.name;
                });

                /*Khởi tạo table*/
                var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + $scope.control + '/list';
                $.dm_datagrid.init(url, $scope.control, '', $scope.columns, $scope.options);
                $scope.table = $('#tbl_' + $scope.control);

                /*Thông tin lưu khi chọn thực phẩm*/
                $scope.foodId = '';
                $scope.price = 0;
                $scope.food = {};
                $scope.measure = '';
            })
        });
    };

    $scope.columns = [[
        {
            field: 'check', formatter: function (value, row) {
                if ((row.vote_id === null || row.vote_id === '')  && row.is_legal !== 2)
                    return '<input id="check-id-' + row.id + '" type="checkbox">';
                return '';
            }
        },
        {title: 'Điểm trường', field: 'school_point', width: 40, align: 'center'},
        {title: 'Phiếu', field: 'vote_name', sortable: true, align: 'left', width: 40},
        {
            title: 'Thực phẩm', field: 'name', sortable: true, align: 'left', width: 120,
            formatter: function (value, row) {
                if(row.is_legal === 2)
                    return value + ' <a title="Tồn" class="fa fa-info color-green"></a>';
                return value;
            }
        },
        {
            title: 'Kho', field: 'warehouse_id', align: 'left', width: 50, formatter: function (value) {
                return $scope.mapWarehouses[value];
            }
        },
        {title: 'Số lượng', field: 'quantity', width: 90, align: 'center', editor: 'textbox'},
        {
            title: 'Đơn giá',
            field: 'price',
            width: 100,
            editor: 'numberbox',
            align: 'center',
            formatter: function (value) {
                return digit_grouping(value);
            }
        },
        {
            title: 'Nhà cung cấp', field: 'supplier_id', width: 100, sortable: true, formatter: function (value) {
                return $scope.mapSuppliers[value];
            }
        },
        {
            title: 'Đơn vị tính', field: 'measure_id', width: 50, align: 'center', formatter: function (value) {
                return $scope.mapMeasure[value];
            }
        }
    ]];
    $scope.options = {
        enableFilter: [
            {
                field: 'school_point',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'storages.school_point');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'storages.school_point',
                                op: 'equal',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'vote_name',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'votes.name');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'votes.name',
                                op: 'beginwith',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'name',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'foods.name');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'foods.name',
                                op: 'beginwith',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'warehouse_id',
                type: 'combobox',
                options: {
                    panelHeight: 'auto',
                    data: [{value: '', text: 'Tất cả'}, {value: 1, text: 'Kho sáng'}, {value: 2, text: 'Kho trưa'}],
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'warehouse_id');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'warehouse_id',
                                op: 'equal',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'measure_id',
                type: 'label'
            },
            {
                field: 'supplier_id',
                type: 'label'
            },
            {
                field: 'price',
                type: 'label'
            },
            {
                field: 'quantity',
                type: 'label'
            },
            {
                field: 'check',
                type: 'label'
            },
        ],
        view: groupview,
        groupField: 'date',
        groupFormatter: function (id, rows) {
            var objectDate = new Date(rows[0].date);
            var dateFomart = objectDate.getDate() + "/" + (objectDate.getMonth() + 1) + "/" + objectDate.getFullYear();
            return 'Ngày nhập ' + dateFomart + ': <i>' + rows.length + '</i>';
        },
    };

    $scope.form = function () {
        $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + $scope.control,
                action: 'form',
                title: 'Quản lý nhập kho',
                size: size.wide,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.templates.form,
                onshown: function () {
                    process($CFG.project + '/' + $scope.control + '/form', {async: true}, function (resp) {
                        $scope.$apply(function () {
                            var data = resp.data;
                            $scope.mapFoods = data.mapFoods;
                            $scope.mapFoodById = {};
                            $scope.mapFoods.forEach(function (food) {
                                $scope.mapFoodById[food.id] = food.name;
                            });
                            delete $scope.mapFoods;

                            $scope.vote = data.vote;
                            $scope.dateDisabled = false;
                            /*Ngày hiện tại*/
                            $scope.school = data.school;

                            /*add*/
                            $scope.supplierId = '';
                            $scope.warehouseId = '2';
                            $scope.quantity = 0;
                            $scope.selected = {};
                            $scope.rows = [];
                        })
                    })
                },
                cancel: function () {
                    $("#tbl_" + $scope.control).datagrid('reload');
                }
            }
        );
    };

    /* 2. Thay đổi số phiếu*/
    $scope.formInputChange = function () {
        var post = {
            'vote': $scope.vote
        };
        process($CFG.project + '/' + $scope.control + '/formInputChange', {post: post, async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                $scope.dateDisabled = false;
                if (data.id !== -1)
                    $scope.dateDisabled = true;

                $scope.date = getDate(data.date);
                if (data.rows.length > 0) {
                    data.rows.forEach(function (row, index) {
                        data.rows[index].quantity = parseFloat(row.quantity);
                        data.rows[index].price = parseFloat(row.price);
                    });
                }
                $scope.rows = data.rows;
            });
        });
    };

    /* 3. Chọn thực phẩm */
    $scope.selectFood = function (food) {
        var url = $CFG.remote.base_url + '/doing/dinhduong/dish/getFoodDetailById';
        var data = {async: true, id: food.id};
        process(url, data, function (resp) {
            if (!resp) return;
            else {
                $scope.$apply(function () {
                    angular.forEach(resp, function (food) {
                        $scope.food = food;
                        $scope.foodId = food.food_id;
                        $scope.measure = food.measure_name;
                        $scope.price = food.price;
                    });
                });
            }
        }, function () {
        }, false);
    };

    /* 4. Lưu */
    $scope.formAdd = function () {
        var post = {
            'vote': $scope.vote,
            'date': $scope.date,
            'food_id': $scope.foodId,
            'warehouse_id': $scope.warehouseId,
            'quantity': $scope.quantity,
            'price': $scope.price,
            'supplier': $scope.supplierId,
            'food': $scope.food
        };
        process($CFG.project + '/' + $scope.control + '/formAdd', {post: post, async: true}, function (resp) {
            if (resp.result === 'success') {
                $scope.$apply(function () {
                    post.id = parseInt(resp.data.id);
                    post.warehouse_id = parseInt(post.warehouse_id);
                    post.food_name = post.food.name;
                    post.supplier_id = post.supplier;
                    post.measure_id = post.food.measure_id;
                    post.school_point = $scope.school;
                    post.exported = false;
                    $scope.rows.push(post);
                });
            }
        });
    };

    $scope.formEdit = function (row) {
        process($CFG.project + '/' + $scope.control + '/formEdit', {post: row}, function (resp) {
            if (resp.result === 'success') {
                alert('Sửa thành công');
            }
        });
    };

    $scope.del = function (index, row) {
        if (!row) {
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>', ' - Dữ liệu sau khi xóa sẽ không thể khôi phục.'];
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function (r) {
            if (r) {
                process($CFG.project + '/' + $scope.control + '/del', {ids: row.id}, function () {
                    $scope.$apply(function () {
                        $scope.rows.splice(index, 1);
                    });
                })
            }
        });
    };
    $scope.showDetail = function (storage_id) {
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + 'storage',
                action: 'detail',
                title: 'Chi tiết xuất kho',
                content: function (element) {
                    loadForm($CFG.project + '/' + 'storage', 'detail', {storage_id: storage_id}, function (resp) {
                        $scope.$apply(function ($scope) {
                            var form = '<div >'+resp+'</div>';
                            $(element).html($scope.compile(form,$scope));
                            if(typeof callback === 'function'){
                                callback($scope);
                            }
                        });
                    })
                }
            },
            function (resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
                    $scope.formInputChange();
                }
            }
        );
    };

    $scope.selectDate = function(date){
        $scope.date = date;
    };
    $scope.addToVote = function () {
        var rows = $scope.table.datagrid('getRows');

        const ADD = 'add';
        const MANY_DATE = 'many-date';
        const NULL = 'null';
        const INIT = 'init';

        var check = ADD;
        var date = INIT;
        for (var i in rows) {
            if(rows.length < 1){
                check = NULL;
                break;
            }
            if ($('input#check-id-' + rows[i].id).prop('checked')) {
                if(date === INIT)
                    date = rows[i].date;
                else {
                    if(date !== rows[i].date){
                        check = MANY_DATE;
                        break;
                    }
                }
            }
        }
        switch (check) {
            case ADD:
                $.dm_datagrid.showAddForm({
                    module: $CFG.project + '/' + $scope.control,
                    action: 'addToVote',
                    title: 'Chọn phiếu',
                    size: size.small,
                    fullScreen: false,
                    showButton: false,
                    scope: $scope,
                    content: $scope.templates.addToVote,
                    onshown: function () {
                        process($CFG.project + '/' + $scope.control + '/addToVote', {async: true, data: {date: date}}, function (resp) {
                            $scope.$apply(function () {
                                var data = resp.data;
                                $scope.votes = data.votes;
                            })
                        })
                    },
                    cancel: function () {
                        $("#tbl_" + $scope.control).datagrid('reload');
                    }
                });
                break;
            case MANY_DATE:
                alert("Chỉ được chọn các thực phẩm cùng ngày để thêm vào phiếu");
                break;
            default:
                alert("Hãy chọn một dòng");
                break;
        }
    };

    $scope.saveToVote = function (voteId) {
        if(voteId === ''){
            alert("Vui lòng chọn một phiếu!");
        }
        else {
            var ids = [];
            var rows = $scope.table.datagrid('getRows');
            for (var i in rows) {
                if ($('input#check-id-' + rows[i].id).prop('checked')) {
                    ids.push(rows[i].id);
                }
            }
            var post = {
                voteId : voteId,
                ids: ids,
            };
            process($CFG.project + '/' + $scope.control + '/saveToVote', {async: true, post: post}, function (resp) {
                if(resp.result === 'success')
                    alert('Thêm thành công!');
            })
        }
    };

    $scope.print = function () {
        $scope.begin = dateboxOnSelect();
        $scope.end = dateboxOnSelect();
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + $scope.control,
            action: 'in',
            title: 'In phiếu nhập kho',
            size: size.normal,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.templates.print,
        });
    }
}]);