.tbl_container{
	padding-top: 40px;
}
.tbl_container label{
	font-size: 15px;
	padding-top: 3px; 
}
.tbl_container td label,.tbl_container .head-weekdays th label{
	font-weight: initial;
}

.tbl_container .tbl-container-header{
	
}
.tbl_container .tbl-container-body{
	overflow-x: hidden;
	overflow-y: auto;
	padding: 10px;
	background: #fff;
}
.tbl_container .calender-content{
	/*padding: 5px;*/
}
.tbl_container .month-container{
	padding: 10px;
}
.tbl_container .table-calender{
	width: 100%;
}
.table-calender tbody td label.title-day{
	margin: 0;
}
.table-calender td,.table-calender th{
	position: relative;	
	height: 35px;
	width: 35px;
	padding: 3px;
}
.table-calender .day-content{
	position: relative;
	padding: 1px;
	height: 100%;
}
.table-calender td#day-selected .day-content {
    background: #b7ffa1;
    border-radius: 3px;	
}

.table-calender tbody td.day-enable:hover{
	background: #fcecd8;
}
.table-calender tbody td#day-selected:hover{
	background: #d2681d;
}
.day-disabled .icon-checked{
	display:none;
}
.table-calender thead .check-all input{
	display: none;
	margin: auto;
	margin-bottom: -12px; 
}
.table-calender:hover thead .check-all input{
	display: block;
}
.table-calender:hover thead .check-all label{
	position: absolute;
	width: 100%;
	text-align: center;
	top: -4px;
	left: 0;
}
.table-calender thead{
	border-bottom: 1px solid #ccc;
	background: #d6e8e8;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
.table-calender thead .year{
	border-bottom: 1px solid #ccc;
}

.table-calender th,.table-calender td{
	text-align: center;
}
.table-calender td{
	border: 1px solid #efefef;
}
.day-disabled label.title-day{
	color: #ccc;
}
.icon-checked, input.checkbox-day{
	position: absolute;
	top: 0;
	right: 0;
	color: red;
	font-size: 11px;
}
input.checkbox-day{
	display: none;
}
td.day-enable:hover input.checkbox-day{
	display: block;
}
td.day-enable:hover .icon-checked{
	display: none;
}
