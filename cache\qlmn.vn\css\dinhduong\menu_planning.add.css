.col-right .cell-right-top{
	position: sticky;
	left: 0;
	padding: 5px;
    margin: 0;
    margin-bottom: 7px;
    /*border-bottom: 1px solid #ccc;*/
}
.col-right .container-table{
	padding: 0 7px;
}
.col-right .table.food-lists{
	width: auto; 
}
.col-right .table.food-lists tbody
,.col-right .table.food-lists thead
,.col-right .table.food-lists tr
,.col-right .table.food-lists th
,.col-right .table.food-lists td {
	padding: 0;	
}
.table.food-lists th
,.table.food-lists td {
	border-right: 1px solid #ccc;
	border-collapse: inherit;
}
.table.food-lists thead{
	background: #e5f6d9;
}
.col-right .table.food-lists tbody tr{
	background: #fff;
}
.table.food-lists .cell-label-container{
	padding: 3px;
	height: 22px;
}
.table.food-lists tbody tr:nth-child(2n+0){
	background: #f6f9f9;
}
.table.food-lists td span{
	padding: 3px;
}
.table.food-lists td.cell-edit{
	position: relative;
	background: #fff;
}
.table.food-lists td.cell-edit input{
	position: absolute;
	width: 100%;
	height: 98%;
	padding: 3px;
	left: 0;
	top: 0;
	border: 0;
	opacity: 0;
	text-align: center;
}
.table.food-lists td.cell-edit input:focus{
	opacity: 1;
}
.col-right .table.food-lists .col-1{
	width: 40px;
	border-right: 1px solid #ccc;
}
.col-right .table.food-lists .col-2{
	border-right: 1px solid #ccc;
}
.col-right .table.food-lists .col-3{
	width: 90px;
}
.col-right .table.food-lists .col-2{
	width: 250px;
	/*position: sticky;
	left: 40px;*/
}
.col-right .table.food-lists tbody .col-2 div.cell{
	text-align: left;
    white-space: nowrap;
}
.col-right .table.food-lists tbody tr#title td,.col-right .table.food-lists .col-1,#container-thongketpdd{
	position: sticky;
	left: 0px;
}
#container-thongketpdd{
	bottom: 0;
    background: #ECECEC;
    padding: 3px 7px 0 7px;
    margin-bottom: 7px;
}
.col-right .table.food-lists tbody tr#title{
	background: #f6f6f6;
}
.scroll-container{
	/*max-height: 497px;*/
    /*overflow: auto;*/
    /*position: relative;*/
}
.table .header tr.row-head-title th{
	text-align: center;
	vertical-align: middle;
	border-right: 1px solid #ccc;
	padding: 3px;
}
.table .header tr{
	background: #D9EDF6;
}

.cell-edit-container{
	display: none;
}
.layout-accordion tr td{
	padding: 0px;
}
.table.food-lists tbody tr:hover td div.cell{
	background: #eee;
}
.table.thanhphandinhduong{
	background: #fff;
	border: 1px solid #ccc;
}
.table.thanhphandinhduong thead th,
.table.thanhphandinhduong tbody td{
	padding: 0 3px;
}
.table.thanhphandinhduong tfoot td{
	padding: 3px 5px;
}
.table.thanhphandinhduong thead th{
	text-align: center;
	vertical-align: middle;
	background: #D9EDF6;
}
.table.thanhphandinhduong tbody tr td .cell-label-container{
	text-align: center;
}
.table.thanhphandinhduong tbody tr td.col-1 .cell-label-container{
	text-align: left;
}
.table.thanhphandinhduong tbody td.col-1 > .cell-label-container{
	font-weight: bold;
}
.table-title-thanhphandinhduong {
    padding: 0 17px;
}
.table.tbl-danhgiabuaan thead tr{
	background: #eef6fd;
}
.table.tbl-danhgiabuaan tfoot tr td{
	padding: 7px 5px;
}
.table.tbl-danhgiabuaan td,
.table.tbl-danhgiabuaan th{
	padding: 3px;
	vertical-align: middle;
}
.table.tbl-danhgiabuaan thead th,
.table.tbl-danhgiabuaan tbody td{
	/*text-align: center;*/
	border-right: 1px solid #ccc;
}
.container-balance-left table{
	width: 100%;
    background: #e5f6d9;
    border: 1px solid #cbdebe;
}
table.tbl-thongtinthucdon{
	width: 100%;
	margin: 7px 0px;
}
table.tbl-thongtinthucdon td{
	padding: 1px 7px;
}

