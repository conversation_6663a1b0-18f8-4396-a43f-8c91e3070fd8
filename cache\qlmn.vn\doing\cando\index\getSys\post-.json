{"request": {"url": "https://qlmn.vn/doing/cando/index/getSys", "method": "POST", "headers": {"connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "x-csrf-token": "CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Brave\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/javascript, */*; q=0.01", "dnt": "1", "sec-gpc": "1", "origin": "http://localhost:3000", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:3000/single/cando", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9,vi;q=0.8", "cookie": "authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiZ1hSQ0NVaTNaZGRva2VoQ0NKdExBOFYzdENzdGJqVGJ4akV5czZGNFIxTDFyRlFmT056Y0xZbkFkZFdwbkhwMF9GUko1NVlMNW5RWG1qMkF1enlCYkEifQ..CYZuZQMi3UweO8EqkJVjyQ.2ncZWaXznq5qIH4xTeeqmmbZBLFPbJPJRl6McXg0PuTn9qk2RRqLnc1v6UWEidR_ssdVI1hHQjDZqgDd2RcY4PYDUKHPQAo9yM7nKXCIDoFYwuHNZC1ge7XDXMmET3_XacK9VLp-MEzc9hLOZkZZC6bKBBFHvkCzNycONocxJGOaj7Dbo2S9vGolzr7qBKQFN6b4bE9C1h4fOJUWmxth4D6ZxS3K9GNRWstZQiNEZa6XYIlN2o2MaAiSQne1YNPYv8DpN_3zjeVFMGHn3G-Xbms-dqKrw4epoK2qiO6e_zJslWjoJefe4y-NeTuyby_nXzMPMXozEOl5wMfHv6UNqybbxsIe3EgorhVKEPt07bln0NZrYV-o1XlR5Jd3HViDOIeuiaKwoxJtjRNrj_9gFCemCNyAiKzkr0qtN3oIWwV3L3gORwSphTygFWGE_ICqkqO8EAFCCPDGVGRALAW4rkrdSIn4aD4Eb4WQ9pGGpHb7fwTp8HECCTpMXT0a4hDEZsZNLeaTLGTwRoWzCuKF-o4NPMMzpnQDq7B6wwluJN6gYKhcIuumBPSfrrTBl3PjIzeZhiCVojV1SGT9V7MAjexwCy1gZwaqeYdWOEn47ubQpEotOED_x19o65Izu-5S48CMtD9uTTMgj-sH9ps1QKO7vHQctlLITLhsIVt2Ww_4iqu8N_5Mvb0lLaZjo1O7hXA5nCGFChAr7-UZlHRQf6xPEk4i4-kTzOPukP63-fsSwhdaP4Zo4T4xkCBUJiYbsVLGUSmkzTQgQN3EzJ8LXg.5h8b88G_eJS0BAVbcHsK0i3Bqzsha4iYJC-p4qV7JRk; fblo_1243027207556797=y; __Secure-authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiTDR5b0l4UkJucEgzX2k0RGpIM09Eb2RkUlZlWEFSdWpDLS1WMjdPYlVXVGxVeTJrZG5ibldmYTNWUS0xZXlILVp1RS1HY3VIWnRQSFV3S3VQb2VZVmcifQ..QRZZhtIV7cpbGuQTKgOHNQ.CqTx0b4Fybgll1gcjstmm1p_3bMQtKcyIL9JSZ37BJx_mFdZngcR3pMMyh8AUNYqQevv-O3LM3U7mZLSr35nsY6K4oRMFpPBQwmK9lD8AV6ujg-HUBThADK035rzhHqCsEjoAmbLmbr1bYxoXTXVGHtLyXzYQYD9pluVzE3PYdemvE2ew8CbhYYOolcmVbr4fBMxQeAkL-TT5Q2DQxlX1tHcNyrNv_3CYXLyKLPVSbr6Yu-Huhcaq9tg81iUWzo3gFQHGaOQUxcRZ6_JKvanu2o9pnQnLO__X6OwLbhDVvtvUkL-krLzdPFf74ulqniLKDGC8kEoEZ8BZrgAuBGri328TgiSh3XBio5xm5hLWvrwjYQW9eivnsDE__9akFje_OQ8jkg44mXOZzVw6X37-ixaWD1hjR-UdDLJuqetx0b9ydm_FPMV2xA6tVwvzj-pSTO8Ma0BH-1o0E8Q8WzVgdssnELpfvt6jNVvJbg4z_CZnSvABaWFZHq6VUn1gK6Jszlz_tj1EoBE9RaF6c7W6jWzj2u1rrej5HkZvSzMD26TeCL5-6gQQehnt4xWLboVCH8r95vD5Ktgr4xuqBacDHvBRKobD_40dBJtq1NUveGsC91Q6nzI-2ymzPzFLGVM.NWGigHg5VYjAv3M-fL9KxSOV1Iy8K1wrKed7VKiNmLE; _ga_J3K0TBMXZ1=GS2.1.s1754620090$o1$g0$t1754620090$j60$l0$h0; _ga=GA1.1.7460584.1754620091; _gid=GA1.1.1442505230.1754620091; XSRF-TOKEN=eyJpdiI6ImNPSER4K0ExdFFkTWxMalQ1V3RseGc9PSIsInZhbHVlIjoid2RPYTNNM1NhRDQya2o5ZE9FWmlFblZYVktWVGNOYnh5eUJrQTVYdlVRQ0MxeHBYamgxdHJFT2t3Ylh2RnMwOSs3eTR6b094RnpTZU95d05icHlrSVE9PSIsIm1hYyI6ImM5ZWEyNmEyMTBmYjM4NTJjMmRlYTc5MjM2ZjRmY2JjOTQ1N2MxY2FlZjBhODZhZjY2OTgyMjgzOGNjNDAxNGIifQ%3D%3D; laravel_session=eyJpdiI6IjA4SG1mVFdGM2dyNXBXcFRWNzJmZHc9PSIsInZhbHVlIjoiYTRLcDZwOXpOTWF1Q2xud2ZGakRtS3FWZFBwd0NGMHU5OStFS2tJUytQeTB4QVJPNDU3MUo1NjdHa3J1NWJyQVFZZDhtNXd2N0ZPUWpIXC85ekp4THFRPT0iLCJtYWMiOiJkY2YwMGFmZjhlNzFkNWMwZmNkNDQ2ODlkMjk3NjAxN2I5ZTQ0NjBlZGI0ZWNkYTE5NTQxOGQ0NzNkNWU0ODEzIn0%3D"}}, "response": {"status": 200, "headers": {"date": "Fri, 08 Aug 2025 02:29:30 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "vary": "Accept-Encoding", "cache-control": "max-age=43200, public, s-maxage=43200, no-cache", "expires": "Fri, 08 Aug 2025 14:29:30GMT", "set-cookie": "XSRF-TOKEN=eyJpdiI6InI2eTd5dFBCcEhyNWszXC9xdDluNnBBPT0iLCJ2YWx1ZSI6IlFXSEtORnhQbkhZUmFMckhEb1NvT0pOQk9hdnN0V1MzT3pvaUYzM3lcL25qQVwvc3RHXC9nTzJCbndueGJnc3lMbFVNV1MrTjZMQUg0bW5nZHpsUmpXcHRRPT0iLCJtYWMiOiIyYjA2NTJmNjE3ZDNjN2E4OTFhOWNkMzhkNjQzZDM2ZGMwOTVlODhlMWU3YmIxODJmYmJlZDRjZDMxNmEzOWMzIn0%3D; expires=Fri, 08-Aug-2025 04:29:30 GMT; Max-Age=7200; path=/; Secure; HttpOnly;; Secure; HttpOnly; SameSite=Lax, laravel_session=eyJpdiI6IlBFQitLMXlOcVZjNlZmU0t6ZiszbUE9PSIsInZhbHVlIjoic1NiRk1xU2VMMTdxalNKdkNNVUFaS2ZKZDJGeVBub2tzd0luXC9wMDViT3ZHRWk1Umoyd1BJNWtTbFpXUGxzdHQrMTlaeTh4WldVTjFJOUlIMjRTSnFRPT0iLCJtYWMiOiJjMzNlZDlhZjBmODhhOTY2MjQ4MWQ0Zjg4MjFmNTMzMmViZjI0ZGQwZGZmYjA5ZTRmNWQzYzI0OTE0ZjU2MDk4In0%3D; path=/; Secure; HttpOnly;; secure; httponly; SameSite=Lax", "x-vietec": "PMS-70", "x-frame-options": "DENY", "content-security-policy": "frame-ancestors 'self';", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-xss-protection": "1; mode=block"}, "body": "eyJyZXN1bHQiOiJzdWNjZXNzIiwiZGF0YSI6eyJsb2NhbCI6eyJiYXNlX3VybCI6IlwvanNcL2FkbWluIn0sInJlbW90ZSI6eyJiYXNlX3VybCI6Imh0dHBzOlwvXC9xbG1uLnZuIn0sInRlbXBsYXRlIjp7ImJhc2VfdXJsIjoiXC90ZW1wbGF0ZXMifSwicHJvamVjdCI6ImNhbmRvIiwic2Nob29sWWVhciI6MjAyNCwic2Nob29sTmFtZSI6bnVsbCwidXNlciI6eyJ1c2VybmFtZSI6ImQuaGQubW5xdWFuZ3Z0Iiwic3VwZXJhZG1pbiI6MX19fQ=="}}