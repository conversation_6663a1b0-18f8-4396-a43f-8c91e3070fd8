<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_unit_food_detail_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="unit_food_detail"></div>
			<div class="function-kh-ct"></div>
		</div>
		<div class="header-search" id="header-search">
			<span style="width: 110px;" class="icon-searching glyphicon" ng-show="keysearch_name || keysearch_foodtype_id" onclick="$.unit_food_detail.cleanSearch()">
                <span style="float: left;" class="glyphicon glyphicon-filter hover-pointer" title="Bỏ tìm kiếm" >
                    <!-- <span class="glyphicon glyphicon-remove"></span> -->
                </span>
                <label class="label-unit-food" style="">Bỏ tìm kiếm</label>
            </span>
		    <input id="name" field="foods.name" class="form-control" style="width: 180px; margin-right: 3px; margin-left: 5px;" placeholder="Tên hoặc mã TP" ng-change="unit_food_detail.onChangeKeysearchName(keysearch_name)" ng-model="keysearch_name" ng-model-options='{debounce: 300}'>
		    <input id="foodtype_id" field="foods.foodtype_id" placeholder="Nhóm loại thực phẩm" ng-model="keysearch_foodtype_id">
            <select class="mL5" ng-model="status" ng-init="status=1" ng-change="unit_food_detail.onchangeStatus(status)">
                <option ng-value="1">Kích hoạt</option>
                <option ng-value="0">Ngừng kích hoạt</option>
            </select>
            <select class="mL5" ng-model="is_static" ng-change="unit_food_detail.onchangeStatic(is_static)" >
                <option value="">--- Lọc TP ---</option>
                <option ng-value="1">Viện DD</option>
                <option ng-value="0">Tự Thêm</option>
                <option ng-value="2">Có đơn giá</option>
            </select>
                        <div style="position: absolute;right: 4px;">
                <div class="dropdown dropdown-setting">
                    <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
                        <i class="fa fa-cog fa-spin icon-setting"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right" style="min-width: 140px !important">
                        <li ng-if="$CFG.province == 48 || $CFG.province == 97">
                            <a href="" ng-click="unit_food_detail.addExcelForm('foodsWithPrice')" class="clrBlue pLR5"><i class="glyphicon glyphicon-upload clrBlue mR5"></i> Tải lên file các thực phẩm có đơn giá</a>
                        </li>
                        <li ng-if="$CFG.province == 48 || $CFG.province == 97">
                            <a href="" ng-click="unit_food_detail.downloadFoodsWithPrice()" class="clrBlue pLR5"><i class="glyphicon glyphicon-download clrBlue mR5"></i> Tải xuống file các thực phẩm có đơn giá</a>
                        </li>
                        <li ng-if="$CFG.province == 48 || $CFG.province == 97" role="separator" class="divider" ng-if-end></li>

                        <li ng-if-start="status">
                            <a href="" ng-click="unit_food_detail.addExcelForm('foodPriceByDay')" class="clrBlue pLR5"><i class="glyphicon glyphicon-upload clrBlue mR5"></i> Tải lên file mẫu Giá TP theo ngày</a>
                        </li>
                        <li>
                            <a href="" onclick="delAllPriceByDay()" class="pLR5"><i class="glyphicon glyphicon-ban-circle clrRed mR5"></i> Hủy giá theo ngày của tất cả TP</a>
                        </li>
                        <li role="separator" class="divider" ng-if-end></li>

                        <li><a href="" ng-click="unit_food_detail.handleDeactive(status ? 0 : 1)" class="pLR5">
							<span ng-if="status"><i class="glyphicon glyphicon-ban-circle clrRed mR5"></i>Bỏ kích hoạt</span>
							<span ng-if="!status"><i class="glyphicon glyphicon-ok-circle clrBlue mR5"></i>Kích hoạt</span>
						</a></li>
                        <li><a href="" ng-click="unit_food_detail.handleDeactiveAll(status ? 0 : 1)" class="pLR5">
							<span ng-if="status"><i class="glyphicon glyphicon-ban-circle clrRed mR5"></i>Bỏ kích hoạt tất cả</span>
							<span ng-if="!status"><i class="glyphicon glyphicon-ok-circle clrBlue mR5"></i>Kích hoạt tất cả</span>
						</a></li>
                        <li><a href="" ng-click="unit_food_detail.handleDeactiveNoUse(status ? 0 : 1)" class="pLR5">
							<span ng-if="status"><i class="glyphicon glyphicon-ban-circle clrRed mR5"></i>Bỏ kích hoạt TP chưa <br>từng sử dụng</span>
							<span ng-if="!status"><i class="glyphicon glyphicon-ok-circle clrBlue mR5"></i>Kích hoạt TP chưa <br>từng sử dụng</span>
                        </a></li>
                        <li ng-if="sys.configs.tptruong_show_canxib1 !== undefined || 0">
                            <label class="hidden_canxi_b1">
                                <input style="margin-left: 5px !important;cursor: pointer;margin-top: 5px !important;" type="checkbox" ng-click="hidden_canxi_b1(sys.configs.tptruong_show_canxib1)" inf-configs="sys.configs.tptruong_show_canxib1" inf-id="tptruong_show_canxib1"
                                    ng-true-value="1" ng-false-value="0">
                                    <span style="margin-left: 3px !important;">Hiển thị cột P, L, G, Canxi, B1</span>
                            </label>
                        </li>
						<!--li ng-if="0">
                            <label style="font-weight:normal;">
                                <input style="margin-left: 5px !important;cursor: pointer;margin-top: 5px !important;" type="checkbox" inf-configs="sys.configs.tptruong_show_plgcalo" inf-id="tptruong_show_plgcalo"
                                    ng-true-value="1" ng-false-value="0">
                                    <span style="margin-left: 3px !important; color:red;">Hiển thị phần mở rộng P-L-G, Calo theo TP trường</span>
                            </label>
                        </li-->
                    </ul>
                </div>
    		  	<div class="btn-group">
                   <!--  <button type="button" onclick="$.unit_food_detail.showAddForm()" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span>Thêm mới
                    </button> -->
                    <button type="button" class="btn btn-primary" onclick="$.unit_food_detail.showEditForm()">
                        <span class="glyphicon glyphicon-edit"></span>Sửa
                    </button>
    			  	<button type="button" onclick="$.unit_food_detail.restorDefault()" class="btn btn-primary">
    			  		<span class="glyphicon glyphicon-erase" title="Cập nhật lại dữ liệu gốc"></span>Cập nhật dữ liệu gốc
    			  	</button>
    			</div>
            </div>
		</div>
	</div>
	<div id="tbl_unit_food_detail"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/unit_food_detail.js"></script>
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/unit_food_detail.css">
<script type="text/javascript">
    process('dinhduong/unit_food_detail/measures',{},function(resp){
        $.unit_food_detail.init(resp);
    },null,false);
	var url = $CFG.remote.base_url +"/doing/dinhduong/unit_food_detail/";
    $.dm_datagrid.combobox('foodtype_id',url+'foodtypes',{
        valueField: 'id',
        textField: 'name',
        value:[],
        panelHeight: 'auto',
        onSelect: function(row, element) {
        	angular.element($("#header-search")).scope().$apply(function(scope){
        		scope.keysearch_foodtype_id = row.id;
        	});
        	$.unit_food_detail.doSearch();
        },
        onLoadSuccess: function(data){
            $.unit_food_detail.foodtypes = data;
        },
        queryParams: {},
        width: 200
    });
    function cleanKeyseach() {
    	angular.element($("#header-search")).scope().$apply(function(scope){
    		scope.keysearch_name = '';
    		scope.keysearch_foodtype_id = '';
    		$('input#foodtype_id').combobox('clear');
    		var queryParams = $('#tbl_unit_food_detail').datagrid('options').queryParams;
    		if(queryParams.filterRules) {
	    		delete queryParams.filterRules;
	    		delete queryParams.filter_type;
	    		$('#tbl_unit_food_detail').datagrid('load',queryParams);
	    	}
    	});
    }

    function delAllPriceByDay() {
        $.messager.confirm('Xác nhận', '<span style="color:red;font-size:15px;">Bạn có chắc chắn muốn hủy các bản ghi giá theo ngày của tất cả thực phẩm?</span>', function(r){
            if (r){
                var url = $CFG.project+'/unit_food_detail/delPriceByDay';
                process(url,{is_all: 1},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_unit_food_detail").datagrid('reload');
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    }
</script>
<style type="text/css">
    .label-unit-food:hover{
        cursor: pointer;
        color: red;
    }
    .label-unit-food{
        float: left;
        word-spacing: -8px;
    }
    .pLR5 {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }
    .hidden_canxi_b1 {
        font-weight: 400 !important;
        width: 100%;
    }
    .hidden_canxi_b1:hover{
        background: #f5f5f5;
        cursor: pointer;
        width: 100%;
    }
</style>

