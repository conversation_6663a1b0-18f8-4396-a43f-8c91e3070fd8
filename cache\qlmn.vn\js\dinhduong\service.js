$.service = {
    module: 'service',
    id: '', /*Mã project*/
    init: function(id,project) {
    	var self = this;
    	self.id = id;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*<PERSON><PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
                { title:'Tên dịch vụ', field:'name', width:120, sortable:true },
                { title:'Gi<PERSON> tiền từng nhóm tuổi', field:'groups', width:300, formatter: function(value,row,index){
                    var groups = [];
                    $.each(row.groups, function(index, group){
                        groups.push('<b>'+group.name+'</b>: '+digit_grouping(group.price));
                    })
                    value = groups.join('; ');
                    return value;
                } },
                // { title:'Tổng tiền', field:'price', width:100, align: 'center', formatter: function(value,row,index){
                //     var value = 0;
                //     $.each(row.groups, function(index, group){
                //         value += Number(group.price);
                //     })
                //     value = digit_grouping(value);
                //     return value;
                // } },
            ]],
            {
                onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }, onLoadSuccess:function(data){
                    if($CFG.is_gokids==1) {
                        $('.datagrid-view').height($('.datagrid-view').height() - 30);
                        $('.datagrid-body').height($('.datagrid-body').height() - 30);
                    }
                }
            }
        );
    }, showAddForm: function(callback) { 
    	var self = this;
        var $scope = null;
        $.dm_datagrid.showAddForm(
			{
				module: $CFG.project+'/'+self.module,
				action:'add',
				title:'Thêm mới',
                showButton: false,
				content: function(element){
					loadForm($CFG.project+'/'+self.module,'add', {id: self.id, dataType: 'json'}, function(resp){
                        self.angular(element,resp.html,function(scope){
                            $scope = scope;
                            scope.service || (scope.service = {});
                            scope.service.groups = resp.groups;
                            angular.forEach(scope.service.groups, function(group,index){
                                group.price = 0;
                                group.block = false
                            })
                            scope.service.priceDefaultChange = function(value){
                                angular.forEach(scope.service.groups, function(group,index){
                                    if(!group.block){
                                        group.price = value;
                                    }
                                })
                            }
                            scope.service.priceDefaultCheck = function(){
                                for(var i in scope.service.groups){
                                    if(!scope.service.groups[i].block){
                                        return false;
                                    }
                                }
                                return true;
                            }
                            scope.service.add = function(dialogRef){

                                process('dinhduong/service/add',{data: JSON.stringify(scope.service)},function(resp){
                                    if(resp.result == 'success'){
                                        dialogRef.close();
                                        $('#tbl_service').datagrid('reload');
                                    }
                                });
                            }
                        })
						// $(element).html(resp);
					})
				},buttons: [
                    {
                        id: 'btn-ok',
                        icon: 'glyphicon glyphicon-ok',
                        label: 'Lưu',
                        cssClass: 'btn-primary',
                        action: function(dialogRef){
                            $scope.service.add(dialogRef);
                        }
                    }
                ]

			},
			function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    }, showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
					content: function(element){
						loadForm($CFG.project+'/'+self.module,'edit', {id: row.id, dataType: 'json'}, function(resp){
                            self.angular(element,resp.html,function(scope){
                                $scope = scope;
                                scope.service = resp.row;
                                angular.forEach(scope.service.groups, function(group,index){
                                    group.block = true;
                                })
                                scope.service.priceDefaultChange = function(value){
                                    angular.forEach(scope.service.groups, function(group,index){
                                        if(!group.block){
                                            group.price = value;
                                        }
                                    })
                                }
                                scope.service.priceDefaultCheck = function(){
                                    for(var i in scope.service.groups){
                                        if(!scope.service.groups[i].block){
                                            return false;
                                        }
                                    }
                                    return true;
                                }
                                scope.service.edit = function(dialogRef){
                                    process('dinhduong/service/edit',{data: JSON.stringify(scope.service)},function(resp){
                                        if(resp.result == 'success'){
                                            dialogRef.close();
                                            $('#tbl_service').datagrid('reload');
                                        }
                                    });
                                }
                            })
						})
					},buttons: [
                    {
                        id: 'btn-ok',
                        icon: 'glyphicon glyphicon-ok',
                        label: 'Lưu',
                        cssClass: 'btn-primary',
                        action: function(dialogRef){
                            $scope.service.edit(dialogRef);
                        }
                    }
                ]
				},
				function(resp){
                    $("#tbl_"+self.module).datagrid('unselectAll');
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Hãy chọn một dòng!');
	    }
       
    }, angular: function(element,resp,callback,dialogRef){
        var form = '<div style="height:100%;">'+resp+'</div>';
            // $(element).html(form);
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Chắc chắn xóa ?</div>', function(r){
            if (r){
                $.dm_datagrid.del($CFG.project+'/'+self.module,ids,function(resp){
                    $("#tbl_"+self.module).datagrid('unselectAll');
                    $("#tbl_"+self.module).datagrid('reload');
                })
            }
        });
    }
}
