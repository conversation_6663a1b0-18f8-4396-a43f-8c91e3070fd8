<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/gumshoe.css?=1430289747" />
<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_gumshoe_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="gumshoe"></div>
			<div class="function-kh-ct head-gum" style="margin-top: 60px !important;">
                <div class="warehouse-gum">
                    <label>Kho sáng : </label><input type="checkbox" id="khosang_id" name="khosang" ng-model="gumshoe.warehouse" ng-change="gumshoe.changeMonthVT()" ng-true-value="1" ng-false-value="2">
                </div>
                <div class="date-gum">
                    <div style="float: left;">
                        <label>Tháng </label>
                        <select ng-model="gumshoe.monthSelected" ng-options="item as item.name for item in gumshoe.months track by item.name" id="year" ng-change="gumshoe.changeMonthVT()">
                        </select>
                    </div>
                    <span class="glyphicon glyphicon-refresh btn-over-red" title="Tải lại tháng {{gumshoe.monthSelected.name}}" ng-click="gumshoe.changeMonthVT()" style="font-size: 17px; margin-left: 3px;"></span>
                </div>
                <div class="report-gum">
                    <ul class="ul-report-gumshoe">
                        <li class="li-report-gumshoe">
                            <button class="btn btn-info" title="Theo dõi sổ kho">Tổng hợp</button>
                            <ul class="child-ul-gumshoe">
                                <li>
                                    <a class="icon-in" ng-click="totalImportForNccForm()">
                                        <button class="btn btn-info" title="Thẻ kho">
                                            <img class="img-btn-info" src="../images/icons/exel-icon2.png">
                                            Nhập kho theo NCC
                                        </button>
                                    </a>
                                </li>
                                <li>
                                    <a class="icon-in" ng-click="totalExportBySupplierForm()">
                                        <button class="btn btn-info" title="Thẻ kho">
                                            <img class="img-btn-info" src="../images/icons/exel-icon2.png">
                                            Xuất kho theo NCC
                                        </button>
                                    </a>
                                </li>
                            </ul>
                            
                        </li>
                        <li class="li-report-gumshoe">
                            <button class="btn btn-info" title="Báo cáo" ng-click="stockReport()">Báo cáo</button>
                        </li>
                    </ul>
                </div>
				<div style="float:right;">
					<div class="dropdown dropdown-setting" style="padding:0px;">
						<button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
							<i class="fa fa-cog fa-spin icon-setting"></i>
						</button>
						<ul class="dropdown-menu dropdown-menu-right">
							<li style="padding-top:5px; color:orange;">
								<label class="checkbox-inline">
									<input type="checkbox"
										   inf-configs="sys.configs.qlykho_show_fast_download_sokho"
										   inf-id="qlykho_show_fast_download_sokho"
										   ng-true-value="1" ng-false-value="0">
									Hiển thị link tải nhanh Sổ kho cho cả Kho sáng + Kho trưa?
								</label>
							</li>
                            <li style="padding-top:5px; color:orange;">
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           inf-configs="sys.configs.qlykho_show_tp_move_next_year"
                                           inf-id="qlykho_show_tp_move_next_year"
                                           ng-true-value="1" ng-false-value="0">
                                    Hiển thị cả lượng TP do đã chuyển kho năm sau?
                                </label>
                            </li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="content-gum" id="tbl-gum-scroll" style="position: relative;">
            <table class="table-gumshoe" ng-style="{width: count(gumshoe.gumshoe_first)*gumshoe.style.col.food*3+gumshoe.style.col.day}">
                <thead id="thead-gumshoe" ng-style="{width: count(gumshoe.gumshoe_first)*gumshoe.style.col.food*3+gumshoe.style.col.day,position:'absolute',background:'#fff'}">
                    <tr class="title-table-gum">
                        <th class="td-date-gum th-food-gum" ng-style="{width: gumshoe.style.col.day, height:'100%',position: 'relative'}" rowspan="2">
                            <div class="col1-static" style="position: absolute; width: 100%;height: 100%; top: 0;left: 0;">Ngày
                                <div><a href="{{$CFG.remote.base_url+'/'+$CFG.project+'/stockReport/stockByDay?begin=01/'+gumshoe.monthSelected.name+'&end='+endOfMonth+'/'+gumshoe.monthSelected.name+'&warehouses='+gumshoe.warehouse}}" target="_blank">[Tải cả tháng]</a></div>
                            </div>
                        </th>
                        <th colspan="3" ng-repeat="(key, firsts) in gumshoe.gumshoe_first" ng-bind="key"></th>
                    </tr>
                    <tr>
                        <th ng-repeat="(index,item) in gumshoe.columns" ng-bind="item.title" ng-style="{width: gumshoe.style.col.food}"></th>
                    </tr>
                    <tr >
                        <th class="td-date-gum td-date-gum-child" ng-style="{width: gumshoe.style.col.day, height:'100%',position: 'relative'}" rowspan="2">
                            <div class="col1-static" style="position: absolute; width:100%;height: 100%;top: 0;left: 0;text-align: center; line-height: 25px;color: #ca6161;">Số dư đầu kỳ</div>
                        </th>
                        <th ng-repeat="(index,item) in gumshoe.columns" style="width: 70px; text-align: center; color: #ca6161;">
                            <span ng-if="item.field=='ton'" ng-bind="digit_grouping(round(gumshoe.gumshoe_first[item.food_name].tondauky, 7),'')"></span>
                        </th>
                    </tr>
                </thead>
                <tbody ng-style="{'margin-top': gumshoe.getHeightHeader(), width: count(gumshoe.gumshoe_first)*gumshoe.style.col.food*3+gumshoe.style.col.day}">
                    <tr ng-repeat="(key, gum) in gumshoe.gumshoenews">
                        <th class="td-date-gum-child" ng-style="{width: gumshoe.style.col.day,position:'relative'}">
                            <div class="col1-static" style="position: absolute; width:100%;height: 100%;left: 0;top: 0;text-align: center;background: #fff">
                                <a title="Click tải xuống báo cáo sổ kho" class="icon-in btn-over-orange" href="{{$CFG.remote.base_url+'/'+$CFG.project+'/stockReport/stockByDay?begin='+(key<10?'0'+key:key)+'/'+gumshoe.monthSelected.name+'&end='+(key<10?'0'+key:key)+'/'+gumshoe.monthSelected.name+'&warehouses='+gumshoe.warehouse}}" style="text-decoration: none;">
                                    <span ng-bind="key"></span>
                                    <span class="glyphicon glyphicon-download-alt"></span>
                                </a>
								<a ng-if="sys.configs.qlykho_show_fast_download_sokho" title="Click tải xuống báo cáo Sổ kho: Kho sáng + Kho trưa" class="icon-in btn-over-orange" href="{{$CFG.remote.base_url+'/'+$CFG.project+'/stockReport/stockByDay?begin='+(key<10?'0'+key:key)+'/'+gumshoe.monthSelected.name+'&end='+(key<10?'0'+key:key)+'/'+gumshoe.monthSelected.name+'&warehouses=1,2'}}" style="text-decoration: none; float:right; margin-right:2px;">
                                    [<span class="glyphicon glyphicon-download-alt"></span> S+T]
                                </a>
                            </div>
                        </th>
                        <td ng-repeat="(index,item) in gumshoe.gumshoenews[key]" ng-style="{width: gumshoe.style.col.food}">
                            <span ng-bind="digit_grouping(round(item.value,7),'')"></span>
                        </td>
                    </tr>
                </tbody>
            </table>    
        </div>
	</div>
	<div id="tbl_gumshoe"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/gumshoe.js"></script>
<script type="text/javascript">
    $.gumshoe.init();
    $('.btn-export').on('click', function () {
        var months = angular.element($('body')).scope().gumshoe.monthSelected.name;
        var report = $("select[name='report']").val();
        var warehouse = $("select[name='warehouse']").val();
        var type = $("select[name='type']").val();
        if(report === '' || warehouse === '' || type === '')
            alert("Dữ liệu nhập không được trống!");
        else{
            var url = $CFG.remote.base_url+'/'+$CFG.project+'/gumshoe/'+report + type +'?months=' + months + '&warehouse='+warehouse;
            $(this).attr('href', url);
        }
    });
</script>
<style type="text/css">
    .table-gumshoe tr{
        height: 22px;
    }
    .table-gumshoe th, .table-gumshoe td{
        text-align: center;
    }
    .btn-info{
        background-color: #feffff;
        color: #417d27;
    }
    .img-btn-info{
        width: 20px !important;
        margin-right: 5px;
    }
    .btn{
        padding: 3px 12px;
    }
	</style>