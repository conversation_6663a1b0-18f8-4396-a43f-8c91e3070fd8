.container-price{
	display: inline-flex;
}
.container-price input{
	width:120px;
	padding-right: 39px;
}
.container-price label.price{
	margin-left: -36px;
	padding-top: 4px;
	border-left: 1px solid #ccc;
	padding-left: 3px;
	margin-top: 4px;
}
.new-price-label{
	z-index: 3;
    line-height: 30px;
    font-size: 12px;
    background: #CCC;
    padding-top: 0px;
    margin-right: 0px;
    padding-right: 4px;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
    height: 30px;
}
.measure-style-new{
	width: 31px;
	padding-left: 8px; 
	line-height: 30px;
}
.form-horizontal{
	padding-left: 15px !important;
	padding-right: 15px !important; 
}
.str-main{
	width: 100%;
	display: inline-block;
	border: 1px solid #DDD;
	border-radius: 5px;
	padding: 10px;
	margin-bottom: 10px;
}
.item-str-main{
	width: 100%;
	display: inline-block;
	height: 28px;
	margin-bottom: 10px;
}
/*.item-str-main.ch{
	background: red;
}
.item-str-main.le{
	background: blue;
}*/
.title-item-str p{
	margin: 0px;
	line-height: 28px;
	text-align: right;
}
.new-style-input-str{
	height: 28px;
	border-radius: 5px;
	width: 160px;
}
span.textbox.combo{
	width: 160px !important;
}
p.valid{
	color: #ff7e00;
}