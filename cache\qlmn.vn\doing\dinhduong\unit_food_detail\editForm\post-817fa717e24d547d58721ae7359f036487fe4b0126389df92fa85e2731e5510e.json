{"request": {"url": "https://qlmn.vn/doing/dinhduong/unit_food_detail/editForm", "method": "POST", "headers": {"connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "x-csrf-token": "CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Brave\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/javascript, */*; q=0.01", "dnt": "1", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "sec-gpc": "1", "origin": "http://localhost:3000", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:3000/single/dinhduong/unit_food_detail/list", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9,vi;q=0.8", "cookie": "authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiZ1hSQ0NVaTNaZGRva2VoQ0NKdExBOFYzdENzdGJqVGJ4akV5czZGNFIxTDFyRlFmT056Y0xZbkFkZFdwbkhwMF9GUko1NVlMNW5RWG1qMkF1enlCYkEifQ..CYZuZQMi3UweO8EqkJVjyQ.2ncZWaXznq5qIH4xTeeqmmbZBLFPbJPJRl6McXg0PuTn9qk2RRqLnc1v6UWEidR_ssdVI1hHQjDZqgDd2RcY4PYDUKHPQAo9yM7nKXCIDoFYwuHNZC1ge7XDXMmET3_XacK9VLp-MEzc9hLOZkZZC6bKBBFHvkCzNycONocxJGOaj7Dbo2S9vGolzr7qBKQFN6b4bE9C1h4fOJUWmxth4D6ZxS3K9GNRWstZQiNEZa6XYIlN2o2MaAiSQne1YNPYv8DpN_3zjeVFMGHn3G-Xbms-dqKrw4epoK2qiO6e_zJslWjoJefe4y-NeTuyby_nXzMPMXozEOl5wMfHv6UNqybbxsIe3EgorhVKEPt07bln0NZrYV-o1XlR5Jd3HViDOIeuiaKwoxJtjRNrj_9gFCemCNyAiKzkr0qtN3oIWwV3L3gORwSphTygFWGE_ICqkqO8EAFCCPDGVGRALAW4rkrdSIn4aD4Eb4WQ9pGGpHb7fwTp8HECCTpMXT0a4hDEZsZNLeaTLGTwRoWzCuKF-o4NPMMzpnQDq7B6wwluJN6gYKhcIuumBPSfrrTBl3PjIzeZhiCVojV1SGT9V7MAjexwCy1gZwaqeYdWOEn47ubQpEotOED_x19o65Izu-5S48CMtD9uTTMgj-sH9ps1QKO7vHQctlLITLhsIVt2Ww_4iqu8N_5Mvb0lLaZjo1O7hXA5nCGFChAr7-UZlHRQf6xPEk4i4-kTzOPukP63-fsSwhdaP4Zo4T4xkCBUJiYbsVLGUSmkzTQgQN3EzJ8LXg.5h8b88G_eJS0BAVbcHsK0i3Bqzsha4iYJC-p4qV7JRk; fblo_1243027207556797=y; _ga=GA1.1.7460584.1754620091; _gid=GA1.1.1442505230.1754620091; __Secure-authjs.callback-url=https%3A%2F%2Ffandom.kamgift.vn; __Host-authjs.csrf-token=247b2412793789c401369609505ff8b898b062ce24bc0db69bdabee42146e43b%7C3b94e3c7fd95b788527783a9424866eeb71d050568b8e0e8068b1c078aed865b; __utma=111872281.7460584.1754620091.1754637288.1754637288.1; __utmc=111872281; __utmz=111872281.1754637288.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmb=111872281.1.10.1754637288; __Secure-authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiTDR5b0l4UkJucEgzX2k0RGpIM09Eb2RkUlZlWEFSdWpDLS1WMjdPYlVXVGxVeTJrZG5ibldmYTNWUS0xZXlILVp1RS1HY3VIWnRQSFV3S3VQb2VZVmcifQ..qKFRbaOhQVN3HmIP3QdeVA.Tm5Y97Mqw59kXAun0MkcE-xEbB6ZjIwiiYhPE9qvr5MH-_1mtCqJvc5PwPnZ-h4J5VPcHsebzs7jHoqB809j6eTjYZTrLSGqTmfDOHYRV6X59J-MeUOOke9bWSK6ucKHAkb_ZeoZUNh1hTOEXrKTFErXwr0q1o6PMcF1GF1WPx3a0_q4AFM7a3iIaOrOGkYn50M7YH4btOu3uUbYZuXJTctcWbB5we-8hCNl_fYiK8eF0rdHloJZ3aGhM3dJ0TztmqR1nPjiUm_CizNqxp13kZghWWmCChayXqXJIxaBgWRCizALeHT-HlDNr-oMMjTUvjjgHpNCfHUXa1ZeUYn12EjNlJRRbwtWpbUXOMylAM_G66yAd_vR0cwXAcCWMYouF9fhygHdxkbUMVb4RcCds8bfbrXiBOjQjM5mrUYNslkFheTApYXPP5Q1giz4u17vTS0ICAHp-WmZwiepx9i2gY5FoeVVlQETEy5Yv9OJAti8GuUMVNK26lCMk27WjJMQwVhn5Ag_jdw8cayxGOdz7l8c_dh1dMQFiXEZeUGZ14yRa7TiRu-3M7ZEc8vm3T4NX7_lk-ByO5o33wQTC4nSjdE59Y6L__fYoTz7UCoO7AxkUJ9binXF2UMRStOqpzm-.N0zGz-39Y7aCw8K_aqvkPwjgOtLOEDl4_6Wf0y2lr68; visitor11234=2; _gat_gtag_UA_109824810_3=1; _ga_J3K0TBMXZ1=GS2.1.s1754637062$o2$g1$t1754638276$j54$l0$h0; XSRF-TOKEN=eyJpdiI6IjhXQ1kxOGRObGlUVDRGV04rdVl1eGc9PSIsInZhbHVlIjoiOVF3RXJteFA2XC81OFYzT01HOWRZa200RUZkTzJQK29iRWY1Q0NxVWpFMlkzVlFqbHoxejZXNkFKbzZhZjFkRHJFSzFTRytRM29xV0ttYk05ZUthQlB3PT0iLCJtYWMiOiIxMjdiNWY1NzI5NzAzODhmZmY5OWUzYzdiMDIxNmY4ZGExNWE4M2E0MjczMzllNjQ5NDAyMWQwZjkyNTViYTNlIn0%3D; laravel_session=eyJpdiI6IjZPY1ZoQ09GV1Y5NWRJblpoSXFQZmc9PSIsInZhbHVlIjoiZlFPdkFlMGZMNDY4T3FhdGZrZ3BwNGxBS0JtXC9pTWcyOXdXbXYxUjZCWmhyWStHS2I5b0o5TUthcTdGNTI2OEw1S0NGTlNzRGZcL2JZb2VlRXhkcVVzdz09IiwibWFjIjoiM2MzMGYwNWQ1ZmYxZGZmNzRkY2NlZTQ3NWI4ODNhZGNiZDcwNjliNWE0NWVhZmU0YjhlMzI0ZTBmODVjOGE2YiJ9"}, "body": {"id": "586"}}, "response": {"status": 200, "headers": {"date": "Fri, 08 Aug 2025 07:31:55 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "vary": "Accept-Encoding", "cache-control": "max-age=43200, public, s-maxage=43200, no-cache", "expires": "Fri, 08 Aug 2025 19:31:55GMT", "set-cookie": "XSRF-TOKEN=eyJpdiI6IlZRaVdQQ1VzTVl2UW0ycWpJb1wvZ1hBPT0iLCJ2YWx1ZSI6Ik1HUDlCeWtRY2w0SXNxNUFTeTRiTGduYkpBQlAyT2dTbm5mdk40dVlCRWVtRGpnUThucXpoK1FjeFRcL2lyRUlHVTVZWlYyXC9mVGxwVUFKUEZzOUUzeEE9PSIsIm1hYyI6ImRmNzNmODZmMzhlNzNjOTgwODRmMTdmNGJkMGYyZmMyOTg3NDMyMjU4OTFiMGRjYTg3NmYwOWQwM2ZiZGY4NWQifQ%3D%3D; expires=Fri, 08-Aug-2025 09:31:55 GMT; Max-Age=7200; path=/; Secure; HttpOnly;; Secure; HttpOnly; SameSite=Lax, laravel_session=eyJpdiI6Ik5nblJuOW5zT0daTDI1VXB3T1REelE9PSIsInZhbHVlIjoiQTBpMFwvTCt0SkZVb0VXSmo1UHVHb05RYjZTU211R0hjMFNOK0ZzWEpHbFd1VVk0Y0RQdm9cLzVLOFRzT0VQUWJDaW5OaUdYY0Y5ajRcL0JUOWJGeUp1WWc9PSIsIm1hYyI6ImYwMWVhYWNkZjlkNTZlY2I0OGU5YTY2NWE5MDFiZjUzZTI0YzFiYTlkMGQ3N2JlYzY0OGIzZGVhYzk1NmRmMjcifQ%3D%3D; path=/; Secure; HttpOnly;; secure; httponly; SameSite=Lax", "x-vietec": "PMS-70", "x-frame-options": "DENY", "content-security-policy": "frame-ancestors 'self';", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-xss-protection": "1; mode=block"}, "body": "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"}}