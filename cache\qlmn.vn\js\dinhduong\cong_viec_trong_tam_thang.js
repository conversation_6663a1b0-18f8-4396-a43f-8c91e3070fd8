angular_app_report.controller('congViecTrongTamThangController', ['$scope', '$compile', function ($scope) {
    $('.overlay').css('visibility', 'visible');

    $scope.module = 'cong_viec_trong_tam_thang';
    $scope.unitSignTypes = { schoolAdmin: 'sign_bangiamhieu' };
    $scope.unitSigns = {}

    $scope.sign = async function (type) {
        let month = $('#month').val();
        let courseId = $('#course_id').val();

        if (isNaN(month)) {
            alert('Vui lòng chọn tháng!');
            return;
        }

        if (isNaN(courseId)) {
            alert('Vui lòng chọn Lớp!');
            return;
        }

        let response = await saveUnitSignConfig({
            module: $scope.module,
            type: type,
            month: month,
            courseId: courseId
        });

        if (response.success) {
            await fetchUnitSignConfig({module: $scope.module, month, courseId});

            alert(response.message);

            return;
        }

        alert(response.message);
    }

    const fetchUnitSignConfig = async function ({ module, projectId = null, groupId = null, courseId = null, date = null, month = null }) {
        const response = await findUnitSignConfig({module, projectId, groupId, courseId, date, month});

        if (response.success) {
            let signData = JSON.parse(response.data['sign_data']);

            Object.keys(signData).forEach((key) => {
                $scope.$apply(function () {
                    $scope.unitSigns[key] = {
                        url: buildUnitSignImageUrl($CFG.remote.base_url, $CFG.unit_id, signData[key]['filename']),
                        title: signData[key]['time'],
                        fullname: signData[key]['fullname'],
                    }
                });
            });
        }
    }

    let month = $('#month').val();
    let course_id = $('#course_id').val();

    if(!isNaN(month) && !isNaN(course_id)) {
        fetchUnitSignConfig({module: $scope.module, month, course_id}).then(r => console.log(r));
    }

    $scope.exportWord = function() {
        $('.overlay').css('visibility', 'visible');
        var formData = new FormData();

        formData.append('course_id', $('#course_id').val());
        formData.append('rpt_type', 3);
        
        $.ajax({
            url: $CFG.remote.base_url + '/doing/dinhduong/class_gr_quality/wordCongViecTrongTamThang',
            type: 'POST',
            xhrFields: {
                responseType: 'blob'
            },
            data: formData,
            processData: false,
            contentType: false,
            success: function(response, status, xhr) {
                var filename = ""; 
                var disposition = xhr.getResponseHeader('Content-Disposition');
                
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    var regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = regex.exec(disposition);
                    if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
                }
                
                var blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
                
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    var link = document.createElement('a');
                    var url = window.URL.createObjectURL(blob);
                    link.href = url;
                    link.download = filename || 'download.docx';
                    document.body.appendChild(link);
                    link.click();
                    
                    setTimeout(function() {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                    }, 100);
                }
            }, 
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus + ': ' + errorThrown);
            }, 
            complete: function() {
                $('.overlay').css('visibility', 'hidden');
            }
        });
    }

    $(document).ready(function() {
        $('.overlay').css('visibility', 'hidden');

        $('#save-btn').click(function() {
            // $('.overlay').css('visibility', 'visible');
            var formData = new FormData();

            formData.append('course_id', $('#course_id').val());
            formData.append('month', $('#month').val());
            formData.append('rpt_description', $('#rpt_description').val().trim());
            formData.append('rpt_value', $('#rpt_value').val().trim());
            formData.append('rpt_type', 3);

            $.ajax({
                url: $CFG.remote.base_url + '/doing/dinhduong/class_gr_quality/saveCongViecTrongTamThang',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    alert(data.message);
                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus + ': ' + errorThrown);
                }, complete: function() {
                    $('.overlay').css('visibility', 'hidden');
                }
            });
        })
        
        $('.loadData').on('change', function() {
            window.location = '/dinhduong/class_gr_quality/congViecTrongTamThang?course_id=' + $('#course_id').val() + '&month=' + $('#month').val();
        });
    });
}]);