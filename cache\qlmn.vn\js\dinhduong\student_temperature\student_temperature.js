$.student_temperature = {
    module: 'student_temperature',
    lock_to: 0,
    init: function(date) {
        process('dinhduong/student_temperature/special_catagory',{date: date},function(resp){
            $.student_temperature.initAngular(resp,date, function (scope) {});
        },null);
    }, initAngular: function(resp, date, callback){
        setTimeout(function(){
            angular.element($('#tbl_container_student_temperature')).scope().$apply(function(scope){
                callback(scope);
                scope.student_temperature = {
                    date: date,
                    select: {},
                };
                scope.student_temperature.grades = resp.grades;
                scope.student_temperature.gvcn = resp.gvcn;
                scope.student_temperature.days = resp.days;
                scope.student_temperature.months = resp.months;
                scope.student_temperature.firstDays = resp.firstDays;
                scope.student_temperature.today = resp.today;
                scope.student_temperature.select.month = resp.month;
                scope.student_temperature.selected = {};
                if (resp.gvcn!=1) {
                    scope.student_temperature.select.course_id = resp.course.course_id;
                    scope.student_temperature.select.course_name = resp.course.course_name;
                    scope.student_temperature.select.grade_id = resp.course.grade_id;
                    scope.student_temperature.select.grade_name = resp.course.grade_name;
                }
                scope.std = {};
                scope.std.col = {
                    stt: 40,
                    tenlop: 150,
                    tongngay: 60,
                    day_none: 45,
                    day: 70
                }
                scope.std.row_head = {
                    over: 60,
                    under: 35
                }
                scope.student_temperature.getStyleTable = function(){
                    var rs = {
                        'margin-top':65,
                        'margin-bottom': 30,
                        width:0
                    };
                    rs.width += scope.sum(scope.std.col);
					if(scope.student_temperature.daysOfMonth){
						angular.forEach(scope.student_temperature.daysOfMonth, function(item){
							if(item.holiday==1){
								rs.width += scope.std.col.day_none;
							}else{
								rs.width += scope.std.col.day;
							}
						})
					}
                    return rs;
                }

                scope.student_temperature.load = function(date){
                    var course_id = scope.student_temperature.select.course_id;
                    if(!course_id) return;
                    if(date){
                        scope.student_temperature.date = date;
						var dstr = date.split("/");
						scope.student_temperature.select.month = parseInt(dstr[1]);
                    }else{
						var month = $("#attendance_monthbox option:selected").val();
						month = month.replace("number:", "");
						date = scope.student_temperature.firstDays[month];
                    }
                    
                    var keyword = scope.student_temperature.keyword;
                    $.student_temperature.loadWeek(scope, date, course_id, keyword);
                }
                scope.student_temperature.load(null);
                scope.student_temperature.attendFocus = function(attend, day){
                    attend['day_old'+day] = attend['day'+day];
                }
                scope.student_temperature.editAttendance = function(attend, item){
                    if(Number(item.day) < 10){
                        item.day = Number(item.day);
                    }
                    if(Number(item.month) < 10){
                        item.month = Number(item.month);
                    }
					if(attend['day_old'+item.day]!=attend['day'+item.day]){
						process($CFG.project+'/student_temperature/edit',{weeks: item, attend: attend},function(resp){
							// TO DO
						},function(){
							attend['day'+item.day] = attend['day_old'+item.day];
						},false);
					}
                }

                scope.student_temperature.dateEqual = function(item,date){
                    var d = [];
                    if(Number(item.day) < 10){
                        d.push("0"+Number(item.day));
                    }else{
                        d.push(item.day);
                    }
                    if(Number(item.month) < 10){
                        d.push("0"+Number(item.month));
                    }else{
                        d.push(item.month);
                    }
                    d.push(item.year);
                    if(d.join('/') == date){
                        return true;
                    }
                }
				
				scope.student_temperature.tongcong = function(item){
                    var rs = 0;
                    if(scope.student_temperature.attendances){
                        angular.forEach(scope.student_temperature.attendances, function(attend, index){
                            if (attend['day'+item.day]!=0 && attend['day'+item.day]!=null && item.holiday==0){
                                rs += parseInt(attend['day'+item.day]);
                            }
                        });
                    }
                    return rs;
                }

                scope.student_temperature.selectItem = function(attend){
                    scope.student_temperature.select.attend = attend;
                };

                scope.student_temperature.selectLock = function(item){
                    var d = [];
                    if(parseInt(item.day) < 10){
                        d.push("0"+Number(item.day));
                    }else{
                        d.push(item.day);
                    }
                    if(parseInt(item.month) < 10){
                        d.push("0"+Number(item.month));
                    }else{
                        d.push(item.month);
                    }
                    d.push(item.year);
                    scope.student_temperature.date = d.join('/');
                };
                scope.class_item = function(item){
                    if(item.holiday != undefined && item.holiday == 1){
                        return 'bg-success';
                    }
                    return '';
                }
                scope.student_temperature.course_change = function(){
                    var course_id = scope.student_temperature.select.course_id;
                    var month = scope.student_temperature.select.month;
                    var keyword = scope.student_temperature.keyword;
                    date = scope.student_temperature.firstDays[month];
                    $.student_temperature.loadWeek(scope, date, course_id, keyword);
                };
            });
        },0);
    },loadWeek: function(scope, date, course_id, keyword){
        process($CFG.project+'/student_temperature/list',{date, course_id, keyword, async: true},function(resp){
            statusloading();
            scope.$apply(function(){
                scope.student_temperature.daysOfMonth = resp.daysOfMonth;
                scope.student_temperature.today = resp.today;
				scope.student_temperature.today_short = resp.today_short;
                scope.student_temperature.temperatures = resp.temperatures;
                angular.element('#tbl_container_student_temperature').ready(function () {
                    statusloadingclose();
                });
            });
        });
    }
}
