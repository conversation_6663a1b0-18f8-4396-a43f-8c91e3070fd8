$.dish_storage = {
    module: 'dish_storage',
    nutritions: null,
    dish_storagetypes: null,
    init:function(){
        process('dinhduong/dish_storage/getCat','',function(resp){
            // var rows = JSON.parse(resp);
            $.each(resp, function (index, value) {
                $.dish_storage[index] = value;
            });
            $.dish_storage.arrGroups = [];
            $.each($.dish_storage.groups, function (index, value) {
                $.dish_storage.arrGroups.push({id: value.id, name: value.name})
            });
            $.dm_datagrid.combobox('group_ids', $.dish_storage.arrGroups,{
                valueField: 'id',
                textField: 'name',
                value:[],
                // panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_group = row.id;
                    });
                    $.dish_storage.doSearch();
                },
                queryParams: {},
                width: 200,
                height : 25
            });
            $.dm_datagrid.combobox('area_ids',$.dish_storage.areas,{
                valueField: 'id',
                textField: 'name',
                value:[],
                panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_area = row.id;
                    });
                    $.dish_storage.doSearch();
                },
                queryParams: {},
                width: 200,
                height : 25
            });
            $.dm_datagrid.combobox('category_ids',$.dish_storage.categories,{
                valueField: 'id',
                textField: 'name',
                value:[],
                panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_category = row.id;
                    });
                    $.dish_storage.doSearch();
                },
                queryParams: {},
                width: 200,
                height : 25
            });

            $.dish_storage.list();
        },null,false)
    },
    comboboxx : function(el){
        var self = this;
        var input = $(`<input class="form-control" id="ingredient-new" placeholder="Chọn nguyên liệu" width="100%" >`);
        $("#div-ingredient").html('').append(input);
        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/'+self.module+'/';
        var ids = [];
        var scope = angular.element($(el)).scope();
        $.each(scope.dish_storage.foods,function(food_id,food){
            ids.push(food_id); 
        });
        var option = {
            valueField:'food_id',
            textField:'name',
            // panelHeight:'auto',
            mode: 'remote',
            onSelect: function(row, element) {
                input.combobox('destroy');
                
                // $.dish_storage.foods.lists[row.id] = row;
                scope.$apply(function(scope){
                    scope.dish_storage.foods[row.food_id] || (scope.dish_storage.foods[row.food_id] = row);
                    $.dish_storage.comboboxx(el);
                }); 
            },
            queryParams: {ids:ids},
            width: 150,
            height: 30
        };
       
        $.dm_datagrid.combobox(input,url+'foods',option);
        
        
    },
    list: function(is_public) {
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dish_storage.initAngular();
        $.dm_datagrid.init(
            urls.join('/'), 
            this.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
                { title:'Mã món ăn', field:'id', width:30, sortable:true },
                { title:'Tên món ăn', field:'name', width:50, sortable:true },
                { title:'Calo / 1 trẻ', field:'sum_calo', width:30, sortable:true },
                { title:'Nhóm tuổi', field:'group_name', width:80, sortable:true },
                { title:'Danh mục món ăn', field:'category_name', width:80, sortable:true },
                { title:'Vùng miền', field:'area_name',width:80, sortable:true }
            ]],
            {
                view: detailview,
                detailFormatter:function(index,row){
                    // console.log(row);
                    var ingredients = JSON.parse(row.ingredient);
                    var html = `<div class="detail-dish-storage">
                                <div class="div-vari">
                                    <div class="name-dds"><label>Tên món ăn : </label><p>`+row.name+`</p></div>
                                    <div class="name-dds"><label>Mô tả : </label><p>`+row.description+`</p></div>
                                    <div class="name-dds"><label>Cách chế biến : </label><p>`+row.recipe+`</p></div>
                                </div>
                                `;
                    html +=`    <div class="div-vari-table">
                                <table class="table-vari">
                                    <tr class="tr-title-vari">
                                        <th class="width-60">TÊN THỰC PHẨM</th>
                                        <th class="width-20">LƯỢNG 1 TRẺ </br> (g)</th>
                                        <th class="width-20">LƯỢNG 1 TRẺ THEO </br> DVT</th>
                                    </tr>
                                    `;
                    $.each(ingredients, function( index, value ) {
                        html +=`<tr class="tr-content-vari">
                                    <th class="width-60">`+value.name+`</th>
                                    <th class="width-20">`+value.quantity+`</th>
                                    <th class="width-20">`+value.quantity_for_measure+`</th>
                                </tr>`;
                    });
                    html +=`</table>`;
                    html +=`</div>`;
                    html +=`</div>`;
                    return html;
                },onExpandRow: function(index,row){

                    
                }
            }
        );
      }, buildDetailView: function(index, row, nutritions_data, nutritions_define){
        var ddv = $('#tbl_dish_storage').datagrid('getRowDetail',index).find('div.detail-view-content');
        if($(ddv).attr('opened')) {
            return;
        }
        $(ddv).attr('opened','true');
        var html = [];
        $.each(nutritions_define, function(i,nutr){});
        ddv.append(html.join(''));
        $('#tbl_dish_storage').datagrid('fixDetailRowHeight',index);
    },
    cleanSearch: function(){
        angular.element($("#header-search")).scope().$apply(function(scope){
            scope.keysearch_unit = '';
            $('input#unit').val('');
            scope.keysearch_name = '';
            $('input#name').val('');
            scope.keysearch_group = '';
            $('input#group_ids').combobox('clear');
            scope.keysearch_area = '';
            $('input#area_ids').combobox('clear');
            scope.keysearch_category = '';
            $('input#category_ids').combobox('clear');
            $.dish_storage.doSearch();
        });
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                scope.dish_storage = {}
                scope.dish_storage.categories = $.dish_storage.categories;
                scope.dish_storage.groups = $.dish_storage.groups;
                scope.dish_storage.areas = $.dish_storage.areas;
                scope.dish_storage.foods = {};

                scope.dish_storage.onChangeNC = function(){
                    angular.forEach(scope.dish_storage.foods,function(food,index){
                        scope.dish_storage.onChangeOneG(food);
                    });
                }

                scope.dish_storage.keysearch_unit = 0;
                scope.dish_storage.onChangeKeySearchUnit = function(unit){
                    if(scope.dish_storage.keysearch_unit === 0){
                        scope.dish_storage.keysearch_unit = 1;
                        setTimeout(function(){
                            if(scope.dish_storage.keysearch_unit === 1){
                                $.dish_storage.doSearch();
                                scope.dish_storage.keysearch_unit = 0;
                            }
                        }, 700);
                    }
                };


                scope.dish_storage.keysearch_name_time = 0;
                scope.dish_storage.onChangeKeysearchName = function(keysearch_name){
                    if(scope.dish_storage.keysearch_name_time == 0){
                        scope.dish_storage.keysearch_name_time = 1;
                        setTimeout(function(){
                            if(scope.dish_storage.keysearch_name_time == 1){
                                $.dish_storage.doSearch();
                                scope.dish_storage.keysearch_name_time = 0;
                            }
                        }, 700);
                    }
                }
                scope.dish_storage.onChangeOtherUnit = function(){
                    $.dish_storage.doSearch();
                }
                scope.dish_storage.onChangeEAG = function(item){
                    if(scope.dish_storage.number_child != undefined||scope.dish_storage.number_child != ''){
                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish_storage.number_child);
                        item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                        item.buy_a_group = parseFloat(item.eat_a_group)+item.extrude_factor_ch;
                        item.buy_a_dvt = parseFloat(item.eat_a_group) + item.extrude_factor_ch*1000/item.gam_exchange;
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = item.quantity*item.nutritions.calo/100;

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                }
                scope.dish_storage.onChangeOneG = function(item){
                    if(scope.dish_storage.number_child != undefined||scope.dish_storage.number_child != ''){
                        item.eat_a_group = scope.rounds(item.quantity/1000*scope.dish_storage.number_child);
                        item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                        item.buy_a_group =  scope.rounds(parseFloat(item.eat_a_group)+item.extrude_factor_ch);
                        item.buy_a_dvt = scope.rounds(parseFloat(item.eat_a_group) + item.extrude_factor_ch*1000/item.gam_exchange);
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = item.quantity*item.nutritions.calo/100;

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                } 
                scope.dish_storage.onChangeBAG = function(item){
                    if(scope.dish_storage.number_child != undefined||scope.dish_storage.number_child != ''){
                        item.extrude_factor_ch = parseFloat(item.extrude_factor) + 100;
                        item.eat_a_group = item.buy_a_group*100/item.extrude_factor_ch;
                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish_storage.number_child);
                        item.buy_a_dvt = item.buy_a_group*1000/item.gam_exchange;
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = item.quantity*item.nutritions.calo/100;

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                }
                scope.dish_storage.onChangeBAD = function(item){
                    if(scope.dish_storage.number_child != undefined||scope.dish_storage.number_child != ''){
                        item.extrude_factor_ch = parseFloat(item.extrude_factor) + 100;
                        item.buy_a_group = item.buy_a_dvt*item.gam_exchange/1000;
                        item.eat_a_group = item.buy_a_group*100/item.extrude_factor_ch;
                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish_storage.number_child);
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = item.quantity*item.nutritions.calo/100;

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                }
                scope.dish_storage.onChangeOneDVT = function(item){
                    if(scope.dish_storage.number_child != undefined||scope.dish_storage.number_child != ''){
                        item.eat_a_group = scope.rounds(item.quantity_for_measure*item.gam_exchange/1000*scope.dish_storage.number_child);
                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish_storage.number_child);
                        item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                        item.buy_a_group = parseFloat(item.eat_a_group)+item.extrude_factor_ch;
                        item.buy_a_dvt = parseFloat(item.eat_a_group) + item.extrude_factor_ch*1000/item.gam_exchange;

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                }
                scope.dish_storage.sum_calo = function(item){
                    var sum_calo = 0;
                    angular.forEach(scope.dish_storage.foods,function(food,idex){
                        sum_calo += food.quantity*food.nutritions.calo/100; 
                    });
                    return sum_calo;
                }
                scope.dish_storage.delFood = function(id){
                    var tam = {};
                    $.each(scope.dish_storage.foods,function(food_id,food){
                        if(food_id != id){
                            tam[food_id] = food; 
                        }
                    });
                    scope.dish_storage.foods = tam;
                    $.dish_storage.comboboxx('#frm-dish-storage-add');
                }
                scope.dish_storage.delAllFood = function(){
                    scope.dish_storage.foods = {};
                    $.dish_storage.comboboxx('#frm-dish-storage-add');
                }
                scope.rounds = function(value) {
                    if(typeof value != 'number') {
                        value = parseFloat(value);
                    }
                    value = Math.round(value*1000)/1000;
                    return value;
                }

            });
        },0);
    }, showAddForm: function(callback) { 
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'add',
                title:'Thêm mới',
                showButton: false,
                size: size.wide,
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
                        $.dish_storage.angular(element,resp,function(){
                        });
                        
                        $.dish_storage.comboboxx(element);
                    })
                },
                buttons: [{
                    id: 'btn-save',
                    icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn-primary',
                    action: function(dialogRef){

                        var data = {
                            foods: $.dish_storage.getDataForm(dialogRef.getModalBody()),
                            together: arrayToJson(getSubmitForm(dialogRef.getModalBody(),true))
                        };
                        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/dish_storage/add';
                        process(url,data,function(resp){
                            if(resp.result == "success"){
                                dialogRef.close();
                                if(typeof callback === 'function'){
                                    callback(resp);
                                }
                                $("#tbl_"+self.module).datagrid('reload');
                            }
                        });
                    }
                }]
            },
            
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
    }, showEditForm: function() {
        var self = this;
        var row = $("#tbl_"+self.module).datagrid('getSelected');
        if(row != null) {
            $.dm_datagrid.showEditForm(
                {
                    module: $CFG.project+'/'+self.module,
                    action:'edit',
                    title:'Chỉnh sửa',
                    size: size.wide,
                    content: function(element){
                        // loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
                        process($CFG.project+'/'+self.module+'/editForm', {id: row.id}, function(resp){
                            // console.log(resp);
                            $.dish_storage.angular(element,resp.html,function($scope){
                                $scope.dish_storage.row = resp.row;
                                $scope.dish_storage.foods = JSON.parse(row.ingredient);
                                angular.forEach($scope.dish_storage.foods,function(food,index){
                                    $scope.dish_storage.number_child = food['number_child'];
                                })
                                $.dish_storage.comboboxx('#frm-dish-storage-add');
                                angular.forEach($scope.dish_storage.areas,function(area,index){
                                    // console.log(resp.row.aea_ids);
                                    if(in_array(area.id+'',resp.row.area_ids)){
                                        area.selected = true;
                                    }else{
                                        area.selected = false;
                                    }

                                });
                                angular.forEach($scope.dish_storage.groups,function(group,index){
                                    if(in_array(group.id+'',resp.row.group_ids)){
                                        group.selected = true;
                                    }else{
                                        group.selected = false;
                                    }
                                });
                                angular.forEach($scope.dish_storage.categories,function(category,index){
                                    if(in_array(category.id+'',resp.row.category_ids)){
                                        category.selected = true;
                                    }else{
                                        category.selected = false;
                                    }
                                });
                                $.dish_storage.comboboxx(element);

                            });
                        })
                    },
                    buttons: [{
                    id: 'btn-save',
                    icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn-primary',
                    action: function(dialogRef){

                        var data = {
                            foods: $.dish_storage.getDataForm(dialogRef.getModalBody()),
                            together: arrayToJson(getSubmitForm(dialogRef.getModalBody(),true))
                        };
                        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/dish_storage/edit';
                        process(url,data,function(resp){
                            if(resp.result == "success"){
                                dialogRef.close();
                                if(typeof callback === 'function'){
                                    callback(resp);
                                }
                                $("#tbl_"+self.module).datagrid('reload');
                            }
                        });
                    }
                }]
                },
                function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                }
            );
        }else{
            $.messager.alert('Thông báo.', 'Phải chọn một dòng!');
        }
       
    },
    showShareForm: function () {
        var self = this;
        var ids = [];
        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
            ids.push(row.id);
        });

        if (ids.length != 0) {
            var captcha = ids.length < $CFG.record_delete_show_captcha ? '' : $CFG.dialog_captcha('copy_dishs');
            $.messager.confirm('Xác nhận', '<div style="font-size: 14px;" class="text-danger">Bạn có chắc chắn muốn copy món ăn đã chọn?</div>' + captcha, function (r) {
                if (r) {
                    process('dinhduong/dish_storage/share', {
                        ids: ids,
                        captcha: $('[name="copy_dishs_captcha"]').val()
                    }, function (resp) {
                        if (resp.result == 'success') {
                            alert("Bạn đã sao chép món ăn thành công");
                            $("#tbl_" + self.module).datagrid('reload');
                        }
                    });
                }
            });
        } else {
            $.messager.alert('Thông báo!', 'Phải chọn ít nhất một món ăn để tiến hành sao chép!');
        }
    }, 
    angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    },getDataForm: function(element){
        var datas = [];
        var scope = angular.element($(element)).scope();
        angular.forEach(scope.dish_storage.foods,function(food,index){
            food['number_child'] = scope.dish_storage.number_child;
            food['sum_calo'] = scope.dish.sum_calo();
        });
        return JSON.stringify(scope.dish_storage.foods);
    },del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Chắc chắn xóa ?</div>', function(r){
            if (r){
                $.dm_datagrid.del($CFG.project+'/'+self.module,ids,function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                })
            }
        });
    },
    doSearch: function(){
        var self = this;
        $.dm_datagrid.doSearch('tbl_'+self.module,{name:"contains",group_ids:"comma_contains",area_ids:"comma_contains",category_ids:"comma_contains", other_unit:"other", unit:"other"},'and');
    },
    exportXML: function () {
        var self = this;
        var ids = [];
        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
            ids.push(row.id);
        });
		var group_id = $('input#group_ids').val();
        if (ids.length !== 0 && group_id>=1) {
            window.open("/dinhduong/dish_storage/exportXML?group_id="+group_id+"&ids=" + ids.join(','));
        } else {
            $.messager.alert('Thông báo!', 'Bạn hãy chọn 1 độ tuổi & món ăn để xuất!');
        }
    },
};
