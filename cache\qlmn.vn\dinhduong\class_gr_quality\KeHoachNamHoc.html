<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/common.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/main-report.css?_=1400892143" />
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
	<script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.0/xlsx.full.min.js"></script>

	<style type="text/css">
		*{
			font-family: Nunito_Regular;
			font-size: 13 !important;
		}
		@media  print {
			body > .panel {
				display: none !important;
			}
		}
	</style>
	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
	<script type="text/javascript">
		$CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'dinhduong',
			school_point: +'1',
			unit_id: parseInt('51461'),
            school_points: +'1',
            school_point_together: parseInt('0'),
            is_view_csdl_nganh: true,
            administrator: 1,//parseInt('0'),
			dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			level: '4',
      	};
	</script>
	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>

	<script src="http://localhost:3000/js/common.js?_=606937553"></script>
	<script type="text/javascript">
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
		angular_app_report = angular_app;
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=376131358754"></script>
	<script src="http://localhost:3000/js/dinhduong/main-angular-report.js?_=812292793"></script>
	<script type="text/javascript" src="http://localhost:3000/js/library.js?v=164325689"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController">
		
<style>
    .d-flex {
        display: flex;
    }
    .pe-1 {
        padding-left: 1rem !important;
    }
    .pe-2 {
        padding-left: 2rem !important;
    }
    .float-right {
        float: right !important;
    }
</style>
<div ng-controller="KeHoachNamHocController">
    <div style="border: 1px dotted #ccc; margin-top: 20px;">
      <b>Chọn lớp: </b><select id="course_id" ng-model="courseId" style="width:auto; height: 28px; display: inline" id="course_id" onchange="changeGrade(this)"  >
        <option value="" >- Chọn lớp học -</option>
              </select>
      <button ng-if="courseId" class="btn btn-primary" ng-click="saveReport()">Lưu lại</button>
      <a class="btn btn-primary glyphicon glyphicon-download-alt float-right" ng-if="courseId" href="javascript:void(0);" ng-click="export()"> Word</a>
    </div>
    
    <body style="margin-right:80px; margin-left:80px; font-size:18px" ng-controller="mainContentController">
      <h3 class="text-bold" style="text-align: center; color:#0652DD">KẾ HOẠCH NĂM HỌC 2026 - 2027</h3>
      <div class="container">
        <div class="row">
            <div class="col-md-12">
                <span class="text-bold">A. ĐẶC ĐIỂM - TÌNH HÌNH</span>
            </div>
            <div class="col-md-12">
                <p class="text-bold">
                    1. Thuận lợi
                </p>
                <p>
                    <textarea class="form-control" name="report-value" id="A_1" rows="5"></textarea>
                </p>
            </div>
            <div class="col-md-12">
                <p class="text-bold">
                    2. Khó khăn
                </p>
                <p>
                    <textarea class="form-control" name="report-value" id="A_2" rows="5"></textarea>
                </p>
            </div>
            <div class="col-md-12">
                <span class="text-bold">B. NHỮNG CHỈ TIÊU VÀ YÊU CẦU CẦN ĐẠT</span>
            </div>
            <div class="col-md-12">
                <p class="text-bold d-flex">
                    <span>1. Số lượng: </span>&nbsp;<input class="form-control w200" type="text" name="report-value" placeholder="Nhập số lượng" id="B_1" value="">
                </p>
                <p class="text-bold pe-1">a. Tình hình sức khỏe trẻ đầu năm</p>
                <div class="row pe-2">
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - Bình thường:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_2" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_3" value="" >%
                        </p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - SDD thể còi cọc:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_4" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_5" value="" >%
                        </p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - SDD thể thấp còi:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_6" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_7" value="" >%
                        </p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - Thừa cân, béo phì:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_8" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_9" value="" >%
                        </p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - SDD thể nhẹ cân:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_10" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_11" value="" >%
                        </p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p class="d-flex">
                            - Khuyết tật:&nbsp;<input class="form-control w70" type="number" name="report-value" id="B_12" value="" >&nbsp; tỷ lệ&nbsp; <input class="form-control w70" type="number" name="report-value" id="B_13" value="" >%
                        </p>
                    </div>
                    <p class="text-bold pe-1">b. Các chỉ tiêu phấn đấu</p>
                    <p class="pe-2">VỀ DANH HIỆU: </p>
                    <p class="d-flex pe-2">
                        - Danh hiệu lớp (tiên tiến / xuất sắc):&nbsp;
                        <input type="text" name="report-value" name="" id="B_14" class="form-control w200" value="">
                    </p>
                    <p class="pe-2">- Danh hiệu của giáo viên:&nbsp;<textarea name="report-value" id="B_15"class="form-control" rows="5"></textarea></p>
                    <p class="pe-2">- Danh hiệu của bé:&nbsp;<textarea name="report-value" id="B_16"class="form-control" rows="5"></textarea></p>
                    <p class="pe-2">VỀ CHĂM SÓC - NUÔI DƯỠNG:&nbsp;<textarea name="report-value" id="B_17"class="form-control" rows="5"></textarea></p>
                    <p class="pe-2">VỀ GIÁO DỤC:&nbsp;<textarea name="report-value" id="B_18"class="form-control" rows="5"></textarea></p>
                    <p class="text-bold">2. Chất lượng</p>
                    <p class="text-bold pe-1">a. Chăm sóc nuôi dưỡng: (Phòng chống SDD, vệ sinh an toàn thực phẩm, phòng chống tai nạn thương tích, phòng chống bạo lực học đường, phòng chống dịch bệnh...)</p>
                    <p class="pe-1"><textarea name="report-value" id="B_19" class="form-control" rows="5"> </textarea></p>
                    <p class="text-bold pe-1">b. Giáo dục: (Xây dựng kế hoạch; xây dựng môi trường giáo dục, nâng cao chất lượng các tổ chức các hoạt động; sử dụng bộ chuẩn PITE 5 tuổi; tăng cường tiếng việt cho trẻ em dân tộc thiểu số)</p>
                    <p class="pe-1"><textarea name="report-value" id="B_20" class="form-control" rows="5"> </textarea></p>
                    <p class="text-bold">3. Các chuyên đề hoạt động khác (tích hợp lồng ghép các nội dung về sử dụng năng lượng tiết kiệm, hiệu quả về bảo vệ tài nguyên, môi trường ứng dụng CNTT; công tác tuyên truyền; sáng kiến kinh nghiệm; tự bồi dưỡng...)</p>
                    <p class=""><textarea name="report-value" id="B_21" class="form-control" rows="5"></textarea></p>
                    <p class="text-bold">C. NHỮNG BIỆN PHÁP CHÍNH (về công tác chăm sóc nuôi dưỡng giáo dục ứng dụng CNTT, tự bồi dưỡng, thực hiện công bằng trong giáo dục, công tác tuyên truyền, ...)</p>
                    <p class=""><textarea name="report-value" id="B_22" class="form-control" rows="5"></textarea></p>
                </div>
            </div>
            <div class="col-md-12">
                <p class="float-right">
                    <input type="text" name="report-value" id="B_23" placeholder="Địa điểm, Ngày ... tháng ... năm ..." class="form-control w350" value="">
                </p>
            </div>
            <br>
            <br>
            <div class="col-md-6 col-sm-6 text-center">
                
                <div class="sign-wrapper-parent">
                    <div class="sign-wrapper">
                        <span class="sign-label">Ban giám hiệu</span>

                        <span class="sign-sub-label">
                            (Ký, Họ tên)
                                                            <i class="fa fa-pencil no-print text-info sign-action" title="Ký"
                                   ng-click="sign(unitSignTypes.schoolAdmin)"></i>
                                                    </span>

                        <img src="{{ unitSigns[unitSignTypes.schoolAdmin]['url'] }}"
                             title="{{ unitSigns[unitSignTypes.schoolAdmin]['title'] }}"
                             class="sign-image"
                        >

                        <span class="text-capitalize" style="font-weight: bold; margin-top: 12px; color: #555;"
                              ng-bind="unitSigns[unitSignTypes.schoolAdmin]['fullname']"></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-sm-6 text-center">
                <div class="sign-wrapper-parent">
                    <div class="sign-wrapper">
                        <span class="sign-label">Giáo viên</span>

                        <span class="sign-sub-label">
                            (Ký, Họ tên)
                                <i class="fa fa-pencil no-print text-info sign-action" title="Ký"
                                   ng-click="sign(unitSignTypes.secretary)"></i>
                        </span>

                        <img src="{{ unitSigns[unitSignTypes.secretary]['url'] }}"
                             title="{{ unitSigns[unitSignTypes.secretary]['title'] }}"
                             class="sign-image"
                        >

                        <span class="text-capitalize" style="font-weight: bold; margin-top: 12px; color: #555;"
                              ng-bind="unitSigns[unitSignTypes.secretary]['fullname']"></span>
                    </div>
                </div>
            </div>
        </div>
      </div>
    </body>
</div>
<style>
    .sign-wrapper {
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
        width: 150px;
        /*margin-left: 25%;*/
        /*margin-right: calc(25% + 100px) !important;*/
    }
    .sign-wrapper-parent {
        display: flex;
        width: 100%;
        justify-content: space-around;
        padding: 0 25% 0 25%;
    }
    .sign-image {
        width: 150px;
        height: auto;
    }

    .sign-label {
        font-weight: bold;
        font-size: 15px;
        color: #333;
    }

    .sign-sub-label {
        font-size: 14px;
        color: #666;
    }
    .sign-action {
        margin-left: 10px;
        cursor: pointer;
    }
</style>
<script type="text/javascript">
    var data = {
        courseId: '',
    }
    function changeGrade(f) {
        window.location = '/dinhduong/class_gr_quality/KeHoachNamHoc?course_id=' + f.value + '&rpt_type=2';
    }
</script>
<script src="http://localhost:3000/js/dinhduong/ke_hoach_nam_hoc.js?_=842679454"></script>
	</div>
</body>
<style>
	@media  print {
		.btn {
		  display: none !important;
		}
	}
</style>
<script>
	var apiUrl = $CFG.remote.base_url + '/doing/admin/user/';
	var url = $CFG.remote.base_url + '/images/signs/' + $CFG.unit_id + '/';
	function getSignConfig(date, module, group_id) {
		var params = {
			module : module,
			date : date,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'getSignConfig',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				if (response.success) {
					let data = JSON.parse(response.data.sign_data);
					Object.keys(data).forEach((v) => {
						$('#'+v).attr('src',data[v]['filename']) ;
						$('#'+v).attr('title',data[v]['time']) ;
					})
					let eleBrs = document.querySelectorAll(".break_line");
					eleBrs.forEach(v => {
						v.style.display = 'none';
					});
				}
			}
		});
	}

	function getSignWithType(date, module, group_id, type) {
		var params = {
			module : module,
			date : date,
			type : type,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'signing',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				alert(response.message)
				if (response.success) {
					$('#'+type).attr('src',response.signInfo.filename);
					$('#'+type).attr('title',response.signInfo.time);
				}
			}
		});
	}
</script>
</html>
