$.storage_inventory = {
    module: 'storage_inventory',
    id: '', /*Mã project*/
    measures: null,
    warehouses: null,
    init: function(id,project) {
        var self = this;
        self.id = id;
        /*<PERSON><PERSON>i đơn vị t<PERSON>h và danh sach kho trước*/
        process($CFG.project+'/'+self.module+'/special_categories',{async: true},function(resp){
            self.suppliers = resp.data.suppliers;
            self.measures = resp.data.measures;
            self.warehouses = resp.data.warehouses;
            self.datagrid(self.warehouses[1].id);
            $.storage_inventory.initAngular();
        },null,false);
        
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                $.storage_inventory.scope = scope;
                scope.storage_inventory || (scope.storage_inventory = {});
                scope.storage_inventory.opt_searching_warehouse = false;
                scope.storage_inventory.opt_searching_date = false;
                scope.storage_inventory.warehouses = self.warehouses;
                scope.storage_inventory.warehouse = scope.storage_inventory.warehouses[1];
                scope.storage_inventory.warehouseChange = function(warehouse_id){
                    var queryParams = $('#tbl_'+self.module).datagrid('options').queryParams;
                    queryParams.warehouse_id = warehouse_id;
                    $('#tbl_'+self.module).datagrid('load', queryParams);
                }
               
                scope.storage_inventory.keysearch_name_time = 0;
                scope.storage_inventory.onChangeKeysearchName = function (keysearch_name) {
                    if(scope.storage_inventory.keysearch_name_time == 0){
                        scope.storage_inventory.keysearch_name_time = 1;
                        setTimeout(function(){
                            if(scope.storage_inventory.keysearch_name_time == 1){
                                var queryParams = $('#tbl_storage_inventory').datagrid('options').queryParams;
                                queryParams.keysearch = scope.storage_inventory.keysearch_ten;
                                $('#tbl_storage_inventory').datagrid({'load': queryParams});
                                scope.storage_inventory.keysearch_name_time = 0;
                            }
                        }, 1000);
                    }
                };
                scope.storage_inventory.delRowInRows = function(index) {
                    var rs = [];
                    angular.forEach(scope.rows, function(row,ind){
                        if(ind != index) {
                            rs.push(row);
                        }
                    });
                    scope.rows = rs;
                    if(scope.rows.length == 0){
                        dialogClose();
                    }else{
                        angular.forEach(scope.rows, function(row, index){
                            if(dateparser(scope.export.maxdate)<dateparser(row.date)) {
                                scope.export.maxdate = row.date;
                            }
                        });
                    }
                }
                scope.storage_inventory.exportInventoriesFinalYearForm = function(){
                    scope.rows = $('#tbl_'+self.module).datagrid('getSelections');
                    if(scope.rows.length == 0){
                        alert("Phải chọn ít nhất 1 dòng!");
                        return;
                    }
                    scope.export = {
                        date: dateboxOnSelect(),
                        maxdate: scope.rows[0].date,
                        note: 'Trả nhà cung cấp'
                    };
                    angular.forEach(scope.rows, function(row, index){
                        if(dateparser(scope.export.maxdate)<dateparser(row.date)) {
                            scope.export.maxdate = row.date;
                        }
                    });
                    if(dateparser(scope.export.maxdate)>dateparser(scope.export.date)) {
                        scope.export.date = scope.export.maxdate;
                    }
                    scope.random = Math.random();
                    scope.captcha = null;
                    $.dm_datagrid.showAddForm({
                            module: $CFG.project+'/'+self.module,
                            action: 'add',
                            title: 'Xuất kho trả nhà cung cấp',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            draggable: true,
                            askBeforeClose: true,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module,'popup_xuatkho_tra_ncc.html', {}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp, scope));
                                    });
                                })
                            }
                        },
                        function(resp){
                            $("#tbl_"+self.module).datagrid('reload');
                        }
                    );
                }

                scope.storage_inventory.exportBrokenFoodForm = function(){
                    scope.rows = $('#tbl_'+self.module).datagrid('getSelections');
                    if(scope.rows.length == 0){
                        alert("Phải chọn ít nhất 1 dòng!");
                        return;
                    }
                    scope.export = {
                        date: dateboxOnSelect(),
                        maxdate: scope.rows[0].date,
                        note: '',
                    };
                    angular.forEach(scope.rows, function(row, index){
                        if(dateparser(scope.export.maxdate)<dateparser(row.date)) {
                            scope.export.maxdate = row.date;
                        }
                    });
                    if(dateparser(scope.export.maxdate)>dateparser(scope.export.date)) {
                        scope.export.date = scope.export.maxdate;
                    }
                    angular.forEach(scope.rows, function(row, index){
                        row.broken_date = scope.export.date;
                    });
                    scope.random = Math.random();
                    scope.captcha = null;
                    $.dm_datagrid.showAddForm({
                            module: $CFG.project+'/'+self.module,
                            action: 'add',
                            title: 'Xuất hủy thực phẩm hỏng, mốc, hết hạn',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            draggable: true,
                            askBeforeClose: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module,'popup_xuathuy.html', {}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp, scope));
                                    });
                                })
                            }
                        },
                        function(resp){
                            $("#tbl_"+self.module).datagrid('reload');
                        }
                    );
                }
                scope.storage_inventory.changeBrokenNote = function() {
                    angular.forEach(scope.rows, function(row, index){
                        row.note = scope.export.note;
                    });
                }
                scope.storage_inventory.changeBrokenQuantity = function(row) {
                    if(row.broken_quantity > row.inventory) {
                        row.broken_quantity = row.inventory;
                    }
                    if(row.broken_quantity < 0) {
                        row.broken_quantity = 0;
                    }
                    row.broken_quantity = parseFloat(row.broken_quantity);
                }
                scope.storage_inventory.onChangeExportBrokenDate = function(row) {
                    if(dateparser(row.broken_date) < dateparser(row.date)) {
                        row.broken_date = row.date;
                        alert('ngày xuất không được nhỏ hơn '+row.date);
                    }
                }
                scope.storage_inventory.changeReturnQuantity = function(row) {
                    if(row.return_quantity > row.inventory) {
                        row.return_quantity = row.inventory;
                    }
                    if(row.return_quantity < 0) {
                        row.return_quantity = 0;
                    }
                    row.return_quantity = parseFloat(row.return_quantity);
                }
                scope.storage_inventory.onChangeExportBrokenDateAll = function() {
                    if(dateparser(scope.export.date) < dateparser(scope.export.maxdate)) {
                        scope.export.date = scope.export.maxdate;
                        alert('ngày xuất không được nhỏ hơn '+scope.export.maxdate);
                    } else {
                        angular.forEach(scope.rows, function(row, index){
                            row.broken_date = scope.export.date;
                        });
                    }
                }
                scope.storage_inventory.exportBrokenFood = function() {
                    if(scope.rows.length == 0){
                        alert("Phải chọn ít nhất 1 dòng!");
                        return;
                    }
                    var validate = true;
                    var ids = [];
                    angular.forEach(scope.rows, function(row, index){
                        if (!row.broken_quantity) {
                            validate = false;
                            alert("Số lượng xuất hủy phải lớn hơn 0");
                            return;
                        }
                        ids.push(row.id);
                    })
                    if (!validate) {
                        return;
                    }
                    var data = {
                        async: true,
                        rows: scope.rows,
                        ids: ids,
                        captcha: scope.captcha,
                    }

                    process($CFG.project+'/'+self.module+'/exportBrokenFood',data,function(resp){
                        if(resp.result == 'success') {
                            dialogClose();
                            $('#tbl_'+self.module).datagrid('reload');
                        }
                    },null);
                }
                scope.storage_inventory.exportBrokenFoodListForm = function() {
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project+'/'+self.module,
                        action: 'add',
                        title: 'DS xuất hủy thực phẩm hỏng mốc, hết hạn',
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        draggable: true,
                        content: function(element){
                            loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module,'popup_list_xuathuy.html', {}, function(resp){
                                scope.$apply(function(){
                                    $(element).html(scope.compile(resp, scope));
                                });
                            });
                            process($CFG.project+'/'+self.module+'/exportRebackSupplierList', {export_end: 3, async: true}, function(resp){
                                if(resp.result == 'success') {
                                    scope.$apply(function(){
                                        scope.reback = {
                                            lists: resp.data,
                                            money: 0
                                        }
                                        angular.forEach(resp.data, function(item,index){
                                            item.selected = 1;
                                            item.money = 0;
                                            angular.forEach(item.exports, function(row, ind){
                                                row.money = row.quantity_export * row.price;
                                                item.money += row.money;
                                            });
                                            scope.reback.money += item.money;
                                        });
                                    });
                                }
                            });
                        }
                    },
                    function(resp){
                        $("#tbl_"+self.module).datagrid('reload');
                    });
                }
                scope.storage_inventory.onChangeExportDate = function() {
                    if(dateparser(scope.export.date) < dateparser(scope.export.maxdate)) {
                        scope.export.date = scope.export.maxdate;
                        alert('ngày xuất không được nhỏ hơn '+scope.export.maxdate);
                    }
                }
                scope.storage_inventory.exportInventoriesFinalYear = function() {
                    if(scope.rows.length == 0){
                        alert("Phải chọn ít nhất 1 dòng!");
                        return;
                    }
                    var ids = [];
                    angular.forEach(scope.rows, function(row, index){
                        ids.push(row.id);
                    })
                    var data = {
                        async: true,
                        ids: ids.join(','),
                        rows: scope.rows,
                        date: scope.export.date,
                        note: scope.export.note,
                        captcha: scope.captcha,
                    }

                    process($CFG.project+'/'+self.module+'/exportInventoriesFinalYear',data,function(resp){
                        if(resp.result == 'success') {
                            dialogClose();
                            $('#tbl_'+self.module).datagrid('reload');
                        }
                    },null);
                }
                // scope.storage_inventory.moveInventoriesToNextYearForm = function(){
                //     $.dm_datagrid.showAddForm({
                //             module: $CFG.project+'/'+self.module,
                //             action:'add',
                //             title:'Chuyển tồn kho sang năm sau',
                //             size: size.wide,
                //             content: function(element){
                //                 loadForm($CFG.project+'/'+self.module,'add', {}, function(resp){
                //                     $(element).html(resp);
                //                 })
                //             }
                //         },
                //         function(resp){
                //             $("#tbl_"+self.module).datagrid('reload');
                //         }
                //     );
                // }
                scope.storage_inventory.showConfirm = function () {
                    scope.rows = $('#tbl_'+self.module).datagrid('getSelections');
                    if(scope.rows.length == 0){
                        alert("Phải chọn ít nhất 1 dòng!");
                        return;
                    }
                    scope.random = Math.random();
                    scope.txt_confirm = 'Bạn chắc chắn muốn chuyển tồn những thực phẩm đã chọn sang năm học sau?';
                    scope.captcha = null;
                    scope.accept_fn = scope.storage_inventory.moveInventoriesToNextYear;
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project + '/' + self.module,
                            action: 'add',
                            title: 'Xác nhận',
                            showButton: false,
                            size: 300,
                            scope: scope,
                            draggable: true,
                            content: function(element){
                                loadForm($CFG.remote.base_url,'/templates/dinhduong/confirm.html',{}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp, scope));
                                    });
                                });
                            }
                        }
                    );
                }
                scope.storage_inventory.moveInventoriesToNextYear = function() {
                    scope.rows = $('#tbl_'+self.module).datagrid('getSelections');
                    var ids = [];
                    angular.forEach(scope.rows, function(row, index){
                        ids.push(row.id);
                    })
                    var data = {
                        async: true,
                        captcha: scope.captcha,
                        warehouse_id: scope.storage_inventory.warehouse.id,
                        ids: ids
                    };
                    process($CFG.project+'/storage_inventory/moveInventoriesToNextYear', data, function(resp){
                        if(resp.result == 'success'){
                            $('.modal').modal('toggle');
                            $('#tbl_' + self.module).datagrid('reload');
                        } else {
                            alert('Mã bảo vệ không hợp lệ!');
                        }
                    },function(){
                        // TO DO
                    },false);   
                };
                scope.storage_inventory.undoMoveInventoriesToNextYear = function() {
                    $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Chắc chắn muốn khôi phục tồn kho?<br/><br/> - Thao tác này đồng nghĩa với việc hệ thống sẽ xóa hết sổ tính tiền ăn của năm sau.</div>', function(r){
                        if (r){
                            process($CFG.project+'/'+self.module+'/moveInventoriesToNextYear',{async: true, type: 'undo', warehouse_id: scope.storage_inventory.warehouse.id},function(resp){
                                if(resp.result == 'success'){
                                    $('#tbl_'+self.module).datagrid('reload');
                                }
                            },null);
                        }
                    });
                }
                scope.storage_inventory.exportInventoriesFinalYearList = function(){
                    $.dm_datagrid.showAddForm({
                            module: $CFG.project+'/'+self.module,
                            action: 'add',
                            title: 'Danh sách xuất kho trả nhà cung cấp',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            draggable: true,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module,'popup_list_xuatkho_tra_ncc.html', {}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp, scope));
                                    });
                                });
                                process($CFG.project+'/'+self.module+'/exportRebackSupplierList', {export_end: 2, async: true}, function(resp){
                                    if(resp.result == 'success') {
                                        scope.$apply(function(){
                                            scope.reback = {
                                                lists: resp.data,
                                                money: 0
                                            }
                                            angular.forEach(resp.data, function(item,index){
                                                item.selected = 1;
                                                item.money = 0;
                                                angular.forEach(item.exports, function(row, ind){
                                                    row.money = row.quantity_export * row.price;
                                                    item.money += row.money;
                                                });
                                                scope.reback.money += item.money;
                                            });
                                        });
                                    }
                                });
                            }
                        },
                        function(resp){
                            $("#tbl_"+self.module).datagrid('reload');
                        }
                    );
                }
                scope.storage_inventory.getDatesExportBack = function(){
                    var rs = [];
                    var tmp = [];
                    if(scope.reback) {
                        angular.forEach(scope.reback.lists, function(item,index){
                            if(item.selected) {
                                rs.push(item.date);
                            }
                            tmp.push(item.date);
                        });
                        if(count(rs) == 0) {
                            rs = tmp;
                        }
                    }
                    return rs.join(',');
                }
                scope.storage_inventory.undoExportInventoriesFinalYear = function(index, item) {
                    var row = undefined;
                    var exps = [];
                    angular.forEach(item.exports, function(exp, ind){
                        if(ind == index) {
                            row = exp;
                        }else{
                            exps.push(exp);
                        }
                    })
                    if(!row){
                        alert('Dữ liệu không chính xác');
                        return;
                    }
                    $.messager.confirm('Cảnh báo', '<div style = "font-size: 14px">Chắc chắn xóa xuất kho.</div>', function(r){
                        if(r) { 
                            process($CFG.project+'/'+self.module+'/exportInventoriesFinalYear',{async: true, type: 'undo', row: row},function(resp){
                                if(resp.result == 'success') {
                                    scope.$apply(function(){
                                        $('#tbl_'+self.module).datagrid('reload');
                                        item.exports = exps;
                                    })
                                }
                            },null);
                        }
                    });
                }
            });
        },0);
    }, datagrid: function(warehouse_id){
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
            urls.join('/'),
            self.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    { field:'ck', checkbox: true, rowspan:2 },
                    { title:'Tên thực phẩm', field:'food_name', rowspan:2, width:250, sortable:true },
                    { title:'Kho lưu', field:'warehouse_id', rowspan:2, width:100, sortable:true, formatter: function(value,row){
                        $.each(self.warehouses,function(index,item){
                            if(item.id == value){
                                value = item.name;
                                return;
                            }
                        })
                        return value;
                    }
                    } ,
                    // { title:'Đơn giá', field:'price', width:100, rowspan:2 },
                    { title:'Đơn giá', field:'price', width:100, rowspan:2, editor: 'numberbox', align: 'center', formatter: function(value,row,index){
                            value = digit_grouping(value);
                            return value;
                        } 
                    },
                    { title:'Số lượng', colspan:3, field:'quantity', width:100, align: 'center'},
                    { title:'Đơn vị tính', field:'measure_id', rowspan:2, width:70,align: 'center', formatter: function(value,row){
                        $.each(self.measures,function(index,item){
                            if(item.id == value){
                                value = item.name;
                                return;
                            }
                        })
                        return value;
                    } },
                    { title:'Ngày nhập', field:'date', width:100, rowspan:2 },
                    { title:'Nhà cung cấp', field:'supplier_id', width:100,rowspan:2 , formatter: function(value,row){
						if (self.suppliers[value]!=undefined) {
							return self.suppliers[value].name;
						}
                        return "";
                    }
                }
                    // { title:'Xuất trước', field:'first', width:100, rowspan:2 ,
                    //     formatter: function(value,row,index){
                    //        if(value === row.id) {
                    //            str = '<input type="checkbox" checked/>';
                    //        } else {
                    //            str = '<input type="checkbox" />';
                    //        }
                    //         return str;
                    //     }
                    // },
                ],[
                    { title:'Nhập', field:'quantity', width:50, align: 'center' },
                    { title:'Xuất', field:'quantity_export', width:50, align: 'center' },
                    { title:'Tồn', field:'inventory', width:50, align: 'center' },
                ],
            ],
            {
                view: groupview,
                groupField: 'food_id',
                // selectOnCheck: 'true',
                // checkOnSelect: 'true',
                // ctrlSelect: 'true',
                // singleSelect: 'false',
                queryParams: {warehouse_id: warehouse_id},
                groupFormatter: function(id,rows){
                    var sum_quan = 0;
                    rows.forEach(function (row) {
                        sum_quan += round(row.inventory);

                    });
                    return rows[0].food_name + ' - Số lượng tồn : <i>' + round(sum_quan) +'</i>' + '<span style="margin-left: 10px"> (Mã TP: ' + rows[0].food_id + ')</span>';
                },onSelect: function(index,row) {
                    if(row.quantity_used==0) {
                        $('#tbl_storage').datagrid('beginEdit',index);
                        // row.editing = true;
                        $('#export_print').prop('disabled',false).css('opacity','1');
                    }
                },onBeforeEdit:function(index,row){
                    row.editing = true;
                    $(this).datagrid('refreshRow', index);
                },onDblClickRow: function(index,row){

                },onAfterEdit:function(index,row){
                    row.editing = false;
                    $(this).datagrid('refreshRow', index);
                    $('#export_print').prop('disabled',true).css('opacity','0.3');
                },
                    onCancelEdit:function(index,row){
                        row.editing = false;
                        $(this).datagrid('refreshRow', index);
                        $('#export_print').prop('disabled',true).css('opacity','0.3');
                    },onLoadSuccess:function(data){
                    $('#export_print').prop('disabled',true).css('opacity','0.3');
					setTimeout(function(){
						if($CFG.is_gokids==1) {
							$('.datagrid-view').height($('.datagrid-view').height() - 30);
							$('.datagrid-body').height($('.datagrid-body').height() - 30);
						}
					},1000);
                },rowStyler:function(index,row){
                    if ((row.quantity-row.quantity_used) < row.min_inventory){
                        return 'background-color:rgb(242, 213, 155);color:#444;';
                    }
                }
            }
        );
}, cancelEditor:function(index){
    $('#tbl_storage').datagrid('cancelEdit', index);
}, saverow: function(index){
    var self = this;
    var item = {};
    var ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'warehouse_id'
    });
    item.warehouse_id = $(ed.target).combobox('getValue');
    ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'price'
    });
    item.price = $(ed.target).combobox('getText');
    ed = $('#tbl_storage').datagrid('getEditor', {
        index: index,
        field: 'quantity'
    });
    item.quantity = $(ed.target).combobox('getText');
    var data = $('#tbl_storage').datagrid('getData');
    if(!data.rows){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var row = data.rows[index];
    if(!row){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    item.id = row.id;
    var action = $CFG.project+'/storage/edit';
    process(action, {data: item}, function(resp){
        if(resp.result == 'success') {
            $('#tbl_storage').datagrid('endEdit', index);
            // $.storage.cancelEditor(index);
        }
    });
}, addAction: function(id){
    if(!id) return;
    if($('.btn-add.add-new').css('opacity')+'' != '1'){
        return;
    }
    var self = this;
    var data = arrayToJson(getSubmitForm(id,true));
    var action = $CFG.project+'/storage/add';
    process(action, {data:data}, function(resp){
        if(resp.result == 'success') {
            $("#tbl_"+self.module).datagrid('reload');
            $('#ma_thuc_pham').combobox('clear');
            $('.btn-add.add-new').css('opacity',0);
        }
    });
}, doSearch: function(){
    setTimeout(function(){
        var scope = $.storage.scope;
        if(!scope){
            return;
        }
        var opt_search = {};
        if(scope.storage.opt_searching_warehouse){
            opt_search.warehouse_id = {value: $.storage.keysearch_warehouse_id,op:'equal'};
        }
        if(scope.storage.opt_searching_date){
            opt_search.date = {value: $.storage.keysearch_date,op:'equal'};
        }
        if(count(opt_search)>0){
            $.dm_datagrid.doSearch('tbl_storage',opt_search,'and');
        }else{
            $.dm_datagrid.doSearch('tbl_storage',{},'and');
        }
    },300)
}, del: function(index){ // XÓA
    var self = this;
    var data = $('#tbl_storage').datagrid('getData');
    if(!data.rows){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var row = data.rows[index];
    if(!row){
        alert('Không tìm thấy dữ liệu.');
        return;
    }
    var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>'];
    msg.push(' - Dữ liệu sau khi xóa sẽ không thể khôi phục.');
    $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
        if (r){
            process($CFG.project+'/'+self.module+'/del',{ids: row.id},function(resp){
                $("#tbl_"+self.module).datagrid('reload');
            })
        }
    });
    setTimeout(function(){
        $('#tbl_storage').datagrid('cancelEdit', index);

    },0);
},  exportFormAction :  function (id){
    var self = this;
    var urls_export = [$CFG.remote.base_url,$CFG.project,'storage','exportExcelPhieuNhapKho'];
    $('#export-dialog').dialog({
        title: 'Thông báo',
        width: 400,
        height: 200,
        closed: false,
        cache: false,
        modal: true ,
        onOpen : function (ele) {
            $(ele).show();
            var selectedrow = $("#tbl_storage").datagrid("getSelected");
            var row = selectedrow ;
            if(!row){
                alert('Không tìm thấy dữ liệu.');
            }
            var f_date  = row.date;
            var ss = f_date.split('/');
            var selected_date = parseInt(ss[2],10)+"-"+parseInt(ss[1],10)+"-"+parseInt(ss[0],10);
            var date;
            var type;
            $("#ngay_xuat").datebox({
                width:100,
                onSelect: function(date){
                    selected_date = date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
                }
            });
            $('#ngay_xuat').datebox('setValue',f_date);
            $('#btn_export').click(function(){
                var nguoi_lap_phieu  = $('#nguoi_lap_phieu').val();
                var nguoi_giao_hang  = $('#nguoi_giao_hang').val();
                var thu_kho  = $('#thu_kho').val();
                var ke_toan_truong  = $('#ke_toan_truong').val();
                if(typeof selected_date == 'undefined') {
                    alert('Vui lòng chọn ngày xuất') ;
                    return;
                }
                date  = selected_date;
                type  = $('#export_type').val();
                process($CFG.project+'/'+self.module+'/update_print_info',
                    {type:2,nguoi_lap_phieu:nguoi_lap_phieu,nguoi_giao_hang:nguoi_giao_hang,thu_kho:thu_kho,ke_toan_truong:ke_toan_truong},
                    function(data){
                        location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                    },
                    function(data){
                        location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                    },
                    false);
            });
            $.getScript($CFG.remote.base_url+'/js/jQuery-printPage-plugin/jquery.printPage.js').done(function(){
                $('#btn_preview').click(function(){
                    if(typeof selected_date == 'undefined') {
                        alert('Vui lòng chọn ngày xuất') ;
                        return;
                    }
                    date  = selected_date;
                    type  = $('#export_type').val();

                    $('#btn_preview').printPage({
                        url: urls_export.join('/')+'?type='+type+'&date='+date+'&preview=1',
                        attr: "href",
                        message:"Phiếu nhập kho đang được tạo ..."
                    });
                });
            });
        }
    });
}
}
