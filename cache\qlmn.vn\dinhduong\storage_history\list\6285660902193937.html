<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_storage_history_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="storage_history"></div>
			<div class="function-kh-ct " style="padding-top: 20px;line-height: 40px;">
                <div style="width: 50%;">
                	<div class="pull-left" style="padding-right: 5px;"><span style="color:red;font-weight: bold">Lưu ý : </span> </div>
	                <div style="float:left;border:black 1px solid;background-color: #fbf3c7 ;line-height: 11px;width:14px;margin-top:13px">&nbsp;</div><div class="pull-left">&nbsp;&nbsp;Nhập&nbsp;&nbsp;</div>
	                <div style="float:left;border:black 1px solid;background-color: white ;line-height: 11px;width:14px;margin-top:13px">&nbsp;</div><div class="pull-left">&nbsp;&nbsp;Xuất&nbsp;&nbsp;</div>
				</div>
                <div style="width: 50%;">
                			    	</div>
				<div class="time-warehousing">
					<span class="time-text">Thời gian</span>
					<input date-box="storage_history.start" style="width: 100px;"
						   title="từ ngày" class="input-custom-style"/>
					<span class="line"></span>
					<input date-box="storage_history.end" style="width: 100px;"
						   title="đến ngày" class="input-custom-style"/>

					<input ng-model="storage_history.keysearch"
						   class="form-control search-input-warehouse"
						   placeholder="Tên thực phẩm hoặc mã thực phẩm">
					<button class="h25" ng-click="storage_history.warehouseHistorySearch()">Tìm kiếm</button>
				</div>
			</div>

		</div>
	</div>
	<div id="tbl_storage_history"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/storage_history.js"></script>
<script type="text/javascript">
	$.storage_history.init();
</script>
<style type="text/css">
.container-price{
	display: inline-flex;
	padding: 1px 5px;
	border: 1px solid #bbb;
	background: #fff;
	border-radius: 3px;
	padding-right: 0px;
	height: 25px;
}
.container-price > input{
	width: 70px;
	padding: 0px;
	border: unset;
}
.container-price > label{
	border-left: 1px solid #ccc;
	padding: 2px 6px;
	margin: 2px;
	color: #969696;
}
#tb_gumshoe_detail{
	padding-top: 0px !important;
}
#frm-storage{
	margin-top: 10px;
	margin-bottom: 10px;
}
.btn-add{
	height: 25px;
	padding: 3px 12px;
	background: #69adde;
	margin-bottom: 1px;
	outline: none;
	color: #FFF;
	opacity: 0.3; 
}
.btn-add span{
	color: #FFF !important;
}
.time-warehousing {
	margin-left: 162px;
	line-height: 0px;
	padding-top: 7px;
	padding-bottom: 13px;
}
.time-warehousing span.time-text{
	font-weight: bold;
	font-size: 1.1em;
	margin-right: 15px;
}
.time-warehousing span.textbox {
	margin: 0px 5px;
}
span.line {
	display: inline-block;
	width: 10px;
	height: 1px;
	background: black;
	margin-bottom: 3px;
}
.time-warehousing .search-input-warehouse {
	display: inline-block;
	width: 145px;
	height: 25px;
	border-radius: 0px;
	font-size: 11px;
}
.time-warehousing .h25 {
	display: inline-block;
	margin-left: 5px;
	padding: 10px 10px;
	background: white;
	border: 1px solid #c6c6c6;
}
</style>

