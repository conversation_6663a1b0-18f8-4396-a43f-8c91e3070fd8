const express = require('express');
const router = express.Router();
const moment = require('moment');

// Mock data cho thu chi
const mockFeeCategories = [
  {
    id: 1,
    name: "Tiền ăn",
    code: "TIEN_AN",
    amount: 30000,
    frequency: "daily",
    description: "<PERSON><PERSON> ăn hàng ngày cho học sinh",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON> ph<PERSON>",
    code: "HOC_PHI",
    amount: 500000,
    frequency: "monthly",
    description: "<PERSON><PERSON><PERSON> phí hàng tháng",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 3,
    name: "<PERSON><PERSON> hoạt động",
    code: "PHI_HOAT_DONG",
    amount: 100000,
    frequency: "monthly",
    description: "<PERSON><PERSON> hoạt động ngoại khóa",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 4,
    name: "<PERSON><PERSON> đồng phục",
    code: "PHI_DONG_PHUC",
    amount: 200000,
    frequency: "yearly",
    description: "<PERSON><PERSON> đồng phục học sinh",
    status: 1,
    created_at: "2024-09-01 08:00:00"
  }
];

const mockStudentFees = [
  {
    id: 1,
    student_id: 1,
    student_name: "Nguyễn Văn A",
    student_code: "HS001",
    grade_name: "Nhóm Chồi",
    fee_category_id: 1,
    fee_name: "Tiền ăn",
    amount: 30000,
    month: "2025-08",
    days_attended: 22,
    total_amount: 660000,
    paid_amount: 660000,
    remaining_amount: 0,
    payment_date: "2025-08-05",
    status: "paid",
    created_at: "2025-08-01 08:00:00"
  },
  {
    id: 2,
    student_id: 1,
    student_name: "Nguyễn Văn A",
    student_code: "HS001",
    grade_name: "Nhóm Chồi",
    fee_category_id: 2,
    fee_name: "Học phí",
    amount: 500000,
    month: "2025-08",
    days_attended: null,
    total_amount: 500000,
    paid_amount: 500000,
    remaining_amount: 0,
    payment_date: "2025-08-01",
    status: "paid",
    created_at: "2025-08-01 08:00:00"
  },
  {
    id: 3,
    student_id: 2,
    student_name: "Trần Thị C",
    student_code: "HS002",
    grade_name: "Nhóm Chồi",
    fee_category_id: 1,
    fee_name: "Tiền ăn",
    amount: 30000,
    month: "2025-08",
    days_attended: 20,
    total_amount: 600000,
    paid_amount: 400000,
    remaining_amount: 200000,
    payment_date: null,
    status: "partial",
    created_at: "2025-08-01 08:00:00"
  }
];

const mockAttendance = [
  {
    id: 1,
    student_id: 1,
    student_name: "Nguyễn Văn A",
    student_code: "HS001",
    date: "2025-08-08",
    morning_session: true,
    afternoon_session: true,
    meal_morning: true,
    meal_lunch: true,
    meal_afternoon: true,
    notes: "",
    marked_by: "Cô Nguyễn Thị A",
    created_at: "2025-08-08 08:00:00"
  },
  {
    id: 2,
    student_id: 2,
    student_name: "Trần Thị C",
    student_code: "HS002",
    date: "2025-08-08",
    morning_session: true,
    afternoon_session: false,
    meal_morning: true,
    meal_lunch: true,
    meal_afternoon: false,
    notes: "Về sớm do ốm",
    marked_by: "Cô Nguyễn Thị A",
    created_at: "2025-08-08 08:00:00"
  },
  {
    id: 3,
    student_id: 3,
    student_name: "Lê Minh E",
    student_code: "HS003",
    date: "2025-08-08",
    morning_session: false,
    afternoon_session: false,
    meal_morning: false,
    meal_lunch: false,
    meal_afternoon: false,
    notes: "Nghỉ ốm",
    marked_by: "Cô Nguyễn Thị A",
    created_at: "2025-08-08 08:00:00"
  }
];

const mockCashBook = [
  {
    id: 1,
    date: "2025-08-08",
    type: "income",
    category: "Học phí",
    description: "Thu học phí tháng 8/2025",
    amount: 15000000,
    student_count: 30,
    reference_number: "TH20250808001",
    created_by: "Kế toán A",
    created_at: "2025-08-08 08:00:00"
  },
  {
    id: 2,
    date: "2025-08-08",
    type: "income",
    category: "Tiền ăn",
    description: "Thu tiền ăn ngày 08/08/2025",
    amount: 2100000,
    student_count: 70,
    reference_number: "TA20250808001",
    created_by: "Kế toán A",
    created_at: "2025-08-08 09:00:00"
  },
  {
    id: 3,
    date: "2025-08-08",
    type: "expense",
    category: "Mua thực phẩm",
    description: "Mua thực phẩm cho bữa ăn",
    amount: 1500000,
    student_count: null,
    reference_number: "MT20250808001",
    created_by: "Kế toán A",
    created_at: "2025-08-08 10:00:00"
  },
  {
    id: 4,
    date: "2025-08-07",
    type: "expense",
    category: "Lương nhân viên",
    description: "Tạm ứng lương tháng 8",
    amount: 5000000,
    student_count: null,
    reference_number: "LNV20250807001",
    created_by: "Kế toán A",
    created_at: "2025-08-07 15:00:00"
  }
];

const mockPayments = [
  {
    id: 1,
    student_id: 1,
    student_name: "Nguyễn Văn A",
    student_code: "HS001",
    payment_date: "2025-08-05",
    amount: 1160000,
    fees: [
      { fee_category_id: 1, fee_name: "Tiền ăn", amount: 660000 },
      { fee_category_id: 2, fee_name: "Học phí", amount: 500000 }
    ],
    payment_method: "cash",
    receipt_number: "PT20250805001",
    notes: "Thanh toán đầy đủ",
    received_by: "Kế toán A",
    created_at: "2025-08-05 08:00:00"
  },
  {
    id: 2,
    student_id: 2,
    student_name: "Trần Thị C",
    student_code: "HS002",
    payment_date: "2025-08-03",
    amount: 400000,
    fees: [
      { fee_category_id: 1, fee_name: "Tiền ăn", amount: 400000 }
    ],
    payment_method: "transfer",
    receipt_number: "PT20250803001",
    notes: "Thanh toán một phần tiền ăn",
    received_by: "Kế toán A",
    created_at: "2025-08-03 08:00:00"
  }
];

// API Routes

// Fee Category APIs
router.post('/fee_config/list', (req, res) => {
  const { page = 1, rows = 30 } = req.body;
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = mockFeeCategories.slice(start, end);
  
  res.json({
    total: mockFeeCategories.length,
    rows: paginatedData
  });
});

router.post('/fee_config/create', (req, res) => {
  const { name, code, amount, frequency, description } = req.body;
  
  const newFeeCategory = {
    id: mockFeeCategories.length + 1,
    name,
    code,
    amount: parseFloat(amount),
    frequency,
    description,
    status: 1,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockFeeCategories.push(newFeeCategory);
  
  res.json({
    success: true,
    message: "Tạo khoản thu thành công",
    data: newFeeCategory
  });
});

router.post('/fee_config/update', (req, res) => {
  const { id, name, code, amount, frequency, description } = req.body;
  const feeIndex = mockFeeCategories.findIndex(f => f.id == id);
  
  if (feeIndex !== -1) {
    mockFeeCategories[feeIndex] = {
      ...mockFeeCategories[feeIndex],
      name,
      code,
      amount: parseFloat(amount),
      frequency,
      description,
      updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
    };
    
    res.json({
      success: true,
      message: "Cập nhật khoản thu thành công",
      data: mockFeeCategories[feeIndex]
    });
  } else {
    res.status(404).json({
      success: false,
      message: "Không tìm thấy khoản thu"
    });
  }
});

// Student Fee APIs
router.post('/fee/list', (req, res) => {
  const { page = 1, rows = 30, month, student_id, status } = req.body;
  let filteredFees = mockStudentFees;
  
  if (month) {
    filteredFees = filteredFees.filter(fee => fee.month === month);
  }
  
  if (student_id) {
    filteredFees = filteredFees.filter(fee => fee.student_id == student_id);
  }
  
  if (status) {
    filteredFees = filteredFees.filter(fee => fee.status === status);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredFees.slice(start, end);
  
  res.json({
    total: filteredFees.length,
    rows: paginatedData
  });
});

router.post('/fee/calculate', (req, res) => {
  const { student_id, month } = req.body;
  
  // Tính toán phí dựa trên số ngày đi học
  const attendance = mockAttendance.filter(a => 
    a.student_id == student_id && 
    a.date.startsWith(month)
  );
  
  const daysAttended = attendance.length;
  const mealDays = attendance.filter(a => a.meal_lunch).length;
  
  const calculatedFees = mockFeeCategories.map(category => {
    let amount = category.amount;
    let totalAmount = amount;
    
    if (category.code === 'TIEN_AN') {
      totalAmount = amount * mealDays;
    }
    
    return {
      fee_category_id: category.id,
      fee_name: category.name,
      amount: amount,
      days_attended: category.code === 'TIEN_AN' ? mealDays : null,
      total_amount: totalAmount
    };
  });
  
  res.json({
    success: true,
    data: {
      student_id,
      month,
      days_attended: daysAttended,
      meal_days: mealDays,
      fees: calculatedFees,
      total_amount: calculatedFees.reduce((sum, fee) => sum + fee.total_amount, 0)
    }
  });
});

// Payment APIs
router.post('/fee/payment', (req, res) => {
  const { student_id, fees, payment_method, notes } = req.body;
  
  const totalAmount = fees.reduce((sum, fee) => sum + fee.amount, 0);
  
  const newPayment = {
    id: mockPayments.length + 1,
    student_id: parseInt(student_id),
    student_name: "Học sinh " + student_id,
    student_code: "HS" + String(student_id).padStart(3, '0'),
    payment_date: moment().format('YYYY-MM-DD'),
    amount: totalAmount,
    fees: fees,
    payment_method: payment_method || 'cash',
    receipt_number: "PT" + moment().format('YYYYMMDD') + String(mockPayments.length + 1).padStart(3, '0'),
    notes: notes || "",
    received_by: "Kế toán",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockPayments.push(newPayment);
  
  // Cập nhật trạng thái thanh toán trong mockStudentFees
  fees.forEach(fee => {
    const feeIndex = mockStudentFees.findIndex(sf => 
      sf.student_id == student_id && sf.fee_category_id == fee.fee_category_id
    );
    if (feeIndex !== -1) {
      mockStudentFees[feeIndex].paid_amount += fee.amount;
      mockStudentFees[feeIndex].remaining_amount = 
        mockStudentFees[feeIndex].total_amount - mockStudentFees[feeIndex].paid_amount;
      mockStudentFees[feeIndex].status = 
        mockStudentFees[feeIndex].remaining_amount <= 0 ? 'paid' : 'partial';
      mockStudentFees[feeIndex].payment_date = newPayment.payment_date;
    }
  });
  
  res.json({
    success: true,
    message: "Thanh toán thành công",
    data: newPayment
  });
});

router.post('/fee/payment_history', (req, res) => {
  const { student_id, date_from, date_to, page = 1, rows = 30 } = req.body;
  let filteredPayments = mockPayments;
  
  if (student_id) {
    filteredPayments = filteredPayments.filter(p => p.student_id == student_id);
  }
  
  if (date_from && date_to) {
    filteredPayments = filteredPayments.filter(p => 
      p.payment_date >= date_from && p.payment_date <= date_to
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredPayments.slice(start, end);
  
  res.json({
    total: filteredPayments.length,
    rows: paginatedData
  });
});

// Attendance APIs
router.post('/student_attendance/list', (req, res) => {
  const { date, grade_id, student_id, page = 1, rows = 30 } = req.body;
  let filteredAttendance = mockAttendance;
  
  if (date) {
    filteredAttendance = filteredAttendance.filter(a => a.date === date);
  }
  
  if (student_id) {
    filteredAttendance = filteredAttendance.filter(a => a.student_id == student_id);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredAttendance.slice(start, end);
  
  res.json({
    total: filteredAttendance.length,
    rows: paginatedData
  });
});

router.post('/student_attendance/mark', (req, res) => {
  const { student_id, date, morning_session, afternoon_session, meal_morning, meal_lunch, meal_afternoon, notes } = req.body;
  
  const existingIndex = mockAttendance.findIndex(a => 
    a.student_id == student_id && a.date === date
  );
  
  const attendanceData = {
    student_id: parseInt(student_id),
    student_name: "Học sinh " + student_id,
    student_code: "HS" + String(student_id).padStart(3, '0'),
    date,
    morning_session: Boolean(morning_session),
    afternoon_session: Boolean(afternoon_session),
    meal_morning: Boolean(meal_morning),
    meal_lunch: Boolean(meal_lunch),
    meal_afternoon: Boolean(meal_afternoon),
    notes: notes || "",
    marked_by: "Giáo viên",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  if (existingIndex !== -1) {
    mockAttendance[existingIndex] = {
      ...mockAttendance[existingIndex],
      ...attendanceData
    };
  } else {
    attendanceData.id = mockAttendance.length + 1;
    mockAttendance.push(attendanceData);
  }
  
  res.json({
    success: true,
    message: "Điểm danh thành công",
    data: attendanceData
  });
});

// Cash Book APIs
router.post('/cash_book/list', (req, res) => {
  const { date_from, date_to, type, page = 1, rows = 30 } = req.body;
  let filteredCashBook = mockCashBook;
  
  if (date_from && date_to) {
    filteredCashBook = filteredCashBook.filter(cb => 
      cb.date >= date_from && cb.date <= date_to
    );
  }
  
  if (type) {
    filteredCashBook = filteredCashBook.filter(cb => cb.type === type);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredCashBook.slice(start, end);
  
  res.json({
    total: filteredCashBook.length,
    rows: paginatedData
  });
});

router.post('/cash_book/create', (req, res) => {
  const { date, type, category, description, amount, student_count } = req.body;
  
  const newEntry = {
    id: mockCashBook.length + 1,
    date,
    type,
    category,
    description,
    amount: parseFloat(amount),
    student_count: student_count ? parseInt(student_count) : null,
    reference_number: type.toUpperCase().substring(0, 2) + moment().format('YYYYMMDD') + String(mockCashBook.length + 1).padStart(3, '0'),
    created_by: "Kế toán",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockCashBook.push(newEntry);
  
  res.json({
    success: true,
    message: "Thêm bút toán thành công",
    data: newEntry
  });
});

// Summary APIs
router.post('/cash_book/summary', (req, res) => {
  const { date_from, date_to } = req.body;
  
  let filteredCashBook = mockCashBook;
  if (date_from && date_to) {
    filteredCashBook = filteredCashBook.filter(cb => 
      cb.date >= date_from && cb.date <= date_to
    );
  }
  
  const totalIncome = filteredCashBook
    .filter(cb => cb.type === 'income')
    .reduce((sum, cb) => sum + cb.amount, 0);
  
  const totalExpense = filteredCashBook
    .filter(cb => cb.type === 'expense')
    .reduce((sum, cb) => sum + cb.amount, 0);
  
  const balance = totalIncome - totalExpense;
  
  res.json({
    success: true,
    data: {
      period: { date_from, date_to },
      total_income: totalIncome,
      total_expense: totalExpense,
      balance: balance,
      income_count: filteredCashBook.filter(cb => cb.type === 'income').length,
      expense_count: filteredCashBook.filter(cb => cb.type === 'expense').length
    }
  });
});

module.exports = router;
