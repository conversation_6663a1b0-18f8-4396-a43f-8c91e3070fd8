const express = require('express');
const router = express.Router();
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');

// Mock data cho dinh dưỡng
const mockStorageItems = [
  {
    id: 1,
    name: "Gạo tẻ",
    unit: "kg",
    quantity: 500,
    price: 25000,
    supplier_id: 1,
    category: "Thực phẩm chính",
    expiry_date: "2025-12-31",
    created_at: "2025-01-01 08:00:00",
    updated_at: "2025-08-06 18:00:00"
  },
  {
    id: 2,
    name: "Thịt heo",
    unit: "kg",
    quantity: 50,
    price: 180000,
    supplier_id: 2,
    category: "Thịt",
    expiry_date: "2025-08-10",
    created_at: "2025-08-05 08:00:00",
    updated_at: "2025-08-06 18:00:00"
  },
  {
    id: 3,
    name: "<PERSON><PERSON> rót",
    unit: "kg",
    quantity: 30,
    price: 15000,
    supplier_id: 3,
    category: "Rau củ",
    expiry_date: "2025-08-12",
    created_at: "2025-08-06 08:00:00",
    updated_at: "2025-08-06 18:00:00"
  },
  {
    id: 4,
    name: "<PERSON>à chua",
    unit: "kg",
    quantity: 25,
    price: 20000,
    supplier_id: 3,
    category: "Rau củ",
    expiry_date: "2025-08-15",
    created_at: "2025-08-06 08:00:00",
    updated_at: "2025-08-06 18:00:00"
  }
];

const mockDishes = [
  {
    id: 1,
    name: "Cơm thịt kho tàu",
    description: "Món ăn truyền thống",
    category: "Món chính",
    ingredients: [
      { item_id: 1, quantity: 0.15, unit: "kg" },
      { item_id: 2, quantity: 0.08, unit: "kg" }
    ],
    nutrition: {
      calories: 350,
      protein: 15,
      carbs: 45,
      fat: 12
    },
    cost_per_serving: 8500,
    created_at: "2025-01-15 08:00:00"
  },
  {
    id: 2,
    name: "Canh cà chua",
    description: "Canh thanh mát",
    category: "Canh",
    ingredients: [
      { item_id: 4, quantity: 0.05, unit: "kg" }
    ],
    nutrition: {
      calories: 25,
      protein: 1,
      carbs: 5,
      fat: 0.2
    },
    cost_per_serving: 1200,
    created_at: "2025-01-15 08:00:00"
  }
];

const mockSuppliers = [
  {
    id: 1,
    name: "Công ty TNHH Thực phẩm An Toàn",
    contact_person: "Nguyễn Văn A",
    phone: "0123456789",
    email: "<EMAIL>",
    address: "123 Đường ABC, Quận 1, TP.HCM",
    products: ["Gạo", "Đậu", "Ngũ cốc"],
    status: 1,
    created_at: "2024-01-01 08:00:00"
  },
  {
    id: 2,
    name: "Cửa hàng thịt tươi Bình Minh",
    contact_person: "Trần Thị B",
    phone: "0987654321",
    email: "<EMAIL>",
    address: "456 Đường DEF, Quận 2, TP.HCM",
    products: ["Thịt heo", "Thịt bò", "Thịt gà"],
    status: 1,
    created_at: "2024-01-01 08:00:00"
  },
  {
    id: 3,
    name: "Vườn rau sạch Xanh",
    contact_person: "Lê Văn C",
    phone: "0912345678",
    email: "<EMAIL>",
    address: "789 Đường GHI, Quận 3, TP.HCM",
    products: ["Rau xanh", "Củ quả", "Trái cây"],
    status: 1,
    created_at: "2024-01-01 08:00:00"
  }
];

const mockGrades = [
  {
    id: 1,
    name: "Nhóm Chồi",
    age_range: "18-24 tháng",
    student_count: 15,
    daily_meal_cost: 25000,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 2,
    name: "Nhóm Lá",
    age_range: "2-3 tuổi",
    student_count: 20,
    daily_meal_cost: 28000,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 3,
    name: "Nhóm Hoa",
    age_range: "3-4 tuổi",
    student_count: 25,
    daily_meal_cost: 30000,
    created_at: "2024-09-01 08:00:00"
  },
  {
    id: 4,
    name: "Nhóm Quả",
    age_range: "4-5 tuổi",
    student_count: 22,
    daily_meal_cost: 32000,
    created_at: "2024-09-01 08:00:00"
  }
];

const mockMenus = [
  {
    id: 1,
    date: "2025-08-08",
    grade_id: 1,
    meals: {
      breakfast: {
        dishes: [{ dish_id: 1, quantity: 1 }],
        total_cost: 8500,
        total_calories: 350
      },
      lunch: {
        dishes: [
          { dish_id: 1, quantity: 1 },
          { dish_id: 2, quantity: 1 }
        ],
        total_cost: 9700,
        total_calories: 375
      },
      snack: {
        dishes: [],
        total_cost: 3000,
        total_calories: 150
      }
    },
    total_daily_cost: 21200,
    total_daily_calories: 875,
    status: "approved",
    created_at: "2025-08-07 08:00:00"
  }
];

// API Routes

// Storage APIs
router.post('/storage/list', (req, res) => {
  const { page = 1, rows = 30, search = '', category = '' } = req.body;
  let filteredItems = mockStorageItems;
  
  if (search) {
    filteredItems = filteredItems.filter(item => 
      item.name.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  if (category) {
    filteredItems = filteredItems.filter(item => item.category === category);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredItems.slice(start, end);
  
  res.json({
    total: filteredItems.length,
    rows: paginatedData
  });
});

router.post('/storage/create', (req, res) => {
  const { name, unit, quantity, price, supplier_id, category, expiry_date } = req.body;
  
  const newItem = {
    id: mockStorageItems.length + 1,
    name,
    unit,
    quantity: parseFloat(quantity),
    price: parseFloat(price),
    supplier_id: parseInt(supplier_id),
    category,
    expiry_date,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockStorageItems.push(newItem);
  
  res.json({
    success: true,
    message: "Thêm vật tư thành công",
    data: newItem
  });
});

router.post('/storage/update', (req, res) => {
  const { id, name, unit, quantity, price, supplier_id, category, expiry_date } = req.body;
  const itemIndex = mockStorageItems.findIndex(item => item.id == id);
  
  if (itemIndex !== -1) {
    mockStorageItems[itemIndex] = {
      ...mockStorageItems[itemIndex],
      name,
      unit,
      quantity: parseFloat(quantity),
      price: parseFloat(price),
      supplier_id: parseInt(supplier_id),
      category,
      expiry_date,
      updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
    };
    
    res.json({
      success: true,
      message: "Cập nhật vật tư thành công",
      data: mockStorageItems[itemIndex]
    });
  } else {
    res.status(404).json({
      success: false,
      message: "Không tìm thấy vật tư"
    });
  }
});

// Dish APIs
router.post('/dish/list', (req, res) => {
  const { page = 1, rows = 30, search = '', category = '' } = req.body;
  let filteredDishes = mockDishes;
  
  if (search) {
    filteredDishes = filteredDishes.filter(dish => 
      dish.name.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  if (category) {
    filteredDishes = filteredDishes.filter(dish => dish.category === category);
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredDishes.slice(start, end);
  
  res.json({
    total: filteredDishes.length,
    rows: paginatedData
  });
});

router.post('/dish/create', (req, res) => {
  const { name, description, category, ingredients, nutrition } = req.body;
  
  const newDish = {
    id: mockDishes.length + 1,
    name,
    description,
    category,
    ingredients: ingredients || [],
    nutrition: nutrition || {},
    cost_per_serving: 0,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockDishes.push(newDish);
  
  res.json({
    success: true,
    message: "Tạo món ăn thành công",
    data: newDish
  });
});

// Supplier APIs
router.post('/supplier/list', (req, res) => {
  const { page = 1, rows = 30, search = '' } = req.body;
  let filteredSuppliers = mockSuppliers;
  
  if (search) {
    filteredSuppliers = filteredSuppliers.filter(supplier => 
      supplier.name.toLowerCase().includes(search.toLowerCase()) ||
      supplier.contact_person.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  const start = (page - 1) * rows;
  const end = start + parseInt(rows);
  const paginatedData = filteredSuppliers.slice(start, end);
  
  res.json({
    total: filteredSuppliers.length,
    rows: paginatedData
  });
});

router.post('/supplier/create', (req, res) => {
  const { name, contact_person, phone, email, address, products } = req.body;
  
  const newSupplier = {
    id: mockSuppliers.length + 1,
    name,
    contact_person,
    phone,
    email,
    address,
    products: products || [],
    status: 1,
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockSuppliers.push(newSupplier);
  
  res.json({
    success: true,
    message: "Tạo nhà cung cấp thành công",
    data: newSupplier
  });
});

// Grade APIs
router.post('/grade/list', (req, res) => {
  res.json({
    total: mockGrades.length,
    rows: mockGrades
  });
});

router.post('/grade/create', (req, res) => {
  const { name, age_range, daily_meal_cost } = req.body;
  
  const newGrade = {
    id: mockGrades.length + 1,
    name,
    age_range,
    student_count: 0,
    daily_meal_cost: parseFloat(daily_meal_cost),
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockGrades.push(newGrade);
  
  res.json({
    success: true,
    message: "Tạo khối lớp thành công",
    data: newGrade
  });
});

// Menu Planning APIs
router.post('/menu_planning/list', (req, res) => {
  const { date_from, date_to, grade_id } = req.body;
  let filteredMenus = mockMenus;
  
  if (grade_id) {
    filteredMenus = filteredMenus.filter(menu => menu.grade_id == grade_id);
  }
  
  if (date_from && date_to) {
    filteredMenus = filteredMenus.filter(menu => 
      menu.date >= date_from && menu.date <= date_to
    );
  }
  
  res.json({
    total: filteredMenus.length,
    rows: filteredMenus
  });
});

router.post('/menu_planning/create', (req, res) => {
  const { date, grade_id, meals } = req.body;
  
  const newMenu = {
    id: mockMenus.length + 1,
    date,
    grade_id: parseInt(grade_id),
    meals: meals || {},
    total_daily_cost: 0,
    total_daily_calories: 0,
    status: "draft",
    created_at: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  
  mockMenus.push(newMenu);
  
  res.json({
    success: true,
    message: "Tạo thực đơn thành công",
    data: newMenu
  });
});

// Menu Adjust APIs
router.post('/menu_adjust/calculate', (req, res) => {
  const { grade_id, date, dishes } = req.body;
  
  // Tính toán calo và chi phí
  let totalCost = 0;
  let totalCalories = 0;
  
  dishes.forEach(dish => {
    const dishData = mockDishes.find(d => d.id == dish.dish_id);
    if (dishData) {
      totalCost += dishData.cost_per_serving * dish.quantity;
      totalCalories += dishData.nutrition.calories * dish.quantity;
    }
  });
  
  res.json({
    success: true,
    data: {
      total_cost: totalCost,
      total_calories: totalCalories,
      cost_per_student: totalCost,
      nutrition_analysis: {
        protein: 15,
        carbs: 45,
        fat: 12,
        fiber: 5
      }
    }
  });
});

// Menu Report APIs
router.post('/menu_report/weekly', (req, res) => {
  const { week_start, grade_id } = req.body;
  
  const weeklyReport = {
    week_start,
    grade_id,
    total_cost: 150000,
    avg_daily_cost: 25000,
    total_calories: 5250,
    avg_daily_calories: 875,
    nutrition_summary: {
      protein: 105,
      carbs: 315,
      fat: 84,
      fiber: 35
    },
    dishes_used: mockDishes.slice(0, 3)
  };
  
  res.json({
    success: true,
    data: weeklyReport
  });
});

module.exports = router;
