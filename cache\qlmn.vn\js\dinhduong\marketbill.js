$.marketbill = {
    module: 'marketbill',
    id: '', /*Mã project*/
    measures: null,
    warehouses: null,
    scope_a : null,
    group : null,
    list:function(month){
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
            urls.join('/'), 
            self.module, /*Đ<PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    { field:'ck', checkbox: true },
                    { title:'Ngày', field:'date', width:120},
                    { title:'Nhóm trẻ', field:'group', width:250, formatter: function(value, row){
                        if(typeof row.menu_plannings === 'string'){
                            return row.menu_plannings;
                        }else{
                            var html = [];
                            angular.forEach(row.menu_plannings,function(group,index){
                                angular.forEach($.marketbill.groups, function(item,i){
                                    if(item.id == group.group_id){
                                        html.push(item.name);
                                        return;
                                    }
                                })
                            })
                            return html.join(', ');
                        }
                    } },
                    { title:'<PERSON><PERSON><PERSON> thực đơn theo nhó<PERSON> trẻ', field:'group_ma', width:250, formatter: function(value, row){
                            var html = [];
                        if(typeof row.menu_plannings === 'object'){
                            angular.forEach(row.menu_plannings,function(group,index){
                                html.push(group.row.mathucdon)
                            })
                        }
                        return html.join(', ');
                    } },
                    { title:'Chức năng', field:'is_bill', align: 'center', width:80, formatter: function(value, row){
                        value = `<button type="button" class="btn-link btn-outline" onclick="$.marketbill.phieukechoShowForm('`+row.date+`')">
                                    <span class="glyphicon glyphicon-copy"></span> Phiếu kê chợ
                                </button>`;
                        return value;
                    } },
                    { title:'', field:'unit_id', width: 50, formatter:function(value,row,index){
                        var html = ``;
                        if(row.is_bill){
                            html = `
                                <label onclick="$.marketbill.del_bill('`+row.date+`')" class='hover-pointer color-red btn-over-red' style="margin-bottom:0px;">
                                    <span class="hover-pointer glyphicon glyphicon-trash"></span> Xóa
                                </label>
                               `;
                        }
                        return html;
                    } }
                ]
            ],
            {
                /*view: groupview,
                groupField: 'date',
                groupFormatter: function(id,rows){
                    var val = '';
                    return rows[0].date + ': <i>' + rows.length+'</i>';
                },*/onDblClickRow: function(index, row) {
                    $.marketbill.phieukechoShowForm(row.date);
                },queryParams: {month: month},
                pageSize: 100
            }
        );
    }, phieukechoShowForm: function(date,type,callback){
        $.marketbill.initMarketBill();
        var self = this;
        var dialog = null;
        var warehouse_ids = [];
        $.each($.marketbill.warehouses, function(index,warehouse){
            if(warehouse.selected){
                warehouse_ids.push(warehouse.id);

            }
        });
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'',
                title:'Phiếu kê chợ ngày '+date,
                size: size.wide,
                fullScreen: true,
                showButton: false,
                content: function(element,dialogRef){
                    dialog = dialogRef;
                    loadForm($CFG.project+'/'+self.module,'marketbill', {date: date, warehouse_ids: warehouse_ids.join(','), dataType: 'json',type: type}, function(resp){
                        if(typeof callback == "function"){
                            callback();
                        }
                        var data = resp.data;
                        $.marketbill.angular(element, resp.html,function(scope){
                            scope.marketbill.isVin = data.isVin;
                            scope.marketbill.data = {};
                            scope.school_point = {
                                points: data.school_points,
                                together: data.school_point_together
                            };
                            /*xử lý cột cho từng nhóm trẻ*/
                            data.groupcols = {};
                            var groups = [];
                            angular.forEach(data.groups, function(group,group_id){
                                group.name = scope.marketbill.getGroup(group.group_id).name;
                                groups.push({
                                    group_id: parseInt(group_id),
                                    id: parseInt(group.group_id),
                                    name: group.name,
                                    selected: true,
                                    mathucdon: group.mathucdon,
                                    siso: group.siso,
                                    siso_real: group.siso_real,
                                    sotres: group.sotres,
                                    tien1tre: group.tien1tre
                                })
                            });
                            /*sắp xếp lại theo thứ tự nhóm trẻ*/
                            for(var i=0; i<groups.length-1; i++) {
                                for(var j=i+1; j<groups.length; j++) {
                                    if(groups[i].id>groups[j].id) {
                                        var tmp = groups[i];
                                        groups[i] = groups[j];
                                        groups[j] = tmp;
                                    }
                                }
                            }
                            data.groups = groups;
                            angular.forEach(groups, function(group,ind){
                                data.groupcols[group.group_id+'_1'] = {id: group.group_id, name: 'Số lượng', field: 'quantity'}
                                data.groupcols[group.group_id+'_2'] = {id: group.group_id, name: 'Thành tiền', field: 'thanhtien'}
                            });
                            /*Xử lý dữ liệu đi chợ, xuất kho*/
                            var warehouses = data.foods;
                            var market = {};
                            var tmp_export = {};
                            var total = {
                                'market': {all: 0, groups:{}},
                                'export': {all: 0, groups:{}},
                                'market_w': {},
                                'export_w': {}
                            };
                            angular.forEach(data.groups, function(group,group_id) {
                                total.market.groups[group.group_id] = 0;
                                total.export.groups[group.group_id] = 0;
                            });
                            var cash_book = JSON.parse(data.cash_book);
                            var exp2mrk = {};
                            if (cash_book.markets){
                                angular.forEach(cash_book.markets, (mrk, warehouse_id)=>{
                                    angular.forEach(mrk.foods, (food, food_key)=>{
                                        if (food_key.split('_').length == 1){
                                            exp2mrk[warehouse_id] || (exp2mrk[warehouse_id] = {});
                                            exp2mrk[warehouse_id][food.food_id] || (exp2mrk[warehouse_id][food.food_id] = {
                                                food_id: food.food_id,
                                                price: food.price,
                                                name: food.name,
                                                price_kg: food.price_kg,
                                                quantity: food.thucmuatheodvt,
                                                groups: {}
                                            });
                                            angular.forEach(food.groups, (group, group_key)=>{
                                                exp2mrk[warehouse_id][food_key].groups[group_key] = {
                                                    quantity: group.thucmuatheodvt
                                                }
                                            })
                                        }
                                    })
                                })
                            }
                            angular.forEach(warehouses, function(foods, warehouse_id) {
                                total.market_w[warehouse_id] = {all: 0, groups:{}};
                                total.export_w[warehouse_id] = {all: 0, groups:{}};
                                // exported[warehouse_id] || (exported[warehouse_id] = {});
                                angular.forEach(data.groups, function(group,group_id) {
                                    total.export_w[warehouse_id].groups[group.group_id]=0;
                                    total.market_w[warehouse_id].groups[group.group_id]=0;
                                });
                                angular.forEach(foods, function(food,fd_id) {
                                    var measure = scope.marketbill.getMeasure(food.measure_id);
                                    food.measure_name = measure.name;
                                    food.quantity = 0; /*Tổng số lượng các nhóm*/
                                    if(food.export) {
                                        var food_id = food.food_id;
                                        tmp_export[warehouse_id] || (tmp_export[warehouse_id] = {});
                                        tmp_export[warehouse_id][food_id] || (tmp_export[warehouse_id][food_id] = {
                                            food_id: food.food_id,
                                            extrude_factor: food.extrude_factor,
                                            product_of: food.product_of,
                                            supplier: food.supplier,
                                            is_dry: food.is_dry,
                                            measure_id: food.measure_id,
                                            measure_name: food.measure_name,
                                            name: food.name,
                                            price: food.price,
                                            quantity_groups: {},
                                            quantity_real : []
                                        });
                                        angular.forEach(food.quantity_real, function (group_real) {
                                            tmp_export[warehouse_id][food_id].quantity_real.push(group_real);
                                        });
                                        angular.forEach(food.quantity_groups, function(quantity_group, group_id){
                                            if(!tmp_export[warehouse_id][food_id].quantity_groups[group_id]) {
                                                tmp_export[warehouse_id][food_id].quantity_groups[group_id] = quantity_group;
                                            }else{
                                                tmp_export[warehouse_id][food_id].quantity_groups[group_id].quantity += quantity_group.quantity;
                                            }
                                        });
                                        /*  Sắp xếp lại */
                                        var texp = [];
                                        angular.forEach(tmp_export[warehouse_id][food_id].quantity_groups, function(group, group_id){
                                            group.group_id || (group.group_id = group_id);
                                            texp.push(group);
                                        });
                                        for(var i=0; i<texp.length-1; i++) {
                                            for(var j=i+1; j<texp.length; j++) {
                                                if(scope.marketbill.groups[texp[i].group_id].group_id > scope.marketbill.groups[texp[j].group_id].group_id) {
                                                    var tmp = texp[i];
                                                    texp[i] = texp[j];
                                                    texp[j] = tmp;
                                                }
                                            }
                                        }
                                        tmp_export[warehouse_id][food_id].quantity_groups = texp;
                                    }else{
                                        var food_id = fd_id;
                                        market[warehouse_id] || (market[warehouse_id] = {});
                                        angular.forEach(food.quantity_groups, function(quantity_group, group_id){
                                            total.market.groups[group_id] || (total.market.groups[group_id] = 0);
                                            food.quantity += quantity_group.quantity;
                                            quantity_group.thanhtien = quantity_group.quantity * food.price;
                                            total.market.all += quantity_group.thanhtien;
                                            total.market.groups[group_id] += quantity_group.thanhtien;
                                            total.market_w[warehouse_id].all +=  quantity_group.thanhtien;
                                            total.market_w[warehouse_id].groups[group_id] +=  quantity_group.thanhtien;
                                        });
                                        market[warehouse_id][food_id] = food;
                                    }
                                    /*
                                    * Thêm thực phẩm kho thiếu chuyển sang đi chợ
                                    * */
                                    if (exp2mrk[warehouse_id]){
                                        if (exp2mrk[warehouse_id][food.food_id]){
                                            market[warehouse_id] || (market[warehouse_id] = {});
                                            var food = clone(food);
                                            food.quantity = 0;
                                            food.price = exp2mrk[warehouse_id][food.food_id].price;
                                            food.price_kg = exp2mrk[warehouse_id][food.food_id].price_kg;
                                            angular.forEach(exp2mrk[warehouse_id][food.food_id].groups, function(quantity_group, group_id){
                                                if (food.quantity_groups[group_id] != undefined) {
                                                    total.market.groups[group_id] || (total.market.groups[group_id] = 0);
                                                    food.quantity = $['+'](food.quantity, quantity_group.quantity);
                                                    food.quantity_groups[group_id].quantity = quantity_group.quantity;
                                                    food.quantity_real[group_id].quantity = quantity_group.quantity;
                                                    var thanhtien = quantity_group.quantity * food.price;
                                                    food.quantity_groups[group_id].thanhtien = thanhtien;
                                                    food.quantity_real[group_id].thanhtien = thanhtien;
                                                    food.quantity_groups[group_id].quantity_sotres =
                                                        scope.divide(quantity_group.quantity, food.quantity_groups[group_id].sotres);
                                                    food.quantity_real[group_id].quantity_sotres =
                                                        scope.divide(quantity_group.quantity, food.quantity_real[group_id].sotres);
                                                    total.market.all += thanhtien;
                                                    total.market.groups[group_id] += thanhtien;
                                                    total.market_w[warehouse_id].all +=  thanhtien;
                                                    total.market_w[warehouse_id].groups[group_id] +=  thanhtien;
                                                }
                                            });
                                            market[warehouse_id][food_id] = food;
                                        }
                                    }
                                });
                            });
                            /*Tách thực phẩm xuất kho theo giá */
                            data.export = {};

                            //Tạo map menu_information
                            scope.marketbill.mapMenuInformations = {};
                            scope.marketbill.menu_informations.forEach(function (value) {
                                scope.marketbill.mapMenuInformations[value.define] = value.warehouse_id;
                            });

                            angular.forEach(tmp_export, function(fds,warehouse_id){
                                data.export[warehouse_id] = {};
                                var warehouse = warehouse_id;
                                if(warehouse == 1)
                                    warehouse = scope.marketbill.mapMenuInformations['buasang']; //Check bữa sáng kho trưa
                                var inventories = data.storages[warehouse];
                                inventories || (inventories = {});
                                angular.forEach(fds,function(fd,fd_id){
                                    if(inventories[fd.food_id]){
                                        var storage = inventories[fd.food_id];
                                        angular.forEach(fd.quantity_groups, function(quantity_group, ind){
                                            var group_id = quantity_group.group_id;
                                            var quantity_need = quantity_group.quantity;
                                            var group_end = null;
                                            angular.forEach(storage.foods, function(inventory, food_key){
                                                if(inventory.ton == undefined){
                                                    inventory.ton = inventory.inventory;
                                                }
                                                if(!data.export[warehouse_id][food_key]) {
                                                    data.export[warehouse_id][food_key] = {
                                                        name: fd.name,
                                                        price: inventory.price,
                                                        extrude_factor: fd.extrude_factor,
                                                        id: fd.id,
                                                        is_dry: fd.is_dry,
                                                        measure_id: fd.measure_id,
                                                        measure_name: fd.measure_name,
                                                        product_of: fd.product_of,
                                                        quantity_groups: {},
                                                        supplier: fd.supplier,
                                                        quantity: 0,
                                                        sotres: fd.sotres
                                                    };
                                                }
                                                if(!data.export[warehouse_id][food_key].quantity_groups[group_id]) {
                                                    data.export[warehouse_id][food_key].quantity_groups[group_id] = {
                                                        number_child: quantity_group.number_child,
                                                        quantity: 0,
                                                        sotres: quantity_group.sotres
                                                    }
                                                }
                                                if(quantity_need<=inventory.ton){
                                                    data.export[warehouse_id][food_key].quantity_groups[group_id].quantity = $['+'](data.export[warehouse_id][food_key].quantity_groups[group_id].quantity, quantity_need);
                                                    inventory.ton = $['-'](inventory.ton, quantity_need);
                                                    quantity_need = 0;
                                                }else{
                                                    data.export[warehouse_id][food_key].quantity_groups[group_id].quantity = $['+'](data.export[warehouse_id][food_key].quantity_groups[group_id].quantity, inventory.ton);
                                                    quantity_need = $['-'](quantity_need, inventory.ton);
                                                    inventory.ton = 0;
                                                }
                                                group_end = data.export[warehouse_id][food_key].quantity_groups[group_id];
                                                if(quantity_need<=0){
                                                    return;
                                                }
                                            });
                                            if(quantity_need>0){
                                                var move_market = false;
                                                if (exp2mrk[warehouse_id]){
                                                    if (exp2mrk[warehouse_id][fd.food_id]){
                                                        move_market = true;
                                                    }
                                                }
                                                if (!move_market){
                                                    group_end.quantity = $['+'](group_end.quantity, quantity_need);
                                                }else{
                                                    group_end.quantity_sotres =
                                                        scope.divide(group_end.quantity, group_end.sotres);
                                                }
                                            }
                                        });
                                        storage = inventories[fd.food_id];
                                        angular.forEach(fd.quantity_real, function(quantity_real, ind){
                                            var group_id = quantity_real.group_id;
                                            var quantity_need = quantity_real.quantity;
                                            var group_end = null;
                                            angular.forEach(storage.foods, function(inventory, food_key){
                                                if(inventory.ton == undefined){
                                                    inventory.ton = inventory.inventory;
                                                }
                                                if(!data.export[warehouse_id][food_key]) {
                                                    data.export[warehouse_id][food_key] = {
                                                        name: fd.name,
                                                        price: inventory.price,
                                                        extrude_factor: fd.extrude_factor,
                                                        id: fd.id,
                                                        is_dry: fd.is_dry,
                                                        measure_id: fd.measure_id,
                                                        measure_name: fd.measure_name,
                                                        product_of: fd.product_of,
                                                        quantity_real: {},
                                                        supplier: fd.supplier,
                                                        quantity: 0,
                                                        sotres: fd.sotres
                                                    };
                                                }
                                                data.export[warehouse_id][food_key].quantity_real = data.export[warehouse_id][food_key].quantity_real || {};
                                                if(!data.export[warehouse_id][food_key].quantity_real[group_id]) {
                                                    data.export[warehouse_id][food_key].quantity_real[group_id] = {
                                                        number_child: quantity_real.number_child,
                                                        real: 0,
                                                        sotres: quantity_real.sotres
                                                    }
                                                }
                                                if(quantity_need<=inventory.ton){
                                                    data.export[warehouse_id][food_key].quantity_real[group_id].real =
                                                        $['+'](data.export[warehouse_id][food_key].quantity_real[group_id].real, quantity_need);
                                                    inventory.ton = $['-'](inventory.ton, quantity_need);
                                                    quantity_need = 0;
                                                }else{
                                                    data.export[warehouse_id][food_key].quantity_real[group_id].real =
                                                        $['+'](data.export[warehouse_id][food_key].quantity_real[group_id].real, inventory.ton);
                                                    quantity_need = $['-'](quantity_need, inventory.ton);
                                                    inventory.ton = 0;
                                                }
                                                group_end = data.export[warehouse_id][food_key].quantity_real[group_id];
                                                if(quantity_need<=0){
                                                    return;
                                                }
                                            });
                                            if(quantity_need>0){
                                                var move_market = false;
                                                if (exp2mrk[warehouse_id]){
                                                    if (exp2mrk[warehouse_id][fd.food_id]){
                                                        move_market = true;
                                                    }
                                                }
                                                if (!move_market){
                                                    group_end.real = $['+'](group_end.real, quantity_need);
                                                }else{
                                                    group_end.quantity_sotres =
                                                        scope.divide(group_end.quantity, group_end.sotres);
                                                }
                                            }
                                        });
                                    }else{
                                        var food_key = fd.food_id+'_'+fd.price;
                                        data.export[warehouse_id][food_key] = fd;
                                    }
                                });
                                var fds = {};
                                angular.forEach(data.export[warehouse_id], function(food, food_id_price){
                                    angular.forEach(food.quantity_groups, function(group, group_id){
                                        food.quantity = $['+'](food.quantity, group.quantity);
                                    });
                                    if(food.quantity>0){
                                        fds[food_id_price] = food;
                                    }
                                });
                                data.export[warehouse_id] = fds;
                            });

                            // Kiểm tra xem có thực phẩm xuất kho 2 giá không?
                            var food_export_multi_price = false;
                            var all_export_food_ids = {1:{}, 2:{}};
                            angular.forEach(data.export, function(foods, warehouse_id){
                                angular.forEach(foods, function(food,food_id){
                                    var tmp_fids = food_id.split('_');
                                    if(all_export_food_ids[warehouse_id][tmp_fids[0]]==undefined){
                                        all_export_food_ids[warehouse_id][tmp_fids[0]] = 1;
                                    }else{
                                        food_export_multi_price = true;
                                        all_export_food_ids[warehouse_id][tmp_fids[0]] += 1;
                                    }
                                    angular.forEach(food.quantity_groups, function(quantity_group,group_id){
                                        quantity_group.thanhtien = quantity_group.quantity * food.price;
                                        /*Thêm làm tròn cho đồng nhất với stta*/
                                        // quantity_group.thanhtien = round(quantity_group.thanhtien,1);
                                        total.export.all += quantity_group.thanhtien;
                                        total.export.groups[group_id] += quantity_group.thanhtien;

                                        total.export_w[warehouse_id].all +=  quantity_group.thanhtien;
                                        total.export_w[warehouse_id].groups[group_id] +=  quantity_group.thanhtien;
                                        var soluong = 0;
                                        school_points = {};
                                        point = 0;
                                        angular.forEach(quantity_group.sotres, function(sotre, school_point){
                                            if(sotre > 0){
                                                var tile = sotre/quantity_group.number_child;
                                                var quantity = tile*quantity_group.quantity;
                                                soluong = $['+'](soluong, quantity);
                                                school_points[school_point] = quantity;
                                                point = school_point;
                                            }else{
                                                school_points[school_point] = 0;
                                            }
                                        });
                                        if(quantity_group.sotres[1] > 0) {
                                            point = 1;
                                        }
                                        if(soluong != quantity_group.quantity && school_points[1] > 0) {
                                            school_points[1] = $['+'](school_points[point], $['-'](quantity_group.quantity, soluong));
                                        }
                                        quantity_group.school_points = school_points;
                                    });

                                    angular.forEach(food.quantity_real, function(quantity_real,group_id){
                                        var soluong = 0;
                                        school_points = {};
                                        point = 0;
                                        angular.forEach(quantity_real.sotres, function(sotre, school_point){
                                            if(sotre > 0){
                                                var tile = sotre/quantity_real.number_child;
                                                var real = tile*quantity_real.real;
                                                soluong = $['+'](soluong, real);
                                                school_points[school_point] = real;
                                                point = school_point;
                                            }else{
                                                school_points[school_point] = 0;
                                            }
                                        });
                                        if(quantity_real.sotres[1] > 0) {
                                            point = 1;
                                        }
                                        if(soluong != quantity_real.real && school_points[1] > 0) {
                                            school_points[1] = $['+'](school_points[point], $['-'](quantity_real.quantity, soluong));
                                        }
                                        quantity_real.school_points = school_points;
                                    });
                                });
                            });
                            // TP 2 giá => Cập nhật giá trị vào field hack_pkc_export trong PKC
                            if(food_export_multi_price && scope.sys.configs.pkc_resolve_food_multi_price){
                                process($CFG.project + '/marketbill/update_hack_pkc_export', {
                                    date: date,
                                    hack_pkc_export: data.export,
                                }, function () {
                                }, null, false, false);
                            }
                            /*  sắp sếp cùng 1 loại thực phẩm nhưng khác giá gần nhau */
                            /* Chuyển sang mảng */
                            var mks = {};
                            angular.forEach(market, function(foods, warehouse_id){
                                mks[warehouse_id] = [];
                                angular.forEach(foods, function(food, food_id_price){
                                    food.food_id_price = food_id_price;
                                    mks[warehouse_id].push(food);
                                });
                            });
                            /*  Sắp xếp gần nhau thôi */
                            angular.forEach(mks, function(foods, warehouse_id){
                                for(var i=0; i< foods.length-1; i++){
                                    for(var j=i; j< foods.length; j++){
                                        if(foods[i].name < foods[j].name){
                                            var tmp = foods[i];
                                            foods[i] = foods[j];
                                            foods[j] = tmp;
                                        }
                                    }
                                }
                            });
                            data.market = mks;
                            data.total = total;
                            // data.tiepphamkho = data.export;
                            scope.marketbill.data = data;
                            scope.marketbill.warehouse_ids = warehouse_ids.toString();
                            setTimeout(function(){
                                $('#left-container').css({
                                    height: $('body').height()-80
                                });

                            },400)
                            $( window ).resize(function() {
                                $('#left-container').css({
                                    height: $('body').height()-80
                                });
                            })
                        });
                    })
                },onshown: function(dialogRef,body){

                }
            }
        );
    }, optionExportOrMarket: function(scope,callback){
    	var self = this;
    	$.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'',
                title:'Chọn thực phẩm xuất kho hay đi chợ',
                size: size.wide,
                fullScreen: false,
                showButton: false,
                // closable: false,
                content: function(element,dialogRef){
                    dialog = dialogRef;
                    loadForm($CFG.project+'/'+self.module,'optionExportOrMarket', {}, function(resp){
                    	$.marketbill.angular(element,resp,function(scope){
                    	})
                    })
                },buttons: [
                	{
                        id: 'btn-ok',
                        icon: 'glyphicon glyphicon-ok',
                        label: 'Áp dụng',
                        cssClass: 'btn-primary',
                        action: function(dialogRef){
                            scope.$apply(function(){
                                callback(dialogRef);
                                dialogRef.close();
                            });
                        }
                    }
                ]
            }
        );
    }, cancelEditor:function(index){
        $('#tbl_marketbill').datagrid('cancelEdit', index);
    }, saverow: function(index){
        var self = this;
        var item = {};
        var ed = $('#tbl_marketbill').datagrid('getEditor', {
            index: index,
            field: 'warehouse_id'
        });
        item.warehouse_id = $(ed.target).combobox('getValue');
        ed = $('#tbl_marketbill').datagrid('getEditor', {
            index: index,
            field: 'price'
        });
        item.price = $(ed.target).combobox('getText');
        ed = $('#tbl_marketbill').datagrid('getEditor', {
            index: index,
            field: 'quantity'
        });
        item.quantity = $(ed.target).combobox('getText');
        var data = $('#tbl_marketbill').datagrid('getData');
        if(!data.rows){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var row = data.rows[index];
        if(!row){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        item.id = row.id;
        var action = $CFG.project+'/marketbill/edit';
        process(action, {data: item}, function(resp){
            if(resp.result == 'success') {
                $('#tbl_marketbill').datagrid('endEdit', index);
                // $.marketbill.cancelEditor(index);
            }
        });
    }, addAction: function(id){
        if(!id) return;
        if($('.btn-add.add-new').css('opacity')+'' != '1'){
            return;
        }
        var self = this;
        var data = arrayToJson(getSubmitForm(id,true));
        var action = $CFG.project+'/marketbill/add';
        process(action, {data:data}, function(resp){
            if(resp.result == 'success') {
                $("#tbl_"+self.module).datagrid('reload');
                $('#ma_thuc_pham').combobox('clear');
                $('.btn-add.add-new').css('opacity',0);
            }
        });
    }, doSearch: function(scope){
            setTimeout(function(){
                // var scope = $.marketbill.scope;
                // if(!scope){
                //     return;
                // }
                var opt_search = {};
                // if(scope.marketbill.opt_searching_warehouse){
                    opt_search.warehouse_id = {value: $.marketbill.keysearch_warehouse_id,op:'equal'};
                // }
                // if(scope.marketbill.opt_searching_date){
                    opt_search.date = {value: $.marketbill.keysearch_date,op:'equal'};
                // }
                // console.log(opt_search);
                if(count(opt_search)>0){
                //     $.dm_datagrid.doSearch('tbl_marketbill',opt_search,'and');
                // }else{
                //     $.dm_datagrid.doSearch('tbl_marketbill',{},'and');
                }
                // console.log($.marketbill.keysearch_date);
                var options = $('#tbl_marketbill').datagrid({
                    queryParams: {
                        date: $.marketbill.keysearch_date,
                        kho: $.marketbill.keysearch_warehouse_id
                    }
                });
                // var param = options.param
                // console.log(options);
            },300)

    }, doShow : function(){
        var checkboxs= $('.nhom_tre_checked');
        $.each(checkboxs , function(index , element){
            if($(element).prop('checked')) {
                $('#tbl_marketbill').datagrid('showColumn', 'group_'+$(element).val());
                $('#tbl_marketbill').datagrid('showColumn', 'soluong_'+$(element).val());
                $('#tbl_marketbill').datagrid('showColumn', 'thanhtien_'+$(element).val());
            } else {
                $('#tbl_marketbill').datagrid('hideColumn', 'group_'+$(element).val());
                $('#tbl_marketbill').datagrid('hideColumn', 'soluong_'+$(element).val());
                $('#tbl_marketbill').datagrid('hideColumn', 'thanhtien_'+$(element).val());
            }
        });
    }, del: function(index){ // XÓA
        var self = this;
        var data = $('#tbl_marketbill').datagrid('getData');
        if(!data.rows){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var row = data.rows[index];
        if(!row){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>'];
        msg.push(' - Dữ liệu sau khi xóa sẽ không thể khôi phục.');
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
            if (r){
                process($CFG.project+'/'+self.module+'/del',{ids: row.id},function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                })
            }
        });
        setTimeout(function(){
            $('#tbl_marketbill').datagrid('cancelEdit', index);

        },0);
    },del_bill: function(date,callback){ // XÓA
        var self = this;
        // console.log(date);
        var msg = ['<div style = "font-size: 14px; font-weight:bold; color:red;">Chắc chắn xóa ?</div>'];
        msg.push('<span style="color:red;"> - Dữ liệu sau khi xóa sẽ không thể khôi phục.</span>');
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
            if (r){
                process($CFG.project+'/'+self.module+'/del_bill',{date: date,async:true},function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                    if(typeof callback == "function"){
                        callback(resp);
                    }
                })
            }
        });
    },
    angular: function(element,resp,callback,dialogRef){
        var form = '<div style="height: 100%; width:100%; display: table;">'+resp+'</div>';
            // $(element).html(form);
        angular.element($('#mainContentController')).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        });

    },
    initMarketBill: function () {
        setTimeout(function () {
            angular.element($('#mainContentController')).scope().$apply(function (scope) {
                scope_a = scope;
                $.marketbill.scope = scope;
                scope.marketbill = {};
                scope.addSupplierNull = false; //Option
                scope.marketbill.selected = {};
                scope.marketbill.opt_searching_warehouse = false;
                scope.marketbill.opt_searching_date = false;
                setTimeout(function () {
                    process($CFG.project + '/menu_report/marketBill', {}, function (resp) {
                        scope.$apply(function () {
                            $.marketbill.groups = scope.menu_report.groups;
                            $.marketbill.warehouses = scope.menu_report.warehouses;
                            scope.marketbill.warehouses = scope.menu_report.warehouses;
                            scope.marketbill.groups = scope.menu_report.groups;
                            scope.marketbill.measures = resp.measures;
                            scope.marketbill.menu_informations = resp.menu_informations;
                            scope.marketbill.months = scope.menu_report.months;
                            scope.marketbill.suppliers = resp.suppliers;
                            scope.marketbill.selected.group = scope.marketbill.groups[0];
                            scope.marketbill.selected.warehouse = 1;
                            scope.marketbill.selected.month = scope.marketbill.months[0];
                            for (var i in resp.warehouses) {
                                if (resp.warehouses[i].id + '' == '2') {
                                        resp.warehouses[i].selected = true;
                                    break;
                                }
                            }
                            $.marketbill.list(scope.marketbill.selected.month.id);
                            /*Set lại cấu hinh do khác controller*/
                            scope.sys = {};
                            scope.sys.configs = window.sysConfigs.configs;
                        });
                    }, null, false);
                    scope.marketbill.getMeasure = function (measure_id) {
                        var rs = scope.marketbill.measures[0];
                        for (var i in scope.marketbill.measures) {
                            if (scope.marketbill.measures[i].id + '' == measure_id + '') {
                                rs = scope.marketbill.measures[i];
                                break;
                            }
                        }
                        return rs;
                    };
                    scope.marketbill.getGroup = function (group_id) {
                        for (var i in scope.marketbill.groups) {
                            if (group_id == scope.marketbill.groups[i].id) {
                                return scope.marketbill.groups[i];
                            }
                        }
                    };

                    scope.marketbill.checkPkcGroup = function () {
                        var pkc_groups = document.getElementsByName('chk_pkc_group');
                        var group_ids = [];
                        var len = pkc_groups.length;
                        for (var i = 0; i < len; i++) {
                            var chk_group_id = pkc_groups[i].id;
                            if(document.getElementById(chk_group_id).checked) {
                                group_id = chk_group_id.replace("chk_pkc_group_", "");
                                group_ids.push(parseInt(group_id));
                            }
                        }
                        return group_ids.join(',');
                    };

                    scope.marketbill.exportForm = function (date, id, type) {
                        scope.marketbill.id = id;
                        scope.marketbill.date = date;
                        if(!scope.addSupplierNull){
                            scope.marketbill.suppliers.push({id: "", unit_id: "", phone: "", shipper: "", boss: "", name: ""});
                            scope.addSupplierNull = true;
                        }
                        var ss = date.split("/");
                        date = parseInt(ss[2], 10) + "-" + parseInt(ss[1], 10) + "-" + parseInt(ss[0], 10);
                        var export_type = 'exportExcelPhieuKeCho';
                        if(id === 9){
                            scope.marketbill.data.total.export.all = 0;
                            /*Thêm lượng xuất thực tế nếu có sổ tính tiền ăn trương export*/
                            process($CFG.project + '/marketbill/getExportInCashBook', {async: true, date: date}, function (resp) {
                                var cashbook = resp.data;
                                var check = cashbook.length !== 0;
                                angular.forEach(scope.marketbill.data.export, function (warehouse, key) {
                                    angular.forEach(warehouse, function (food, index) {
                                        if(check){
                                            scope.marketbill.data.export[key][index].export = (((cashbook[key] || {}).foods || {})[index] || {}).thucmuatheodvt;
                                            scope.marketbill.data.export[key][index].money = scope.marketbill.data.export[key][index].price * scope.marketbill.data.export[key][index].export;
                                        }else
                                            scope.marketbill.data.export[key][index].money = scope.marketbill.data.export[key][index].price * scope.marketbill.data.export[key][index].quantity;
                                        scope.marketbill.data.total.export.all += scope.marketbill.data.export[key][index].money;
                                    });
                                });
                            });
                        }
                        switch (id) {
                            case 1 :
                                export_type = 'exportExcelPhieuKeCho';
                                scope.marketbill.data.counter = {
                                    market: [],
                                    export: [],
                                    total: [],
                                    groups: []
                                };
                                var counter = 7;
                                scope.marketbill.data.counter.market.push(counter);
                                Object.keys(scope.marketbill.data.market).map(index => {
                                    counter += (1 + Object.keys(scope.marketbill.data.market[index]).length); // title + foods
                                    scope.marketbill.data.counter.market.push(counter);
                                });
                                scope.marketbill.data.counter.export.push(counter++);

                                scope.marketbill.data.counter.export.push(counter);
                                Object.keys(scope.marketbill.data.export).map(index => {
                                    counter += (1 + Object.keys(scope.marketbill.data.export[index]).length); // title + foods
                                    scope.marketbill.data.counter.export.push(counter);
                                });

                                var cols = ["H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T"];
                                if (count(scope.marketbill.data.market) <= 0) {
                                    counter -= 1;
                                }
                                if (count(scope.marketbill.data.export) <= 0) {
                                    counter -= 1;
                                }
                                Object.keys(scope.marketbill.data.groups).map((key, index, map) => {
                                    scope.marketbill.data.counter.groups.push(++counter);
                                    if (index < cols.length) {
                                        scope.marketbill.data.groups[key].excel = cols[index * 2];
                                        scope.marketbill.data.groups[key].merge = cols[index * 2 + 1];
                                    }
                                });

                                Object.keys(scope.marketbill.data.groupcols).map((key, index, map) => {
                                    if (index < cols.length) {
                                        scope.marketbill.data.groupcols[key].excel = cols[index];
                                        scope.marketbill.data.counter.total.push(cols[index]);
                                    }
                                });
                                break;
                            case 2:
                                export_type = 'exportExcelPhieuKeHang';
                                scope.marketbill.data.counter = {
                                    market: []
                                };
                                var counter = 12;
                                scope.marketbill.data.counter.market.push(counter);
                                Object.keys(scope.marketbill.data.market).map(index => {
                                    counter += (1 + Object.keys(scope.marketbill.data.market[index]).length); // title + foods
                                    scope.marketbill.data.counter.market.push(counter);
                                });
                                break;
                            case 3 :
                                export_type = 'exportExcelPhieuTiepPham';
                                scope.marketbill.data.counter = {
                                    market: []
                                };
                                var counter = 10;
                                scope.marketbill.data.counter.market.push(counter);
                                Object.keys(scope.marketbill.data.market).map(index => {
                                    counter += (1 + Object.keys(scope.marketbill.data.market[index]).length); // title + foods
                                    scope.marketbill.data.counter.market.push(counter);
                                });
                                break;
                            case 4 :
                                export_type = 'exportExcelTiepPhamTuoi';
                                scope.marketbill.data.counter = {
                                    market: []
                                };
                                var i = 0;
                                Object.keys(scope.marketbill.data.tiepphamtuoi).map(index => {
                                    i++;
                                    scope.marketbill.data.counter.market[index] = i;
                                    if (Object.keys(scope.marketbill.data.tiepphamtuoi[index]).length > 0) {
                                        Object.keys(scope.marketbill.data.tiepphamtuoi[index]).map(key => {
                                            i++;
                                            var cell_stt = i;
                                            if (count(scope.marketbill.data.tiepphamtuoi) <= 1) {
                                                cell_stt = i - 1; //Not show warehouse row

                                            }
                                            scope.marketbill.data.tiepphamtuoi[index][key].cell = cell_stt;
                                        })
                                    }
                                });
                                break;
                            case 5 :
                                export_type = 'exportExcelTiepPhamKho';
                                scope.marketbill.data.counter = {
                                    market: []
                                };
                                var i = 0;

                                /*Bỏ thực phẩm mà quantily bằng 0*/
                                angular.forEach(scope.marketbill.data.tiepphamkho, function(value, key) {
                                    angular.forEach(value, function(item, index) {
                                        if(item.quantity <= 0){
                                            delete scope.marketbill.data.tiepphamkho[key][index];
                                        }
                                    });
                                });
                                Object.keys(scope.marketbill.data.tiepphamkho).map(index => {
                                    i++;
                                    scope.marketbill.data.counter.market[index] = i;
                                    if (Object.keys(scope.marketbill.data.tiepphamkho[index]).length > 0) {
                                        Object.keys(scope.marketbill.data.tiepphamkho[index]).map(key => {
                                            i++;
                                            var cell_stt = i;
                                            if (count(scope.marketbill.data.tiepphamkho) <= 1) {
                                                cell_stt = i - 1; //Not show warehouse row
                                            }
                                            scope.marketbill.data.tiepphamkho[index][key].cell = cell_stt;
                                        })
                                    }
                                });
                                break;
                            case 6 :
                                export_type = 'exportExcelSoCheBien';
                                break;
                            case 7 :
                                export_type = 'exportExcelLuuHuyMau';
                                break;
                            case 8 :
                                export_type = 'exportExcelLuuMauChinh';
                                break;
                            case 9 :
                                export_type = 'exportExcelPhieuXuatKho';
                                break;
                            case 10 :
                                export_type = 'exportNhanmauthuanluu';
                                break;
                            case 11 :
                                export_type = 'exportExcelLuuMauChinh';
                                break;
                            case 12 :
                                export_type = 'exportExcelVsc';
                                break;
                            case 14 :
                                export_type = 'naFreshFood';
                                break;
/*                            case 15 :
                                export_type = 'naDryFood';
                                break;*/
                            case 16:
                                export_type = 'exportExcelGiaoNhanThucPham';
                                break;
                            case 17:
                                export_type = 'exportExcelGiaoNhanThucPham2';
                                break;
                        }
                        var urls_export = [$CFG.remote.base_url, $CFG.project, 'marketbill', export_type];
                        // var data = JSON.stringify(scope.marketbill.data);
                        // var suppliers = scope.marketbill.suppliers;
                        var key = export_type + id;
                        var group_ids = scope.marketbill.checkPkcGroup();
                        var url = urls_export.join('/') + '?type=' + type + '&date=' + date + '&preview=1&id=' + id+'&group_ids='+group_ids;
                        if (scope.sys.configs.standard && (id === 4 || id === 5)) {
                            url += '&standard=true';
                        }
                        if (!window.printpreview) {
                            window.printpreview = {};
                        }

                        console.log('urls_export: ', urls_export);
                        console.log('key: ', key);
                        console.log('group_ids: ', group_ids);
                        console.log('url: ', url);
                        
                        window.printpreview[key] = window.open(url);
                        window.printpreview[key].onload = function (el, el1) {
                            //this.init(scope.marketbill);
                        }
                    };

                    scope.marketbill.openKt3BForm = function (action, date) {
                        var group_ids = scope.marketbill.checkPkcGroup();
                        var urls_export = [$CFG.remote.base_url, $CFG.project, action];
                        var full_url = urls_export.join('/') + '?date=' + date + '&group_ids=' + group_ids;
                        if (scope.sys.configs.standard) {
                            full_url += '&standard=true';
                        }
                        window.open(full_url);
                    }

                    scope.marketbill.dateChange = function (date) {
                        var queryParams = $('#tbl_marketbill').datagrid('options').queryParams;
                        if (queryParams) {
                            if (parseInt(queryParams.month) != parseInt(date.id)) {
                                queryParams.month = date.id;
                                $('#tbl_marketbill').datagrid('load', queryParams);
                            }
                        }
                    }
                });
            });
        });
    }
};
