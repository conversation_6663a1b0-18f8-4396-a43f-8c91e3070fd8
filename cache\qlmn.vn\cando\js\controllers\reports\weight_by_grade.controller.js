(function WeightByGradeReportController(app) {
    'use strict';

    app.controller('WeightByGradeReportController', WeightByGradeReportController);

    function WeightByGradeReportController($scope, $location, AuthService, CommonService, ACTIVE_MONTHS, StudentBmiService) {
        var vm = this;

        // FIXME: can we move this logic to router config?
        if (!AuthService.isSuperAdmin()) {
            $location.path('/cando');

            return;
        }

        $scope.sys.module.title = 'Thể lực trẻ theo khối';

        vm.months = _.map(ACTIVE_MONTHS, function (month) {
            return {id: month, title: _.padStart(month, 2, '0')};
        });

        vm.months.unshift({id: 0, title: 'Chọn tháng'});

        vm.filters = {
            'school_year': $CFG.active_year || (new Date()).getFullYear(),
            'month': 0,
        };

        vm.schoolYears = CommonService.generateSchoolYear();

        vm.items = [];

        vm.onSchoolYearChanged = function onSchoolYearChanged() {
            fetchReport();
        };

        vm.onMonthChanged = function onMonthChanged() {
            fetchReport();
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentBmiService.generateDownloadExcelUrlBmiWeightByGrade(vm.filters);
        };

        function fetchReport() {
            vm.items = [];

            if (!vm.filters.month || !vm.filters.school_year) {
                return;
            }

            StudentBmiService.fetchBmiWeightByGradeReport(vm.filters)
                .then(function (items) {
                    vm.items = items;
                });
        }
    }

    WeightByGradeReportController.$inject = [
        '$scope',
        '$location',
        'AuthService',
        'CommonService',
        'ACTIVE_MONTHS',
        'StudentBmiService',
    ];
})(window.angular_app);
