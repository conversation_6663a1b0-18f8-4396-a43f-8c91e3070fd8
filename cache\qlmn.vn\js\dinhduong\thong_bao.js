setTimeout(function(){
    loadThongbao();
});
$.ajaxSetup({ cache: false });
function loadThongbao() {
    /*code: mã dự án;
        * type: 0,1,2 thoe giới hạn thời gian
        * type: 1: login; 2: trong login; 0: cả 2; 3: tất cả (không theo giới hạn thời gian)*/
    var type = 2;
    var code = 'qlmn';
    //var code = 'testing';
    var tb_is_hide_name = 'sys_thong_bao_inner_is_hide' + type + code;
    $.ajax({
        url: 'https://thongbao.vietec.com.vn/lay-thong-bao?_=' + Date.now(),
        dataType: 'json',
        data: {code: code, type: 3},
        method: 'get',
        crossDomain: true,
        async: true,
        success: function (response) {
            if (response.status == 1) {
                var items = response.data.filter(rs => rs.type == type || rs.type == 3);
				for (var i=0; i<items.length;i++) {
					var provinces = ''; // Bao gom cac tinh thanh
					var not_provinces = ''; // Khong bao gom cac tinh thanh
                    var only_unit_ids = ''; // Chỉ các trường có mã này mới áp dụng
					var jsondata = ''; // Json data cua HS
					if (items[i].json!='') {
						jsondata = JSON.parse(atob(items[i].json));
						if (typeof jsondata.provinces !=='undefined') {
							provinces = jsondata.provinces;
						}
						if (typeof jsondata.not_provinces !=='undefined') {
							not_provinces = jsondata.not_provinces;
						}
                        if (typeof jsondata.only_unit_ids !=='undefined') {
                            only_unit_ids = jsondata.only_unit_ids;
                        }
					}
					// Loc theo tinh thanh
					if((jsondata=='' || provinces=='*' || provinces.indexOf(","+$CFG.province+',')>-1 || provinces.indexOf(","+$CFG.district+',')>-1)
						&& not_provinces.indexOf(','+$CFG.province+',')==-1 && not_provinces.indexOf(","+$CFG.district+',')==-1) {
						setUpConfirm(items[i]);
						return true;
					}else if(only_unit_ids!='' && only_unit_ids.indexOf(","+$CFG.unit_id+',')>-1){
                        setUpConfirm(items[i]);
                        return true;
                    }
				}
            }
        }
    });
    function setUpConfirm(msg) {
        if (!msg) return;
        if (!msg.id) return;
        if (getCache(tb_is_hide_name + msg.id)) return;
        var width = msg.width || 500;
        var style_pointer = '';
        if(msg.link!='' && msg.link!=undefined){
            style_pointer = ' cursor:pointer;';
        }
        var div = $('<div class="contain-thong-bao"><div class="dialog-thong-bao" style="position: relative;"><style>' + getStyle() + '</style>' +
            '<div class="dialog-content"><label class="btn-dialog-close" title="Đóng"><span>&#8855;</span> Đóng</label>' +
            '<img style="max-width: 90vw;'+style_pointer+'" width="' + width + '" src="' + msg.image + '" onclick="openLink('+msg.id+',\''+msg.link+'\')"/></div></div></div>');
        var checkbox = $('<label class="input-checkbox" style="color:orange;"> &#8855; Không hiện lại</label>');
        div.find('.dialog-content').append(checkbox);
        $('body').append(div);
        $('.btn-dialog-close').click(function () {
            div.remove();
        });
        checkbox.click(function () {
            setCache(tb_is_hide_name + msg.id, true);
            div.remove();
        });
    }
}
function buildMessage(msgs) {
    if (typeof msgs != 'object') {
        return;
    }
    if (msgs.length == 0) {
        return;
    }
    var msg = [];
    for (var item of msgs) {
        msg.push($(`<span style="white-space: nowrap;">` + decodeHTML(item.content) + `</span>`).find('p').html());
    }
    var sp = '&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32; &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32; &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;.....';
    var div = $(`<div class="sort-thong-bao"><style>` + getStyleSort() + `</style></div>`);
    div.append(`<div class="tb-content"><div class="marquee">
          <div style="white-space: nowrap;">` + sp + sp + msg.join('/')+  sp  + sp + sp + `</div>
        </div><div>`);
    $('body').append(div);
}
function decodeHTML(encodedStr) {
    return $("<div/>").html(encodedStr).text();
}
function getStyleSort() {
    return `
        .sort-thong-bao{
            position: absolute;
            z-index: 100;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 25px;
            text-align: center;
        }
        .sort-thong-bao .tb-content{
            min-width: 400px;
            width: 80%;
            display: inline-block;
            
            overflow: hidden;
            position: relative;
        }
        .sort-thong-bao .tb-content .marguee{
            display: block;
            width: 100%;
            height: 30px;
            position: absolute;
            animation: marquee 8s linear infinite;
        }
    
        .marquee {
            height: 25px;
            width: 100%;
            position: relative;
            color: #22338b;
        }
        
        .marquee div {
            display: block;
            width: auto;
            height: 30px;
            position: absolute;
            animation: marquee 25s linear infinite;
        }
        
        .marquee span {
            float: left;
            width: auto;
        }
        
        @keyframes marquee {
            0% { left: 0; }
            100% { left: -100%; }
        }
    `;
}
function getStyle() {
    var style = `
        .dialog-thong-bao{
            margin-top: 20px;
        }
        .dialog-thong-bao .btn-dialog-close{
            position: absolute;
            color: #344144;
            font-weight: bold;
            cursor: pointer;
            font-size: 20px;
            right: 15px;
            top: 10px;
			color: orange;
        }
        .dialog-thong-bao .btn-dialog-close>span{
            font-size: 25px;
        }
        .dialog-thong-bao .btn-dialog-close:hover{
            color: white;
        }
        .contain-thong-bao{
            position: fixed !important;
            height: 100% !important;
            width: 100% !important;
            background: rgba(100,100,100,0.6) !important;
            box-shadow: unset !important;
            padding: 0px !important;
            text-align: center;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        .window-shadow{
            background: rgba(10,10,10,0.5);
        }
        .dialog-thong-bao .dialog-content{
            position: relative;
            display: inline-block;
            width: auto;
        }
        .dialog-thong-bao .input-checkbox{
            display: block;
            margin-top: -18px;
            cursor: pointer;
        }
        .dialog-thong-bao .input-checkbox:hover{
            color: orange;
            font-weight: unset;
        }
    `;
    return style;
}
/*
* exdays: giây  || nếu không set thì mặc định hết hạn sau 1 ngày
* */
function setCache(c_name, value, exdays) {
    if (typeof value === 'object') {
        value = JSON.stringify(value);
    }
    localStorage[c_name] = value;
    if (typeof exdays != 'number' ) {   /* Mặc định lưu 1 ngày */
        exdays = 60 * 60 * 24 * 1000;
    }else{
        exdays = exdays * 1000;
    }
    var exdate = new Date().getTime();
    localStorage.setItem(c_name + '_exp', exdate + exdays);
};

function getCache(c_name) {
    var exdate = new Date().getTime();
    var exp = localStorage.getItem(c_name + '_exp');
    if (exp) {
        exp = Number(exp);
        if (exp < exdate) {
            console.log('hết hạn cockie');
            return;
        }
    }
    var value = localStorage.getItem(c_name);
    if (value) {
        if (value === 'true') {
            value = true;
        } else if ( value === 'false' ) {
            value = false;
        } else if (value[0] === '[' && value[value.length-1] === ']' || value[0] === '{' && value[value.length-1] === '}') {
            value = JSON.parse(value);
        }
    }
    return value;
};

/* Người dùng click vào thông báo */
function openLink(msg_id, link){
    var unit_id = $CFG.unit_id;
    var user_id = $CFG.user_id;
    var base_api = $CFG.base_http_url+'/api/notify/update_view'
    if(link!='' && link!=undefined && link!='null'){
        $.ajax({
            url: base_api,
            dataType: 'json',
            data: {user_id:user_id, unit_id:unit_id, msg_id:msg_id, link:link, code:'qlmn', page:'main'},
            method: 'POST',
            crossDomain: true,
            async: true,
            success: function (response) {
                window.open(link);
            }
        });
    }
}
