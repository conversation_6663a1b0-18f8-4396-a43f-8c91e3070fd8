angular_app.controller('appController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', function ($scope, $routeParams, $compile, MyCache, $filter, $cookies) {
    $scope.$CFG = $CFG;
    $scope.namhoc = $CFG.namhoc;
    $scope.$CFG || ($scope.$CFG = {});
    $scope.$CFG.round_number_config || ($scope.$CFG.round_number_config = 3);
    $scope.$CFG.digit_grouping_char || ($scope.$CFG.digit_grouping_char = ',');
    $scope.window = {
        screen: {
            width: $(window).width(),
            height: $(window).height()
        }
    };
    $(window).resize(function () {
        screen_size = {
            width: $(window).width(),
            height: $(window).height()
        };
        setTimeout(function () {
            $scope.$apply(function () {
                $scope.window.screen = screen_size;
            });
        });
    });

    $scope.filter = function (arr, key) {
        return $filter('filter')(arr, key);
    };
    $scope.rand = function (min, max) {
        return rand(min, max);
    };
    $scope.compile = function (html, scope) {
        if (scope) {
            return $compile(html)(scope);
        } else {
            return $compile(html)($scope);
        }
    };
    $scope.parseInt = function (value) {
        value || (value = 0);
        return parseInt(value);
    };
    $scope.parseFloat = function (value) {
        value || (value = 0);
        return parseFloat(value);
    };
    $scope.NaN = function (value, resp) {
        if (resp == undefined) {
            resp = '';
        }
        if (value + '' == 'NaN') {
            value = resp;
        }
        return value;
    };
    $scope.digit_grouping = function (value, num) {
        return digit_grouping(value, num);
    };
    $scope.number_stand = function (value) {
        return number_stand(value);
    };
    $scope.round = function (value, num, get_default) {
        return round(value, num, get_default);
    };
    $scope.safeRound = function (val) {
        val = Number(val);
        if (val < 0.0005) {
            val = 0.001;
        } else if (val < 0.005) {
            val = 0.01;
        } else if (val <= 0.07) {
            val = round(val, 2);
        } else {
            val = round(val, 1);
        }
        return val;
    };

    $scope.divide = function (total, values, notsaferound) {
        var all = 0;
        var rs = {};
        angular.forEach(values, function (val, key) {
            rs[key] = 0;
            all += val;
        });
        if (total != all) {
            tong = 0;
            var max_val = {point: 1, value: 0};
            var min_val = {point: 1, value: 1000000};
            angular.forEach(values, function (sotre, point) {
                if (sotre == 0) {
                    rs[point] = 0;
                    return;
                }
                var tile = sotre / all;
                var val = $['*'](tile, total);
                if (!notsaferound) {
                    val = $scope.safeRound(val);
                } else {
                    val = round(val, 3);
                }
                tong = $['+'](tong, val);
                rs[point] = val;
                if (max_val.value < val) {
                    max_val = {point: point, value: val};
                }
                if (min_val.value > val) {
                    min_val = {point: point, value: val};
                }
            });
            if (total != tong) {
                if (total - tong > 0) {
                    rs[min_val.point] = $['+'](rs[min_val.point], $['-'](total, tong));
                } else {
                    rs[max_val.point] = $['+'](rs[max_val.point], $['-'](total, tong));
                }
            }
        }else{
            rs = values;
        }
        return rs;
    };
    /*
    * Chia tỉ lệ theo chiều dọc danh sách thực phẩm xuất kho nhiều giá
    * */
    $scope.divideH = function (vals, exps) {
        var tmp = clone(exps);
        var mau = {};
        var i = 1;
        angular.forEach(exps, function (food, food_id_price) {
            mau[i] = food.quantity;
            food.quantities = {};
            i++;
        });
        angular.forEach(vals, function (val, point) {
            var i = 1;
            var arr = $scope.divide(val, mau);
            angular.forEach(exps, function (food, food_id_price) {
                if (val == 0) {
                    food.quantities[point] = 0;
                } else {
                    food.quantities[point] = arr[i];
                    i++;
                }
            });
        });
        return exps;
    };
    $scope.isSchoolPointTogether = function() {
        return $CFG.school_points > 1 && $CFG.school_point_together == 1;
    };
    $scope.mathRound = function (value) {
        return Math.round(value);
    };
    $scope.Infinity = function (value, ext) {
        return $Infinity(value, ext);
    };
    $scope.count = function (arr) {
        return count(arr);
    };
    $scope.sum = function (arr) {
        var rs = 0;
        for (var i in arr) {
            rs += Number(arr[i]);
        }
        return rs;
    };
    $scope.num2array = function (num) {
        var rs = [];
        if (typeof num == 'string') {
            num = Number(num);
        }
        if (typeof num == 'number') {
            for (var i = 0; i < num; i++) {
                rs.push(i + 1);
            }
        } else if (angular.isArray(num)) {
            for (var i = num[0]; i < num[1]; i++) {
                rs.push(i);
            }
        }
        return rs;
    };
    $scope.convert_number_to_words = function (num) {
        return convert_number_to_words(num);
    };
    $scope.in_array = function (value, arr) {
        return in_array(value, arr);
    };
    $scope.objToArray = function (obj) {
        if (typeof obj === 'object') {
            return Object.values(obj);
        }
    };
    setTimeout(function () {
        var view_container = $('.angular-view-container');
        var screen = $('body');
        if (!view_container) {
            return;
        }
        $scope.$apply(function () {
            if (screen) {
                $scope.screen = {
                    width: screen.css('width').replace('px', ''),
                    height: screen.css('height').replace('px', '')
                }
            }
            if (view_container.length) {
                $scope.view = {
                    width: view_container.css('width').replace('px', ''),
                    height: view_container.css('height').replace('px', '')
                }
            }
        })
    }, 1000);
}])
    .config(['$compileProvider', function ($compileProvider) {
        $compileProvider.debugInfoEnabled(true);
    }])
    .filter('date2form', ['$sce', function ($sce) {
        var div = document.createElement('div');
        return function (date, type) {
            var d = '';
            if (date != undefined && date != '') {
                type || (type = '/');
                d = date.split(' ')[0].split('-');
                var dt = [];
                if (d[2]) dt.push(d[2]);
                if (d[1]) dt.push(d[1]);
                if (d[0]) dt.push(d[0]);
                d = dt.join(type);
            }
            return d;
        };
    }])
    .filter('decodeHTML', ['$sce', function ($sce) {
        var div = document.createElement('div');
        return function (text) {
            text || (text = '');
            div.innerHTML = $('<textarea />').html(text).text();
            return $sce.trustAsHtml($('<textarea />').html(text).text());
        };
    }])
    .filter('parseFloat', [function ($sce) {
        return function (value) {
            var val = parseFloat(value);
            return val;
        };
    }])
    .filter('round', ['$sce', function ($sce) {
        return function (value, num) {
            num || (num = 2);
            var v = (value + '').split('.');
            if (v.length == 2) {
                v[1] = v[1].substring(0, num);
            } else {
                v.push('0');
            }
            return parseFloat(v[0] + '.' + v[1]);
        };
    }])
    .factory('MyCache', function ($cacheFactory) {
        return $cacheFactory('myCache');
    })

    .directive('menuDropdownHover', function () {
        console.log(1111);
        return {
            link: function (scope, element) {
                $(element).hover(
                    function () {
                        $(this).addClass('open')
                    },
                    function () {
                        $(this).removeClass('open')
                    }
                )
            }
        };
    })
    .directive('iconHover', function () {
        return {
            link: function (scope, element) {
                $(element).hover(
                    function () {
                    }
                )

            }
        };
    })
    .directive('focusMe', function ($timeout) {
        return {
            scope: {trigger: '=focusMe'},
            link: function (scope, element) {
                scope.$watch('trigger', function (value) {
                    if (value === true) {
                        $timeout(function () {
                            element.focus();
                        });
                    }
                });
            }
        };
    })
    .directive('meFocus', function ($timeout) {
        return {
            scope: {meFocus: '&meFocus'},
            link: function (scope, element) {
                $(element).focus(function () {
                    /*console.log(element);*/
                    scope.meFocus();
                });
            }
        };
    })
    .directive('numberFormat', function ($timeout) {
        return {
            scope: {
                numberFormat: '@numberFormat',
                ngModel: '=ngModel'
            }, link: function (scope, element, attrs) {
                var type = scope.numberFormat;
                if (!type || type != 'float') {
                    type = 'int';
                } else {
                    type = 'float';
                }
                var char_sp = [0, 8];
                $(element).keypress(function (e) {
                    if (e.which >= 48 && e.which <= 57 || e.which == 46) {
                        /*console.log(type,e.which);*/
                        if (type == 'int' && e.which == 46) {
                            return false;
                        } else {
                            var val = $(this).val();
                            if (val.split('.').length > 1 && e.which == 46) {
                            } else {
                                return false;
                            }
                        }
                    } else {
                        if (!in_array(e.which, char_sp)) {
                            return false;
                        }
                    }
                    if (attrs.ngModel) {
                        scope.ngModel = Number($(this).val());
                    }
                })
            }
        };
    })
    .directive('typeNumber', function ($timeout) {
        return {
            scope: {
                typeNumber: '@typeNumber',
                ngModel: '=ngModel',
                numAbs: '=numAbs',
                min: '=min',
                max: '=max'
            }, link: function (scope, element, attrs) {
                var type = scope.typeNumber;
                var numAbs = scope.numAbs;
                if (!type || type != 'float') {
                    type = 'int';
                } else {
                    type = 'float';
                }
                if (!attrs.title) {
                    if (type == 'float') {
                        $(element).attr('title', 'Định dạng phải là số');
                    } else {
                        $(element).attr('title', 'Chỉ chấp nhận số nguyên');
                    }
                }
                $(element)
                .keypress(function (e) {
                    var key = e.which;
                    var rs = false;
                    /*
                    * 46: dấu chấm;
                    * 45: dấu trừ;
                    * */
                    if (key >= 48 && key <= 57 || key === 46 || key === 45) {
                        rs = true;
                        var val = $(this).val();
                        if (key === 46 && (type === 'int' || type === 'float' && $(this).val().split('.').length > 1)) {
                            rs = false;
                        }
                        if (key === 45 && (scope.numAbs || val.split('-').length > 1)) {
                            rs = false;
                        }
                    }
                    return rs;
                }).keyup(function (e) {
                    if(e.which !== 8) {
                        if (e.which === 190) {
                            var val = $(this).val();
                            if (val.split('.').length > 1 && val.split('.')[0] === '') {
                                $(this).val(0 + val);
                            }
                        }
                        if (e.which == 189 || e.which == 173) {
                            var val = $(this).val();
                            if (val.split('-').length > 1) {
                                val = val.replace('-', '');
                                $(this).val('-' + val);
                            }
                        }
                        if (typeof scope.min != "undefined") {
                            if (Number($(this).val()) < scope.min) {
                                $(this).val(scope.min);
                            }
                        }
                        if (typeof scope.max != "undefined") {
                            if (Number($(this).val()) > scope.max) {
                                $(this).val(scope.max);
                            }
                        }
                    }
                }).blur(function () {
                    if (!$(this).val()) {
                        $(this).val('');
                    }
                    scope.ngModel = $(this).val();
                }).bind("paste", function(e) {
                    var pastedData = e.originalEvent.clipboardData.getData('text');
                    var tmp = [];
                    var value_old = $(this).val();
                    var selectionStart = $(this)[0].selectionStart;
                    var selectionEnd = $(this)[0].selectionEnd;
                    tmp.push(value_old.toString().substr(0, selectionStart));
                    tmp.push(pastedData);
                    tmp.push(value_old.toString().substr(selectionEnd, value_old.length));
                    var value_new = tmp.join('');
                    var val = checkInputNumber(value_new);
                    console.log(tmp, tmp.join('::'), value_new, val);
                    if (val != value_new) {
                        alert('Giá trị thay đổi "' + value_new + '" không hợp lệ');
                        return false;
                    }
                });
                function checkInputNumber(val) {
                    var match_reg = '0-9';
                    if (!numAbs) {
                        match_reg += '|-';
                    }
                    if (type === 'float') {
                        match_reg += '|\.';
                    }
                    val = val.match(new RegExp('[' + match_reg + ']','g'));
                    if (!val) {
                        return val;
                    }
                    if (!numAbs) {
                        var dem = 0;
                        var tmp = [];
                        for (var i in val) {
                            if (val[i] == '-') {
                                dem++;
                                if (i == 0 && dem < 2) {
                                    tmp.push(val[i]);
                                }
                            } else {
                                tmp.push(val[i]);
                            }
                        }
                        val = tmp;
                    }
                    if (type === 'float') {
                        var dem = 0;
                        var tmp = [];
                        for (var i in val) {
                            if (val[i] == '.') {
                                dem++;
                                if (dem < 2) {
                                    tmp.push(val[i]);
                                }
                            } else {
                                tmp.push(val[i]);
                            }
                        }
                        val = tmp;
                    }
                    return val.join('');
                }
            }
        };
    })
    .directive('parseInt', function ($timeout) {
        return {
            scope: {
                parseInt: '=parseInt'
            }, link: function (scope, element, attrs) {
                $(element).keyup(function (e) {
                    var val = $(element).val();
                    if (val) {
                        val = val.replace(',', '.');
                        val = val.replace('.', '');
                        val || (val = 0);
                        val = parseInt(val);
                    } else {
                        val = 0;
                    }
                    $(element).val(val);
                    scope.trigger = val;
                })
            }
        };
    })
    .directive('parseFloat', function ($timeout) {
        return {
            scope: {
                parseInt: '=parseInt'
            }, link: function (scope, element, attrs) {
                $(element).keyup(function (e) {
                    var val = $(element).val();
                    if (val) {
                        val = val.replace(',', '.');
                        val = val.replace('..', '.');
                        val || (val = 0);
                        if (e.key != '.') {
                            val = parseFloat(val);
                        }
                    } else {
                        val = 0;
                    }
                    $(element).val(val);
                    scope.trigger = val;
                })
            }
        };
    })
    .directive('dateBox', function ($timeout) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=dateBox',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                dateBoxDisabled: '=dateBoxDisabled',
                onSelect: '&onSelect',
                onLoad: '&onLoad',
                build: '=build'
            },
            link: function (scope, element, attrs) {
                element.begin = true;
                var options = {
                    value: scope.trigger,
                    formatter: myformatter,
                    parser: dateparser,
                    onSelect: function (date) {
                        var val_new = dateboxOnSelect(date);
                        var val_old = element.oldValue;
                        onChange(val_new, val_old);
                    }, onChange: function (date) {
                    },
                    width: scope.boxWidth,
                    height: scope.boxHeight
                };
                scope.onLoad();
                var el = element.datebox(options);
                scope.$watch('trigger', function (value) {
                    el.datebox('setValue', scope.trigger);
                });
                el.next().children('input:first').bind('blur', function () {
                    var val_new = $(this).val();
                    var val_old = element.oldValue;
                    onChange(val_new, val_old);
                }).bind('focus', function () {
                    element.oldValue = $(this).val();
                }).click(function () {
                    element.oldValue = $(this).val();
                });

                function onChange(value_new, value_old) {
                    if (value_new != value_old) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.trigger = value_new;
                            });
                            scope.$apply(function () {
                                scope.onSelect();
                            });
                        });

                    }
                }
            }
        };
    })
    .directive('vtDraggable', function ($timeout) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=vtDraggable',
                dragContain: '@dragContain'
            },
            link: function (scope, $element, $attrs, $event) {
                if (!$($element).children('span.box-handle').length) {
                    $($element).append('<span class="box-handle" style="float: right; width: 30px; height: 30px;"></span>');
                }
                $($element).addClass('draggable-container');
                var options = {
                    handle: $($element).children('span.box-handle')
                };
                if (scope.dragContain) {
                    options.containment = scope.dragContain;
                } else {
                    options.containment = $('body');
                }
                $($element).draggable(options);
            }
        };
    })
    .directive('comboBox', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=comboBox',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                url: '@url',
                data: '=data',
                delay: '@delay',
                mode: '@mode',
                paramKey: '@paramKey',
                valueField: '@valueField',
                textField: '@textField',
                paramValue: '=paramValue',
                clearForNull: '=clearForNull',
                reloadOnSelected: '=reloadOnSelected',
                clearOnSelected: '=clearOnSelected',
                onSelect: '&onSelect',
                onLoadSuccess: '&onLoadSuccess'
            }, controller: function ($scope, $element, $attrs, $transclude) {

            },
            link: function (scope, element, attrs, ctrls) {
                var created = false;
                var is_selected = false;
                var queryParams = {};
                scope.valueField || (scope.valueField = 'id');
                scope.textField || (scope.textField = 'text');
                scope.delay || (scope.delay = 700);
                scope.mode || (scope.mode = 'remote');
                if (scope.paramKey) {
                    queryParams[scope.paramKey] = scope.paramValue;
                }
                var option = {
                    valueField: scope.valueField,
                    textField: scope.textField,
                    /*panelHeight:'auto',*/
                    mode: scope.mode,
                    delay: scope.delay,
                    onSelect: function (row, el, test) {
                        is_selected = true;
                    }, onLoadSuccess: function (data, el) {
                        is_selected = false;
                        $(element).combobox('panel').children().click(function (e) {
                            element.onSelected();
                        });
                        $(element).next().children('input').keyup(function (e) {
                            if (e.which == 13) {
                                element.onSelected();
                            }
                        });
                        scope.data = data;
                        scope.onLoadSuccess();
                        created = true;
                    },
                    queryParams: queryParams,
                    width: 170,
                    height: 30
                };
                element.onSelected = function () {
                    setTimeout(function () {
                        var id = $(element).combobox('getValue');
                        var item = {};
                        var data = $(element).combobox('getData');
                        angular.forEach(data, function (fd, ind) {
                            if (id + '' == fd[scope.valueField] + '') {
                                item = fd;
                            }
                        });
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.trigger = item;
                            });
                            scope.$apply(function () {
                                scope.onSelect();
                            });
                        });
                        if (scope.clearForNull) {
                            $(element).combobox('clear');
                        }
                    });
                };
                if (scope.trigger) {
                    option.value = scope.trigger[scope.valueField];
                }
                if (scope.boxWidth) {
                    option.width = scope.boxWidth;
                }
                scope.$watch('data', function (value) {
                    if (value) {
                        $(element).combobox('loadData', value);
                    } else {

                    }
                });
                scope.$watch('trigger', function (value) {
                    if (created) {
                        if (value != undefined) {
                            var data = scope.data;
                            var id = value[scope.valueField];
                            var index = -1;
                            angular.forEach(data, function (item, ind) {
                                if (value[scope.valueField] == item[scope.valueField]) {
                                    index = ind;
                                }
                            });
                        }
                    }
                });
                scope.$watch('paramValue', function (value) {
                    if (scope.mode != 'local') {
                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {
                            var queryParams = $(element).combobox('options').queryParams;
                            if (scope.paramKey) {
                                queryParams[scope.paramKey] = scope.paramValue;
                            }
                            $(element).combobox({queryParams: queryParams});
                        }
                    } else {
                        if (in_array(typeof scope.data, ['object'])) {
                            if (in_array(typeof value, ['object'])) {
                                var data_new = [];
                                angular.forEach(scope.data, function (item, index) {
                                    if (!in_array(item[scope.valueField], value)) {
                                        data_new.push(item);
                                    }
                                });
                                $(element).combobox('loadData', data_new).combobox('clear');
                            }
                        }
                    }
                });
                var url = scope.url;
                // if(scope.data != undefined){
                //     url = scope.data;
                // }
                $.dm_datagrid.combobox(element, url, option);
            }
        };
    })
    .directive('infConfigs', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                infConfigs: '=infConfigs',
                infId: '@infId',
                url: '@url',
                resp: '=resp',
                onSuccess: '&onSuccess',
                onError: '&onError',
                infDefault: '=infDefault',
                ngTrueValue: '=ngTrueValue',
                ngFalseValue: '=ngFalseValue',
                onChange: '&onChange',
                ngValue: '=ngValue'
            }, controller: function ($scope, $element, $attrs, $transclude) {
                $scope.value_old = $scope.infConfigs;
            },
            link: function (scope, element, attrs, ctrls) {
                var ngTrueValue = true;
                var ngFalseValue = false;
                if (typeof attrs.ngFalseValue != 'undefined') {
                    ngFalseValue = scope.ngFalseValue;
                }
                if (typeof attrs.ngTrueValue != 'undefined') {
                    ngTrueValue = scope.ngTrueValue;
                }
                if (scope.infConfigs === undefined) {
                    if (attrs.type === 'checkbox') {
                        scope.infConfigs = ngFalseValue;
                    } else {
                        scope.infConfigs = scope.infDefault;
                    }
                }
                if (!scope.url) {
                    scope.url = [$CFG.remote.base_url, 'information_configs', $CFG.project].join('/');
                }
                if (attrs.type === 'checkbox') {
                    if (scope.infConfigs === ngTrueValue) {
                        $(element).prop('checked', true);
                    }
                } else if (attrs.type === 'radio') {
                    if (scope.ngValue === scope.infConfigs) {
                        $(element).prop('checked', true);
                    } else {
                        $(element).prop('checked', false);
                    }
                } else if (attrs.type === 'number') {
                    console.log('set default', scope.infDefault)
                    $(element).val(scope.infDefault);
                } else {
                    $(element).focus(function () {
                        scope.value_old = scope.infConfigs;
                    });
                }
                if (attrs.type === 'checkbox') {
                    $(element).change(function () {
                        var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);
                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {
                            scope.$apply(function () {
                                scope.infConfigs = value;
                            });
                        }, function () {
                            if (typeof scope.onError == 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, scope.infAlert, false);
                    })
                } else if (attrs.type === 'radio') {
                    $(element).click(function () {
                        var value = scope.ngValue;
                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {
                            scope.$apply(function () {
                                scope.infConfigs = value;
                            })
                        }, function () {
                            if (typeof scope.onError == 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, scope.infAlert, false);
                    })
                } else {
                    $(element).blur(function () {
                        /* directive id is array */
                        var arr = scope.infId.split('.');
                        var id = angular.copy(scope.infId);
                        var value = angular.copy(scope.infConfigs);

                        if (arr.length >= 1) {
                            id = arr[0];
                            for (var i = arr.length; i > 0; i--) {
                                var tmp = {};
                                tmp[arr[i - 1]] = value;
                                value = tmp;
                            }
                            value = value[id];
                        }

                        if (scope.value_old !== scope.infConfigs) {
                            process(scope.url, {async: true, id: id, value: value}, function (resp) {
                                scope.$apply(function () {
                                    if (typeof scope.onSuccess == 'function') {
                                        scope.onSuccess();
                                    }
                                })
                            }, function () {
                                if (typeof scope.onError == 'function') {
                                    scope.$apply(function () {
                                        scope.onError();
                                    });
                                }
                            }, scope.infAlert, false);
                        }
                    }).keyup(function () {
                        console.log(111111111111)
                        scope.$apply(function () {
                            scope.infConfigs = $(element).val();
                        });
                    });
                }
                scope.$watch('infConfigs', function (value) {
                    if (attrs.type === 'checkbox') {
                        if (scope.ngTrueValue + '' === value + '') {
                            $(element).prop('checked', true);
                        } else {
                            $(element).prop('checked', false);
                        }
                        scope.onSuccess();
                    } else if (attrs.type === 'radio') {
                        scope.onSuccess();
                    } else {
                        $(element).val(value);
                    }
                    scope.onChange();
                })
            }
        };
    })
    .directive('countFbComment', function ($timeout) {
        return {
            scope: {trigger: '=countFbComment'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-comments-count" data-href="' + attrs.url + value + '"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('helperInclude', function ($timeout) {
        return {
            scope: {
                trigger: '=helperInclude',
                url: '@url',
                key: '@key'
            },
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    var data = {};
                    if (scope.key) {
                        data[scope.key] = scope.trigger;
                    }
                    loadForm(scope.url, '', data, function (resp) {
                        $(element).html(resp);
                    })
                });
            }
        };
    })
    .directive('addId', function ($timeout) {
        return {
            scope: {trigger: '=addId'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    $(element).attr('id', value);
                });
            }
        };
    })
    .directive('classHidden', function ($timeout) {
        return {
            scope: {trigger: '=classHidden'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('hidden');
                    } else {
                        $(element).removeClass('hidden');
                    }
                });
            }
        };
    })
    .directive('classActive', function ($timeout) {
        return {
            scope: {trigger: '=classActive'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('active');
                    } else {
                        $(element).removeClass('active');
                    }
                });
            }
        };
    })
    .directive('idActive', function ($timeout) {
        return {
            scope: {trigger: '=idActive'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('active');
                    } else {
                        $(element).removeClass('active');
                    }
                });
            }
        };
    })
    .directive('viewFbComments', function ($timeout) {
        return {
            scope: {trigger: '=viewFbComments'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-comments" data-href="' + attrs.url + value + '" width="100%" datanumposts="5"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('shareFb', function ($timeout) {
        return {
            scope: {trigger: '=shareFb'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-share-button" data-href="' + attrs.url + value + '" data-layout="button_count" data-mobile-iframe="true"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('myEnter', function () {
        return function (scope, element, attrs) {
            element.bind("keydown keypress", function (event) {
                if (event.which === 13) {
                    scope.$apply(function () {
                        scope.$eval(attrs.myEnter);
                    });
                    event.preventDefault();
                }
            });
        };
    })
    .directive('starHoverChangeColor', function ($timeout) {
        return {
            restrict: 'A',
            compile: function (element, attrs) {
                return function (scope, element, attrs) {
                    $timeout(function () {
                        if (attrs.starHoverChangeColor && taikhoan != '') {
                            var p = element.parent();
                            var no = parseInt(attrs.field);
                            element.hover(function () {
                                if (scope.monan.dabinhchon) {
                                    element.attr('title', 'Bạn đã bình chọn món ăn này');
                                    return;
                                }
                                p.children().each(function (index, el) {
                                    if (index < no) {
                                        $(el).addClass('star-hover');
                                    } else {
                                        $(el).removeClass('star-hover');
                                    }
                                })
                            }, function () {
                                p.children().removeClass('star-hover');
                            })
                                .click(function () {
                                    if (scope.monan.dabinhchon) {
                                        return;
                                    }
                                    var arr_data = {
                                        diem: attrs.starHoverChangeColor,
                                        mamn: attrs.maMn,

                                    };
                                    var data = JSON.stringify(arr_data);

                                    $.ajax({
                                        type: "POST",
                                        url: base_url + don_vi + '/_xuly/mon_ngon/xulyBC',
                                        timeout: 120000,
                                        data: {data: data},
                                        dataType: "json",
                                        success: function (resp) {
                                            if (resp.result == "success") {
                                                alert("Bạn đã bình chọn thành công. Cảm ơn!");
                                                scope.monan.dabinhchon = true;
                                                scope.$apply(scope.$eval(attrs.mnUpdate));
                                            } else if (resp.result == "fail") {
                                                alert(resp.err);
                                            } else {
                                                var errors = "";
                                                $.each(resp.errors, function (i, v) {
                                                    errors += "- " + v + "\n";
                                                });
                                                alert('Lỗi!', errors);
                                            }
                                        }
                                    });
                                })
                        } else {
                            element.attr('title', 'Đăng nhập để bình chọn');
                        }
                    })
                }
            }
        };
    })
    .directive('fileUpload', function () {
        return {
            scope: {
                fileUpload: '=fileUpload',
                url: '@url',
                urlBind: '=urlBind',
                format: '@format',
                size: '@size',
                uploadDone: '&uploadDone'
            },
            link: function (scope, element, attrs) {
                var url = scope.url;
                url || (url = scope.urlBind);
                if (url) {
                    scope.size || (scope.size = 2);
                    scope.files || (scope.files = 'files');
                    scope.format || (scope.format = 'zip|rar|7z|doc|docx|pdf|xls|xlsx|jpg|png|gif');
                    element.attr('name', scope.files + '[]').attr('type', 'file');
                    init_uploader(element, url, scope.format, scope.size, function (resp) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.fileUpload = resp.data;
                            })
                        })
                    });
                }
                scope.$watch('fileUpload', function (value) {
                    if (typeof value !== 'undefined') {
                        scope.uploadDone();
                    }
                });
            }
        }
    }).directive('dynamic', function () {
    return {
        restrict: 'A',
        scope: {
            dynamic: '=dynamic',
            dynamicId: '@dynamicId',
            url: '@url',
            resp: '=resp',
            onSuccess: '&onSuccess',
            onError: '&onError',
            ngTrueValue: '=ngTrueValue',
            ngFalseValue: '=ngFalseValue',
            ngValue: '=ngValue'
        }, controller: function ($scope) {
            $scope.value_old = $scope.dynamic;
        },
        link: function (scope, element, attrs) {
            var ngTrueValue = true;
            var ngFalseValue = false;
            if (attrs.ngFalseValue !== undefined) {
                ngFalseValue = scope.ngFalseValue;
            }
            if (attrs.ngTrueValue !== undefined) {
                ngTrueValue = scope.ngTrueValue;
            }
            if (scope.dynamic === undefined) {
                if (attrs.type === 'checkbox') {
                    scope.dynamic = ngFalseValue;
                } else if (attrs.type === 'radio') {

                } else {
                    scope.dynamic = '';
                }
            }
            if (!scope.url) {
                scope.url = [$CFG.remote.base_url, 'dynamic-variable/update'].join('/');
            }
            if (attrs.type === 'checkbox') {
                if (scope.dynamic === ngTrueValue) {
                    $(element).prop('checked', true);
                }
            } else if (attrs.type === 'radio') {
                if (scope.ngValue === scope.dynamic) {
                    $(element).prop('checked', true);
                } else {
                    $(element).prop('checked', false);
                }
            } else {
                $(element).focus(function () {
                    scope.value_old = scope.dynamic;
                })
            }
            if (attrs.type === 'checkbox') {
                $(element).change(function () {
                    var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);
                    process(scope.url, {
                        async: true,
                        id: scope.dynamicId,
                        value: value,
                        pathname: window.location.pathname
                    }, function () {
                        scope.$apply(function () {
                            scope.dynamic = value;
                        })
                    }, function () {
                        if (typeof scope.onError === 'function') {
                            scope.$apply(function () {
                                scope.onError();
                            })
                        }
                    }, false, false);
                })
            } else if (attrs.type === 'radio') {
                $(element).click(function () {
                    var value = scope.ngValue;
                    process(scope.url, {
                        async: true,
                        id: scope.dynamicId,
                        value: value,
                        pathname: window.location.pathname
                    }, function () {
                        scope.$apply(function () {
                            scope.dynamic = value;
                        })
                    }, function () {
                        if (typeof scope.onError === 'function') {
                            scope.$apply(function () {
                                scope.onError();
                            })
                        }
                    }, false, false);
                })
            } else {
                $(element).blur(function () {
                    /* directive id is array */
                    var arr = scope.dynamicId.split('.');
                    var id = angular.copy(scope.dynamicId);
                    var value = angular.copy(scope.dynamic);

                    if (arr.length >= 1) {
                        id = arr[0];
                        for (var i = arr.length; i > 0; i--) {
                            var tmp = {};
                            tmp[arr[i - 1]] = value;
                            value = tmp;
                        }
                        value = value[id];
                    }

                    if (scope.value_old !== scope.dynamic) {
                        process(scope.url, {
                            async: true,
                            id: id,
                            value: value,
                            pathname: window.location.pathname
                        }, function () {
                            scope.$apply(function () {
                                if (typeof scope.onSuccess === 'function') {
                                    scope.onSuccess();
                                }
                            })
                        }, function () {
                            if (typeof scope.onError === 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, false, false);
                    }
                }).keyup(function () {
                    scope.$apply(function () {
                        scope.dynamic = $(element).val();
                    })
                })
            }
            /*Set lại thuộc tính*/
            scope.$watch('dynamic', function (value) {
                if (attrs.type === 'checkbox') {
                    if (scope.ngTrueValue + '' === value + '') {
                        $(element).prop('checked', true);
                    } else {
                        $(element).prop('checked', false);
                    }
                    scope.onSuccess();
                } else if (attrs.type === 'radio') {
                    scope.onSuccess();
                } else {
                    $(element).val(value);
                }
            })
        }
    };
})
    .directive('comboBoxFood', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=comboBoxFood',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                url: '@url',
                data: '=data',
                delay: '@delay',
                mode: '@mode',
                paramKey: '@paramKey',
                valueField: '@valueField',
                textField: '@textField',
                paramValue: '=paramValue',
                clearForNull: '=clearForNull',
                reloadOnSelected: '=reloadOnSelected',
                clearOnSelected: '=clearOnSelected',
                onSelect: '&onSelect',
                onLoadSuccess: '&onLoadSuccess'
            }, controller: function ($scope, $element, $attrs, $transclude) {

            },
            link: function (scope, element, attrs, ctrls) {
                var created = false;
                var is_selected = false;
                var queryParams = {};
                scope.valueField || (scope.valueField = 'id');
                scope.textField || (scope.textField = 'text');
                scope.delay || (scope.delay = 700);
                scope.mode || (scope.mode = 'remote');
                if (scope.paramKey) {
                    queryParams[scope.paramKey] = scope.paramValue;
                }
                var option = {
                    valueField: scope.valueField,
                    textField: scope.textField,
                    /*panelHeight:'auto',*/
                    mode: scope.mode,
                    delay: scope.delay,
                    onSelect: function (row, el, test) {
                        is_selected = true;
                    }, onLoadSuccess: function (data, el) {
                        is_selected = false;
                        $(element).combobox('panel').children().click(function (e) {
                            element.onSelected();
                        })
                        $(element).next().children('input').keyup(function (e) {
                            if (e.which == 13) {
                                var id = $(element).combobox('getValue');
                                element.onSelected(id);
                            }
                        });
                        var kt = false;
                        for (var i in data) {
                            var kesearch = removeUnicode($(element).next().find('input').val());
                            if (scope.trigger != undefined && typeof scope.trigger == 'object') {
                                if (data[i][scope.valueField] == scope.trigger[scope.valueField] || removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {
                                    kt = true;
                                    break;
                                }
                            } else if (typeof data[i][scope.textField] == 'string') {
                                if (removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {
                                    kt = true;
                                    break;
                                }
                            }
                        }
                        if (!kt && scope.clearForNull) {
                            $(element).combobox('clear');
                        }
                        scope.data = data;
                        scope.onLoadSuccess();
                        created = true;
                    },
                    queryParams: queryParams,
                    width: 170,
                    height: 30,
                    formatter: function (row) {
                        var html = `<span class="glyphicon glyphicon` + (row.is_static == 1 ? '-ok color-green' : '-grain') + `"></span> `;
                        return ' <div title="' + (row.is_static == 1 ? 'Theo viện dinh dưỡng' : 'Nguồn khác') + '">' + html + ' <span class="item-text">' + row.name + '</span></div>';
                    }
                };
                element.onSelected = function onSelected(id) {
                    setTimeout(function () {
                        id || (id = $(element).combobox('getValue'));
                        var item = scope.data[0];
                        if (scope.trigger) {
                            item = scope.trigger;
                        }
                        angular.forEach(scope.data, function (fd, ind) {
                            if (id + '' == fd[scope.valueField] + '') {
                                item = fd;
                            }
                        })
                        scope.$apply(function () {
                            scope.trigger = item;
                        })
                    }, 400)
                }
                if (scope.trigger) {
                    option.value = scope.trigger[scope.valueField];
                }
                if (scope.boxWidth) {
                    option.width = scope.boxWidth;
                }
                scope.$watch('data', function (value) {
                    value || (value = []);
                    $(element).combobox('loadData', value);
                });
                scope.$watch('trigger', function (value) {
                    if (value) {
                        var data = scope.data;
                        var id = value[scope.valueField];
                        var index = -1;
                        angular.forEach(data, function (item, ind) {
                            if (value[scope.valueField] == item[scope.valueField]) {
                                index = ind;
                            }
                        });
                        if (index >= 0) {
                            $(element).combobox('setValue', value[scope.textField]);
                        }
                        scope.onSelect();
                    }
                    if (scope.clearOnSelected) {
                        $(element).combobox('clear');
                    }
                });
                scope.$watch('paramValue', function (value) {
                    if (scope.mode != 'local') {
                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {
                            var queryParams = $(element).combobox('options').queryParams;
                            if (scope.paramKey) {
                                queryParams[scope.paramKey] = scope.paramValue;
                            }
                            $(element).combobox({queryParams: queryParams});
                        }
                    } else {
                        if (in_array(typeof scope.data, ['object'])) {
                            if (in_array(typeof value, ['object'])) {
                                var data_new = [];
                                angular.forEach(scope.data, function (item, index) {
                                    if (!in_array(item[scope.valueField], value)) {
                                        data_new.push(item);
                                    }
                                });
                                $(element).combobox('loadData', data_new).combobox('clear');
                            }
                        }
                    }
                });
                $.dm_datagrid.combobox(element, scope.url, option);
            }
        };
    })