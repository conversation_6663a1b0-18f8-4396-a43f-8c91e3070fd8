{"data": [{"id": 245, "subject": "<PERSON><PERSON> sung Sổ theo dõi nhóm lớp và Sổ tài sản ", "summary": "Th<PERSON>y/cô vui lòng vào mục <PERSON>ân hệ > B<PERSON>o cáo thống kê> \r\n1. Sổ theo dõi nhóm lớp : T<PERSON><PERSON> khoản nhà trường sẽ cập nhật mục Kế hoạch năm còn tài khoản giáo viên sẽ nhập mục Công việc trọng tâm tháng\r\n2. Sổ tài sản : Tà<PERSON> khoản khối trưởng sẽ cập nhật dữ liệu theo kỳ I và kỳ II theo từng độ tuổi. Tài khoản nhà trường sẽ xem dữ liệu và xuất báo cáo tất cả các độ tuổi.", "summary_en": "Thay/co vui long vao muc <PERSON>an he > <PERSON><PERSON> cao thong ke> \r\n1. So theo doi nhom lop : Tai khoan nha truong se cap nhat muc Ke hoach nam con tai khoan giao vien se nhap muc Cong viec trong tam thang\r\n2. So tai san : Tai khoan khoi truong se cap nhat du lieu theo ky I va ky II theo tung do tuoi. Tai khoan nha truong se xem du lieu va xuat bao cao tat ca cac do tuoi.", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/1_20241217080452.jpg", "attachments": [{"id": "{\"id\":245,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed5 sung S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp v\\u00e0 S\\u1ed5 t\\u00e0i s\\u1ea3n \",\"summary\":null,\"content\":\"Th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng v\\u00e0o m\\u1ee5c Ph\\u00e2n h\\u1ec7 > B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea> \\r\\n1. S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp : T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd c\\u1eadp nh\\u1eadt m\\u1ee5c K\\u1ebf ho\\u1ea1ch n\\u0103m c\\u00f2n t\\u00e0i kho\\u1ea3n gi\\u00e1o vi\\u00ean s\\u1ebd nh\\u1eadp m\\u1ee5c C\\u00f4ng vi\\u1ec7c tr\\u1ecdng t\\u00e2m th\\u00e1ng\\r\\n2. S\\u1ed5 t\\u00e0i s\\u1ea3n : T\\u00e0i kho\\u1ea3n kh\\u1ed1i tr\\u01b0\\u1edfng s\\u1ebd c\\u1eadp nh\\u1eadt d\\u1eef li\\u1ec7u theo k\\u1ef3 I v\\u00e0 k\\u1ef3 II theo t\\u1eebng \\u0111\\u1ed9 tu\\u1ed5i. T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd xem d\\u1eef li\\u1ec7u v\\u00e0 xu\\u1ea5t b\\u00e1o c\\u00e1o t\\u1ea5t c\\u1ea3 c\\u00e1c \\u0111\\u1ed9 tu\\u1ed5i.\",\"attachments\":\"[\\\"1_20241217080452.jpg\\\",\\\"2_20241217080453.jpg\\\",\\\"3_20241217080453.jpg\\\",\\\"4_20241217080453.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-11-27\",\"deactive_at\":null,\"province\":\"56,97\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-11-27 14:19:45\",\"updated_at\":\"2024-12-17 08:04:54\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/1_20241217080452.jpg"}, {"id": "{\"id\":245,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed5 sung S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp v\\u00e0 S\\u1ed5 t\\u00e0i s\\u1ea3n \",\"summary\":null,\"content\":\"Th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng v\\u00e0o m\\u1ee5c Ph\\u00e2n h\\u1ec7 > B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea> \\r\\n1. S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp : T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd c\\u1eadp nh\\u1eadt m\\u1ee5c K\\u1ebf ho\\u1ea1ch n\\u0103m c\\u00f2n t\\u00e0i kho\\u1ea3n gi\\u00e1o vi\\u00ean s\\u1ebd nh\\u1eadp m\\u1ee5c C\\u00f4ng vi\\u1ec7c tr\\u1ecdng t\\u00e2m th\\u00e1ng\\r\\n2. S\\u1ed5 t\\u00e0i s\\u1ea3n : T\\u00e0i kho\\u1ea3n kh\\u1ed1i tr\\u01b0\\u1edfng s\\u1ebd c\\u1eadp nh\\u1eadt d\\u1eef li\\u1ec7u theo k\\u1ef3 I v\\u00e0 k\\u1ef3 II theo t\\u1eebng \\u0111\\u1ed9 tu\\u1ed5i. T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd xem d\\u1eef li\\u1ec7u v\\u00e0 xu\\u1ea5t b\\u00e1o c\\u00e1o t\\u1ea5t c\\u1ea3 c\\u00e1c \\u0111\\u1ed9 tu\\u1ed5i.\",\"attachments\":\"[\\\"1_20241217080452.jpg\\\",\\\"2_20241217080453.jpg\\\",\\\"3_20241217080453.jpg\\\",\\\"4_20241217080453.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-11-27\",\"deactive_at\":null,\"province\":\"56,97\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-11-27 14:19:45\",\"updated_at\":\"2024-12-17 08:04:54\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/2_20241217080453.jpg"}, {"id": "{\"id\":245,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed5 sung S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp v\\u00e0 S\\u1ed5 t\\u00e0i s\\u1ea3n \",\"summary\":null,\"content\":\"Th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng v\\u00e0o m\\u1ee5c Ph\\u00e2n h\\u1ec7 > B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea> \\r\\n1. S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp : T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd c\\u1eadp nh\\u1eadt m\\u1ee5c K\\u1ebf ho\\u1ea1ch n\\u0103m c\\u00f2n t\\u00e0i kho\\u1ea3n gi\\u00e1o vi\\u00ean s\\u1ebd nh\\u1eadp m\\u1ee5c C\\u00f4ng vi\\u1ec7c tr\\u1ecdng t\\u00e2m th\\u00e1ng\\r\\n2. S\\u1ed5 t\\u00e0i s\\u1ea3n : T\\u00e0i kho\\u1ea3n kh\\u1ed1i tr\\u01b0\\u1edfng s\\u1ebd c\\u1eadp nh\\u1eadt d\\u1eef li\\u1ec7u theo k\\u1ef3 I v\\u00e0 k\\u1ef3 II theo t\\u1eebng \\u0111\\u1ed9 tu\\u1ed5i. T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd xem d\\u1eef li\\u1ec7u v\\u00e0 xu\\u1ea5t b\\u00e1o c\\u00e1o t\\u1ea5t c\\u1ea3 c\\u00e1c \\u0111\\u1ed9 tu\\u1ed5i.\",\"attachments\":\"[\\\"1_20241217080452.jpg\\\",\\\"2_20241217080453.jpg\\\",\\\"3_20241217080453.jpg\\\",\\\"4_20241217080453.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-11-27\",\"deactive_at\":null,\"province\":\"56,97\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-11-27 14:19:45\",\"updated_at\":\"2024-12-17 08:04:54\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/3_20241217080453.jpg"}, {"id": "{\"id\":245,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed5 sung S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp v\\u00e0 S\\u1ed5 t\\u00e0i s\\u1ea3n \",\"summary\":null,\"content\":\"Th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng v\\u00e0o m\\u1ee5c Ph\\u00e2n h\\u1ec7 > B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea> \\r\\n1. S\\u1ed5 theo d\\u00f5i nh\\u00f3m l\\u1edbp : T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd c\\u1eadp nh\\u1eadt m\\u1ee5c K\\u1ebf ho\\u1ea1ch n\\u0103m c\\u00f2n t\\u00e0i kho\\u1ea3n gi\\u00e1o vi\\u00ean s\\u1ebd nh\\u1eadp m\\u1ee5c C\\u00f4ng vi\\u1ec7c tr\\u1ecdng t\\u00e2m th\\u00e1ng\\r\\n2. S\\u1ed5 t\\u00e0i s\\u1ea3n : T\\u00e0i kho\\u1ea3n kh\\u1ed1i tr\\u01b0\\u1edfng s\\u1ebd c\\u1eadp nh\\u1eadt d\\u1eef li\\u1ec7u theo k\\u1ef3 I v\\u00e0 k\\u1ef3 II theo t\\u1eebng \\u0111\\u1ed9 tu\\u1ed5i. T\\u00e0i kho\\u1ea3n nh\\u00e0 tr\\u01b0\\u1eddng s\\u1ebd xem d\\u1eef li\\u1ec7u v\\u00e0 xu\\u1ea5t b\\u00e1o c\\u00e1o t\\u1ea5t c\\u1ea3 c\\u00e1c \\u0111\\u1ed9 tu\\u1ed5i.\",\"attachments\":\"[\\\"1_20241217080452.jpg\\\",\\\"2_20241217080453.jpg\\\",\\\"3_20241217080453.jpg\\\",\\\"4_20241217080453.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-11-27\",\"deactive_at\":null,\"province\":\"56,97\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-11-27 14:19:45\",\"updated_at\":\"2024-12-17 08:04:54\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_3", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/4_20241217080453.jpg"}], "is_read": 0, "active_at": 1732640400, "created": 1732691985, "lastmodified": 1734397494}, {"id": 242, "subject": "HƯỚNG DẪN KẾT NỐI & ĐỒNG BỘ LÊN CSDL NGÀNH", "summary": "Hướng dẫn kết nối & đồng bộ lên CSDL ngành! Vui lòng xem thêm chi tiết trong link dưới đây:\r\n<a target=\"_blank\" href=\"https://docs.google.com/document/d/1uF9yh3g7ZNFuDitbvytFKE5DDVvYy3uHdXhbiOi9s9w/edit\"> KẾT NỐI CSDL NGÀNH </a>", "summary_en": "<PERSON>ong dan ket noi & dong bo len CSDL nganh! Vui long xem them chi tiet trong link duoi day:\r\n<a target=\"_blank\" href=\"https://docs.google.com/document/d/1uF9yh3g7ZNFuDitbvytFKE5DDVvYy3uHdXhbiOi9s9w/edit\"> KET NOI CSDL NGANH </a>", "content": null, "attachment": "", "attachments": [], "is_read": 0, "active_at": **********, "created": **********, "lastmodified": **********}, {"id": 241, "subject": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hình thức thanh toán qua ngân hàng LPBank", "summary": "<PERSON><PERSON><PERSON> nhật thanh toán học phí của học sinh qua ngân hàng LPBank \r\nLink hướng dẫn:\r\n<a target=\"_blank\" href=\"https://docs.google.com/document/d/1uHZDR0ZPg0gfYwgXWW47NH4PkU8IxjKi\">Click tại đây</a>", "summary_en": "Cap nhat thanh toan hoc phi cua hoc sinh qua ngan hang LPBank \r\nLink huong dan:\r\n<a target=\"_blank\" href=\"https://docs.google.com/document/d/1uHZDR0ZPg0gfYwgXWW47NH4PkU8IxjKi\">Click tai day</a>", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/7_20240926161430.jpg", "attachments": [{"id": "{\"id\":241,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt h\\u00ecnh th\\u1ee9c thanh to\\u00e1n qua ng\\u00e2n h\\u00e0ng LPBank\",\"summary\":null,\"content\":\"C\\u1eadp nh\\u1eadt thanh to\\u00e1n h\\u1ecdc ph\\u00ed c\\u1ee7a h\\u1ecdc sinh qua ng\\u00e2n h\\u00e0ng LPBank \\r\\nLink h\\u01b0\\u1edbng d\\u1eabn:\\r\\n<a target=\\\"_blank\\\" href=\\\"https:\\/\\/docs.google.com\\/document\\/d\\/1uHZDR0ZPg0gfYwgXWW47NH4PkU8IxjKi\\\">Click t\\u1ea1i \\u0111\\u00e2y<\\/a>\",\"attachments\":\"[\\\"7_20240926161430.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u1eadp nh\\u1eadt h\\u00ecnh th\\u1ee9c thanh to\\u00e1n qua ng\\u00e2n h\\u00e0ng LPBank\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-09-26\",\"deactive_at\":null,\"province\":\"01,97\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-09-26 15:52:40\",\"updated_at\":\"2024-09-26 16:14:31\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/7_20240926161430.jpg"}], "is_read": 0, "active_at": **********, "created": **********, "lastmodified": **********}, {"id": 238, "subject": "Tiện ích tăng khoảng cách giữa nhãn chữ ký & tên người ký khi in trực tiếp 1 số báo cáo", "summary": "Tiện ích \"Tăng khoảng cách giữa nhãn chữ ký & tên người ký khi in\" trực tiếp 1 số báo cáo. <PERSON><PERSON><PERSON> báo cáo có đặt tiện ích:\r\n1. <PERSON><PERSON><PERSON> qu<PERSON> khẩu phần dinh dưỡng (KQKPDD), <PERSON><PERSON> tính tiền ăn (STTA)\r\n2. <PERSON><PERSON><PERSON> kê chợ (PKC), Phiếu kê hàng chợ (PKHC)\r\n3. <PERSON><PERSON>u tiếp nhận & KTCLTP (PTN và KTCLTP), M1. KT trước khi CBTP tươi, M1. KT trước khi CBTP khô                                           \r\n4. M2. Kiểm tra trước khi CBTA, M3. Kiểm tra trước khi ăn - Lưu và hủy mẫu thức ăn, M3. <PERSON>ể<PERSON> tra trước khi ăn, M5. <PERSON> dõi lưu và hủy mẫu thức ăn                         \r\n5. Thực đơn tuần\r\n=> Hướng dẫn: V<PERSON><PERSON> trong bánh xe của các bá<PERSON> cáo, g<PERSON> lại giá trị mặc định tăng lên đến khi thấy phù hợp để in & đóng dấu! Xem ví dụ với mẫu M1 tươi (ảnh)", "summary_en": "Tien ich \"Tang khoang cach giua nhan chu ky & ten nguoi ky khi in\" truc tiep 1 so bao cao. Cac bao cao co dat tien ich:\r\n1. <PERSON><PERSON> qua khau phan dinh duong (KQKPDD), <PERSON> <PERSON>h tien an (STTA)\r\n2. <PERSON><PERSON> ke cho (PKC), <PERSON><PERSON> ke hang cho (PKHC)\r\n3. <PERSON>eu tiep nhan & KTCLTP (PTN va KTCLTP), M1. KT truoc khi CBTP tuoi, M1. KT truoc khi CBTP kho                                           \r\n4. M2. <PERSON>em tra truoc khi CBTA, M3. Kiem tra truoc khi an - Luu va huy mau thuc an, M3. <PERSON><PERSON> tra truoc khi an, M5. <PERSON> doi luu va huy mau thuc an                         \r\n5. Thuc don tuan\r\n=> Huong dan: Vao trong banh xe cua cac bao cao, go lai gia tri mac dinh tang len den khi thay phu hop de in & dong dau! Xem vi du voi mau M1 tuoi (anh)", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/bctk_print_opt_20240705154759.png", "attachments": [{"id": "{\"id\":238,\"unit_id\":2539,\"course_id\":null,\"title\":\"Ti\\u1ec7n \\u00edch t\\u0103ng kho\\u1ea3ng c\\u00e1ch gi\\u1eefa nh\\u00e3n ch\\u1eef k\\u00fd & t\\u00ean ng\\u01b0\\u1eddi k\\u00fd khi in tr\\u1ef1c ti\\u1ebfp 1 s\\u1ed1 b\\u00e1o c\\u00e1o\",\"summary\":null,\"content\":\"Ti\\u1ec7n \\u00edch \\\"T\\u0103ng kho\\u1ea3ng c\\u00e1ch gi\\u1eefa nh\\u00e3n ch\\u1eef k\\u00fd & t\\u00ean ng\\u01b0\\u1eddi k\\u00fd khi in\\\" tr\\u1ef1c ti\\u1ebfp 1 s\\u1ed1 b\\u00e1o c\\u00e1o. C\\u00e1c b\\u00e1o c\\u00e1o c\\u00f3 \\u0111\\u1eb7t ti\\u1ec7n \\u00edch:\\r\\n1. K\\u1ebft qu\\u1ea3 kh\\u1ea9u ph\\u1ea7n dinh d\\u01b0\\u1ee1ng (KQKPDD), S\\u1ed1 t\\u00ednh ti\\u1ec1n \\u0103n (STTA)\\r\\n2. Phi\\u1ebfu k\\u00ea ch\\u1ee3 (PKC), Phi\\u1ebfu k\\u00ea h\\u00e0ng ch\\u1ee3 (PKHC)\\r\\n3. Phi\\u1ebfu ti\\u1ebfp nh\\u1eadn & KTCLTP (PTN v\\u00e0 KTCLTP), M1. KT tr\\u01b0\\u1edbc khi CBTP t\\u01b0\\u01a1i, M1. KT tr\\u01b0\\u1edbc khi CBTP kh\\u00f4                                           \\r\\n4. M2. Ki\\u1ec3m tra tr\\u01b0\\u1edbc khi CBTA, M3. Ki\\u1ec3m tra tr\\u01b0\\u1edbc khi \\u0103n - L\\u01b0u v\\u00e0 h\\u1ee7y m\\u1eabu th\\u1ee9c \\u0103n, M3. Ki\\u1ec3m tra tr\\u01b0\\u1edbc khi \\u0103n, M5. Theo d\\u00f5i l\\u01b0u v\\u00e0 h\\u1ee7y m\\u1eabu th\\u1ee9c \\u0103n                         \\r\\n5. Th\\u1ef1c \\u0111\\u01a1n tu\\u1ea7n\\r\\n=> H\\u01b0\\u1edbng d\\u1eabn: V\\u00e0o trong b\\u00e1nh xe c\\u1ee7a c\\u00e1c b\\u00e1o c\\u00e1o, g\\u00f5 l\\u1ea1i gi\\u00e1 tr\\u1ecb m\\u1eb7c \\u0111\\u1ecbnh t\\u0103ng l\\u00ean \\u0111\\u1ebfn khi th\\u1ea5y ph\\u00f9 h\\u1ee3p \\u0111\\u1ec3 in & \\u0111\\u00f3ng d\\u1ea5u! Xem v\\u00ed d\\u1ee5 v\\u1edbi m\\u1eabu M1 t\\u01b0\\u01a1i (\\u1ea3nh)\",\"attachments\":\"[\\\"bctk_print_opt_20240705154759.png\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Ti\\u1ec7n \\u00edch in b\\u00e1o c\\u00e1o\",\"status\":1,\"is_read\":0,\"active_at\":\"2024-07-05\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2024-07-05 15:42:44\",\"updated_at\":\"2024-12-17 08:12:27\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/bctk_print_opt_20240705154759.png"}], "is_read": 0, "active_at": 1720112400, "created": 1720168964, "lastmodified": 1734397947}, {"id": 206, "subject": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON>  menu theo phân hệ trong PMS", "summary": "<PERSON><PERSON><PERSON> mềm PM<PERSON> đã cập nhật lại danh sách các menu theo 2 nhóm phân hệ:\r\n1. <PERSON><PERSON> hệ Dinh Dưỡng: <PERSON><PERSON><PERSON> cung cấp, <PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> tr<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ho, <PERSON><PERSON><PERSON>ho, <PERSON><PERSON><PERSON> sử kho, <PERSON>ổ kho, <PERSON><PERSON><PERSON><PERSON> đơn mẫu, <PERSON><PERSON> đối khẩu phần, <PERSON><PERSON><PERSON><PERSON> mẫu thống kê\r\n2. <PERSON><PERSON> hệ Thu chi: <PERSON><PERSON><PERSON><PERSON> lý khố<PERSON>, <PERSON><PERSON><PERSON><PERSON> lý lớ<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> họ<PERSON> sinh, <PERSON><PERSON><PERSON> hình ngày nghỉ, <PERSON><PERSON><PERSON><PERSON><PERSON> họ<PERSON> sin<PERSON>, <PERSON>, <PERSON><PERSON> m<PERSON><PERSON>ho<PERSON> thu, <PERSON><PERSON><PERSON><PERSON> lậ<PERSON> kho<PERSON> thu, <PERSON><PERSON> toán, <PERSON><PERSON><PERSON> cá<PERSON> thống kê\r\n=> <PERSON><PERSON><PERSON> chứ<PERSON> năng bên trong sẽ không bị ảnh hưởng bởi việc thay đổi các menu này! Việc thay đổi sẽ giúp các trường tiện hơn trong việc thao tác & chuyển đổi giữa các menu!\r\n=> N<PERSON><PERSON> chưa thấy thay đổi menu theo phân quyền, vui lòng thoát ra & đăng nhập lại nhé!", "summary_en": "<PERSON><PERSON> mem PMS da cap nhat lai danh sach cac menu theo 2 nhom phan he:\r\n1. <PERSON><PERSON> he <PERSON>h <PERSON>: <PERSON>ha cung cap, <PERSON><PERSON><PERSON> <PERSON>ham truong, <PERSON>, <PERSON><PERSON><PERSON>ho, <PERSON><PERSON>ho, <PERSON><PERSON> su kho, <PERSON> doi so kho, <PERSON><PERSON><PERSON> don mau, <PERSON> doi khau phan, <PERSON><PERSON> mau thong ke\r\n2. <PERSON><PERSON> he Thu chi: <PERSON><PERSON>y <PERSON>ho<PERSON>, <PERSON><PERSON>y lo<PERSON>, <PERSON><PERSON> sach hoc sinh, <PERSON><PERSON> hinh ngay nghi, <PERSON><PERSON>nh hoc sinh, <PERSON> doi hoc sinh, <PERSON><PERSON> muc khoan thu, <PERSON><PERSON><PERSON> lap khoan thu, <PERSON><PERSON> <PERSON><PERSON> toan, <PERSON><PERSON> cao thong ke\r\n=> <PERSON>ac chuc nang ben trong se khong bi anh huong boi viec thay doi cac menu nay! Viec thay doi se giup cac truong tien hon trong viec thao tac & chuyen doi giua cac menu!\r\n=> Neu chua thay thay doi menu theo phan quyen, vui long thoat ra & dang nhap lai nhe!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/menu01_20230605133933.jpg", "attachments": [{"id": "{\"id\":206,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m PMS \\u0111\\u00e3 c\\u1eadp nh\\u1eadt l\\u1ea1i danh s\\u00e1ch c\\u00e1c menu theo 2 nh\\u00f3m ph\\u00e2n h\\u1ec7:\\r\\n1. Ph\\u00e2n h\\u1ec7 Dinh D\\u01b0\\u1ee1ng: Nh\\u00e0 cung c\\u1ea5p, Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng, M\\u00f3n \\u0103n, Nh\\u1eadp kho, T\\u1ed3n kho, L\\u1ecbch s\\u1eed kho, Theo d\\u00f5i s\\u1ed5 kho, Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n, Bi\\u1ec3u m\\u1eabu th\\u1ed1ng k\\u00ea\\r\\n2. Ph\\u00e2n h\\u1ec7 Thu chi: Qu\\u1ea3n l\\u00fd kh\\u1ed1i, Qu\\u1ea3n l\\u00fd l\\u1edbp, Danh s\\u00e1ch h\\u1ecdc sinh, C\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9, \\u0110i\\u1ec3m danh h\\u1ecdc sinh, Theo d\\u00f5i h\\u1ecdc sinh, Danh m\\u1ee5c kho\\u1ea3n thu, Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu, Thu thanh to\\u00e1n, B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\\r\\n=> C\\u00e1c ch\\u1ee9c n\\u0103ng b\\u00ean trong s\\u1ebd kh\\u00f4ng b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng b\\u1edfi vi\\u1ec7c thay \\u0111\\u1ed5i c\\u00e1c menu n\\u00e0y! Vi\\u1ec7c thay \\u0111\\u1ed5i s\\u1ebd gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng ti\\u1ec7n h\\u01a1n trong vi\\u1ec7c thao t\\u00e1c & chuy\\u1ec3n \\u0111\\u1ed5i gi\\u1eefa c\\u00e1c menu!\\r\\n=> N\\u1ebfu ch\\u01b0a th\\u1ea5y thay \\u0111\\u1ed5i menu theo ph\\u00e2n quy\\u1ec1n, vui l\\u00f2ng tho\\u00e1t ra & \\u0111\\u0103ng nh\\u1eadp l\\u1ea1i nh\\u00e9!\",\"attachments\":\"[\\\"menu01_20230605133933.jpg\\\",\\\"menu02_20230605133933.jpg\\\",\\\"menu03_20230605133933.jpg\\\",\\\"menu04_20230605133933.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-06-05\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-06-05 13:39:33\",\"updated_at\":\"2024-10-09 08:44:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/menu01_20230605133933.jpg"}, {"id": "{\"id\":206,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m PMS \\u0111\\u00e3 c\\u1eadp nh\\u1eadt l\\u1ea1i danh s\\u00e1ch c\\u00e1c menu theo 2 nh\\u00f3m ph\\u00e2n h\\u1ec7:\\r\\n1. Ph\\u00e2n h\\u1ec7 Dinh D\\u01b0\\u1ee1ng: Nh\\u00e0 cung c\\u1ea5p, Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng, M\\u00f3n \\u0103n, Nh\\u1eadp kho, T\\u1ed3n kho, L\\u1ecbch s\\u1eed kho, Theo d\\u00f5i s\\u1ed5 kho, Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n, Bi\\u1ec3u m\\u1eabu th\\u1ed1ng k\\u00ea\\r\\n2. Ph\\u00e2n h\\u1ec7 Thu chi: Qu\\u1ea3n l\\u00fd kh\\u1ed1i, Qu\\u1ea3n l\\u00fd l\\u1edbp, Danh s\\u00e1ch h\\u1ecdc sinh, C\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9, \\u0110i\\u1ec3m danh h\\u1ecdc sinh, Theo d\\u00f5i h\\u1ecdc sinh, Danh m\\u1ee5c kho\\u1ea3n thu, Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu, Thu thanh to\\u00e1n, B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\\r\\n=> C\\u00e1c ch\\u1ee9c n\\u0103ng b\\u00ean trong s\\u1ebd kh\\u00f4ng b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng b\\u1edfi vi\\u1ec7c thay \\u0111\\u1ed5i c\\u00e1c menu n\\u00e0y! Vi\\u1ec7c thay \\u0111\\u1ed5i s\\u1ebd gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng ti\\u1ec7n h\\u01a1n trong vi\\u1ec7c thao t\\u00e1c & chuy\\u1ec3n \\u0111\\u1ed5i gi\\u1eefa c\\u00e1c menu!\\r\\n=> N\\u1ebfu ch\\u01b0a th\\u1ea5y thay \\u0111\\u1ed5i menu theo ph\\u00e2n quy\\u1ec1n, vui l\\u00f2ng tho\\u00e1t ra & \\u0111\\u0103ng nh\\u1eadp l\\u1ea1i nh\\u00e9!\",\"attachments\":\"[\\\"menu01_20230605133933.jpg\\\",\\\"menu02_20230605133933.jpg\\\",\\\"menu03_20230605133933.jpg\\\",\\\"menu04_20230605133933.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-06-05\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-06-05 13:39:33\",\"updated_at\":\"2024-10-09 08:44:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/menu02_20230605133933.jpg"}, {"id": "{\"id\":206,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m PMS \\u0111\\u00e3 c\\u1eadp nh\\u1eadt l\\u1ea1i danh s\\u00e1ch c\\u00e1c menu theo 2 nh\\u00f3m ph\\u00e2n h\\u1ec7:\\r\\n1. Ph\\u00e2n h\\u1ec7 Dinh D\\u01b0\\u1ee1ng: Nh\\u00e0 cung c\\u1ea5p, Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng, M\\u00f3n \\u0103n, Nh\\u1eadp kho, T\\u1ed3n kho, L\\u1ecbch s\\u1eed kho, Theo d\\u00f5i s\\u1ed5 kho, Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n, Bi\\u1ec3u m\\u1eabu th\\u1ed1ng k\\u00ea\\r\\n2. Ph\\u00e2n h\\u1ec7 Thu chi: Qu\\u1ea3n l\\u00fd kh\\u1ed1i, Qu\\u1ea3n l\\u00fd l\\u1edbp, Danh s\\u00e1ch h\\u1ecdc sinh, C\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9, \\u0110i\\u1ec3m danh h\\u1ecdc sinh, Theo d\\u00f5i h\\u1ecdc sinh, Danh m\\u1ee5c kho\\u1ea3n thu, Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu, Thu thanh to\\u00e1n, B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\\r\\n=> C\\u00e1c ch\\u1ee9c n\\u0103ng b\\u00ean trong s\\u1ebd kh\\u00f4ng b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng b\\u1edfi vi\\u1ec7c thay \\u0111\\u1ed5i c\\u00e1c menu n\\u00e0y! Vi\\u1ec7c thay \\u0111\\u1ed5i s\\u1ebd gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng ti\\u1ec7n h\\u01a1n trong vi\\u1ec7c thao t\\u00e1c & chuy\\u1ec3n \\u0111\\u1ed5i gi\\u1eefa c\\u00e1c menu!\\r\\n=> N\\u1ebfu ch\\u01b0a th\\u1ea5y thay \\u0111\\u1ed5i menu theo ph\\u00e2n quy\\u1ec1n, vui l\\u00f2ng tho\\u00e1t ra & \\u0111\\u0103ng nh\\u1eadp l\\u1ea1i nh\\u00e9!\",\"attachments\":\"[\\\"menu01_20230605133933.jpg\\\",\\\"menu02_20230605133933.jpg\\\",\\\"menu03_20230605133933.jpg\\\",\\\"menu04_20230605133933.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-06-05\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-06-05 13:39:33\",\"updated_at\":\"2024-10-09 08:44:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/menu03_20230605133933.jpg"}, {"id": "{\"id\":206,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m PMS \\u0111\\u00e3 c\\u1eadp nh\\u1eadt l\\u1ea1i danh s\\u00e1ch c\\u00e1c menu theo 2 nh\\u00f3m ph\\u00e2n h\\u1ec7:\\r\\n1. Ph\\u00e2n h\\u1ec7 Dinh D\\u01b0\\u1ee1ng: Nh\\u00e0 cung c\\u1ea5p, Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng, M\\u00f3n \\u0103n, Nh\\u1eadp kho, T\\u1ed3n kho, L\\u1ecbch s\\u1eed kho, Theo d\\u00f5i s\\u1ed5 kho, Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n, Bi\\u1ec3u m\\u1eabu th\\u1ed1ng k\\u00ea\\r\\n2. Ph\\u00e2n h\\u1ec7 Thu chi: Qu\\u1ea3n l\\u00fd kh\\u1ed1i, Qu\\u1ea3n l\\u00fd l\\u1edbp, Danh s\\u00e1ch h\\u1ecdc sinh, C\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9, \\u0110i\\u1ec3m danh h\\u1ecdc sinh, Theo d\\u00f5i h\\u1ecdc sinh, Danh m\\u1ee5c kho\\u1ea3n thu, Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu, Thu thanh to\\u00e1n, B\\u00e1o c\\u00e1o th\\u1ed1ng k\\u00ea\\r\\n=> C\\u00e1c ch\\u1ee9c n\\u0103ng b\\u00ean trong s\\u1ebd kh\\u00f4ng b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng b\\u1edfi vi\\u1ec7c thay \\u0111\\u1ed5i c\\u00e1c menu n\\u00e0y! Vi\\u1ec7c thay \\u0111\\u1ed5i s\\u1ebd gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng ti\\u1ec7n h\\u01a1n trong vi\\u1ec7c thao t\\u00e1c & chuy\\u1ec3n \\u0111\\u1ed5i gi\\u1eefa c\\u00e1c menu!\\r\\n=> N\\u1ebfu ch\\u01b0a th\\u1ea5y thay \\u0111\\u1ed5i menu theo ph\\u00e2n quy\\u1ec1n, vui l\\u00f2ng tho\\u00e1t ra & \\u0111\\u0103ng nh\\u1eadp l\\u1ea1i nh\\u00e9!\",\"attachments\":\"[\\\"menu01_20230605133933.jpg\\\",\\\"menu02_20230605133933.jpg\\\",\\\"menu03_20230605133933.jpg\\\",\\\"menu04_20230605133933.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u1eadp nh\\u1eadt  menu theo ph\\u00e2n h\\u1ec7 trong PMS\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-06-05\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-06-05 13:39:33\",\"updated_at\":\"2024-10-09 08:44:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_3", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/menu04_20230605133933.jpg"}], "is_read": 1, "active_at": 1685898000, "created": 1685947173, "lastmodified": 1728438245}, {"id": 194, "subject": "Tiện ích Mở/kh<PERSON><PERSON> các chức năng theo năm học", "summary": "<PERSON>ần mề<PERSON> b<PERSON> sung Tiện ích Mở/kh<PERSON><PERSON> các chức năng theo năm học, gi<PERSON><PERSON> các trường hạn chế thao tác nhầm dữ liệu ở các năm học cũ:\r\n1. Năm học 2022-2023 trở đi hệ thống sẽ mặc định mở để thao tác dữ liệu bình thường\r\n=> Cuối năm học trường có thể khóa dữ liệu trong bảng điều khiển sau khi đã chốt sổ sách\r\n2. Năm học 2021-2022 trở về trước, hệ thống sẽ mặc định tạm khóa các chức năng để hạn chế việc thao tác nhầm năm cũ!\r\n=> Trường có thể tự mở lại để sửa nếu có nhu cầu sửa lại dữ liệu năm học cũ\r\nGhi chú: Cột trạng thái được tích [v] sẽ là đang mở, ko tích [ ] sẽ là tạm khóa.\r\n*** Video hướng dẫn thầy/cô vui lòng click vào : <a href=\"https://youtu.be/_-sSC9QFjns\">Xem tại đây</a>", "summary_en": "<PERSON><PERSON> mem bo sung Tien ich Mo/khoa cac chuc nang theo nam hoc, giup cac truong han che thao tac nham du lieu o cac nam hoc cu:\r\n1. Nam hoc 2022-2023 tro di he thong se mac dinh mo de thao tac du lieu binh thuong\r\n=> Cuoi nam hoc truong co the khoa du lieu trong bang dieu khien sau khi da chot so sach\r\n2. Nam hoc 2021-2022 tro ve truoc, he thong se mac dinh tam khoa cac chuc nang de han che viec thao tac nham nam cu!\r\n=> Truong co the tu mo lai de sua neu co nhu cau sua lai du lieu nam hoc cu\r\nGhi chu: Cot trang thai duoc tich [v] se la dang mo, ko tich [ ] se la tam khoa.\r\n*** Video huong dan thay/co vui long click vao : <a href=\"https://youtu.be/_-sSC9QFjns\">Xem tai day</a>", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Lock01_20230102131246.jpg", "attachments": [{"id": "{\"id\":194,\"unit_id\":2539,\"course_id\":null,\"title\":\"Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m b\\u1ed5 sung Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc, gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng h\\u1ea1n ch\\u1ebf thao t\\u00e1c nh\\u1ea7m d\\u1eef li\\u1ec7u \\u1edf c\\u00e1c n\\u0103m h\\u1ecdc c\\u0169:\\r\\n1. N\\u0103m h\\u1ecdc 2022-2023 tr\\u1edf \\u0111i h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh m\\u1edf \\u0111\\u1ec3 thao t\\u00e1c d\\u1eef li\\u1ec7u b\\u00ecnh th\\u01b0\\u1eddng\\r\\n=> Cu\\u1ed1i n\\u0103m h\\u1ecdc tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 kh\\u00f3a d\\u1eef li\\u1ec7u trong b\\u1ea3ng \\u0111i\\u1ec1u khi\\u1ec3n sau khi \\u0111\\u00e3 ch\\u1ed1t s\\u1ed5 s\\u00e1ch\\r\\n2. N\\u0103m h\\u1ecdc 2021-2022 tr\\u1edf v\\u1ec1 tr\\u01b0\\u1edbc, h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh t\\u1ea1m kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng \\u0111\\u1ec3 h\\u1ea1n ch\\u1ebf vi\\u1ec7c thao t\\u00e1c nh\\u1ea7m n\\u0103m c\\u0169!\\r\\n=> Tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 t\\u1ef1 m\\u1edf l\\u1ea1i \\u0111\\u1ec3 s\\u1eeda n\\u1ebfu c\\u00f3 nhu c\\u1ea7u s\\u1eeda l\\u1ea1i d\\u1eef li\\u1ec7u n\\u0103m h\\u1ecdc c\\u0169\\r\\nGhi ch\\u00fa: C\\u1ed9t tr\\u1ea1ng th\\u00e1i \\u0111\\u01b0\\u1ee3c t\\u00edch [v] s\\u1ebd l\\u00e0 \\u0111ang m\\u1edf, ko t\\u00edch [ ] s\\u1ebd l\\u00e0 t\\u1ea1m kh\\u00f3a.\\r\\n*** Video h\\u01b0\\u1edbng d\\u1eabn th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng click v\\u00e0o : <a href=\\\"https:\\/\\/youtu.be\\/_-sSC9QFjns\\\">Xem t\\u1ea1i \\u0111\\u00e2y<\\/a>\",\"attachments\":\"[\\\"VT_Lock01_20230102131246.jpg\\\",\\\"VT_Lock02a_20230102131246.jpg\\\",\\\"VT_Lock03_20230102131246.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-01-02\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-01-02 12:40:07\",\"updated_at\":\"2024-03-27 11:03:06\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Lock01_20230102131246.jpg"}, {"id": "{\"id\":194,\"unit_id\":2539,\"course_id\":null,\"title\":\"Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m b\\u1ed5 sung Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc, gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng h\\u1ea1n ch\\u1ebf thao t\\u00e1c nh\\u1ea7m d\\u1eef li\\u1ec7u \\u1edf c\\u00e1c n\\u0103m h\\u1ecdc c\\u0169:\\r\\n1. N\\u0103m h\\u1ecdc 2022-2023 tr\\u1edf \\u0111i h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh m\\u1edf \\u0111\\u1ec3 thao t\\u00e1c d\\u1eef li\\u1ec7u b\\u00ecnh th\\u01b0\\u1eddng\\r\\n=> Cu\\u1ed1i n\\u0103m h\\u1ecdc tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 kh\\u00f3a d\\u1eef li\\u1ec7u trong b\\u1ea3ng \\u0111i\\u1ec1u khi\\u1ec3n sau khi \\u0111\\u00e3 ch\\u1ed1t s\\u1ed5 s\\u00e1ch\\r\\n2. N\\u0103m h\\u1ecdc 2021-2022 tr\\u1edf v\\u1ec1 tr\\u01b0\\u1edbc, h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh t\\u1ea1m kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng \\u0111\\u1ec3 h\\u1ea1n ch\\u1ebf vi\\u1ec7c thao t\\u00e1c nh\\u1ea7m n\\u0103m c\\u0169!\\r\\n=> Tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 t\\u1ef1 m\\u1edf l\\u1ea1i \\u0111\\u1ec3 s\\u1eeda n\\u1ebfu c\\u00f3 nhu c\\u1ea7u s\\u1eeda l\\u1ea1i d\\u1eef li\\u1ec7u n\\u0103m h\\u1ecdc c\\u0169\\r\\nGhi ch\\u00fa: C\\u1ed9t tr\\u1ea1ng th\\u00e1i \\u0111\\u01b0\\u1ee3c t\\u00edch [v] s\\u1ebd l\\u00e0 \\u0111ang m\\u1edf, ko t\\u00edch [ ] s\\u1ebd l\\u00e0 t\\u1ea1m kh\\u00f3a.\\r\\n*** Video h\\u01b0\\u1edbng d\\u1eabn th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng click v\\u00e0o : <a href=\\\"https:\\/\\/youtu.be\\/_-sSC9QFjns\\\">Xem t\\u1ea1i \\u0111\\u00e2y<\\/a>\",\"attachments\":\"[\\\"VT_Lock01_20230102131246.jpg\\\",\\\"VT_Lock02a_20230102131246.jpg\\\",\\\"VT_Lock03_20230102131246.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-01-02\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-01-02 12:40:07\",\"updated_at\":\"2024-03-27 11:03:06\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Lock02a_20230102131246.jpg"}, {"id": "{\"id\":194,\"unit_id\":2539,\"course_id\":null,\"title\":\"Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m b\\u1ed5 sung Ti\\u1ec7n \\u00edch M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc, gi\\u00fap c\\u00e1c tr\\u01b0\\u1eddng h\\u1ea1n ch\\u1ebf thao t\\u00e1c nh\\u1ea7m d\\u1eef li\\u1ec7u \\u1edf c\\u00e1c n\\u0103m h\\u1ecdc c\\u0169:\\r\\n1. N\\u0103m h\\u1ecdc 2022-2023 tr\\u1edf \\u0111i h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh m\\u1edf \\u0111\\u1ec3 thao t\\u00e1c d\\u1eef li\\u1ec7u b\\u00ecnh th\\u01b0\\u1eddng\\r\\n=> Cu\\u1ed1i n\\u0103m h\\u1ecdc tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 kh\\u00f3a d\\u1eef li\\u1ec7u trong b\\u1ea3ng \\u0111i\\u1ec1u khi\\u1ec3n sau khi \\u0111\\u00e3 ch\\u1ed1t s\\u1ed5 s\\u00e1ch\\r\\n2. N\\u0103m h\\u1ecdc 2021-2022 tr\\u1edf v\\u1ec1 tr\\u01b0\\u1edbc, h\\u1ec7 th\\u1ed1ng s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh t\\u1ea1m kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng \\u0111\\u1ec3 h\\u1ea1n ch\\u1ebf vi\\u1ec7c thao t\\u00e1c nh\\u1ea7m n\\u0103m c\\u0169!\\r\\n=> Tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 t\\u1ef1 m\\u1edf l\\u1ea1i \\u0111\\u1ec3 s\\u1eeda n\\u1ebfu c\\u00f3 nhu c\\u1ea7u s\\u1eeda l\\u1ea1i d\\u1eef li\\u1ec7u n\\u0103m h\\u1ecdc c\\u0169\\r\\nGhi ch\\u00fa: C\\u1ed9t tr\\u1ea1ng th\\u00e1i \\u0111\\u01b0\\u1ee3c t\\u00edch [v] s\\u1ebd l\\u00e0 \\u0111ang m\\u1edf, ko t\\u00edch [ ] s\\u1ebd l\\u00e0 t\\u1ea1m kh\\u00f3a.\\r\\n*** Video h\\u01b0\\u1edbng d\\u1eabn th\\u1ea7y\\/c\\u00f4 vui l\\u00f2ng click v\\u00e0o : <a href=\\\"https:\\/\\/youtu.be\\/_-sSC9QFjns\\\">Xem t\\u1ea1i \\u0111\\u00e2y<\\/a>\",\"attachments\":\"[\\\"VT_Lock01_20230102131246.jpg\\\",\\\"VT_Lock02a_20230102131246.jpg\\\",\\\"VT_Lock03_20230102131246.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"M\\u1edf\\/kh\\u00f3a c\\u00e1c ch\\u1ee9c n\\u0103ng theo n\\u0103m h\\u1ecdc\",\"status\":1,\"is_read\":1,\"active_at\":\"2023-01-02\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":1,\"created_at\":\"2023-01-02 12:40:07\",\"updated_at\":\"2024-03-27 11:03:06\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Lock03_20230102131246.jpg"}], "is_read": 1, "active_at": 1672592400, "created": 1672638007, "lastmodified": 1711512186}, {"id": 179, "subject": "PHIẾU KHẢO SÁT VỀ SẢN PHẨM VÀ DỊCH VỤ KHÁCH HÀNG", "summary": "<b>LINK KHẢO SÁT:</b>\r\n<a target=\"_blank\" href=\"https://docs.google.com/forms/d/1bGyaYa3OloGLu6Svxu6gYMCP6Onm3pwmQBYEwiU6J68/viewform?edit_requested=true#settings\">https://docs.google.com/forms/d/1bGyaYa3OloGLu6Svxu6gYMCP6Onm3pwmQBYEwiU6J68/viewform?edit_requested=true#settings</a>\r\n\r\nK<PERSON>h gửi <PERSON>u<PERSON>/<PERSON>ô,\r\n\r\nCảm ơn Quý Thầy/C<PERSON> đã đồng hành cùng Công ty Vietec, chúng tôi rất trân trọng và cảm ơn sự ủng hộ của Quý Thầy/Cô trong khoảng thời gian vừa qua.\r\nCông ty Vietec luôn muốn có thể cải thiện sản phẩm và cung cấp cho <PERSON>u<PERSON>/<PERSON><PERSON> dịch vụ tuyệ<PERSON> nhất, vì vậy chúng tôi hy vọng có thể nhận được những phản hồi từ Quý Thầy/Cô.\r\n\r\nViệc tham gia khảo sát là không bắt buộc và không tiềm ẩn bất cứ rủi ro nào. Trong quá trình tham gia khảo sát, nếu Quý Thầy/Cô cảm thấy không thoải mái ở bất cứ câu hỏi nào, Quý Thầy/Cô có thể bỏ qua hoặc rút lui. Tuy nhiên, ý kiến đóng góp của Quý Thầy/Cô là cực kỳ quan trọng với Công ty và được sử dụng trong báo cáo tổng hợp lên Ban Quản lý Công ty.\r\n\r\nKết quả của phiếu khảo sát được giữ tuyệt mật và được sử dụng nhằm mục đích tham khảo, đo lường và là căn cứ điều chỉnh, thay đổi, cập nhật các sản phẩm, quy trình làm việc trong thời gian tới của Công ty Vietec. Trong quá trình khảo sát, nếu Quý Thầy/Cô có thắc mắc hoặc chưa rõ thông tin nào, Quý Thầy/Cô có thể liên hệ số điện thoại CSKH: 19000101 - 19000200 hoặc tại địa chỉ email: <EMAIL> để được giải đáp.\r\n\r\nTrân trọng cảm ơn sự hợp tác của Quý Thầy/Cô!", "summary_en": "<b>LINK KHAO SAT:</b>\r\n<a target=\"_blank\" href=\"https://docs.google.com/forms/d/1bGyaYa3OloGLu6Svxu6gYMCP6Onm3pwmQBYEwiU6J68/viewform?edit_requested=true#settings\">https://docs.google.com/forms/d/1bGyaYa3OloGLu6Svxu6gYMCP6Onm3pwmQBYEwiU6J68/viewform?edit_requested=true#settings</a>\r\n\r\nKinh gui Quy Thay/Co,\r\n\r\nCam on Quy Thay/Co da dong hanh cung Cong ty Vietec, chung toi rat tran trong va cam on su ung ho cua Quy Thay/Co trong khoang thoi gian vua qua.\r\nCong ty Vietec luon muon co the cai thien san pham va cung cap cho Quy Thay/Co dich vu tuyet nhat, vi vay chung toi hy vong co the nhan duoc nhung phan hoi tu Quy Thay/Co.\r\n\r\nViec tham gia khao sat la khong bat buoc va khong tiem an bat cu rui ro nao. Trong qua trinh tham gia khao sat, neu Quy Thay/Co cam thay khong thoai mai o bat cu cau hoi nao, Quy Thay/Co co the bo qua hoac rut lui. Tuy nhien, y kien dong gop cua Quy Thay/Co la cuc ky quan trong voi Cong ty va duoc su dung trong bao cao tong hop len Ban Quan ly Cong ty.\r\n\r\nKet qua cua phieu khao sat duoc giu tuyet mat va duoc su dung nham muc dich tham khao, do luong va la can cu dieu chinh, thay doi, cap nhat cac san pham, quy trinh lam viec trong thoi gian toi cua Cong ty Vietec. Trong qua trinh khao sat, neu Quy Thay/Co co thac mac hoac chua ro thong tin nao, Quy Thay/Co co the lien he so dien thoai CSKH: 19000101 - 19000200 hoac tai dia chi email: <EMAIL> de duoc giai dap.\r\n\r\nTran trong cam on su hop tac cua Quy Thay/Co!", "content": null, "attachment": "", "attachments": [], "is_read": 1, "active_at": 1647450000, "created": 1647506120, "lastmodified": 1711513228}, {"id": 162, "subject": "Phiếu kê chợ: <PERSON><PERSON> sung mẫu xem, in & xuất excel cho nhiều ngày trong tháng", "summary": "Phiếu kê chợ: <PERSON><PERSON> sung mẫu xem, in & xuất excel cho nhiều ngày trong tháng đối với các mẫu Phiếu kê chợ, <PERSON> tươi, khô. Vui lòng xem vị trí mẫu trong ảnh đính kèm", "summary_en": "<PERSON><PERSON> ke cho: <PERSON> sung mau xem, in & xuat excel cho nhieu ngay trong thang doi voi cac mau <PERSON>eu ke cho, <PERSON> tuoi, kho. Vui long xem vi tri mau trong anh dinh kem", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/05_PKC_01NhieuNgay_20210826171909.jpg", "attachments": [{"id": "{\"id\":162,\"unit_id\":2539,\"course_id\":null,\"title\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng\",\"summary\":null,\"content\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng \\u0111\\u1ed1i v\\u1edbi c\\u00e1c m\\u1eabu Phi\\u1ebfu k\\u00ea ch\\u1ee3, M1 t\\u01b0\\u01a1i, kh\\u00f4. Vui l\\u00f2ng xem v\\u1ecb tr\\u00ed m\\u1eabu trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"05_PKC_01NhieuNgay_20210826171909.jpg\\\",\\\"05_PKC_02M1Tuoi_NhieuNgay01_20210826171909.jpg\\\",\\\"05_PKC_03M1Kho_NhieuNgay01_20210826171909.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\",\"status\":1,\"is_read\":1,\"active_at\":\"2021-08-16\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 17:19:09\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/05_PKC_01NhieuNgay_20210826171909.jpg"}, {"id": "{\"id\":162,\"unit_id\":2539,\"course_id\":null,\"title\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng\",\"summary\":null,\"content\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng \\u0111\\u1ed1i v\\u1edbi c\\u00e1c m\\u1eabu Phi\\u1ebfu k\\u00ea ch\\u1ee3, M1 t\\u01b0\\u01a1i, kh\\u00f4. Vui l\\u00f2ng xem v\\u1ecb tr\\u00ed m\\u1eabu trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"05_PKC_01NhieuNgay_20210826171909.jpg\\\",\\\"05_PKC_02M1Tuoi_NhieuNgay01_20210826171909.jpg\\\",\\\"05_PKC_03M1Kho_NhieuNgay01_20210826171909.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\",\"status\":1,\"is_read\":1,\"active_at\":\"2021-08-16\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 17:19:09\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/05_PKC_02M1Tuoi_NhieuNgay01_20210826171909.jpg"}, {"id": "{\"id\":162,\"unit_id\":2539,\"course_id\":null,\"title\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng\",\"summary\":null,\"content\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3: B\\u1ed5 sung m\\u1eabu xem, in & xu\\u1ea5t excel cho nhi\\u1ec1u ng\\u00e0y trong th\\u00e1ng \\u0111\\u1ed1i v\\u1edbi c\\u00e1c m\\u1eabu Phi\\u1ebfu k\\u00ea ch\\u1ee3, M1 t\\u01b0\\u01a1i, kh\\u00f4. Vui l\\u00f2ng xem v\\u1ecb tr\\u00ed m\\u1eabu trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"05_PKC_01NhieuNgay_20210826171909.jpg\\\",\\\"05_PKC_02M1Tuoi_NhieuNgay01_20210826171909.jpg\\\",\\\"05_PKC_03M1Kho_NhieuNgay01_20210826171909.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\",\"status\":1,\"is_read\":1,\"active_at\":\"2021-08-16\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 17:19:09\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/05_PKC_03M1Kho_NhieuNgay01_20210826171909.jpg"}], "is_read": 1, "active_at": 1629046800, "created": 1629973149, "lastmodified": 1646393233}, {"id": 129, "subject": "Cập nhật 1 số tiện ích về Sổ kho, <PERSON><PERSON><PERSON> chiế<PERSON> tiền chợ, <PERSON><PERSON> t<PERSON>h khẩu phần ăn", "summary": "<PERSON><PERSON>n mềm đã cập nhật 1 số tiện ích về Sổ kho, Đ<PERSON><PERSON> chiếu tiền chợ, STKPA:\r\n1. Sổ kho: Thêm tiện ích tải sổ kho cho cả tháng, mỗi ngày trên 1 sheet của Excel\r\n2. Đ<PERSON><PERSON> chiếu tiền chợ: Thêm tiện ích lựa chọn \"Đi chợ\", \"Nhập kho\"\r\n3. Sổ tính Khẩu phần ăn: Thêm tiện ích sắp xếp thực phẩm kho lên trước thực phẩm đi chợ trong biểu tách bữa\r\nVui lòng xem thêm trong các ảnh đính kèm!", "summary_en": "<PERSON>an mem da cap nhat 1 so tien ich ve So kho, <PERSON><PERSON> chieu tien cho, STKPA:\r\n1. So kho: Them tien ich tai so kho cho ca thang, moi ngay tren 1 sheet cua Excel\r\n2. <PERSON><PERSON> chieu tien cho: Them tien ich lua chon \"Di cho\", \"<PERSON>ha<PERSON> kho\"\r\n3. So tinh Khau phan an: Them tien ich sap xep thuc pham kho len truoc thuc pham di cho trong bieu tach bua\r\nVui long xem them trong cac anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/13SoKhoCaThang_20210721152751.png", "attachments": [{"id": "{\"id\":129,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, S\\u1ed5 t\\u00ednh kh\\u1ea9u ph\\u1ea7n \\u0103n\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, STKPA:\\r\\n1. S\\u1ed5 kho: Th\\u00eam ti\\u1ec7n \\u00edch t\\u1ea3i s\\u1ed5 kho cho c\\u1ea3 th\\u00e1ng, m\\u1ed7i ng\\u00e0y tr\\u00ean 1 sheet c\\u1ee7a Excel\\r\\n2. \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3: Th\\u00eam ti\\u1ec7n \\u00edch l\\u1ef1a ch\\u1ecdn \\\"\\u0110i ch\\u1ee3\\\", \\\"Nh\\u1eadp kho\\\"\\r\\n3. S\\u1ed5 t\\u00ednh Kh\\u1ea9u ph\\u1ea7n \\u0103n: Th\\u00eam ti\\u1ec7n \\u00edch s\\u1eafp x\\u1ebfp th\\u1ef1c ph\\u1ea9m kho l\\u00ean tr\\u01b0\\u1edbc th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 trong bi\\u1ec3u t\\u00e1ch b\\u1eefa\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"13SoKhoCaThang_20210721152751.png\\\",\\\"14DoiChieuTC_20210721152751.png\\\",\\\"15STKPA_TachBua_20210721152751.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 15:27:51\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/13SoKhoCaThang_20210721152751.png"}, {"id": "{\"id\":129,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, S\\u1ed5 t\\u00ednh kh\\u1ea9u ph\\u1ea7n \\u0103n\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, STKPA:\\r\\n1. S\\u1ed5 kho: Th\\u00eam ti\\u1ec7n \\u00edch t\\u1ea3i s\\u1ed5 kho cho c\\u1ea3 th\\u00e1ng, m\\u1ed7i ng\\u00e0y tr\\u00ean 1 sheet c\\u1ee7a Excel\\r\\n2. \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3: Th\\u00eam ti\\u1ec7n \\u00edch l\\u1ef1a ch\\u1ecdn \\\"\\u0110i ch\\u1ee3\\\", \\\"Nh\\u1eadp kho\\\"\\r\\n3. S\\u1ed5 t\\u00ednh Kh\\u1ea9u ph\\u1ea7n \\u0103n: Th\\u00eam ti\\u1ec7n \\u00edch s\\u1eafp x\\u1ebfp th\\u1ef1c ph\\u1ea9m kho l\\u00ean tr\\u01b0\\u1edbc th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 trong bi\\u1ec3u t\\u00e1ch b\\u1eefa\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"13SoKhoCaThang_20210721152751.png\\\",\\\"14DoiChieuTC_20210721152751.png\\\",\\\"15STKPA_TachBua_20210721152751.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 15:27:51\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/14DoiChieuTC_20210721152751.png"}, {"id": "{\"id\":129,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, S\\u1ed5 t\\u00ednh kh\\u1ea9u ph\\u1ea7n \\u0103n\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt 1 s\\u1ed1 ti\\u1ec7n \\u00edch v\\u1ec1 S\\u1ed5 kho, \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3, STKPA:\\r\\n1. S\\u1ed5 kho: Th\\u00eam ti\\u1ec7n \\u00edch t\\u1ea3i s\\u1ed5 kho cho c\\u1ea3 th\\u00e1ng, m\\u1ed7i ng\\u00e0y tr\\u00ean 1 sheet c\\u1ee7a Excel\\r\\n2. \\u0110\\u1ed1i chi\\u1ebfu ti\\u1ec1n ch\\u1ee3: Th\\u00eam ti\\u1ec7n \\u00edch l\\u1ef1a ch\\u1ecdn \\\"\\u0110i ch\\u1ee3\\\", \\\"Nh\\u1eadp kho\\\"\\r\\n3. S\\u1ed5 t\\u00ednh Kh\\u1ea9u ph\\u1ea7n \\u0103n: Th\\u00eam ti\\u1ec7n \\u00edch s\\u1eafp x\\u1ebfp th\\u1ef1c ph\\u1ea9m kho l\\u00ean tr\\u01b0\\u1edbc th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 trong bi\\u1ec3u t\\u00e1ch b\\u1eefa\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"13SoKhoCaThang_20210721152751.png\\\",\\\"14DoiChieuTC_20210721152751.png\\\",\\\"15STKPA_TachBua_20210721152751.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 15:27:51\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/15STKPA_TachBua_20210721152751.png"}], "is_read": 1, "active_at": 1626800400, "created": 1626856071, "lastmodified": 1646393233}, {"id": 127, "subject": "<PERSON><PERSON> đối Khẩu phần: Tiện ích tìm kiếm món ăn trong thư viện chia sẻ hoặc thêm món ăn mới", "summary": "Phần mềm đã hỗ trợ tiện ích tìm món ăn trong thư viện chia sẻ của hệ thống hoặc thêm mới món ăn khi làm Cân đối khẩu phần mà ko cần quay về Danh mục món ăn để tạo:\r\nB1: Trong Cân đối khẩu phần, ch<PERSON><PERSON> \"Thêm món ăn\" cho bữa cần thêm\r\nB2: <PERSON><PERSON> chọn \"Tìm trong thư viện chia sẻ\" hoặc \"Món ăn mới\" tùy theo nhu cầu\r\nB3: Thực hiện thêm món ăn như bình thường, phần mềm có thêm tiện ích \"Cập nhập về DS\r\nmón ăn của trường\" để lần sau có thể dùng lại đc!\r\nVui lòng xem thêm trong ảnh đính kèm!\r\n", "summary_en": "Phan mem da ho tro tien ich tim mon an trong thu vien chia se cua he thong hoac them moi mon an khi lam Can doi khau phan ma ko can quay ve Danh muc mon an de tao:\r\nB1: Trong Can doi khau phan, chon \"Them mon an\" cho bua can them\r\nB2: <PERSON><PERSON> chon \"Tim trong thu vien chia se\" hoac \"Mon an moi\" tuy theo nhu cau\r\nB3: Thuc hien them mon an nhu binh thuong, phan mem co them tien ich \"Cap nhap ve DS\r\nmon an cua truong\" de lan sau co the dung lai dc!\r\nVui long xem them trong anh dinh kem!\r\n", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/09CDKP_MonAn_20210721151858.png", "attachments": [{"id": "{\"id\":127,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00e2n \\u0111\\u1ed1i Kh\\u1ea9u ph\\u1ea7n: Ti\\u1ec7n \\u00edch t\\u00ecm ki\\u1ebfm m\\u00f3n \\u0103n trong th\\u01b0 vi\\u1ec7n chia s\\u1ebb ho\\u1eb7c th\\u00eam m\\u00f3n \\u0103n m\\u1edbi\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch t\\u00ecm m\\u00f3n \\u0103n trong th\\u01b0 vi\\u1ec7n chia s\\u1ebb c\\u1ee7a h\\u1ec7 th\\u1ed1ng ho\\u1eb7c th\\u00eam m\\u1edbi m\\u00f3n \\u0103n khi l\\u00e0m C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n m\\u00e0 ko c\\u1ea7n quay v\\u1ec1 Danh m\\u1ee5c m\\u00f3n \\u0103n \\u0111\\u1ec3 t\\u1ea1o:\\r\\nB1: Trong C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n, ch\\u1ecdn \\\"Th\\u00eam m\\u00f3n \\u0103n\\\" cho b\\u1eefa c\\u1ea7n th\\u00eam\\r\\nB2: Cick ch\\u1ecdn \\\"T\\u00ecm trong th\\u01b0 vi\\u1ec7n chia s\\u1ebb\\\" ho\\u1eb7c \\\"M\\u00f3n \\u0103n m\\u1edbi\\\" t\\u00f9y theo nhu c\\u1ea7u\\r\\nB3: Th\\u1ef1c hi\\u1ec7n th\\u00eam m\\u00f3n \\u0103n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, ph\\u1ea7n m\\u1ec1m c\\u00f3 th\\u00eam ti\\u1ec7n \\u00edch \\\"C\\u1eadp nh\\u1eadp v\\u1ec1 DS\\r\\nm\\u00f3n \\u0103n c\\u1ee7a tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 l\\u1ea7n sau c\\u00f3 th\\u1ec3 d\\u00f9ng l\\u1ea1i \\u0111c!\\r\\nVui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\\r\\n\",\"attachments\":\"[\\\"09CDKP_MonAn_20210721151858.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 15:18:58\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/09CDKP_MonAn_20210721151858.png"}], "is_read": 1, "active_at": 1626800400, "created": 1626855538, "lastmodified": 1646393233}, {"id": 123, "subject": "Thu phí: Ti<PERSON>n ích giảm giá sữa hoặc 1 số khoản thu theo ngày khác ăn", "summary": "Thực tế ở một số trường áp dụng việc giảm giá Sữa cho 1 số học sinh có hoàn cảnh khó khăn. VD mỗi phiếu (hộp) sữa có mức đóng là 7000đ, tuy nhiên những HS đặc biệt này được giảm giá còn 3000đ. Nhà trường có thể xử lý theo hướng dẫn sau:\r\nB1: Click vào biểu tượng con mắt ở phần Thu thanh toán của HS được giảm giá\r\nB2: Chọn \"Tắt công thức liên hệ giữa giá tiền phải đóng & phiếu thu mới...\"\r\nB3: Nhập giá 1 phiếu cho HS được giảm giá, vd 3000\r\n=> <PERSON><PERSON><PERSON> bi<PERSON><PERSON> la<PERSON>, phiếu thu, điểm danh của những HS này sẽ mặc định được tính trên đơn giá sữa đã giảm. <PERSON><PERSON> lưu vết qua các tháng cho đến khi trường thiết lập lại về giá chung của trường!\r\nVui lòng xem thêm trong ảnh đính kèm!", "summary_en": "Thuc te o mot so truong ap dung viec giam gia Sua cho 1 so hoc sinh co hoan canh kho khan. VD moi phieu (hop) sua co muc dong la 7000d, tuy nhien nhung HS dac biet nay duoc giam gia con 3000d. <PERSON>ha truong co the xu ly theo huong dan sau:\r\nB1: Click vao bieu tuong con mat o phan Thu thanh toan cua HS duoc giam gia\r\nB2: Chon \"Tat cong thuc lien he giua gia tien phai dong & phieu thu moi...\"\r\nB3: Nhap gia 1 phieu cho HS duoc giam gia, vd 3000\r\n=> Cac bien lai, phieu thu, diem danh cua nhung HS nay se mac dinh duoc tinh tren don gia sua da giam. Co luu vet qua cac thang cho den khi truong thiet lap lai ve gia chung cua truong!\r\nVui long xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_GiamGia_20210721150059.png", "attachments": [{"id": "{\"id\":123,\"unit_id\":2539,\"course_id\":null,\"title\":\"Thu ph\\u00ed: Ti\\u1ec7n \\u00edch gi\\u1ea3m gi\\u00e1 s\\u1eefa ho\\u1eb7c 1 s\\u1ed1 kho\\u1ea3n thu theo ng\\u00e0y kh\\u00e1c \\u0103n\",\"summary\":null,\"content\":\"Th\\u1ef1c t\\u1ebf \\u1edf m\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng \\u00e1p d\\u1ee5ng vi\\u1ec7c gi\\u1ea3m gi\\u00e1 S\\u1eefa cho 1 s\\u1ed1 h\\u1ecdc sinh c\\u00f3 ho\\u00e0n c\\u1ea3nh kh\\u00f3 kh\\u0103n. VD m\\u1ed7i phi\\u1ebfu (h\\u1ed9p) s\\u1eefa c\\u00f3 m\\u1ee9c \\u0111\\u00f3ng l\\u00e0 7000\\u0111, tuy nhi\\u00ean nh\\u1eefng HS \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y \\u0111\\u01b0\\u1ee3c gi\\u1ea3m gi\\u00e1 c\\u00f2n 3000\\u0111. Nh\\u00e0 tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 x\\u1eed l\\u00fd theo h\\u01b0\\u1edbng d\\u1eabn sau:\\r\\nB1: Click v\\u00e0o bi\\u1ec3u t\\u01b0\\u1ee3ng con m\\u1eaft \\u1edf ph\\u1ea7n Thu thanh to\\u00e1n c\\u1ee7a HS \\u0111\\u01b0\\u1ee3c gi\\u1ea3m gi\\u00e1\\r\\nB2: Ch\\u1ecdn \\\"T\\u1eaft c\\u00f4ng th\\u1ee9c li\\u00ean h\\u1ec7 gi\\u1eefa gi\\u00e1 ti\\u1ec1n ph\\u1ea3i \\u0111\\u00f3ng & phi\\u1ebfu thu m\\u1edbi...\\\"\\r\\nB3: Nh\\u1eadp gi\\u00e1 1 phi\\u1ebfu cho HS \\u0111\\u01b0\\u1ee3c gi\\u1ea3m gi\\u00e1, vd 3000\\r\\n=> C\\u00e1c bi\\u00ean lai, phi\\u1ebfu thu, \\u0111i\\u1ec3m danh c\\u1ee7a nh\\u1eefng HS n\\u00e0y s\\u1ebd m\\u1eb7c \\u0111\\u1ecbnh \\u0111\\u01b0\\u1ee3c t\\u00ednh tr\\u00ean \\u0111\\u01a1n gi\\u00e1 s\\u1eefa \\u0111\\u00e3 gi\\u1ea3m. C\\u00f3 l\\u01b0u v\\u1ebft qua c\\u00e1c th\\u00e1ng cho \\u0111\\u1ebfn khi tr\\u01b0\\u1eddng thi\\u1ebft l\\u1eadp l\\u1ea1i v\\u1ec1 gi\\u00e1 chung c\\u1ee7a tr\\u01b0\\u1eddng!\\r\\nVui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"ThuTT_GiamGia_20210721150059.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 15:00:59\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_GiamGia_20210721150059.png"}], "is_read": 1, "active_at": 1626800400, "created": 1626854459, "lastmodified": 1646393233}, {"id": 121, "subject": "Thu phí: <PERSON><PERSON><PERSON><PERSON> <PERSON>ch chọn một số học sinh để in biên lai, phi<PERSON>u thu", "summary": "<PERSON>ần mềm đã bổ sung tiện ích chọn 1 số hs để in biên lai, phiếu thu. Trước đó phần cũng đã cho phép in 1 HS đã chọn hoặc cả lớp! Vui lòng xem thêm ảnh ví dụ đính kèm!", "summary_en": "<PERSON>an mem da bo sung tien ich chon 1 so hs de in bien lai, phieu thu. <PERSON>ruoc do phan cung da cho phep in 1 HS da chon hoac ca lop! Vui long xem them anh vi du dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuThanhToan1_20210721144612.png", "attachments": [{"id": "{\"id\":121,\"unit_id\":2539,\"course_id\":null,\"title\":\"Thu ph\\u00ed: Ti\\u1ec7n \\u00edch ch\\u1ecdn m\\u1ed9t s\\u1ed1 h\\u1ecdc sinh \\u0111\\u1ec3 in bi\\u00ean lai, phi\\u1ebfu thu\",\"summary\":null,\"content\":\"Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch ch\\u1ecdn 1 s\\u1ed1 hs \\u0111\\u1ec3 in bi\\u00ean lai, phi\\u1ebfu thu. Tr\\u01b0\\u1edbc \\u0111\\u00f3 ph\\u1ea7n c\\u0169ng \\u0111\\u00e3 cho ph\\u00e9p in 1 HS \\u0111\\u00e3 ch\\u1ecdn ho\\u1eb7c c\\u1ea3 l\\u1edbp! Vui l\\u00f2ng xem th\\u00eam \\u1ea3nh v\\u00ed d\\u1ee5 \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"ThuThanhToan1_20210721144612.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-07-21\",\"deactive_at\":\"2021-07-21\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-07-21 14:46:12\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuThanhToan1_20210721144612.png"}], "is_read": 1, "active_at": 1626800400, "created": 1626853572, "lastmodified": 1646393233}, {"id": 117, "subject": "HƯỚNG DẪN XỬ LÝ THU PHÍ THÁNG 2/2021 - NGHỈ CẢ THÁNG DO COVID-19", "summary": "HƯỚNG DẪN XỬ LÝ THU PHÍ THÁNG 2/2021 - NGHỈ CẢ THÁNG DO COVID-19:\r\nBước 1: Trong cấu hình ngày nghỉ vẫn thiết lập như bình thường hoặc để nghỉ tất cả các ngày\r\nBước 2: Trong phần Thu thanh toán chỉ cần Click đúp vào chữ (i) màu cam ở cột \"Nghỉ cả tháng\", để báo nghỉ cả tháng cho cả lớp, lần lư<PERSON>t thực hiện với các lớp\r\n=> <PERSON><PERSON> khi thực hiện xong bước 2, thì tất cả các phiếu tồn từ tháng 1/2021 sẽ được chuyển tiếp lên tháng 3/2021 như bình thường (tương tự cách cho từng HS nghỉ cả tháng)\r\nVui lòng xem thêm ảnh đính kèm!", "summary_en": "HUONG DAN XU LY THU PHI THANG 2/2021 - NGHI CA THANG DO COVID-19:\r\nBuoc 1: Trong cau hinh ngay nghi van thiet lap nhu binh thuong hoac de nghi tat ca cac ngay\r\nBuoc 2: Trong phan Thu thanh toan chi can Click dup vao chu (i) mau cam o cot \"Nghi ca thang\", de bao nghi ca thang cho ca lop, lan luot thuc hien voi cac lop\r\n=> Sau khi thuc hien xong buoc 2, thi tat ca cac phieu ton tu thang 1/2021 se duoc chuyen tiep len thang 3/2021 nhu binh thuong (tuong tu cach cho tung HS nghi ca thang)\r\nVui long xem them anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_FeeT2_20210302083318.png", "attachments": [{"id": "{\"id\":117,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd THU PH\\u00cd TH\\u00c1NG 2\\/2021 - NGH\\u1ec8 C\\u1ea2 TH\\u00c1NG DO COVID-19\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd THU PH\\u00cd TH\\u00c1NG 2\\/2021 - NGH\\u1ec8 C\\u1ea2 TH\\u00c1NG DO COVID-19:\\r\\nB\\u01b0\\u1edbc 1: Trong c\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9 v\\u1eabn thi\\u1ebft l\\u1eadp nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng ho\\u1eb7c \\u0111\\u1ec3 ngh\\u1ec9 t\\u1ea5t c\\u1ea3 c\\u00e1c ng\\u00e0y\\r\\nB\\u01b0\\u1edbc 2: Trong ph\\u1ea7n Thu thanh to\\u00e1n ch\\u1ec9 c\\u1ea7n Click \\u0111\\u00fap v\\u00e0o ch\\u1eef (i) m\\u00e0u cam \\u1edf c\\u1ed9t \\\"Ngh\\u1ec9 c\\u1ea3 th\\u00e1ng\\\", \\u0111\\u1ec3 b\\u00e1o ngh\\u1ec9 c\\u1ea3 th\\u00e1ng cho c\\u1ea3 l\\u1edbp, l\\u1ea7n l\\u01b0\\u1ee3t th\\u1ef1c hi\\u1ec7n v\\u1edbi c\\u00e1c l\\u1edbp\\r\\n=> Sau khi th\\u1ef1c hi\\u1ec7n xong b\\u01b0\\u1edbc 2, th\\u00ec t\\u1ea5t c\\u1ea3 c\\u00e1c phi\\u1ebfu t\\u1ed3n t\\u1eeb th\\u00e1ng 1\\/2021 s\\u1ebd \\u0111\\u01b0\\u1ee3c chuy\\u1ec3n ti\\u1ebfp l\\u00ean th\\u00e1ng 3\\/2021 nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng (t\\u01b0\\u01a1ng t\\u1ef1 c\\u00e1ch cho t\\u1eebng HS ngh\\u1ec9 c\\u1ea3 th\\u00e1ng)\\r\\nVui l\\u00f2ng xem th\\u00eam \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"VT_FeeT2_20210302083318.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2021-03-02\",\"deactive_at\":\"2021-03-02\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-03-02 08:33:18\",\"updated_at\":\"2021-03-11 08:50:59\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_FeeT2_20210302083318.png"}], "is_read": 1, "active_at": 1614618000, "created": 1614648798, "lastmodified": 1615427459}, {"id": 114, "subject": "HƯỚNG DẪN XỬ LÝ SỮA HỌC ĐƯỜNG THÁNG 1/2021", "summary": "Tháng 1/2021 có 1 số trường tạm dừng chương trình Sữa học đườ<PERSON>, tuy nhiên vẫn muốn chuyển phiếu tồn từ tháng 12/2020 lên tháng 1/2021 để trả lại vào tháng 1/2020 hoặc tiếp tục chuyển tồn cho đến khi Chương trình Sữa học đường tiếp tục!\r\nGIẢI PHÁP XỬ LÝ NHƯ SAU:\r\n- B1: Cấu hình ngày nghỉ T1/2021 nh<PERSON> bình thường, ko học Chủ nhật\r\n- B2: Thiết lập S<PERSON><PERSON> học đường theo ngày, vẫn để giá như Tháng 12/2020, chọn vào Chủ nhật để ko thu thêm, chỉ chuyển tồn. C<PERSON> trả lại cuối tháng hay ko thì vẫn làm như mọi tháng\r\n- B3: Ko cần điểm danh Sữa vì ko học Chủ nhật!\r\n\r\nCác t/h trả lại Sữa học đường vào cuối tháng 12/2020 cho những HS còn tồn phiếu vẫn như bình thường!", "summary_en": "Thang 1/2021 co 1 so truong tam dung chuong trinh Sua hoc duong, tuy nhien van muon chuyen phieu ton tu thang 12/2020 len thang 1/2021 de tra lai vao thang 1/2020 hoac tiep tuc chuyen ton cho den khi Chuong trinh Sua hoc duong tiep tuc!\r\nGIAI PHAP XU LY NHU SAU:\r\n- B1: Cau hinh ngay nghi T1/2021 nhu binh thuong, ko hoc Chu nhat\r\n- B2: Thiet lap Sua hoc duong theo ngay, van de gia nhu Thang 12/2020, chon vao Chu nhat de ko thu them, chi chuyen ton. Co tra lai cuoi thang hay ko thi van lam nhu moi thang\r\n- B3: Ko can diem danh Sua vi ko hoc Chu nhat!\r\n\r\nCac t/h tra lai Sua hoc duong vao cuoi thang 12/2020 cho nhung HS con ton phieu van nhu binh thuong!", "content": null, "attachment": "", "attachments": [], "is_read": 1, "active_at": 1609693200, "created": 1609735402, "lastmodified": 1615427457}, {"id": 42, "subject": "ĐỐI TƯỢNG MIỄN GIẢM SỮA HỌC ĐƯỜNG: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích xử lý các trường hợp HS được miễn giảm sữa học đường", "summary": "ĐỐI TƯỢNG MIỄN GIẢM SỮA HỌC ĐƯỜNG: <PERSON><PERSON>n mềm đã hỗ trợ tiện ích xử lý các trường hợp HS được miễn giảm sữa học đường! Không phải đóng tiền, phiếu sữa nhưng vẫn tính phiếu sử dụng theo điểm danh!\r\nVui lòng xem hướng dẫn sử dụng trong ảnh đính kèm!", "summary_en": "DOI TUONG MIEN GIAM SUA HOC DUONG: <PERSON>an mem da ho tro tien ich xu ly cac truong hop HS duoc mien giam sua hoc duong! <PERSON><PERSON> phai dong tien, phieu sua nhung van tinh phieu su dung theo diem danh!\r\nVui long xem huong dan su dung trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Sua_HD_MP1_20201219223105.png", "attachments": [{"id": "{\"id\":42,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110\\u1ed0I T\\u01af\\u1ee2NG MI\\u1ec4N GI\\u1ea2M S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch x\\u1eed l\\u00fd c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p HS \\u0111\\u01b0\\u1ee3c mi\\u1ec5n gi\\u1ea3m s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"\\u0110\\u1ed0I T\\u01af\\u1ee2NG MI\\u1ec4N GI\\u1ea2M S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch x\\u1eed l\\u00fd c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p HS \\u0111\\u01b0\\u1ee3c mi\\u1ec5n gi\\u1ea3m s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng! Kh\\u00f4ng ph\\u1ea3i \\u0111\\u00f3ng ti\\u1ec1n, phi\\u1ebfu s\\u1eefa nh\\u01b0ng v\\u1eabn t\\u00ednh phi\\u1ebfu s\\u1eed d\\u1ee5ng theo \\u0111i\\u1ec3m danh!\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn s\\u1eed d\\u1ee5ng trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"Sua_HD_MP1_20201219223105.png\\\",\\\"Sua_HD_MP2_20201219223105.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-12-05\",\"deactive_at\":\"2020-12-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-12-05 06:30:00\",\"updated_at\":\"2021-03-11 08:51:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Sua_HD_MP1_20201219223105.png"}, {"id": "{\"id\":42,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110\\u1ed0I T\\u01af\\u1ee2NG MI\\u1ec4N GI\\u1ea2M S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch x\\u1eed l\\u00fd c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p HS \\u0111\\u01b0\\u1ee3c mi\\u1ec5n gi\\u1ea3m s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"\\u0110\\u1ed0I T\\u01af\\u1ee2NG MI\\u1ec4N GI\\u1ea2M S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch x\\u1eed l\\u00fd c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p HS \\u0111\\u01b0\\u1ee3c mi\\u1ec5n gi\\u1ea3m s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng! Kh\\u00f4ng ph\\u1ea3i \\u0111\\u00f3ng ti\\u1ec1n, phi\\u1ebfu s\\u1eefa nh\\u01b0ng v\\u1eabn t\\u00ednh phi\\u1ebfu s\\u1eed d\\u1ee5ng theo \\u0111i\\u1ec3m danh!\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn s\\u1eed d\\u1ee5ng trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"Sua_HD_MP1_20201219223105.png\\\",\\\"Sua_HD_MP2_20201219223105.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-12-05\",\"deactive_at\":\"2020-12-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-12-05 06:30:00\",\"updated_at\":\"2021-03-11 08:51:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Sua_HD_MP2_20201219223105.png"}], "is_read": 1, "active_at": 1607101200, "created": 1607124600, "lastmodified": 1615427465}, {"id": 41, "subject": "KHÓA SỔ ĐIỂM DANH LỚP/THÁNG: <PERSON>ần mềm đã hỗ trợ tiện tích khóa sổ điểm danh theo từng tháng cho lớp", "summary": "KHÓA SỔ ĐIỂM DANH LỚP/THÁNG: <PERSON>ần mềm đã hỗ trợ tiện tích khóa sổ điểm danh theo từng tháng cho lớp! Vui lòng xem hướng dẫn chi tiết trong ảnh đính kèm!", "summary_en": "KHOA SO DIEM DANH LOP/THANG: Phan mem da ho tro tien tich khoa so diem danh theo tung thang cho lop! Vui long xem huong dan chi tiet trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_DiemDanhLock_20201219223006.png", "attachments": [{"id": "{\"id\":41,\"unit_id\":2539,\"course_id\":null,\"title\":\"KH\\u00d3A S\\u1ed4 \\u0110I\\u1ec2M DANH L\\u1edaP\\/TH\\u00c1NG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n t\\u00edch kh\\u00f3a s\\u1ed5 \\u0111i\\u1ec3m danh theo t\\u1eebng th\\u00e1ng cho l\\u1edbp\",\"summary\":null,\"content\":\"KH\\u00d3A S\\u1ed4 \\u0110I\\u1ec2M DANH L\\u1edaP\\/TH\\u00c1NG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n t\\u00edch kh\\u00f3a s\\u1ed5 \\u0111i\\u1ec3m danh theo t\\u1eebng th\\u00e1ng cho l\\u1edbp! Vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"VT_DiemDanhLock_20201219223006.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-12-05\",\"deactive_at\":\"2020-12-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-12-05 06:30:00\",\"updated_at\":\"2021-03-11 08:51:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_DiemDanhLock_20201219223006.png"}], "is_read": 1, "active_at": 1607101200, "created": 1607124600, "lastmodified": 1615427465}, {"id": 161, "subject": "<PERSON><PERSON> đối khẩu phần: Tiện ích đổi món ăn sang bữa khác trong cùng nhóm Ăn chính", "summary": "<PERSON><PERSON> đối khẩu phần: Tiện ích đổi món ăn sang bữa khác trong cùng nhóm Ăn chính. Vui lòng xem thêm trong ảnh đính kèm", "summary_en": "Can doi khau phan: Tien ich doi mon an sang bua khac trong cung nhom An chinh. Vui long xem them trong anh dinh kem", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/04_CDKP_ChangeBA_20210826170421.jpg", "attachments": [{"id": "{\"id\":161,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n: Ti\\u1ec7n \\u00edch \\u0111\\u1ed5i m\\u00f3n \\u0103n sang b\\u1eefa kh\\u00e1c trong c\\u00f9ng nh\\u00f3m \\u0102n ch\\u00ednh\",\"summary\":null,\"content\":\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n: Ti\\u1ec7n \\u00edch \\u0111\\u1ed5i m\\u00f3n \\u0103n sang b\\u1eefa kh\\u00e1c trong c\\u00f9ng nh\\u00f3m \\u0102n ch\\u00ednh. Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"04_CDKP_ChangeBA_20210826170421.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"C\\u0110KP - T\\u0110M\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-11-28\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 17:04:21\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/04_CDKP_ChangeBA_20210826170421.jpg"}], "is_read": 1, "active_at": 1606496400, "created": 1629972261, "lastmodified": 1646393233}, {"id": 40, "subject": "CẬP NHẬT TIỆN ÍCH: <PERSON><PERSON><PERSON> mềm mới cập nhật một số tiện ích trong quản lý kho, thực đơn mẫu!", "summary": "CẬP NHẬT TIỆN ÍCH: <PERSON><PERSON><PERSON> mềm mới cập nhật một số tiện ích trong quản lý kho, thực đơn mẫu!\r\n- Quản lý kho - Nhập kho: <PERSON><PERSON> sung tiện ích lọc theo nhà cung cấp để xem, in phiếu nhập kho\r\n- Thực đơn mẫu: B<PERSON> sung tiện ích khôi phục các thực đơn mẫu đã nhỡ tay xóa nhầm!\r\nVui lòng xem thêm trong các ảnh đính kèm dưới đây!", "summary_en": "CAP NHAT TIEN ICH: <PERSON>an mem moi cap nhat mot so tien ich trong quan ly kho, thuc don mau!\r\n- Quan ly kho - Nhap kho: Bo sung tien ich loc theo nha cung cap de xem, in phieu nhap kho\r\n- Thuc don mau: Bo sung tien ich khoi phuc cac thuc don mau da nho tay xoa nham!\r\nVui long xem them trong cac anh dinh kem duoi day!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Kho_NCC_20201219222912.png", "attachments": [{"id": "{\"id\":40,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eacP NH\\u1eacT TI\\u1ec6N \\u00cdCH: Ph\\u1ea7n m\\u1ec1m m\\u1edbi c\\u1eadp nh\\u1eadt m\\u1ed9t s\\u1ed1 ti\\u1ec7n \\u00edch trong qu\\u1ea3n l\\u00fd kho, th\\u1ef1c \\u0111\\u01a1n m\\u1eabu!\",\"summary\":null,\"content\":\"C\\u1eacP NH\\u1eacT TI\\u1ec6N \\u00cdCH: Ph\\u1ea7n m\\u1ec1m m\\u1edbi c\\u1eadp nh\\u1eadt m\\u1ed9t s\\u1ed1 ti\\u1ec7n \\u00edch trong qu\\u1ea3n l\\u00fd kho, th\\u1ef1c \\u0111\\u01a1n m\\u1eabu!\\r\\n- Qu\\u1ea3n l\\u00fd kho - Nh\\u1eadp kho: B\\u1ed5 sung ti\\u1ec7n \\u00edch l\\u1ecdc theo nh\\u00e0 cung c\\u1ea5p \\u0111\\u1ec3 xem, in phi\\u1ebfu nh\\u1eadp kho\\r\\n- Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu: B\\u1ed5 sung ti\\u1ec7n \\u00edch kh\\u00f4i ph\\u1ee5c c\\u00e1c th\\u1ef1c \\u0111\\u01a1n m\\u1eabu \\u0111\\u00e3 nh\\u1ee1 tay x\\u00f3a nh\\u1ea7m!\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"VT_Kho_NCC_20201219222912.png\\\",\\\"VT_TDM_Trash_20201219222912.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-11-16\",\"deactive_at\":\"2020-11-16\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-11-16 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_Kho_NCC_20201219222912.png"}, {"id": "{\"id\":40,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eacP NH\\u1eacT TI\\u1ec6N \\u00cdCH: Ph\\u1ea7n m\\u1ec1m m\\u1edbi c\\u1eadp nh\\u1eadt m\\u1ed9t s\\u1ed1 ti\\u1ec7n \\u00edch trong qu\\u1ea3n l\\u00fd kho, th\\u1ef1c \\u0111\\u01a1n m\\u1eabu!\",\"summary\":null,\"content\":\"C\\u1eacP NH\\u1eacT TI\\u1ec6N \\u00cdCH: Ph\\u1ea7n m\\u1ec1m m\\u1edbi c\\u1eadp nh\\u1eadt m\\u1ed9t s\\u1ed1 ti\\u1ec7n \\u00edch trong qu\\u1ea3n l\\u00fd kho, th\\u1ef1c \\u0111\\u01a1n m\\u1eabu!\\r\\n- Qu\\u1ea3n l\\u00fd kho - Nh\\u1eadp kho: B\\u1ed5 sung ti\\u1ec7n \\u00edch l\\u1ecdc theo nh\\u00e0 cung c\\u1ea5p \\u0111\\u1ec3 xem, in phi\\u1ebfu nh\\u1eadp kho\\r\\n- Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu: B\\u1ed5 sung ti\\u1ec7n \\u00edch kh\\u00f4i ph\\u1ee5c c\\u00e1c th\\u1ef1c \\u0111\\u01a1n m\\u1eabu \\u0111\\u00e3 nh\\u1ee1 tay x\\u00f3a nh\\u1ea7m!\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"VT_Kho_NCC_20201219222912.png\\\",\\\"VT_TDM_Trash_20201219222912.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-11-16\",\"deactive_at\":\"2020-11-16\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-11-16 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_TDM_Trash_20201219222912.png"}], "is_read": 1, "active_at": 1605459600, "created": 1605483000, "lastmodified": 1608431257}, {"id": 39, "subject": "CẬP NHẬT TRÌNH DUYỆT CHROME: <PERSON><PERSON>t số trường hợp máy tính cá nhân có phiên bản trình duyệt Chrome quá cũ", "summary": "CẬP NHẬT TRÌNH DUYỆT CHROME: M<PERSON>t số trường hợp máy tính cá nhân có phiên bản trình duyệt Chrome quá cũ, hiệu suất làm việc không cao hoặc vào 1 số trang, ch<PERSON><PERSON> n<PERSON>, thao tác chậm mà ko phải do mạng Internet.\r\nQuý Thầy Cô có thể làm theo các ảnh hướng dẫn sau để cập nhật phiên bản Chrome mới nhất!", "summary_en": "CAP NHAT TRINH DUYET CHROME: <PERSON><PERSON> so truong hop may tinh ca nhan co phien ban trinh duyet Chrome qua cu, hieu suat lam viec khong cao hoac vao 1 so trang, chuc nang, thao tac cham ma ko phai do mang Internet.\r\nQuy Thay Co co the lam theo cac anh huong dan sau de cap nhat phien ban Chrome moi nhat!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Chrome_UpdateA1_20201219222819.png", "attachments": [{"id": "{\"id\":39,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eacP NH\\u1eacT TR\\u00ccNH DUY\\u1ec6T CHROME: M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p m\\u00e1y t\\u00ednh c\\u00e1 nh\\u00e2n c\\u00f3 phi\\u00ean b\\u1ea3n tr\\u00ecnh duy\\u1ec7t Chrome qu\\u00e1 c\\u0169\",\"summary\":null,\"content\":\"C\\u1eacP NH\\u1eacT TR\\u00ccNH DUY\\u1ec6T CHROME: M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p m\\u00e1y t\\u00ednh c\\u00e1 nh\\u00e2n c\\u00f3 phi\\u00ean b\\u1ea3n tr\\u00ecnh duy\\u1ec7t Chrome qu\\u00e1 c\\u0169, hi\\u1ec7u su\\u1ea5t l\\u00e0m vi\\u1ec7c kh\\u00f4ng cao ho\\u1eb7c v\\u00e0o 1 s\\u1ed1 trang, ch\\u1ee9c n\\u0103ng, thao t\\u00e1c ch\\u1eadm m\\u00e0 ko ph\\u1ea3i do m\\u1ea1ng Internet.\\r\\nQu\\u00fd Th\\u1ea7y C\\u00f4 c\\u00f3 th\\u1ec3 l\\u00e0m theo c\\u00e1c \\u1ea3nh h\\u01b0\\u1edbng d\\u1eabn sau \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt phi\\u00ean b\\u1ea3n Chrome m\\u1edbi nh\\u1ea5t!\",\"attachments\":\"[\\\"Chrome_UpdateA1_20201219222819.png\\\",\\\"Chrome_UpdateB_20201219222819.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-11-12\",\"deactive_at\":\"2020-11-12\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-11-12 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Chrome_UpdateA1_20201219222819.png"}, {"id": "{\"id\":39,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eacP NH\\u1eacT TR\\u00ccNH DUY\\u1ec6T CHROME: M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p m\\u00e1y t\\u00ednh c\\u00e1 nh\\u00e2n c\\u00f3 phi\\u00ean b\\u1ea3n tr\\u00ecnh duy\\u1ec7t Chrome qu\\u00e1 c\\u0169\",\"summary\":null,\"content\":\"C\\u1eacP NH\\u1eacT TR\\u00ccNH DUY\\u1ec6T CHROME: M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p m\\u00e1y t\\u00ednh c\\u00e1 nh\\u00e2n c\\u00f3 phi\\u00ean b\\u1ea3n tr\\u00ecnh duy\\u1ec7t Chrome qu\\u00e1 c\\u0169, hi\\u1ec7u su\\u1ea5t l\\u00e0m vi\\u1ec7c kh\\u00f4ng cao ho\\u1eb7c v\\u00e0o 1 s\\u1ed1 trang, ch\\u1ee9c n\\u0103ng, thao t\\u00e1c ch\\u1eadm m\\u00e0 ko ph\\u1ea3i do m\\u1ea1ng Internet.\\r\\nQu\\u00fd Th\\u1ea7y C\\u00f4 c\\u00f3 th\\u1ec3 l\\u00e0m theo c\\u00e1c \\u1ea3nh h\\u01b0\\u1edbng d\\u1eabn sau \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt phi\\u00ean b\\u1ea3n Chrome m\\u1edbi nh\\u1ea5t!\",\"attachments\":\"[\\\"Chrome_UpdateA1_20201219222819.png\\\",\\\"Chrome_UpdateB_20201219222819.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-11-12\",\"deactive_at\":\"2020-11-12\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-11-12 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/Chrome_UpdateB_20201219222819.png"}], "is_read": 1, "active_at": 1605114000, "created": 1605137400, "lastmodified": 1608431257}, {"id": 159, "subject": "<PERSON> dõi HS: <PERSON><PERSON> sung mẫu 3 cho báo cáo điểm danh & tiện ích để trống ô nghỉ P/KP", "summary": "<PERSON> dõi HS: <PERSON><PERSON> sung mẫu 3 cho báo cáo điểm danh & tiện ích để trống ô nghỉ P/KP. Vui lòng xem thêm trong ảnh đính kèm\r\n", "summary_en": "Theo doi HS: <PERSON> sung mau 3 cho bao cao diem danh & tien ich de trong o nghi P/KP. Vui long xem them trong anh dinh kem\r\n", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/03_TheoDoiDD_02_20210826164949.jpg", "attachments": [{"id": "{\"id\":159,\"unit_id\":2539,\"course_id\":null,\"title\":\"Theo d\\u00f5i HS: B\\u1ed5 sung m\\u1eabu 3 cho b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh & ti\\u1ec7n \\u00edch \\u0111\\u1ec3 tr\\u1ed1ng \\u00f4 ngh\\u1ec9 P\\/KP\",\"summary\":null,\"content\":\"Theo d\\u00f5i HS: B\\u1ed5 sung m\\u1eabu 3 cho b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh & ti\\u1ec7n \\u00edch \\u0111\\u1ec3 tr\\u1ed1ng \\u00f4 ngh\\u1ec9 P\\/KP. Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\\r\\n\",\"attachments\":\"[\\\"03_TheoDoiDD_02_20210826164949.jpg\\\",\\\"03_TheoDoiDD_03_20210826164949.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Theo d\\u00f5i HS\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-28\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 16:49:49\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/03_TheoDoiDD_02_20210826164949.jpg"}, {"id": "{\"id\":159,\"unit_id\":2539,\"course_id\":null,\"title\":\"Theo d\\u00f5i HS: B\\u1ed5 sung m\\u1eabu 3 cho b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh & ti\\u1ec7n \\u00edch \\u0111\\u1ec3 tr\\u1ed1ng \\u00f4 ngh\\u1ec9 P\\/KP\",\"summary\":null,\"content\":\"Theo d\\u00f5i HS: B\\u1ed5 sung m\\u1eabu 3 cho b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh & ti\\u1ec7n \\u00edch \\u0111\\u1ec3 tr\\u1ed1ng \\u00f4 ngh\\u1ec9 P\\/KP. Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\\r\\n\",\"attachments\":\"[\\\"03_TheoDoiDD_02_20210826164949.jpg\\\",\\\"03_TheoDoiDD_03_20210826164949.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Theo d\\u00f5i HS\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-28\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 16:49:49\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/03_TheoDoiDD_03_20210826164949.jpg"}], "is_read": 1, "active_at": 1603818000, "created": 1629971389, "lastmodified": 1646393233}, {"id": 158, "subject": "<PERSON> dõi HS: <PERSON><PERSON> sung các sổ chấm sữa, theo dõi điểm danh sữa cho lớp, to<PERSON><PERSON> tr<PERSON><PERSON><PERSON>", "summary": "<PERSON> dõi HS: <PERSON><PERSON><PERSON> mềm đã bổ sung các sổ chấm sữa, theo dõi điểm danh sữa cho lớp, toàn trường. Tiện ích này áp dụng cho các khoản thu theo ngày như Sữ<PERSON>ọ<PERSON>, ăn sáng...Vui lòng xem thêm vị trí trong ảnh đính kèm!\r\n", "summary_en": "Theo doi HS: <PERSON><PERSON> mem da bo sung cac so cham sua, theo doi diem danh sua cho lop, toan truong. Tien ich nay ap dung cho cac khoan thu theo ngay nhu Sua hoc duong, an sang...Vui long xem them vi tri trong anh dinh kem!\r\n", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/03_TheoDoiDD_01_20210826163409.jpg", "attachments": [{"id": "{\"id\":158,\"unit_id\":2539,\"course_id\":null,\"title\":\"Theo d\\u00f5i HS: B\\u1ed5 sung c\\u00e1c s\\u1ed5 ch\\u1ea5m s\\u1eefa, theo d\\u00f5i \\u0111i\\u1ec3m danh s\\u1eefa cho l\\u1edbp, to\\u00e0n tr\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"Theo d\\u00f5i HS: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 b\\u1ed5 sung c\\u00e1c s\\u1ed5 ch\\u1ea5m s\\u1eefa, theo d\\u00f5i \\u0111i\\u1ec3m danh s\\u1eefa cho l\\u1edbp, to\\u00e0n tr\\u01b0\\u1eddng. Ti\\u1ec7n \\u00edch n\\u00e0y \\u00e1p d\\u1ee5ng cho c\\u00e1c kho\\u1ea3n thu theo ng\\u00e0y nh\\u01b0 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0103n s\\u00e1ng...Vui l\\u00f2ng xem th\\u00eam v\\u1ecb tr\\u00ed trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\\r\\n\",\"attachments\":\"[\\\"03_TheoDoiDD_01_20210826163409.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Theo d\\u00f5i HS\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-27\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 16:34:09\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/03_TheoDoiDD_01_20210826163409.jpg"}], "is_read": 1, "active_at": 1603731600, "created": 1629970449, "lastmodified": 1646393233}, {"id": 157, "subject": "<PERSON><PERSON><PERSON> thực 3 bước: <PERSON><PERSON> sung tiện ích hiện thị theo thực ăn mẫu M2 - <PERSON><PERSON><PERSON> tra trước khi chế biến", "summary": "<PERSON><PERSON><PERSON> thực 3 bước: <PERSON><PERSON> sung tiện ích hiện thị theo thực ăn (ko bao gồm hệ số thải bỏ) mẫu M2 - <PERSON><PERSON><PERSON> tra trước khi chế biến", "summary_en": "<PERSON><PERSON> thuc 3 buoc: <PERSON> sung tien ich hien thi theo thuc an (ko bao gom he so thai bo) mau M2 - <PERSON><PERSON> tra truoc khi che bien", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/02_KT3BuocM2_01_20210826155425.jpg", "attachments": [{"id": "{\"id\":157,\"unit_id\":2539,\"course_id\":null,\"title\":\"Ki\\u1ec3m th\\u1ef1c 3 b\\u01b0\\u1edbc: B\\u1ed5 sung ti\\u1ec7n \\u00edch hi\\u1ec7n th\\u1ecb theo th\\u1ef1c \\u0103n m\\u1eabu M2 - Ki\\u1ec3m tra tr\\u01b0\\u1edbc khi ch\\u1ebf bi\\u1ebfn\",\"summary\":null,\"content\":\"Ki\\u1ec3m th\\u1ef1c 3 b\\u01b0\\u1edbc: B\\u1ed5 sung ti\\u1ec7n \\u00edch hi\\u1ec7n th\\u1ecb theo th\\u1ef1c \\u0103n (ko bao g\\u1ed3m h\\u1ec7 s\\u1ed1 th\\u1ea3i b\\u1ecf) m\\u1eabu M2 - Ki\\u1ec3m tra tr\\u01b0\\u1edbc khi ch\\u1ebf bi\\u1ebfn\",\"attachments\":\"[\\\"02_KT3BuocM2_01_20210826155425.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Ki\\u1ec3m th\\u1ef1c 3 b\\u01b0\\u1edbc\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-26\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 15:54:25\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/02_KT3BuocM2_01_20210826155425.jpg"}], "is_read": 1, "active_at": 1603645200, "created": 1629968065, "lastmodified": 1646393233}, {"id": 156, "subject": "<PERSON> dõi sổ kho: T<PERSON><PERSON><PERSON> ích chọn nhà cung cấp để in / xuất dữ liệu báo cáo", "summary": "<PERSON> dõi sổ kho: <PERSON><PERSON><PERSON> mềm đã bổ sung tiện ích chọn nhà cung cấp để in / xuất dữ liệu báo cáo", "summary_en": "<PERSON> doi so kho: <PERSON><PERSON> mem da bo sung tien ich chon nha cung cap de in / xuat du lieu bao cao", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/01_NKho_NCC1_20210826154904.jpg", "attachments": [{"id": "{\"id\":156,\"unit_id\":2539,\"course_id\":null,\"title\":\"Theo d\\u00f5i s\\u1ed5 kho: Ti\\u1ec7n \\u00edch ch\\u1ecdn nh\\u00e0 cung c\\u1ea5p \\u0111\\u1ec3 in \\/ xu\\u1ea5t d\\u1eef li\\u1ec7u b\\u00e1o c\\u00e1o\",\"summary\":null,\"content\":\"Theo d\\u00f5i s\\u1ed5 kho: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch ch\\u1ecdn nh\\u00e0 cung c\\u1ea5p \\u0111\\u1ec3 in \\/ xu\\u1ea5t d\\u1eef li\\u1ec7u b\\u00e1o c\\u00e1o\",\"attachments\":\"[\\\"01_NKho_NCC1_20210826154904.jpg\\\"]\",\"project_code\":\"qlmn\",\"module\":\"Qu\\u1ea3n l\\u00fd kho\",\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-20\",\"deactive_at\":null,\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2021-08-26 15:49:04\",\"updated_at\":\"2022-03-04 18:27:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/01_NKho_NCC1_20210826154904.jpg"}], "is_read": 1, "active_at": 1603126800, "created": 1629967744, "lastmodified": 1646393233}, {"id": 38, "subject": "ĐIỂM DANH SỮA HỌC ĐƯỜNG: <PERSON><PERSON><PERSON> mềm đã cập nhật tiện ích để hỗ trợ các trường về điểm danh sữa học đường", "summary": "ĐIỂM DANH SỮA HỌC ĐƯỜNG: <PERSON><PERSON><PERSON> mềm đã cập nhật tiện ích để hỗ trợ các trường ko cần phải điểm danh sữa học đường đối với những Học sinh đã lưu sổ thu, bỏ tích sữa học đường bên Thu thanh toán!\r\n<PERSON><PERSON><PERSON> sổ chấm sữa, điểm danh sữa cũng sẽ ko tính những học sinh bỏ tích sữa này!\r\nVui lòng xem thêm ví dụ trong ảnh đính kèm!", "summary_en": "DIEM DANH SUA HOC DUONG: Phan mem da cap nhat tien ich de ho tro cac truong ko can phai diem danh sua hoc duong doi voi nhung Hoc sinh da luu so thu, bo tich sua hoc duong ben Thu thanh toan!\r\n<PERSON><PERSON> so cham sua, diem danh sua cung se ko tinh nhung hoc sinh bo tich sua nay!\r\nVui long xem them vi du trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_KoTichSua_20201219222713.png", "attachments": [{"id": "{\"id\":38,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch \\u0111\\u1ec3 h\\u1ed7 tr\\u1ee3 c\\u00e1c tr\\u01b0\\u1eddng v\\u1ec1 \\u0111i\\u1ec3m danh s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch \\u0111\\u1ec3 h\\u1ed7 tr\\u1ee3 c\\u00e1c tr\\u01b0\\u1eddng ko c\\u1ea7n ph\\u1ea3i \\u0111i\\u1ec3m danh s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng \\u0111\\u1ed1i v\\u1edbi nh\\u1eefng H\\u1ecdc sinh \\u0111\\u00e3 l\\u01b0u s\\u1ed5 thu, b\\u1ecf t\\u00edch s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng b\\u00ean Thu thanh to\\u00e1n!\\r\\nC\\u00e1c s\\u1ed5 ch\\u1ea5m s\\u1eefa, \\u0111i\\u1ec3m danh s\\u1eefa c\\u0169ng s\\u1ebd ko t\\u00ednh nh\\u1eefng h\\u1ecdc sinh b\\u1ecf t\\u00edch s\\u1eefa n\\u00e0y!\\r\\nVui l\\u00f2ng xem th\\u00eam v\\u00ed d\\u1ee5 trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"ThuTT_KoTichSua_20201219222713.png\\\",\\\"ThuTT_SoSua_20201219222713.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-19\",\"deactive_at\":\"2020-10-19\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-10-19 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_KoTichSua_20201219222713.png"}, {"id": "{\"id\":38,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch \\u0111\\u1ec3 h\\u1ed7 tr\\u1ee3 c\\u00e1c tr\\u01b0\\u1eddng v\\u1ec1 \\u0111i\\u1ec3m danh s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch \\u0111\\u1ec3 h\\u1ed7 tr\\u1ee3 c\\u00e1c tr\\u01b0\\u1eddng ko c\\u1ea7n ph\\u1ea3i \\u0111i\\u1ec3m danh s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng \\u0111\\u1ed1i v\\u1edbi nh\\u1eefng H\\u1ecdc sinh \\u0111\\u00e3 l\\u01b0u s\\u1ed5 thu, b\\u1ecf t\\u00edch s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng b\\u00ean Thu thanh to\\u00e1n!\\r\\nC\\u00e1c s\\u1ed5 ch\\u1ea5m s\\u1eefa, \\u0111i\\u1ec3m danh s\\u1eefa c\\u0169ng s\\u1ebd ko t\\u00ednh nh\\u1eefng h\\u1ecdc sinh b\\u1ecf t\\u00edch s\\u1eefa n\\u00e0y!\\r\\nVui l\\u00f2ng xem th\\u00eam v\\u00ed d\\u1ee5 trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"ThuTT_KoTichSua_20201219222713.png\\\",\\\"ThuTT_SoSua_20201219222713.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-10-19\",\"deactive_at\":\"2020-10-19\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-10-19 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_SoSua_20201219222713.png"}], "is_read": 1, "active_at": 1603040400, "created": 1603063800, "lastmodified": 1608431257}, {"id": 37, "subject": "DANH MỤC KHOẢN THU: <PERSON><PERSON><PERSON> mềm đã hỗ trợ thiết lập thứ tự phân bổ khi đóng tiền", "summary": "DANH MỤC KHOẢN THU: Phần mềm đã hỗ trợ thiết lập thứ tự phân bổ khi đóng tiền cho các khoản thu trong trường hợp đóng thiếu tiền hoặc có cột \"Tiền dư\" từ tháng trướ<PERSON> sang ở Thu thanh toán!\r\nVD: \"Học phí\" -> Thứ tự phân bổ là 1, \"Tiền ăn trưa\" thứ tự phân bổ là 2\r\n- Trong thiết lập khoản thu Học phí = 200000, Tiền ăn 20 phiếu ~ 400000 (ứng với 20000 1 vé ăn)\r\n=> PHHS đã đóng 500000, thì khi đó thứ tự phân bổ sẽ quyết định ưu tiên đóng khoản nào trước! Ở ví dụ này thì Học phí đã đóng 200000, tiền ăn đã đóng 300000\r\n=> Nếu ko thiết lập thứ tự thì mặc định theo mã khoản thu tăng dần trong danh mục khoản thu!\r\n=> Với các trường hợp đặc biệt không theo quy luật chung, trường vẫn có thể phân bổ thủ công ở \"Con mắt\" trong phần Thu thanh toán như trước đây!\r\n\r\nLƯU Ý: Quy tắc chỉ áp dụng khi gõ số tiền đã đóng hoặc tích đóng đủ, chứ không tự động thay đổi số liệu đã lưu!\r\nVui lòng xem thêm chi tiết trong các ảnh đính kèm!", "summary_en": "DANH MUC KHOAN THU: Ph<PERSON> mem da ho tro thiet lap thu tu phan bo khi dong tien cho cac khoan thu trong truong hop dong thieu tien hoac co cot \"Tien du\" tu thang truoc sang o Thu thanh toan!\r\nVD: \"Hoc phi\" -> Thu tu phan bo la 1, \"Tien an trua\" thu tu phan bo la 2\r\n- Trong thiet lap khoan thu Hoc phi = 200000, Tien an 20 phieu ~ 400000 (ung voi 20000 1 ve an)\r\n=> PHHS da dong 500000, thi khi do thu tu phan bo se quyet dinh uu tien dong khoan nao truoc! O vi du nay thi Hoc phi da dong 200000, tien an da dong 300000\r\n=> Neu ko thiet lap thu tu thi mac dinh theo ma khoan thu tang dan trong danh muc khoan thu!\r\n=> Voi cac truong hop dac biet khong theo quy luat chung, truong van co the phan bo thu cong o \"Con mat\" trong phan Thu thanh toan nhu truoc day!\r\n\r\nLUU Y: Quy tac chi ap dung khi go so tien da dong hoac tich dong du, chu khong tu dong thay doi so lieu da luu!\r\nVui long xem them chi tiet trong cac anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DM_KhoanThu1_20201219222557.png", "attachments": [{"id": "{\"id\":37,\"unit_id\":2539,\"course_id\":null,\"title\":\"DANH M\\u1ee4C KHO\\u1ea2N THU: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 khi \\u0111\\u00f3ng ti\\u1ec1n\",\"summary\":null,\"content\":\"DANH M\\u1ee4C KHO\\u1ea2N THU: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 khi \\u0111\\u00f3ng ti\\u1ec1n cho c\\u00e1c kho\\u1ea3n thu trong tr\\u01b0\\u1eddng h\\u1ee3p \\u0111\\u00f3ng thi\\u1ebfu ti\\u1ec1n ho\\u1eb7c c\\u00f3 c\\u1ed9t \\\"Ti\\u1ec1n d\\u01b0\\\" t\\u1eeb th\\u00e1ng tr\\u01b0\\u1edbc sang \\u1edf Thu thanh to\\u00e1n!\\r\\nVD: \\\"H\\u1ecdc ph\\u00ed\\\" -> Th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 l\\u00e0 1, \\\"Ti\\u1ec1n \\u0103n tr\\u01b0a\\\" th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 l\\u00e0 2\\r\\n- Trong thi\\u1ebft l\\u1eadp kho\\u1ea3n thu H\\u1ecdc ph\\u00ed = 200000, Ti\\u1ec1n \\u0103n 20 phi\\u1ebfu ~ 400000 (\\u1ee9ng v\\u1edbi 20000 1 v\\u00e9 \\u0103n)\\r\\n=> PHHS \\u0111\\u00e3 \\u0111\\u00f3ng 500000, th\\u00ec khi \\u0111\\u00f3 th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 s\\u1ebd quy\\u1ebft \\u0111\\u1ecbnh \\u01b0u ti\\u00ean \\u0111\\u00f3ng kho\\u1ea3n n\\u00e0o tr\\u01b0\\u1edbc! \\u1ede v\\u00ed d\\u1ee5 n\\u00e0y th\\u00ec H\\u1ecdc ph\\u00ed \\u0111\\u00e3 \\u0111\\u00f3ng 200000, ti\\u1ec1n \\u0103n \\u0111\\u00e3 \\u0111\\u00f3ng 300000\\r\\n=> N\\u1ebfu ko thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 th\\u00ec m\\u1eb7c \\u0111\\u1ecbnh theo m\\u00e3 kho\\u1ea3n thu t\\u0103ng d\\u1ea7n trong danh m\\u1ee5c kho\\u1ea3n thu!\\r\\n=> V\\u1edbi c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p \\u0111\\u1eb7c bi\\u1ec7t kh\\u00f4ng theo quy lu\\u1eadt chung, tr\\u01b0\\u1eddng v\\u1eabn c\\u00f3 th\\u1ec3 ph\\u00e2n b\\u1ed5 th\\u1ee7 c\\u00f4ng \\u1edf \\\"Con m\\u1eaft\\\" trong ph\\u1ea7n Thu thanh to\\u00e1n nh\\u01b0 tr\\u01b0\\u1edbc \\u0111\\u00e2y!\\r\\n\\r\\nL\\u01afU \\u00dd: Quy t\\u1eafc ch\\u1ec9 \\u00e1p d\\u1ee5ng khi g\\u00f5 s\\u1ed1 ti\\u1ec1n \\u0111\\u00e3 \\u0111\\u00f3ng ho\\u1eb7c t\\u00edch \\u0111\\u00f3ng \\u0111\\u1ee7, ch\\u1ee9 kh\\u00f4ng t\\u1ef1 \\u0111\\u1ed9ng thay \\u0111\\u1ed5i s\\u1ed1 li\\u1ec7u \\u0111\\u00e3 l\\u01b0u!\\r\\nVui l\\u00f2ng xem th\\u00eam chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"DM_KhoanThu1_20201219222557.png\\\",\\\"DM_KhoanThu2_20201219222557.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-30\",\"deactive_at\":\"2020-09-30\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-30 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DM_KhoanThu1_20201219222557.png"}, {"id": "{\"id\":37,\"unit_id\":2539,\"course_id\":null,\"title\":\"DANH M\\u1ee4C KHO\\u1ea2N THU: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 khi \\u0111\\u00f3ng ti\\u1ec1n\",\"summary\":null,\"content\":\"DANH M\\u1ee4C KHO\\u1ea2N THU: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 khi \\u0111\\u00f3ng ti\\u1ec1n cho c\\u00e1c kho\\u1ea3n thu trong tr\\u01b0\\u1eddng h\\u1ee3p \\u0111\\u00f3ng thi\\u1ebfu ti\\u1ec1n ho\\u1eb7c c\\u00f3 c\\u1ed9t \\\"Ti\\u1ec1n d\\u01b0\\\" t\\u1eeb th\\u00e1ng tr\\u01b0\\u1edbc sang \\u1edf Thu thanh to\\u00e1n!\\r\\nVD: \\\"H\\u1ecdc ph\\u00ed\\\" -> Th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 l\\u00e0 1, \\\"Ti\\u1ec1n \\u0103n tr\\u01b0a\\\" th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 l\\u00e0 2\\r\\n- Trong thi\\u1ebft l\\u1eadp kho\\u1ea3n thu H\\u1ecdc ph\\u00ed = 200000, Ti\\u1ec1n \\u0103n 20 phi\\u1ebfu ~ 400000 (\\u1ee9ng v\\u1edbi 20000 1 v\\u00e9 \\u0103n)\\r\\n=> PHHS \\u0111\\u00e3 \\u0111\\u00f3ng 500000, th\\u00ec khi \\u0111\\u00f3 th\\u1ee9 t\\u1ef1 ph\\u00e2n b\\u1ed5 s\\u1ebd quy\\u1ebft \\u0111\\u1ecbnh \\u01b0u ti\\u00ean \\u0111\\u00f3ng kho\\u1ea3n n\\u00e0o tr\\u01b0\\u1edbc! \\u1ede v\\u00ed d\\u1ee5 n\\u00e0y th\\u00ec H\\u1ecdc ph\\u00ed \\u0111\\u00e3 \\u0111\\u00f3ng 200000, ti\\u1ec1n \\u0103n \\u0111\\u00e3 \\u0111\\u00f3ng 300000\\r\\n=> N\\u1ebfu ko thi\\u1ebft l\\u1eadp th\\u1ee9 t\\u1ef1 th\\u00ec m\\u1eb7c \\u0111\\u1ecbnh theo m\\u00e3 kho\\u1ea3n thu t\\u0103ng d\\u1ea7n trong danh m\\u1ee5c kho\\u1ea3n thu!\\r\\n=> V\\u1edbi c\\u00e1c tr\\u01b0\\u1eddng h\\u1ee3p \\u0111\\u1eb7c bi\\u1ec7t kh\\u00f4ng theo quy lu\\u1eadt chung, tr\\u01b0\\u1eddng v\\u1eabn c\\u00f3 th\\u1ec3 ph\\u00e2n b\\u1ed5 th\\u1ee7 c\\u00f4ng \\u1edf \\\"Con m\\u1eaft\\\" trong ph\\u1ea7n Thu thanh to\\u00e1n nh\\u01b0 tr\\u01b0\\u1edbc \\u0111\\u00e2y!\\r\\n\\r\\nL\\u01afU \\u00dd: Quy t\\u1eafc ch\\u1ec9 \\u00e1p d\\u1ee5ng khi g\\u00f5 s\\u1ed1 ti\\u1ec1n \\u0111\\u00e3 \\u0111\\u00f3ng ho\\u1eb7c t\\u00edch \\u0111\\u00f3ng \\u0111\\u1ee7, ch\\u1ee9 kh\\u00f4ng t\\u1ef1 \\u0111\\u1ed9ng thay \\u0111\\u1ed5i s\\u1ed1 li\\u1ec7u \\u0111\\u00e3 l\\u01b0u!\\r\\nVui l\\u00f2ng xem th\\u00eam chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"DM_KhoanThu1_20201219222557.png\\\",\\\"DM_KhoanThu2_20201219222557.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-30\",\"deactive_at\":\"2020-09-30\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-30 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DM_KhoanThu2_20201219222557.png"}], "is_read": 1, "active_at": 1601398800, "created": 1601422200, "lastmodified": 1608431257}, {"id": 36, "subject": "CẬP NHẬT SỔ THU THANH TOÁN: <PERSON><PERSON> thống đã cập nhật vị trí các tiện ích trong Sổ thu thanh toán", "summary": "CẬP NHẬT SỔ THU THANH TOÁN: <PERSON><PERSON> thống đã cập nhật vị trí các tiện ích trong Sổ thu thanh toán, di chuyển vào nút \"Bánh xe\". <PERSON><PERSON><PERSON> thời bổ sung tiện ích \"Hiện trả lại HS khoản thu\" ngoài khoản tiền ăn cùng trên Sổ thu Than toán!\r\nQuý thầy cô vui lòng cập nhật thông tin hoặc xem thêm trong ảnh đính kèm!", "summary_en": "CAP NHAT SO THU THANH TOAN: He thong da cap nhat vi tri cac tien ich trong So thu thanh toan, di chuyen vao nut \"Banh xe\". <PERSON> thoi bo sung tien ich \"Hien tra lai HS khoan thu\" ngoai khoan tien an cung tren So thu Than toan!\r\nQuy thay co vui long cap nhat thong tin hoac xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/STTT_Opt_20201219222503.png", "attachments": [{"id": "{\"id\":36,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u1eacP NH\\u1eacT S\\u1ed4 THU THANH TO\\u00c1N: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong S\\u1ed5 thu thanh to\\u00e1n\",\"summary\":null,\"content\":\"C\\u1eacP NH\\u1eacT S\\u1ed4 THU THANH TO\\u00c1N: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong S\\u1ed5 thu thanh to\\u00e1n, di chuy\\u1ec3n v\\u00e0o n\\u00fat \\\"B\\u00e1nh xe\\\". \\u0110\\u1ed3ng th\\u1eddi b\\u1ed5 sung ti\\u1ec7n \\u00edch \\\"Hi\\u1ec7n tr\\u1ea3 l\\u1ea1i HS kho\\u1ea3n thu\\\" ngo\\u00e0i kho\\u1ea3n ti\\u1ec1n \\u0103n c\\u00f9ng tr\\u00ean S\\u1ed5 thu Than to\\u00e1n!\\r\\nQu\\u00fd th\\u1ea7y c\\u00f4 vui l\\u00f2ng c\\u1eadp nh\\u1eadt th\\u00f4ng tin ho\\u1eb7c xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"STTT_Opt_20201219222503.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-18\",\"deactive_at\":\"2020-09-18\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-18 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/STTT_Opt_20201219222503.png"}], "is_read": 1, "active_at": 1600362000, "created": 1600385400, "lastmodified": 1608431257}, {"id": 35, "subject": "BỔ SUNG MẪU BÁO CÁO ĐIỂM DANH HỌC SINH", "summary": "BỔ SUNG MẪU BÁO CÁO ĐIỂM DANH HỌC SINH: <PERSON><PERSON> thống đã cập nhật thêm mẫu báo cáo điểm danh mới. Vui lòng xem thêm trong ảnh đính kèm!", "summary_en": "BO SUNG MAU BAO CAO DIEM DANH HOC SINH: He thong da cap nhat them mau bao cao diem danh moi. Vui long xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DiemDanhM3_A_20201219222415.png", "attachments": [{"id": "{\"id\":35,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed4 SUNG M\\u1eaaU B\\u00c1O C\\u00c1O \\u0110I\\u1ec2M DANH H\\u1eccC SINH\",\"summary\":null,\"content\":\"B\\u1ed4 SUNG M\\u1eaaU B\\u00c1O C\\u00c1O \\u0110I\\u1ec2M DANH H\\u1eccC SINH: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 c\\u1eadp nh\\u1eadt th\\u00eam m\\u1eabu b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh m\\u1edbi. Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"DiemDanhM3_A_20201219222415.png\\\",\\\"DiemDanhM3_B_20201219222415.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-18\",\"deactive_at\":\"2020-09-18\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-18 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DiemDanhM3_A_20201219222415.png"}, {"id": "{\"id\":35,\"unit_id\":2539,\"course_id\":null,\"title\":\"B\\u1ed4 SUNG M\\u1eaaU B\\u00c1O C\\u00c1O \\u0110I\\u1ec2M DANH H\\u1eccC SINH\",\"summary\":null,\"content\":\"B\\u1ed4 SUNG M\\u1eaaU B\\u00c1O C\\u00c1O \\u0110I\\u1ec2M DANH H\\u1eccC SINH: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 c\\u1eadp nh\\u1eadt th\\u00eam m\\u1eabu b\\u00e1o c\\u00e1o \\u0111i\\u1ec3m danh m\\u1edbi. Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"DiemDanhM3_A_20201219222415.png\\\",\\\"DiemDanhM3_B_20201219222415.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-18\",\"deactive_at\":\"2020-09-18\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-18 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DiemDanhM3_B_20201219222415.png"}], "is_read": 1, "active_at": 1600362000, "created": 1600385400, "lastmodified": 1608431257}, {"id": 34, "subject": "ĐIỂM DANH KHOẢN THU: <PERSON><PERSON> thống đã bổ sung tiện ích Điểm danh theo khoản thu ngày", "summary": "ĐIỂM DANH KHOẢN THU: <PERSON>ệ thống đã bổ sung tiện ích Điểm danh theo khoản thu ngày, tích hợp kết quả này với Thu thanh toán và báo cáo bên Sổ theo dõi học sinh! Chi tiết như sau:\r\n\r\n1. <PERSON><PERSON><PERSON><PERSON> danh họ<PERSON>, để trống khoản thu sẽ là Điểm danh ch<PERSON>h (chuyên cần + ăn trưa) như trước đây!\r\n\r\n2. Hệ thống sẽ tự đồng bộ 1 chiều từ Điểm danh chính sang Điểm danh khoản thu, không có chiều ngược lại. VD: <PERSON><PERSON><PERSON><PERSON> danh phép (P) Sữa học đường, thì điểm danh chính ko bị ảnh hưởng. Nhưng điểm danh chính phép (P) -> Điểm danh sữa cũng tự động được đồng bộ kết quả!\r\n\r\n3. <PERSON><PERSON><PERSON> kho<PERSON>n thu theo ng<PERSON>y để điểm danh nế<PERSON>, ví dụ: <PERSON><PERSON><PERSON> đi h<PERSON>, c<PERSON> <PERSON>n trưa, nhưng không uống sữa...\r\n\r\n4. Kết quả Điểm danh khoản thu được tự động kết nối với Thu thanh toán để tính phiếu tồn cuối tháng, hoặc xuất ra các báo cáo bên Theo dõi học sinh!\r\n\r\n5. Bạn có thể bỏ qua thông báo này, nếu ko Điểm danh từng khoản thu theo ngày! Các nghiệp vụ của Điểm danh chính & Thu thanh toán, Sổ báo ăn ko có thay đổi gì so với trước!\r\n\r\nVui lòng xem thêm hướng dẫn trong các ảnh đính kèm dưới đây!", "summary_en": "DIEM DANH KHOAN THU: He thong da bo sung tien ich Diem danh theo khoan thu ngay, tich hop ket qua nay voi Thu thanh toan va bao cao ben So theo doi hoc sinh! Chi tiet nhu sau:\r\n\r\n1. <PERSON><PERSON> danh hoc sinh, de trong khoan thu se la Diem danh chinh (chuyen can + an trua) nhu truoc day!\r\n\r\n2. He thong se tu dong bo 1 chieu tu Diem danh chinh sang Diem danh khoan thu, khong co chieu nguoc lai. VD: <PERSON><PERSON>nh <PERSON>he<PERSON> (P) Sua hoc duong, thi diem danh chinh ko bi anh huong. Nhung diem danh chinh phep (P) -> Diem danh sua cung tu dong duoc dong bo ket qua!\r\n\r\n3. Chon khoan thu theo ngay de diem danh neu khac, vi du: Hoc sinh di hoc, co an trua, nhung khong uong sua...\r\n\r\n4. Ket qua Diem danh khoan thu duoc tu dong ket noi voi Thu thanh toan de tinh phieu ton cuoi thang, hoac xuat ra cac bao cao ben Theo doi hoc sinh!\r\n\r\n5. <PERSON> co the bo qua thong bao nay, neu ko Diem danh tung khoan thu theo ngay! Cac nghiep vu cua Diem danh chinh & Thu thanh toan, So bao an ko co thay doi gi so voi truoc!\r\n\r\nVui long xem them huong dan trong cac anh dinh kem duoi day!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DD_KhoanThu1_20201219222321.png", "attachments": [{"id": "{\"id\":34,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y, t\\u00edch h\\u1ee3p k\\u1ebft qu\\u1ea3 n\\u00e0y v\\u1edbi Thu thanh to\\u00e1n v\\u00e0 b\\u00e1o c\\u00e1o b\\u00ean S\\u1ed5 theo d\\u00f5i h\\u1ecdc sinh! Chi ti\\u1ebft nh\\u01b0 sau:\\r\\n\\r\\n1. \\u0110i\\u1ec3m danh h\\u1ecdc sinh, \\u0111\\u1ec3 tr\\u1ed1ng kho\\u1ea3n thu s\\u1ebd l\\u00e0 \\u0110i\\u1ec3m danh ch\\u00ednh (chuy\\u00ean c\\u1ea7n + \\u0103n tr\\u01b0a) nh\\u01b0 tr\\u01b0\\u1edbc \\u0111\\u00e2y!\\r\\n\\r\\n2. H\\u1ec7 th\\u1ed1ng s\\u1ebd t\\u1ef1 \\u0111\\u1ed3ng b\\u1ed9 1 chi\\u1ec1u t\\u1eeb \\u0110i\\u1ec3m danh ch\\u00ednh sang \\u0110i\\u1ec3m danh kho\\u1ea3n thu, kh\\u00f4ng c\\u00f3 chi\\u1ec1u ng\\u01b0\\u1ee3c l\\u1ea1i. VD: \\u0110i\\u1ec3m danh ph\\u00e9p (P) S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, th\\u00ec \\u0111i\\u1ec3m danh ch\\u00ednh ko b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng. Nh\\u01b0ng \\u0111i\\u1ec3m danh ch\\u00ednh ph\\u00e9p (P) -> \\u0110i\\u1ec3m danh s\\u1eefa c\\u0169ng t\\u1ef1 \\u0111\\u1ed9ng \\u0111\\u01b0\\u1ee3c \\u0111\\u1ed3ng b\\u1ed9 k\\u1ebft qu\\u1ea3!\\r\\n\\r\\n3. Ch\\u1ecdn kho\\u1ea3n thu theo ng\\u00e0y \\u0111\\u1ec3 \\u0111i\\u1ec3m danh n\\u1ebfu kh\\u00e1c, v\\u00ed d\\u1ee5: H\\u1ecdc sinh \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n tr\\u01b0a, nh\\u01b0ng kh\\u00f4ng u\\u1ed1ng s\\u1eefa...\\r\\n\\r\\n4. K\\u1ebft qu\\u1ea3 \\u0110i\\u1ec3m danh kho\\u1ea3n thu \\u0111\\u01b0\\u1ee3c t\\u1ef1 \\u0111\\u1ed9ng k\\u1ebft n\\u1ed1i v\\u1edbi Thu thanh to\\u00e1n \\u0111\\u1ec3 t\\u00ednh phi\\u1ebfu t\\u1ed3n cu\\u1ed1i th\\u00e1ng, ho\\u1eb7c xu\\u1ea5t ra c\\u00e1c b\\u00e1o c\\u00e1o b\\u00ean Theo d\\u00f5i h\\u1ecdc sinh!\\r\\n\\r\\n5. B\\u1ea1n c\\u00f3 th\\u1ec3 b\\u1ecf qua th\\u00f4ng b\\u00e1o n\\u00e0y, n\\u1ebfu ko \\u0110i\\u1ec3m danh t\\u1eebng kho\\u1ea3n thu theo ng\\u00e0y! C\\u00e1c nghi\\u1ec7p v\\u1ee5 c\\u1ee7a \\u0110i\\u1ec3m danh ch\\u00ednh & Thu thanh to\\u00e1n, S\\u1ed5 b\\u00e1o \\u0103n ko c\\u00f3 thay \\u0111\\u1ed5i g\\u00ec so v\\u1edbi tr\\u01b0\\u1edbc!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"DD_KhoanThu1_20201219222321.png\\\",\\\"DD_KhoanThu2_20201219222321.png\\\",\\\"DD_KhoanThu3_20201219222321.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-13\",\"deactive_at\":\"2020-09-13\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-13 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DD_KhoanThu1_20201219222321.png"}, {"id": "{\"id\":34,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y, t\\u00edch h\\u1ee3p k\\u1ebft qu\\u1ea3 n\\u00e0y v\\u1edbi Thu thanh to\\u00e1n v\\u00e0 b\\u00e1o c\\u00e1o b\\u00ean S\\u1ed5 theo d\\u00f5i h\\u1ecdc sinh! Chi ti\\u1ebft nh\\u01b0 sau:\\r\\n\\r\\n1. \\u0110i\\u1ec3m danh h\\u1ecdc sinh, \\u0111\\u1ec3 tr\\u1ed1ng kho\\u1ea3n thu s\\u1ebd l\\u00e0 \\u0110i\\u1ec3m danh ch\\u00ednh (chuy\\u00ean c\\u1ea7n + \\u0103n tr\\u01b0a) nh\\u01b0 tr\\u01b0\\u1edbc \\u0111\\u00e2y!\\r\\n\\r\\n2. H\\u1ec7 th\\u1ed1ng s\\u1ebd t\\u1ef1 \\u0111\\u1ed3ng b\\u1ed9 1 chi\\u1ec1u t\\u1eeb \\u0110i\\u1ec3m danh ch\\u00ednh sang \\u0110i\\u1ec3m danh kho\\u1ea3n thu, kh\\u00f4ng c\\u00f3 chi\\u1ec1u ng\\u01b0\\u1ee3c l\\u1ea1i. VD: \\u0110i\\u1ec3m danh ph\\u00e9p (P) S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, th\\u00ec \\u0111i\\u1ec3m danh ch\\u00ednh ko b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng. Nh\\u01b0ng \\u0111i\\u1ec3m danh ch\\u00ednh ph\\u00e9p (P) -> \\u0110i\\u1ec3m danh s\\u1eefa c\\u0169ng t\\u1ef1 \\u0111\\u1ed9ng \\u0111\\u01b0\\u1ee3c \\u0111\\u1ed3ng b\\u1ed9 k\\u1ebft qu\\u1ea3!\\r\\n\\r\\n3. Ch\\u1ecdn kho\\u1ea3n thu theo ng\\u00e0y \\u0111\\u1ec3 \\u0111i\\u1ec3m danh n\\u1ebfu kh\\u00e1c, v\\u00ed d\\u1ee5: H\\u1ecdc sinh \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n tr\\u01b0a, nh\\u01b0ng kh\\u00f4ng u\\u1ed1ng s\\u1eefa...\\r\\n\\r\\n4. K\\u1ebft qu\\u1ea3 \\u0110i\\u1ec3m danh kho\\u1ea3n thu \\u0111\\u01b0\\u1ee3c t\\u1ef1 \\u0111\\u1ed9ng k\\u1ebft n\\u1ed1i v\\u1edbi Thu thanh to\\u00e1n \\u0111\\u1ec3 t\\u00ednh phi\\u1ebfu t\\u1ed3n cu\\u1ed1i th\\u00e1ng, ho\\u1eb7c xu\\u1ea5t ra c\\u00e1c b\\u00e1o c\\u00e1o b\\u00ean Theo d\\u00f5i h\\u1ecdc sinh!\\r\\n\\r\\n5. B\\u1ea1n c\\u00f3 th\\u1ec3 b\\u1ecf qua th\\u00f4ng b\\u00e1o n\\u00e0y, n\\u1ebfu ko \\u0110i\\u1ec3m danh t\\u1eebng kho\\u1ea3n thu theo ng\\u00e0y! C\\u00e1c nghi\\u1ec7p v\\u1ee5 c\\u1ee7a \\u0110i\\u1ec3m danh ch\\u00ednh & Thu thanh to\\u00e1n, S\\u1ed5 b\\u00e1o \\u0103n ko c\\u00f3 thay \\u0111\\u1ed5i g\\u00ec so v\\u1edbi tr\\u01b0\\u1edbc!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"DD_KhoanThu1_20201219222321.png\\\",\\\"DD_KhoanThu2_20201219222321.png\\\",\\\"DD_KhoanThu3_20201219222321.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-13\",\"deactive_at\":\"2020-09-13\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-13 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DD_KhoanThu2_20201219222321.png"}, {"id": "{\"id\":34,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH KHO\\u1ea2N THU: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 b\\u1ed5 sung ti\\u1ec7n \\u00edch \\u0110i\\u1ec3m danh theo kho\\u1ea3n thu ng\\u00e0y, t\\u00edch h\\u1ee3p k\\u1ebft qu\\u1ea3 n\\u00e0y v\\u1edbi Thu thanh to\\u00e1n v\\u00e0 b\\u00e1o c\\u00e1o b\\u00ean S\\u1ed5 theo d\\u00f5i h\\u1ecdc sinh! Chi ti\\u1ebft nh\\u01b0 sau:\\r\\n\\r\\n1. \\u0110i\\u1ec3m danh h\\u1ecdc sinh, \\u0111\\u1ec3 tr\\u1ed1ng kho\\u1ea3n thu s\\u1ebd l\\u00e0 \\u0110i\\u1ec3m danh ch\\u00ednh (chuy\\u00ean c\\u1ea7n + \\u0103n tr\\u01b0a) nh\\u01b0 tr\\u01b0\\u1edbc \\u0111\\u00e2y!\\r\\n\\r\\n2. H\\u1ec7 th\\u1ed1ng s\\u1ebd t\\u1ef1 \\u0111\\u1ed3ng b\\u1ed9 1 chi\\u1ec1u t\\u1eeb \\u0110i\\u1ec3m danh ch\\u00ednh sang \\u0110i\\u1ec3m danh kho\\u1ea3n thu, kh\\u00f4ng c\\u00f3 chi\\u1ec1u ng\\u01b0\\u1ee3c l\\u1ea1i. VD: \\u0110i\\u1ec3m danh ph\\u00e9p (P) S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, th\\u00ec \\u0111i\\u1ec3m danh ch\\u00ednh ko b\\u1ecb \\u1ea3nh h\\u01b0\\u1edfng. Nh\\u01b0ng \\u0111i\\u1ec3m danh ch\\u00ednh ph\\u00e9p (P) -> \\u0110i\\u1ec3m danh s\\u1eefa c\\u0169ng t\\u1ef1 \\u0111\\u1ed9ng \\u0111\\u01b0\\u1ee3c \\u0111\\u1ed3ng b\\u1ed9 k\\u1ebft qu\\u1ea3!\\r\\n\\r\\n3. Ch\\u1ecdn kho\\u1ea3n thu theo ng\\u00e0y \\u0111\\u1ec3 \\u0111i\\u1ec3m danh n\\u1ebfu kh\\u00e1c, v\\u00ed d\\u1ee5: H\\u1ecdc sinh \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n tr\\u01b0a, nh\\u01b0ng kh\\u00f4ng u\\u1ed1ng s\\u1eefa...\\r\\n\\r\\n4. K\\u1ebft qu\\u1ea3 \\u0110i\\u1ec3m danh kho\\u1ea3n thu \\u0111\\u01b0\\u1ee3c t\\u1ef1 \\u0111\\u1ed9ng k\\u1ebft n\\u1ed1i v\\u1edbi Thu thanh to\\u00e1n \\u0111\\u1ec3 t\\u00ednh phi\\u1ebfu t\\u1ed3n cu\\u1ed1i th\\u00e1ng, ho\\u1eb7c xu\\u1ea5t ra c\\u00e1c b\\u00e1o c\\u00e1o b\\u00ean Theo d\\u00f5i h\\u1ecdc sinh!\\r\\n\\r\\n5. B\\u1ea1n c\\u00f3 th\\u1ec3 b\\u1ecf qua th\\u00f4ng b\\u00e1o n\\u00e0y, n\\u1ebfu ko \\u0110i\\u1ec3m danh t\\u1eebng kho\\u1ea3n thu theo ng\\u00e0y! C\\u00e1c nghi\\u1ec7p v\\u1ee5 c\\u1ee7a \\u0110i\\u1ec3m danh ch\\u00ednh & Thu thanh to\\u00e1n, S\\u1ed5 b\\u00e1o \\u0103n ko c\\u00f3 thay \\u0111\\u1ed5i g\\u00ec so v\\u1edbi tr\\u01b0\\u1edbc!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"DD_KhoanThu1_20201219222321.png\\\",\\\"DD_KhoanThu2_20201219222321.png\\\",\\\"DD_KhoanThu3_20201219222321.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-09-13\",\"deactive_at\":\"2020-09-13\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-09-13 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/DD_KhoanThu3_20201219222321.png"}], "is_read": 1, "active_at": 1599930000, "created": 1599953400, "lastmodified": 1608431257}, {"id": 33, "subject": "ĐIỂM DANH HỌC SINH: <PERSON><PERSON>n mềm đã hỗ trợ điểm danh cho 1 hoặc nhiều học sinh trong 1 kho<PERSON>ng ngày trong tháng", "summary": "ĐIỂM DANH HỌC SINH: <PERSON><PERSON><PERSON> mềm đã hỗ trợ điểm danh cho 1 hoặc nhiều học sinh trong 1 khoảng ngày trong tháng:\r\nBước 1: <PERSON><PERSON><PERSON> \"Điểm danh học sinh\", theo lớp, click vào ô STT đối với các học sinh cần điểm danh chung! Nếu điểm danh cho cả lớp thì ko cần tick từng HS\r\nBước 2: V<PERSON><PERSON> tiện ích \"Điểm danh cho cả lớp\" ở nút bánh răng góc phải trang điểm danh\r\nBước 3: Tùy chọn điểm danh cho 1 số học sinh hoặc cả lớp\r\nVui lòng xem thêm trong các ảnh đính kèm (2 ảnh)", "summary_en": "DIEM DANH HOC SINH: <PERSON>an mem da ho tro diem danh cho 1 hoac nhieu hoc sinh trong 1 khoang ngay trong thang:\r\nBuoc 1: <PERSON><PERSON> \"Diem danh hoc sinh\", theo lop, click vao o STT doi voi cac hoc sinh can diem danh chung! Neu diem danh cho ca lop thi ko can tick tung HS\r\nBuoc 2: <PERSON><PERSON> tien ich \"Diem danh cho ca lop\" o nut banh rang goc phai trang diem danh\r\nBuoc 3: Tuy chon diem danh cho 1 so hoc sinh hoac ca lop\r\nVui long xem them trong cac anh dinh kem (2 anh)", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_DiemDanhCL1_20201219222213.png", "attachments": [{"id": "{\"id\":33,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111i\\u1ec3m danh cho 1 ho\\u1eb7c nhi\\u1ec1u h\\u1ecdc sinh trong 1 kho\\u1ea3ng ng\\u00e0y trong th\\u00e1ng\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111i\\u1ec3m danh cho 1 ho\\u1eb7c nhi\\u1ec1u h\\u1ecdc sinh trong 1 kho\\u1ea3ng ng\\u00e0y trong th\\u00e1ng:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"\\u0110i\\u1ec3m danh h\\u1ecdc sinh\\\", theo l\\u1edbp, click v\\u00e0o \\u00f4 STT \\u0111\\u1ed1i v\\u1edbi c\\u00e1c h\\u1ecdc sinh c\\u1ea7n \\u0111i\\u1ec3m danh chung! N\\u1ebfu \\u0111i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp th\\u00ec ko c\\u1ea7n tick t\\u1eebng HS\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o ti\\u1ec7n \\u00edch \\\"\\u0110i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp\\\" \\u1edf n\\u00fat b\\u00e1nh r\\u0103ng g\\u00f3c ph\\u1ea3i trang \\u0111i\\u1ec3m danh\\r\\nB\\u01b0\\u1edbc 3: T\\u00f9y ch\\u1ecdn \\u0111i\\u1ec3m danh cho 1 s\\u1ed1 h\\u1ecdc sinh ho\\u1eb7c c\\u1ea3 l\\u1edbp\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (2 \\u1ea3nh)\",\"attachments\":\"[\\\"VT_DiemDanhCL1_20201219222213.png\\\",\\\"VT_DiemDanhCL2_20201219222213.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-06\",\"deactive_at\":\"2020-08-06\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-06 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_DiemDanhCL1_20201219222213.png"}, {"id": "{\"id\":33,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111i\\u1ec3m danh cho 1 ho\\u1eb7c nhi\\u1ec1u h\\u1ecdc sinh trong 1 kho\\u1ea3ng ng\\u00e0y trong th\\u00e1ng\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111i\\u1ec3m danh cho 1 ho\\u1eb7c nhi\\u1ec1u h\\u1ecdc sinh trong 1 kho\\u1ea3ng ng\\u00e0y trong th\\u00e1ng:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"\\u0110i\\u1ec3m danh h\\u1ecdc sinh\\\", theo l\\u1edbp, click v\\u00e0o \\u00f4 STT \\u0111\\u1ed1i v\\u1edbi c\\u00e1c h\\u1ecdc sinh c\\u1ea7n \\u0111i\\u1ec3m danh chung! N\\u1ebfu \\u0111i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp th\\u00ec ko c\\u1ea7n tick t\\u1eebng HS\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o ti\\u1ec7n \\u00edch \\\"\\u0110i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp\\\" \\u1edf n\\u00fat b\\u00e1nh r\\u0103ng g\\u00f3c ph\\u1ea3i trang \\u0111i\\u1ec3m danh\\r\\nB\\u01b0\\u1edbc 3: T\\u00f9y ch\\u1ecdn \\u0111i\\u1ec3m danh cho 1 s\\u1ed1 h\\u1ecdc sinh ho\\u1eb7c c\\u1ea3 l\\u1edbp\\r\\nVui l\\u00f2ng xem th\\u00eam trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (2 \\u1ea3nh)\",\"attachments\":\"[\\\"VT_DiemDanhCL1_20201219222213.png\\\",\\\"VT_DiemDanhCL2_20201219222213.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-06\",\"deactive_at\":\"2020-08-06\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-06 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_DiemDanhCL2_20201219222213.png"}], "is_read": 1, "active_at": 1596646800, "created": 1596670200, "lastmodified": 1608431257}, {"id": 32, "subject": "THỰC PHẨM TRƯỜNG: <PERSON><PERSON>n mềm đã hỗ trợ tiện ích giá theo ngày của các thực phẩm đi chợ", "summary": "THỰC PHẨM TRƯỜNG: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích giá theo ngày của các thực phẩm đi chợ:\r\nBước 1: <PERSON><PERSON><PERSON> \"Danh mục\" -> \"Thực phẩm trường\" để cập nhật giá theo ngày trực tiếp trên từng thực phẩm đi chợ hoặc up từ file excel mẫu!\r\nBước 2: <PERSON><PERSON><PERSON> \"Cân đối khẩu phần\" làm thực đơn như bình thường, gi<PERSON> các thực phẩm đi chợ sẽ áp dụng giá theo ngày (nếu có)\r\nBước 3: Nh<PERSON>n nút \"Lưu\" ở Cân đối khẩu phần để lưu lại các thay đổi!\r\nLƯU Ý:\r\n- Ko áp dụng cho các Cân đối khẩu phần có ngày đã lưu Sổ tính tiền ăn!\r\n- Nếu đã áp dụng giá theo ngày thì ko thể sửa lại được giá ở CĐKP, hãy quay về TP trường để xóa/sửa giá hoặc bỏ đi nếu muốn tùy ý sửa như cũ!\r\n\r\nXin vui lòng xem hướng dẫn trong các ảnh đính kèm (5 ảnh)", "summary_en": "THUC PHAM TRUONG: <PERSON><PERSON> mem da ho tro tien ich gia theo ngay cua cac thuc pham di cho:\r\nBuoc 1: <PERSON><PERSON> \"Danh muc\" -> \"Thuc pham truong\" de cap nhat gia theo ngay truc tiep tren tung thuc pham di cho hoac up tu file excel mau!\r\nBuoc 2: <PERSON><PERSON> \"Can doi khau phan\" lam thuc don nhu binh thuong, gia cac thuc pham di cho se ap dung gia theo ngay (neu co)\r\nBuoc 3: Nhan nut \"Luu\" o Can doi khau phan de luu lai cac thay doi!\r\nLUU Y:\r\n- Ko ap dung cho cac Can doi khau phan co ngay da luu So tinh tien an!\r\n- Neu da ap dung gia theo ngay thi ko the sua lai duoc gia o CDKP, hay quay ve TP truong de xoa/sua gia hoac bo di neu muon tuy y sua nhu cu!\r\n\r\nXin vui long xem huong dan trong cac anh dinh kem (5 anh)", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia1_20201219222056.png", "attachments": [{"id": "{\"id\":32,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt gi\\u00e1 theo ng\\u00e0y tr\\u1ef1c ti\\u1ebfp tr\\u00ean t\\u1eebng th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 ho\\u1eb7c up t\\u1eeb file excel m\\u1eabu!\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\" l\\u00e0m th\\u1ef1c \\u0111\\u01a1n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, gi\\u00e1 c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 s\\u1ebd \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y (n\\u1ebfu c\\u00f3)\\r\\nB\\u01b0\\u1edbc 3: Nh\\u1ea5n n\\u00fat \\\"L\\u01b0u\\\" \\u1edf C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n \\u0111\\u1ec3 l\\u01b0u l\\u1ea1i c\\u00e1c thay \\u0111\\u1ed5i!\\r\\nL\\u01afU \\u00dd:\\r\\n- Ko \\u00e1p d\\u1ee5ng cho c\\u00e1c C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n c\\u00f3 ng\\u00e0y \\u0111\\u00e3 l\\u01b0u S\\u1ed5 t\\u00ednh ti\\u1ec1n \\u0103n!\\r\\n- N\\u1ebfu \\u0111\\u00e3 \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y th\\u00ec ko th\\u1ec3 s\\u1eeda l\\u1ea1i \\u0111\\u01b0\\u1ee3c gi\\u00e1 \\u1edf C\\u0110KP, h\\u00e3y quay v\\u1ec1 TP tr\\u01b0\\u1eddng \\u0111\\u1ec3 x\\u00f3a\\/s\\u1eeda gi\\u00e1 ho\\u1eb7c b\\u1ecf \\u0111i n\\u1ebfu mu\\u1ed1n t\\u00f9y \\u00fd s\\u1eeda nh\\u01b0 c\\u0169!\\r\\n\\r\\nXin vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (5 \\u1ea3nh)\",\"attachments\":\"[\\\"TPTruong_Gia1_20201219222056.png\\\",\\\"TPTruong_Gia2_20201219222056.png\\\",\\\"TPTruong_Gia3_20201219222056.png\\\",\\\"TPTruong_Gia4_20201219222056.png\\\",\\\"TPTruong_Gia5_20201219222056.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-05\",\"deactive_at\":\"2020-08-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-05 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia1_20201219222056.png"}, {"id": "{\"id\":32,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt gi\\u00e1 theo ng\\u00e0y tr\\u1ef1c ti\\u1ebfp tr\\u00ean t\\u1eebng th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 ho\\u1eb7c up t\\u1eeb file excel m\\u1eabu!\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\" l\\u00e0m th\\u1ef1c \\u0111\\u01a1n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, gi\\u00e1 c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 s\\u1ebd \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y (n\\u1ebfu c\\u00f3)\\r\\nB\\u01b0\\u1edbc 3: Nh\\u1ea5n n\\u00fat \\\"L\\u01b0u\\\" \\u1edf C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n \\u0111\\u1ec3 l\\u01b0u l\\u1ea1i c\\u00e1c thay \\u0111\\u1ed5i!\\r\\nL\\u01afU \\u00dd:\\r\\n- Ko \\u00e1p d\\u1ee5ng cho c\\u00e1c C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n c\\u00f3 ng\\u00e0y \\u0111\\u00e3 l\\u01b0u S\\u1ed5 t\\u00ednh ti\\u1ec1n \\u0103n!\\r\\n- N\\u1ebfu \\u0111\\u00e3 \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y th\\u00ec ko th\\u1ec3 s\\u1eeda l\\u1ea1i \\u0111\\u01b0\\u1ee3c gi\\u00e1 \\u1edf C\\u0110KP, h\\u00e3y quay v\\u1ec1 TP tr\\u01b0\\u1eddng \\u0111\\u1ec3 x\\u00f3a\\/s\\u1eeda gi\\u00e1 ho\\u1eb7c b\\u1ecf \\u0111i n\\u1ebfu mu\\u1ed1n t\\u00f9y \\u00fd s\\u1eeda nh\\u01b0 c\\u0169!\\r\\n\\r\\nXin vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (5 \\u1ea3nh)\",\"attachments\":\"[\\\"TPTruong_Gia1_20201219222056.png\\\",\\\"TPTruong_Gia2_20201219222056.png\\\",\\\"TPTruong_Gia3_20201219222056.png\\\",\\\"TPTruong_Gia4_20201219222056.png\\\",\\\"TPTruong_Gia5_20201219222056.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-05\",\"deactive_at\":\"2020-08-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-05 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia2_20201219222056.png"}, {"id": "{\"id\":32,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt gi\\u00e1 theo ng\\u00e0y tr\\u1ef1c ti\\u1ebfp tr\\u00ean t\\u1eebng th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 ho\\u1eb7c up t\\u1eeb file excel m\\u1eabu!\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\" l\\u00e0m th\\u1ef1c \\u0111\\u01a1n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, gi\\u00e1 c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 s\\u1ebd \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y (n\\u1ebfu c\\u00f3)\\r\\nB\\u01b0\\u1edbc 3: Nh\\u1ea5n n\\u00fat \\\"L\\u01b0u\\\" \\u1edf C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n \\u0111\\u1ec3 l\\u01b0u l\\u1ea1i c\\u00e1c thay \\u0111\\u1ed5i!\\r\\nL\\u01afU \\u00dd:\\r\\n- Ko \\u00e1p d\\u1ee5ng cho c\\u00e1c C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n c\\u00f3 ng\\u00e0y \\u0111\\u00e3 l\\u01b0u S\\u1ed5 t\\u00ednh ti\\u1ec1n \\u0103n!\\r\\n- N\\u1ebfu \\u0111\\u00e3 \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y th\\u00ec ko th\\u1ec3 s\\u1eeda l\\u1ea1i \\u0111\\u01b0\\u1ee3c gi\\u00e1 \\u1edf C\\u0110KP, h\\u00e3y quay v\\u1ec1 TP tr\\u01b0\\u1eddng \\u0111\\u1ec3 x\\u00f3a\\/s\\u1eeda gi\\u00e1 ho\\u1eb7c b\\u1ecf \\u0111i n\\u1ebfu mu\\u1ed1n t\\u00f9y \\u00fd s\\u1eeda nh\\u01b0 c\\u0169!\\r\\n\\r\\nXin vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (5 \\u1ea3nh)\",\"attachments\":\"[\\\"TPTruong_Gia1_20201219222056.png\\\",\\\"TPTruong_Gia2_20201219222056.png\\\",\\\"TPTruong_Gia3_20201219222056.png\\\",\\\"TPTruong_Gia4_20201219222056.png\\\",\\\"TPTruong_Gia5_20201219222056.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-05\",\"deactive_at\":\"2020-08-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-05 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia3_20201219222056.png"}, {"id": "{\"id\":32,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt gi\\u00e1 theo ng\\u00e0y tr\\u1ef1c ti\\u1ebfp tr\\u00ean t\\u1eebng th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 ho\\u1eb7c up t\\u1eeb file excel m\\u1eabu!\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\" l\\u00e0m th\\u1ef1c \\u0111\\u01a1n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, gi\\u00e1 c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 s\\u1ebd \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y (n\\u1ebfu c\\u00f3)\\r\\nB\\u01b0\\u1edbc 3: Nh\\u1ea5n n\\u00fat \\\"L\\u01b0u\\\" \\u1edf C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n \\u0111\\u1ec3 l\\u01b0u l\\u1ea1i c\\u00e1c thay \\u0111\\u1ed5i!\\r\\nL\\u01afU \\u00dd:\\r\\n- Ko \\u00e1p d\\u1ee5ng cho c\\u00e1c C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n c\\u00f3 ng\\u00e0y \\u0111\\u00e3 l\\u01b0u S\\u1ed5 t\\u00ednh ti\\u1ec1n \\u0103n!\\r\\n- N\\u1ebfu \\u0111\\u00e3 \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y th\\u00ec ko th\\u1ec3 s\\u1eeda l\\u1ea1i \\u0111\\u01b0\\u1ee3c gi\\u00e1 \\u1edf C\\u0110KP, h\\u00e3y quay v\\u1ec1 TP tr\\u01b0\\u1eddng \\u0111\\u1ec3 x\\u00f3a\\/s\\u1eeda gi\\u00e1 ho\\u1eb7c b\\u1ecf \\u0111i n\\u1ebfu mu\\u1ed1n t\\u00f9y \\u00fd s\\u1eeda nh\\u01b0 c\\u0169!\\r\\n\\r\\nXin vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (5 \\u1ea3nh)\",\"attachments\":\"[\\\"TPTruong_Gia1_20201219222056.png\\\",\\\"TPTruong_Gia2_20201219222056.png\\\",\\\"TPTruong_Gia3_20201219222056.png\\\",\\\"TPTruong_Gia4_20201219222056.png\\\",\\\"TPTruong_Gia5_20201219222056.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-05\",\"deactive_at\":\"2020-08-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-05 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_3", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia4_20201219222056.png"}, {"id": "{\"id\":32,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch gi\\u00e1 theo ng\\u00e0y c\\u1ee7a c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3:\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 c\\u1eadp nh\\u1eadt gi\\u00e1 theo ng\\u00e0y tr\\u1ef1c ti\\u1ebfp tr\\u00ean t\\u1eebng th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 ho\\u1eb7c up t\\u1eeb file excel m\\u1eabu!\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\" l\\u00e0m th\\u1ef1c \\u0111\\u01a1n nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng, gi\\u00e1 c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111i ch\\u1ee3 s\\u1ebd \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y (n\\u1ebfu c\\u00f3)\\r\\nB\\u01b0\\u1edbc 3: Nh\\u1ea5n n\\u00fat \\\"L\\u01b0u\\\" \\u1edf C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n \\u0111\\u1ec3 l\\u01b0u l\\u1ea1i c\\u00e1c thay \\u0111\\u1ed5i!\\r\\nL\\u01afU \\u00dd:\\r\\n- Ko \\u00e1p d\\u1ee5ng cho c\\u00e1c C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n c\\u00f3 ng\\u00e0y \\u0111\\u00e3 l\\u01b0u S\\u1ed5 t\\u00ednh ti\\u1ec1n \\u0103n!\\r\\n- N\\u1ebfu \\u0111\\u00e3 \\u00e1p d\\u1ee5ng gi\\u00e1 theo ng\\u00e0y th\\u00ec ko th\\u1ec3 s\\u1eeda l\\u1ea1i \\u0111\\u01b0\\u1ee3c gi\\u00e1 \\u1edf C\\u0110KP, h\\u00e3y quay v\\u1ec1 TP tr\\u01b0\\u1eddng \\u0111\\u1ec3 x\\u00f3a\\/s\\u1eeda gi\\u00e1 ho\\u1eb7c b\\u1ecf \\u0111i n\\u1ebfu mu\\u1ed1n t\\u00f9y \\u00fd s\\u1eeda nh\\u01b0 c\\u0169!\\r\\n\\r\\nXin vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m (5 \\u1ea3nh)\",\"attachments\":\"[\\\"TPTruong_Gia1_20201219222056.png\\\",\\\"TPTruong_Gia2_20201219222056.png\\\",\\\"TPTruong_Gia3_20201219222056.png\\\",\\\"TPTruong_Gia4_20201219222056.png\\\",\\\"TPTruong_Gia5_20201219222056.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-08-05\",\"deactive_at\":\"2020-08-05\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-08-05 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_4", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TPTruong_Gia5_20201219222056.png"}], "is_read": 1, "active_at": 1596560400, "created": 1596583800, "lastmodified": 1608431257}, {"id": 31, "subject": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích \"Tích/bỏ tích khoản thu cho cả lớp\"", "summary": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích \"Tích/bỏ tích khoản thu cho cả lớp\" ở phần thu thanh toán! Chi tiết xin vui lòng xem trong ảnh đính kèm!", "summary_en": "THU THANH TOAN: <PERSON>an mem da ho tro tien ich \"Tich/bo tich khoan thu cho ca lop\" o phan thu thanh toan! Chi tiet xin vui long xem trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_VT_20201219221933.png", "attachments": [{"id": "{\"id\":31,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"T\\u00edch\\/b\\u1ecf t\\u00edch kho\\u1ea3n thu cho c\\u1ea3 l\\u1edbp\\\"\",\"summary\":null,\"content\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"T\\u00edch\\/b\\u1ecf t\\u00edch kho\\u1ea3n thu cho c\\u1ea3 l\\u1edbp\\\" \\u1edf ph\\u1ea7n thu thanh to\\u00e1n! Chi ti\\u1ebft xin vui l\\u00f2ng xem trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"ThuTT_VT_20201219221933.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-07-27\",\"deactive_at\":\"2020-07-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-07-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuTT_VT_20201219221933.png"}], "is_read": 1, "active_at": 1595782800, "created": 1595806200, "lastmodified": 1608431257}, {"id": 30, "subject": "THỰC PHẨM TRƯỜNG: <PERSON><PERSON> thống đã hỗ trợ tiện ích \"Ngừng kích hoạt\" các thực phẩm trường ít dùng", "summary": "THỰC PHẨM TRƯỜNG: <PERSON><PERSON> thống đã hỗ trợ tiện ích \"Ngừng kích hoạt\" các thực phẩm trường ít dùng để các trường dễ theo dõi, tập trung hơn vào các thực phẩm dùng thường xuyên!\r\n\r\nBước 1: <PERSON><PERSON><PERSON> \"Danh mục\" -> \"Thực phẩm trường\", mặc định chỉ xuất hiện các thực phẩm có trạng thái \"Kích hoạt\"\r\nBước 2: Tích chọn các thực phẩm ít dùng rồi chọn tiện ích \"Bỏ kích hoạt\" ở nút bánh xe.\r\nPhần mềm có hỗ trợ sẵn các tiện ích \"Bỏ kích hoạt tất cả\", \"Bỏ kích hoạt TP chưa từng sử dụng\"\r\n=> N<PERSON><PERSON> muốn \"<PERSON>ích hoạt\" trở lại các thực phẩm đã ngừng kích hoạt thì chọn trạng thái \"Ngừng kích hoạt\", làm tương tự như bước 1, 2\r\n\r\nLƯU Ý:\r\n- Khi ngừng kích hoạt 1 thực phẩm trường thì các Món ăn, Thực đơn mẫu, CĐKP có cũ lưu trước đó vẫn bình thường, ko mất đi thực phẩm bị ngừng kích hoạt!\r\n- Các thực phẩm ngừng kích hoạt sẽ ko được tìm thấy trong các combobox tìm thực phẩm để thêm khi tạo món ăn, thực đơn mẫu, cân đối khẩu phần!", "summary_en": "THUC PHAM TRUONG: He thong da ho tro tien ich \"Ngung kich hoat\" cac thuc pham truong it dung de cac truong de theo doi, tap trung hon vao cac thuc pham dung thuong xuyen!\r\n\r\nBuoc 1: <PERSON><PERSON> \"Danh muc\" -> \"Thuc pham truong\", mac dinh chi xuat hien cac thuc pham co trang thai \"Kich hoat\"\r\nBuoc 2: Tich chon cac thuc pham it dung roi chon tien ich \"Bo kich hoat\" o nut banh xe.\r\nPhan mem co ho tro san cac tien ich \"Bo kich hoat tat ca\", \"Bo kich hoat TP chua tung su dung\"\r\n=> Neu muon \"Kich hoat\" tro lai cac thuc pham da ngung kich hoat thi chon trang thai \"Ngung kich hoat\", lam tuong tu nhu buoc 1, 2\r\n\r\nLUU Y:\r\n- Khi ngung kich hoat 1 thuc pham truong thi cac Mon an, <PERSON><PERSON><PERSON> don mau, CDKP co cu luu truoc do van binh thuong, ko mat di thuc pham bi ngung kich hoat!\r\n- <PERSON><PERSON> thuc pham ngung kich hoat se ko duoc tim thay trong cac combobox tim thuc pham de them khi tao mon an, thuc don mau, can doi khau phan!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TP_KichHoat_VT_20201219221848.png", "attachments": [{"id": "{\"id\":30,\"unit_id\":2539,\"course_id\":null,\"title\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"Ng\\u1eebng k\\u00edch ho\\u1ea1t\\\" c\\u00e1c th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng \\u00edt d\\u00f9ng\",\"summary\":null,\"content\":\"TH\\u1ef0C PH\\u1ea8M TR\\u01af\\u1edcNG: H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"Ng\\u1eebng k\\u00edch ho\\u1ea1t\\\" c\\u00e1c th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng \\u00edt d\\u00f9ng \\u0111\\u1ec3 c\\u00e1c tr\\u01b0\\u1eddng d\\u1ec5 theo d\\u00f5i, t\\u1eadp trung h\\u01a1n v\\u00e0o c\\u00e1c th\\u1ef1c ph\\u1ea9m d\\u00f9ng th\\u01b0\\u1eddng xuy\\u00ean!\\r\\n\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c\\\" -> \\\"Th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng\\\", m\\u1eb7c \\u0111\\u1ecbnh ch\\u1ec9 xu\\u1ea5t hi\\u1ec7n c\\u00e1c th\\u1ef1c ph\\u1ea9m c\\u00f3 tr\\u1ea1ng th\\u00e1i \\\"K\\u00edch ho\\u1ea1t\\\"\\r\\nB\\u01b0\\u1edbc 2: T\\u00edch ch\\u1ecdn c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u00edt d\\u00f9ng r\\u1ed3i ch\\u1ecdn ti\\u1ec7n \\u00edch \\\"B\\u1ecf k\\u00edch ho\\u1ea1t\\\" \\u1edf n\\u00fat b\\u00e1nh xe.\\r\\nPh\\u1ea7n m\\u1ec1m c\\u00f3 h\\u1ed7 tr\\u1ee3 s\\u1eb5n c\\u00e1c ti\\u1ec7n \\u00edch \\\"B\\u1ecf k\\u00edch ho\\u1ea1t t\\u1ea5t c\\u1ea3\\\", \\\"B\\u1ecf k\\u00edch ho\\u1ea1t TP ch\\u01b0a t\\u1eebng s\\u1eed d\\u1ee5ng\\\"\\r\\n=> N\\u1ebfu mu\\u1ed1n \\\"K\\u00edch ho\\u1ea1t\\\" tr\\u1edf l\\u1ea1i c\\u00e1c th\\u1ef1c ph\\u1ea9m \\u0111\\u00e3 ng\\u1eebng k\\u00edch ho\\u1ea1t th\\u00ec ch\\u1ecdn tr\\u1ea1ng th\\u00e1i \\\"Ng\\u1eebng k\\u00edch ho\\u1ea1t\\\", l\\u00e0m t\\u01b0\\u01a1ng t\\u1ef1 nh\\u01b0 b\\u01b0\\u1edbc 1, 2\\r\\n\\r\\nL\\u01afU \\u00dd:\\r\\n- Khi ng\\u1eebng k\\u00edch ho\\u1ea1t 1 th\\u1ef1c ph\\u1ea9m tr\\u01b0\\u1eddng th\\u00ec c\\u00e1c M\\u00f3n \\u0103n, Th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, C\\u0110KP c\\u00f3 c\\u0169 l\\u01b0u tr\\u01b0\\u1edbc \\u0111\\u00f3 v\\u1eabn b\\u00ecnh th\\u01b0\\u1eddng, ko m\\u1ea5t \\u0111i th\\u1ef1c ph\\u1ea9m b\\u1ecb ng\\u1eebng k\\u00edch ho\\u1ea1t!\\r\\n- C\\u00e1c th\\u1ef1c ph\\u1ea9m ng\\u1eebng k\\u00edch ho\\u1ea1t s\\u1ebd ko \\u0111\\u01b0\\u1ee3c t\\u00ecm th\\u1ea5y trong c\\u00e1c combobox t\\u00ecm th\\u1ef1c ph\\u1ea9m \\u0111\\u1ec3 th\\u00eam khi t\\u1ea1o m\\u00f3n \\u0103n, th\\u1ef1c \\u0111\\u01a1n m\\u1eabu, c\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n!\",\"attachments\":\"[\\\"TP_KichHoat_VT_20201219221848.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-07-14\",\"deactive_at\":\"2020-07-14\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-07-14 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TP_KichHoat_VT_20201219221848.png"}], "is_read": 1, "active_at": 1594659600, "created": 1594683000, "lastmodified": 1608431257}, {"id": 29, "subject": "THIẾT LẬP KHOẢN THU THÁNG 7/2020", "summary": "THIẾT LẬP KHOẢN THU THÁNG 7/2020:\r\nDo phát sinh Covid-19 nhiều trường học chính đến 15/7/2020 và tiếp tục học hè từ 16/7/2020. <PERSON><PERSON> nhiên các khoản thu học ch<PERSON> & họ<PERSON> hè khác nhau về số lượng & giá tiền trong cùng tháng 7. Hệ thống đã hỗ trợ để các trường có thể thiết lập khoản thu & thu phí được thuận lợi nhất cho t/h đặc biệt này!\r\nVui lòng xem hướng dẫn chi tiết trong các ảnh đính kèm dưới đây!", "summary_en": "THIET LAP KHOAN THU THANG 7/2020:\r\n<PERSON> phat sinh Covid-19 nhieu truong hoc chinh den 15/7/2020 va tiep tuc hoc he tu 16/7/2020. <PERSON>y nhien cac khoan thu hoc chinh & hoc he khac nhau ve so luong & gia tien trong cung thang 7. He thong da ho tro de cac truong co the thiet lap khoan thu & thu phi duoc thuan loi nhat cho t/h dac biet nay!\r\nVui long xem huong dan chi tiet trong cac anh dinh kem duoi day!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_ThuT7_1_20201219221752.png", "attachments": [{"id": "{\"id\":29,\"unit_id\":2539,\"course_id\":null,\"title\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020\",\"summary\":null,\"content\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020:\\r\\nDo ph\\u00e1t sinh Covid-19 nhi\\u1ec1u tr\\u01b0\\u1eddng h\\u1ecdc ch\\u00ednh \\u0111\\u1ebfn 15\\/7\\/2020 v\\u00e0 ti\\u1ebfp t\\u1ee5c h\\u1ecdc h\\u00e8 t\\u1eeb 16\\/7\\/2020. Tuy nhi\\u00ean c\\u00e1c kho\\u1ea3n thu h\\u1ecdc ch\\u00ednh & h\\u1ecdc h\\u00e8 kh\\u00e1c nhau v\\u1ec1 s\\u1ed1 l\\u01b0\\u1ee3ng & gi\\u00e1 ti\\u1ec1n trong c\\u00f9ng th\\u00e1ng 7. H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 c\\u00e1c tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 thi\\u1ebft l\\u1eadp kho\\u1ea3n thu & thu ph\\u00ed \\u0111\\u01b0\\u1ee3c thu\\u1eadn l\\u1ee3i nh\\u1ea5t cho t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y!\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"VT_ThuT7_1_20201219221752.png\\\",\\\"VT_ThuT7_2_20201219221752.png\\\",\\\"VT_ThuT7_3_20201219221752.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-06-24\",\"deactive_at\":\"2020-06-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-06-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_ThuT7_1_20201219221752.png"}, {"id": "{\"id\":29,\"unit_id\":2539,\"course_id\":null,\"title\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020\",\"summary\":null,\"content\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020:\\r\\nDo ph\\u00e1t sinh Covid-19 nhi\\u1ec1u tr\\u01b0\\u1eddng h\\u1ecdc ch\\u00ednh \\u0111\\u1ebfn 15\\/7\\/2020 v\\u00e0 ti\\u1ebfp t\\u1ee5c h\\u1ecdc h\\u00e8 t\\u1eeb 16\\/7\\/2020. Tuy nhi\\u00ean c\\u00e1c kho\\u1ea3n thu h\\u1ecdc ch\\u00ednh & h\\u1ecdc h\\u00e8 kh\\u00e1c nhau v\\u1ec1 s\\u1ed1 l\\u01b0\\u1ee3ng & gi\\u00e1 ti\\u1ec1n trong c\\u00f9ng th\\u00e1ng 7. H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 c\\u00e1c tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 thi\\u1ebft l\\u1eadp kho\\u1ea3n thu & thu ph\\u00ed \\u0111\\u01b0\\u1ee3c thu\\u1eadn l\\u1ee3i nh\\u1ea5t cho t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y!\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"VT_ThuT7_1_20201219221752.png\\\",\\\"VT_ThuT7_2_20201219221752.png\\\",\\\"VT_ThuT7_3_20201219221752.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-06-24\",\"deactive_at\":\"2020-06-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-06-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_ThuT7_2_20201219221752.png"}, {"id": "{\"id\":29,\"unit_id\":2539,\"course_id\":null,\"title\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020\",\"summary\":null,\"content\":\"THI\\u1ebeT L\\u1eacP KHO\\u1ea2N THU TH\\u00c1NG 7\\/2020:\\r\\nDo ph\\u00e1t sinh Covid-19 nhi\\u1ec1u tr\\u01b0\\u1eddng h\\u1ecdc ch\\u00ednh \\u0111\\u1ebfn 15\\/7\\/2020 v\\u00e0 ti\\u1ebfp t\\u1ee5c h\\u1ecdc h\\u00e8 t\\u1eeb 16\\/7\\/2020. Tuy nhi\\u00ean c\\u00e1c kho\\u1ea3n thu h\\u1ecdc ch\\u00ednh & h\\u1ecdc h\\u00e8 kh\\u00e1c nhau v\\u1ec1 s\\u1ed1 l\\u01b0\\u1ee3ng & gi\\u00e1 ti\\u1ec1n trong c\\u00f9ng th\\u00e1ng 7. H\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 c\\u00e1c tr\\u01b0\\u1eddng c\\u00f3 th\\u1ec3 thi\\u1ebft l\\u1eadp kho\\u1ea3n thu & thu ph\\u00ed \\u0111\\u01b0\\u1ee3c thu\\u1eadn l\\u1ee3i nh\\u1ea5t cho t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y!\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"VT_ThuT7_1_20201219221752.png\\\",\\\"VT_ThuT7_2_20201219221752.png\\\",\\\"VT_ThuT7_3_20201219221752.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-06-24\",\"deactive_at\":\"2020-06-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-06-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_ThuT7_3_20201219221752.png"}], "is_read": 1, "active_at": 1592931600, "created": 1592955000, "lastmodified": 1608431257}, {"id": 28, "subject": "HỖ TRỢ PHIẾU TỒN SỮA: <PERSON><PERSON>n mềm đã hỗ trợ cập nhật phiếu tồn sữa cuối tháng", "summary": "HỖ TRỢ PHIẾU TỒN SỮA: <PERSON>ần mềm đã hỗ trợ cập nhật phiếu tồn sữa cuối tháng cho những trường hợp tồn sữa ko theo điểm danh ăn chính! VD các t/h HS có đi học, c<PERSON> ăn, nhưng ko uống sữa...\r\nBước 1: Trường vào trang Thu thanh toán -> Chọn đến HS có phiếu tồn sữa ko theo quy luật của điểm danh ăn chính!\r\nBước 2: Click vào biểu tượng \"Con mắt\" ở cột phân bổ -> G<PERSON> số phiếu tồn cuối tháng tương ứng với dòng Sữa học đường\r\nBước3: <PERSON><PERSON> thể vào \"Sổ thu thanh toán\", tích chọn \"Hiện thị thừa thiếu cuối tháng\" các khoản như Sữ<PERSON> học đườ<PERSON>, Ăn sáng để theo dõi chung cho lớp hoặc toàn trường!\r\n\r\nVui lòng xem thêm hướng dẫn chi tiết trong các ảnh đính kèm dưới đây!", "summary_en": "HO TRO PHIEU TON SUA: Phan mem da ho tro cap nhat phieu ton sua cuoi thang cho nhung truong hop ton sua ko theo diem danh an chinh! VD cac t/h HS co di hoc, co an, nhung ko uong sua...\r\nBuoc 1: <PERSON><PERSON><PERSON> vao trang Thu thanh toan -> Chon den HS co phieu ton sua ko theo quy luat cua diem danh an chinh!\r\nBuoc 2: Click vao bieu tuong \"Con mat\" o cot phan bo -> Go so phieu ton cuoi thang tuong ung voi dong Sua hoc duong\r\nBuoc3: Co the vao \"So thu thanh toan\", tich chon \"Hien thi thua thieu cuoi thang\" cac khoan nhu Sua hoc duong, An sang de theo doi chung cho lop hoac toan truong!\r\n\r\nVui long xem them huong dan chi tiet trong cac anh dinh kem duoi day!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TonSua_0VT_20201219221648.png", "attachments": [{"id": "{\"id\":28,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1ed6 TR\\u1ee2 PHI\\u1ebeU T\\u1ed2N S\\u1eeeA: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 c\\u1eadp nh\\u1eadt phi\\u1ebfu t\\u1ed3n s\\u1eefa cu\\u1ed1i th\\u00e1ng\",\"summary\":null,\"content\":\"H\\u1ed6 TR\\u1ee2 PHI\\u1ebeU T\\u1ed2N S\\u1eeeA: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 c\\u1eadp nh\\u1eadt phi\\u1ebfu t\\u1ed3n s\\u1eefa cu\\u1ed1i th\\u00e1ng cho nh\\u1eefng tr\\u01b0\\u1eddng h\\u1ee3p t\\u1ed3n s\\u1eefa ko theo \\u0111i\\u1ec3m danh \\u0103n ch\\u00ednh! VD c\\u00e1c t\\/h HS c\\u00f3 \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n, nh\\u01b0ng ko u\\u1ed1ng s\\u1eefa...\\r\\nB\\u01b0\\u1edbc 1: Tr\\u01b0\\u1eddng v\\u00e0o trang Thu thanh to\\u00e1n -> Ch\\u1ecdn \\u0111\\u1ebfn HS c\\u00f3 phi\\u1ebfu t\\u1ed3n s\\u1eefa ko theo quy lu\\u1eadt c\\u1ee7a \\u0111i\\u1ec3m danh \\u0103n ch\\u00ednh!\\r\\nB\\u01b0\\u1edbc 2: Click v\\u00e0o bi\\u1ec3u t\\u01b0\\u1ee3ng \\\"Con m\\u1eaft\\\" \\u1edf c\\u1ed9t ph\\u00e2n b\\u1ed5 -> G\\u00f5 s\\u1ed1 phi\\u1ebfu t\\u1ed3n cu\\u1ed1i th\\u00e1ng t\\u01b0\\u01a1ng \\u1ee9ng v\\u1edbi d\\u00f2ng S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\r\\nB\\u01b0\\u1edbc3: C\\u00f3 th\\u1ec3 v\\u00e0o \\\"S\\u1ed5 thu thanh to\\u00e1n\\\", t\\u00edch ch\\u1ecdn \\\"Hi\\u1ec7n th\\u1ecb th\\u1eeba thi\\u1ebfu cu\\u1ed1i th\\u00e1ng\\\" c\\u00e1c kho\\u1ea3n nh\\u01b0 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0102n s\\u00e1ng \\u0111\\u1ec3 theo d\\u00f5i chung cho l\\u1edbp ho\\u1eb7c to\\u00e0n tr\\u01b0\\u1eddng!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"TonSua_0VT_20201219221648.png\\\",\\\"TonSua_2C_20201219221648.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-06-17\",\"deactive_at\":\"2020-06-17\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-06-17 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TonSua_0VT_20201219221648.png"}, {"id": "{\"id\":28,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1ed6 TR\\u1ee2 PHI\\u1ebeU T\\u1ed2N S\\u1eeeA: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 c\\u1eadp nh\\u1eadt phi\\u1ebfu t\\u1ed3n s\\u1eefa cu\\u1ed1i th\\u00e1ng\",\"summary\":null,\"content\":\"H\\u1ed6 TR\\u1ee2 PHI\\u1ebeU T\\u1ed2N S\\u1eeeA: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 c\\u1eadp nh\\u1eadt phi\\u1ebfu t\\u1ed3n s\\u1eefa cu\\u1ed1i th\\u00e1ng cho nh\\u1eefng tr\\u01b0\\u1eddng h\\u1ee3p t\\u1ed3n s\\u1eefa ko theo \\u0111i\\u1ec3m danh \\u0103n ch\\u00ednh! VD c\\u00e1c t\\/h HS c\\u00f3 \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n, nh\\u01b0ng ko u\\u1ed1ng s\\u1eefa...\\r\\nB\\u01b0\\u1edbc 1: Tr\\u01b0\\u1eddng v\\u00e0o trang Thu thanh to\\u00e1n -> Ch\\u1ecdn \\u0111\\u1ebfn HS c\\u00f3 phi\\u1ebfu t\\u1ed3n s\\u1eefa ko theo quy lu\\u1eadt c\\u1ee7a \\u0111i\\u1ec3m danh \\u0103n ch\\u00ednh!\\r\\nB\\u01b0\\u1edbc 2: Click v\\u00e0o bi\\u1ec3u t\\u01b0\\u1ee3ng \\\"Con m\\u1eaft\\\" \\u1edf c\\u1ed9t ph\\u00e2n b\\u1ed5 -> G\\u00f5 s\\u1ed1 phi\\u1ebfu t\\u1ed3n cu\\u1ed1i th\\u00e1ng t\\u01b0\\u01a1ng \\u1ee9ng v\\u1edbi d\\u00f2ng S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\r\\nB\\u01b0\\u1edbc3: C\\u00f3 th\\u1ec3 v\\u00e0o \\\"S\\u1ed5 thu thanh to\\u00e1n\\\", t\\u00edch ch\\u1ecdn \\\"Hi\\u1ec7n th\\u1ecb th\\u1eeba thi\\u1ebfu cu\\u1ed1i th\\u00e1ng\\\" c\\u00e1c kho\\u1ea3n nh\\u01b0 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0102n s\\u00e1ng \\u0111\\u1ec3 theo d\\u00f5i chung cho l\\u1edbp ho\\u1eb7c to\\u00e0n tr\\u01b0\\u1eddng!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"TonSua_0VT_20201219221648.png\\\",\\\"TonSua_2C_20201219221648.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-06-17\",\"deactive_at\":\"2020-06-17\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-06-17 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TonSua_2C_20201219221648.png"}], "is_read": 1, "active_at": 1592326800, "created": 1592350200, "lastmodified": 1608431257}, {"id": 27, "subject": "HƯỚNG DẪN CHUYỂN NĂM HỌC:", "summary": "HƯỚNG DẪN CHUYỂN NĂM HỌC:\r\n<PERSON><PERSON><PERSON> với những trường có tháng bắt đầu là tháng 6, đ<PERSON> tiế<PERSON> tụ<PERSON> làm <PERSON>h<PERSON> đơ<PERSON>, <PERSON><PERSON> đối khẩu phần trong tháng 6/2020, quý Thầy cô vui lòng chuyển năm học để thực hiện tiếp!\r\n\r\nVới những trường làm danh sách <PERSON>, <PERSON><PERSON> <PERSON> từ năm học 2019-2020, để ko bị gián đoạn công việc, quý Thầy cô vui lòng thực hiện chức năng lên lớp cho Học sinh!\r\n\r\nQuý Thầy cô vui lòng Click vào các ảnh phía dưới để xem hướng dẫn chi tiết. Những trường nào có tháng bắt đầu khác tháng 6 (<PERSON><PERSON> tháng 8,9) có thể bỏ qua thông báo này!", "summary_en": "HUONG DAN CHUYEN NAM HOC:\r\n<PERSON><PERSON> voi nhung truong co thang bat dau la thang 6, de tiep tuc lam Thuc don, Can doi khau phan trong thang 6/2020, quy Thay co vui long chuyen nam hoc de thuc hien tiep!\r\n\r\nVoi nhung truong lam danh sach Hoc sinh, <PERSON><PERSON> <PERSON> tu nam hoc 2019-2020, de ko bi gian doan cong viec, quy Thay co vui long thuc hien chuc nang len lop cho Hoc sinh!\r\n\r\nQuy Thay co vui long Click vao cac anh phia duoi de xem huong dan chi tiet. Nhung truong nao co thang bat dau khac thang 6 (VD thang 8,9) co the bo qua thong bao nay!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_NLenLop_20201219221537.png", "attachments": [{"id": "{\"id\":27,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN CHUY\\u1ec2N N\\u0102M H\\u1eccC:\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN CHUY\\u1ec2N N\\u0102M H\\u1eccC:\\r\\n\\u0110\\u1ed1i v\\u1edbi nh\\u1eefng tr\\u01b0\\u1eddng c\\u00f3 th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u l\\u00e0 th\\u00e1ng 6, \\u0111\\u1ec3 ti\\u1ebfp t\\u1ee5c l\\u00e0m Th\\u1ef1c \\u0111\\u01a1n, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n trong th\\u00e1ng 6\\/2020, qu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng chuy\\u1ec3n n\\u0103m h\\u1ecdc \\u0111\\u1ec3 th\\u1ef1c hi\\u1ec7n ti\\u1ebfp!\\r\\n\\r\\nV\\u1edbi nh\\u1eefng tr\\u01b0\\u1eddng l\\u00e0m danh s\\u00e1ch H\\u1ecdc sinh, Thu Chi t\\u1eeb n\\u0103m h\\u1ecdc 2019-2020, \\u0111\\u1ec3 ko b\\u1ecb gi\\u00e1n \\u0111o\\u1ea1n c\\u00f4ng vi\\u1ec7c, qu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng th\\u1ef1c hi\\u1ec7n ch\\u1ee9c n\\u0103ng l\\u00ean l\\u1edbp cho H\\u1ecdc sinh!\\r\\n\\r\\nQu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng Click v\\u00e0o c\\u00e1c \\u1ea3nh ph\\u00eda d\\u01b0\\u1edbi \\u0111\\u1ec3 xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft. Nh\\u1eefng tr\\u01b0\\u1eddng n\\u00e0o c\\u00f3 th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u kh\\u00e1c th\\u00e1ng 6 (VD th\\u00e1ng 8,9) c\\u00f3 th\\u1ec3 b\\u1ecf qua th\\u00f4ng b\\u00e1o n\\u00e0y!\",\"attachments\":\"[\\\"VT_NamHoc_20201219221537.png\\\",\\\"VT_NLenLop_20201219221537.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-31\",\"deactive_at\":\"2020-05-31\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-31 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_NLenLop_20201219221537.png"}, {"id": "{\"id\":27,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN CHUY\\u1ec2N N\\u0102M H\\u1eccC:\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN CHUY\\u1ec2N N\\u0102M H\\u1eccC:\\r\\n\\u0110\\u1ed1i v\\u1edbi nh\\u1eefng tr\\u01b0\\u1eddng c\\u00f3 th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u l\\u00e0 th\\u00e1ng 6, \\u0111\\u1ec3 ti\\u1ebfp t\\u1ee5c l\\u00e0m Th\\u1ef1c \\u0111\\u01a1n, C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n trong th\\u00e1ng 6\\/2020, qu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng chuy\\u1ec3n n\\u0103m h\\u1ecdc \\u0111\\u1ec3 th\\u1ef1c hi\\u1ec7n ti\\u1ebfp!\\r\\n\\r\\nV\\u1edbi nh\\u1eefng tr\\u01b0\\u1eddng l\\u00e0m danh s\\u00e1ch H\\u1ecdc sinh, Thu Chi t\\u1eeb n\\u0103m h\\u1ecdc 2019-2020, \\u0111\\u1ec3 ko b\\u1ecb gi\\u00e1n \\u0111o\\u1ea1n c\\u00f4ng vi\\u1ec7c, qu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng th\\u1ef1c hi\\u1ec7n ch\\u1ee9c n\\u0103ng l\\u00ean l\\u1edbp cho H\\u1ecdc sinh!\\r\\n\\r\\nQu\\u00fd Th\\u1ea7y c\\u00f4 vui l\\u00f2ng Click v\\u00e0o c\\u00e1c \\u1ea3nh ph\\u00eda d\\u01b0\\u1edbi \\u0111\\u1ec3 xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft. Nh\\u1eefng tr\\u01b0\\u1eddng n\\u00e0o c\\u00f3 th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u kh\\u00e1c th\\u00e1ng 6 (VD th\\u00e1ng 8,9) c\\u00f3 th\\u1ec3 b\\u1ecf qua th\\u00f4ng b\\u00e1o n\\u00e0y!\",\"attachments\":\"[\\\"VT_NamHoc_20201219221537.png\\\",\\\"VT_NLenLop_20201219221537.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-31\",\"deactive_at\":\"2020-05-31\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-31 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/VT_NamHoc_20201219221537.png"}], "is_read": 1, "active_at": 1590858000, "created": 1590881400, "lastmodified": 1608431257}, {"id": 26, "subject": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích thiết lập \"<PERSON><PERSON><PERSON> đóng tiền mặc định\"", "summary": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích thiết lập \"<PERSON><PERSON><PERSON> đóng tiền mặc định\" đối với các học sinh chưa đóng tiền & chưa có sổ thu tháng sau! Vui lòng xem thêm hướng dẫn chi tiết trong ảnh đính kèm!", "summary_en": "THU THANH TOAN: <PERSON><PERSON> mem da ho tro tien ich thiet lap \"Ngay dong tien mac dinh\" doi voi cac hoc sinh chua dong tien & chua co so thu thang sau! Vui long xem them huong dan chi tiet trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NgayDT_VT_20201219221428.png", "attachments": [{"id": "{\"id\":26,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch thi\\u1ebft l\\u1eadp \\\"Ng\\u00e0y \\u0111\\u00f3ng ti\\u1ec1n m\\u1eb7c \\u0111\\u1ecbnh\\\"\",\"summary\":null,\"content\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch thi\\u1ebft l\\u1eadp \\\"Ng\\u00e0y \\u0111\\u00f3ng ti\\u1ec1n m\\u1eb7c \\u0111\\u1ecbnh\\\" \\u0111\\u1ed1i v\\u1edbi c\\u00e1c h\\u1ecdc sinh ch\\u01b0a \\u0111\\u00f3ng ti\\u1ec1n & ch\\u01b0a c\\u00f3 s\\u1ed5 thu th\\u00e1ng sau! Vui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"NgayDT_VT_20201219221428.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-26\",\"deactive_at\":\"2020-05-26\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-26 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NgayDT_VT_20201219221428.png"}], "is_read": 1, "active_at": 1590426000, "created": 1590449400, "lastmodified": 1608431257}, {"id": 25, "subject": "HƯỚNG DẪN XỬ LÝ PHÁT SINH THU CHI THÁNG 1,2,3,4,5/2020 DO COVID-19 & LÊN LỚP CHO HS NĂM HỌC 2020-2021:", "summary": "HƯỚNG DẪN XỬ LÝ PHÁT SINH THU CHI THÁNG 1,2,3,4,5/2020 DO COVID-19 & LÊN LỚP CHO HS NĂM HỌC 2020-2021:\r\n- M<PERSON>t số trường đang có thiết lập tháng bắt đầu là tháng 6. Do vậy sẽ phải thực hiện lên lớp vào cuối tháng 5/2020 cho HS để điểm danh, thu chi vào các tháng 6,7,8/2020. <PERSON>ứ<PERSON> năng này hệ thống đã cung cấp từ các năm học trước, tuy nhiên thông báo để 1 số trường mới tiện sử dụng!\r\n- <PERSON><PERSON> lòng xem hướng dẫn trong ảnh đính kèm, cũng như các lưu ý về thu chi trong tháng 1,2,3,4,5/2020 do phát sinh Covid-19!", "summary_en": "HUONG DAN XU LY PHAT SINH THU CHI THANG 1,2,3,4,5/2020 DO COVID-19 & LEN LOP CHO HS NAM HOC 2020-2021:\r\n- Mot so truong dang co thiet lap thang bat dau la thang 6. Do vay se phai thuc hien len lop vao cuoi thang 5/2020 cho HS de diem danh, thu chi vao cac thang 6,7,8/2020. Chuc nang nay he thong da cung cap tu cac nam hoc truoc, tuy nhien thong bao de 1 so truong moi tien su dung!\r\n- <PERSON>ui long xem huong dan trong anh dinh kem, cung nhu cac luu y ve thu chi trong thang 1,2,3,4,5/2020 do phat sinh Covid-19!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLopT6A_20201219221258.png", "attachments": [{"id": "{\"id\":25,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\\r\\n- M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng \\u0111ang c\\u00f3 thi\\u1ebft l\\u1eadp th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u l\\u00e0 th\\u00e1ng 6. Do v\\u1eady s\\u1ebd ph\\u1ea3i th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp v\\u00e0o cu\\u1ed1i th\\u00e1ng 5\\/2020 cho HS \\u0111\\u1ec3 \\u0111i\\u1ec3m danh, thu chi v\\u00e0o c\\u00e1c th\\u00e1ng 6,7,8\\/2020. Ch\\u1ee9c n\\u0103ng n\\u00e0y h\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 cung c\\u1ea5p t\\u1eeb c\\u00e1c n\\u0103m h\\u1ecdc tr\\u01b0\\u1edbc, tuy nhi\\u00ean th\\u00f4ng b\\u00e1o \\u0111\\u1ec3 1 s\\u1ed1 tr\\u01b0\\u1eddng m\\u1edbi ti\\u1ec7n s\\u1eed d\\u1ee5ng!\\r\\n- Vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m, c\\u0169ng nh\\u01b0 c\\u00e1c l\\u01b0u \\u00fd v\\u1ec1 thu chi trong th\\u00e1ng 1,2,3,4,5\\/2020 do ph\\u00e1t sinh Covid-19!\",\"attachments\":\"[\\\"LenLopT6A_20201219221258.png\\\",\\\"LenLopT6B_20201219221258.png\\\",\\\"LenLopT6C_20201219221258.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-08\",\"deactive_at\":\"2020-05-08\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-08 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLopT6A_20201219221258.png"}, {"id": "{\"id\":25,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\\r\\n- M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng \\u0111ang c\\u00f3 thi\\u1ebft l\\u1eadp th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u l\\u00e0 th\\u00e1ng 6. Do v\\u1eady s\\u1ebd ph\\u1ea3i th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp v\\u00e0o cu\\u1ed1i th\\u00e1ng 5\\/2020 cho HS \\u0111\\u1ec3 \\u0111i\\u1ec3m danh, thu chi v\\u00e0o c\\u00e1c th\\u00e1ng 6,7,8\\/2020. Ch\\u1ee9c n\\u0103ng n\\u00e0y h\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 cung c\\u1ea5p t\\u1eeb c\\u00e1c n\\u0103m h\\u1ecdc tr\\u01b0\\u1edbc, tuy nhi\\u00ean th\\u00f4ng b\\u00e1o \\u0111\\u1ec3 1 s\\u1ed1 tr\\u01b0\\u1eddng m\\u1edbi ti\\u1ec7n s\\u1eed d\\u1ee5ng!\\r\\n- Vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m, c\\u0169ng nh\\u01b0 c\\u00e1c l\\u01b0u \\u00fd v\\u1ec1 thu chi trong th\\u00e1ng 1,2,3,4,5\\/2020 do ph\\u00e1t sinh Covid-19!\",\"attachments\":\"[\\\"LenLopT6A_20201219221258.png\\\",\\\"LenLopT6B_20201219221258.png\\\",\\\"LenLopT6C_20201219221258.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-08\",\"deactive_at\":\"2020-05-08\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-08 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLopT6B_20201219221258.png"}, {"id": "{\"id\":25,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\",\"summary\":null,\"content\":\"H\\u01af\\u1edaNG D\\u1eaaN X\\u1eec L\\u00dd PH\\u00c1T SINH THU CHI TH\\u00c1NG 1,2,3,4,5\\/2020 DO COVID-19 & L\\u00caN L\\u1edaP CHO HS N\\u0102M H\\u1eccC 2020-2021:\\r\\n- M\\u1ed9t s\\u1ed1 tr\\u01b0\\u1eddng \\u0111ang c\\u00f3 thi\\u1ebft l\\u1eadp th\\u00e1ng b\\u1eaft \\u0111\\u1ea7u l\\u00e0 th\\u00e1ng 6. Do v\\u1eady s\\u1ebd ph\\u1ea3i th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp v\\u00e0o cu\\u1ed1i th\\u00e1ng 5\\/2020 cho HS \\u0111\\u1ec3 \\u0111i\\u1ec3m danh, thu chi v\\u00e0o c\\u00e1c th\\u00e1ng 6,7,8\\/2020. Ch\\u1ee9c n\\u0103ng n\\u00e0y h\\u1ec7 th\\u1ed1ng \\u0111\\u00e3 cung c\\u1ea5p t\\u1eeb c\\u00e1c n\\u0103m h\\u1ecdc tr\\u01b0\\u1edbc, tuy nhi\\u00ean th\\u00f4ng b\\u00e1o \\u0111\\u1ec3 1 s\\u1ed1 tr\\u01b0\\u1eddng m\\u1edbi ti\\u1ec7n s\\u1eed d\\u1ee5ng!\\r\\n- Vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m, c\\u0169ng nh\\u01b0 c\\u00e1c l\\u01b0u \\u00fd v\\u1ec1 thu chi trong th\\u00e1ng 1,2,3,4,5\\/2020 do ph\\u00e1t sinh Covid-19!\",\"attachments\":\"[\\\"LenLopT6A_20201219221258.png\\\",\\\"LenLopT6B_20201219221258.png\\\",\\\"LenLopT6C_20201219221258.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-05-08\",\"deactive_at\":\"2020-05-08\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-05-08 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLopT6C_20201219221258.png"}], "is_read": 1, "active_at": 1588870800, "created": 1588894200, "lastmodified": 1608431257}, {"id": 24, "subject": "CÂN ĐỐI KHẨU PHẦN: <PERSON><PERSON><PERSON> mềm đã cập nhật vị trí của nút \"Chức năng - Tùy chỉnh\"", "summary": "CÂN ĐỐI KHẨU PHẦN: <PERSON><PERSON><PERSON> mềm đã cập nhật vị trí của nút \"Chức năng - Tùy chỉnh\" trong trang cân đối khẩu phần! <PERSON><PERSON><PERSON> chức năng ko thay đổi về logic hay nghiệp vụ so với trước!\r\nVui lòng xem chi tiết trong ảnh đính kèm!", "summary_en": "CAN DOI KHAU PHAN: <PERSON>an mem da cap nhat vi tri cua nut \"Chuc nang - Tuy chinh\" trong trang can doi khau phan! Cac chuc nang ko thay doi ve logic hay nghiep vu so voi truoc!\r\nVui long xem chi tiet trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_Setting_20201219221154.png", "attachments": [{"id": "{\"id\":24,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u1ee7a n\\u00fat \\\"Ch\\u1ee9c n\\u0103ng - T\\u00f9y ch\\u1ec9nh\\\"\",\"summary\":null,\"content\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u1ee7a n\\u00fat \\\"Ch\\u1ee9c n\\u0103ng - T\\u00f9y ch\\u1ec9nh\\\" trong trang c\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n! C\\u00e1c ch\\u1ee9c n\\u0103ng ko thay \\u0111\\u1ed5i v\\u1ec1 logic hay nghi\\u1ec7p v\\u1ee5 so v\\u1edbi tr\\u01b0\\u1edbc!\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"CDKP_Setting_20201219221154.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-03-11\",\"deactive_at\":\"2020-03-11\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-03-11 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_Setting_20201219221154.png"}], "is_read": 1, "active_at": 1583859600, "created": 1583883000, "lastmodified": 1608431257}, {"id": 23, "subject": "ĐIỂM DANH HỌC SINH: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích \"Điểm danh cho cả lớp\"", "summary": "ĐIỂM DANH HỌC SINH: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện ích \"Điểm danh cho cả lớp\" để hỗ trợ các t/h phát sinh nghỉ cả lớp trong 1 ngày hoặc 1 khoảng ngày nào đó theo thực tế của nhà trường quy định!\r\nXin vui lòng xem thêm chi tiết trong ảnh đính kèm!", "summary_en": "DIEM DANH HOC SINH: <PERSON>an mem da ho tro tien ich \"Diem danh cho ca lop\" de ho tro cac t/h phat sinh nghi ca lop trong 1 ngay hoac 1 khoang ngay nao do theo thuc te cua nha truong quy dinh!\r\nXin vui long xem them chi tiet trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NghiNNgay_20201219221057.png", "attachments": [{"id": "{\"id\":23,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"\\u0110i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp\\\"\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH H\\u1eccC SINH: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch \\\"\\u0110i\\u1ec3m danh cho c\\u1ea3 l\\u1edbp\\\" \\u0111\\u1ec3 h\\u1ed7 tr\\u1ee3 c\\u00e1c t\\/h ph\\u00e1t sinh ngh\\u1ec9 c\\u1ea3 l\\u1edbp trong 1 ng\\u00e0y ho\\u1eb7c 1 kho\\u1ea3ng ng\\u00e0y n\\u00e0o \\u0111\\u00f3 theo th\\u1ef1c t\\u1ebf c\\u1ee7a nh\\u00e0 tr\\u01b0\\u1eddng quy \\u0111\\u1ecbnh!\\r\\nXin vui l\\u00f2ng xem th\\u00eam chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"NghiNNgay_20201219221057.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-02-24\",\"deactive_at\":\"2020-02-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-02-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NghiNNgay_20201219221057.png"}], "is_read": 1, "active_at": 1582477200, "created": 1582500600, "lastmodified": 1608431257}, {"id": 22, "subject": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện tích \"Tích nghỉ cả tháng cho cả lớp\"", "summary": "THU THANH TOÁN: <PERSON><PERSON><PERSON> mềm đã hỗ trợ tiện tích \"Tích nghỉ cả tháng cho cả lớp\" trong Thu thanh toán để tiết kiệm thời gian cho nhà trường, ph<PERSON>t sinh nghỉ cả tháng 2/2020 do dịch nCoV.\r\nVui lòng xem thêm vị trí của tiện ích trong ảnh đính kèm!", "summary_en": "THU THANH TOAN: <PERSON>an mem da ho tro tien tich \"Tich nghi ca thang cho ca lop\" trong Thu thanh toan de tiet kiem thoi gian cho nha truong, phat sinh nghi ca thang 2/2020 do dich nCoV.\r\nVui long xem them vi tri cua tien ich trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NghiCaThang_20201219220959.png", "attachments": [{"id": "{\"id\":22,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n t\\u00edch \\\"T\\u00edch ngh\\u1ec9 c\\u1ea3 th\\u00e1ng cho c\\u1ea3 l\\u1edbp\\\"\",\"summary\":null,\"content\":\"THU THANH TO\\u00c1N: Ph\\u1ea7n m\\u1ec1m \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n t\\u00edch \\\"T\\u00edch ngh\\u1ec9 c\\u1ea3 th\\u00e1ng cho c\\u1ea3 l\\u1edbp\\\" trong Thu thanh to\\u00e1n \\u0111\\u1ec3 ti\\u1ebft ki\\u1ec7m th\\u1eddi gian cho nh\\u00e0 tr\\u01b0\\u1eddng, ph\\u00e1t sinh ngh\\u1ec9 c\\u1ea3 th\\u00e1ng 2\\/2020 do d\\u1ecbch nCoV.\\r\\nVui l\\u00f2ng xem th\\u00eam v\\u1ecb tr\\u00ed c\\u1ee7a ti\\u1ec7n \\u00edch trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"NghiCaThang_20201219220959.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-02-24\",\"deactive_at\":\"2020-02-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-02-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/NghiCaThang_20201219220959.png"}], "is_read": 1, "active_at": 1582477200, "created": 1582500600, "lastmodified": 1608431257}, {"id": 21, "subject": "HỌC SINH - THU CHI: <PERSON><PERSON><PERSON> nhật vị trí tiện ích", "summary": "HỌC SINH - THU CHI:\r\n- DANH SÁCH HỌC SINH: Đ<PERSON> di chuyển vị trí 1 số nút \"Sửa ngày đi học\", \"Chuyể<PERSON> lớp\", \"<PERSON>h<PERSON><PERSON> học\", \"<PERSON><PERSON><PERSON> học sinh\", \"<PERSON><PERSON> học lại\" vào trong \"Tiện ích\" ở trang Danh sách học sinh.\r\n- THU THANH TOÁN: Đã di chuyển vị trí 1 số nút \"Hiện thừa thiếu tháng trước\", \"ĐVT: 1000đ\", \"Mẫu C45-BB\", \"<PERSON><PERSON>u/<PERSON><PERSON><PERSON> sổ thu\" vào trong \"Tiện ích\" ở trang thu thanh toán.\r\n\r\nLưu ý: <PERSON><PERSON><PERSON> chức năng không thay đổi về nghiệp vụ so với trước khi di chuyển vị trí!\r\nQuý thầy cô vui lòng xem thêm chi tiết ở các ảnh đính kèm!", "summary_en": "HOC SINH - THU CHI:\r\n- DANH SACH HOC SINH: <PERSON> di chuyen vi tri 1 so nut \"Sua ngay di hoc\", \"<PERSON>yen lop\", \"Thoi hoc\", \"Xoa hoc sinh\", \"Di hoc lai\" vao trong \"Tien ich\" o trang Danh sach hoc sinh.\r\n- THU THANH TOAN: Da di chuyen vi tri 1 so nut \"Hien thua thieu thang truoc\", \"DVT: 1000d\", \"Mau C45-BB\", \"Luu/Xoa so thu\" vao trong \"Tien ich\" o trang thu thanh toan.\r\n\r\nLuu y: <PERSON><PERSON> chuc nang khong thay doi ve nghiep vu so voi truoc khi di chuyen vi tri!\r\nQuy thay co vui long xem them chi tiet o cac anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/HS_TienIch_20201219220908.png", "attachments": [{"id": "{\"id\":21,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1eccC SINH - THU CHI: C\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed ti\\u1ec7n \\u00edch\",\"summary\":null,\"content\":\"H\\u1eccC SINH - THU CHI:\\r\\n- DANH S\\u00c1CH H\\u1eccC SINH: \\u0110\\u00e3 di chuy\\u1ec3n v\\u1ecb tr\\u00ed 1 s\\u1ed1 n\\u00fat \\\"S\\u1eeda ng\\u00e0y \\u0111i h\\u1ecdc\\\", \\\"Chuy\\u1ec3n l\\u1edbp\\\", \\\"Th\\u00f4i h\\u1ecdc\\\", \\\"X\\u00f3a h\\u1ecdc sinh\\\", \\\"\\u0110i h\\u1ecdc l\\u1ea1i\\\" v\\u00e0o trong \\\"Ti\\u1ec7n \\u00edch\\\" \\u1edf trang Danh s\\u00e1ch h\\u1ecdc sinh.\\r\\n- THU THANH TO\\u00c1N: \\u0110\\u00e3 di chuy\\u1ec3n v\\u1ecb tr\\u00ed 1 s\\u1ed1 n\\u00fat \\\"Hi\\u1ec7n th\\u1eeba thi\\u1ebfu th\\u00e1ng tr\\u01b0\\u1edbc\\\", \\\"\\u0110VT: 1000\\u0111\\\", \\\"M\\u1eabu C45-BB\\\", \\\"L\\u01b0u\\/X\\u00f3a s\\u1ed5 thu\\\" v\\u00e0o trong \\\"Ti\\u1ec7n \\u00edch\\\" \\u1edf trang thu thanh to\\u00e1n.\\r\\n\\r\\nL\\u01b0u \\u00fd: C\\u00e1c ch\\u1ee9c n\\u0103ng kh\\u00f4ng thay \\u0111\\u1ed5i v\\u1ec1 nghi\\u1ec7p v\\u1ee5 so v\\u1edbi tr\\u01b0\\u1edbc khi di chuy\\u1ec3n v\\u1ecb tr\\u00ed!\\r\\nQu\\u00fd th\\u1ea7y c\\u00f4 vui l\\u00f2ng xem th\\u00eam chi ti\\u1ebft \\u1edf c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"HS_TienIch_20201219220908.png\\\",\\\"TC_TienIch_20201219220908.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-02-17\",\"deactive_at\":\"2020-02-17\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-02-17 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/HS_TienIch_20201219220908.png"}, {"id": "{\"id\":21,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1eccC SINH - THU CHI: C\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed ti\\u1ec7n \\u00edch\",\"summary\":null,\"content\":\"H\\u1eccC SINH - THU CHI:\\r\\n- DANH S\\u00c1CH H\\u1eccC SINH: \\u0110\\u00e3 di chuy\\u1ec3n v\\u1ecb tr\\u00ed 1 s\\u1ed1 n\\u00fat \\\"S\\u1eeda ng\\u00e0y \\u0111i h\\u1ecdc\\\", \\\"Chuy\\u1ec3n l\\u1edbp\\\", \\\"Th\\u00f4i h\\u1ecdc\\\", \\\"X\\u00f3a h\\u1ecdc sinh\\\", \\\"\\u0110i h\\u1ecdc l\\u1ea1i\\\" v\\u00e0o trong \\\"Ti\\u1ec7n \\u00edch\\\" \\u1edf trang Danh s\\u00e1ch h\\u1ecdc sinh.\\r\\n- THU THANH TO\\u00c1N: \\u0110\\u00e3 di chuy\\u1ec3n v\\u1ecb tr\\u00ed 1 s\\u1ed1 n\\u00fat \\\"Hi\\u1ec7n th\\u1eeba thi\\u1ebfu th\\u00e1ng tr\\u01b0\\u1edbc\\\", \\\"\\u0110VT: 1000\\u0111\\\", \\\"M\\u1eabu C45-BB\\\", \\\"L\\u01b0u\\/X\\u00f3a s\\u1ed5 thu\\\" v\\u00e0o trong \\\"Ti\\u1ec7n \\u00edch\\\" \\u1edf trang thu thanh to\\u00e1n.\\r\\n\\r\\nL\\u01b0u \\u00fd: C\\u00e1c ch\\u1ee9c n\\u0103ng kh\\u00f4ng thay \\u0111\\u1ed5i v\\u1ec1 nghi\\u1ec7p v\\u1ee5 so v\\u1edbi tr\\u01b0\\u1edbc khi di chuy\\u1ec3n v\\u1ecb tr\\u00ed!\\r\\nQu\\u00fd th\\u1ea7y c\\u00f4 vui l\\u00f2ng xem th\\u00eam chi ti\\u1ebft \\u1edf c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"HS_TienIch_20201219220908.png\\\",\\\"TC_TienIch_20201219220908.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2020-02-17\",\"deactive_at\":\"2020-02-17\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2020-02-17 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TC_TienIch_20201219220908.png"}], "is_read": 1, "active_at": 1581872400, "created": 1581895800, "lastmodified": 1608431257}, {"id": 20, "subject": "ĐIỂM DANH: Đã hỗ trợ nhà trường và giáo viên tra cứu lịch sử điểm danh", "summary": "ĐIỂM DANH: Đã hỗ trợ nhà trường và giáo viên tra cứu lịch sử điểm danh. Lịch sử điểm danh của hệ thống chỉ được lưu từ ngày 15/11/2019, tính từ ngày cập nhật tiện ích này!\r\n\r\nVui lòng xem thêm hướng dẫn trong ảnh đính kèm!", "summary_en": "DIEM DANH: Da ho tro nha truong va giao vien tra cuu lich su diem danh. Lich su diem danh cua he thong chi duoc luu tu ngay 15/11/2019, tinh tu ngay cap nhat tien ich nay!\r\n\r\nVui long xem them huong dan trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LS_DiemDanh1_20201219220804.png", "attachments": [{"id": "{\"id\":20,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 nh\\u00e0 tr\\u01b0\\u1eddng v\\u00e0 gi\\u00e1o vi\\u00ean tra c\\u1ee9u l\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 nh\\u00e0 tr\\u01b0\\u1eddng v\\u00e0 gi\\u00e1o vi\\u00ean tra c\\u1ee9u l\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh. L\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh c\\u1ee7a h\\u1ec7 th\\u1ed1ng ch\\u1ec9 \\u0111\\u01b0\\u1ee3c l\\u01b0u t\\u1eeb ng\\u00e0y 15\\/11\\/2019, t\\u00ednh t\\u1eeb ng\\u00e0y c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch n\\u00e0y!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"LS_DiemDanh1_20201219220804.png\\\",\\\"LS_DiemDanh2_20201219220804.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-11-19\",\"deactive_at\":\"2019-11-19\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-11-19 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LS_DiemDanh1_20201219220804.png"}, {"id": "{\"id\":20,\"unit_id\":2539,\"course_id\":null,\"title\":\"\\u0110I\\u1ec2M DANH: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 nh\\u00e0 tr\\u01b0\\u1eddng v\\u00e0 gi\\u00e1o vi\\u00ean tra c\\u1ee9u l\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh\",\"summary\":null,\"content\":\"\\u0110I\\u1ec2M DANH: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 nh\\u00e0 tr\\u01b0\\u1eddng v\\u00e0 gi\\u00e1o vi\\u00ean tra c\\u1ee9u l\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh. L\\u1ecbch s\\u1eed \\u0111i\\u1ec3m danh c\\u1ee7a h\\u1ec7 th\\u1ed1ng ch\\u1ec9 \\u0111\\u01b0\\u1ee3c l\\u01b0u t\\u1eeb ng\\u00e0y 15\\/11\\/2019, t\\u00ednh t\\u1eeb ng\\u00e0y c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch n\\u00e0y!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"LS_DiemDanh1_20201219220804.png\\\",\\\"LS_DiemDanh2_20201219220804.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-11-19\",\"deactive_at\":\"2019-11-19\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-11-19 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LS_DiemDanh2_20201219220804.png"}], "is_read": 1, "active_at": 1574096400, "created": 1574119800, "lastmodified": 1608431257}, {"id": 19, "subject": "BIỂU MẪU THỐNG KÊ: <PERSON><PERSON> cập nhật vị trí các tiện ích trong 2 biểu PHIẾU KÊ HÀNG CHỢ và KIỂM KÊ CUỐI THÁNG", "summary": "BIỂU MẪU THỐNG KÊ: <PERSON><PERSON> cập nhật vị trí các tiện ích trong 2 biểu PHIẾU KÊ HÀNG CHỢ và KIỂM KÊ CUỐI THÁNG. Đ<PERSON>a các tiện ích vào bên trong nút bánh xe ở góc trái báo cáo!\r\n\r\nVui lòng xem chi tiết trong ảnh đính kèm!", "summary_en": "BIEU MAU THONG KE: Da cap nhat vi tri cac tien ich trong 2 bieu PHIEU KE HANG CHO va KIEM KE CUOI THANG. Dua cac tien ich vao ben trong nut banh xe o goc trai bao cao!\r\n\r\nVui long xem chi tiet trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/KKCT_BCTK_20201219220705.png", "attachments": [{"id": "{\"id\":19,\"unit_id\":2539,\"course_id\":null,\"title\":\"BI\\u1ec2U M\\u1eaaU TH\\u1ed0NG K\\u00ca: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong 2 bi\\u1ec3u PHI\\u1ebeU K\\u00ca H\\u00c0NG CH\\u1ee2 v\\u00e0 KI\\u1ec2M K\\u00ca CU\\u1ed0I TH\\u00c1NG\",\"summary\":null,\"content\":\"BI\\u1ec2U M\\u1eaaU TH\\u1ed0NG K\\u00ca: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong 2 bi\\u1ec3u PHI\\u1ebeU K\\u00ca H\\u00c0NG CH\\u1ee2 v\\u00e0 KI\\u1ec2M K\\u00ca CU\\u1ed0I TH\\u00c1NG. \\u0110\\u01b0a c\\u00e1c ti\\u1ec7n \\u00edch v\\u00e0o b\\u00ean trong n\\u00fat b\\u00e1nh xe \\u1edf g\\u00f3c tr\\u00e1i b\\u00e1o c\\u00e1o!\\r\\n\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"KKCT_BCTK_20201219220705.png\\\",\\\"PKC_Opt1_20201219220705.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-29\",\"deactive_at\":\"2019-10-29\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-29 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/KKCT_BCTK_20201219220705.png"}, {"id": "{\"id\":19,\"unit_id\":2539,\"course_id\":null,\"title\":\"BI\\u1ec2U M\\u1eaaU TH\\u1ed0NG K\\u00ca: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong 2 bi\\u1ec3u PHI\\u1ebeU K\\u00ca H\\u00c0NG CH\\u1ee2 v\\u00e0 KI\\u1ec2M K\\u00ca CU\\u1ed0I TH\\u00c1NG\",\"summary\":null,\"content\":\"BI\\u1ec2U M\\u1eaaU TH\\u1ed0NG K\\u00ca: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt v\\u1ecb tr\\u00ed c\\u00e1c ti\\u1ec7n \\u00edch trong 2 bi\\u1ec3u PHI\\u1ebeU K\\u00ca H\\u00c0NG CH\\u1ee2 v\\u00e0 KI\\u1ec2M K\\u00ca CU\\u1ed0I TH\\u00c1NG. \\u0110\\u01b0a c\\u00e1c ti\\u1ec7n \\u00edch v\\u00e0o b\\u00ean trong n\\u00fat b\\u00e1nh xe \\u1edf g\\u00f3c tr\\u00e1i b\\u00e1o c\\u00e1o!\\r\\n\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"KKCT_BCTK_20201219220705.png\\\",\\\"PKC_Opt1_20201219220705.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-29\",\"deactive_at\":\"2019-10-29\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-29 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/PKC_Opt1_20201219220705.png"}], "is_read": 1, "active_at": 1572282000, "created": 1572305400, "lastmodified": 1608431257}, {"id": 18, "subject": "CÂN ĐỐI KHẨU PHẦN: <PERSON><PERSON> <PERSON> chuyển chức năng xuất excel \"Kết quả khẩu phần dinh dưỡng\"", "summary": "CÂN ĐỐI KHẨU PHẦN: <PERSON><PERSON> <PERSON> chuyển chức năng xuất excel \"Kết quả khẩu phần dinh dưỡng\" vào trong trang xem trước! Vui lòng xem thêm trong ảnh đính kèm", "summary_en": "CAN DOI KHAU PHAN: <PERSON> <PERSON> chuyen chuc nang xuat excel \"Ket qua khau phan dinh duong\" vao trong trang xem truoc! Vui long xem them trong anh dinh kem", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_XuatExcel_20201219220557.png", "attachments": [{"id": "{\"id\":18,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: \\u0110\\u00e3 di chuy\\u1ec3n ch\\u1ee9c n\\u0103ng xu\\u1ea5t excel \\\"K\\u1ebft qu\\u1ea3 kh\\u1ea9u ph\\u1ea7n dinh d\\u01b0\\u1ee1ng\\\"\",\"summary\":null,\"content\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: \\u0110\\u00e3 di chuy\\u1ec3n ch\\u1ee9c n\\u0103ng xu\\u1ea5t excel \\\"K\\u1ebft qu\\u1ea3 kh\\u1ea9u ph\\u1ea7n dinh d\\u01b0\\u1ee1ng\\\" v\\u00e0o trong trang xem tr\\u01b0\\u1edbc! Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"CDKP_XuatExcel_20201219220557.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-24\",\"deactive_at\":\"2019-10-24\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-24 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_XuatExcel_20201219220557.png"}], "is_read": 1, "active_at": 1571850000, "created": 1571873400, "lastmodified": 1608431257}, {"id": 17, "subject": "THU CHI: Đã hỗ trợ hiển thị số phiếu thừa thiếu trong biên lai của các khoản thu theo ngày khác tiền ăn", "summary": "THU CHI: Đã hỗ trợ hiển thị số phiếu thừa thiếu trong biên lai của các khoản thu theo ngày khác tiền ăn, vd sữa học đường. Nhằm giải thích rõ hơn về số phiếu thu của HS!\r\n\r\nVui lòng xem thêm trong ảnh đính kèm!", "summary_en": "THU CHI: Da ho tro hien thi so phieu thua thieu trong bien lai cua cac khoan thu theo ngay khac tien an, vd sua hoc duong. Nham giai thich ro hon ve so phieu thu cua HS!\r\n\r\nVui long xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/BienLaiHS_20201219220505.png", "attachments": [{"id": "{\"id\":17,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 hi\\u1ec3n th\\u1ecb s\\u1ed1 phi\\u1ebfu th\\u1eeba thi\\u1ebfu trong bi\\u00ean lai c\\u1ee7a c\\u00e1c kho\\u1ea3n thu theo ng\\u00e0y kh\\u00e1c ti\\u1ec1n \\u0103n\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 hi\\u1ec3n th\\u1ecb s\\u1ed1 phi\\u1ebfu th\\u1eeba thi\\u1ebfu trong bi\\u00ean lai c\\u1ee7a c\\u00e1c kho\\u1ea3n thu theo ng\\u00e0y kh\\u00e1c ti\\u1ec1n \\u0103n, vd s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng. Nh\\u1eb1m gi\\u1ea3i th\\u00edch r\\u00f5 h\\u01a1n v\\u1ec1 s\\u1ed1 phi\\u1ebfu thu c\\u1ee7a HS!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"BienLaiHS_20201219220505.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-03\",\"deactive_at\":\"2019-10-03\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-03 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/BienLaiHS_20201219220505.png"}], "is_read": 1, "active_at": 1570035600, "created": 1570059000, "lastmodified": 1608431257}, {"id": 16, "subject": "CÂN ĐỐI KHẨU PHẦN: Đ<PERSON> hỗ trợ tiện ích hiển thị thông tin Canxi, B1", "summary": "CÂN ĐỐI KHẨU PHẦN: Đ<PERSON> hỗ trợ tiện ích hiển thị thông tin Canxi, B1 trong mục cân đối khẩu phần cho người dùng! Vui lòng xem thêm trong ảnh đính kèm!", "summary_en": "CAN DOI KHAU PHAN: Da ho tro tien ich hien thi thong tin <PERSON>, B1 trong muc can doi khau phan cho nguoi dung! Vui long xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CanxiB1_20201219220348.png", "attachments": [{"id": "{\"id\":16,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch hi\\u1ec3n th\\u1ecb th\\u00f4ng tin Canxi, B1\",\"summary\":null,\"content\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 ti\\u1ec7n \\u00edch hi\\u1ec3n th\\u1ecb th\\u00f4ng tin Canxi, B1 trong m\\u1ee5c c\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n cho ng\\u01b0\\u1eddi d\\u00f9ng! Vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"CanxiB1_20201219220348.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-03\",\"deactive_at\":\"2019-10-03\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-03 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CanxiB1_20201219220348.png"}], "is_read": 1, "active_at": 1570035600, "created": 1570059000, "lastmodified": 1608431257}, {"id": 15, "subject": "THU CHI: Đã hỗ trợ hiển thị số phiếu thừa thiếu của các khoản thu theo ngày", "summary": "THU CHI: Đã hỗ trợ hiển thị số phiếu thừa thiếu của các khoản thu theo ngày ngoài tiền ăn trong sổ thu thanh toán lớp, sổ thu thanh toán trườ<PERSON>, nhằm gi<PERSON>p trường dễ kiểm tra hơn số liệu về Sữa học đường và các khoản thu khác!\r\n\r\nVui lòng xem thêm trong ảnh đính kèm!", "summary_en": "THU CHI: Da ho tro hien thi so phieu thua thieu cua cac khoan thu theo ngay ngoai tien an trong so thu thanh toan lop, so thu thanh toan truong, nham giup truong de kiem tra hon so lieu ve Sua hoc duong va cac khoan thu khac!\r\n\r\nVui long xem them trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHD_20201219220233.png", "attachments": [{"id": "{\"id\":15,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 hi\\u1ec3n th\\u1ecb s\\u1ed1 phi\\u1ebfu th\\u1eeba thi\\u1ebfu c\\u1ee7a c\\u00e1c kho\\u1ea3n thu theo ng\\u00e0y\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 hi\\u1ec3n th\\u1ecb s\\u1ed1 phi\\u1ebfu th\\u1eeba thi\\u1ebfu c\\u1ee7a c\\u00e1c kho\\u1ea3n thu theo ng\\u00e0y ngo\\u00e0i ti\\u1ec1n \\u0103n trong s\\u1ed5 thu thanh to\\u00e1n l\\u1edbp, s\\u1ed5 thu thanh to\\u00e1n tr\\u01b0\\u1eddng, nh\\u1eb1m gi\\u00fap tr\\u01b0\\u1eddng d\\u1ec5 ki\\u1ec3m tra h\\u01a1n s\\u1ed1 li\\u1ec7u v\\u1ec1 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng v\\u00e0 c\\u00e1c kho\\u1ea3n thu kh\\u00e1c!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"SuaHD_20201219220233.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-10-03\",\"deactive_at\":\"2019-10-03\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-10-03 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHD_20201219220233.png"}], "is_read": 1, "active_at": 1570035600, "created": 1570059000, "lastmodified": 1608431257}, {"id": 14, "subject": "SỮA HỌC ĐƯỜNG T9: <PERSON><PERSON><PERSON>ng dẫn xử lý khoản thu sữa học đường tháng 9 năm học 2019", "summary": "SỮA HỌC ĐƯỜNG T9: <PERSON><PERSON><PERSON><PERSON> dẫn xử lý khoản thu sữa học đường tháng 9 năm học 2019.\r\nBài toán thực tế: Tháng 9/2019, học sinh chỉ đăng ký mua 17 phiếu sữa (t<PERSON><PERSON> từ ngày 6/9->30/9) trong khi vẫn mua 20 phiếu ăn như cấu hình ngày nghỉ!\r\n\r\nCách xử lý cho vấn đề Sữa học đường tháng 9 trên phần mềm như sau:\r\n\r\nBước 1: <PERSON><PERSON><PERSON> \"Danh mục khoản thu\", tạo thêm 1 kho<PERSON>n thu là \"Sữa học đường T9\" dành riêng cho tháng 9/2019 vì phát sinh t/h đặc biệt này\r\n\r\nBước 2: <PERSON><PERSON><PERSON> \"Thiết lập khoản thu\" chọn thiết lập khoản thu cho tháng 9/2019 sau đó chọn khoản thu \"<PERSON>ữ<PERSON> học đường T9\" (k<PERSON> chọ<PERSON> kho<PERSON> \"<PERSON><PERSON><PERSON> học đường\" b<PERSON>nh thường vẫn sử dụng) để cấu hình:\r\n- <PERSON><PERSON><PERSON>n thu \"Sữa học đường T9\" sẽ đặt là thu theo tháng\r\n- Số tiền tương ứng với số phiếu dự tính thu, vd 50218đ ~ 17 phiếu của tháng 9\r\n\r\nBước 3: Khi thiết lập khoản thu cho tháng 10/2019\r\n- Chọn lại khoản thu \"Sữa học đường\" để thiết lập như các tháng trước đây bình thường (tính theo ngày, số tiền 2954đ...)\r\n- Ko chọn \"Sữa học đường T9\" cho thiết lập khoản thu tháng 10, vì khoản này sinh ra chỉ để giải quyết bài toán sữa học đường của tháng 9\r\n\r\nLưu ý: Vẫn điểm danh HS tháng 9 như bình thường vẫn thực hiện, ở Thu thanh toán tháng 10 HS nào thừa thiếu bao nhiêu phiếu so với thực tế, nhà trường chủ động điền vào lúc làm sổ thu tháng 10!\r\n\r\nVui lòng xem hướng dẫn trong các ảnh đính kèm!", "summary_en": "SUA HOC DUONG T9: <PERSON><PERSON> dan xu ly khoan thu sua hoc duong thang 9 nam hoc 2019.\r\nBai toan thuc te: Thang 9/2019, hoc sinh chi dang ky mua 17 phieu sua (tinh tu ngay 6/9->30/9) trong khi van mua 20 phieu an nhu cau hinh ngay nghi!\r\n\r\nCach xu ly cho van de Sua hoc duong thang 9 tren phan mem nhu sau:\r\n\r\nBuoc 1: <PERSON><PERSON> \"Danh muc khoan thu\", tao them 1 khoan thu la \"Sua hoc duong T9\" danh rieng cho thang 9/2019 vi phat sinh t/h dac biet nay\r\n\r\nBuoc 2: <PERSON><PERSON> \"Thiet lap khoan thu\" chon thiet lap khoan thu cho thang 9/2019 sau do chon khoan thu \"Sua hoc duong T9\" (ko chon khoan \"Sua hoc duong\" binh thuong van su dung) de cau hinh:\r\n- <PERSON><PERSON><PERSON> thu \"Sua hoc duong T9\" se dat la thu theo thang\r\n- So tien tuong ung voi so phieu du tinh thu, vd 50218d ~ 17 phieu cua thang 9\r\n\r\nBuoc 3: <PERSON>hi thiet lap khoan thu cho thang 10/2019\r\n- Chon lai khoan thu \"Sua hoc duong\" de thiet lap nhu cac thang truoc day binh thuong (tinh theo ngay, so tien 2954d...)\r\n- Ko chon \"Sua hoc duong T9\" cho thiet lap khoan thu thang 10, vi khoan nay sinh ra chi de giai quyet bai toan sua hoc duong cua thang 9\r\n\r\nLuu y: Van diem danh HS thang 9 nhu binh thuong van thuc hien, o Thu thanh toan thang 10 HS nao thua thieu bao nhieu phieu so voi thuc te, nha truong chu dong dien vao luc lam so thu thang 10!\r\n\r\nVui long xem huong dan trong cac anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHocDuongT9_1_20201219220121.png", "attachments": [{"id": "{\"id\":14,\"unit_id\":2539,\"course_id\":null,\"title\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019\",\"summary\":null,\"content\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019.\\r\\nB\\u00e0i to\\u00e1n th\\u1ef1c t\\u1ebf: Th\\u00e1ng 9\\/2019, h\\u1ecdc sinh ch\\u1ec9 \\u0111\\u0103ng k\\u00fd mua 17 phi\\u1ebfu s\\u1eefa (t\\u00ednh t\\u1eeb ng\\u00e0y 6\\/9->30\\/9) trong khi v\\u1eabn mua 20 phi\\u1ebfu \\u0103n nh\\u01b0 c\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9!\\r\\n\\r\\nC\\u00e1ch x\\u1eed l\\u00fd cho v\\u1ea5n \\u0111\\u1ec1 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 tr\\u00ean ph\\u1ea7n m\\u1ec1m nh\\u01b0 sau:\\r\\n\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c kho\\u1ea3n thu\\\", t\\u1ea1o th\\u00eam 1 kho\\u1ea3n thu l\\u00e0 \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" d\\u00e0nh ri\\u00eang cho th\\u00e1ng 9\\/2019 v\\u00ec ph\\u00e1t sinh t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y\\r\\n\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu\\\" ch\\u1ecdn thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 9\\/2019 sau \\u0111\\u00f3 ch\\u1ecdn kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" (ko ch\\u1ecdn kho\\u1ea3n \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn s\\u1eed d\\u1ee5ng) \\u0111\\u1ec3 c\\u1ea5u h\\u00ecnh:\\r\\n- Kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" s\\u1ebd \\u0111\\u1eb7t l\\u00e0 thu theo th\\u00e1ng\\r\\n- S\\u1ed1 ti\\u1ec1n t\\u01b0\\u01a1ng \\u1ee9ng v\\u1edbi s\\u1ed1 phi\\u1ebfu d\\u1ef1 t\\u00ednh thu, vd 50218\\u0111 ~ 17 phi\\u1ebfu c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nB\\u01b0\\u1edbc 3: Khi thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 10\\/2019\\r\\n- Ch\\u1ecdn l\\u1ea1i kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 thi\\u1ebft l\\u1eadp nh\\u01b0 c\\u00e1c th\\u00e1ng tr\\u01b0\\u1edbc \\u0111\\u00e2y b\\u00ecnh th\\u01b0\\u1eddng (t\\u00ednh theo ng\\u00e0y, s\\u1ed1 ti\\u1ec1n 2954\\u0111...)\\r\\n- Ko ch\\u1ecdn \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" cho thi\\u1ebft l\\u1eadp kho\\u1ea3n thu th\\u00e1ng 10, v\\u00ec kho\\u1ea3n n\\u00e0y sinh ra ch\\u1ec9 \\u0111\\u1ec3 gi\\u1ea3i quy\\u1ebft b\\u00e0i to\\u00e1n s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nL\\u01b0u \\u00fd: V\\u1eabn \\u0111i\\u1ec3m danh HS th\\u00e1ng 9 nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn th\\u1ef1c hi\\u1ec7n, \\u1edf Thu thanh to\\u00e1n th\\u00e1ng 10 HS n\\u00e0o th\\u1eeba thi\\u1ebfu bao nhi\\u00eau phi\\u1ebfu so v\\u1edbi th\\u1ef1c t\\u1ebf, nh\\u00e0 tr\\u01b0\\u1eddng ch\\u1ee7 \\u0111\\u1ed9ng \\u0111i\\u1ec1n v\\u00e0o l\\u00fac l\\u00e0m s\\u1ed5 thu th\\u00e1ng 10!\\r\\n\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"SuaHocDuongT9_1_20201219220121.png\\\",\\\"SuaHocDuongT9_2_20201219220121.png\\\",\\\"SuaHocDuongT9_3_20201219220121.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-08-27\",\"deactive_at\":\"2019-08-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-08-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHocDuongT9_1_20201219220121.png"}, {"id": "{\"id\":14,\"unit_id\":2539,\"course_id\":null,\"title\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019\",\"summary\":null,\"content\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019.\\r\\nB\\u00e0i to\\u00e1n th\\u1ef1c t\\u1ebf: Th\\u00e1ng 9\\/2019, h\\u1ecdc sinh ch\\u1ec9 \\u0111\\u0103ng k\\u00fd mua 17 phi\\u1ebfu s\\u1eefa (t\\u00ednh t\\u1eeb ng\\u00e0y 6\\/9->30\\/9) trong khi v\\u1eabn mua 20 phi\\u1ebfu \\u0103n nh\\u01b0 c\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9!\\r\\n\\r\\nC\\u00e1ch x\\u1eed l\\u00fd cho v\\u1ea5n \\u0111\\u1ec1 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 tr\\u00ean ph\\u1ea7n m\\u1ec1m nh\\u01b0 sau:\\r\\n\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c kho\\u1ea3n thu\\\", t\\u1ea1o th\\u00eam 1 kho\\u1ea3n thu l\\u00e0 \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" d\\u00e0nh ri\\u00eang cho th\\u00e1ng 9\\/2019 v\\u00ec ph\\u00e1t sinh t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y\\r\\n\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu\\\" ch\\u1ecdn thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 9\\/2019 sau \\u0111\\u00f3 ch\\u1ecdn kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" (ko ch\\u1ecdn kho\\u1ea3n \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn s\\u1eed d\\u1ee5ng) \\u0111\\u1ec3 c\\u1ea5u h\\u00ecnh:\\r\\n- Kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" s\\u1ebd \\u0111\\u1eb7t l\\u00e0 thu theo th\\u00e1ng\\r\\n- S\\u1ed1 ti\\u1ec1n t\\u01b0\\u01a1ng \\u1ee9ng v\\u1edbi s\\u1ed1 phi\\u1ebfu d\\u1ef1 t\\u00ednh thu, vd 50218\\u0111 ~ 17 phi\\u1ebfu c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nB\\u01b0\\u1edbc 3: Khi thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 10\\/2019\\r\\n- Ch\\u1ecdn l\\u1ea1i kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 thi\\u1ebft l\\u1eadp nh\\u01b0 c\\u00e1c th\\u00e1ng tr\\u01b0\\u1edbc \\u0111\\u00e2y b\\u00ecnh th\\u01b0\\u1eddng (t\\u00ednh theo ng\\u00e0y, s\\u1ed1 ti\\u1ec1n 2954\\u0111...)\\r\\n- Ko ch\\u1ecdn \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" cho thi\\u1ebft l\\u1eadp kho\\u1ea3n thu th\\u00e1ng 10, v\\u00ec kho\\u1ea3n n\\u00e0y sinh ra ch\\u1ec9 \\u0111\\u1ec3 gi\\u1ea3i quy\\u1ebft b\\u00e0i to\\u00e1n s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nL\\u01b0u \\u00fd: V\\u1eabn \\u0111i\\u1ec3m danh HS th\\u00e1ng 9 nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn th\\u1ef1c hi\\u1ec7n, \\u1edf Thu thanh to\\u00e1n th\\u00e1ng 10 HS n\\u00e0o th\\u1eeba thi\\u1ebfu bao nhi\\u00eau phi\\u1ebfu so v\\u1edbi th\\u1ef1c t\\u1ebf, nh\\u00e0 tr\\u01b0\\u1eddng ch\\u1ee7 \\u0111\\u1ed9ng \\u0111i\\u1ec1n v\\u00e0o l\\u00fac l\\u00e0m s\\u1ed5 thu th\\u00e1ng 10!\\r\\n\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"SuaHocDuongT9_1_20201219220121.png\\\",\\\"SuaHocDuongT9_2_20201219220121.png\\\",\\\"SuaHocDuongT9_3_20201219220121.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-08-27\",\"deactive_at\":\"2019-08-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-08-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHocDuongT9_2_20201219220121.png"}, {"id": "{\"id\":14,\"unit_id\":2539,\"course_id\":null,\"title\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019\",\"summary\":null,\"content\":\"S\\u1eeeA H\\u1eccC \\u0110\\u01af\\u1edcNG T9: H\\u01b0\\u1edbng d\\u1eabn x\\u1eed l\\u00fd kho\\u1ea3n thu s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 n\\u0103m h\\u1ecdc 2019.\\r\\nB\\u00e0i to\\u00e1n th\\u1ef1c t\\u1ebf: Th\\u00e1ng 9\\/2019, h\\u1ecdc sinh ch\\u1ec9 \\u0111\\u0103ng k\\u00fd mua 17 phi\\u1ebfu s\\u1eefa (t\\u00ednh t\\u1eeb ng\\u00e0y 6\\/9->30\\/9) trong khi v\\u1eabn mua 20 phi\\u1ebfu \\u0103n nh\\u01b0 c\\u1ea5u h\\u00ecnh ng\\u00e0y ngh\\u1ec9!\\r\\n\\r\\nC\\u00e1ch x\\u1eed l\\u00fd cho v\\u1ea5n \\u0111\\u1ec1 S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng th\\u00e1ng 9 tr\\u00ean ph\\u1ea7n m\\u1ec1m nh\\u01b0 sau:\\r\\n\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o \\\"Danh m\\u1ee5c kho\\u1ea3n thu\\\", t\\u1ea1o th\\u00eam 1 kho\\u1ea3n thu l\\u00e0 \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" d\\u00e0nh ri\\u00eang cho th\\u00e1ng 9\\/2019 v\\u00ec ph\\u00e1t sinh t\\/h \\u0111\\u1eb7c bi\\u1ec7t n\\u00e0y\\r\\n\\r\\nB\\u01b0\\u1edbc 2: V\\u00e0o \\\"Thi\\u1ebft l\\u1eadp kho\\u1ea3n thu\\\" ch\\u1ecdn thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 9\\/2019 sau \\u0111\\u00f3 ch\\u1ecdn kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" (ko ch\\u1ecdn kho\\u1ea3n \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn s\\u1eed d\\u1ee5ng) \\u0111\\u1ec3 c\\u1ea5u h\\u00ecnh:\\r\\n- Kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" s\\u1ebd \\u0111\\u1eb7t l\\u00e0 thu theo th\\u00e1ng\\r\\n- S\\u1ed1 ti\\u1ec1n t\\u01b0\\u01a1ng \\u1ee9ng v\\u1edbi s\\u1ed1 phi\\u1ebfu d\\u1ef1 t\\u00ednh thu, vd 50218\\u0111 ~ 17 phi\\u1ebfu c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nB\\u01b0\\u1edbc 3: Khi thi\\u1ebft l\\u1eadp kho\\u1ea3n thu cho th\\u00e1ng 10\\/2019\\r\\n- Ch\\u1ecdn l\\u1ea1i kho\\u1ea3n thu \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" \\u0111\\u1ec3 thi\\u1ebft l\\u1eadp nh\\u01b0 c\\u00e1c th\\u00e1ng tr\\u01b0\\u1edbc \\u0111\\u00e2y b\\u00ecnh th\\u01b0\\u1eddng (t\\u00ednh theo ng\\u00e0y, s\\u1ed1 ti\\u1ec1n 2954\\u0111...)\\r\\n- Ko ch\\u1ecdn \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng T9\\\" cho thi\\u1ebft l\\u1eadp kho\\u1ea3n thu th\\u00e1ng 10, v\\u00ec kho\\u1ea3n n\\u00e0y sinh ra ch\\u1ec9 \\u0111\\u1ec3 gi\\u1ea3i quy\\u1ebft b\\u00e0i to\\u00e1n s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng c\\u1ee7a th\\u00e1ng 9\\r\\n\\r\\nL\\u01b0u \\u00fd: V\\u1eabn \\u0111i\\u1ec3m danh HS th\\u00e1ng 9 nh\\u01b0 b\\u00ecnh th\\u01b0\\u1eddng v\\u1eabn th\\u1ef1c hi\\u1ec7n, \\u1edf Thu thanh to\\u00e1n th\\u00e1ng 10 HS n\\u00e0o th\\u1eeba thi\\u1ebfu bao nhi\\u00eau phi\\u1ebfu so v\\u1edbi th\\u1ef1c t\\u1ebf, nh\\u00e0 tr\\u01b0\\u1eddng ch\\u1ee7 \\u0111\\u1ed9ng \\u0111i\\u1ec1n v\\u00e0o l\\u00fac l\\u00e0m s\\u1ed5 thu th\\u00e1ng 10!\\r\\n\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"SuaHocDuongT9_1_20201219220121.png\\\",\\\"SuaHocDuongT9_2_20201219220121.png\\\",\\\"SuaHocDuongT9_3_20201219220121.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-08-27\",\"deactive_at\":\"2019-08-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-08-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaHocDuongT9_3_20201219220121.png"}], "is_read": 1, "active_at": 1566838800, "created": 1566862200, "lastmodified": 1608431257}, {"id": 13, "subject": "QUẢN LÝ LỚP HỌC: Đã hỗ trợ chức năng ẩn hiện các lớp học hè", "summary": "QUẢN LÝ LỚP HỌC: Đã hỗ trợ chức năng ẩn hiện các lớp học hè khi nhà trường ko muốn xuất hiện các lớp này trong danh sách củ<PERSON> họ<PERSON> sin<PERSON>, danh sách điểm danh, thu chi.\r\n<PERSON>ui lòng xem hướng dẫn trong ảnh đính kèm", "summary_en": "QUAN LY LOP HOC: Da ho tro chuc nang an hien cac lop hoc he khi nha truong ko muon xuat hien cac lop nay trong danh sach cua hoc sinh, danh sach diem danh, thu chi.\r\n<PERSON>ui long xem huong dan trong anh dinh kem", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/An_Lop_HocHe_20201219220007.png", "attachments": [{"id": "{\"id\":13,\"unit_id\":2539,\"course_id\":null,\"title\":\"QU\\u1ea2N L\\u00dd L\\u1edaP H\\u1eccC: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 ch\\u1ee9c n\\u0103ng \\u1ea9n hi\\u1ec7n c\\u00e1c l\\u1edbp h\\u1ecdc h\\u00e8\",\"summary\":null,\"content\":\"QU\\u1ea2N L\\u00dd L\\u1edaP H\\u1eccC: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 ch\\u1ee9c n\\u0103ng \\u1ea9n hi\\u1ec7n c\\u00e1c l\\u1edbp h\\u1ecdc h\\u00e8 khi nh\\u00e0 tr\\u01b0\\u1eddng ko mu\\u1ed1n xu\\u1ea5t hi\\u1ec7n c\\u00e1c l\\u1edbp n\\u00e0y trong danh s\\u00e1ch c\\u1ee7a h\\u1ecdc sinh, danh s\\u00e1ch \\u0111i\\u1ec3m danh, thu chi.\\r\\nVui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"An_Lop_HocHe_20201219220007.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-08-10\",\"deactive_at\":\"2019-08-10\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-08-10 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/An_Lop_HocHe_20201219220007.png"}], "is_read": 1, "active_at": 1565370000, "created": 1565393400, "lastmodified": 1608431257}, {"id": 12, "subject": "HỌC SINH - LÊN LỚP: <PERSON><PERSON><PERSON><PERSON> dẫn thực hiện lên lớp cho học sinh từ năm học 2018-2019 lên 2019-2020!", "summary": "HỌC SINH - LÊN LỚP: <PERSON><PERSON><PERSON>ng dẫn thực hiện lên lớp cho học sinh từ năm học 2018-2019 lên 2019-2020!\r\nBước 1: <PERSON><PERSON><PERSON> <PERSON> sách học sinh, ch<PERSON><PERSON>, chọ<PERSON> lớp có học sinh cần lên lớp\r\nBước 2: <PERSON><PERSON>n 1 hoặc nhiều học sinh cần thực hiện lên lớp cho năm học 2019-2020\r\nBước 3: Tích vào checkbox \"Lên lớp học cho năm học 2019-2020\" -> <PERSON><PERSON><PERSON>hối, lớp sẽ chuyển đến của những hs này => <PERSON>h<PERSON>n nút \"Lên lớp\"\r\n\r\nSau khi thực hiện xong các bước trên, vui lòng chọn lại năm học hiện tại trên menu là năm 2019-2020 để xem kết quả đã chuyển!\r\n\r\nCác bạn vui lòng xem thêm trong ảnh hướng dẫn đính kèm nhé!", "summary_en": "HOC SINH - LEN LOP: <PERSON><PERSON> dan thuc hien len lop cho hoc sinh tu nam hoc 2018-2019 len 2019-2020!\r\nBuoc 1: <PERSON><PERSON> sach hoc sinh, chon <PERSON><PERSON>, chon lop co hoc sinh can len lop\r\nBuoc 2: Chon 1 hoac nhieu hoc sinh can thuc hien len lop cho nam hoc 2019-2020\r\nBuoc 3: Tich vao checkbox \"Len lop hoc cho nam hoc 2019-2020\" -> <PERSON><PERSON> kho<PERSON>, lop se chuyen den cua nhung hs nay => Nhan nut \"Len lop\"\r\n\r\nSau khi thuc hien xong cac buoc tren, vui long chon lai nam hoc hien tai tren menu la nam 2019-2020 de xem ket qua da chuyen!\r\n\r\nCac ban vui long xem them trong anh huong dan dinh kem nhe!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLop_HS_20201219215831.png", "attachments": [{"id": "{\"id\":12,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1eccC SINH - L\\u00caN L\\u1edaP: H\\u01b0\\u1edbng d\\u1eabn th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp cho h\\u1ecdc sinh t\\u1eeb n\\u0103m h\\u1ecdc 2018-2019 l\\u00ean 2019-2020!\",\"summary\":null,\"content\":\"H\\u1eccC SINH - L\\u00caN L\\u1edaP: H\\u01b0\\u1edbng d\\u1eabn th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp cho h\\u1ecdc sinh t\\u1eeb n\\u0103m h\\u1ecdc 2018-2019 l\\u00ean 2019-2020!\\r\\nB\\u01b0\\u1edbc 1: V\\u00e0o Danh s\\u00e1ch h\\u1ecdc sinh, ch\\u1ecdn Kh\\u1ed1i, ch\\u1ecdn l\\u1edbp c\\u00f3 h\\u1ecdc sinh c\\u1ea7n l\\u00ean l\\u1edbp\\r\\nB\\u01b0\\u1edbc 2: Ch\\u1ecdn 1 ho\\u1eb7c nhi\\u1ec1u h\\u1ecdc sinh c\\u1ea7n th\\u1ef1c hi\\u1ec7n l\\u00ean l\\u1edbp cho n\\u0103m h\\u1ecdc 2019-2020\\r\\nB\\u01b0\\u1edbc 3: T\\u00edch v\\u00e0o checkbox \\\"L\\u00ean l\\u1edbp h\\u1ecdc cho n\\u0103m h\\u1ecdc 2019-2020\\\" -> Ch\\u1ecdn kh\\u1ed1i, l\\u1edbp s\\u1ebd chuy\\u1ec3n \\u0111\\u1ebfn c\\u1ee7a nh\\u1eefng hs n\\u00e0y => Nh\\u1ea5n n\\u00fat \\\"L\\u00ean l\\u1edbp\\\"\\r\\n\\r\\nSau khi th\\u1ef1c hi\\u1ec7n xong c\\u00e1c b\\u01b0\\u1edbc tr\\u00ean, vui l\\u00f2ng ch\\u1ecdn l\\u1ea1i n\\u0103m h\\u1ecdc hi\\u1ec7n t\\u1ea1i tr\\u00ean menu l\\u00e0 n\\u0103m 2019-2020 \\u0111\\u1ec3 xem k\\u1ebft qu\\u1ea3 \\u0111\\u00e3 chuy\\u1ec3n!\\r\\n\\r\\nC\\u00e1c b\\u1ea1n vui l\\u00f2ng xem th\\u00eam trong \\u1ea3nh h\\u01b0\\u1edbng d\\u1eabn \\u0111\\u00ednh k\\u00e8m nh\\u00e9!\",\"attachments\":\"[\\\"LenLop_HS_20201219215831.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-05-30\",\"deactive_at\":\"2019-05-30\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-05-30 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/LenLop_HS_20201219215831.png"}], "is_read": 1, "active_at": 1559149200, "created": 1559172600, "lastmodified": 1608431257}, {"id": 11, "subject": "THU CHI: <PERSON><PERSON> bổ sung bảng kê trả lại tiền <PERSON><PERSON><PERSON> học đường", "summary": "THU CHI: <PERSON><PERSON> <PERSON>ổ sung bảng kê trả lại tiền S<PERSON><PERSON> họ<PERSON> đ<PERSON>, để xử lý các t/h HS thô<PERSON> học, thôi sử dụng hoặc do thiết lập khoản thu ko chọn \"T<PERSON>h gộp vào thừa thiếu tháng sau\". Bảng kê bao gồm những học sinh còn dư phiếu & có hỗ trợ in biên lai trả lại!\r\nVui lòng xem thêm hướng dẫn trong các ảnh đính kèm!", "summary_en": "THU CHI: <PERSON> bo sung bang ke tra lai tien Sua hoc duong, de xu ly cac t/h HS thoi hoc, thoi su dung hoac do thiet lap khoan thu ko chon \"Tinh gop vao thua thieu thang sau\". <PERSON> ke bao gom nhung hoc sinh con du phieu & co ho tro in bien lai tra lai!\r\nVui long xem them huong dan trong cac anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TralaiSuaHD1_20201219215732.png", "attachments": [{"id": "{\"id\":11,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0111\\u1ec3 x\\u1eed l\\u00fd c\\u00e1c t\\/h HS th\\u00f4i h\\u1ecdc, th\\u00f4i s\\u1eed d\\u1ee5ng ho\\u1eb7c do thi\\u1ebft l\\u1eadp kho\\u1ea3n thu ko ch\\u1ecdn \\\"T\\u00ednh g\\u1ed9p v\\u00e0o th\\u1eeba thi\\u1ebfu th\\u00e1ng sau\\\". B\\u1ea3ng k\\u00ea bao g\\u1ed3m nh\\u1eefng h\\u1ecdc sinh c\\u00f2n d\\u01b0 phi\\u1ebfu & c\\u00f3 h\\u1ed7 tr\\u1ee3 in bi\\u00ean lai tr\\u1ea3 l\\u1ea1i!\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"TralaiSuaHD1_20201219215732.png\\\",\\\"TralaiSuaHD2_20201219215732.png\\\",\\\"TralaiSuaHD3_20201219215732.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-27\",\"deactive_at\":\"2019-03-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TralaiSuaHD1_20201219215732.png"}, {"id": "{\"id\":11,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0111\\u1ec3 x\\u1eed l\\u00fd c\\u00e1c t\\/h HS th\\u00f4i h\\u1ecdc, th\\u00f4i s\\u1eed d\\u1ee5ng ho\\u1eb7c do thi\\u1ebft l\\u1eadp kho\\u1ea3n thu ko ch\\u1ecdn \\\"T\\u00ednh g\\u1ed9p v\\u00e0o th\\u1eeba thi\\u1ebfu th\\u00e1ng sau\\\". B\\u1ea3ng k\\u00ea bao g\\u1ed3m nh\\u1eefng h\\u1ecdc sinh c\\u00f2n d\\u01b0 phi\\u1ebfu & c\\u00f3 h\\u1ed7 tr\\u1ee3 in bi\\u00ean lai tr\\u1ea3 l\\u1ea1i!\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"TralaiSuaHD1_20201219215732.png\\\",\\\"TralaiSuaHD2_20201219215732.png\\\",\\\"TralaiSuaHD3_20201219215732.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-27\",\"deactive_at\":\"2019-03-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TralaiSuaHD2_20201219215732.png"}, {"id": "{\"id\":11,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 b\\u1ed5 sung b\\u1ea3ng k\\u00ea tr\\u1ea3 l\\u1ea1i ti\\u1ec1n S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng, \\u0111\\u1ec3 x\\u1eed l\\u00fd c\\u00e1c t\\/h HS th\\u00f4i h\\u1ecdc, th\\u00f4i s\\u1eed d\\u1ee5ng ho\\u1eb7c do thi\\u1ebft l\\u1eadp kho\\u1ea3n thu ko ch\\u1ecdn \\\"T\\u00ednh g\\u1ed9p v\\u00e0o th\\u1eeba thi\\u1ebfu th\\u00e1ng sau\\\". B\\u1ea3ng k\\u00ea bao g\\u1ed3m nh\\u1eefng h\\u1ecdc sinh c\\u00f2n d\\u01b0 phi\\u1ebfu & c\\u00f3 h\\u1ed7 tr\\u1ee3 in bi\\u00ean lai tr\\u1ea3 l\\u1ea1i!\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong c\\u00e1c \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"TralaiSuaHD1_20201219215732.png\\\",\\\"TralaiSuaHD2_20201219215732.png\\\",\\\"TralaiSuaHD3_20201219215732.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-27\",\"deactive_at\":\"2019-03-27\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-27 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_2", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TralaiSuaHD3_20201219215732.png"}], "is_read": 1, "active_at": 1553619600, "created": 1553643000, "lastmodified": 1608431257}, {"id": 10, "subject": "THU CHI: PM đ<PERSON> hỗ trợ trường in biên lai trả tiền sữa học đường", "summary": "THU CHI: PM <PERSON><PERSON> hỗ trợ trường in biên lai trả tiền sữa học đường cho những HS còn dư phiếu sữa & ko tiếp tục uống sữa. <PERSON><PERSON><PERSON> bạn vào biểu tượng \"Con mắt\" ở Thu thanh toán, mở lên và nhập số phiếu trả lại cho HS.\r\n\r\nVD trả lại HS 8 phiếu sữa thì ghi -8, số tiền tương ứng & biểu tượng in sẽ hiển thị lên để xem hoặc in khoản trả lại cho HS!\r\n\r\nVui lòng xem thêm hướng dẫn trong ảnh đính kèm!", "summary_en": "THU CHI: PM <PERSON> <PERSON> tro truong in bien lai tra tien sua hoc duong cho nhung HS con du phieu sua & ko tiep tuc uong sua. Cac ban vao bieu tuong \"Con mat\" o Thu thanh toan, mo len va nhap so phieu tra lai cho HS.\r\n\r\nVD tra lai HS 8 phieu sua thi ghi -8, so tien tuong ung & bieu tuong in se hien thi len de xem hoac in khoan tra lai cho HS!\r\n\r\nVui long xem them huong dan trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/BienlaiTraSua_20201219215606.png", "attachments": [{"id": "{\"id\":10,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: PM \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 tr\\u01b0\\u1eddng in bi\\u00ean lai tr\\u1ea3 ti\\u1ec1n s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"THU CHI: PM \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 tr\\u01b0\\u1eddng in bi\\u00ean lai tr\\u1ea3 ti\\u1ec1n s\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng cho nh\\u1eefng HS c\\u00f2n d\\u01b0 phi\\u1ebfu s\\u1eefa & ko ti\\u1ebfp t\\u1ee5c u\\u1ed1ng s\\u1eefa. C\\u00e1c b\\u1ea1n v\\u00e0o bi\\u1ec3u t\\u01b0\\u1ee3ng \\\"Con m\\u1eaft\\\" \\u1edf Thu thanh to\\u00e1n, m\\u1edf l\\u00ean v\\u00e0 nh\\u1eadp s\\u1ed1 phi\\u1ebfu tr\\u1ea3 l\\u1ea1i cho HS.\\r\\n\\r\\nVD tr\\u1ea3 l\\u1ea1i HS 8 phi\\u1ebfu s\\u1eefa th\\u00ec ghi -8, s\\u1ed1 ti\\u1ec1n t\\u01b0\\u01a1ng \\u1ee9ng & bi\\u1ec3u t\\u01b0\\u1ee3ng in s\\u1ebd hi\\u1ec3n th\\u1ecb l\\u00ean \\u0111\\u1ec3 xem ho\\u1eb7c in kho\\u1ea3n tr\\u1ea3 l\\u1ea1i cho HS!\\r\\n\\r\\nVui l\\u00f2ng xem th\\u00eam h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"BienlaiTraSua_20201219215606.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-19\",\"deactive_at\":\"2019-03-19\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-19 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/BienlaiTraSua_20201219215606.png"}], "is_read": 1, "active_at": 1552928400, "created": 1552951800, "lastmodified": 1608431257}, {"id": 9, "subject": "CÂN ĐỐI KHẨU PHẦN: <PERSON><PERSON><PERSON> nhật tiện ích chia thực phẩm cho điểm trường", "summary": "CÂN ĐỐI KHẨU PHẦN :\r\n1. <PERSON><PERSON><PERSON> nhật thêm tính năng chia thực phẩm cho từng điểm trường áp dụng với trường có nhiều điểm và chung thực đơn\r\n2. <PERSON><PERSON><PERSON> nhật thêm chức năng sửa \"Phiếu kê chợ\" và In \"Phiếu kê chợ\" tại phần \"Cân đối khẩu phần\"\r\n3. \"Phiếu kê chợ\" cập nhật mẫu báo cáo theo nhiều điểm trường\r\nCó thể xem ở ảnh đính kèm.", "summary_en": "CAN DOI KHAU PHAN :\r\n1. Cap nhat them tinh nang chia thuc pham cho tung diem truong ap dung voi truong co nhieu diem va chung thuc don\r\n2. Cap nhat them chuc nang sua \"<PERSON>eu ke cho\" va In \"<PERSON>eu ke cho\" tai phan \"Can doi khau phan\"\r\n3. \"<PERSON>eu ke cho\" cap nhat mau bao cao theo nhieu diem truong\r\nCo the xem o anh dinh kem.", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_DiemTruong_20201219215459.png", "attachments": [{"id": "{\"id\":9,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N: C\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch chia th\\u1ef1c ph\\u1ea9m cho \\u0111i\\u1ec3m tr\\u01b0\\u1eddng\",\"summary\":null,\"content\":\"C\\u00c2N \\u0110\\u1ed0I KH\\u1ea8U PH\\u1ea6N :\\r\\n1. C\\u1eadp nh\\u1eadt th\\u00eam t\\u00ednh n\\u0103ng chia th\\u1ef1c ph\\u1ea9m cho t\\u1eebng \\u0111i\\u1ec3m tr\\u01b0\\u1eddng \\u00e1p d\\u1ee5ng v\\u1edbi tr\\u01b0\\u1eddng c\\u00f3 nhi\\u1ec1u \\u0111i\\u1ec3m v\\u00e0 chung th\\u1ef1c \\u0111\\u01a1n\\r\\n2. C\\u1eadp nh\\u1eadt th\\u00eam ch\\u1ee9c n\\u0103ng s\\u1eeda \\\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\\\" v\\u00e0 In \\\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\\\" t\\u1ea1i ph\\u1ea7n \\\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n\\\"\\r\\n3. \\\"Phi\\u1ebfu k\\u00ea ch\\u1ee3\\\" c\\u1eadp nh\\u1eadt m\\u1eabu b\\u00e1o c\\u00e1o theo nhi\\u1ec1u \\u0111i\\u1ec3m tr\\u01b0\\u1eddng\\r\\nC\\u00f3 th\\u1ec3 xem \\u1edf \\u1ea3nh \\u0111\\u00ednh k\\u00e8m.\",\"attachments\":\"[\\\"CDKP_DiemTruong_20201219215459.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-16\",\"deactive_at\":\"2019-03-16\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-16 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_DiemTruong_20201219215459.png"}], "is_read": 1, "active_at": 1552669200, "created": 1552692600, "lastmodified": 1608431257}, {"id": 8, "subject": "THU CHI: Đ<PERSON> hỗ trợ sửa phiếu tồn tháng trước của những khoản tính theo ngày ko phải tiền ăn", "summary": "THU CHI: Đ<PERSON> hỗ trợ sửa phiếu tồn tháng trước của những khoản tính theo ngày ko phải tiền ăn. Để xử lý t/h các khoản này ko theo quy tắc chung của điểm danh Ăn\r\n\r\nVD như kho<PERSON> \"Sữa học đường\" mà điểm danh ko thể hiện được việc cháu có đi học, c<PERSON> ăn, nhưng ko uống sữa 1 số ngày trong tháng...\r\n\r\nCác bạn vui lòng xem hướng dẫn chi tiết trong ảnh đính kèm nhé!", "summary_en": "THU CHI: Da ho tro sua phieu ton thang truoc cua nhung khoan tinh theo ngay ko phai tien an. De xu ly t/h cac khoan nay ko theo quy tac chung cua diem danh An\r\n\r\nVD nhu khoan \"Sua hoc duong\" ma diem danh ko the hien duoc viec chau co di hoc, co an, nhung ko uong sua 1 so ngay trong thang...\r\n\r\nCac ban vui long xem huong dan chi tiet trong anh dinh kem nhe!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaPhieuTon_20201219215239.png", "attachments": [{"id": "{\"id\":8,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 s\\u1eeda phi\\u1ebfu t\\u1ed3n th\\u00e1ng tr\\u01b0\\u1edbc c\\u1ee7a nh\\u1eefng kho\\u1ea3n t\\u00ednh theo ng\\u00e0y ko ph\\u1ea3i ti\\u1ec1n \\u0103n\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 s\\u1eeda phi\\u1ebfu t\\u1ed3n th\\u00e1ng tr\\u01b0\\u1edbc c\\u1ee7a nh\\u1eefng kho\\u1ea3n t\\u00ednh theo ng\\u00e0y ko ph\\u1ea3i ti\\u1ec1n \\u0103n. \\u0110\\u1ec3 x\\u1eed l\\u00fd t\\/h c\\u00e1c kho\\u1ea3n n\\u00e0y ko theo quy t\\u1eafc chung c\\u1ee7a \\u0111i\\u1ec3m danh \\u0102n\\r\\n\\r\\nVD nh\\u01b0 kho\\u1ea3n \\\"S\\u1eefa h\\u1ecdc \\u0111\\u01b0\\u1eddng\\\" m\\u00e0 \\u0111i\\u1ec3m danh ko th\\u1ec3 hi\\u1ec7n \\u0111\\u01b0\\u1ee3c vi\\u1ec7c ch\\u00e1u c\\u00f3 \\u0111i h\\u1ecdc, c\\u00f3 \\u0103n, nh\\u01b0ng ko u\\u1ed1ng s\\u1eefa 1 s\\u1ed1 ng\\u00e0y trong th\\u00e1ng...\\r\\n\\r\\nC\\u00e1c b\\u1ea1n vui l\\u00f2ng xem h\\u01b0\\u1edbng d\\u1eabn chi ti\\u1ebft trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m nh\\u00e9!\",\"attachments\":\"[\\\"SuaPhieuTon_20201219215239.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-09\",\"deactive_at\":\"2019-03-09\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-09 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SuaPhieuTon_20201219215239.png"}], "is_read": 1, "active_at": 1552064400, "created": 1552087800, "lastmodified": 1608431257}, {"id": 7, "subject": "<PERSON><PERSON>u tiếp phẩm: Hỗ trợ xử lý trong trường hợp không hiển thị dữ liệu", "summary": "Phiếu tiếp phẩm: Hỗ trợ xử lý trong trường hợp không hiển thị dữ liệu, hoặc xuất excel lỗi, có thể nhân do cache của trình duyệt trên máy tính của người dùng. <PERSON><PERSON><PERSON> bạn vui lòng nhấn tổ hợp phím CTRL + F5", "summary_en": "Phieu tiep pham: Ho tro xu ly trong truong hop khong hien thi du lieu, hoac xuat excel loi, co the nhan do cache cua trinh duyet tren may tinh cua nguoi dung. Cac ban vui long nhan to hop phim CTRL + F5", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/mtieppham_20201219215129.png", "attachments": [{"id": "{\"id\":7,\"unit_id\":2539,\"course_id\":null,\"title\":\"Phi\\u1ebfu ti\\u1ebfp ph\\u1ea9m: H\\u1ed7 tr\\u1ee3 x\\u1eed l\\u00fd trong tr\\u01b0\\u1eddng h\\u1ee3p kh\\u00f4ng hi\\u1ec3n th\\u1ecb d\\u1eef li\\u1ec7u\",\"summary\":null,\"content\":\"Phi\\u1ebfu ti\\u1ebfp ph\\u1ea9m: H\\u1ed7 tr\\u1ee3 x\\u1eed l\\u00fd trong tr\\u01b0\\u1eddng h\\u1ee3p kh\\u00f4ng hi\\u1ec3n th\\u1ecb d\\u1eef li\\u1ec7u, ho\\u1eb7c xu\\u1ea5t excel l\\u1ed7i, c\\u00f3 th\\u1ec3 nh\\u00e2n do cache c\\u1ee7a tr\\u00ecnh duy\\u1ec7t tr\\u00ean m\\u00e1y t\\u00ednh c\\u1ee7a ng\\u01b0\\u1eddi d\\u00f9ng. C\\u00e1c b\\u1ea1n vui l\\u00f2ng nh\\u1ea5n t\\u1ed5 h\\u1ee3p ph\\u00edm CTRL + F5\",\"attachments\":\"[\\\"mtieppham_20201219215129.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-03-07\",\"deactive_at\":\"2019-03-07\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-03-07 06:30:00\",\"updated_at\":\"2020-12-20 09:27:37\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/mtieppham_20201219215129.png"}], "is_read": 1, "active_at": 1551891600, "created": 1551915000, "lastmodified": 1608431257}, {"id": 6, "subject": "Hỗ trợ mở file Excel: Trong 1 số trường hợp khi người dùng tải file excel từ trên phần mềm về", "summary": "Hỗ trợ mở file Excel: Trong 1 số trường hợp khi người dùng tải file excel từ trên phần mềm về. Mỗi lần mở file lên, Excel yêu cầu xác nhận \"Yes\" để xem đúng định dạng. Đ<PERSON> khắc tình trạng này các bạn nhấn link tải file hỗ trợ ở đây: <a href=\"http://qlmn.vn/notify/files/fix.reg\" target=\"_blank\" rel=\"noreferrer\"><b>Tải file hỗ trợ Excel</b></a>\r\nCác bạn vui lòng làm theo hướng dẫn trong ảnh đính kèm thông báo này! Lần sau mở file excel lên sẽ không bị hỏi nữa nhé!", "summary_en": "Ho tro mo file Excel: Trong 1 so truong hop khi nguoi dung tai file excel tu tren phan mem ve. <PERSON>i lan mo file len, Excel yeu cau xac nhan \"Yes\" de xem dung dinh dang. De khac tinh trang nay cac ban nhan link tai file ho tro o day: <a href=\"http://qlmn.vn/notify/files/fix.reg\" target=\"_blank\" rel=\"noreferrer\"><b>Tai file ho tro Excel</b></a>\r\nCac ban vui long lam theo huong dan trong anh dinh kem thong bao nay! Lan sau mo file excel len se khong bi hoi nua nhe!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/FixExcel_20201219215000.png", "attachments": [{"id": "{\"id\":6,\"unit_id\":2539,\"course_id\":null,\"title\":\"H\\u1ed7 tr\\u1ee3 m\\u1edf file Excel: Trong 1 s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p khi ng\\u01b0\\u1eddi d\\u00f9ng t\\u1ea3i file excel t\\u1eeb tr\\u00ean ph\\u1ea7n m\\u1ec1m v\\u1ec1\",\"summary\":null,\"content\":\"H\\u1ed7 tr\\u1ee3 m\\u1edf file Excel: Trong 1 s\\u1ed1 tr\\u01b0\\u1eddng h\\u1ee3p khi ng\\u01b0\\u1eddi d\\u00f9ng t\\u1ea3i file excel t\\u1eeb tr\\u00ean ph\\u1ea7n m\\u1ec1m v\\u1ec1. M\\u1ed7i l\\u1ea7n m\\u1edf file l\\u00ean, Excel y\\u00eau c\\u1ea7u x\\u00e1c nh\\u1eadn \\\"Yes\\\" \\u0111\\u1ec3 xem \\u0111\\u00fang \\u0111\\u1ecbnh d\\u1ea1ng. \\u0110\\u1ec3 kh\\u1eafc t\\u00ecnh tr\\u1ea1ng n\\u00e0y c\\u00e1c b\\u1ea1n nh\\u1ea5n link t\\u1ea3i file h\\u1ed7 tr\\u1ee3 \\u1edf \\u0111\\u00e2y: <a href=\\\"http:\\/\\/qlmn.vn\\/notify\\/files\\/fix.reg\\\" target=\\\"_blank\\\" rel=\\\"noreferrer\\\"><b>T\\u1ea3i file h\\u1ed7 tr\\u1ee3 Excel<\\/b><\\/a>\\r\\nC\\u00e1c b\\u1ea1n vui l\\u00f2ng l\\u00e0m theo h\\u01b0\\u1edbng d\\u1eabn trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m th\\u00f4ng b\\u00e1o n\\u00e0y! L\\u1ea7n sau m\\u1edf file excel l\\u00ean s\\u1ebd kh\\u00f4ng b\\u1ecb h\\u1ecfi n\\u1eefa nh\\u00e9!\",\"attachments\":\"[\\\"FixExcel_20201219215000.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-28\",\"deactive_at\":\"2019-02-28\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-28 16:30:00\",\"updated_at\":\"2020-12-20 09:31:28\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/FixExcel_20201219215000.png"}], "is_read": 1, "active_at": 1551286800, "created": 1551346200, "lastmodified": 1608431488}, {"id": 5, "subject": "<PERSON><PERSON> đối khẩu phần: <PERSON><PERSON> bảng thành phần dinh dưỡng nếu không muốn hiển thị dòng cơ cấu áp dụng", "summary": "Cân đối khẩu phần: Trong bảng thành phần dinh dưỡng nếu không muốn hiển thị dòng cơ cấu áp dụng thì vào \"Chức năng tùy chỉnh\" ở form cân đối khẩu phần tích chọn vào \"Ẩn cơ cấu áp dụng ...\". <PERSON><PERSON> thể xem ở ảnh đính kèm", "summary_en": "Can doi khau phan: <PERSON><PERSON> bang thanh phan dinh duong neu khong muon hien thi dong co cau ap dung thi vao \"Chuc nang tuy chinh\" o form can doi khau phan tich chon vao \"An co cau ap dung ...\". Co the xem o anh dinh kem", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_CCAD_20201219214347.png", "attachments": [{"id": "{\"id\":5,\"unit_id\":2539,\"course_id\":null,\"title\":\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n: Trong b\\u1ea3ng th\\u00e0nh ph\\u1ea7n dinh d\\u01b0\\u1ee1ng n\\u1ebfu kh\\u00f4ng mu\\u1ed1n hi\\u1ec3n th\\u1ecb d\\u00f2ng c\\u01a1 c\\u1ea5u \\u00e1p d\\u1ee5ng\",\"summary\":null,\"content\":\"C\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n: Trong b\\u1ea3ng th\\u00e0nh ph\\u1ea7n dinh d\\u01b0\\u1ee1ng n\\u1ebfu kh\\u00f4ng mu\\u1ed1n hi\\u1ec3n th\\u1ecb d\\u00f2ng c\\u01a1 c\\u1ea5u \\u00e1p d\\u1ee5ng th\\u00ec v\\u00e0o \\\"Ch\\u1ee9c n\\u0103ng t\\u00f9y ch\\u1ec9nh\\\" \\u1edf form c\\u00e2n \\u0111\\u1ed1i kh\\u1ea9u ph\\u1ea7n t\\u00edch ch\\u1ecdn v\\u00e0o \\\"\\u1ea8n c\\u01a1 c\\u1ea5u \\u00e1p d\\u1ee5ng ...\\\". C\\u00f3 th\\u1ec3 xem \\u1edf \\u1ea3nh \\u0111\\u00ednh k\\u00e8m\",\"attachments\":\"[\\\"CDKP_CCAD_20201219214347.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-26\",\"deactive_at\":\"2019-02-26\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-26 08:30:00\",\"updated_at\":\"2020-12-20 09:31:23\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/CDKP_CCAD_20201219214347.png"}], "is_read": 1, "active_at": 1551114000, "created": 1551144600, "lastmodified": 1608431483}, {"id": 4, "subject": "THU CHI: Đã hỗ trợ để Kế toán có thể gõ số tiền \"<PERSON>ải đóng\" tùy ý ko theo công thức", "summary": "THU CHI: Đã hỗ trợ để Kế toán có thể gõ số tiền \"Phải đóng\" tùy ý ko theo công thức chuẩn: \"S<PERSON> phiếu\" x \"Tiền 1 phiếu\" = \"Tiền phải nộp\".\r\n\r\nTiện ích này nhằm giúp xử lý các t/h như HS được miễn gi<PERSON>, vd mua 20 phiếu, tiền mỗi phiếu là 20k, nhưng HS này được giảm 50% => Tiền phải nộp chỉ là 100k thôi.\r\n\r\nVui lòng xem chi tiết hướng dẫn sử dụng trong ảnh đính kèm!", "summary_en": "THU CHI: <PERSON> ho tro de <PERSON> toan co the go so tien \"Phai dong\" tuy y ko theo cong thuc chuan: \"So phieu\" x \"Tien 1 phieu\" = \"Tien phai nop\".\r\n\r\nTien ich nay nham giup xu ly cac t/h nhu HS duoc mien giam, vd mua 20 phieu, tien moi phieu la 20k, nhung HS nay duoc giam 50% => Tien phai nop chi la 100k thoi.\r\n\r\nVui long xem chi tiet huong dan su dung trong anh dinh kem!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TienTuyY_20201219214333.png", "attachments": [{"id": "{\"id\":4,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 K\\u1ebf to\\u00e1n c\\u00f3 th\\u1ec3 g\\u00f5 s\\u1ed1 ti\\u1ec1n \\\"Ph\\u1ea3i \\u0111\\u00f3ng\\\" t\\u00f9y \\u00fd ko theo c\\u00f4ng th\\u1ee9c\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 K\\u1ebf to\\u00e1n c\\u00f3 th\\u1ec3 g\\u00f5 s\\u1ed1 ti\\u1ec1n \\\"Ph\\u1ea3i \\u0111\\u00f3ng\\\" t\\u00f9y \\u00fd ko theo c\\u00f4ng th\\u1ee9c chu\\u1ea9n: \\\"S\\u1ed1 phi\\u1ebfu\\\" x \\\"Ti\\u1ec1n 1 phi\\u1ebfu\\\" = \\\"Ti\\u1ec1n ph\\u1ea3i n\\u1ed9p\\\".\\r\\n\\r\\nTi\\u1ec7n \\u00edch n\\u00e0y nh\\u1eb1m gi\\u00fap x\\u1eed l\\u00fd c\\u00e1c t\\/h nh\\u01b0 HS \\u0111\\u01b0\\u1ee3c mi\\u1ec5n gi\\u1ea3m, vd mua 20 phi\\u1ebfu, ti\\u1ec1n m\\u1ed7i phi\\u1ebfu l\\u00e0 20k, nh\\u01b0ng HS n\\u00e0y \\u0111\\u01b0\\u1ee3c gi\\u1ea3m 50% => Ti\\u1ec1n ph\\u1ea3i n\\u1ed9p ch\\u1ec9 l\\u00e0 100k th\\u00f4i.\\r\\n\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft h\\u01b0\\u1edbng d\\u1eabn s\\u1eed d\\u1ee5ng trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m!\",\"attachments\":\"[\\\"TienTuyY_20201219214333.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-20\",\"deactive_at\":\"2019-02-20\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-20 09:30:00\",\"updated_at\":\"2020-12-20 09:31:18\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/TienTuyY_20201219214333.png"}], "is_read": 1, "active_at": 1550595600, "created": 1550629800, "lastmodified": 1608431478}, {"id": 3, "subject": "THU CHI: <PERSON><PERSON> cập nhật tiện ích thu khoản phí bổ sung của học sinh", "summary": "THU CHI: <PERSON><PERSON> cập nhật tiện ích thu khoản phí bổ sung của học sinh, vd các khoản phí đầu năm, cá<PERSON> kho<PERSON>n phụ thu chỉ áp dụng cho 1 số học sinh. <PERSON><PERSON><PERSON> biên lai & sổ sách vẫn hiển thị đầy đủ khoản phụ thu này của học sinh. Cách mới này dễ hiểu & dễ sử dụng hơn!\r\n\r\nVui lòng xem chi tiết hướng dẫn cách làm trong ảnh đính kèm dưới đây!", "summary_en": "THU CHI: Da cap nhat tien ich thu khoan phi bo sung cua hoc sinh, vd cac khoan phi dau nam, cac khoan phu thu chi ap dung cho 1 so hoc sinh. Cac bien lai & so sach van hien thi day du khoan phu thu nay cua hoc sinh. Cach moi nay de hieu & de su dung hon!\r\n\r\nVui long xem chi tiet huong dan cach lam trong anh dinh kem duoi day!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuBoSung1_20201219214304.png", "attachments": [{"id": "{\"id\":3,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch thu kho\\u1ea3n ph\\u00ed b\\u1ed5 sung c\\u1ee7a h\\u1ecdc sinh\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch thu kho\\u1ea3n ph\\u00ed b\\u1ed5 sung c\\u1ee7a h\\u1ecdc sinh, vd c\\u00e1c kho\\u1ea3n ph\\u00ed \\u0111\\u1ea7u n\\u0103m, c\\u00e1c kho\\u1ea3n ph\\u1ee5 thu ch\\u1ec9 \\u00e1p d\\u1ee5ng cho 1 s\\u1ed1 h\\u1ecdc sinh. C\\u00e1c bi\\u00ean lai & s\\u1ed5 s\\u00e1ch v\\u1eabn hi\\u1ec3n th\\u1ecb \\u0111\\u1ea7y \\u0111\\u1ee7 kho\\u1ea3n ph\\u1ee5 thu n\\u00e0y c\\u1ee7a h\\u1ecdc sinh. C\\u00e1ch m\\u1edbi n\\u00e0y d\\u1ec5 hi\\u1ec3u & d\\u1ec5 s\\u1eed d\\u1ee5ng h\\u01a1n!\\r\\n\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft h\\u01b0\\u1edbng d\\u1eabn c\\u00e1ch l\\u00e0m trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"ThuBoSung1_20201219214304.png\\\",\\\"ThuBoSung2_20201219214304.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-18\",\"deactive_at\":\"2019-02-18\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-18 08:30:00\",\"updated_at\":\"2020-12-20 09:31:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuBoSung1_20201219214304.png"}, {"id": "{\"id\":3,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch thu kho\\u1ea3n ph\\u00ed b\\u1ed5 sung c\\u1ee7a h\\u1ecdc sinh\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 c\\u1eadp nh\\u1eadt ti\\u1ec7n \\u00edch thu kho\\u1ea3n ph\\u00ed b\\u1ed5 sung c\\u1ee7a h\\u1ecdc sinh, vd c\\u00e1c kho\\u1ea3n ph\\u00ed \\u0111\\u1ea7u n\\u0103m, c\\u00e1c kho\\u1ea3n ph\\u1ee5 thu ch\\u1ec9 \\u00e1p d\\u1ee5ng cho 1 s\\u1ed1 h\\u1ecdc sinh. C\\u00e1c bi\\u00ean lai & s\\u1ed5 s\\u00e1ch v\\u1eabn hi\\u1ec3n th\\u1ecb \\u0111\\u1ea7y \\u0111\\u1ee7 kho\\u1ea3n ph\\u1ee5 thu n\\u00e0y c\\u1ee7a h\\u1ecdc sinh. C\\u00e1ch m\\u1edbi n\\u00e0y d\\u1ec5 hi\\u1ec3u & d\\u1ec5 s\\u1eed d\\u1ee5ng h\\u01a1n!\\r\\n\\r\\nVui l\\u00f2ng xem chi ti\\u1ebft h\\u01b0\\u1edbng d\\u1eabn c\\u00e1ch l\\u00e0m trong \\u1ea3nh \\u0111\\u00ednh k\\u00e8m d\\u01b0\\u1edbi \\u0111\\u00e2y!\",\"attachments\":\"[\\\"ThuBoSung1_20201219214304.png\\\",\\\"ThuBoSung2_20201219214304.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-18\",\"deactive_at\":\"2019-02-18\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-18 08:30:00\",\"updated_at\":\"2020-12-20 09:31:13\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/ThuBoSung2_20201219214304.png"}], "is_read": 1, "active_at": 1550422800, "created": 1550453400, "lastmodified": 1608431473}, {"id": 2, "subject": "THU CHI: <PERSON><PERSON> hỗ trợ để trường, <PERSON><PERSON> toán dễ dàng phát hiện ra các t/h học sinh có khoản thu hoặc đóng bị lệch", "summary": "THU CHI: <PERSON><PERSON> hỗ trợ để trường, <PERSON><PERSON> toán dễ dàng phát hiện ra các t/h học sinh có khoản thu hoặc đóng bị lệch do quá trình thao tác hoặc các t/h như mua phiếu, nhưng nợ tiền. Trường/Kế toán có thể dựa vào đây để phát hiện chỉnh sửa lại theo mong muốn!", "summary_en": "THU CHI: <PERSON> <PERSON> tro de <PERSON>ru<PERSON>, <PERSON> toan de dang phat hien ra cac t/h hoc sinh co khoan thu hoac dong bi lech do qua trinh thao tac hoac cac t/h nhu mua phieu, nhung no tien. <PERSON><PERSON><PERSON>/Ke toan co the dua vao day de phat hien chinh sua lai theo mong muon!", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SoThuTTColor_20201219214156.png", "attachments": [{"id": "{\"id\":2,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 tr\\u01b0\\u1eddng, K\\u1ebf to\\u00e1n d\\u1ec5 d\\u00e0ng ph\\u00e1t hi\\u1ec7n ra c\\u00e1c t\\/h h\\u1ecdc sinh c\\u00f3 kho\\u1ea3n thu ho\\u1eb7c \\u0111\\u00f3ng b\\u1ecb l\\u1ec7ch\",\"summary\":null,\"content\":\"THU CHI: \\u0110\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 tr\\u01b0\\u1eddng, K\\u1ebf to\\u00e1n d\\u1ec5 d\\u00e0ng ph\\u00e1t hi\\u1ec7n ra c\\u00e1c t\\/h h\\u1ecdc sinh c\\u00f3 kho\\u1ea3n thu ho\\u1eb7c \\u0111\\u00f3ng b\\u1ecb l\\u1ec7ch do qu\\u00e1 tr\\u00ecnh thao t\\u00e1c ho\\u1eb7c c\\u00e1c t\\/h nh\\u01b0 mua phi\\u1ebfu, nh\\u01b0ng n\\u1ee3 ti\\u1ec1n. Tr\\u01b0\\u1eddng\\/K\\u1ebf to\\u00e1n c\\u00f3 th\\u1ec3 d\\u1ef1a v\\u00e0o \\u0111\\u00e2y \\u0111\\u1ec3 ph\\u00e1t hi\\u1ec7n ch\\u1ec9nh s\\u1eeda l\\u1ea1i theo mong mu\\u1ed1n!\",\"attachments\":\"[\\\"SoThuTTColor_20201219214156.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-15\",\"deactive_at\":\"2019-02-15\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-15 05:30:00\",\"updated_at\":\"2020-12-20 09:31:10\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/SoThuTTColor_20201219214156.png"}], "is_read": 1, "active_at": 1550163600, "created": 1550183400, "lastmodified": 1608431470}, {"id": 1, "subject": "THU CHI: <PERSON><PERSON> <PERSON> đã hỗ trợ để PHHS có thể mua số phiếu ăn/sữa...tùy ý", "summary": "THU CHI: <PERSON><PERSON> <PERSON> đã hỗ trợ để PHHS có thể mua số phiếu ăn/sữa...tùy ý, ko theo số ngày đi học/điểm danh. Vui lòng xem mô tả chi tiết hơn trong ảnh dưới đây nhé", "summary_en": "THU CHI: <PERSON><PERSON> <PERSON> da ho tro de PHHS co the mua so phieu an/sua...tuy y, ko theo so ngay di hoc/diem danh. Vui long xem mo ta chi tiet hon trong anh duoi day nhe", "content": null, "attachment": "https://storage.ura.edu.vn/ura/files/notify/qlmn/1AThuPhi_20201219213915.png", "attachments": [{"id": "{\"id\":1,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: <PERSON>hu <PERSON> \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 PHHS c\\u00f3 th\\u1ec3 mua s\\u1ed1 phi\\u1ebfu \\u0103n\\/s\\u1eefa...t\\u00f9y \\u00fd\",\"summary\":null,\"content\":\"THU CHI: Thu Chi \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 PHHS c\\u00f3 th\\u1ec3 mua s\\u1ed1 phi\\u1ebfu \\u0103n\\/s\\u1eefa...t\\u00f9y \\u00fd, ko theo s\\u1ed1 ng\\u00e0y \\u0111i h\\u1ecdc\\/\\u0111i\\u1ec3m danh. Vui l\\u00f2ng xem m\\u00f4 t\\u1ea3 chi ti\\u1ebft h\\u01a1n trong \\u1ea3nh d\\u01b0\\u1edbi \\u0111\\u00e2y nh\\u00e9\",\"attachments\":\"[\\\"1AThuPhi_20201219213915.png\\\",\\\"1BThuPhi_20201219213915.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-15\",\"deactive_at\":\"2019-02-15\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-15 07:30:00\",\"updated_at\":\"2020-12-20 09:31:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_0", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/1AThuPhi_20201219213915.png"}, {"id": "{\"id\":1,\"unit_id\":2539,\"course_id\":null,\"title\":\"THU CHI: <PERSON>hu <PERSON> \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 PHHS c\\u00f3 th\\u1ec3 mua s\\u1ed1 phi\\u1ebfu \\u0103n\\/s\\u1eefa...t\\u00f9y \\u00fd\",\"summary\":null,\"content\":\"THU CHI: Thu Chi \\u0111\\u00e3 h\\u1ed7 tr\\u1ee3 \\u0111\\u1ec3 PHHS c\\u00f3 th\\u1ec3 mua s\\u1ed1 phi\\u1ebfu \\u0103n\\/s\\u1eefa...t\\u00f9y \\u00fd, ko theo s\\u1ed1 ng\\u00e0y \\u0111i h\\u1ecdc\\/\\u0111i\\u1ec3m danh. Vui l\\u00f2ng xem m\\u00f4 t\\u1ea3 chi ti\\u1ebft h\\u01a1n trong \\u1ea3nh d\\u01b0\\u1edbi \\u0111\\u00e2y nh\\u00e9\",\"attachments\":\"[\\\"1AThuPhi_20201219213915.png\\\",\\\"1BThuPhi_20201219213915.png\\\"]\",\"project_code\":\"qlmn\",\"module\":null,\"status\":1,\"is_read\":1,\"active_at\":\"2019-02-15\",\"deactive_at\":\"2019-02-15\",\"province\":\"0\",\"not_province\":null,\"unit_level\":4,\"is_principal\":0,\"created_at\":\"2019-02-15 07:30:00\",\"updated_at\":\"2020-12-20 09:31:05\",\"created_user\":\"admin\",\"updated_user\":\"admin\"}_1", "url": "https://storage.ura.edu.vn/ura/files/notify/qlmn/1BThuPhi_20201219213915.png"}], "is_read": 1, "active_at": 1550163600, "created": 1550190600, "lastmodified": 1608431465}], "result": "success"}