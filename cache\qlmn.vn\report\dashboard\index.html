<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/common.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/main-report.css?_=1812802314" />
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
	<script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.0/xlsx.full.min.js"></script>

	<style type="text/css">
		*{
			font-family: Nunito_Regular;
			font-size: 13 !important;
		}
		@media  print {
			body > .panel {
				display: none !important;
			}
		}
	</style>
	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
	<script type="text/javascript">
		$CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'dinhduong',
			school_point: +'1',
			unit_id: parseInt('51461'),
            school_points: +'1',
            school_point_together: parseInt('0'),
            is_view_csdl_nganh: true,
            administrator: 1,//parseInt('0'),
			dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			level: '4',
      	};
	</script>
	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>

	<script src="http://localhost:3000/js/common.js?_=428172856"></script>
	<script type="text/javascript">
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
		angular_app_report = angular_app;
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=376825673494"></script>
	<script src="http://localhost:3000/js/dinhduong/main-angular-report.js?_=1879008265"></script>
	<script type="text/javascript" src="http://localhost:3000/js/library.js?v=307125735"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController">
		<div class="report-container" ng-controller="dashboardController">
    <div class="row" style="margin-left: 5px">
        <div class="col-md-5 blog-content" style="padding-bottom: 5px">

            <div class="col-md-2" ng-if="$CFG.level == 2">
                <span>Chọn phòng:</span>
            </div>
            <div class="col-md-4" ng-if="$CFG.level == 2">
                <select class="form-control" name="" id="" ng-model="filter.id_phong" ng-change="init()">
                    <option value="" disabled>Chọn phòng</option>
                                    </select>
            </div>
            <div class="col-md-2">
                Chọn tháng
            </div>
            <div class="col-md-4">
                <select class="form-control custom-select" ng-options="month.id as month.title for month in months"
                    ng-model="filter.month" ng-change="onMonthChanged()">
                </select>
            </div>
        </div>
    </div>
    <div class="row">
                <div class="col-md-12 col-lg-12">
            <div class="row blog-content">
                <div class="col-sm-12 col-lg-4 col-md-4">
                    <canvas id="chart_attend"></canvas>
                </div>
                <div class="col-sm-12 col-lg-4 col-md-4">
                    <canvas id="chart_cdkp_money"></canvas>
                </div>
                <div class="col-sm-12 col-lg-4 col-md-4">
                    <canvas id="chart_cdkp_quantity"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-12 col-lg-12">
            <div class="row blog-content">
                <div class="col-sm-12 col-lg-6 col-md-6">
                    <canvas id="chart_fee_amount"></canvas>
                </div>
                <div class="col-sm-12 col-lg-6 col-md-6">
                    <canvas id="chart_fee_student"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .report-container {
        background-color: #ccc;
        padding-bottom: 30px;
    }
    .blog-content {
        background-color: white;
        border-radius: 10px;
        margin: 10px 10px;
        padding-top: 5px;
        
    }

    .h300 {
        height: 300px;
    }
    .blog-content p {
        margin: 0 0 5px !important;
    }
    .range-slider-container {
        position: relative;
        height: 8px;
    }

    .range-slider-background {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background: #ccc;
        border-radius: 5px;
        z-index: 1;
    }

    .range-slider-fill {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: linear-gradient(90deg, rgba(0, 128, 255, 0.481), rgba(32, 173, 248, 0.752), rgba(93, 187, 250, 0.859));
        border-radius: 5px;
        z-index: 2;
        pointer-events: none;
    }
</style>
<script>
    var schoolyear = '2024'
</script>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/javascript" src="http://localhost:3000/js/report/dashboard/index.js?1754638108"></script>
	</div>
</body>
<style>
	@media  print {
		.btn {
		  display: none !important;
		}
	}
</style>
<script>
	var apiUrl = $CFG.remote.base_url + '/doing/admin/user/';
	var url = $CFG.remote.base_url + '/images/signs/' + $CFG.unit_id + '/';
	function getSignConfig(date, module, group_id) {
		var params = {
			module : module,
			date : date,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'getSignConfig',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				if (response.success) {
					let data = JSON.parse(response.data.sign_data);
					Object.keys(data).forEach((v) => {
						$('#'+v).attr('src',data[v]['filename']) ;
						$('#'+v).attr('title',data[v]['time']) ;
					})
					let eleBrs = document.querySelectorAll(".break_line");
					eleBrs.forEach(v => {
						v.style.display = 'none';
					});
				}
			}
		});
	}

	function getSignWithType(date, module, group_id, type) {
		var params = {
			module : module,
			date : date,
			type : type,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'signing',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				alert(response.message)
				if (response.success) {
					$('#'+type).attr('src',response.signInfo.filename);
					$('#'+type).attr('title',response.signInfo.time);
				}
			}
		});
	}
</script>
</html>
