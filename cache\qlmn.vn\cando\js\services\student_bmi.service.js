(function StudentBmiYearlyController(app) {
    'use strict';

    app.service('StudentBmiService', ['$http', 'APP_CONFIG', '$httpParamSerializerJQLike', studentBmiServices]);

    function studentBmiServices($http, APP_CONFIG, $httpParamSerializerJQLike) {
        function fetchStudents(filters) {
            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime() + 'list_student_bmi_yearly_history', '');

            var options = {
                params: {filters: filters},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            return $http.get(APP_CONFIG.API.ENDPOINT + '/measures/api/reports/bmi-histories', options)
                .then(function (response) {
                    return _.get(response, 'data.students.data', []);
                })
                .catch(function () {
                    return [];
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        }

        function generateDownloadExcelUrlForBmiYearlyHistory(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/reports/bmi-histories/yearly/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        }

        function fetchBmiWeightByGradeReport(filters) {
            var url =  APP_CONFIG.API.ENDPOINT + '/measures/api/reports/weight/by-grades';

            var options = {
                params: {filters: filters},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime() + 'list_student_bmi_yearly_history', '');

            return $http.get(url, options)
                .then(function (response) {
                    return _.get(response, 'data.grades.data', []);
                })
                .catch(function () {
                    return [];
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        }

        function generateDownloadExcelUrlBmiWeightByGrade(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/reports/weight/by-grades/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        }

        function fetchBmiWeightByCourseReport(filters) {
            var url =  APP_CONFIG.API.ENDPOINT + '/measures/api/reports/weight/by-courses';

            var options = {
                params: {filters: filters},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime());

            return $http.get(url, options)
                .then(function (response) {
                    return _.get(response, 'data.courses.data', []);
                })
                .catch(function () {
                    return [];
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        }

        function generateDownloadExcelUrlBmiWeightByCourse(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/reports/weight/by-courses/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        }

        function generateDownloadExcelUrlForBmiForWeight(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/reports/weight/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        }

        function generateDownloadExcelUrlForBmiForHeight(filters) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/reports/height/download';

            return url + '?' + $httpParamSerializerJQLike({filters: filters, 'time': (new Date()).getTime()});
        }

        return {
            fetchStudents: fetchStudents,
            generateDownloadExcelUrlForBmiYearlyHistory: generateDownloadExcelUrlForBmiYearlyHistory,
            fetchBmiWeightByGradeReport: fetchBmiWeightByGradeReport,
            generateDownloadExcelUrlBmiWeightByGrade: generateDownloadExcelUrlBmiWeightByGrade,
            fetchBmiWeightByCourseReport: fetchBmiWeightByCourseReport,
            generateDownloadExcelUrlBmiWeightByCourse: generateDownloadExcelUrlBmiWeightByCourse,
            generateDownloadExcelUrlForBmiForWeight: generateDownloadExcelUrlForBmiForWeight,
            generateDownloadExcelUrlForBmiForHeight: generateDownloadExcelUrlForBmiForHeight,
        };
    }
})(window.angular_app);
