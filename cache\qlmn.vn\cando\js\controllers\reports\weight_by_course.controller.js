(function WeightByCourseReportController(app) {
    'use strict';

    app.controller('WeightByCourseReportController', WeightByCourseReportController);

    function WeightByCourseReportController($scope, $location, AuthService, CommonService, CourseService, ACTIVE_MONTHS, StudentBmiService) {
        var vm = this;

        // FIXME: can we move this logic to router config?
        if (!AuthService.isSuperAdmin()) {
            $location.path('/cando');

            return;
        }

        $scope.sys.module.title = 'Thể lực trẻ theo lớp';

        vm.filters = {
            'school_year': $CFG.active_year || (new Date()).getFullYear(),
            'month': 0,
            'grade_id': 0,
        };

        vm.months = _.map(ACTIVE_MONTHS, function (month) {
            return {id: month, title: _.padStart(month, 2, '0')};
        });

        vm.months.unshift({id: 0, title: 'Chọn tháng'});

        vm.grades = [];

        CourseService.fetchCourses().then(function (items) {
            vm.grades = _.map(items, function (item) {
                return {id: item.id, title: item.name};
            });

            vm.grades.unshift({id: 0, title: 'Chọn khối'});
        });

        vm.schoolYears = CommonService.generateSchoolYear();

        vm.items = [];

        vm.onSchoolYearChanged = function onSchoolYearChanged() {
            fetchReport();
        };

        vm.onMonthChanged = function onMonthChanged() {
            fetchReport();
        };

        vm.onGradeChanged = function onGradeChanged() {
            fetchReport();
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentBmiService.generateDownloadExcelUrlBmiWeightByCourse(vm.filters);
        };

        function fetchReport() {
            vm.items = [];

            if (!vm.filters.month || !vm.filters.school_year || !vm.filters.grade_id) {
                return;
            }

            StudentBmiService.fetchBmiWeightByCourseReport(vm.filters)
                .then(function (items) {
                    vm.items = items;
                });
        }
    }

    WeightByCourseReportController.$inject = [
        '$scope',
        '$location',
        'AuthService',
        'CommonService',
        'CourseService',
        'ACTIVE_MONTHS',
        'StudentBmiService',
    ];
})(window.angular_app);
