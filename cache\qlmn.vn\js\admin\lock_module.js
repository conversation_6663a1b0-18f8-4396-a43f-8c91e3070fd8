(function($, doc){
    $.lock_module = {
        module: 'lock_module',
        init: function() {
            var self = this;
            var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
            $.dm_datagrid.init(
                urls.join('/'),
                this.module, /*<PERSON><PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
                '', /*Tiêu đề cho bảng dữ liệu*/
                [[
                    { title:'<PERSON><PERSON> hệ', field:'project_id', width:100,
                        formatter:function(value,row,index){
                            if(value==1) {
                                return '<span><PERSON><PERSON> đo học sinh</span>';
                            }else{
                                return '<span><PERSON><PERSON> trú (Dinh dưỡng - Thu chi)</span>';
                            }
                        }
                    },
                    { title:'Tên chức năng', field:'name', width:120},
                    { title:'<PERSON><PERSON><PERSON> ho<PERSON> (Mở)?', field:'status', width:30, align:'center',
                        formatter:function(value,row,index){
                            if(value==1) {
                                return '<input type="checkbox" onclick="changeLockStatus('+row.id+',0)" checked="checked"></input>';
                            }else{
                                return '<input type="checkbox" onclick="changeLockStatus('+row.id+',1)"></input>';
                            }
                        }
                    },
                    { title:'Ngày cập nhật', field:'updated_at_vn', width:120},
                ]]
            );
             $.lock_module.initAngular();
        },initAngular: function(){
            var self = this;
            setTimeout(function(){
                angular.element($('body')).scope().$apply(function(scope){
                    $.lock_module.scope = scope;
                })
            })

        }
    }
})($, document);