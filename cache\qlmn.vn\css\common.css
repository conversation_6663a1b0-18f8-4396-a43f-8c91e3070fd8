/*-- Common css file --*/
.clearfix {clear: both !important;}
.fL {float: left !important;}
.fLi {float: left !important;}
.fR {float: right !important;}
.fRi {float: right !important;}
.dpb {display: block !important;}
.dpn {display: none !important;}
.dpi {display: inline !important;}
.dpib {display: inline-block !important;}
.ulsNone{list-style: none !important;}
.wSpaceNormal {white-space: normal !important;}
.wSpacePreline {white-space: pre-line !important;}
.mcur {cursor: pointer !important;}

.noBorderBottomi {border-bottom:none !important;}
.borderGrey {border: 1px solid #CCCCCC !important;}
.borderLightGrey {border: 1px solid #e4e9f0 !important;}
.borderTopGrey {border-top: 1px solid #CCCCCC !important;}
.borderLeftGrey {border-left: 1px solid #CCCCCC !important;}
.borderRightGrey {border-right: 1px solid #CCCCCC !important;}
.borderBottomGrey {border-bottom: 1px solid #CCCCCC !important;}
.bgLight<PERSON>rey{background-color: #f7f7f7 !important;}
.bgWhite{background-color: #fff !important;}
.bgBlack{background-color: #000 !important;}
.bgNone{background-color: transparent !important;}
.bgNotiUnread {background-color: #edf2fa !important;}
.webkitTransform {-webkit-transform: translateZ(0) !important;}
.brdNone {border: none !important;}

/* text-align class builder */
.tAc {text-align: center !important;}
.tAl {text-align: left !important;}
.tAr {text-align: right !important;}
.tDu {text-decoration: underline !important;}
.tP10i {top: 10px !important;}
.fsBold{ font-weight: bold !important;}
.fsNormal { font-weight: normal !important;}
.fw500 { font-weight: 500 !important;}
.fw700 { font-weight: 700 !important;}
.clrBlack {color:#000 !important;}
.clrWhite {color: #fff !important;}
.clrRed {color: red !important;}
.clrLink {color: #325da7 !important;}
.clrOrange {color: #FF8000 !important;}
.clrBlue {color: blue !important;}
.clrGreen {color: green !important;}
.clrDark {color: #353535 !important;}

/* margin class builder */
.mA0 {margin: 0px !important;}
.mA0i {margin: 0px !important;}
.mA5 {margin: 5px !important;}
.mA8 {margin: 8px !important;}
.mA10 {margin: 10px !important;}
.mA15 {margin: 15px !important;}
.mA20 {margin: 20px !important;}
.mA25 {margin: 25px !important;}
.mA30 {margin: 30px !important;}
.mA35 {margin: 35px !important;}
.mA40 {margin: 40px !important;}
.mA45 {margin: 45px !important;}
.mA50 {margin: 50px !important;}
.mA55 {margin: 55px !important;}
.mA60 {margin: 60px !important;}
.mA65 {margin: 65px !important;}
.mA70 {margin: 70px !important;}
.mA75 {margin: 75px !important;}
.mA80 {margin: 80px !important;}
.mA85 {margin: 85px !important;}
.mA90 {margin: 90px !important;}
.mA95 {margin: 95px !important;}
.mA100 {margin: 100px !important;}

/* margin left class builder */
.mL0 {margin-left: 0px !important;}
.mL0i {margin-left: 0px !important;}
.mL5 {margin-left: 5px !important;}
.mL8 {margin-left: 5px !important;}
.mL10 {margin-left: 10px !important;}
.mL10i {margin-left: 10px !important;}
.mL15 {margin-left: 15px !important;}
.mL15i {margin-left: 15px !important;}
.mL20 {margin-left: 20px !important;}
.mL20i {margin-left: 20px !important;}
.mL25 {margin-left: 25px !important;}
.mL30 {margin-left: 30px !important;}
.mL35 {margin-left: 35px !important;}
.mL40 {margin-left: 40px !important;}
.mL45 {margin-left: 45px !important;}
.mL50 {margin-left: 50px !important;}
.mL55 {margin-left: 55px !important;}
.mL60 {margin-left: 60px !important;}
.mL65 {margin-left: 65px !important;}
.mL70 {margin-left: 70px !important;}
.mL75 {margin-left: 75px !important;}
.mL80 {margin-left: 80px !important;}
.mL85 {margin-left: 85px !important;}
.mL90 {margin-left: 90px !important;}
.mL95 {margin-left: 95px !important;}
.mL100 {margin-left: 100px !important;}
.mL224i {margin-left: 224px !important;}

/* margin right class builder */
.mR0 {margin-right: 0px !important;}
.mR0i {margin-right: 0px !important;}
.mR3 {margin-right: 3px !important;}
.mR5 {margin-right: 5px !important;}
.mR8 {margin-right: 8px !important;}
.mR10 {margin-right: 10px !important;}
.mR13 {margin-right: 13px !important;}
.mR15 {margin-right: 15px !important;}
.mR15i {margin-right: 15px !important;}
.mR20 {margin-right: 20px !important;}
.mR25 {margin-right: 25px !important;}
.mR30 {margin-right: 30px !important;}
.mR35 {margin-right: 35px !important;}
.mR40 {margin-right: 40px !important;}
.mR45 {margin-right: 45px !important;}
.mR50 {margin-right: 50px !important;}
.mR55 {margin-right: 55px !important;}
.mR60 {margin-right: 60px !important;}
.mR65 {margin-right: 65px !important;}
.mR70 {margin-right: 70px !important;}
.mR75 {margin-right: 75px !important;}
.mR80 {margin-right: 80px !important;}
.mR85 {margin-right: 85px !important;}
.mR90 {margin-right: 90px !important;}
.mR95 {margin-right: 95px !important;}
.mR100 {margin-right: 100px !important;}

/* margin top class builder */
.mT0 {margin-top: 0px !important;}
.mT1 {margin-top: 1px !important;}
.mT2 {margin-top: 2px !important;}
.mT0i {margin-top: 0px !important;}
.mT3 {margin-top: 3px !important;}
.mT4i {margin-top: 4px !important;}
.mT5 {margin-top: 5px !important;}
.mT6 {margin-top: 6px !important;}
.mT6i {margin-top: 6px !important;}
.mT7 {margin-top: 7px !important;}
.mT8 {margin-top: 8px !important;}
.mT10 {margin-top: 10px !important;}
.mT12 {margin-top: 12px !important;}
.mT12i {margin-top: 12px !important;}
.mT15 {margin-top: 15px !important;}
.mT18 {margin-top: 18px !important;}
.mT20 {margin-top: 20px !important;}
.mT25 {margin-top: 25px !important;}
.mT25i {margin-top: 25px !important;}
.mT30 {margin-top: 30px !important;}
.mT35 {margin-top: 35px !important;}
.mT36 {margin-top: 36px !important;}
.mT40 {margin-top: 40px !important;}
.mT45 {margin-top: 45px !important;}
.mT50 {margin-top: 50px !important;}
.mT55 {margin-top: 55px !important;}
.mT56 {margin-top: 56px !important;}
.mT60 {margin-top: 60px !important;}
.mT62 {margin-top: 62px !important;}
.mT65 {margin-top: 65px !important;}
.mT70 {margin-top: 70px !important;}
.mT75 {margin-top: 75px !important;}
.mT77 {margin-top: 77px !important;}
.mT80 {margin-top: 80px !important;}
.mT85 {margin-top: 85px !important;}
.mT90 {margin-top: 90px !important;}
.mT95 {margin-top: 95px !important;}
.mT100 {margin-top: 100px !important;}
.mT130 {margin-top: 130px !important;}

/* margin bottom class builder */
.mB0 {margin-bottom: 0px !important;}
.mB0i {margin-bottom: 0px !important;}
.mB2 {margin-bottom: 2px !important;}
.mB5 {margin-bottom: 5px !important;}
.mB10 {margin-bottom: 10px !important;}
.mB12 {margin-bottom: 12px !important;}
.mB15 {margin-bottom: 15px !important;}
.mB20 {margin-bottom: 20px !important;}
.mB22i {margin-bottom: 22px !important;}
.mB25 {margin-bottom: 25px !important;}
.mB25i {margin-bottom: 25px !important;}
.mB30 {margin-bottom: 30px !important;}
.mB35 {margin-bottom: 35px !important;}
.mB40 {margin-bottom: 40px !important;}
.mB45 {margin-bottom: 45px !important;}
.mB50 {margin-bottom: 50px !important;}
.mB55 {margin-bottom: 55px !important;}
.mB60 {margin-bottom: 60px !important;}
.mB65 {margin-bottom: 65px !important;}
.mB70 {margin-bottom: 70px !important;}
.mB75 {margin-bottom: 75px !important;}
.mB80 {margin-bottom: 80px !important;}
.mB85 {margin-bottom: 85px !important;}
.mB90 {margin-bottom: 90px !important;}
.mB95 {margin-bottom: 95px !important;}
.mB100 {margin-bottom: 100px !important;}

/* padding class builder */
.pA0 {padding: 0px !important;}
.pA0i {padding: 0px !important;}
.pA5 {padding: 5px !important;}
.pA5i {padding: 5px !important;}
.pA8 {padding: 8px !important;}
.pA8i {padding: 8px !important;}
.pA10 {padding: 10px !important;}
.pA10i {padding: 10px !important;}
.pA15 {padding: 15px !important;}
.pA15i {padding: 15px !important;}
.pA20 {padding: 20px !important;}
.pA25 {padding: 25px !important;}
.pA30 {padding: 30px !important;}
.pA35 {padding: 35px !important;}
.pA40 {padding: 40px !important;}
.pA45 {padding: 45px !important;}
.pA50 {padding: 50px !important;}
.pA55 {padding: 55px !important;}
.pA60 {padding: 60px !important;}
.pA65 {padding: 65px !important;}
.pA70 {padding: 70px !important;}
.pA75 {padding: 75px !important;}
.pA80 {padding: 80px !important;}
.pA85 {padding: 85px !important;}
.pA90 {padding: 90px !important;}
.pA95 {padding: 95px !important;}
.pA100 {padding: 100px !important;}

/* padding top class builder */
.pT0 {padding-top: 0px !important;}
.pT2 {padding-top: 2px !important;}
.pT3 {padding-top: 3px !important;}
.pT5 {padding-top: 5px !important;}
.pT7 {padding-top: 7px !important;}
.pT8 {padding-top: 8px !important;}
.pT10 {padding-top: 10px !important;}
.pT12 {padding-top: 12px !important;}
.pT15 {padding-top: 15px !important;}
.pT15i {padding-top: 15px !important;}
.pT20 {padding-top: 20px !important;}
.pT25 {padding-top: 25px !important;}
.pT30 {padding-top: 30px !important;}
.pT35 {padding-top: 35px !important;}
.pT40 {padding-top: 40px !important;}
.pT45 {padding-top: 45px !important;}
.pT50 {padding-top: 50px !important;}
.pT55 {padding-top: 55px !important;}
.pT60 {padding-top: 60px !important;}
.pT65 {padding-top: 65px !important;}
.pT70 {padding-top: 70px !important;}
.pT75 {padding-top: 75px !important;}
.pT80 {padding-top: 80px !important;}
.pT85 {padding-top: 85px !important;}
.pT90 {padding-top: 90px !important;}
.pT95 {padding-top: 95px !important;}
.pT100 {padding-top: 100px !important;}

/* padding left class builder */
.pL0 {padding-left: 0px !important;}
.pL0i {padding-left: 0px !important;}
.pL5 {padding-left: 5px !important;}
.pL7 {padding-left: 7px !important;}
.pL8 {padding-left: 8px !important;}
.pL10 {padding-left: 10px !important;}
.pL12 {padding-left: 12px !important;}
.pL15 {padding-left: 15px !important;}
.pL15i {padding-left: 15px !important;}
.pL20 {padding-left: 20px !important;}
.pL25 {padding-left: 25px !important;}
.pL30 {padding-left: 30px !important;}
.pL35 {padding-left: 35px !important;}
.pL40 {padding-left: 40px !important;}
.pL45 {padding-left: 45px !important;}
.pL50 {padding-left: 50px !important;}
.pL55 {padding-left: 55px !important;}
.pL60 {padding-left: 60px !important;}
.pL65 {padding-left: 65px !important;}
.pL70 {padding-left: 70px !important;}
.pL75 {padding-left: 75px !important;}
.pL80 {padding-left: 80px !important;}
.pL85 {padding-left: 85px !important;}
.pL90 {padding-left: 90px !important;}
.pL95 {padding-left: 95px !important;}
.pL100 {padding-left: 100px !important;}

/* padding right class builder */
.pR0 {padding-right: 0px !important;}
.pR0i {padding-right: 0px !important;}
.pR5 {padding-right: 5px !important;}
.pR8 {padding-right: 8px !important;}
.pR10 {padding-right: 10px !important;}
.pR10i {padding-right: 10px !important;}
.pR12 {padding-right: 12px !important;}
.pR15 {padding-right: 15px !important;}
.pR20 {padding-right: 20px !important;}
.pR25 {padding-right: 25px !important;}
.pR30 {padding-right: 30px !important;}
.pR35 {padding-right: 35px !important;}
.pR40 {padding-right: 40px !important;}
.pR45 {padding-right: 45px !important;}
.pR50 {padding-right: 50px !important;}
.pR55 {padding-right: 55px !important;}
.pR60 {padding-right: 60px !important;}
.pR65 {padding-right: 65px !important;}
.pR70 {padding-right: 70px !important;}
.pR75 {padding-right: 75px !important;}
.pR80 {padding-right: 80px !important;}
.pR85 {padding-right: 85px !important;}
.pR90 {padding-right: 90px !important;}
.pR95 {padding-right: 95px !important;}
.pR100 {padding-right: 100px !important;}

/* padding bottom class builder */
.pB0 {padding-bottom: 0px !important;}
.pB0i {padding-bottom: 0px !important;}
.pB5 {padding-bottom: 5px !important;}
.pB5i {padding-bottom: 5px !important;}
.pB10 {padding-bottom: 10px !important;}
.pB15 {padding-bottom: 15px !important;}
.pB15i {padding-bottom: 15px !important;}
.pB20 {padding-bottom: 20px !important;}
.pB25 {padding-bottom: 25px !important;}
.pB30 {padding-bottom: 30px !important;}
.pB35 {padding-bottom: 35px !important;}
.pB40 {padding-bottom: 40px !important;}
.pB45 {padding-bottom: 45px !important;}
.pB50 {padding-bottom: 50px !important;}
.pB55 {padding-bottom: 55px !important;}
.pB60 {padding-bottom: 60px !important;}
.pB65 {padding-bottom: 65px !important;}
.pB70 {padding-bottom: 70px !important;}
.pB75 {padding-bottom: 75px !important;}
.pB80 {padding-bottom: 80px !important;}
.pB85 {padding-bottom: 85px !important;}
.pB90 {padding-bottom: 90px !important;}
.pB95 {padding-bottom: 95px !important;}
.pB100 {padding-bottom: 100px !important;}

/* font-size class builder */
.fs8 {font-size: 8pt !important;}
.fs9 {font-size: 9pt !important;}
.fs10 {font-size: 10pt !important;}
.fs11 {font-size: 11pt !important;}
.fs12 {font-size: 12pt !important;}
.fs13 {font-size: 13pt !important;}
.fs14 {font-size: 14pt !important;}
.fs15 {font-size: 15pt !important;}
.fs16 {font-size: 16pt !important;}
.fs17 {font-size: 17pt !important;}
.fs18 {font-size: 18pt !important;}
.fs19 {font-size: 19pt !important;}
.fs20 {font-size: 20pt !important;}
.fs21 {font-size: 21pt !important;}
.fs22 {font-size: 22pt !important;}
.fs23 {font-size: 23pt !important;}
.fs24 {font-size: 24pt !important;}
.fs25 {font-size: 25pt !important;}

/* width class builder */
.w10 {width: 10px !important;}
.w15 {width: 15px !important;}
.w20 {width: 20px !important;}
.w25 {width: 25px !important;}
.w30 {width: 30px !important;}
.w35 {width: 35px !important;}
.w40 {width: 40px !important;}
.w50 {width: 50px !important;}
.w55 {width: 55px !important;}
.w60 {width: 60px !important;}
.w65 {width: 65px !important;}
.w65i {width: 65px !important;}
.w70 {width: 70px !important;}
.w70i {width: 70px !important;}
.w80 {width: 80px !important;}
.w90 {width: 90px !important;}
.w100 {width: 100px !important;}
.w120 {width: 120px !important;}
.w150 {width: 150px !important;}
.w180 {width: 180px !important;}
.w200 {width: 200px !important;}
.w212 {width: 212px !important;}
.w214 {width: 214px !important;}
.w230 {width: 214px !important;}
.w250 {width: 250px !important;}
.w300 {width: 300px !important;}
.w320 {width: 320px !important;}
.w320i {width: 320px !important;}
.w350 {width: 350px !important;}
.w400 {width: 400px !important;}
.w450 {width: 450px !important;}
.w500 {width: 500px !important;}
.w550 {width: 550px !important;}
.w600 {width: 600px !important;}
.w650 {width: 650px !important;}
.w700 {width: 700px !important;}
.w750 {width: 750px !important;}
.w800 {width: 800px !important;}
.w820 {width: 820px !important;}
.w850 {width: 850px !important;}
.w900 {width: 900px !important;}
.w950 {width: 950px !important;}
.w960 {width: 960px !important;}
.w1000 {width: 1000px !important;}

/* height class builder */
.h0 {height: 0px !important;}
.h2 {height: 2px !important;}
.h5 {height: 5px !important;}
.h10 {height: 10px !important;}
.h15 {height: 15px !important;}
.h20 {height: 20px !important;}
.h25 {height: 25px !important;}
.h30 {height: 30px !important;}
.h35 {height: 35px !important;}
.h40 {height: 40px !important;}
.h45 {height: 45px !important;}
.h50 {height: 50px !important;}
.h50i {height: 50px !important;}
.h60 {height: 60px !important;}
.h70i {height: 70px !important;}
.h80 {height: 80px !important;}
.h100 {height: 100px !important;}
.h120 {height: 120px !important;}
.h135 {height: 135px !important;}
.h150 {height: 150px !important;}
.h180 {height: 180px !important;}
.h200 {height: 200px !important;}
.h220 {height: 220px !important;}
.h250 {height: 250px !important;}
.h270 {height: 270px !important;}
.h300 {height: 300px !important;}
.h320 {height: 320px !important;}
.h350 {height: 350px !important;}
.h380 {height: 380px !important;}
.h400 {height: 400px !important;}

/* z-index class builder */
.zi0 {z-index: 0 !important;}
.zi1 {z-index: 1 !important;}
.zi2 {z-index: 2 !important;}
.zi3 {z-index: 3 !important;}
.zi4 {z-index: 4 !important;}
.zi5 {z-index: 5 !important;}
.zi6 {z-index: 6 !important;}
.zi7 {z-index: 7 !important;}
.zi8 {z-index: 8 !important;}
.zi9 {z-index: 9 !important;}
.zi10 {z-index: 10 !important;}
.zi100 {z-index: 100 !important;}
.zi1000 {z-index: 1000 !important;}

/* min-height class builder */
.mH50{min-height: 50px !important;}
.mH80{min-height: 80px !important;}
.mH100{min-height: 100px !important;}
.mH120{min-height: 120px !important;}
.mH150{min-height: 150px !important;}
.mH180{min-height: 180px !important;}
.mH190{min-height: 190px !important;}
.mH200{min-height: 200px !important;}
.mH300{min-height: 300px !important;}
.mH400{min-height: 400px !important;}
.mH500{min-height: 500px !important;}
.mH540{min-height: 540px !important;}
.mH600{min-height: 600px !important;}
.mH700{min-height: 700px !important;}
.mH800{min-height: 800px !important;}
.mH900{min-height: 900px !important;}
.mH1000{min-height: 1000px !important;}

/* min-width class builder */
.mW50{min-width: 50px !important;}
.mW80{min-width: 80px !important;}
.mW100{min-width: 100px !important;}
.mW120{min-width: 120px !important;}
.mW150{min-width: 150px !important;}
.mW180{min-width: 180px !important;}
.mW190{min-width: 190px !important;}
.mW200{min-width: 200px !important;}
.mW300{min-width: 300px !important;}
.mW400{min-width: 400px !important;}
.mW500{min-width: 500px !important;}
.mW540{min-width: 540px !important;}
.mW600{min-width: 600px !important;}
.mW700{min-width: 700px !important;}
.mW800{min-width: 800px !important;}
.mW900{min-width: 900px !important;}
.mW1000{min-width: 1000px !important;}

.maxW50{max-width: 50px !important;}
.maxW75{max-width: 75px !important;}
.maxW100{max-width: 100px !important;}
.maxH150{max-width: 150px !important;}

/* rotate */
.ro180{transform: rotate(180deg) !important;}

/* Css for border */
.bRni{border-right: none !important;}

.btn-over{
	cursor: pointer;
}
.btn-over-red:hover, .btn-over-red:hover *{
	cursor: pointer;
	color:red !important;
}
.btn-over-green:hover, .btn-over-green:hover *{
	cursor: pointer;
	color: green !important;
}
.btn-over-orange:hover,.btn-over-orange:hover *{
	cursor: pointer;
	color: #ff7b05 !important;
}
.btn-over-blue:hover,.btn-over-blue:hover *{
	cursor: pointer;
	color: blue !important;
}
.btn-color-gray{
	color: gray  !important;
}
.btn-color-red{
	color: red  !important;
}
.btn-color-green{
	color: #7ac54b !important;
}
.btn-color-blue{
	color: #428bca  !important;
}
.color-red{
	color: red;
}
.color-blue{
	color: blue;
}
.color-gray{
	color: gray;
}
.color-green{
	color: #7ac54b;
}
.color-orange{
	color: #ff7e00;
}
.color-yellow{
	color: #7b792c;
}
.color-blue-light {
	color: #3c6ab3;
}
.color-red-dark {
	color: #ca1d19;;
}

.bg-color-whitesmoke-hover:hover{
	background: whitesmoke;
}
.bg-color-snow-hover:hover{
	background: snow;
}
.bg-color-gray-hover:hover{
	background: gray;
}
.bg-color-red{
	background: red;
}
.bg-color-green{
	background: #7ac54b;
}
.bg-color-blue{
	background: blue;
}
.bg-color-orange{
	background: #ff7e00;
}

.dialog select{
	border: 1px solid #ddd;
	height: 25px;
	line-height: 25px;
	border-radius: 4px;
}
.dialog td{
	padding-right: 15px;
	padding-bottom: 5px;
}
.dialog .btn{
	line-height: 0.8;
}
.dialog .textbox{
	border-radius: 4px;
}

.inline-block-center {
	text-align: center;
	display: inline-block;
}

.form-table td{
	padding-right: 15px;
	padding-bottom: 5px;
}
.input-custom-style{
	position: relative;
	border: 1px solid #ddd;
	background-color: #fff;
	vertical-align: middle;
	display: inline-block;
	overflow: hidden;
	white-space: nowrap;
	margin: 0;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	height: 28px;
	line-height: 28px;
	padding-left: 4px;
}

.txt-right{
	text-align: right !important;;
}
.txt-left{
	text-align: left !important;;
}
.txt-center{
	text-align: center !important;;
}

/*anhnt*/

/*Dùng chung*/
.bold-i{
font-weight: bold !important;
}
.f-right {
	float: right;
}

.f-left {
	float: left;
}

.align-left-i {
	text-align: left !important;
}

.align-center-i {
	text-align: center !important;
}

.align-right-i {
	text-align: right !important;
}

.w-25 {
	width: 25%;
}

.w-50 {
	width: 50%;
}

/*Chữ kí*/
@media print {
	.table-signature span {
		display: block !important;
	}

	.table-signature input {
		display: none !important;
	}
}

.table-signature, .table-signature th {
	text-align: center;
}

.table-signature th {
	padding-bottom: 50px;
}

.table-signature i {
	font-weight: 100;
}

.table-signature span {
	display: none;
}

.table-signature input {
	border: 1px dotted;
	color: #7ac54b;
	text-align: center;
}

.table-signature {
	border: 0 !important;
}

/*report*/

.report-content {
	padding: 15px 30px;
}

.report-content table {
	width: 100%;
}

.report-content.body table td {
	border: 1px solid #000 !important;
}

.table-rows {
	border-collapse: collapse;
	border-spacing: 0;
}

.table-rows td {
	padding: 5px;
	overflow: hidden;
	word-break: normal;
	border: 1px solid black;
}

.table-rows th {
	padding: 5px;
	overflow: hidden;
	word-break: normal;
	border: 1px solid black;
	text-align: center;
	background: #ccc !important;
}
#config{
	width: 280px;
}
#config > div{
	margin: 7px 0px 0 25px;
	border: 1px solid rgba(0,0,0,0.2);
	border-radius: 4px;
	background: #fff;
}
#config input{
	border: none;
}
#config ul{
	padding: 5px 0px 5px 10px;
	margin-bottom: 0;
}
.dialog-show{
	display: none;
	padding: 10px;
	overflow-x: hidden;
}
.dialog-show table th {
	min-width: 40px;
	padding-right: 10px;
	padding-bottom: 10px;
}
.btn-small {
	display: inline-block;
	padding: 2px 12px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px;
}
.excel-show{
	display: none;
}

.dropdown-setting{
	float: right;
	padding: 6px
}
.dropdown-setting > button{
	padding: 0;
}
.dropdown-setting .icon-setting{
	font-size: 20px;
}
.dropdown-setting > ul > li{
	padding: 0 8px;
}
/*end-anhnt*/

.app-version{
	font-size: 11px;
}