<div ng-controller="B<PERSON><PERSON>ear<PERSON><PERSON>ontroller as vm" class="position-relative w-100 h-100 mh-100">
    <div class="row">
        <div class="col-md-12 d-flex">
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-options="year.id as year.title for year in vm.schoolYears"
                        ng-model="vm.filters.school_year"
                        ng-change="vm.onSchoolYearChanged()">
                </select>
            </div>
            <div class="form-group m-0">
                <select class="form-control custom-select"
                        ng-change="vm.onCourseChanged()"
                        ng-model="vm.filters.course_id">
                    <option ng-value="0">Chọn lớp</option>
                    <optgroup ng-repeat="grade in vm.courses" label="{{grade.name}}">
                        <option ng-repeat="course in grade.courses.data" ng-value="course.id">{{course.name}}</option>
                    </optgroup>
                </select>
            </div>
        </div>
    </div>

    <div class="mt-2 h-100 table-responsive table-bmi table-student-bmi-yearly-history">
        <table class="table table-striped table-hover">
            <thead>
                <tr class="table_header">
                    <th rowspan="2" class="student-bmi-yearly-history--stt">STT</th>
                    <th rowspan="2" class="student-bmi-yearly-history--full-name">Họ tên</th>
                    <th rowspan="2" class="student-bmi-yearly-history--birthday">Ngày sinh</th>
                    <th colspan="2" ng-repeat="month in vm.ACTIVE_MONTHS" class="student-bmi-yearly-history--month">T{{month}}</th>
                </tr>
                <tr class="table_header">
                    <th ng-repeat-start="month in vm.ACTIVE_MONTHS" class="student-bmi-yearly-history--month--weight">CN</th>
                    <th ng-repeat-end class="student-bmi-yearly-history--month--height">CC</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-if="vm.students.length > 0" class="" ng-repeat="(index, student) in vm.students">
                    <td class="text-center student-bmi-yearly-history--stt">{{$index+1}}</td>
                    <td class="student-bmi-yearly-history--full-name">
                        {{student.full_name}}</td>
                    <td class="student-bmi-yearly-history--birthday text-center">{{student.birthday}}</td>
                    <td ng-repeat-start="(key, month) in vm.ACTIVE_MONTHS" class="student-bmi-yearly-history--month--weight text-center">
                        {{vm.getWeightOfMonth(index, month)}}
                    </td>
                    <td ng-repeat-end class="student-bmi-yearly-history--month--height text-center">
                        {{vm.getHeightOfMonth(index, month)}}
                    </td>
                </tr>
                </tbody>
            </table>
    </div>

    <div class="mt-2 d-flex justify-content-end">
        <button type="button"
                ng-click="vm.downloadExcel()"
                class="btn btn btn-info waves-effect waves-light"
                ng-disabled="vm.students.length === 0">
            <i class="mdi mdi-download"></i> Xuất danh sách
        </button>
    </div>
</div>
