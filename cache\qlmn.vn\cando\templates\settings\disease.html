<div ng-controller="SettingDiseaseController as vm" class="position-relative w-100 h-100 mh-100">
    <div class="row">
        <div class="col" style="font-size: 18px; font-weight: 600; color: #0870e0; padding-top: 5px;">DS bệnh cần theo dõi của: {{vm.unit_name}}</div>
        <div class="col text-right">
            <button type="button" class="btn btn-info" ng-click="vm.is_admin?vm.showCreateForm():vm.showAddForm()">Thêm mới</button>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 d-flex">
            <div class="form-group m-0">
                <select class="form-control custom-select"
                        ng-model="vm.grade_selected"
                        ng-options="grade.id as grade.label for grade in vm.grades"
                        ng-change="vm.onGradeChanged()">
                </select>
            </div>
        </div>
    </div>
    <div class="h-100 table-responsive mt-2 table-bmi table-list-disease">
        <table class="jsgrid-table table table-striped table-hover">
            <thead>
            <tr class="text-center">
                <th style="width: 80px;">STT</th>
                <th style="width: 300px;">Tên bệnh</th>
                <th style="width: 300px;">Mã bệnh</th>
                <th style="width: 300px;">Khối</th>
                <th>Thao tác</th>
            </tr>
            </thead>
            <tbody>
            <tr ng-if="vm.diseases.length === 0 && vm.loaded">
                <td colspan="4" class="text-center">Không có dữ liệu</td>
            </tr>
            <tr ng-if="vm.loaded === undefined || vm.loading">
                <td colspan="4" class="text-center">Đang tải dữ liệu...</td>
            </tr>
            <tr ng-repeat="disease in vm.diseases">
                <td class="text-center">{{ $index + 1 }}</td>
                <td>{{ disease.name }}</td>
                <td>{{ disease.code }}</td>
                <td>{{ vm.gradesInDisease(disease.grade_ids) }}</td>
                <td>
                    <button class="btn btn-sm btn-info" ng-click="vm.showEditForm(disease)">Sửa</button>
                    <button class="btn btn-sm btn-danger" ng-click="vm.showConfirmDeleteModal(disease)">Xóa</button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<script type="text/ng-template" id="update_disease_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Chỉnh sửa: {{ $ctrl.diseaseOrigin.name }}</h3>
    </div>
    <form name="$ctrl.diseaseForm" ng-submit="$ctrl.save()" autocomplete="off" spellcheck="false">
        <div class="modal-body">
            <div class="form-group">
                <label for="disease_name">Tên bệnh <span class="text-danger"> (*)</span></label>
                <input class="form-control" ng-disabled="!vm.is_admin" id="disease_name" name="name" ng-model="$ctrl.disease.name" required ng-maxlength="100">
            </div>
            <div class="form-group">
                <label for="disease_code">Mã bệnh <span class="text-danger"> (*)</span></label>
                <input class="form-control" ng-disabled="!vm.is_admin" id="disease_code" name="code" ng-model="$ctrl.disease.code" required ng-maxlength="100">
            </div>
        </div>
        <div class="modal-body row">
            <div class="col-md-12">
                <label><b>Chọn khối</b> <span class="text-danger"></span></label>       
            </div>
            <div class="col-md-6" ng-repeat="item in $ctrl.grades">
                <label class="checkbox-inline">
                    <input type="checkbox" ng-checked="$ctrl.is_checked(item.id)" ng-value="item.id" name="grades_edit" style="position:unset;opacity:1">
                    <text ng-bind="item.label" class="ng-binding"></text>
                </label>
                
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit" ng-disabled="$ctrl.diseaseForm.$invalid">Lưu</button>
        </div>
    </form>
</script>

<script type="text/ng-template" id="create_disease_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Thêm mới</h3>
    </div>
    <form name="$ctrl.diseaseForm" ng-submit="$ctrl.save()" autocomplete="off" spellcheck="false">
        <div class="modal-body">
            <div class="form-group">
                <label for="disease_name">Tên bệnh <span class="text-danger"> (*)</span></label>
                <input class="form-control" id="disease_name" name="name" ng-model="$ctrl.disease.name" required ng-maxlength="100">
            </div>
            <div class="form-group">
                <label for="disease_code">Mã bệnh <span class="text-danger"> (*)</span></label>
                <input class="form-control" id="disease_code" name="code" ng-model="$ctrl.disease.code" required ng-maxlength="100">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit" ng-disabled="$ctrl.diseaseForm.$invalid">Lưu</button>
        </div>
    </form>
</script>

<script type="text/ng-template" id="add_disease_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Chọn danh sách bệnh để thêm</h3>
    </div>
    <form name="$ctrl.diseaseForm" ng-submit="$ctrl.save()" autocomplete="off" spellcheck="false">
        <div class="modal-body row">
            <div class="col-md-6" ng-repeat="(key,disease) in $ctrl.admin_diseases">
                <label class="checkbox-inline">
                    <input type="checkbox" ng-value="key" name="disease" style="position:unset;opacity:1">
                    <text ng-bind="disease.name" class="ng-binding">Tên bệnh</text> (<text ng-bind="disease.code" class="ng-binding">Mã bệnh</text>)
                </label>
            </div>
            
        </div>
        <div class="modal-body row">
            <div class="col-md-12">
                <label><b>Chọn khối</b> <span class="text-danger"></span></label>       
            </div>
            <div class="col-md-6" ng-repeat="item in $ctrl.grades">
                <label class="checkbox-inline">
                    <input type="checkbox" ng-value="item.id" name="grades" style="position:unset;opacity:1">
                    <text ng-bind="item.label" class="ng-binding"></text>
                </label>
                
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit" ng-disabled="$ctrl.diseaseForm.$invalid">Lưu</button>
        </div>
		<style>
			.modal-dialog, .modal-content{
				max-width:600px !important;
			}
		</style>
    </form>
</script>

<script type="text/ng-template" id="delete_disease_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xác nhận xóa</h3>
    </div>
    <div class="modal-body">
        <div class="alert alert-danger mb-0">
            <p class="m-0 p-0">Bạn đang thực hiện xóa dữ liệu bệnh <em></em>. Bạn có chắc chắc muốn xóa dữ liệu. Mọi dữ liệu sau khi xóa đều không thể khôi phục lại.</p>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
        <button class="btn btn-danger" type="button" ng-click="$ctrl.delete()">Xóa</button>
    </div>
</script>
