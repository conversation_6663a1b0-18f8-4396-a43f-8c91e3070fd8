$.supplier = {
    module: 'supplier',
    id: '', /*Mã project*/
    init: function(id,project) {
    	var self = this;
    	self.id = id;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*<PERSON><PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
                { title:'Tên nhà cung cấp', field:'name', width:150, sortable:true },
                { title:'Số mã hóa', field:'id', width:80, sortable:true },
                { title:'Địa chỉ', field:'address', width:150, sortable:true },
                { title:'Chủ cửa hàng', field:'boss', width:150, sortable:true },
                { title:'<PERSON><PERSON><PERSON><PERSON> giao hàng', field:'shipper', width:150, sortable:true },
                { title:'<PERSON><PERSON><PERSON><PERSON> thoại', field:'phone', width:120, sortable:true },
				{ title:'CMT/CCCD', field:'cmt_cccd', width:120, sortable:true }
            ]],
            {
                onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm(rowData);
                }, onLoadSuccess:function(data){
                    if($CFG.is_gokids==1) {
                        $('.datagrid-view').height($('.datagrid-view').height() - 30);
                        $('.datagrid-body').height($('.datagrid-body').height() - 30);
                    }
                },
                pageSize:100
            }
        );
    }, showAddForm: function(callback) { 
    	var self = this;
        var $scope = null;
        $.dm_datagrid.showAddForm(
			{
				module: $CFG.project+'/'+self.module,
				action:'add',
				title:'Thêm mới',
				content: function(element){
					loadForm($CFG.project+'/'+self.module,'add', {}, function(resp){
                        self.angular(element,resp,function(scope){
                            
                        })
						// $(element).html(resp);
					})
				}
			},
			function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    }, showEditForm: function(row) {
    	var self = this;
        if(!row){
    	   var row = $("#tbl_"+self.module).datagrid('getSelected');
        }
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
					content: function(element){
						loadForm($CFG.project+'/'+self.module,'edit', {id: row.id, dataType: 'json'}, function(resp){
                            self.angular(element,resp.html,function(scope){
                                scope.supplier || (scope.supplier = {});
                                scope.supplier = resp.row;
                                scope.supplier.old_id = resp.row.id;
                            })
						})
					}
				},
				function(resp){
                    $("#tbl_"+self.module).datagrid('unselectAll');
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Hãy chọn một dòng!');
	    }
       
    }, angular: function(element,resp,callback,dialogRef){
        var form = '<div style="height:100%;">'+resp+'</div>';
            // $(element).html(form);
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = $CFG.dialog_captcha('delete_supplier');
        var msg = '<div style = "font-size: 14px">Nếu đồng ý xóa, bạn sẽ ko thể khôi phục được các Nhà cung cấp đã xóa! Hãy chắc chắn bạn muốn xóa?</div>' + captchaForm;
        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="delete_supplier_captcha"]').val();
                console.log(captcha);
                process($CFG.project+'/'+self.module+'/del',{ids: ids, captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.supplier.del();
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    }
}
