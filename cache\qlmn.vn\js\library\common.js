$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
    }
});

var clone = function (obj) {
    if (typeof obj === 'object') {
        return JSON.parse(JSON.stringify(obj));
    }
};

var loading = function (hide) {
    if (hide) {
        $("#loading").css('display', 'none');
    } else {
        $("#loading").css('display', 'block');
    }
};

var buildTree = function (rows, parent) {
    parent = parent || null;
    var tree = {};
    Object.keys(rows).forEach(function (i) {
        var row = rows[i];
        if (row['parent'] == parent || (row['parent']==0 && parent==null)) {
            var merge_id = row['id'];
            if (row['merge_id'] != undefined) {
                merge_id = row['merge_id'];
            }
            tree[merge_id] = row;
            tree[merge_id]['children'] = buildTree(rows, row['id']);
        }
    });
    return tree;
};

var getURLParameter = function (sParam) {
    var sPageURL = window.location.search.substring(1);
    var sURLVariables = sPageURL.split('&');
    for (var i = 0; i < sURLVariables.length; i++) {
        var sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] == sParam) {
            return sParameterName[1];
        }
    }
};