(function StudentModel(models) {
    var MALE = 1;
    var FEMALE = 0;

    /**
     *
     * @param props
     * @constructor
     *
     * @property {String} first_name
     * @property {String} last_name
     * @property {Number} gender
     * @property {String} birth_date
     * @property {Number} month_old
     * @property {Number} weight
     * @property {Number} height
     * @property {Number} bmi_m
     * @property {Number} bmi_l
     * @property {Number} bmi_s
     */
    function Student(props) {
        var self = this;
        self.original = {};

        props.weight = props.weight || '';
        props.height = props.height || '';
        props.bmi_m = props.bmi_m || '';
        props.bmi_l = props.bmi_l || '';
        props.bmi_s = props.bmi_s || '';
        props.note = props.note || '';

        _.assign(self, props);
        _.assign(self.original, props);

        self.bmi = self.bmi ? self.bmi.toFixed(1) : '';

        self.isFemale = function () {
            return self.gender === FEMALE;
        };

        self.calculateBmi = function calculateBmi(check_zscore) {
            if (self.weight <= 0 || self.height <= 0) {
                return '';
            }
            var weight = +self.weight;
            var height = +self.height;

            var bmi = (weight / height / height) * 10000;

            if(self.month_old >= 24 && self.month_old <= 60 && check_zscore){
                if (self.bmi_m == '' || self.bmi_l == '' || self.bmi_s == '') {
                    return '';
                }
                var z_score = ( Math.pow( bmi / self.bmi_m , self.bmi_l) - 1 ) / ( self.bmi_l * self.bmi_s );
                return _.isNaN(z_score) ? '' : z_score.toFixed(1);
            }
            return _.isNaN(bmi) ? '' : bmi.toFixed(1);
        };

        self.weightChanged = function () {
            return +self.weight !== +self.original.weight;
        };

        self.resetWeight = function () {
            self.weight = self.original.weight;
        };

        self.heightChanged = function () {
            return +self.height !== +self.original.height;
        };

        self.resetHeight = function () {
            self.height = self.original.height;
        };

        self.noteChanged = function () {
            return self.note != self.original.note;
        };

        self.resetNote = function () {
            self.note = self.original.note;
        };
    }

    models.Student = Student;
})(window.models = window.models || {});
