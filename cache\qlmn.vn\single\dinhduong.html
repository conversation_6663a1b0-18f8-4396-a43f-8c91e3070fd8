<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
    <meta charset="utf-8">
    <base href="http://localhost:3000/single/">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
    <!-- Fonts -->
    <link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="http://localhost:3000/build/build/css/style.min-b0affb6d2f.css?v=3">
    <link rel="stylesheet" href="http://localhost:3000/css/common.css?=1268520273">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!-- 1. Load libraries -->
    <!-- Polyfill for older browsers -->
    <!-- 2. Configure SystemJS -->
    <!-- Google Tag Manager -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-109824810-3"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'UA-109824810-3');
    </script>
    <script type="text/javascript">
        var allowed_provinces = '35_79';
		var allowed_province_arr = allowed_provinces.split('_');
        $CFG = {
			co_cau: 'cocau_chuan',
            co_cau_from: '01/01/2010',
            local: {base_url: "http://localhost:3000/js/admin"},
            remote: {base_url: "http://localhost:3000"},
            template: {base_url: "http://localhost:3000/templates"},
            balance_api: 'http://localhost:3000/api/balancer/calculate',
			base_http_url: 'http://localhost:3000',
            project: 'dinhduong',
            namhoc: '2024',
            round_number_config: "",
            digit_grouping_char: "",
            spinController: {},
            unit_id: parseInt('51461'),
            admin: parseInt('1'),
            administrator: parseInt('0'),
			is_payment_gate_on: parseInt('0'),
            self_id: '',
			user_id: '190299',
            username: 'd.hd.mnquangvt',
            level: '4',
			school_level: parseInt('0'),
            province: '97',
			district: '97_974',
            user: {
                name: '',
                phone: '',
                email: '',
            },
            is_principal: parseInt('1'),
            school_point: +'1',
            school_points: +'1',
            school_point_together: parseInt('0'),
            is_vin: +'false',
            record_delete_show_captcha: +'2',
            dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin-top: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			marked_close_notifier: '',
            api_base_url_ura: 'https://ura.edu.vn/api/v1/',
            is_enabled_ura: '0',
            is_show_plg_rank: parseInt('0'),
            is_show_supplier_sign: parseInt('1'),
            pkc_show_bangkecho: parseInt('1'),
            is_sync_csdl_nganh: parseInt('0'),
			allowed_provinces: allowed_province_arr,
            help_base_url: "https://storage.ura.edu.vn"
        };
    </script>
    <script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/library/moment.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/socket.io.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/load-image.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/canvas-to-blob.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/textAngular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/build/build/js/vendor.min-77199f347f.js"></script>
                        <script type="text/javascript" src="http://localhost:3000/js/common.js?_=1900752369"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/init_angular.js?_=325400468"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/my-angular.js?_=887568248"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/main-angular.js?_=1293739416"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/balance_money.js?_=356642219"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/balance.js?_=1540998430"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/index.js?_=2118512724"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/payout.js?_=114122284"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/export.js?_=676187516"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/receipt.js?_=106806442"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_adjust-controller.js?_=1229934396"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_planning-controller.js?_=746632896"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_adjust/total-group-meal-controller.js?_=1275240059"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/library.js?=1071893733"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
    <script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
</head>
<body ng-app="angular_app" ng-controller="appController">
<div class="full-container" ng-controller="mainContentController" id="mainContentController">
    <div class="fix-main">
        <div class="header">
            <div class="img-tree"><img src="http://localhost:3000/css/dinhduong/images/tree.png"></div>
            <div class="logo-kh" ng-show="menu.page=='index'">
            <!-- <img src="http://localhost:3000/css/dinhduong/images/logo_dd_new2.png"> -->
            </div>
            <div class="school-year school-year-kh">
                <div role="navigation" class="navbar-buttons navbar-header">
                    <ul class="nav ace-nav">
                                            <li class="light-blue">
                                                                                                    <li class="light-blue">
                                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                    <i class="fa fa-cubes"></i>
                                    <b>Phân hệ</b>
                                    <i class="ace-icon fa fa-caret-down"></i>
                                </a>
                                <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                                                                                                                                                                    <li class="item-home">
                                                <a href="/single/cando" target="_blank">
                                                    <img width="18px" height="18px" class="swivel-img" src="http://localhost:3000/images/item_cando.png">
                                                    C&acirc;n đo
                                                </a>
                                            </li>
                                                                                                                                                                                                                                                                                                        <li class="item-home">
                                                <a href="http://localhost:3000/dinhduong" target="_blank">
                                                    <img width="18px" height="18px" class="swivel-img" src="http://localhost:3000/images/item_ctbt.png">
                                                    C&ocirc;ng t&aacute;c b&aacute;n tr&uacute;
                                                </a>
                                            </li>
                                                                                                                                                                                                                                                                                                                <li class="item-home" ng-click="detailSchool();">
                                            <a href="javascript: void(0);">
                                                <img width="18px" height="18px" class="swivel-img" src="http://localhost:3000/images/item_bctk.png">
                                                Báo cáo thống kê
                                            </a>
                                        </li>
                                                                    </ul>
                            </li>
                                                <li class="light-blue">
                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
	                                <span class="">
	                                    <strong>2024-2025</strong>
	                                </span>
                                <i class="ace-icon fa fa-caret-down"></i>
                            </a>
                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                                                                    <li class="option-schoolyear ">
                                        <a href="javascript: void(0);"
                                            onclick="$.selectSchoolYear('2022','dinhduong')" >
                                            <span class="glyphicon glyphicon-ok-circle"
                                                  style="width: 20px;"></span>
                                            2022-2023
                                        </a>
                                    </li>
                                                                    <li class="option-schoolyear ">
                                        <a href="javascript: void(0);"
                                            onclick="$.selectSchoolYear('2023','dinhduong')" >
                                            <span class="glyphicon glyphicon-ok-circle"
                                                  style="width: 20px;"></span>
                                            2023-2024
                                        </a>
                                    </li>
                                                                    <li class="option-schoolyear active">
                                        <a href="javascript: void(0);"
                                           >
                                            <span class="glyphicon glyphicon-check"
                                                  style="width: 20px;"></span>
                                            2024-2025
                                        </a>
                                    </li>
                                                                    <li class="option-schoolyear ">
                                        <a href="javascript: void(0);"
                                            onclick="$.selectSchoolYear('2025','dinhduong')" >
                                            <span class="glyphicon glyphicon-ok-circle"
                                                  style="width: 20px;"></span>
                                            2025-2026
                                        </a>
                                    </li>
                                                                    <li class="option-schoolyear ">
                                        <a href="javascript: void(0);"
                                            onclick="$.selectSchoolYear('2026','dinhduong')" >
                                            <span class="glyphicon glyphicon-ok-circle"
                                                  style="width: 20px;"></span>
                                            2026-2027
                                        </a>
                                    </li>
                                                            </ul>
                        </li>
                        <li class="light-blue">
                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
	                                <span class="user-info">
	                                    <strong style="color: #3b5844;">
	                                    Tài khoản
	                                    </strong>
	                                </span>
                                <i class="ace-icon fa fa-caret-down"></i>
                            </a>
                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                                <li ng-click="detailUserForm();">
                                    <a href="javascript: void(0);">
                                        <span class="glyphicon glyphicon-user"></span>
                                        d.hd.mnquangvt
                                        (<span id="onlineTogether">  </span>)
                                    </a>
                                </li>
                                                                                                    <li class="li-admin">
                                        <a href="http://localhost:3000/admin"><i style="color: #363636;" title="Quản trị" class="glyphicon glyphicon-cog"></i>
                                            Quản trị
                                        </a>
                                    </li>
                                    <li class="li-admin">
                                        <a href="http://localhost:3000/admin/lock_module"><i style="color: #363636; color:yellowgreen; font-size: 15px;" title="Khóa/mở chức năng" class="fa fa-unlock pointer fs12"></i>
                                            Khóa/mở chức năng
                                        </a>
                                    </li>
                                                                                                <li>
                                    <a href="javascript: void(0);" ng-click="detailUserSignForm();">
                                        <span class="glyphicon glyphicon-edit"></span>
                                        Thông tin chữ kí ảnh, ký số
                                    </a>
                                </li>
                                <li>
                                    <a href="javascript: void(0);" ng-click="detailUserSignReport();">
                                        <span class="glyphicon glyphicon-edit"></span>
                                        Ký báo cáo
                                    </a>
                                </li>
                                                                									
									<li>
										<a target="_blank" href="http://localhost:3000/report/dashboard/index">
											<span class="glyphicon glyphicon-signal"></span>
											Trang thống kê
										</a>
									</li>
								                                <li>
                                    <a href="javascript: void(0);" onclick="form_edit_pass()">
                                        <span class="glyphicon glyphicon-lock"></span>
                                        Đổi mật khẩu
                                    </a>
                                </li>
                                <li class="divider"></li>
                                <li>
                                    <a href="http://localhost:3000/logout" tabindex="-1">
                                        <i class="ace-icon fa fa-power-off" style="color: #ff0000;"></i>
                                        Đăng xuất
                                    </a>
                                </li>
                                <li class="divider"></li>
                                <li>
                                    <a href="#version" tabindex="-1">
                                    <i class="ace-icon fa fa-info-circle" style="color: #000000;"></i>
                                    Phiên bản 2535ad23.70
                                    </a>
                                </li>
                            </ul>
                        </li>
						                        <li class="light-blue">
                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                <span class=""><strong style="color: #3b5844;">Hướng dẫn</strong>
                                    <img title="Hướng dẫn" style="width:17px; margin-top:-2px;"
                                         src="http://localhost:3000/css/dinhduong/images/support.gif">
                                </span>
                            </a>
                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                                <li><a target="_blank" href="http://localhost:3000/maps" class="clrLink">Sơ đồ chức năng</a></li>
                                <li>
                                    <a target="_blank" class="clrLink"
                                       href="http://localhost:3000/css/dinhduong/files/QuyTrinhThucHien.pdf?v=2">
                                        Quy trình thực hiện
                                    </a>
                                </li>
                                                                <li>
                                    <a target="_blank" class="clrLink"
                                       href="http://localhost:3000/css/dinhduong/files/dinhluong100tre.pdf?v=3">
                                        Định lượng 100 trẻ
                                    </a>
                                </li>
                                                                    <li>
                                        <a target="_blank" class="clrLink"
                                           href="http://localhost:3000/css/dinhduong/files/HDSD-KHAUPHANDINHDUONG-RUTGON.pdf?v=1">
                                            HDSD - Dinh dưỡng
                                        </a>
                                    </li>
                                    <li>
                                        <a target="_blank" class="clrLink"
                                           href="https://docs.google.com/document/d/1-SuV6PQaBzBV7AeIIxSx-oT5CPiLPXfw/edit?tab=t.0">
                                            HDSD - Thu chi
                                        </a>
                                    </li>
									<li>
                                        <a target="_blank" class="clrLink"
                                           href="http://localhost:3000/cando/assets/files/HDSD-CANDO.pdf?t=1">
                                            HDSD - Cân Đo
                                        </a>
                                    </li>
                                    <li>
                                        <span style="padding-left: 20px;color: #333333;">Video dinh dưỡng</span>
                                        <ul class="ul-child-help">
                                            <li>
                                                <a target="_blank" href="https://www.youtube.com/watch?v=TpEEZruzcyw">
                                                    1. Đăng nhập
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank" href="https://www.youtube.com/watch?v=ac0ucwZIM3A">
                                                    2. Tạo món ăn
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank" href="https://www.youtube.com/watch?v=fsFD3kkGun0">
                                                    3. Tạo thực đơn mẫu
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank" href="https://www.youtube.com/watch?v=oluxYpqImLU">
                                                    4. Nhập kho
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   href="https://www.youtube.com/watch?v=svUJ0E6LVos">
                                                    5. Cân đối khẩu phần
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   href="https://www.youtube.com/watch?v=A8L5UyVCnn0">
                                                    6. Biểu mẫu thống kê
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span style="padding-left: 20px;color: #333333;">Video thu chi</span>
                                        <ul class="ul-child-help">
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/taokhoilop.mp4'}}">
                                                    1. Tạo khối lớp
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/themhocsinh.mp4'}}">
                                                    2. Thêm học sinh
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/cauhinhngaynghi.mp4'}}">
                                                    3. Cấu hình ngày nghỉ
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/danhmuckhoanthu.mp4'}}">
                                                    4. Danh mục khoản thu
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/thietlapkhoanthu.mp4'}}">
                                                    5. Thiết lập khoản thu
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/diemdanhhocsinh.mp4'}}">
                                                    6. Điểm danh học sinh
                                                </a>
                                            </li>
                                            <li>
                                                <a target="_blank"
                                                   ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/thuphi_baocao.mp4'}}">
                                                    7. Thu phí - Báo cáo
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                                                <li>
                                    <a href="http://localhost:3000/css/dinhduong/files/UltraViewer.exe" class="clrLink">
                                        Tải phần mềm UltraViewer
                                    </a>
                                </li>
                                <li>
                                    <a href="https://download.anydesk.com/AnyDesk.exe" class="clrLink" target="_blank">
                                        Tải phần mềm AnyDesk
                                    </a>
                                </li>
                                                                <li ng-if="$CFG.is_payment_gate_on">
                                    <a href="https://docs.google.com/document/d/1ezX1lk0aeTJOR6NNekMCxloa3TNXPgDw/edit" class="clrLink" target="_blank">
                                       <b>HƯỚNG DẪN THANH TOÁN HỌC PHÍ PMS QUA APP VIETTEL MONEY</b>
                                    </a>
                                </li>
                                                            </ul>
                        </li>
												<li class="light-blue"><div id="div_ura_notification"></div></li>
                        <li class="li-home-image">
                            <a href="http://localhost:3000"><img title="Trở về trang chủ"
                                                       src="http://localhost:3000/css/dinhduong/images/logo_pms_new.png"></a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="content" style="">
            <div class="main-content" style="margin-top:10px;">
                <div class="body-main{{ (menu.page == 'children'?' show-view':'') }}" style="">
                    <div id="header-title" class="header-title" ng-include="menu.templates.group"
                         ng-show="menu.page!='index'">
                        Menu trong trang chính
                    </div>
                    <div class="angular-view-container" ng-view="" ng-show="menu.page!='index'">
                        <div style="width: 270px; margin: auto;margin-top: 250px; color: orange; font-size: 30px;">
                            Đang xử lý ...
                        </div>
                    </div>
                    <div class="in-main-content {{menu.page=='index'?'in-main':'in-main-after'}}"
                         ng-show="menu.page=='index'" style="margin-top: 50px !important;">
                        <!-- <div class="title-dd" ng-show="menu.page=='index'"><p>Quản lý dinh dưỡng</p></div> -->
                        <div ng-include="menu.templates.top.index">
                            Menu chính
                        </div>
                    </div>
                    <div ng-include="menu.templates.bottom[menu.page]"
                         style="position: absolute;bottom: 0px;width: 100%;">
                        Menu chân trang
                    </div>
                </div>
                <div class="mouse-mc">
                    <img src="http://localhost:3000/css/dinhduong/images/mouse.png">
                </div>
            </div>
        </div>
        <div class="problem-view-container">
            <div class="bottom-show {{menu.page=='index'?'':'bottom-show-after'}}" onclick="bottomShow()">
                <img title="Những vấn đề thường gặp" src="http://localhost:3000/css/dinhduong/images/qs.png">
                <p>Những vấn đề thường gặp</p>
            </div>
            <div class="bottom-content">
                <div class="in-bc">
                    <div class="menu-bc">
                        <div class="title-menu-bc">
                            <i class="bottom-hidden glyphicon glyphicon-remove-circle" title="Đóng"></i>
                            <h4>Những vấn đề thường gặp</h4>
                        </div>
                        <div class="main-menu-bc">
                            <ul>
                                <li><a href="">Kho</a></li>
                                <li><a href="">Thực đơn</a></li>
                                <li><a href="">Tính tiền ăn</a></li>
                                <li><a href="">Quản lý thu chi</a></li>
                                <li><a href="">Quản lý học sinh</a></li>
                                <li><a href="">Danh mục</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="content-bc">

                    </div>
                </div>
            </div>
        </div>

                    <div class="quy-trinh">
                <div class="content-qt">
                    <div class="title-qt">
                        <h5>quy trình rút gọn thao tác phần mềm khẩu phần dinh dưỡng</h5>
                        <label id="lb-tudong" class="checkbox-inline"><input type="checkbox" id="tudonghien12"
                                                                             name="tudonghien12"
                                                                             onchange="tudonghien(this)">Tự động hiện
                            lần sau</label>
                    </div>
                    <div class="main-qt">
                        <div class="top-main-qt">
                            <div class="title-main-qt">
                                <h5>Thao tác thực hiện 1 lần</h5>
                                <p>Hãy thực hiện bước này khi mới <span>sử dụng phần mềm lần đầu </span>bạn nhé. Khâu
                                    này giúp chúng ta ...</p>
                            </div>
                            <div class="content-main-qt content-main-tt">
                                <div class="item-main-tt">
                                    <a href="http://localhost:3000/single/dinhduong/norm" onclick="hideqt()">
                                        <div class="top-item-tt">
                                            <img src="http://localhost:3000/css/dinhduong/images/dichvu.png">
                                        </div>
                                        <div class="bottom-item-tt">
                                            <p class="number-item-tt">
                                                1
                                            </p>
                                            <p class="content-item-tt">
                                                Chọn Cơ cấu dinh dưỡng phù hợp với tiền ăn của trường
                                            </p>
                                        </div>
                                    </a>
                                </div>

                                <div class="item-main-tt">
                                    <a href="http://localhost:3000/single/dinhduong/dish" onclick="hideqt()">
                                        <div class="top-item-tt">
                                            <img src="http://localhost:3000/css/dinhduong/images/monan.png">
                                        </div>
                                        <div class="bottom-item-tt">
                                            <p class="number-item-tt">
                                                2
                                            </p>
                                            <p class="content-item-tt">
                                                Tạo món ăn cho trường
                                            </p>
                                        </div>
                                    </a>
                                </div>
                                <div class="item-main-tt">
                                    <a href="http://localhost:3000/single/dinhduong/dish_storage" onclick="hideqt()">
                                        <div class="top-item-tt">
                                            <img src="http://localhost:3000/css/dinhduong/images/thuvienmonanchiase.png">
                                        </div>
                                        <div class="bottom-item-tt">
                                            <p class="number-item-tt">
                                                3
                                            </p>
                                            <p class="content-item-tt">
                                                Vào thư viện món ăn chia sẻ để tìm món ăn cho trường
                                            </p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-main-qt">
                            <div class="title-main-qt">
                                <h5>Thao tác thường xuyên</h5>
                            </div>
                            <div class="content-main-qt content-main-tx">
                                <div class="item-main-tx" style="margin-left: 100px;">
                                    <a href="http://localhost:3000/single/dinhduong/storage" onclick="hideqt()">
                                        <p class="number-item-tx">1</p>
                                        <p class="title-item-tx">Nhập kho</p>
                                        <p class="link-item-tx"><a
                                                    href="http://localhost:3000/single/dinhduong/storage"
                                                    onclick="hideqt()">Quản lý kho > Nhập kho</a></p>
                                        <p class="nd-item-tx">Thực hiện khi phát sinh nhập kho</p>
                                    </a>
                                </div>
                                <div class="dau-main-tx">
                                    <i class="glyphicon glyphicon-menu-right"></i>
                                </div>
                                <div class="item-main-tx">
                                    <a href="http://localhost:3000/single/dinhduong/menu_adjust" onclick="hideqt()">
                                        <p class="number-item-tx">2</p>
                                        <p class="title-item-tx">Cân đối khẩu phần</p>
                                        <p class="link-item-tx"><a
                                                    href="http://localhost:3000/single/dinhduong/menu_adjust"
                                                    onclick="hideqt()">Khẩu phần dinh dưỡng > Điều chỉnh thực phẩm</a>
                                        </p>
                                        <p class="nd-item-tx"><span>1. </span>Tạo thực đơn (hoặc sử dụng thực đơn mẫu)
                                            để cân đối khẩu phần trước khi đi chợ</p>
                                        <p class="nd-item-tx"><span>2. </span>Sau khi đi chợ thì điểu chỉnh lại để tiền
                                            ăn trong ngày chính xác.</p>
                                    </a>
                                </div>
                                <div class="dau-main-tx">
                                    <i class="glyphicon glyphicon-menu-right"></i>
                                </div>
                                <div class="item-main-tx">
                                    <a href="http://localhost:3000/single/dinhduong/menu_report" onclick="hideqt()">
                                        <p class="number-item-tx">3</p>
                                        <p class="title-item-tx">Xuất biểu mẫu thống kê</p>
                                        <p class="link-item-tx"><a
                                                    href="http://localhost:3000/single/dinhduong/menu_report"
                                                    onclick="hideqt()">Khẩu phần dinh dưỡng > Báo cáo thống kê</a></p>
                                        <p class="nd-item-tx"><span>1. </span>Phiếu kê chợ</p>
                                        <p class="nd-item-tx"><span>2. </span>Tiếp phẩm tươi</p>
                                        <p class="nd-item-tx"><span>3. </span>Tiếp phẩm khô</p>
                                        <p class="nd-item-tx"><span>4. </span>Sơ chế biến</p>
                                        <p class="nd-item-tx"><span>5. </span>Lưu hủy mẫu</p>
                                        <p class="nd-item-tx"><span>6. </span>Tính tiền ăn</p>
                                        <p class="nd-item-tx"><span>7. </span>Calo tuần</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="btn-quy-trinh">
                    <img src="http://localhost:3000/css/dinhduong/images/btnquytrinh.png"
                         title="Quy trình rút gọn thao tác phần mềm khẩu phần dinh dưỡng">
                </div>
            </div>
            </div>
</div>
<div id="tructuyen" style="position: fixed;bottom: 3px; left: 3px; width: 60px;z-index: 100;"></div>
<!-- Thông báo tổng đài -->
<div style="display: none; position: relative;" id="dialog-tphcm">
    <img class="img-tb-login" src="http://localhost:3000/images/thong-bao-910.png?_=376">
</div>
<!-- Template menu -->
    <div style="position: fixed; height: 24px; padding: 3px; text-align: center; top: 0; margin-left: 7%; z-index: 3;">
        <label title="Để đảm bảo công việc không bị gián đoạn, thầy cô vui lòng liên hệ nhà cung cấp để gia hạn sử dụng hệ thống. Trân trọng!">
                            <i class="expire_soon_notify" style="color: rgba(165, 50, 50, 83%); font-size: 18px">Chú ý: Hạn sử dụng còn 17 ngày.</i>
                    </label>
    </div>
    <!-- Hiện báo đang xử lý -->
<div id="statusloading-mash" style="display: unset;">
    <div class="spinner-container-loading">
        <div class="spinner-container btn-hover spinner3" id="spinner-container">
            <div id="time-progress-bar"></div>
        </div>
        <span class="spinner-text">Đang xử lý ...</span>
    </div>
</div>

<script type="text/ng-template" id="menu_top_index.htm">
        <div class="menu-main-content">
        <ul class="nav nav-tabs nav-main-content" ng-if="count(menu.data.top)>=1" style="min-width:550px !important">
            <i class="glyphicon glyphicon-align-justify show-tab-main" onclick="tabMain()"></i>
            <div class="tab-main-index" ng-repeat="(index,menu_top) in menu.data.top">
                <li class="{{(menu_top.name==menu.selected.name?'active':'')}} li-hover li{{index}}" 
                    ng-click="menu.selected=menu_top" id="{{menu_top.icon}}">
                    <span data-toggle="tab" href="#nav{{index}}" ng-bind="menu_top.name" class="text-menu" onclick="tabMain()"></span>
                </li>
            </div>
            <div class="nav-main-content-copy"></div>
            <div class="menu-cauhinh-container dropdown">
                <div class="setup-mc-new dropdown-toggle" ng-bind="menu.data.bottom[0].name" data-toggle="dropdown">Cấu
                    hình
                </div>
                <div class="dropdown-menu">
                    <li ng-repeat="(index,item) in menu.data.bottom[0].children" style="width: 100%;">
                        <a class="a-click" ng-href="{{$CFG.remote.base_url+'/'+item.path}}" style="padding: 3px 10px;">
                            <img ng-src="http://localhost:3000/css/dinhduong/images/{{item.icon}}.png"
                                 style="width: 20px;">
                            {{item.name}}  
                        </a>
                    </li>
                </div>
            </div>
        </ul>
        <div class="tab-content">
            <div ng-repeat="(index,menu_top) in menu.data.top">
                <ul class="child-nav-main" ng-repeat="child in menu_top.children" style="margin-top:0px !important">
                    <div id="nav{{index}}"
                         class="tab-pane fade {{(menu_top.name==menu.selected.name?'in active':'')}}">
                        <div class="item-child-nav-main" id="item-child-nav-main{{$index}}">
                            <a class="b-click" ng-href="{{$CFG.remote.base_url+'/'+child.path}}">
                                <div class="thumb-chid-icon">
                                    <img ng-src="http://localhost:3000/css/dinhduong/images/{{child.icon}}.png">
                                </div>
                                <h4 ng-bind="child.name"></h4>
                            </a>
                        </div>
                    </div>
                </ul>
            </div>
        </div>
    </div>
    <style type="text/css">
        ul.nav.nav-tabs.nav-main-content {
            display: flex;
            justify-content: space-evenly;
            align-content: center;
            flex-wrap: wrap;
            align-items: baseline;
        }
        .nav-main-content{
            min-width: 500px !important;
        }
        .thongbao-thaydoi-menu {
            margin: 0 auto;
            height: 50px;
            width: 650px;
            text-align: justify;
            padding-top: 15px;
            clear: both;
        }

        .menu-cauhinh-container {
            position: absolute;
            left: 100%;
            white-space: nowrap;
            margin-left: 10px;
            margin-top: 7px;
        }

        .menu-cauhinh-container li {
            width: 100%;
            padding: 3px 5px;
            color: #333;
        }

        .menu-cauhinh-container .dropdown-menu {
            background: #eaffcf;
        }

        .menu-cauhinh-container li:hover {

        }
        .text-menu{
            font-size: 15px;
        }
        .item-child-nav-main {
            width: 15%;
            float: left;
            margin-top: 3%;
            text-align: center;
            position: relative;
            margin-left: 3%;
        }
        .thumb-chid-icon {
            height: 130px;
        }
        @media  screen and (device-width: 800px), screen and (device-height: 600px){
            .item-child-nav-main {
                width: 12%;
                float: left;
                margin-top: 2%;
                text-align: center;
                position: relative;
                margin-left: 5%;
            }
            .thumb-chid-icon {
                height: 65px;
            }
            .tab-content .tab-pane {
                margin-left: 9%;
            }
            
        }
        @media  screen and (device-width: 1024px){
            .item-child-nav-main {
                width: 20%;
                float: left;
                margin-top: 3%;
                text-align: center;
                position: relative;
                margin-left: 5%;
            }
            .thumb-chid-icon {
                height: 65px;
            }
            .tab-content .tab-pane {
                margin-left: 9%;
            }
            
        }
        @media  screen and (device-width: 1152px), screen and (device-height: 864px){
            .item-child-nav-main {
                width: 18%;
                margin-left: 2%;
            }
            .thumb-chid-icon {
                height: 90px;
            }
            .tab-content .tab-pane {
                margin-left: 0px;
            }
        }
        @media  screen and (device-width: 1280px), screen and (max-height: 600px){
            .item-child-nav-main {
                width: 80px;
                margin-top: 3%;
                margin-left: 4%;
            }
            .thumb-chid-icon {
                height: 80px;
            }
            .tab-content .tab-pane {
                margin-left: 0px;
            }
        }
        @media  screen and (device-width: 1280px), screen and (height: 768px){
            .tab-content{
                margin-left: 8%;
            }

        }
        @media  screen and (device-width: 1280px), screen and (height: 1024px){
            .item-child-nav-main {
                width: 110px;
                margin-left: 1%;
            }
        }
        
        @media  screen and (device-width: 1360px){
            .item-child-nav-main {
                width: 109px;
                margin-left: 1%;
            }
            .thumb-chid-icon {
                height: 110px;
            }
            .tab-content .tab-pane {
                margin-left: 5%;
            }
        }
        @media  screen and (device-width: 1366px){
            .item-child-nav-main {
                width: 109px;
                margin-left: 1%;
            }
            .thumb-chid-icon {
                height: 110px;
            }
            .tab-content .tab-pane {
                margin-left: 5%;
            }
        }
        @media  screen and (device-width: 1400px), screen and (device-height: 1050px){
            .item-child-nav-main {
                width: 20%;
            }
            .thumb-chid-icon {
                height: 110px;
            }
        }
        /* @media  screen and (device-width: 1440px){
            .item-child-nav-main {
                width: 12%;
            }
            .thumb-chid-icon {
                height: 110px;
            }
        } */
        @media  screen and (device-width: 1600px), screen and (device-height: 900px){
            .item-child-nav-main {
                width: 15%;
                margin-left: 5%;
            }
            .thumb-chid-icon {
                height: 110px;
            }
            .tab-content .tab-pane {
                margin-left: 1%;
            }
        }
    </style>
    </script>
<script type="text/ng-template" id="menu_bottom_children.htm">
    <div class="fcn-main-content fcn-main-content-after" style="bottom:6px;">
        <div role="navigation" class="navbar-buttons navbar-header in-fcn" style="width:100%">
            <ul class="nav ace-nav">
                <li class="light-blue" ng-repeat="item in menu.data.bottom" menu-dropdown-hover>
                    <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                        {{item.name}}
                    </a>
                    <ul class="user-menu dropdown-menu-left dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                        <li ng-repeat="itemi in item.children">
                            <a ng-href="{{$CFG.remote.base_url+'/'+itemi.path}}">
                                {{itemi.name}}
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <div style="" class="content-info-help content-info-help-running">
            			Hotline
            <img src="http://localhost:3000/images/phone-icon.gif">
            <span>
				1900 0101			</span>
        </div>
        <style type="text/css">
            .content-info-help {
                float: right;
                padding: 8px 17px 0px 0px;
            }

            .content-info-help img {
                width: 40px;
                margin-top: -15px;
            }

            .content-info-help span {
                color: red;
                font-weight: bold;
                font-size: 16px;
            }
        </style>
    </div>
</script>
<script type="text/ng-template" id="menu_bottom_index.htm">
    <div class="fcn-main-content-new">
        <div ng-repeat="item in menu.data.bottom">
            <div class="setup-mc-new" onclick="mcNew()">
                {{item.name}}
            </div>
            <div class="item-setup-mc-new" style="display: none;">
                <div ng-repeat="itemi in item.children">
                    <a class="a-click" ng-href="{{$CFG.remote.base_url+'/'+item.path}}">
                        <img ng-src="http://localhost:3000/css/dinhduong/images/{{itemi.icon}}.png">
                        <p>{{itemi.name}}</p>
                    </a>
                </div>
            </div>
        </div>
                    <div style="" class="content-info-help content-info-help-running">
				<!--div style="float:left; padding-right:5px;" ng-if="$CFG.is_payment_gate_on"><b class="fa fa-hand-o-right fs13"></b><a style="color:blue;" target="_blank" href="https://docs.google.com/document/d/1ezX1lk0aeTJOR6NNekMCxloa3TNXPgDw/edit"> <b>HƯỚNG DẪN THANH TOÁN HỌC PHÍ PMS QUA APP VIETTEL MONEY</b></a> | </div-->
                                <div style="float:left; padding-right:5px;">
                    <b class="fa fa-hand-o-right fs18"></b>
                    <a style="color:blue;" target="_blank" href="https://docs.google.com/document/d/1dwdn_l4BOK20vlEz1UbomsrX0NLbSAI4/edit?tab=t.0"> 
                        <b style="font-size:23px;">THÔNG BÁO LỊCH ĐÀO TẠO TẬP TRUNG: 9h-12h, THỨ 6, NGÀY 08/08/2025</b>
                    </a> | </div>
                				<!--a href="http://localhost:3000/css/dinhduong/files/UltraViewer.exe" style="color:blue; font-size:13px;"><b>Tải phần mềm UltraViewer</b></a-->
                <b>Hotline</b>
                <img src="http://localhost:3000/images/phone-icon.gif">
                <span style="font-size:14px;">
					1900 0101				</span>
            </div>
                <style type="text/css">
            .content-info-help img {
                width: 40px;
                margin-top: -18px;
            }

            .content-info-help span {
                color: red;
                font-weight: bold;
                font-size: 16px;
            }

            .content-info-help {
                position: fixed;
                bottom: 50%;
                right: 42%;
                padding: 20px;
                background: rgba(255, 255, 255, 1);
                border-radius: 45px;

                -webkit-transition: bottom 1.5s, right 1s, background 1.5s, -webkit-transform 1s; /* Safari */
                transition: bottom 1.5s, right 1s, background 1.5s, transform 1s;
            }

            .content-info-help-running {
                bottom: 0%;
                right: 0%;
                background: rgba(255, 255, 255, 0);
            }
            .function-kh-ct{
                margin-top: 30px !important;
            }

        </style>
    </div>
</script>
<style type="text/css">
    .li-eye-image img {
        width: 17px;
    }
	.expire_soon_notify {
		-webkit-animation: my 700ms infinite;
		-moz-animation: my 700ms infinite;
		-o-animation: my 700ms infinite;
		animation: my 700ms infinite;
	}
    .menu-main-option, .li-button-back{
        height: 50px !important;
        margin-top: 10px !important;
    }
</style>
<script type="text/javascript" src="http://localhost:3000/js/dinhduong/thong_bao.js?1754620099"></script>
<script type="text/javascript" src="http://localhost:3000/js/dinhduong/notification.js?1754620099"></script>
<script>
    $('body').on('click', '.panel button.captcha-refresh-btn', function () {
        var img = $(this).prev();
        $(this).prev().prop('src', img.prop('src').toString().replace(/t=(.*?)&/, 't=' + (new Date()).getTime() + '&'));
    });
			//form_edit_pass();
	
    function checkNccSso(ncc_token) {
        var pmsurl = "http://localhost:3000/admin/user/checkNccSso?ncc_token=" + ncc_token;
        window.location.href = pmsurl;
    }
</script>
</body>
</html>
