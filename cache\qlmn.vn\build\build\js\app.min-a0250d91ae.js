$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
    }
});
$(function () {
    initStatusLoading();
    $('#statusloading-mash').find('#spinner-container').click(function () {
        $('#statusloading-mash').hide();
    })
});
(function (angular) {
    if (typeof angular != 'undefined') {
        angular.fromJson = function (str) {
            if (str && str.charAt(0) === '"') {
                return JSON.parse(str);
            }
            return str;
        }
    }
})(window.angular);

/*Bắt sự kiện đóng cửa sổ trình duyệt hoặc tải lại trang (F5)*/
var eventCloseWindows = {
    title: '',
    enable: false
};
$(window).on("beforeunload", function () {
    if (eventCloseWindows.enable) {
        return eventCloseWindows.enable ? eventCloseWindows.title : null;
    }
});
/*<PERSON><PERSON><PERSON> thúc bắt sự kiện đóng cửa sổ trình duyệt*/
var BootstrapDialog_cache = {};
var statusloadingCache = {};
var refress_request = 1;
var screen_size = {
    width: $(window).width(),
    height: $(window).height()
};
var size = {
    'normal': BootstrapDialog.SIZE_NORMAL,
    'small': BootstrapDialog.SIZE_SMALL,
    'wide': BootstrapDialog.SIZE_WIDE,
    'large': BootstrapDialog.SIZE_LARGE
}
var keyString = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
$.selectSchoolYear = function (schoolyear, project) {
    process($CFG.remote.base_url + '/doing/admin/user/set_schoolyear', {
        namhoc: schoolyear,
        project: project
    }, function (resp) {
        if (resp.result == 'success') {
            location.reload();
        }
    });
}
$.timer = function (callback_realtime, m, callback_finish) {
    if (m <= 0) {
        if (typeof callback_finish == 'function') {
            callback_finish();
        }
        return;
    }
    setTimeout(function () {
        m -= 1;
        callback_realtime(m);
        $.timer(callback_realtime, m, callback_finish);
    }, 1000);

}
$.dm_datagrid = {
    init: function (url, module, title, columns, fixOption) { /* init */
        fixOption || (fixOption = {});
        fixOption.width || (fixOption.width = 500);
        fixOption.height || (fixOption.height = 300);
        // fixOption.onDblClickRow || (fixOption.onDblClickRow = 'showEditForm');
        fixOption.idField || (fixOption.idField = 'id');
        fixOption.pageSize || (fixOption.pageSize = 30);
        fixOption.checkOnSelect || (fixOption.checkOnSelect = true);
        fixOption.selectOnCheck || (fixOption.selectOnCheck = true);
        fixOption.ctrlSelect || (fixOption.ctrlSelect = true);
        fixOption.singleSelect || (fixOption.singleSelect = false);
        fixOption.nowrap || (fixOption.nowrap = false);
        fixOption.enableFilter || (fixOption.enableFilter = false);

        if (fixOption.checkOnSelect == 'false' || fixOption.checkOnSelect == false) {
            fixOption.checkOnSelect = false;
        } else {
            fixOption.checkOnSelect = true;
        }
        if (fixOption.selectOnCheck == 'false' || fixOption.selectOnCheck == false) {
            fixOption.selectOnCheck = false;
        } else {
            fixOption.selectOnCheck = true;
        }
        if (fixOption.ctrlSelect == 'false' || fixOption.ctrlSelect == false) {
            fixOption.ctrlSelect = false;
        } else {
            fixOption.ctrlSelect = true;
        }
        if (fixOption.pagination == 'false') {
            fixOption.pagination = false;
        }
        if (fixOption.pagination != false) {
            fixOption.pagination = true;
        }
        if (!jQuery.isArray(fixOption.pageList)) {
            fixOption.pageList = [30, 70, 100, 200];
        }
        module || (module = 'view');
        var exten = '_detail';
        var dataoption = {
            title: title,
            url: url + "?_=" + refress_request + '&_online=' + $('#tructuyen').html(),
            width: fixOption.width,
            height: fixOption.height,
            async: false,
            fit: true,
            fitColumns: true,
            idField: fixOption.idField,
            nowrap: fixOption.nowrap,
            filterDelay: 1200,
            autoRowHeight: true,
            remoteFilter: true,
            rownumbers: true,
            pageSize: fixOption.pageSize,
            pageList: fixOption.pageList,
            pagination: fixOption.pagination,
            ctrlSelect: fixOption.ctrlSelect,
            checkOnSelect: fixOption.checkOnSelect,
            selectOnCheck: fixOption.selectOnCheck,
            toolbar: '#tb_' + module + exten,
            columns: columns,
            border: false,
            onLoadSuccess: function (data) {
                $('#tbl_' + module).datagrid('unselectAll');
                if (typeof fixOption.onLoadSuccess === 'function') {
                    fixOption.onLoadSuccess(data);
                }
            },
            onBeforeLoad: function (data) {
                $('#tbl_' + module).datagrid('unselectAll');
            }
        };
        if (typeof fixOption.remoteFilter != 'undefined') {
            dataoption.remoteFilter = fixOption.remoteFilter;
        }
        if (typeof fixOption.onDblClickRow === 'function') {
            dataoption.onDblClickRow = fixOption.onDblClickRow;
        }
        $.each(fixOption, function (index, value) {
            dataoption[index] = value;
        });
        var selector = $('#tbl_' + module);
        selector.datagrid(dataoption);
        if (fixOption.enableFilter !== false) {
            selector.datagrid('enableFilter', fixOption.enableFilter);
        }
    }, showAddForm: function ($option, callback) {
        var dialog_key = count(BootstrapDialog_cache) + Math.random();
        var op = {
            title: ($option.title || 'Mẫu nhập liệu'),
            size: ($option.size || size.normal),
            message: $('<div style="margin:20px; width:100%;height:50px;"></div>').append(getLoadingHTML())
        };
        if (typeof $option == 'number') {
            op.size = size.wide;
        }
        if ($option.showButton === 'false' || $option.showButton === false) {
            $option.showButton = false;
        } else {
            $option.showButton = true;
        }
        if ($option.fullScreen === 'true' || $option.fullScreen === true) {
            $option.fullScreen = true;
        } else {
            $option.fullScreen = false;
        }
        if ($option.draggable === 'true' || $option.draggable === true) {
            $option.draggable = true;
        } else {
            $option.draggable = false;
        }
        if ($option.noteWarning === 'true' || $option.noteWarning === true || $option.noteWarning === undefined) {
            $option.noteWarning = true;
        } else {
            $option.noteWarning = false;
        }
        var onShown = '';
        if (typeof $option.onshown === 'function') {
            onShown = $option.onshown;
        }
        if (typeof $option.onShown === 'function') {
            onShown = $option.onShown;
        }
        var onShow = '';
        if (typeof $option.onshow === 'function') {
            onShow = $option.onshow;
        }
        if (typeof $option.onShow === 'function') {
            onShow = $option.onShow;
        }
        $option.titleAskBeforeClose = 'Những thay đổi có thể chưa được lưu.';
        if ($option.askBeforeClose != undefined && $option.askBeforeClose != '' && $option.askBeforeClose != 'false' && $option.askBeforeClose != false) {
            if ($option.askBeforeClose == 'true') {
                $option.askBeforeClose = true;
            }
            if (typeof $option.askBeforeClose === 'string') {
                $option.titleAskBeforeClose = $option.askBeforeClose;
                $option.askBeforeClose = true;
            }
            eventCloseWindows.title = $option.titleAskBeforeClose;
            eventCloseWindows.enable = true;
        }
        if ($option.showButton && !$option.buttons) {
            op['buttons'] = [
                {
                    id: 'btn-save',
                    icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn-primary',
                    action: function (dialogRef) {
                        var data = {};
                        var valid = true;
                        var form_value = getSubmitForm(dialogRef.getModalBody(), true);
                        if ($option.cb != undefined && typeof ($option.cb) == "function") {
                            valid = $option.cb(form_value);
                        }
                        if (!valid) {
                            return valid;
                        }
                        if (op['data']) {
                            data = op['data'];
                        } else {
                            data = {data: arrayToJson(form_value)};
                        }
                        data.async = true;
                        var url = $CFG.remote.base_url + '/doing/' + $option.module + '/' + $option.action;
                        process(url, data, function (resp) {
                            if (resp.result == "success") {
                                dialogRef.close();
                                if (typeof callback === 'function') {
                                    callback(resp);
                                }
                            }
                        });
                    }
                }, {
                    label: 'Bỏ qua',
                    icon: 'glyphicon glyphicon-log-out',
                    action: function (dialog) {
                        dialog.close();
                    }
                }
            ];
            op['closable'] = false;
        } else {
            if ($option.closable == undefined || $option.closable == true || $option.closable == 'true') {
                op['closable'] = true;
            } else {
                op['closable'] = false;
            }
            if ($option.buttons) {
                op.buttons = $option.buttons;
            }
        }
        op.draggable = $option.draggable;
        op['onshown'] = function (dialogRef) {
            dialogRef.askBeforeClose = $option.askBeforeClose;
            BootstrapDialog_cache[dialog_key] = dialogRef;
            if (typeof $option.content === 'function') {
                $option.content(dialogRef.getModalBody(), dialogRef);
            } else if (typeof $option.content == 'string') {
                if ($option.scope != undefined) {
                    dialogRef.scope = $option.scope;
                    dialogRef.url = $option.content;
                    $option.scope.getTemplate($option.content, function (template) {
                        setTimeout(function () {
                            $option.scope.$apply(function () {
                                dialogRef.getModalBody().html($option.scope.compile(template, $option.scope));
                            });
                        });
                    });
                } else {
                    op.message.html($option.content);
                }
            } else {
            }
            setDialog2fullscreen(dialogRef, $option);
            if (typeof onShown == 'function') {
                onShown(dialogRef, dialogRef.getModalBody());
            }
            if (typeof $option.size == 'number') {
                dialogRef.$modalContent.css({width: $option.size, margin: 'auto'});
            }
        };
        op['onshow'] = function (dialogRef) {
            if (typeof onShow == 'function') {
                onShow(dialogRef, dialogRef.getModalBody());
            }
        };
        op['onhide'] = function (dialog) {
            if (dialog.askBeforeClose === true) {
                $.messager.confirm('Đóng cửa sổ chức năng?', '<div style = "font-size: 14px">' + $option.titleAskBeforeClose + '</div>', function (r) {
                    if (r) {
                        dialog.askBeforeClose = false;
                        dialog.close();
                        eventCloseWindows.title = '';
                        eventCloseWindows.enable = false;
                    }
                });
                return false;
            } else {
                if (typeof $option.cancel === 'function') {
                    $option.cancel();
                }
                delete BootstrapDialog_cache[dialog_key];
            }
        };
        if (op['buttons'] && $option.noteWarning) {
            if (op['buttons'].length > 0) {
                op['buttons'].push({
                    id: '',
                    icon: '',
                    label: 'Chú ý: Những mục nhập có tiêu đề màu cam là bắt buộc.',
                    cssClass: 'btn-link pull-left valid-red valid-i'
                });
            }
        }
        BootstrapDialog.show(op);
    }, showEditForm: function ($option, callback) {
        this.showAddForm($option, callback);
    }, del: function (controller, ids, callback, data_extend) {
        if (controller.split('del').length == 1) {
            controller += '/del';
        }
        if (typeof ids === 'array') {
            ids = ids.join(',');
        }
        var data = {ids: ids, async: true};
        if (typeof data_extend == 'object') {
            jQuery.extend(data, data_extend);
        }
        process(controller, data, function (resp) {
            if (resp.result == 'success') {
                if (typeof callback === 'function') {
                    callback(resp);
                }
            }
        })
    }, combobox: function (id, data, options) {
        new comboboxInit(id, data, options);
    }, doSearch: function (datagridID, ids, filter_type) {
        if (typeof ids != 'object' || !datagridID) {
            return;
        }
        if (datagridID.split('#').length == 1 && datagridID.split('.').length == 1) {
            datagridID = '#' + datagridID;
        }
        var queryParams = $(datagridID).datagrid('options').queryParams;
        filter_type || (filter_type = 'and');
        var filterRules = [];
        $.each(ids, function (id, op) {
            if (typeof op != 'string') {
                if (op.value != 'clear') {
                    filterRules.push({field: id, op: op.op, value: op.value});
                }
            } else if (op != 'clear') {
                var input = $('#' + id);
                if (input.attr('type') == 'checkbox') {
                    input = $('#' + id + ':checked');
                }
                if (input.length) {
                    if (input.attr('field')) {
                        id = input.attr('field');
                    }
                    op || (op = 'equal');
                    var value = input.val();
                    if (value + '' != '') {
                        filterRules.push({field: id, op: op, value: value});
                    }
                }
            }
        });
        queryParams.filterRules = filterRules;
        queryParams.filter_type = filter_type;
        setTimeout(function () {
            $(datagridID).datagrid('load', queryParams);
        }, 0);
    }, show: function (options, callback) {
        var dialog_key = count(BootstrapDialog_cache) + Math.random();
        options.message = '<div style="padding: 10px;">' + options.message + '</div>';
        options.onshown = function (dialogRef) {
            BootstrapDialog_cache[dialog_key] = dialogRef;
            if (typeof callback === 'function') {
                callback();
            }
        };
        options.onhide = function () {
            delete BootstrapDialog_cache[dialog_key];
        };
        BootstrapDialog.show(options);
    }
}

function rand(min, max) {
    var $ran = (Math.random() + '').split('.')[1];
    var num = $ran;
    if (min && max) {
        if (max < 10) {
            num = Number(num[0]);
        } else {
            var tmp = ''
            for (var i = 0; i < (max + '').length; i++) {
                tmp += num[i];
            }
            num = Number(tmp);
        }
        if (num > max) {
            num = max;
        } else if (num <= min) {
            num = min;
        }
    }
    return num;
}

function dialogCloseAll(callback, type) {
    for (var i in BootstrapDialog_cache) {
        if (BootstrapDialog_cache[i]) {
            if (!type) {
                BootstrapDialog_cache[i].askBeforeClose = false;
            }
            BootstrapDialog_cache[i].close();
        }
    }
    BootstrapDialog_cache = {};
    if (typeof callback === 'function') {
        callback();
    }
}

function dialogClose(callback, type) {
    var id = undefined;
    for (var i in BootstrapDialog_cache) {
        id = i;
    }
    if (!type) {
        BootstrapDialog_cache[id].askBeforeClose = false;
    }
    BootstrapDialog_cache[id].close();
    delete BootstrapDialog_cache[id];
    if (typeof callback === 'function') {
        callback();
    }
}

function dialogRefresh() {
    var dialog = undefined;
    var id = undefined;
    for (var i in BootstrapDialog_cache) {
        id = i;
    }
    if (BootstrapDialog_cache[id]) {
        if (BootstrapDialog_cache[id].btnReload) {
            console.log(BootstrapDialog_cache[id].btnReload);
            $(BootstrapDialog_cache[id].btnReload).click();
        }
    }
}

function comboboxInit(id, data, options) {
    options || (options = {});
    options.valueField || (options.valueField = 'id');
    options.textField || (options.textField = 'text');
    options.width || (options.width = 200);
    options.height || (options.height = 34);
    options.delay || (options.delay = 500);
    options.placeholder || (options.placeholder = '');
    options.queryParams || (options.queryParams = {});
    options.searchFields || (options.searchFields = options.textField);
    if (typeof id != 'object') {
        if (id.split('#').length == 1) {
            id = 'input#' + id;
        }
        id = $(id);
    }
    if (typeof options.searchFields === 'string') {
        options.searchFields = [options.searchFields];
    }
    if (id.attr('placeholder')) {
        options.placeholder = id.attr('placeholder');
    }
    var $options = {
        queryParams: options.queryParams,
        valueField: options.valueField,
        textField: options.textField,
        width: options.width,
        height: options.height,
        mode: options.mode,
        delay: options.delay,
        value: options.value,
        multiple: options.multiple,
        onBeforeLoad: function (params) {
            $(this).next().children('input').attr('placeholder', options.placeholder);
            if (options.mode != 'local') {
                $(this).next().children('span').append($(getLoadingHTML()));
                $(this).next().children('span').children('a').hide();
            }
        }, onSelect: function (row) {
            if (options.multiple) {
                var value = [];
                id.next().find('input.combo-value').each(function (index, el) {
                    value.push($(el).val());
                });
                id.val(value.join(','));
            } else {
                id.val(row[options.valueField])
            }
            if (typeof options.onSelect === 'function') {
                options.onSelect(row, this);
            }
        }, onLoadSuccess: function (data, el) {
            setTimeout(function () {
                id.next().children('span').children('a').show();
                id.next().children('span').children('div').hide();
            }, 300);
            if (options.multiple) {
                var value = [];
                id.next().find('input.combo-value').each(function (index, ele) {
                    value.push($(ele).val());
                });
                id.val(value.join(','));
            }
            if (typeof options.onLoadSuccess === 'function') {
                options.onLoadSuccess(data, el, this);
            }
        }, onLoadError: function () {
            id.next().children('span').children('div').hide();
            id.next().children('span').children('span').hide();
            id.next().children('span').append(getErrorHTML());

        }, onUnselect: function (row) {
            var value = [];
            id.next().find('input.combo-value').each(function (index, el) {
                value.push($(el).val());
            });
            id.val(value.join(','));
            if (typeof options.onUnselect === 'function') {
                options.onUnselect(row, this);
            }
        }, filter: function (q, row) {
            var opts = $(this).combobox('options');
            var qs = removeUnicode(q.toLowerCase());
            if (removeUnicode(row[opts.textField]).toLowerCase().indexOf(qs) >= 0) {
                return true;
            }
            return false;
        }, onChange: function (newvalue, oldvalue) {
            if (typeof options.onChange === 'function') {
                options.onChange(newvalue, oldvalue, this);
            }
        }
    };
    if (typeof options.formatter === 'function') {
        $options['formatter'] = options.formatter;
    }
    delete $options.url;
    delete $options.data;
    if (typeof data === 'string') {
        $options.url = data;
    } else {
        $options.data = data;
    }
    id.combobox($options);
}

function init_uploader(id_fileupload, url, accept_file_types, limit_file_size, success_func) {
    var input_element = null;
    if (typeof id_fileupload === 'string') {
        input_element = $("#" + id_fileupload);
    } else {
        input_element = id_fileupload;
    }
    var container = $('<span class="container-input-file"></span>');
    input_element.after(container);
    var content_input = $('<span class="btn fileinput-button"></span>');
    var id_files = $('<span id="files" class="files"></span>');
    content_input.append($('<span class="btn btn-warring">Chọn file...</span>')).append(input_element);
    container.append(content_input).append(id_files);
    var length = arguments.length;
    var resizable = true;
    if (length == 7) {
        resizable = arguments[6];
    }
    input_element.fileupload({
        url: url,
        dataType: 'json',
        singleFileUploads: true,
        autoUpload: true,
        acceptFileTypes: new RegExp("(\.|\/)(" + accept_file_types + ")$", "i"),
        maxFileSize: (15 * 1024 * 1024), /* 15 MB */
        disableImageResize: !resizable, /* Android(?!.*Chrome)|Opera */
        previewMaxWidth: 70,
        previewMaxHeight: 70,
        previewCrop: true
    }).on('fileuploadprocessalways', function (e, data) {
        id_files.html('');
        var index = 0;
        file = data.files[index];
        if (file.error) {
            $.messager.alert('Lỗi', file.error);
        } else {
            statusloading();
            data.context = id_files;
            var node = $('<span class="file_upload_choise"/>')
                .text(file.name)
                .append('<span style="font-weight:bold">&nbsp; - ' + (file.size / 1024 / 1024).toFixed(2) + ' Mb</span>&nbsp;');
            node.append('<span id="upload_stt" style="color: red"></span>');
            node.appendTo(data.context);
        }
    }).on('fileuploadprogressall', function (e, data) {
        var progress = parseInt(data.loaded / data.total * 100, 10);
        $("#upload_stt").text(progress + "%");
    }).on('fileuploaddone', function (e, data) {
        success_func(data.result);
        if (data.result.files) {
            var index = 0, file = data.result.files[index];
            if (file.url) {
                var link = $('<a>')
                    .attr('target', '_blank')
                    .prop('href', file.url);
                if (data.context) {
                    $(data.context.children()[index]).wrap(link);
                }
            }
            if (file.error) {
                $.messager.alert('Lỗi', file.error);
            }
        }
        id_files.html('');
        statusloadingclose();
    }).on('fileuploadfail', function (e, data) {
        id_files.html('');
        $.messager.alert('Lỗi', 'Tải file không thành công');
        statusloadingclose();
    }).prop('disabled', !$.support.fileInput)
        .parent().addClass($.support.fileInput ? undefined : 'disabled');
}

function form_edit_pass() {
    $.dm_datagrid.showEditForm(
        {
            module: 'admin/user',
            action: 'changepass',
            title: 'Đổi mật khẩu',
            size: size.small,
            noteWarning: '',
            content: function (element) {
                loadForm('admin/user', 'changepass', {}, function (resp) {
                    $(element).html(resp);
                })
            }
        }
    );
}

function count($ob) {
    $rs = 0;
    if (typeof $ob === 'string' || typeof $ob === 'array') {
        $rs = $ob.length;
    } else if (typeof $ob === 'number') {
        var str = $ob + '';
        $rs = $str.length;
    } else if (typeof $ob === 'function') {
    } else if (typeof $ob === 'object') {
        for (var i in $ob) {
            $rs++;
        }
    }
    return $rs;
}
function openDlgThongBao(name_cache, filename, solan) {
    var tb_count = getCookie(name_cache);
    if (!tb_count) {
        tb_count = 1;
    }else{
        tb_count = parseInt(tb_count);
    }
    if (tb_count > solan) {
        return;
    }
    tb_count += 1;
    setCookie(name_cache, tb_count, 60*60*24*30*12);
    var content = '<div id="dlg_warning"><div class="dlg-content"></div></div>';
    if (!$('body > #dlg_warning').length) {
        $('body').append(content);
    }
    $('#dlg_warning').dialog({
        title: '',
        width: 550,
        height: 400,
        closed: false,
        cache: false,
        modal: true,
        onOpen : function (ele) {
            var img = $('<img style="width: 100%; height: 100%;">');
            img.attr('src',$CFG.remote.base_url+'/images/thongbao/'+filename+'?_' + Math.random().toString().split('.')[1]);
            $('.window').css({"background":"rgba(0,0,0,0)","border":"0px","box-shadow":"none"});
            var btn_close = $('<div style="top: 60px;right: 53px;position: absolute;font-size:17px; opacity: 0.3;" class="close"></div>');
            btn_close.append('<img src="' + $CFG.remote.base_url + '/images/close.png">');
            $('#dlg_warning').css({"background":"rgba(0,0,0,0)","border":"0px", "overflow": "unset"}).append(btn_close).append(img);
            btn_close.click(function(){
                $('#dlg_warning').dialog("close");
            });
            $('.window-mask').click(function(){
                $('#dlg_warning').dialog("close");
            });
        }
    });
}
function loadForm(module, action, params, callback) {
    params || (params = {});
    params.dataType || (params.dataType = 'html');
    params.async || (params.async = false);
    /*Truyền số người online để*/
    var online = $('#tructuyen').html();
    params._online = online;
    var url = module;
    if (action != '') {
        url = url + '/' + action;
    }
    url = url + "?_=" + rand();
    if(url.split('//').length === 1){
		if(url[0] !== '/') {
			url = '/' + url;
		}
		url = $CFG.remote.base_url + url;
	}
    var note = {
        title: 'Thông báo',
        message: '',
        buttons: [{
            label: 'Đóng',
            icon: 'glyphicon glyphicon-log-out',
            action: function (dialog) {
                dialog.close();
            }
        }]
    };
    $.ajax({
        type: 'GET',
        url: url,
        data: params,
        dataType: params.dataType,
        async: params.async,
        success: function (resp) {
            if (typeof callback === 'function') {
                callback(resp);
            }
            if (params.dataType == 'json') {
                if (resp.errors != undefined) {
                    if (count(resp.errors) > 0) {
                        var html = [];
                        $.each(resp.errors, function (index, item) {
                            if (typeof item != 'string') {
                                $.each(item, function (i1, value) {
                                    html.push('<div> - ' + value + '</div>');
                                })
                            } else {
                                html.push('<div> - ' + item + '</div>');
                            }
                        });
                        if (html.length > 0) {
                            note.message = '<div style="max-height:400px;overflow-y:scroll;">' + html.join(' ') + '</div>';
                            $.dm_datagrid.show(note);
                        }
                    }
                }
            }
        },
        error: function (e) {
            if (typeof callback === 'function') {
                callback('Lỗi khi xử lý');
            }
        }, beforeSend: function () {
            showSpinner('load_form_' + module);
        }, complete: function () {
            hiddenSpinner('load_form_' + module);
        }
    });
}

function setDialog2fullscreen(dialogRef, options) {
    var header = dialogRef.getModalHeader().children('.bootstrap-dialog-header');
    var body_h = $('body').height() - 50;
    if (options.fullScreen) {
        dialogRef.getModalDialog().addClass('full-screen');
        dialogRef.getModalBody().css({'min-height': body_h})
    }
    $(window).resize(function () {
        if (dialogRef.getModalDialog().hasClass('full-screen')) {
            dialogRef.getModalBody().css({'min-height': body_h});
        }
    });
    header.dblclick(function () {
        if (dialogRef.getModalDialog().hasClass('full-screen')) {
            dialogRef.getModalDialog().removeClass('full-screen');
            dialogRef.getModalBody().css({'min-height': ''})
        } else {
            dialogRef.getModalDialog().addClass('full-screen');
            dialogRef.getModalBody().css({'min-height': body_h})
        }
    });
    if (options.closable != false && options.closable != 'false') {
        header.children('.bootstrap-dialog-close-button').css('display', 'block');
    }
    var btnZoomOutIn = $('<div href="javascript: void(0);" class="bootstrap-dialog-close-button btn-dialog-fullscreen"></div>');
    var zoomOut = $('<span class="glyphicon glyphicon-fullscreen" title="Toàn màn hình"></span>');
    zoomOut.click(function () {
        dialogRef.getModalDialog().addClass('full-screen');
        dialogRef.getModalBody().css({'min-height': body_h})
    });
    var zoomIn = $('<span class="glyphicon glyphicon-resize-small" title="Thu nhỏ"></span>')
    zoomIn.click(function () {
        dialogRef.getModalDialog().removeClass('full-screen');
        dialogRef.getModalBody().css({'min-height': ''});
    });
    btnZoomOutIn.append(zoomOut).append(zoomIn);
    header.append(btnZoomOutIn);
    if (typeof options.content === 'function' || options.scope != undefined) {
        dialogRef.btnReload = $('<div href="javascript: void(0);" class="bootstrap-dialog-close-button btn-dialog-fullscreen"></div>')
            .append($('<span class="glyphicon glyphicon-refresh" title="Tải lại"></span>'));
        dialogRef.btnReload.click(function () {
            if (options.scope != undefined) {
                options.scope.getTemplate(options.content, function (template) {
                    dialogRef.getModalBody().html(options.scope.compile(template, options.scope));
                    if (typeof options.reload === 'function') {
                        options.reload(dialogRef);
                    }
                }, true);
            } else {
                dialogRef.getModalBody().html($('<div style="margin:20px; width:100%;height:50px;"></div>').append(getLoadingHTML()));
                options.content(dialogRef.getModalBody());
            }
        });
        header.append(dialogRef.btnReload);
    }
}

function utf8Encode(string) {
    string = string.replace(/\x0d\x0a/g, "\x0a");
    var output = "";
    for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
            output += String.fromCharCode(c);
        } else if ((c > 127) && (c < 2048)) {
            output += String.fromCharCode((c >> 6) | 192);
            output += String.fromCharCode((c & 63) | 128);
        } else {
            output += String.fromCharCode((c >> 12) | 224);
            output += String.fromCharCode(((c >> 6) & 63) | 128);
            output += String.fromCharCode((c & 63) | 128);
        }
    }
    return output;
}

function utf8Decode(input) {
    var string = "";
    var i = 0;
    var c = c1 = c2 = 0;
    while (i < input.length) {
        c = input.charCodeAt(i);
        if (c < 128) {
            string += String.fromCharCode(c);
            i++;
        } else if ((c > 191) && (c < 224)) {
            c2 = input.charCodeAt(i + 1);
            string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
            i += 2;
        } else {
            c2 = input.charCodeAt(i + 1);
            c3 = input.charCodeAt(i + 2);
            string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
            i += 3;
        }
    }
    return string;
}

function base64Encode(input) {
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    input = utf8Encode(input);
    while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
            enc4 = 64;
        }
        output = output + keyString.charAt(enc1) + keyString.charAt(enc2) + keyString.charAt(enc3) + keyString.charAt(enc4);
    }
    return output;
}

function base64Decode(input) {
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (i < input.length) {
        enc1 = keyString.indexOf(input.charAt(i++));
        enc2 = keyString.indexOf(input.charAt(i++));
        enc3 = keyString.indexOf(input.charAt(i++));
        enc4 = keyString.indexOf(input.charAt(i++));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output = output + String.fromCharCode(chr1);
        if (enc3 != 64) {
            output = output + String.fromCharCode(chr2);
        }
        if (enc4 != 64) {
            output = output + String.fromCharCode(chr3);
        }
    }
    output = utf8Decode(output);
    return output;
}

function arrayToJson(values) {
    var json = "";
    i = 1;
    for (t in values) {
        if (typeof values[t] == "object") {
            json += "\"" + values[t]['id'] + "\" : \"" + values[t]['value'] + "\",";
            i++;
        } else {
            json += "\"" + t + "\" : \"" + values[t] + "\",";
            i++;
        }
    }
    json = json.substring(0, json.lastIndexOf(','));
    json = "{" + json + "}";
    return json;
}

function checkNeeded(thisInput, ignoredIds, neededIds) {
    ignoredIds || (ignoredIds = []);
    neededIds || (neededIds = []);
    var check = true;
    if (neededIds.length > 0) {
        if ($.inArray(thisInput.attr("id"), neededIds) == -1) check = false;
    }
    if (ignoredIds.length > 0) {
        if ($.inArray(thisInput.attr("id"), ignoredIds) != -1) check = false;
    }
    return check;
}

function getSubmitForm(name, ignoredIds, neededIds) {
    ignoredIds || (ignoredIds = []);
    neededIds || (neededIds = []);
    var form = '';
    if (typeof name == 'string') {
        if (name[0] == '#') {
            form = $(name);
        } else {
            form = $("#" + name);
            if (!form.length) {
                form = $('.' + name);
            }
        }
    } else {
        form = name;
    }
    var data = [];
    form.find("input:text").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find('input[type="number"]').each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds)) {
            pushToArray(data, $(this));
        }
    });
    form.find("select").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:file").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:hidden").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:password").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("textarea").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:radio").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds)) {
            var id = $(this).attr('id');
            if (id != "" && id != null) {
                var value = $("input[id='" + id + "']:checked").val();
                if (value == undefined) value = "";
                //value = base64Encode(value);
                var item = {id: id, value: value};
                data.push(item);
            }
        }
    });
    form.find("input:checkbox").each(function (i) {
        var id = $(this).attr('id');
        if (id != "" && id != null) {
            var tmp = "";
            form.find("input[id='" + id + "']:checked").each(function (i) {
                tmp += $(this).val() + ",";
            });
            tmp = tmp.substring(0, tmp.length - 1);
            //tmp = base64Encode(tmp);
            var item = {id: id, value: tmp};
            data.push(item);
        }
    });
    return data;
}

function pushToArray(arr, obj) {
    var id = obj.attr('id');
    var css_class = obj.attr("class");
    var value = null;
    if (id != '' && id != undefined) {
        switch (css_class) {
            case 'datebox-f combo-f':
                value = encodeHTML($("#" + id).datebox('getValue'));
                break;
            case 'combobox-f combo-f':
                value = encodeHTML($("#" + id).combobox('getValue'));
                break;

            case 'easyui-datebox datebox-f combo-f':
                value = encodeHTML($("#" + id).combobox('getValue'));
                break;

            case 'tree':
                var nodes = $('#' + id).tree('getChecked');
                var s = '';
                for (var i = 0; i < nodes.length; i++) {
                    if (nodes[i].id != '') {
                        s += nodes[i].id + ',';
                    }
                }
                value = s;
                break;
            default:
                value = obj.val() + '';
                if (obj.attr("type") == 'number') {
                    value = value.replace(',', '.');
                }
                value = encodeHTML(value);
                break;
        }
        //value = base64Encode(value);
        var item = {id: id, value: value};
        arr.push(item);
    }
}

$.alert = function (msg) {
    if (typeof msg != 'string') {
        BootstrapDialog.show(msg);
    } else {
        BootstrapDialog.show({
            title: 'Thông báo',
            message: '<div class="col col-right col-md-12 col-sm-12">' + msg + '</div>',
            buttons: [
                {
                    label: 'Đóng',
                    icon: 'glyphicon glyphicon-log-out',
                    action: function (dialog) {
                        dialog.close();
                    }
                }
            ]
        });
    }
}

function encodeHTML(html) {
    if (html != null)
        return html.replace(/"/g, "\\'");
    else
        return "";
}

$(function () {
    $.fn.datebox.defaults.formatter = function (date) {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
    };
    $.fn.datebox.defaults.parser = function (s) {
        if (!s) return new Date();
        var ss = s.split('/');
        var d = parseInt(ss[0], 10);
        var m = parseInt(ss[1], 10);
        var y = parseInt(ss[2], 10);
        if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
            return new Date(y, m - 1, d);
        } else {
            return new Date();
        }
    };
});
var selectedRow = null;

function process(url, params, success_func, error_func, show_notification, show_loading) {
    if (url.split('api').length == 1) {
        url = url.replace($CFG.remote.base_url + '/', '');
        url = url.replace('doing/', '');
        url = $CFG.remote.base_url + '/doing/' + url;
    }
    if (show_notification == 'false' || show_notification == false) {
        show_notification = false;
    } else {
        show_notification = true;
    }
    var text_status = undefined;
    if (typeof show_loading === 'string' && show_loading != '') {
        text_status = show_loading;
    }
    if (show_loading == 'false' || show_loading == false) {
        show_loading = false;
    } else {
        show_loading = true;
    }
    if (show_loading) {
        statusloading(null, text_status);
    }
    params || (params = {});
    var async = params.async;
    async || (async = false);
    if (async != true && async != 'true') {
        async = false;
    } else {
        async = true;
    }
    delete params.async;
    /*Truyền số người online để*/
    var online = $('#tructuyen').html();
    params._online = online;
    var note = {
        title: 'Thông báo',
        message: '',
        buttons: [{
            label: 'Đóng',
            icon: 'glyphicon glyphicon-log-out',
            action: function (dialog) {
                dialog.close();
            }
        }]
    };
    return $.ajax({
        type: "POST",
        url: url,
        async: async,
        data: params,
        timeout: 120000,
        dataType: 'json',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function (resp) {
            if (!resp) {
                if (show_notification != false) {
                    note.message = "Lỗi";
                    $.dm_datagrid.show(note);
                }
            } else if (resp.result == 'logouted') {
                var html = resp.html;
                $.dm_datagrid.showAddForm(
                    {
                        module: $CFG.project + '/menu_adjust',
                        action: 'add',
                        title: 'Đăng nhập',
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            if (resp.html) {
                                $(element).html(resp.html);
                            }
                        }
                    }
                );
            } else if (resp.result == 'success') {
                if (resp.errors != undefined) {
                    var html = [];
                    $.each(resp.errors, function (index, item) {
                        if (typeof item != 'string') {
                            $.each(item, function (i1, value) {
                                html.push('<div> - ' + value + '</div>');
                            })
                        } else {
                            html.push('<div> - ' + item + '</div>');
                        }
                    });
                    if (html.length > 0) {
                        note.message = '<div style="max-height:400px;overflow-y:scroll;">' + html.join(' ') + '</div>';
                        $.dm_datagrid.show(note);
                    }
                }
                $.messager.show({
                    msg: '<i class="fa fa-bolt color-orange" aria-hidden="true" style="margin-right: 3px;font-size: 15px;"></i> Xử lý thành công!',
                    timeout: 1800,
                    showType: 'slide',
                    width: 150,
                    height: 40
                });
            } else if (resp.result == 'fail') {
                if (show_notification != false) {
                    note.message = "Lỗi xử lý";
                    $.dm_datagrid.show(note);
                }
            } else {
                if (show_notification != false) {
                    var html = [];
                    if (resp.errors) {
                        $.each(resp.errors, function (index, item) {
                            html.push('<div> - ' + item + '</div>');
                        })
                    }
                    if (html.length == 0) {
                        html.push('Lỗi.');
                    }
                    note.message = html.join(' ');
                    $.dm_datagrid.show(note);
                }
            }
            if (typeof success_func === 'function' && resp.result != 'logouted') {
                success_func(resp);
            }
        },
        error: function () {
            if (show_notification != false) {
                setTimeout(function () {
                    note.message = 'Không xử lý được chức năng';
                    $.dm_datagrid.show(note);
                });
            }
            if (typeof error_func === 'function') {
                error_func();
            }
        }, beforeSend: function () {
        }, complete: function () {
            if (show_loading) {
                statusloadingclose();
            }
        }
    });
}

jQuery.downloadFile = function (url, data) {
    //url and data options required
    if (url) {
        if (data) {
            var form_submit = jQuery('<form action="' + url + '" method="post" style="position: fixed; width: 5px; height: 5px; top: -100px;"></form>');

            jQuery('body').append(form_submit);
            jQuery.each(data, function (key, val) {
                if (typeof val === 'object') {
                    val = JSON.stringify(val);
                }
                var frm_input = jQuery('<input type="hidden" name="' + key + '" value="" />');
                form_submit.append(frm_input);
                frm_input.val(val);
            });
            $('<input type="submit" value="submit">').appendTo(form_submit);
            form_submit.submit(function () {
                console.log('2222222')
            });
            const a = form_submit.submit();
            console.log(111,a);

        }
    }
    ;
};
$.loading = {
    show: function (title) {
        var zindex = $("body div.window").css("z-index");
        if (title == undefined) title = "Tải dữ liệu...";
        $("body").append('<div id="loading"><div id="div-loading" class="loading window" style="background:white;color:black;display: block; width: 180px; z-index: 20001;"><img src="theme/img/loading3.gif" border=0 align="top"/>&nbsp;' + title + '</div><div class="window-mask" style="width: 100%; height: 100%; display: block; z-index: 20000;"></div></div>');
        $('#div-loading').center();
        $('#loading').show();
    },
    hide: function () {
        $('#div-loading').hide();
        $('#div-loading').remove();
        $('#loading').hide();
        $('#loading').remove();
    }
}
/*Hàm tính nhân chia số thực*/
FLOAT = {
    0: 10,
    1: 100,
    2: 1000,
    3: 10000,
    4: 100000,
    5: 1000000,
    6: 10000000,
    7: 100000000,
    8: 1000000000,
    9: 10000000000,
}
$['*'] = function (a, b) {
    if (isNaN(a)) {
        a = 0;
    }
    if (isNaN(b)) {
        b = 0;
    }
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return a * b / factor / factor;
};
$['/'] = function (a, b) {
    if (isNaN(a)) {
        a = 0;
    }
    if (isNaN(b)) {
        b = 0;
    }
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return rs = a / b;
};
$['+'] = function (a, b) {
    if (isNaN(a)) {
        a = 0;
    }
    if (isNaN(b)) {
        b = 0;
    }
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return (a + b) / factor;
};
$['-'] = function (a, b) {
    if (isNaN(a)) {
        a = 0;
    }
    if (isNaN(b)) {
        b = 0;
    }
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return (a - b) / factor;
};

/*  Chuẩn hóa chuỗi số*/
function clearnNumeric(value) {
    var number = ['-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'];
    var type_number = 'int';
    var is_number = false;
    if (typeof value === 'number') {
        is_number = true;
    }
    value += '';
    if (value.split('.') > 1) {
        type_number = 'float';
    }
    value = value.replace(',', '.');
    value = value.replace('..', '.');
    value = value.replace('..', '.');
    value = value.replace('--', '-');
    value = value.replace('--', '-');
    if (value[0] == '.') {
        value = value.substring(1, value.length);
    }
    if (value[value.length - 1] == '-') {
        value = value.substring(0, value.length - 1);
    }
    var rs = '';
    var number_dot = 0;
    for (var i = 0; i < value.length; i++) {
        if (value[i] == '.') {
            number_dot++;
        }
        if (i > 0 && i < value.length - 1 && value[i] == '-' || number_dot > 1 && value[i] == '.') {

        } else if (in_array(value[i], number)) {
            rs += value[i];
        }
    }
    if (is_number) {
        if (type_number == 'int') {
            value = parseInt(value);
        } else {
            value = parseFloat(value);
        }
    }
    return rs;
}

function absNumeric(value) { /* Trị tuỵet đối dùng hàm có sẵn trong Math sẽ không gõ được dâu chấm ở cuối nếu là số */
    var type_number = 'int';
    var is_number = false;
    if (typeof value === 'number') {
        is_number = true;
    }
    value += '';
    if (value.split('.') > 1) {
        type_number = 'float';
    }
    while (value[0] == '-') {
        value = value.substring(1, value.length);
    }
    if (is_number) {
        if (type_number == 'int') {
            value = parseInt(value);
        } else {
            value = parseFloat(value);
        }
    }
    return value;
}

function toFloat(value) {
    value += '';
    var arr = value.replace(',', '.').split('.');
    var rs = parseInt(arr[0]);
    if (arr.length == 2) {
        var d = 10;
        for (var i = 0; i < arr[1].length; i++) {
            var v = parseInt(arr[1][i]);
            rs += v / d;
            d *= 10;
        }
    }
    return rs;
}

function round(value, num, get_default) {
    if (get_default == undefined) {
        get_default = 0;
    }
    if (value + '' == 'NaN' || value == undefined) {
        value = get_default;
    } else {
        if (num === undefined || num === "") {
            if ($CFG) {
                num = $CFG.round_number_config;
            }
        }
        if (num === undefined || num === "") {
            num = 3;
        }
        var per = 1;
        for (var i = 0; i < num; i++) {
            per *= 10;
        }

        if (typeof value != 'number') {
            value = parseFloat(value);
        }
        value = Math.round(value * per) / per;
        if (value + '' === '0') {
            value = get_default;
        }
    }
    return value;
}

function myformatter(date) {
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
}

function myparser(s) {
    if (!s) return new Date();
    if (typeof s === 'object') {
        return s;
    }
    var ss = (s.split('/'));
    var y = parseInt(ss[0], 10);
    var m = parseInt(ss[1], 10);
    var d = parseInt(ss[2], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(d, m - 1, y);
    } else {
        return new Date();
    }
}

function dateparser(s) {
    if (!s) return new Date();
    var ss = (s.split('/'));
    var d = parseInt(ss[0], 10);
    var m = parseInt(ss[1], 10);
    var y = parseInt(ss[2], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(y, m - 1, d);
    } else {
        return new Date();
    }
}

function converdate(s) {
    if (!s) return new Date();
    var ss = (s.split('/'));
    var y = parseInt(ss[0], 10);
    var m = parseInt(ss[1], 10);
    var d = parseInt(ss[2], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(y, m - 1, d);
    } else {
        return new Date();
    }
}

function $NaN(value, end) {
    end || (end = '');
    if (value + '' == 'NaN') {
        return end;
    }
    return value;
}

function $Infinity(value, ext) {
    ext || (ext = 0);
    if (value + '' == 'Infinity') {
        value = ext;
    }
    return value;
}

function dateboxOnSelect(date) {
    if (!date) {
        date = new Date();
    }
    if (typeof date.getFullYear === 'function') {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        var val = (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
        $(this).val(val);
        return val;
    }
    return date;
}

function monthNow() {
    var date = new Date();
    var m = date.getMonth() + 1;
    return m.toString();
}

function array_del_value($array, $value) {
    var res = null;
    if ($.type($array) === 'array') {
        res = [];
    }
    if ($.type($array) === 'object') {
        res = {};
    }
    if (res != null) {
        $.each($array, function (index, item) {
            if (item != $value) {
                if ($.type($array) === 'array') {
                    res.push(item);
                }
                if ($.type($array) === 'object') {
                    res[index] = item;
                }
            }
        });
    } else {
        res = $array;
    }
    return res;
}

function array_del_key($array, $value) {
    var res = null;
    if ($.type($array) === 'array') {
        res = [];
    }
    if ($.type($array) === 'object') {
        res = {};
    }
    if (res != null) {
        $.each($array, function (index, item) {
            if (index != $value) {
                if ($.type($array) === 'array') {
                    res.push(item);
                }
                if ($.type($array) === 'object') {
                    res[index] = item;
                }
            }
        });
    } else {
        res = $array;
    }
    return res;
}

function in_array($value, $array) {
    var res = false;
    $.each($array, function (index, item) {
        if (item == $value) {
            res = true;
            return true;
        }
    });
    return res;
}

function indexOf_array($value, $array) {
    var res = false;
    if ($value) {
        $value += '';
        for (var i in $array) {
            if ($value.toLowerCase().indexOf($array[i].toLowerCase()) >= 0) {
                return true;
            }
        }
    }
    return false;
}

function in_array_key($value, $array) {
    var res = false;
    $.each($array, function (index, item) {
        if (index + '' == $value + '') {
            res = true;
            return;
        }
    });
    return res;
}

function createLinkButton($script, $title, $iconclass, $text) {
    var html = '<a id="linkbutton" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" plain="true" '
        + ' onclick="' + $script + '" href="javascript:void(0)" group="" title="' + $title + '">'
        + '<span class="l-btn-left l-btn-icon-left">'
        + '<span class="l-btn-text">' + $text + '</span>'
        + '<span class="l-btn-icon ' + $iconclass + '"> </span>'
        + '</span>'
        + '</a>';
    return html;
}

function formatdate(value) {
    if (value) {
        value = value.split(' ')[0];
        var d = value.split("-");
        return d[2] + "/" + d[1] + "/" + d[0];
    } else {
        return '';
    }
}

function getLoadingHTML(msg, type) {
    if (!type) {
        var num = Math.floor((Math.random() * 10) + 1);
        if (num == 0) {
            num = 1;
        } else if (num > 4) {
            num = num - 4;
        }
        type = num;
    } else if (type > 4) {
        type = 4;
    }
    msg = '<div class="spinner-container spinner'
        + type + '"><div id="time-progress-bar" style="width:28px;text-align:center;padding-top:7px"></div></div>'
        + '<span style="position: absolute; padding: 5px;">' + (msg ? msg : 'Đang tải ...') + '</span>';
    return msg;
}

function getErrorHTML() {
    html = '<div><span style="padding: 5px;color: #ff8700;" title="Lỗi khi tải dữ liệu" class="glyphicon glyphicon-warning-sign"></span></div>';
    return html;
}
function decodeHTML(encodedStr) {
    return $("<div/>").html(encodedStr).text();
}
function removeUnicode(str) {
    if (typeof str != 'string') {
        return str;
    }
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|$|_/g, "");
    str = str.replace(/-+-/g, " ");
    str = str.replace(/^\-+|\-+$/g, " ");
    return str;
}
function setCookie(c_name, value, exdays) {
    exdays || (exdays = 60 * 60 * 24);
    var exdate = new Date();
    exdate.setDate(exdate.getDate() + exdays);
    var c_value = escape(value) + ((exdays == null) ? "" : "; expires=" + exdate.toUTCString());
    document.cookie = c_name + "=" + c_value;
};

function getCookie(c_name) {
    var i, x, y, ARRcookies = document.cookie.split(";");
    for (i = 0; i < ARRcookies.length; i++) {
        x = ARRcookies[i].substr(0, ARRcookies[i].indexOf("="));
        y = ARRcookies[i].substr(ARRcookies[i].indexOf("=") + 1);
        x = x.replace(/^\s+|\s+$/g, "");
        if (x == c_name) {
            return unescape(y);
        }
    }
}

function showSpinner(id) {
    if (id === 'tmp_layout') {
        dialogCloseAll();
    }
    if (!$CFG.spinController) {
        $CFG.spinController = {}
    }
    $CFG.spinController[id] = true;
}

function hiddenSpinner(id) {
    delete $CFG.spinController[id];
}

function setCacheProcess(id, msg) {
    statusloadingCache || (statusloadingCache = {});
    id || (id = Math.random());
    statusloadingCache[id] = msg;
    return id;
}

function delCacheProcess(delAll) {
    if (delAll) {
        statusloadingCache = {};
    } else {
        if (count(statusloadingCache) > 0) {
            delete statusloadingCache[Object.keys(statusloadingCache)[Object.keys(statusloadingCache).length - 1]];
        }
    }
}

statusloading = function (id, msg) {
    setCacheProcess(id, msg);
    setTimeout(function () {
        var thongbao = 'Đang xử lý...';
        if (typeof msg === 'string') {
            thongbao = msg;
        }
        $('#statusloading-mash').show().find('.spinner-text').html(thongbao);
    }, 0);
}
statusloadingclose = function (closeAll) {
    angular.element(document).ready(function () {
        delCacheProcess(closeAll);
        if (count(statusloadingCache) == 0) {
            setTimeout(function () {
                $('#statusloading-mash').hide();
            }, 100);
        }
    });
}
initStatusLoading = function () {
    var html = '<div id="statusloading-mash" style="display: none;"><div class="spinner-container-loading"><div class="spinner-container btn-hover spinner3" id="spinner-container"> <div id="time-progress-bar"></div></div><span class="spinner-text">Đang xử lý ...</span></div></div>';
    var spinner = $('#statusloading-mash');
    if (!spinner.length) {
        spinner = $(html);
        $('body').append(spinner);
    }
    spinner.children().click(function () {
        spinner.css({display: 'none'});
        statusloadingCache = {};
    })
}

function number_stand(value) {
    value || (value = 0);
    value += '';
    var tmp = value.split('.');
    if (tmp.length == 1) {
        value = parseInt(tmp[0]) + '';
    } else {
        tmp[0] = parseInt(tmp[0]);
        value = tmp.join('.');
    }
    return value;
}

function digit_grouping(value, ext) {
    $CFG || ($CFG = {});
    $CFG.digit_grouping_char || ($CFG.digit_grouping_char = ',');
    var phancach = $CFG.digit_grouping_char;
    var thapphan = '.';
    phancach || (phancach == ',')
    if (phancach == '.') {
        thapphan = ',';
    }
    if (ext == undefined) {
        ext = 0;
    }
    if (value == '' || value + '' === '0' || isNaN(value)) {
        return ext;
    }
    var num = 3;
    value || (value = 0);
    value += '';
    var test_symboy = (3 / 2) + '';
    var symboy = '.';
    var smb_float = ',';
    if (test_symboy.split('.').length > 1) {
        symboy = ',';
        smb_float = '.';
    }
    if (value.length <= 3) {
        value = value.replace(smb_float, thapphan);
        return value;
    } else {
        if (value.split(smb_float).length == 2) {
            if (value.split(smb_float)[0].length <= 3 && value.split(smb_float)[1].length <= 3) {
                value = value.replace(smb_float, thapphan);
                return value;
            }
        }
    }
    var dau = '';
    if (value.split('-').length > 1) {
        dau = '-';
        value = value.replace('-', '');
    }
    value = value.split(smb_float);
    var tmp = value[0];
    if (value.length == 1) {
        tmp = parseInt(value[0]) + '';
    }
    if (tmp.length > num) {
        var arrn = [];
        var d = 0;
        for (var i = tmp.length - 1; i >= 0; i--) {
            d++;
            arrn.push(tmp[i]);
            if (d % num == 0 && i != 0) {
                arrn.push(phancach);
            }
        }
        value[0] = '';
        for (var i = arrn.length - 1; i >= 0; i--) {
            value[0] += arrn[i];
        }
    }
    return dau + value.join(thapphan);
}

function clone(obj) {
    if (typeof obj === 'object') {
        return JSON.parse(JSON.stringify(obj));
    }
}

/* Xử lý đọc số thành chữ */
var mangso = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];

function dochangchuc(so, daydu) {
    var chuoi = "";
    chuc = Math.floor(so / 10);
    donvi = so % 10;
    if (chuc > 1) {
        chuoi = " " + mangso[chuc] + " mươi";
        if (donvi == 1) {
            chuoi += " mốt";
        }
    } else if (chuc == 1) {
        chuoi = " mười";
        if (donvi == 1) {
            chuoi += " một";
        }
    } else if (daydu && donvi > 0) {
        chuoi = " lẻ";
    }
    if (donvi == 5 && chuc > 1) {
        chuoi += " lăm";
    } else if (donvi > 1 || (donvi == 1 && chuc == 0)) {
        chuoi += " " + mangso[donvi];
    }
    return chuoi;
}

function docblock(so, daydu) {
    var chuoi = "";
    tram = Math.floor(so / 100);
    so = so % 100;
    if (daydu || tram > 0) {
        chuoi = " " + mangso[tram] + " trăm";
        chuoi += dochangchuc(so, true);
    } else {
        chuoi = dochangchuc(so, false);
    }
    return chuoi;
}

function dochangtrieu(so, daydu) {
    var chuoi = "";
    trieu = Math.floor(so / 10000);
    so = so % 10000;
    if (trieu > 0) {
        chuoi = docblock(trieu, daydu) + " triệu";
        daydu = true;
    }
    nghin = Math.floor(so / 1000);
    so = so % 1000;
    if (nghin > 0) {
        chuoi += docblock(nghin, daydu) + " nghìn";
        daydu = true;
    }
    if (so > 0) {
        chuoi += docblock(so, daydu);
    }
    return chuoi;
}

function convert_number_to_words(so) {
    if (so == 0) return mangso[0];
    var chuoi = "", hauto = "", tiento = ' ', phancach = ' ';
    var list = [];
    if ((so + '').split('.').length > 1) {
        phancach = ' phảy';
        return convert_number_to_words((so + '').split('.')[0]) + phancach + convert_number_to_words((so + '').split('.')[1]);
    } else if ((so + '').split('-').length > 1) {
        tiento = 'Âm';
        return tiento + convert_number_to_words((so + '').split('-')[1]);
    } else {
        do {
            ty = so % 10000000;
            so = Math.floor(so / 10000000);
            if (so > 0) {
                chuoi = dochangtrieu(ty, true) + hauto + chuoi;
            } else {
                chuoi = dochangtrieu(ty, false) + hauto + chuoi;
            }
            hauto = " tỷ";
        } while (so > 0);
        return chuoi;
    }
    return chuoi;
}

function getURLParameter(sParam) {
    var sPageURL = window.location.search.substring(1);
    var sURLVariables = sPageURL.split('&');
    for (var i = 0; i < sURLVariables.length; i++) {
        var sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] == sParam) {
            return sParameterName[1];
        }
    }
}

function replaceUrlParam(url, paramName, paramValue) {
    if (paramValue == null) {
        paramValue = '';
    }
    var pattern = new RegExp('\\b(' + paramName + '=).*?(&|#|$)');
    if (url.search(pattern) >= 0) {
        return url.replace(pattern, '$1' + paramValue + '$2');
    }
    url = url.replace(/[?#]$/, '');
    return url + (url.indexOf('?') > 0 ? '&' : '?') + paramName + '=' + paramValue;
}

function exportExcel() {
    var cols = []; /* Init data */
    var colsData = $("[data-col]");
    var lengthCol = colsData.length;
    for (var c = 0; c < lengthCol; c++) {
        if (colsData[c].id !== 'undefined') {
            var colId = colsData[c].id;
            var colElement = document.getElementById(colId);
            var colValue = colElement.getAttribute("data-col");
            var colCell = colElement.getAttribute("data-cells");
            cols.push({key: colCell, value: colValue});
        }
    }

    var cellsExcel = []; /* Init data */
    var cellsData = $("[data-name='cell']");
    var lengthCell = cellsData.length;
    for (var i = 0; i < lengthCell; i++) {
        if (cellsData[i].id !== 'undefined') {
            var cellId = cellsData[i].id;
            var cellElement = document.getElementById(cellId);
            var cellValue = cellElement.innerText;
            if (cellElement.type === 'text') {
                cellValue = cellElement.value;
            }
            var cellsExcelId = cellId.replace("cell-insert.", "");
            cellsExcel.push({key: cellsExcelId, value: cellValue});
        }
    }

    var rowsExcel = []; /* Init data */
    var rowsData = $("[data-name='row']");
    var lengthRow = rowsData.length;
    for (var j = 0; j < lengthRow; j++) {
        if (rowsData[j].id !== 'undefined') {
            var rowId = rowsData[j].id;
            var rowElement = document.getElementById(rowId);
            var rowValue = rowElement.innerText;
            if (rowElement.type === 'text') {
                rowValue = rowElement.value;
            }
            var rowsExcelId = rowId.replace("row-insert.", "");
            rowsExcel.push({key: rowsExcelId, value: rowValue});
        }
    }

    var style = []; /* Init data style */
    var styleData = $("[data-cells]");
    var lengthStyle = styleData.length;
    for (var s = 0; s < lengthStyle; s++) {
        if (styleData[s].id !== 'undefined') {
            var styleId = styleData[s].id;
            var styleElement = document.getElementById(styleId);
            var styleCell = styleElement.getAttribute("data-cells");
            var styleOptions = styleElement.getAttribute("data-options");
            var splitOptions = styleOptions.split(",");

            /*font*/
            var font = {};
            if (splitOptions.indexOf('bold') !== -1)
                font['bold'] = true;
            if (splitOptions.indexOf('italic') !== -1)
                font['italic'] = true;
            var tmp = {key: styleCell, font: font};

            /*merge*/
            if (splitOptions.indexOf('merge') !== -1)
                tmp['merge'] = true;

            /*alignment*/
            if (splitOptions.indexOf('center') !== -1)
                tmp['alignment'] = {'center': true};
            if (splitOptions.indexOf('left') !== -1)
                tmp['alignment'] = {'left': true};
            if (splitOptions.indexOf('right') !== -1)
                tmp['alignment'] = {'right': true};
            style.push(tmp);
        }
    }
    return {
        cells: JSON.stringify(cellsExcel),
        rows: JSON.stringify(rowsExcel),
        style: JSON.stringify(style),
        cols: JSON.stringify(cols),
    };

}


/*Excel new*/

/* attribute: data-row */
function getDataExcelByAttribute(attribute) {
    var data = [];
    var selectors = $('[' + attribute + ']');
    var length = selectors.length;
    for (var i = 0; i < length; i++) {
        var selector = selectors[i];
        var cell = selector.getAttribute(attribute);
        var value = selector.innerText;
        if (selector.type === 'text') {
            value = selector.value;
        }
        data.push({cell: cell, value: value})
    }
    return data;
}

function getDate(strDate) {
    var date = new Date();
    if (strDate)
        date = new Date(strDate);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
}

function getCurrentDate(strDate) {
    var date = new Date();
    if (strDate)
        date = new Date(strDate);
    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    var first = getDayMonthYear(firstDay);
    var now = getDayMonthYear(date);
    var last = getDayMonthYear(lastDay);

    return {
        first: first.day + '/' + first.month + '/' + first.year,
        now: now.day + '/' + now.month + '/' + now.year,
        last: last.day + '/' + last.month + '/' + last.year,
    };
}

function getDayMonthYear(dateObject) {
    var y = dateObject.getFullYear();
    var m = dateObject.getMonth() + 1;
    var d = dateObject.getDate();
    d = (d < 10 ? ('0' + d) : d);
    m = (m < 10 ? ('0' + m) : m);
    return {
        day: d,
        month: m,
        year: y
    };
}

$.angularComplie = function (element, html, callback) { /* Biên dịch html cho popup để chạy angularjs*/
    var form = '<div >' + html + '</div>';
    // $(element).html(form);
    angular.element($(element)).scope().$apply(function (scope) {
        $(element).html(scope.compile(form, scope));
        if (typeof callback === 'function') {
            callback(scope);
        }
    });
};
angular_app = angular.module("angular_app",['ngRoute','ngResource','ngCookies','ngAnimate','textAngular', 'ngStorage'])
.config(['$routeProvider','$locationProvider',function ($routeProvider,$locationProvider) {
    $locationProvider.html5Mode(true);
    $routeProvider
    .when('/dinhduong', {
        template: ''+'<input type="hidden" value="">',
        controller: function($scope) {
            $scope.menu.page = 'index';
            $scope.$CFG.spinController = {};
            dialogCloseAll();
            /*Chạy biểu tượng tổng đâì hỗ trợ xuống góc bên phải*/
            angular.element(document).ready(function(){
                setTimeout(function(){
                    $('.content-info-help').addClass('content-info-help-running');
                },200);
            })
        }
    })
    .when('/dinhduong/view/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                'list.html'
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/dinhduong/view/:module/:action', {
        templateUrl: function(request){
            console.log(request);
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                'templates',
                $CFG.project,
                request.module,
                request.action+'.html'
            ];
            delete request.module;
            delete request.action;
            var req = ['?_='+Math.random().toString().split('.')[1]];
            for(var key in request) {
                req.push(key+'='+request[key]);
            }
            return urls.join('/')+req.join('&');
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose();
        }
    })
    .when('/dinhduong/:module/:action', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action
            ];
            return urls.join('/')+'/'+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose();
        }
    })
    .when('/dinhduong/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                'list'
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/dinhduong/:module/:action/:id', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action,
                request.id
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .otherwise({redirectTo:'/dinhduong'})
}]).config(['$compileProvider', function($compileProvider){
    $compileProvider.debugInfoEnabled(true);
}]);
angular_app.controller('appController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', function ($scope, $routeParams, $compile, MyCache, $filter, $cookies) {
    $scope.$CFG = $CFG;
    $scope.namhoc = parseInt($CFG.namhoc);
    $scope.$CFG || ($scope.$CFG = {});
    $scope.$CFG.round_number_config || ($scope.$CFG.round_number_config = 3);
    $scope.$CFG.digit_grouping_char || ($scope.$CFG.digit_grouping_char = ',');
    $scope.window = {
        screen: {
            width: $(window).width(),
            height: $(window).height()
        }
    };
    $(window).resize(function () {
        screen_size = {
            width: $(window).width(),
            height: $(window).height()
        };
        setTimeout(function () {
            $scope.$apply(function () {
                $scope.window.screen = screen_size;
            });
        });
    });

    $scope.filter = function (arr, key) {
        return $filter('filter')(arr, key);
    };
    $scope.rand = function (min, max) {
        return rand(min, max);
    };
    $scope.compile = function (html, scope) {
        if (scope) {
            return $compile(html)(scope);
        } else {
            return $compile(html)($scope);
        }
    };
    $scope.parseInt = function (value) {
        value || (value = 0);
        return parseInt(value);
    };
    $scope.parseFloat = function (value) {
        value || (value = 0);
        return parseFloat(value);
    };
    $scope.NaN = function (value, resp) {
        if (resp == undefined) {
            resp = '';
        }
        if (value + '' == 'NaN') {
            value = resp;
        }
        return value;
    };
    $scope.digit_grouping = function (value, num) {
        return digit_grouping(value, num);
    };
    $scope.number_stand = function (value) {
        return number_stand(value);
    };
    $scope.round = function (value, num, get_default) {
        return round(value, num, get_default);
    };
    $scope.safeRound = function (val) {
        val = Number(val);
        if (val < 0.0005) {
            val = 0.001;
        } else if (val < 0.005) {
            val = 0.01;
        } else if (val <= 0.07) {
            val = round(val, 2);
        } else {
            val = round(val, 1);
        }
        return val;
    };

    $scope.divide = function (total, values, notsaferound) {
        var all = 0;
        var rs = {};
        angular.forEach(values, function (val, key) {
            rs[key] = 0;
            all += val;
        });
        if (total != all) {
            tong = 0;
            var max_val = {point: 1, value: 0};
            var min_val = {point: 1, value: 1000000};
            angular.forEach(values, function (sotre, point) {
                if (sotre == 0) {
                    rs[point] = 0;
                    return;
                }
                var tile = sotre / all;
                var val = $['*'](tile, total);
                if (!notsaferound) {
                    val = $scope.safeRound(val);
                } else {
                    val = round(val, 3);
                }
                tong = $['+'](tong, val);
                rs[point] = val;
                if (max_val.value < val) {
                    max_val = {point: point, value: val};
                }
                if (min_val.value > val) {
                    min_val = {point: point, value: val};
                }
            });
            if (total != tong) {
                if (total - tong > 0) {
                    rs[min_val.point] = $['+'](rs[min_val.point], $['-'](total, tong));
                } else {
                    rs[max_val.point] = $['+'](rs[max_val.point], $['-'](total, tong));
                }
            }
        } else {
            rs = values;
        }
        return rs;
    };
    /*
        * Mở form hướng dẫn tắt cảnh báo lỗi khi mở tệp excel được tải trực tiếp ở form in
        * */
    $scope.formHelperFixErrorOpenExcel = function () {
        $.dm_datagrid.showAddForm({
            title: 'Thông báo',
            size: size.wide,
            fullScreen: true,
            showButton: false,
            draggable: true,
            content: function (dialog) {
                loadForm($CFG.remote.base_url + '/templates/dinhduong/helper-fix-error-open-excel.html', '', {async: true}, function (resp) {
                    $scope.$apply(function () {
                        $(dialog).html($scope.compile(resp, $scope));
                    });
                });
            }
        });
    };
    /*
    * Chia tỉ lệ theo chiều dọc danh sách thực phẩm xuất kho nhiều giá
    * */
    $scope.divideH = function (vals, exps) {
        var tmp = clone(exps);
        var mau = {};
        var i = 1;
        angular.forEach(exps, function (food, food_id_price) {
            mau[i] = food.quantity;
            food.quantities = {};
            i++;
        });
        angular.forEach(vals, function (val, point) {
            var i = 1;
            var arr = $scope.divide(val, mau);
            angular.forEach(exps, function (food, food_id_price) {
                if (val == 0) {
                    food.quantities[point] = 0;
                } else {
                    food.quantities[point] = arr[i];
                    i++;
                }
            });
        });
        return exps;
    };
    $scope.mathRound = function (value) {
        return Math.round(value);
    };
    $scope.Infinity = function (value, ext) {
        return $Infinity(value, ext);
    };
    $scope.count = function (arr) {
        return count(arr);
    };
    $scope.sum = function (arr) {
        var rs = 0;
        for (var i in arr) {
            rs += Number(arr[i]);
        }
        return rs;
    };
    $scope.num2array = function (num) {
        var rs = [];
        if (typeof num == 'string') {
            num = Number(num);
        }
        if (typeof num == 'number') {
            for (var i = 0; i < num; i++) {
                rs.push(i + 1);
            }
        } else if (angular.isArray(num)) {
            for (var i = num[0]; i < num[1]; i++) {
                rs.push(i);
            }
        }
        return rs;
    };
    $scope.convert_number_to_words = function (num) {
        return convert_number_to_words(num);
    };
    $scope.in_array = function (value, arr) {
        return in_array(value, arr);
    };
    $scope.objToArray = function (obj) {
        if (typeof obj === 'object') {
            return Object.values(obj);
        }
    };
    setTimeout(function () {
        var view_container = $('.angular-view-container');
        var screen = $('body');
        if (!view_container) {
            return;
        }
        $scope.$apply(function () {
            if (screen) {
                $scope.screen = {
                    width: screen.css('width').replace('px', ''),
                    height: screen.css('height').replace('px', '')
                }
            }
            if (view_container.length) {
                $scope.view = {
                    width: view_container.css('width').replace('px', ''),
                    height: view_container.css('height').replace('px', '')
                }
            }
        })
    }, 1000);
}])
    .config(['$compileProvider', function ($compileProvider) {
        $compileProvider.debugInfoEnabled(true);
    }])
    .filter('date2form', ['$sce', function ($sce) {
        var div = document.createElement('div');
        return function (date, type) {
            var d = '';
            if (date != undefined && date != '') {
                type || (type = '/');
                d = date.split(' ')[0].split('-');
                var dt = [];
                if (d[2]) dt.push(d[2]);
                if (d[1]) dt.push(d[1]);
                if (d[0]) dt.push(d[0]);
                d = dt.join(type);
            }
            return d;
        };
    }])
    .filter('decodeHTML', ['$sce', function ($sce) {
        var div = document.createElement('div');
        return function (text) {
            text || (text = '');
            div.innerHTML = $('<textarea />').html(text).text();
            return $sce.trustAsHtml($('<textarea />').html(text).text());
        };
    }])
    .filter('parseFloat', [function ($sce) {
        return function (value) {
            var val = parseFloat(value);
            return val;
        };
    }])
    .filter('round', ['$sce', function ($sce) {
        return function (value, num) {
            num || (num = 2);
            var v = (value + '').split('.');
            if (v.length == 2) {
                v[1] = v[1].substring(0, num);
            } else {
                v.push('0');
            }
            return parseFloat(v[0] + '.' + v[1]);
        };
    }])
    .factory('MyCache', function ($cacheFactory) {
        return $cacheFactory('myCache');
    })

    .directive('menuDropdownHover', function () {
        return {
            link: function (scope, element) {
                $(element).hover(
                    function () {
                        $(this).addClass('open')
                    },
                    function () {
                        $(this).removeClass('open')
                    }
                )
            }
        };
    })
    .directive('iconHover', function () {
        return {
            link: function (scope, element) {
                $(element).hover(
                    function () {
                    }
                )

            }
        };
    })
    .directive('focusMe', function ($timeout) {
        return {
            scope: {trigger: '=focusMe'},
            link: function (scope, element) {
                scope.$watch('trigger', function (value) {
                    if (value === true) {
                        $timeout(function () {
                            element.focus();
                        });
                    }
                });
            }
        };
    })
    .directive('meFocus', function ($timeout) {
        return {
            scope: {meFocus: '&meFocus'},
            link: function (scope, element) {
                $(element).focus(function () {
                    /*console.log(element);*/
                    scope.meFocus();
                });
            }
        };
    })
    .directive('numberFormat', function ($timeout) {
        return {
            scope: {
                numberFormat: '@numberFormat',
                ngModel: '=ngModel'
            }, link: function (scope, element, attrs) {
                var type = scope.numberFormat;
                if (!type || type != 'float') {
                    type = 'int';
                } else {
                    type = 'float';
                }
                var char_sp = [0, 8];
                $(element).keypress(function (e) {
                    if (e.which >= 48 && e.which <= 57 || e.which == 46) {
                        /*console.log(type,e.which);*/
                        if (type == 'int' && e.which == 46) {
                            return false;
                        } else {
                            var val = $(this).val();
                            if (val.split('.').length > 1 && e.which == 46) {
                            } else {
                                return false;
                            }
                        }
                    } else {
                        if (!in_array(e.which, char_sp)) {
                            return false;
                        }
                    }
                    if (attrs.ngModel) {
                        scope.ngModel = Number($(this).val());
                    }
                })
            }
        };
    })
    .directive('typeNumber', function ($timeout) {
        return {
            scope: {
                typeNumber: '@typeNumber',
                ngModel: '=ngModel',
                numAbs: '=numAbs',
                min: '=min',
                max: '=max'
            }, link: function (scope, element, attrs) {
                var type = scope.typeNumber;
                if (!type || type != 'float') {
                    type = 'int';
                } else {
                    type = 'float';
                }
                if (!attrs.title) {
                    if (type == 'float') {
                        $(element).attr('title', 'Định dạng phải là số');
                    } else {
                        $(element).attr('title', 'Chỉ chấp nhận số nguyên');
                    }
                }
                $(element).keypress(function (e) {
                    var key = e.which;
                    var rs = false;
                    /*
                    * 46: dấu chấm;
                    * 45: dấu trừ;
                    * */
                    if (key >= 48 && key <= 57 || key === 46 || key === 45) {
                        rs = true;
                        var val = $(this).val();
                        if (key === 46 && (type === 'int' || type === 'float' && $(this).val().split('.').length > 1)) {
                            rs = false;
                        }
                        if (key === 45 && (scope.numAbs || val.split('-').length > 1)) {
                            rs = false;
                        }
                    }
                    return rs;
                });
                $(element).keyup(function (e) {
                    if (e.which === 190) {
                        var val = $(this).val();
                        if (val.split('.').length > 1 && val.split('.')[0] === '') {
                            $(this).val(0 + val);
                        }
                    }
                    if (e.which == 189 || e.which == 173) {
                        var val = $(this).val();
                        if (val.split('-').length > 1) {
                            val = val.replace('-', '');
                            $(this).val('-' + val);
                        }
                    }
                    if (typeof scope.min != "undefined") {
                        if (Number($(this).val()) < scope.min) {
                            $(this).val(scope.min);
                        }
                    }
                    if (typeof scope.max != "undefined") {
                        if (Number($(this).val()) > scope.max) {
                            $(this).val(scope.max);
                        }
                    }
                }).blur(function () {
                    scope.ngModel = Number($(this).val());
                });
            }
        };
    })
    .directive('parseInt', function ($timeout) {
        return {
            scope: {
                parseInt: '=parseInt'
            }, link: function (scope, element, attrs) {
                $(element).keyup(function (e) {
                    var val = $(element).val();
                    if (val) {
                        val = val.replace(',', '.');
                        val = val.replace('.', '');
                        val || (val = 0);
                        val = parseInt(val);
                    } else {
                        val = 0;
                    }
                    $(element).val(val);
                    scope.trigger = val;
                })
            }
        };
    })
    .directive('parseFloat', function ($timeout) {
        return {
            scope: {
                parseInt: '=parseInt'
            }, link: function (scope, element, attrs) {
                $(element).keyup(function (e) {
                    var val = $(element).val();
                    if (val) {
                        val = val.replace(',', '.');
                        val = val.replace('..', '.');
                        val || (val = 0);
                        if (e.key != '.') {
                            val = parseFloat(val);
                        }
                    } else {
                        val = 0;
                    }
                    $(element).val(val);
                    scope.trigger = val;
                })
            }
        };
    })
    .directive('dateBox', function ($timeout) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=dateBox',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                dateBoxDisabled: '=dateBoxDisabled',
                onSelect: '&onSelect',
                onLoad: '&onLoad',
                build: '=build'
            },
            link: function (scope, element, attrs) {
                element.begin = true;
                var options = {
                    value: scope.trigger,
                    formatter: myformatter,
                    parser: dateparser,
                    onSelect: function (date) {
                        var val_new = dateboxOnSelect(date);
                        var val_old = element.oldValue;
                        onChange(val_new, val_old);
                    }, onChange: function (date) {
                    },
                    width: scope.boxWidth,
                    height: scope.boxHeight
                };
                scope.onLoad();
                var el = element.datebox(options);
                scope.$watch('trigger', function (value) {
                    el.datebox('setValue', scope.trigger);
                });
                el.next().children('input:first').bind('blur', function () {
                    var val_new = $(this).val();
                    var val_old = element.oldValue;
                    onChange(val_new, val_old);
                }).bind('focus', function () {
                    element.oldValue = $(this).val();
                }).click(function () {
                    element.oldValue = $(this).val();
                });

                function onChange(value_new, value_old) {
                    if (value_new != value_old) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.trigger = value_new;
                            });
                            scope.$apply(function () {
                                scope.onSelect();
                            });
                        });

                    }
                }
            }
        };
    })
    .directive('vtDraggable', function ($timeout) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=vtDraggable'
            },
            compile: function($element, $attrs) {
                $($element).addClass('draggable-container');
                var check = false;
                $element.find('button.btn-print').each(function (index, el) {
                    var click = $(el).attr('ng-click');
                    click || (click = $(el).attr('onclick'));
                    if (click) {
                        if (click.split('Export.file').length > 1) {
                            check = true;
                        }
                    }
                });
                if (check) {
                    $element.append('<span class="btn glyphicon glyphicon-info-sign color-orange btn-over-blue" ng-click="formHelperFixErrorOpenExcel()" title="Hướng dẫn sửa lỗi khi mở tệp excel"></span>');
                }
                $($element).draggable();
                return $element;
            }
        };
    })
    .directive('comboBox', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=comboBox',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                url: '@url',
                data: '=data',
                delay: '@delay',
                mode: '@mode',
                paramKey: '@paramKey',
                valueField: '@valueField',
                textField: '@textField',
                paramValue: '=paramValue',
                clearForNull: '=clearForNull',
                reloadOnSelected: '=reloadOnSelected',
                clearOnSelected: '=clearOnSelected',
                onSelect: '&onSelect',
                onLoadSuccess: '&onLoadSuccess'
            }, controller: function ($scope, $element, $attrs, $transclude) {

            },
            link: function (scope, element, attrs, ctrls) {
                var created = false;
                var is_selected = false;
                var queryParams = {};
                scope.valueField || (scope.valueField = 'id');
                scope.textField || (scope.textField = 'text');
                scope.delay || (scope.delay = 700);
                scope.mode || (scope.mode = 'remote');
                if (scope.paramKey) {
                    queryParams[scope.paramKey] = scope.paramValue;
                }
                var option = {
                    valueField: scope.valueField,
                    textField: scope.textField,
                    /*panelHeight:'auto',*/
                    mode: scope.mode,
                    delay: scope.delay,
                    onSelect: function (row, el, test) {
                        is_selected = true;
                    }, onLoadSuccess: function (data, el) {
                        is_selected = false;
                        $(element).combobox('panel').children().click(function (e) {
                            element.onSelected();
                        });
                        $(element).next().children('input').keyup(function (e) {
                            if (e.which == 13) {
                                element.onSelected();
                            }
                        });
                        scope.data = data;
                        scope.onLoadSuccess();
                        created = true;
                    },
                    queryParams: queryParams,
                    width: 170,
                    height: 30
                };
                element.onSelected = function () {
                    setTimeout(function () {
                        var id = $(element).combobox('getValue');
                        var item = {};
                        var data = $(element).combobox('getData');
                        angular.forEach(data, function (fd, ind) {
                            if (id + '' == fd[scope.valueField] + '') {
                                item = fd;
                            }
                        });
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.trigger = item;
                            });
                            scope.$apply(function () {
                                scope.onSelect();
                            });
                        });
                        if (scope.clearForNull) {
                            $(element).combobox('clear');
                        }
                    });
                };
                if (scope.trigger) {
                    option.value = scope.trigger[scope.valueField];
                }
                if (scope.boxWidth) {
                    option.width = scope.boxWidth;
                }
                scope.$watch('data', function (value) {
                    if (value) {
                        $(element).combobox('loadData', value);
                    } else {

                    }
                });
                scope.$watch('trigger', function (value) {
                    if (created) {
                        if (value != undefined) {
                            var data = scope.data;
                            var id = value[scope.valueField];
                            var index = -1;
                            angular.forEach(data, function (item, ind) {
                                if (value[scope.valueField] == item[scope.valueField]) {
                                    index = ind;
                                }
                            });
                        }
                    }
                });
                scope.$watch('paramValue', function (value) {
                    if (scope.mode != 'local') {
                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {
                            var queryParams = $(element).combobox('options').queryParams;
                            if (scope.paramKey) {
                                queryParams[scope.paramKey] = scope.paramValue;
                            }
                            $(element).combobox({queryParams: queryParams});
                        }
                    } else {
                        if (in_array(typeof scope.data, ['object'])) {
                            if (in_array(typeof value, ['object'])) {
                                var data_new = [];
                                angular.forEach(scope.data, function (item, index) {
                                    if (!in_array(item[scope.valueField], value)) {
                                        data_new.push(item);
                                    }
                                });
                                $(element).combobox('loadData', data_new).combobox('clear');
                            }
                        }
                    }
                });
                var url = scope.url;
                // if(scope.data != undefined){
                //     url = scope.data;
                // }
                $.dm_datagrid.combobox(element, url, option);
            }
        };
    })
    .directive('infConfigs', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                infConfigs: '=infConfigs',
                infId: '@infId',
                url: '@url',
                resp: '=resp',
                onSuccess: '&onSuccess',
                onError: '&onError',
                infDefault: '=infDefault',
                ngTrueValue: '=ngTrueValue',
                ngFalseValue: '=ngFalseValue',
                onChange: '&onChange',
                ngValue: '=ngValue'
            }, controller: function ($scope, $element, $attrs, $transclude) {
                $scope.value_old = $scope.infConfigs;
            },
            link: function (scope, element, attrs, ctrls) {
                var ngTrueValue = true;
                var ngFalseValue = false;
                if (typeof attrs.ngFalseValue != 'undefined') {
                    ngFalseValue = scope.ngFalseValue;
                }
                if (typeof attrs.ngTrueValue != 'undefined') {
                    ngTrueValue = scope.ngTrueValue;
                }
                if (scope.infConfigs === undefined) {
                    if (attrs.type === 'checkbox') {
                        scope.infConfigs = ngFalseValue;
                    } else {
                        scope.infConfigs = scope.infDefault;
                    }
                }
                if (!scope.url) {
                    scope.url = [$CFG.remote.base_url, 'information_configs', $CFG.project].join('/');
                }
                if (attrs.type === 'checkbox') {
                    if (scope.infConfigs === ngTrueValue) {
                        $(element).prop('checked', true);
                    }
                } else if (attrs.type === 'radio') {
                    if (scope.ngValue === scope.infConfigs) {
                        $(element).prop('checked', true);
                    } else {
                        $(element).prop('checked', false);
                    }
                } else if (attrs.type === 'number') {
                    console.log('set default', scope.infDefault)
                    $(element).val(scope.infDefault);
                } else {
                    $(element).focus(function () {
                        scope.value_old = scope.infConfigs;
                    });
                }
                if (attrs.type === 'checkbox') {
                    $(element).change(function () {
                        var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);
                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {
                            scope.$apply(function () {
                                scope.infConfigs = value;
                            });
                        }, function () {
                            if (typeof scope.onError == 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, scope.infAlert, false);
                    })
                } else if (attrs.type === 'radio') {
                    $(element).click(function () {
                        var value = scope.ngValue;
                        process(scope.url, {async: true, id: scope.infId, value: value}, function (resp) {
                            scope.$apply(function () {
                                scope.infConfigs = value;
                            })
                        }, function () {
                            if (typeof scope.onError == 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, scope.infAlert, false);
                    })
                } else {
                    $(element).blur(function () {
                        /* directive id is array */
                        var arr = scope.infId.split('.');
                        var id = angular.copy(scope.infId);
                        var value = angular.copy(scope.infConfigs);

                        if (arr.length >= 1) {
                            id = arr[0];
                            for (var i = arr.length; i > 0; i--) {
                                var tmp = {};
                                tmp[arr[i - 1]] = value;
                                value = tmp;
                            }
                            value = value[id];
                        }

                        if (scope.value_old !== scope.infConfigs) {
                            process(scope.url, {async: true, id: id, value: value}, function (resp) {
                                scope.$apply(function () {
                                    if (typeof scope.onSuccess == 'function') {
                                        scope.onSuccess();
                                    }
                                })
                            }, function () {
                                if (typeof scope.onError == 'function') {
                                    scope.$apply(function () {
                                        scope.onError();
                                    });
                                }
                            }, scope.infAlert, false);
                        }
                    }).keyup(function () {
                        console.log(111111111111)
                        scope.$apply(function () {
                            scope.infConfigs = $(element).val();
                        });
                    });
                }
                scope.$watch('infConfigs', function (value) {
                    if (attrs.type === 'checkbox') {
                        if (scope.ngTrueValue + '' === value + '') {
                            $(element).prop('checked', true);
                        } else {
                            $(element).prop('checked', false);
                        }
                        scope.onSuccess();
                    } else if (attrs.type === 'radio') {
                        scope.onSuccess();
                    } else {
                        $(element).val(value);
                    }
                    scope.onChange();
                })
            }
        };
    })
    .directive('countFbComment', function ($timeout) {
        return {
            scope: {trigger: '=countFbComment'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-comments-count" data-href="' + attrs.url + value + '"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('helperInclude', function ($timeout) {
        return {
            scope: {
                trigger: '=helperInclude',
                url: '@url',
                key: '@key'
            },
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    var data = {};
                    if (scope.key) {
                        data[scope.key] = scope.trigger;
                    }
                    loadForm(scope.url, '', data, function (resp) {
                        $(element).html(resp);
                    })
                });
            }
        };
    })
    .directive('addId', function ($timeout) {
        return {
            scope: {trigger: '=addId'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    $(element).attr('id', value);
                });
            }
        };
    })
    .directive('classHidden', function ($timeout) {
        return {
            scope: {trigger: '=classHidden'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('hidden');
                    } else {
                        $(element).removeClass('hidden');
                    }
                });
            }
        };
    })
    .directive('classActive', function ($timeout) {
        return {
            scope: {trigger: '=classActive'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('active');
                    } else {
                        $(element).removeClass('active');
                    }
                });
            }
        };
    })
    .directive('idActive', function ($timeout) {
        return {
            scope: {trigger: '=idActive'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $(element).addClass('active');
                    } else {
                        $(element).removeClass('active');
                    }
                });
            }
        };
    })
    .directive('viewFbComments', function ($timeout) {
        return {
            scope: {trigger: '=viewFbComments'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-comments" data-href="' + attrs.url + value + '" width="100%" datanumposts="5"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('shareFb', function ($timeout) {
        return {
            scope: {trigger: '=shareFb'},
            link: function (scope, element, attrs) {
                scope.$watch('trigger', function (value) {
                    if (value) {
                        $timeout(function () {
                            element.append('<div class="fb-share-button" data-href="' + attrs.url + value + '" data-layout="button_count" data-mobile-iframe="true"></div>');
                            reloadFace();
                        });
                    }
                });
            }
        };
    })
    .directive('myEnter', function () {
        return function (scope, element, attrs) {
            element.bind("keydown keypress", function (event) {
                if (event.which === 13) {
                    scope.$apply(function () {
                        scope.$eval(attrs.myEnter);
                    });
                    event.preventDefault();
                }
            });
        };
    })
    .directive('starHoverChangeColor', function ($timeout) {
        return {
            restrict: 'A',
            compile: function (element, attrs) {
                return function (scope, element, attrs) {
                    $timeout(function () {
                        if (attrs.starHoverChangeColor && taikhoan != '') {
                            var p = element.parent();
                            var no = parseInt(attrs.field);
                            element.hover(function () {
                                if (scope.monan.dabinhchon) {
                                    element.attr('title', 'Bạn đã bình chọn món ăn này');
                                    return;
                                }
                                p.children().each(function (index, el) {
                                    if (index < no) {
                                        $(el).addClass('star-hover');
                                    } else {
                                        $(el).removeClass('star-hover');
                                    }
                                })
                            }, function () {
                                p.children().removeClass('star-hover');
                            })
                                .click(function () {
                                    if (scope.monan.dabinhchon) {
                                        return;
                                    }
                                    var arr_data = {
                                        diem: attrs.starHoverChangeColor,
                                        mamn: attrs.maMn,

                                    };
                                    var data = JSON.stringify(arr_data);

                                    $.ajax({
                                        type: "POST",
                                        url: base_url + don_vi + '/_xuly/mon_ngon/xulyBC',
                                        timeout: 120000,
                                        data: {data: data},
                                        dataType: "json",
                                        success: function (resp) {
                                            if (resp.result == "success") {
                                                alert("Bạn đã bình chọn thành công. Cảm ơn!");
                                                scope.monan.dabinhchon = true;
                                                scope.$apply(scope.$eval(attrs.mnUpdate));
                                            } else if (resp.result == "fail") {
                                                alert(resp.err);
                                            } else {
                                                var errors = "";
                                                $.each(resp.errors, function (i, v) {
                                                    errors += "- " + v + "\n";
                                                });
                                                alert('Lỗi!', errors);
                                            }
                                        }
                                    });
                                })
                        } else {
                            element.attr('title', 'Đăng nhập để bình chọn');
                        }
                    })
                }
            }
        };
    })
    .directive('fileUpload', function () {
        return {
            scope: {
                fileUpload: '=fileUpload',
                url: '@url',
                urlBind: '=urlBind',
                format: '@format',
                size: '@size',
                uploadDone: '&uploadDone'
            },
            link: function (scope, element, attrs) {
                var url = scope.url;
                url || (url = scope.urlBind);
                if (url) {
                    scope.size || (scope.size = 2);
                    scope.files || (scope.files = 'files');
                    scope.format || (scope.format = 'zip|rar|7z|doc|docx|pdf|xls|xlsx|jpg|png|gif');
                    element.attr('name', scope.files + '[]').attr('type', 'file');
                    init_uploader(element, url, scope.format, scope.size, function (resp) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.fileUpload = resp.data;
                            })
                        })
                    });
                }
                scope.$watch('fileUpload', function (value) {
                    if (typeof value !== 'undefined') {
                        scope.uploadDone();
                    }
                });
            }
        }
    }).directive('dynamic', function () {
    return {
        restrict: 'A',
        scope: {
            dynamic: '=dynamic',
            dynamicId: '@dynamicId',
            url: '@url',
            resp: '=resp',
            onSuccess: '&onSuccess',
            onError: '&onError',
            ngTrueValue: '=ngTrueValue',
            ngFalseValue: '=ngFalseValue',
            ngValue: '=ngValue'
        }, controller: function ($scope) {
            $scope.value_old = $scope.dynamic;
        },
        link: function (scope, element, attrs) {
            var ngTrueValue = true;
            var ngFalseValue = false;
            if (attrs.ngFalseValue !== undefined) {
                ngFalseValue = scope.ngFalseValue;
            }
            if (attrs.ngTrueValue !== undefined) {
                ngTrueValue = scope.ngTrueValue;
            }
            if (scope.dynamic === undefined) {
                if (attrs.type === 'checkbox') {
                    scope.dynamic = ngFalseValue;
                } else if (attrs.type === 'radio') {

                } else {
                    scope.dynamic = '';
                }
            }
            if (!scope.url) {
                scope.url = [$CFG.remote.base_url, 'dynamic-variable/update'].join('/');
            }
            if (attrs.type === 'checkbox') {
                if (scope.dynamic === ngTrueValue) {
                    $(element).prop('checked', true);
                }
            } else if (attrs.type === 'radio') {
                if (scope.ngValue === scope.dynamic) {
                    $(element).prop('checked', true);
                } else {
                    $(element).prop('checked', false);
                }
            } else {
                $(element).focus(function () {
                    scope.value_old = scope.dynamic;
                })
            }
            if (attrs.type === 'checkbox') {
                $(element).change(function () {
                    var value = ($(element).is(':checked') ? ngTrueValue : ngFalseValue);
                    process(scope.url, {
                        async: true,
                        id: scope.dynamicId,
                        value: value,
                        pathname: window.location.pathname
                    }, function () {
                        scope.$apply(function () {
                            scope.dynamic = value;
                        })
                    }, function () {
                        if (typeof scope.onError === 'function') {
                            scope.$apply(function () {
                                scope.onError();
                            })
                        }
                    }, false, false);
                })
            } else if (attrs.type === 'radio') {
                $(element).click(function () {
                    var value = scope.ngValue;
                    process(scope.url, {
                        async: true,
                        id: scope.dynamicId,
                        value: value,
                        pathname: window.location.pathname
                    }, function () {
                        scope.$apply(function () {
                            scope.dynamic = value;
                        })
                    }, function () {
                        if (typeof scope.onError === 'function') {
                            scope.$apply(function () {
                                scope.onError();
                            })
                        }
                    }, false, false);
                })
            } else {
                $(element).blur(function () {
                    /* directive id is array */
                    var arr = scope.dynamicId.split('.');
                    var id = angular.copy(scope.dynamicId);
                    var value = angular.copy(scope.dynamic);

                    if (arr.length >= 1) {
                        id = arr[0];
                        for (var i = arr.length; i > 0; i--) {
                            var tmp = {};
                            tmp[arr[i - 1]] = value;
                            value = tmp;
                        }
                        value = value[id];
                    }

                    if (scope.value_old !== scope.dynamic) {
                        process(scope.url, {
                            async: true,
                            id: id,
                            value: value,
                            pathname: window.location.pathname
                        }, function () {
                            scope.$apply(function () {
                                if (typeof scope.onSuccess === 'function') {
                                    scope.onSuccess();
                                }
                            })
                        }, function () {
                            if (typeof scope.onError === 'function') {
                                scope.$apply(function () {
                                    scope.onError();
                                })
                            }
                        }, false, false);
                    }
                }).keyup(function () {
                    scope.$apply(function () {
                        scope.dynamic = $(element).val();
                    })
                })
            }
            /*Set lại thuộc tính*/
            scope.$watch('dynamic', function (value) {
                if (attrs.type === 'checkbox') {
                    if (scope.ngTrueValue + '' === value + '') {
                        $(element).prop('checked', true);
                    } else {
                        $(element).prop('checked', false);
                    }
                    scope.onSuccess();
                } else if (attrs.type === 'radio') {
                    scope.onSuccess();
                } else {
                    $(element).val(value);
                }
            })
        }
    };
})
    .directive('comboBoxFood', function ($timeout, $compile) {
        return {
            restrict: 'A',
            scope: {
                trigger: '=comboBoxFood',
                boxWidth: '=boxWidth',
                boxHeight: '=boxHeight',
                url: '@url',
                data: '=data',
                delay: '@delay',
                mode: '@mode',
                paramKey: '@paramKey',
                valueField: '@valueField',
                textField: '@textField',
                paramValue: '=paramValue',
                clearForNull: '=clearForNull',
                reloadOnSelected: '=reloadOnSelected',
                clearOnSelected: '=clearOnSelected',
                onSelect: '&onSelect',
                onLoadSuccess: '&onLoadSuccess'
            }, controller: function ($scope, $element, $attrs, $transclude) {

            },
            link: function (scope, element, attrs, ctrls) {
                var created = false;
                var is_selected = false;
                var queryParams = {};
                scope.valueField || (scope.valueField = 'id');
                scope.textField || (scope.textField = 'text');
                scope.delay || (scope.delay = 700);
                scope.mode || (scope.mode = 'remote');
                if (scope.paramKey) {
                    queryParams[scope.paramKey] = scope.paramValue;
                }
                var option = {
                    valueField: scope.valueField,
                    textField: scope.textField,
                    /*panelHeight:'auto',*/
                    mode: scope.mode,
                    delay: scope.delay,
                    onSelect: function (row, el, test) {
                        is_selected = true;
                    }, onLoadSuccess: function (data, el) {
                        is_selected = false;
                        $(element).combobox('panel').children().click(function (e) {
                            element.onSelected();
                        })
                        $(element).next().children('input').keyup(function (e) {
                            if (e.which == 13) {
                                var id = $(element).combobox('getValue');
                                element.onSelected(id);
                            }
                        });
                        var kt = false;
                        for (var i in data) {
                            var kesearch = removeUnicode($(element).next().find('input').val());
                            if (scope.trigger != undefined && typeof scope.trigger == 'object') {
                                if (data[i][scope.valueField] == scope.trigger[scope.valueField] || removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {
                                    kt = true;
                                    break;
                                }
                            } else if (typeof data[i][scope.textField] == 'string') {
                                if (removeUnicode(data[i][scope.textField]).indexOf(kesearch)) {
                                    kt = true;
                                    break;
                                }
                            }
                        }
                        if (!kt && scope.clearForNull) {
                            $(element).combobox('clear');
                        }
                        scope.data = data;
                        scope.onLoadSuccess();
                        created = true;
                    },
                    queryParams: queryParams,
                    width: 170,
                    height: 30,
                    formatter: function (row) {
                        var html = `<span class="glyphicon glyphicon` + (row.is_static == 1 ? '-ok color-green' : '-grain') + `"></span> `;
                        return ' <div title="' + (row.is_static == 1 ? 'Theo viện dinh dưỡng' : 'Nguồn khác') + '">' + html + ' <span class="item-text">' + row.name + '</span></div>';
                    }
                };
                element.onSelected = function onSelected(id) {
                    setTimeout(function () {
                        id || (id = $(element).combobox('getValue'));
                        var item = scope.data[0];
                        if (scope.trigger) {
                            item = scope.trigger;
                        }
                        angular.forEach(scope.data, function (fd, ind) {
                            if (id + '' == fd[scope.valueField] + '') {
                                item = fd;
                            }
                        })
                        scope.$apply(function () {
                            scope.trigger = item;
                        })
                    }, 400)
                }
                if (scope.trigger) {
                    option.value = scope.trigger[scope.valueField];
                }
                if (scope.boxWidth) {
                    option.width = scope.boxWidth;
                }
                scope.$watch('data', function (value) {
                    value || (value = []);
                    $(element).combobox('loadData', value);
                });
                scope.$watch('trigger', function (value) {
                    if (value) {
                        var data = scope.data;
                        var id = value[scope.valueField];
                        var index = -1;
                        angular.forEach(data, function (item, ind) {
                            if (value[scope.valueField] == item[scope.valueField]) {
                                index = ind;
                            }
                        });
                        if (index >= 0) {
                            $(element).combobox('setValue', value[scope.textField]);
                        }
                        scope.onSelect();
                    }
                    if (scope.clearOnSelected) {
                        $(element).combobox('clear');
                    }
                });
                scope.$watch('paramValue', function (value) {
                    if (scope.mode != 'local') {
                        if ($(element).next('.textbox.combo').length && scope.reloadOnSelected) {
                            var queryParams = $(element).combobox('options').queryParams;
                            if (scope.paramKey) {
                                queryParams[scope.paramKey] = scope.paramValue;
                            }
                            $(element).combobox({queryParams: queryParams});
                        }
                    } else {
                        if (in_array(typeof scope.data, ['object'])) {
                            if (in_array(typeof value, ['object'])) {
                                var data_new = [];
                                angular.forEach(scope.data, function (item, index) {
                                    if (!in_array(item[scope.valueField], value)) {
                                        data_new.push(item);
                                    }
                                });
                                $(element).combobox('loadData', data_new).combobox('clear');
                            }
                        }
                    }
                });
                $.dm_datagrid.combobox(element, scope.url, option);
            }
        };
    })
angular_app.controller('mainContentController', ['$scope', '$routeParams', '$compile', 'MyCache', '$filter', '$cookies', '$templateCache', '$templateRequest', '$sce', '$localStorage', '$location',
    function ($scope, $routeParams, $compile, MyCache, $filter, $cookies, $templateCache, $templateRequest, $sce, $localStorage, $location) {
        /*Khai báo mẫu định mức số phần*/
        $scope.dinhmuc = {
            'ngucoc': {name: 'Ngũ cốc', value: 3},
            'rau': {name: 'Rau', value: 7},
            'traicay': {name: 'Trái cây', value: 2},
            'damdv': {name: 'Đạm ĐV', value: 7}
        };
        $scope.sys = {
            truong: {
                lists: [],
                selected: undefined
            },
            phong: {
                lists: [],
                selected: undefined
            },
            configs: {},
            tempVer: (new Date()).getTime()
        };
        $scope.menu = {
            select: {},
            page: 'index',
            templates: {
                top: {
                    index: 'menu_top_index.htm',
                    children: 'menu_top_children.htm'
                },
                bottom: {
                    index: 'menu_bottom_index.htm',
                    children: 'menu_bottom_children.htm'
                },
                group: $CFG.template.base_url + '/dinhduong/menu_main-index.html'
            },
            data: {
                top: {},
                bottom: {}
            }
        };
        $scope.sys.phong.onSelected = function (old) {
            var id = 0;
            if ($scope.sys.phong.selected) {
                id = $scope.sys.phong.selected.id;
            }
            if (id != old.id) {
                process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
                    async: true,
                    id: id,
                    project_id: 2
                }, function (resp) {
                    $scope.$apply(function () {
                        $scope.sys.truong.lists = resp;
                    });
                }, function () {
                    $('#select-school').combobox('clear');
                }, false);
            }
        };
        $scope.sys.truong.onLoadSuccess = function () {
            if ($scope.sys.truong.old_id && count($scope.sys.truong.lists) > 0) {
                angular.forEach($scope.sys.truong.lists, function (item, ind) {
                    if (item.id == $scope.sys.truong.old_id) {
                        setTimeout(function () {
                            $('#select-school').val(item.text).next().find('input[type="text"]').val(item.text);
                        }, 300)
                        $scope.sys.truong.old_id = undefined;
                    }
                });
            }
        };
        $scope.sys.truong.onSelected = function () {
            var truong = $scope.sys.truong.selected;
            var id = '';
            if (truong) {
                id = truong.id;
            }
            process($CFG.remote.base_url + '/doing/admin/unit/setTruong', {
                async: true,
                id: id,
                project_id: 2
            }, function (resp) {
                statusloading();
                setTimeout(function () {
                    location.reload();
                })
            }, function () {
                $('#select-school').combobox('clear');
            });
        };
        $scope.getParams = function () {
            var urls = $location.path().split('/');
            var index = 2;
            if (urls[2] == 'view') {
                index = 3;
            }
            var params = {};
            params.module = urls[index];
            params.action = urls[index + 1];
            return params;
        };
        $scope.getGroupMenu = function ($define) {
            $rs = {};
            $define || ($define = $scope.getParams().module);
            if ($define) {
                var kt = false;
                $.each($scope.menu.data.top, function (index, menus) {
                    for (var i in menus.children) {
                        var menu = menus.children[i];
                        if ($define == menu.define) {
                            kt = true;
                            $rs = menus;
                            $rs['module_name'] = menu.name;
                            return;
                        }
                    }
                });
                if (!kt) {
                    $.each($scope.menu.data.bottom, function (index, menus) {
                        for (var i in menus.children) {
                            var menu = menus.children[i];
                            if ($define == menu.define) {
                                kt = true;
                                $rs = menus;
                                $rs['module_name'] = menu.name;
                                return;
                            }
                        }
                    });
                }
            }
            return $rs;
        };
        $scope.selected_menu_khauphandinhduong = function () {
            angular.forEach($scope.menu.data.top, function (menu, ind) {
                if (menu.icon == 'khau_phan_dinh_duong') {
                    $scope.menu.selected = menu;
                    setTimeout(function () {
                        $('li#danh_muc').click();
                    })
                }
            })
        };
        $scope.setMonth_selected = function (month) {
            setCookie('month_selected', month);
        };
        $scope.getMonth_selected = function (get_string) {
            var month = getCookie('month_selected');
            month || (month = (new Date().getMonth() + 1));
            $scope.setMonth_selected(month);
            var m = parseInt(month);
            if (get_string) {
                if (m < 10) {
                    month = '0' + m;
                } else {
                    month = '' + m;
                }
            } else {
                month = m;
            }
            return month;
        };
        /* Khởi tạo menu và cấu hình chung cho các báo cáo */
        $scope._initSys = function () {
            var data = window.sysConfigs;
            data || (data = $templateCache.get('sysConfigs'));
            if (data.rows[1]) {
                $scope.menu.data.top = data.rows[1];
                $scope.menu.selected = '';
                $.each($scope.menu.data.top, function (index, menu) {
                    $scope.menu.selected = menu;
                });
            }
            if (data.rows[2]) {
                $scope.menu.data.bottom = data.rows[2];
            }
            $scope.sys.configs = data.configs;
            window.sysConfigs || (window.sysConfigs = data);
            angular.element(document).ready(function () {
                var li0 = $(".li0").width() + 26;
                var li1 = $(".li1").width() + 26;
                var li2 = $(".li2").width() + 26;
                var li3 = $(".li3").width() + 26;
                var li4 = $(".li4").width() + 26;
                var width = (li0 + li1 + li2 + li3 + li4 + 40);
                if ($(window).width() < 1025) {
                    width = width - 76;
                }
                if (width > 50) {
                    width = width + 20;
                    $(".nav-main-content").css("width", width + "px");
                }
                $('#statusloading-mash').hide();
            });
            $templateCache.put('menu', $scope.menu);
        };
        $scope.detailUserForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Thông tin chi tiết tài khoản',
                size: size.normal,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/tmp/admin/user', 'popup_detail.html', {}, function (resp) {
                        $scope.$apply(function () {
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.updateSelfUser = function () {
            var data = {
                async: true,
                email: $CFG.user.email,
                phone: $CFG.user.phone,
                name: $CFG.user.name
            };
            process($CFG.remote.base_url + '/doing/admin/user/updateSelfUser', data, function (resp) {

            }, function () {

            });
        };
        $scope.onchangeSchool_point = function (point, callback) {
            var data = {
                async: true,
                point: point
            };
            process($CFG.remote.base_url + '/doing/admin/user/changePoint', data, function (resp) {
                if (typeof callback === 'function' && resp.result === 'success') {
                    console.log('aaaaaaaaaaaaa', resp)
                    callback(resp);
                }
            }, function () {
            });
        };
        $scope.detailUnitForm = function () {
            $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Cập nhật thông tin đơn vị',
                size: size.small,
                fullScreen: false,
                showButton: false,
                draggable: true,
                content: function (element) {
                    loadForm($CFG.remote.base_url + '/templates/admin/unit/popup_detail.html', '', {}, function (resp) {
                        $scope.$apply(function () {
                            $scope.$CFG.self_id_old = $scope.$CFG.self_id;
                            $(element).html($scope.compile(resp, $scope));
                        });
                    });
                }
            });
        };
        $scope.updateUnitInfo = function () {
            if ($scope.$CFG.self_id_old != $scope.$CFG.self_id) {
                var data = {
                    id: $scope.$CFG.self_id,
                    async: true
                };
                process($CFG.remote.base_url + '/doing/admin/unit/updateInfo', data, function (resp) {
                    if (resp.result == 'success') {
                        dialogCloseAll();
                    }
                }, function () {

                });
            }
        };
        $scope._initSys();
        /*
        * Load template for cached
        */
        $scope.loadTemplates = function (urls, scope) {
            if (typeof urls === 'string') {
                urls = [urls];
            }
            for (var i in urls) {
                $scope.getTemplate(urls[i]);
            }
            /* Bind template loaded to scope */
            $scope.templateCache = $templateCache;
        };
        $scope.parseUrlTemp = parseUrlTemp;
        $scope.getTemplate = getTemplate;

        function parseUrlTemp(url) {
            if (url.split('//').length === 1) {
                if (url[0] !== '/') {
                    url = '/' + url;
                }
                url = $CFG.template.base_url + url;
            }
            return url;
        }

        function getTemplate(url, callback, reload) {
            var fullUrl = $scope.parseUrlTemp(url);
            if (reload) {
                $templateCache.remove(fullUrl);
                $scope.sys.tempVer++;
            }
            var template = $templateCache.get(fullUrl);
            if (template) {
                if (typeof callback === 'function') {
                    callback(template);
                }
            } else {
                $templateRequest(fullUrl + '?_=' + $scope.sys.tempVer, false).then(function (template) {
                    $templateCache.put(fullUrl, template);
                    if (typeof callback === 'function') {
                        callback(template);
                    }
                }, function () {
                    if (typeof callback === 'function') {
                        callback('Lỗi khi tải.');
                    }
                });
            }
            return template;
        };
        $scope.storage = $localStorage;
    }])
    .run(function ($templateCache) {
        var overload = 0;

        function _initSys() {
            if (overload > 5) {
                return;
            }
            process($CFG.remote.base_url + '/doing/dinhduong/index/sysConfigs', {}, function (resp) {
                $templateCache.put('sysConfigs', resp);
            }, function () {
                overload++;
                _initSys();
            }, false, false, false);
        }

        if (!window.sysConfigs) {
            _initSys();
        }
    });
$.balance_money = {
    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */
    	scope.balance_money = {
            fields:[
                { field: 'name', title: 'Tên thực phẩm', width: 200 },
                { field: 'luong1tre_tmp', title: 'Lượng (g)'},
                { field: 'price_kg', type:'number', title: 'Đơn giá (đ/kg)'},
                { field: 'price', title: 'Đơn giá theo ĐVT', type:'number'}
            ], getValue: function(field, row){
                return row[field.field];
            }, formatter: function(field, value, row){
                return value;
            }, data: scope.datagrid.data,
            getStyles: function(field) {
                var styles = {};
                var width = field.width;
                if(width != undefined){
                    width = width + '';
                    if(width.split('%') == 1){
                        width = width + 'px';
                    }
                    styles.width = width;
                }
                return styles;
            }
        };
        scope.balance_moneyShow = function () {
            dialogClose();
            $.dm_datagrid.showAddForm({
                title: 'Cân đối tiền',
                size: size.wide,
                fullScreen: true,
                scope: scope,
                showButton: false,
                content: $CFG.template.base_url + '/dinhduong/balance/balance_money.html',
                onShown: function () {
                    scope.balance_money.init();
                }, reload: function () {
                    scope.balance_money.init();
                }
            });
        };
        scope.balance_money.selectallfood = false;
        scope.balance.money_min = 5;
        scope.balance.money_minChange = function(){
            if(scope.balance.money_min<1){
                scope.balance.money_min = 1;
            }
        };
        scope.balance_money.apply = function(){
            scope.balance.data = scope.balance_money.data;
            scope.balance.meals = scope.balance_money.meals;
            scope.balance.apply();
            dialogClose();
        };
        scope.balance_money.toZero = function(step) {
            
        };
        scope.balance_money.init = function() {
            scope.balance_money.data = clone(scope.balance.data);
            scope.balance_money.meals = clone(scope.balance.meals);
            scope.balance_money.foods = [];
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id){
                    if (scope.balance.data[meal_key][food_id].exports) {
                        food.exports = clone(scope.balance.data[meal_key][food_id].exports);
                    }
                    food.tiendieuchinh = 0;
                    food.thucmuatheodvt_old = scope.balance.data[meal_key][food_id].thucmuatheodvt;
                    food.money_selected = false;
                    food.foods = [];
                });
            });
            angular.forEach(scope.balance_money.meals, function (meal, meal_define) {
                var meal_key = scope.meal_defines[meal_define];
                angular.forEach(meal.dishes, function (dish, dish_id) {
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        if (!scope.balance_money.data[meal_key][food_id]) {
                            scope.balance_money.data[meal_key][food_id] = clone(food);
                            scope.balance_money.data[meal_key][food_id].foods = [];
                            scope.balance_money.data[meal_key][food_id].luong1tre = 0;
                            scope.balance_money.data[meal_key][food_id].thucmuatheodvt = 0;
                            scope.balance_money.data[meal_key][food_id].thucmua1nhom = 0;
                        }
                        scope.balance_money.data[meal_key][food_id].luong1tre = $['+'](scope.balance_money.data[meal_key][food_id].luong1tre, food.quantity_edit);
                        scope.balance_money.data[meal_key][food_id].foods.push(food);
                    });
                });
            });
            scope.balance_money.selectallfood = false;
            scope.balance_money.totalCalculator();
        };
        /*  Tính lượng khẩu phần (calo) đạt hay chưa */
        scope.balance_money.caloRate = function(){
            var value = 0;
            var calo = round(scope.sumCaloMeals(scope.balance_money.meals), 0);
            var rate = scope.getNormSelected();
            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                value = 1;
            } else {
                if (calo < rate.smallest_rate) {
                    value = 0;
                } else {
                    value = 2;
                }
            }

            return value;
        };
        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */
        scope.balance_money.plgRate = function(){
            var rs = {};
            var tile = scope.getTile_PLG(scope.balance_money.meals);
            var tile_dat = [];
            var tile_chuadat = [];
            var tile_vuotqua = [];
            if(tile.protein >= scope.selected.group.protein_min && tile.protein <= scope.selected.group.protein_max){
                tile_dat.push({define:'protein',name: 'Chất đạm'});
            }else if(tile.protein<scope.selected.group.protein_min) {
                tile_chuadat.push({define:'protein',name: 'Chất đạm'})
            }else{
                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})
            }
            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){
                tile_dat.push({define:'fat',name: 'Chất béo'});
            }else if(tile.fat<scope.selected.group.fat_min) {
                tile_chuadat.push({define:'fat',name: 'Chất béo'})
            }else{
                tile_vuotqua.push({define:'fat',name: 'Chất béo'})
            }
            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){
                tile_dat.push({define:'sugar',name: 'Chất bột'});
            }else if(tile.sugar<scope.selected.group.suga_min) {
                tile_chuadat.push({define:'sugar',name: 'Chất bột'})
            }else{
                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})
            }
            rs = {
                dat: tile_dat,
                chuadat: tile_chuadat,
                vuotqua: tile_vuotqua
            };
            return rs;
        };
        scope.balance_money.plgRateBind = function(){
            var thanhphan = scope.balance_money.plgRate();
            var text = '';
            if(count(thanhphan.dat) == 3){
                text = 'Cân đối';
                class_color = '';
            }else{
                text = 'Chưa cân đối';
                class_color = 'color-red';
            }
            return {text: text, 'class': class_color, thanhphan: thanhphan};
        };
        scope.balance_money.caloRateBind = function(){
            var value = scope.balance_money.caloRate();
            if(value == 1){
                text = 'Đạt';
                class_color = '';
            }else if(value == 0){
                text = 'Chưa đạt';
                class_color = 'color-red';
            }else{
                text = 'Vượt quá định mức';
                class_color = 'btn-color-blue';
            }
            return {text: text, 'class': class_color, value: value};
        };
        scope.balance_money.foodSelectAll = function() {
            scope.balance_money.selectallfood = !scope.balance_money.selectallfood;
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    food.money_selected = scope.balance_money.selectallfood;
                })
            });
            scope.balance_money.foodSelectedChange();
        };
        scope.balance_money.foodClickSelected = function(meal_key_select, food){
            var foods = [];
            food.money_selected = !food.money_selected;
            for(var meal_key in scope.balance_money.data){
                fs = scope.balance_money.data[meal_key];
                for(var food_id in fs){
                    var food = fs[food_id];
                    food.tiendieuchinh = 0;
                    food.thucmuatheodvt = scope.balance.data[meal_key][food_id].thucmuatheodvt;
                    food.thanhtien1nhom = scope.datagrid.data[meal_key][food_id].thanhtien1nhom;
                    if(scope.row.meal_selection[meal_key].selected && food.money_selected){
                        foods.push(food);
                    }
                }
            }
            console.log(food.name, foods);
            scope.balance_money.foods = foods;
            scope.balance_money.run(foods);
            scope.balance_money.applyQuantity(foods);
            scope.balance_money.totalCalculator();
        };
        scope.balance_money.isApply = function(){
            var rs = false;
            angular.forEach(scope.balance_money.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    if(food.money_selected && Number(food.tiendieuchinh)!=0){
                        rs = true;
                    }
                });
            });
            return rs;
        };
        scope.balance_money.isNegative = function(){
            var rs = true;
            scope.balance_money.foods || (scope.balance_money.foods = []);
            for (var food of scope.balance_money.foods) {
                if(Number(food.thanhtien1nhom) < 0){
                    rs = false;
                    break;
                }
            }
            return rs;
        };
        scope.balance_money.onChange_tiendieuchinh = function(fd){
            var fds = [];
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    if(food.money_selected && food.food_id != fd.food_id){
                        food.tiendieuchinh = 0;
                        fds.push(food);
                    }
                });
            });
            money = $['-'](scope.balance_money.tienchenhlech1tre , fd.tiendieuchinh);
            scope.balance_money.run(fds, money);
            scope.balance_money.applyQuantity();
            scope.balance_money.totalCalculator();
        };
        scope.balance_money.autoSelectForm = function() {
            $.dm_datagrid.showAddForm({
                title: 'Chọn thưc phẩm muốn cố định',
                size: size.wide,
                fullScreen: true,
                scope: scope,
                showButton: false,
                content: $CFG.template.base_url + '/dinhduong/balance/balance_money_option.html'
            });
        };
        scope.balance_money.autoSelectStart = function() {

        };
        scope.balance_money.applyQuantity = function() {
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if (food.price && food.money_selected) {
                        food.thanhtien1nhom = $['+'](scope.datagrid.data[meal_key][food_id].thanhtien1nhom, food.tiendieuchinh);
                        food.thucmuatheodvt = $['/'](food.thanhtien1nhom, food.price);
                        scope.onChange_thucmuatheodvt(food, true);
                    }
                });
            });
        };
        scope.balance_money.totalCalculator = function(){
            var thanhtien1nhom = 0;
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if(food.exports) {
                        if (count(food.exports) == 1) {
                            thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);
                        } else {
                            angular.forEach(food.exports, function(fd, food_id_price){
                                thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));
                            });
                        }
                    }else{
                        thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);
                    }
                });
            });
            var service = scope.getTiendichvu();
            var tongtien_dichvu = $['*'](service, scope.row.sotre);
            var tien_bo_tro = scope.row.tien_bo_tro || 0;
            var surplus_end = 0;
            if (scope.surplus) {
                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {
                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];
                }
            }
            scope.balance_money.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);
            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, tien_bo_tro);
            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, surplus_end);
            console.log('thanhtien1nhom', thanhtien1nhom);
        };
        scope.balance_money.run = function(foods) {
            var money = scope.row.tongtienchenhlech;
            if(foods.length > 0){
                if(scope.balance_money.money_min<=0){
                    scope.balance_money.money_min = 1;
                }
                var tong_tien = 0;
                angular.forEach(foods, function(food, index){
                    tong_tien = $['+'](tong_tien , food.thanhtien1nhom);
                });
                var tong_tien_chia = 0;
                var food_max = {tiendieuchinh:0};
                var da_tru = 0;
                var food_max = {tiendieuchinh:0};
                angular.forEach(foods, function(food, index){
                    var tile = food.thanhtien1nhom/tong_tien;
                    var ext = $['*'](tile , money);
                    var ext_round = scope.round(ext,0);
                    food.tiendieuchinh = ext_round;
                    if (food.tiendieuchinh )
                    da_tru = $['+'](da_tru, ext_round);
                    if(food_max.tiendieuchinh <= food.tiendieuchinh){
                        food_max = food;
                    }
                });
                if(money != da_tru){
                    var con_lai = $['-'](money, da_tru);
                    food_max.tiendieuchinh = $['+'](food_max.tiendieuchinh, con_lai);
                }
            }
        };
    }
};
$.balance = {
    project: 'dinhduong',
    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */
        scope.balance = {};
        scope.balance.sophan = {
            ngucoc: {name:'Ngũ cốc', foods:{}},
            traicay: {name:'Trái cây', foods:{}},
            rau: {name:'Rau', foods:{}},
            damdv: {name:'Ngũ cốc', foods:{}},
            khac: {name:'Khác', foods:{}}
        };
        scope.balance.templates = {
            show: this.project + '/balance/balance.html',
            change_money: this.project + '/balance/change_money_of_child.html',
            keepBoundMeal: this.project + '/balance/keep_bound_meal.html'
        };
        // scope.loadTemplates(scope.templates, scope);
        scope.balance.selectallfood = false;
        scope.balance.checkautofortype = false;
        scope.balance.keeping_recipes = true;
        scope.balance.for_money = true;
        scope.balance.for_quantity = true;
        scope.balance.for_quanlity = true;
        scope.balance.init = function() {
            scope.balance.data = clone(scope.datagrid.data);
            scope.balance.meals = clone(scope.selected.meals);
            scope.balance.build_data();
            scope.balance.totalCalculator();
        };
        scope.balance.build_data = function() {
            var data = {};
            angular.forEach(scope.balance.data, function (foods, meal_key) {
                data[meal_key] = {};
                angular.forEach(foods, function (food, food_id) {
                    food.foods = [];
                    data[meal_key][food_id] = food;
                });
            });
            angular.forEach(scope.balance.meals, function (meal, meal_define) {
                var meal_key = scope.meal_defines[meal_define];
                angular.forEach(meal.dishes, function (dish, dish_id) {
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        data[meal_key] || (data[meal_key] = clone(food));
                        if (data[meal_key] == undefined) {
                            delete dish.ingredient[food_id];
                        } else if (data[meal_key][food_id] == undefined) {
                            delete dish.ingredient[food_id];
                        }else{
                            data[meal_key][food_id].foods.push(food);
                        }
                    });
                });
            });
            // angular.forEach(data, function (foods, wh_id) {
            //     angular.forEach(foods, function (food, food_id) {
            //         scope.onChange_luong1tre(food, true);
            //     });
            // });
            scope.balance.data = data;
        };
        scope.balance.getFoodSelected = function(){
            var rs = {};
            scope.balance.nutritions || (scope.balance.nutritions = {});
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food,food_id) {
                    if(!scope.balance.nutritions[food_id]) {
                        scope.balance.nutritions[food_id] = food.nutritions;
                    }else{
                        food.nutritions = scope.balance.nutritions[food_id];
                    }
                    if(food.selected){
                        rs[meal_key] || (rs[meal_key] = {});
                        rs[meal_key][food_id] || (rs[meal_key][food_id] = 0);
                        rs[meal_key][food_id] += food.luong1tre;
                    }
                });
            });
            return rs;
        };
        scope.balance.getData = function(meals, is_meals) {
            /*Đoạn này viết để lấy cấu trúc thực đơn*/
            scope.balance.nutritions = {};
            var keep_foods = scope.balance.getFoodSelected(); /* Danh sách thực phẩm cố định lượng*/
            var nutritions = scope.balance.nutritions;
            var tmps = {};
            var tmp_foods = {};
            var total_calo = 0;
            meals || (meals = scope.selected.meals);
            angular.forEach(meals, function(meal, meal_define) {
                tmps[meal_define] = [];
                if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;
                tmp_foods[meal.warehouse_id] || (tmp_foods[meal.warehouse_id]= {});
                var meal_key = 2;
                if (meal_define == 'buasang') {
                    meal_key = 1;
                } else if (meal_define == 'buatoi') {
                    meal_key = 3;
                }
                angular.forEach(meal.dishes, function(dish, dish_id) {
                    var tmp_meals = [];
                    angular.forEach(dish.ingredient, function(food, food_id) {
                        if(!nutritions[food_id]) {
                            nutritions[food_id] = food.nutritions;
                        }else{
                            food.nutritions = nutritions[food_id];
                        }
                        food.gam_exchange || (food.gam_exchange = 1000);
                        var f = {name: food_id};
                        if(food.extrude_factor){
                            food.extrude_factor = Number(food.extrude_factor);
                            if(food.extrude_factor >= 100) {
                                food.extrude_factor = 50;
                            }
                        }
                        f.quantity = Number(food.quantity_edit);
                        if(f.quantity < 0) {
                            f.quantity = 0;
                        }
                        if(food.price_kg) {
                            f.price = food.price_kg;
                        }else{
                            f.price = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                        }
                        if(!f.price){
                            f.price = 0;
                        }
                        f.protein = $['*'](food.nutritions.protein * 10 , 4.1);
                        f.fat = $['*'](food.nutritions.fat * 10 , 9.3);
                        f.sugar = $['*'](food.nutritions.sugar * 10 , 4.1); 
                        f.PLG = $['+']($['+'](f.protein , f.fat) , f.sugar);    /*(Kcal)*/
                        total_calo += (
                                f.quantity * food.nutritions.protein / 100 * 4.1 +
                                f.quantity * food.nutritions.fat / 100 * 9.3 +
                                f.quantity * food.nutritions.sugar / 100 * 4.1 
                            );
                        if(food.extrude_factor > 0){
                            f.price = (100*f.price)/(100 - food.extrude_factor);
                            var gam = (100*100)/(100 - food.extrude_factor);
                        }
                        if(in_array_key(food_id, keep_foods[meal_key])) {
                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){
                                if(food_id+'' == f_id+''){
                                    f['i_' + meal_key + '_' + f_id] = 1.0;
                                }else{
                                    f['i_' + meal_key + '_' + f_id] = 0.0;
                                }
                            });
                        }else{
                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){
                                f['i_' + meal_key + '_' + f_id] = 0.0;
                            });
                        }
                        tmp_meals.push(f);
                        // if(!tmp_foods[meal_key][food_id]) {
                        //     tmp_foods[meal_key][food_id] = {
                        //         id: food_id,
                        //         name: food.name,
                        //         extrude_factor: food.extrude_factor,
                        //         gam_exchange: food.gam_exchange,
                        //         quantity: 0,
                        //         price: food.price,
                        //         quantity_real: 0
                        //     }
                        // }
                        // tmp_foods[meal_key][food_id].quantity = $['+'](tmp_foods[meal_key][food_id].quantity,food.quantity_edit);
                        // tmp_foods[meal_key][food_id].quantity_real = $['+'](tmp_foods[meal_key][food_id].quantity_real,f.quantity);
                    });
                    dish.lower_bound = Number(dish.lower_bound);
                    dish.upper_bound = Number(dish.upper_bound);
                    if(!dish.lower_bound){
                        dish.lower_bound = 5
                    }
                    if(!dish.upper_bound){
                        dish.upper_bound = 500
                    }
                    if(dish.upper_bound < dish.lower_bound){
                        dish.upper_bound = dish.lower_bound;
                    }
                    var tmp_disk = {
                        dish: dish_id,
                        lower_bound: dish.lower_bound/1000,
                        upper_bound: dish.upper_bound/1000,
                        ingredients: tmp_meals
                    };
                    if(count(tmp_meals)==0) {
                        tmp_disk.lower_bound = 0;
                    }
                    tmps[meal_define].push(tmp_disk);
                });
            });
            // console.log(tmps); return;
            var group_id = scope.row.group_id;
            var group_selected = scope.getGroupSelected();
            var nutritions = scope.selected.group.nutritions;
            var protein = (nutritions.animal_protein + nutritions.vegetable_protein) * 4.1;
            var fat = (nutritions.animal_fat + nutritions.vegetable_fat) * 9.3;
            var sugar = nutritions.sugar * 4.1;
            var calo = protein+fat+sugar;
            var asym = 0.5;
            /*Kiểm tra và tính thêm tỉ lệ bữa sáng nếu có*/
            var tmp_meal = {
                buasang_min: $['/']($['*'](group_selected.meals.buasang.min + asym, nutritions.calo) , 100),
                buasang_max: $['/']($['*'](group_selected.meals.buasang.max - asym , nutritions.calo) , 100),
                buatrua_min: $['/']($['*'](group_selected.meals.buatrua.min + asym , nutritions.calo) , 100),
                buatrua_max: $['/']($['*'](group_selected.meals.buatrua.max - asym , nutritions.calo) , 100),
                buaxe_min: $['/']($['*'](group_selected.meals.buaxe.min + asym , nutritions.calo) , 100),
                buaxe_max: $['/']($['*'](group_selected.meals.buaxe.max - asym , nutritions.calo) , 100),
                buaphu_min: $['/']($['*'](group_selected.meals.buaphu.min + asym , nutritions.calo) , 100),
                buaphu_max: $['/']($['*'](group_selected.meals.buaphu.max - asym , nutritions.calo) , 100)
            };
            asym = 0.5;
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = 0;
            var biggest_rate = 0;
            if(scope.dinhduong_calc[1]){
                smallest_rate += buasang_norm.smallest_rate;
                biggest_rate += buasang_norm.biggest_rate;
            }
            if(scope.dinhduong_calc[2]){
                smallest_rate += scope.selected.group.smallest_rate;
                biggest_rate += scope.selected.group.biggest_rate;
            }
            var tmp_norm = {
                protein_min: $['/']($['*'](protein , smallest_rate + asym) , 100),
                fat_min: $['/']($['*'](fat , smallest_rate + asym) , 100),
                sugar_min: $['/']($['*'](sugar , smallest_rate + asym) , 100),
                protein_max: $['/']($['*'](protein , biggest_rate - asym) , 100),
                fat_max: $['/']($['*'](fat , biggest_rate - asym) , 100),
                sugar_max: $['/']($['*'](sugar , biggest_rate - asym) , 100),
                calo_min: 0,
                calo_max: 0
            };
            if(scope.dinhduong_calc[1]){
                tmp_norm.calo_min += nutritions.calo_rate_morning_low;
                tmp_norm.calo_max += nutritions.calo_rate_morning;
            }
            if(scope.dinhduong_calc[2]){
                tmp_norm.calo_min += nutritions.calo_rate_low;
                tmp_norm.calo_max += nutritions.calo_rate;
            }

            var tmp_quantity = {
                protein_min: (scope.selected.group.protein_min + asym)/100,
                fat_min: (scope.selected.group.fat_min + asym)/100,
                sugar_min: (scope.selected.group.sugar_min + asym)/100,
                protein_max: (scope.selected.group.protein_max - asym)/100,
                fat_max: (scope.selected.group.fat_max - asym)/100,
                sugar_max: (scope.selected.group.sugar_max - asym)/100
            };
            var service_price = scope.getTiendichvu();
            var data = {
                keeping_recipes: (scope.balance.keeping_recipes?1:0),
                menu_price: scope.row.tien1tre - service_price,
                input_menu: tmps,
                constraints: []
            };
            if(scope.balance.for_money) {}
            if(scope.balance.for_quantity) {
                data.constraints.push({"name": "PLG", "lower_bound": tmp_norm.calo_min+1, "upper_bound": tmp_norm.calo_max-1, "type": "nominal", denominator: "","meal":"all"});
            }
            if(scope.balance.for_quanlity) {
                data.constraints.push({name: 'protein', lower_bound: tmp_quantity.protein_min, upper_bound: tmp_quantity.protein_max,type:'percentage',denominator: "PLG",meal:"all"});
                data.constraints.push({name: 'fat', lower_bound: tmp_quantity.fat_min, upper_bound: tmp_quantity.fat_max,type:'percentage',denominator: "PLG",meal:"all"});
                data.constraints.push({name: 'sugar', lower_bound: tmp_quantity.sugar_min, upper_bound: tmp_quantity.sugar_max,type:'percentage',denominator: "PLG",meal:"all"});
            }

            if(is_meals){
                angular.forEach(tmps, function(meal, meal_define){
                    var kt = false;
                    for(var i in tmps[meal_define]) {
                        if(count(tmps[meal_define][i].ingredients)>0){
                            kt = true;
                            break;
                        }
                    }
                    if(kt){
                        data.constraints.push({
                            name: 'PLG', 
                            lower_bound: tmp_meal[meal_define+'_min'], 
                            upper_bound: tmp_meal[meal_define+'_max'], 
                            type:'nominal', 
                            denominator: "", 
                            meal: meal_define 
                        });
                    }
                });
            }
            /*  Thêm điều kiện cố định thực phẩm    */
            if(count(keep_foods)>0) {
                angular.forEach(keep_foods, function(foods, meal_key){
                    angular.forEach(foods, function(quantity, food_id){
                        data.constraints.push({
                            name: 'i_' + meal_key + '_' + food_id,
                            lower_bound: quantity/1000,
                            upper_bound: quantity/1000,
                            type: "nominal",
                            denominator: "",
                            meal: "all"
                        });
                    });
                });
            }
            return data;
        };
        scope.balance.alertData = function(meals){
            var data = scope.balance.getData(meals,true);
            var rand = (Math.random()+'').split('.')[1];
            scope.balance.data_tmp = data;
            var html = '<div><div>{{balance.data_tmp}}</div></div>';
            $.dm_datagrid.show({
                title: 'Menu input for processing',
                message: '<div id="' + rand + '" style="padding: 15px; min-height: 350px; overflow-x: auto;"></div>'
            }, function() {
                setTimeout(function() {
                    scope.$apply( function() {
                        $('#'+rand).html(scope.compile(html));
                    });
                });
            });
        };
        scope.balance.alertEnd = function(resp, msgs) {
            msgs || (msgs = []);
            var status = resp.status;
            switch (status){
                case 0:
                    break;
                case 1:
                    break;
                case 2:
                    msgs.push('Tiền ăn quá thấp. Đã giảm lượng thực phẩm tối đa để tiền thiếu ít nhất');
                    break;
                case 3:
                    msgs.push('Tiền ăn quá cao. Đã tăng lượng thực phẩm tối đa để tiền thừa ít nhất.');
                    break;
                case 4:
                    msgs.push('Công thức (tỉ lệ thực phẩm) bị thay đổi, dẫn đến món ăn có thể không nấu được.');
                    break;
                default:
                    msgs.push('Hệ thống không thể cân đối.');
                    msgs.push('Hãy kiểm tra và chọn lại thực đơn đầu vào.');
                    msgs.push('Hoặc lựa chọn tiện ích khác nếu có.');
                    break;
            }
            /*Kiểm tra có thực phẩm nào giá bằng 0 để đưa thông báo*/
            var tmp_foods = {};
            angular.forEach(scope.datagrid.data, function(foods, warehouse_id){
                angular.forEach(foods,function(food,food_id){
                    if(food.price == 0){
                        tmp_foods[food_id] = food;
                    }
                })
            });
            var foods = [];
            angular.forEach(tmp_foods, function(food,food_id){
                foods.push(food.name);
            });
            if(foods.length) {
                msgs.push('Một số thực phẩm chưa có đơn giá hãy kiểm tra lại để đảm bảo việc cân đối được chính xác : '+foods.join(', '));
            }
            if(count(msgs)>0) {
                $.dm_datagrid.show({
                    size: size.small,
                    title: 'Thông báo',
                    message: '<div style="padding:15px;min-height:250px;overflow-x: auto"> - '+msgs.join('<br/> - ')+'</div>'
                });
            }
        };
        scope.balance.processAction = function(meals) {
            meals || (meals = scope.balance.meals);
            var tien1tre;
            var canh_bao = (scope.row.meal_selection[1].selected != scope.row.meal_selection[2].selected);
            if (canh_bao) {
                angular.forEach(scope.row.meal_selection, function (meal, meal_key) {
                    if (!meal.selected && meal.visible) {
                        if (count(scope.balance.data[meal_key]) == 0) {
                            canh_bao = false;
                        }
                    }
                });
            }
            if (canh_bao) {
                scope.balance.meals = meals;
                $.dm_datagrid.showAddForm({
                    module: $CFG.project + '/' + self.module,
                    title: 'Tiền ăn 1 trẻ',
                    draggable: true,
                    fullScreen: false,
                    showButton: false,
                    scope: scope,
                    content: scope.balance.templates.change_money,
                });
            }else{
                scope.balance.send(meals);
            }
        };
        scope.balance.send = function(meals, tien1tre) {
            meals || (meals = scope.balance.meals);
            var data = scope.balance.getData(meals,true);
            data.input_menu = JSON.stringify(data.input_menu);
            data.constraints = JSON.stringify(data.constraints);
            data.async = true;
            if (tien1tre) {
                data.menu_price = tien1tre;
            }
            var url = $CFG.balance_api;
            process(url, data, function(resp) {
                if(in_array(resp.status, [1])) {
                    scope.balance.process(resp);
                    var msgs = [];
                    msgs.push('Thực đơn đã được cân đối.');
                    scope.balance.alertEnd(resp, msgs);
                } else {
                    data = scope.balance.getData(meals);
                    data.input_menu = JSON.stringify(data.input_menu);
                    data.constraints = JSON.stringify(data.constraints);
                    if (tien1tre) {
                        data.menu_price = tien1tre;
                    }
                    data.async = true;
                    process(url, data, function(resp) {
                        var msgs = [];
                        if(in_array(resp.status, [1,2,3])){
                            if(in_array(resp.status, [1,2,3])){
                                msgs.push('Thực đơn đã được cân đối..');
                            }
                            scope.balance.process(resp);
                            console.log('calo theo bữa: not ok.');
                        }else{
                            console.log('thực đơn không cân đối được.');
                        }
                        scope.balance.alertEnd(resp, msgs);
                    },null,false);
                }
            }, null, false);
        };
        scope.balance.process = function(resp) {
            setTimeout(function () {
                scope.$apply(function () {
                    var keep_foods = scope.balance.getFoodSelected();
                    var tmp_fds = {};
                    var meals = resp.output_menu;
                    var status = resp.status;
                    var root_meals = scope.balance.meals;
                    angular.forEach(meals, function(meal, meal_define) {
                        if (count(meal) > 0 ) {
                            if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;
                            var meal_key = 2;
                            if (meal_define == 'buasang') {
                                meal_key = 1;
                            } else if (meal_define == 'buatoi') {
                                meal_key = 3;
                            }
                            angular.forEach(meal, function(dish, ind) {
                                angular.forEach(dish.ingredients, function (food, ind1) {
                                    if (keep_foods[meal_key]) {
                                        if (keep_foods[meal_key][food.food_id]) {
                                            return;
                                        }
                                    }
                                    root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity = food.quantity * 1000;
                                    root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity_edit = food.quantity * 1000;
                                });
                            });
                        }
                    });
                    scope.balance.build_data();
                    angular.forEach(scope.balance.data, function(foods, meal_key) {
                        angular.forEach(foods, function (food, food_id) {
                            food.foods = [];
                            if (keep_foods[meal_key]) {
                                if (keep_foods[meal_key][food.food_id]) {
                                    return;
                                }
                            }
                            food.luong1tre = 0;
                        });
                    });
                    angular.forEach(root_meals, function (meal, meal_define) {
                        var meal_key = 2;
                        if (meal_define == 'buasang') {
                            meal_key = 1;
                        } else if (meal_define == 'buatoi') {
                            meal_key = 3;
                        }
                        angular.forEach(meal.dishes, function(dish, ind) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                scope.balance.data[scope.meal_defines[meal.define]][food_id].foods.push(food);
                                if (keep_foods[meal_key]) {
                                    if (keep_foods[meal_key][food.food_id]) {
                                        return;
                                    }
                                }
                                scope.balance.data[scope.meal_defines[meal.define]][food_id].luong1tre += food.quantity_edit;
                            });
                        });
                    });
                    angular.forEach(scope.balance.data, function(foods, meal_key) {
                        angular.forEach(foods, function (food, food_id) {
                            if (keep_foods[meal_key]) {
                                if (keep_foods[meal_key][food.food_id]) {
                                    return;
                                }
                            }
                            scope.onChange_luong1tre(food, true);
                        });
                    });
                    scope.balance.totalCalculator();
                });
            });
        };
        scope.balance.roundThucmuatheo_dvt = function(food,status){
            var num = 0;
            var price = food.price+'';
            if(status==1) {
                if(food.thucmuatheodvt <= 0.00005){
                    food.thucmuatheodvt = 0.0001;
                }
                if(price.split('.').length==0){
                    num = 0;
                }else{
                    price += 'aa';
                    if(price.split('0000aa').length==2){
                        num = 4;
                    }else if(price.split('000aa').length==2){
                        if(food.thucmuatheodvt<=0.0005){
                            food.thucmuatheodvt = 0.001;
                        }
                        num = 3;
                    }else if(price.split('00aa').length==2){
                        if(food.thucmuatheodvt<=0.005){
                            food.thucmuatheodvt = 0.01;
                        }
                        num = 2;
                    }else if(price.split('0aa').length==2){
                        if(food.thucmuatheodvt<=0.05){
                            food.thucmuatheodvt = 0.1;
                        }
                        num = 1;
                    }
                    if(num==0 && food.thucmuatheodvt<0.5){
                    }else{
                        food.thucmuatheodvt = round(food.thucmuatheodvt, num);
                    }
                }
            } else {
                food.thucmuatheodvt = scope.round_thucmuatheodvt(food.thucmuatheodvt, 2);
                if((price.replace('0aa')+'aa').split('0aa').length==1 && food.thucmuatheodvt > 0.1){
                    food.thucmuatheodvt = round(food.thucmuatheodvt, 1);
                }else if(price.split('0aa').length==1 && food.thucmuatheodvt > 1){
                }
            }
            return food;
        };

        scope.balance.apply = function() {
            var foods = {};
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id){
                    scope.datagrid.data[meal_key][food_id] = food;
                    scope.onChange_thucmuatheodvt(food, true);
                    if (food.luong1tre == 0) {
                        food.luong1tre = 0.05;
                        scope.onChange_luong1tre(food, true);
                    }
                });
            });
            angular.forEach(scope.balance.meals, function (meal, meal_define) {
                scope.selected.meals[meal_define] = meal;
                angular.forEach(meal.dishes, function (dish, ind) {
                    angular.forEach(dish.ingredient, function (fd, ind1) {
                        fd.quantity = round(fd.quantity, 3);
                    });
                });
            });
            scope.totalCalculator();
            scope.balance.init();
        };
        scope.balance.toZeroMoney = function(){
            var chenh = round(scope.balance.getTongchenh(),1);
            if(Math.abs(chenh) == 0 || Math.abs(chenh) > 4000) { 
                return;
            }
            /*Tìm kiếm thực phẩm giá cao nhất mà có số 0 đăng sau lớn hơn hoặc bằng 3*/
            var foods = {};
            angular.forEach(scope.datagrid.data,function(fds, warehouse_id){
                angular.forEach(fds,function(fd,food_id){
                    var price = fd.price+'aa';
                    if(fd.price > 0 && price.split('0000aa').length==2 || price.split('000aa').length==2){
                        foods[food_id] = fd;
                    }
                })
            });
            var fds = [];
            angular.forEach(foods,function(food,food_id){
                fds.push(food);
            });
            if(fds.length>1){
                /*Sắp xếp giảm dần theo số lượng thực phẩm*/
                for(var i=0; i<fds.length-1; i++) {
                    for(var j=i+1; j<fds.length; j++){
                        if(fds[i].thucmuatheodvt_balance<fds[j].thucmuatheodvt_balance){
                            var tmp = fds[i];
                            fds[i] = fds[j];
                            fds[j] = tmp;
                        }
                    }
                }
                /*Lấy 1 nửa ds thực phẩm thôi*/
                foods = [];
                for(var i=0; i<fds.length/2; i++){
                    foods.push(fds[i]);
                }
                /*sắp xếp ds giảm dần theo giá*/
                for(var i=0; i<foods.length-1; i++) {
                    for(var j=i+1; j<foods.length; j++){
                        
                        if(foods[i].price<foods[j].price){
                            var tmp = foods[i];
                            foods[i] = foods[j];
                            foods[j] = tmp;
                        }
                    }
                }
            }
            if(foods[0]){
                scope.balance.addThucmuaForFood(foods[0],chenh);
                scope.balance.totalCalculator(); 
                var thucmua_arr = foods[0].thucmuatheodvt_balance+''.split('.');
                if(thucmua_arr.length == 2){
                    if(thucmua_arr[1].length>4){}
                }
                chenh = round(scope.balance.getTongchenh(),1);
            }
        };
        scope.balance.addThucmuaForFood = function(food,chenh) {
            var thucmua_chenh = $['/'](chenh,food.price);
            food.thucmuatheodvt_balance = Math.abs($['+'](food.thucmuatheodvt_balance,thucmua_chenh));
        };
        scope.balance.addThucmuaForFood1 = function(food,chenh) {
            for(var i=1; i<=5; i++){
                var per = i*2;
                var thucmua_chenh = $['/']($['/'](chenh,food.price),per);
                food.thucmuatheodvt_balance = $['+'](food.thucmuatheodvt_balance,thucmua_chenh);
                scope.balance.totalCalculator();
                chenh = scope.balance.getTongchenh();
            }
        };
        scope.balance.formKeepBound_meal = function(module,meals){
            angular.forEach(meals, function(meal,meal_define){
                angular.forEach(meal.dishes, function(dish, dish_id){
                    if(!dish.lower_bound){
                        dish.lower_bound = 5;
                    }
                    if(!dish.upper_bound){
                        dish.upper_bound = 500;
                    }
                });
            });
            $.dm_datagrid.showAddForm(
                {
                    module: $CFG.project+'/'+module,
                    action:'balance',
                    title:'Cân đối',
                    size: 800,
                    fullScreen: false,
                    showButton: false,
                    content: function(element){
                        $(element).html(scope.getTemplate(scope.balance.templates.keepBoundMeal));
                    }
                },
                function(resp){
                    
                }
            );
        };
        scope.balance.show = function(){
            scope.balance.init();
            var nutritions = scope.selected.group.nutritions;
            if(nutritions+'' === 'null') {
                var msg = ['Không thể cân đối vì chưa cấu hình định mức dinh dưỡng cho nhóm trẻ đang chọn.'];
                msg.push('<a href="'+$CFG.remote.base_url+'/single/'+$CFG.project+'/norm">Vào cấu hình định mức</a>');
                $.dm_datagrid.show({
                    title: 'Thông báo',
                    message: '<div style="padding:15px;height:100px;">'+msg.join('<br/>')+'</div>'
                });
                return;
            }
            $.dm_datagrid.showAddForm(
                {
                    action: 'balance',
                    title: 'Cân đối dinh dưỡng tự động',
                    size: size.wide,
                    scope: scope,
                    fullScreen: true,
                    showButton: false,
                    content: scope.balance.templates.show
                }
            );
        };
        scope.balance.runAuto = function(){
            showSpinner('balance_auto');
            /*Kiểm tra nếu có số phần nào đó bằng 0 thì chạy theo lượng như bản cũ*/
            /*ngược lại cân đối theo số phần*/
            var sophan = scope.balance.getCaloFor_SoPhan();
            var kt = true;
            for(var i in sophan){
                if(sophan[i] == 0){
                    kt = false;
                    break;
                }
            }
            if(kt && false){
                scope.balance.runSophan();
            }else{
                scope.balance.runQuantity();
                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                    angular.forEach(kho, function(food,food_id){
                        if(food.luong1tre_balance+'' == 'Infinity'){
                            food.luong1tre_balance = food.luong1tre;
                        }else if(food.luong1tre_balance == 0){
                            food.luong1tre_balance == 0.01;
                        }
                        scope.balance.onChange_luong1tre(food);
                    })
                })
            }
            hiddenSpinner('balance_auto');
        }
        /*Tổng hợp lượng theo số phần*/
        scope.balance.getCaloFor_SoPhan = function(){
            var ngucoc = 0;
            var traicay = 0;
            var rau = 0;
            var damdv = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,index){
                    if(warehouse_id == 2){
                        var calo = 0;
                        if(!scope.apdungcongthuc){
                            calo += $['*'](scope.co_cau_chuan.protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                                + $['*'](scope.co_cau_chuan.fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                                + $['*'](scope.co_cau_chuan.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)
                        }else{
                            calo += $['*'](scope.co_cau.protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                                + $['*'](scope.co_cau.fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                                + $['*'](scope.co_cau.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)
                        }
                        if(food.ngucoc){
                            ngucoc = $['+'](ngucoc,calo);
                        }else if(food.traicay){
                            traicay = $['+'](traicay,calo);
                        }else if(food.rau){
                            rau = $['+'](rau,calo);
                        }else if(food.damdv){
                            damdv = $['+'](damdv,calo);
                        }
                    }
                });
            });
            return {ngucoc: ngucoc, traicay: traicay, rau: rau, damdv: damdv};
        };

        scope.balance.getFoodsFor_SoPhan = function(){
            scope.balance.sophan = {
                ngucoc: {name:'Ngũ cốc',foods:{}},
                traicay: {name:'Trái cây',foods:{}},
                rau: {name:'Rau',foods:{}},
                damdv: {name:'Ngũ cốc',foods:{}},
                khac: {name:'Khác',foods:{}}
            };
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,food_id){
                    if(scope.selected.balance_warehouse[warehouse_id]) {
                        if(!food.selected) {
                            if(food.ngucoc){
                                scope.balance.sophan.ngucoc.foods[food_id] = food;
                            }else if(food.traicay){
                                scope.balance.sophan.traicay.foods[food_id] = food;
                            }else if(food.rau){
                                scope.balance.sophan.rau.foods[food_id] = food;
                            }else if(food.damdv){
                                scope.balance.sophan.damdv.foods[food_id] = food;
                            }else{
                                scope.balance.sophan.khac.foods[food_id] = food;
                            }
                        }
                    }
                });
            });
        };

        scope.balance.onChange_luong1tre = function(food) {
            scope.onChange_luong1tre(food, true);
            scope.balance.totalCalculator();
        };
        scope.balance.onChange_thucmuatheodvt = function(food){
            scope.onChange_thucmuatheodvt(food, true);
            scope.balance.totalCalculator();
        };
        /*Tổng tiền chênh lệch 1 tre*/
        scope.balance.getTiendicho1tre = function() {
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){
                angular.forEach(foods, function(food,food_id){
                    rs = $['+'](rs,food.thanhtien1nhom_balance);
                });
            });
            return scope.round($['/'](rs,scope.row.sotre));
        };
        /*Tổng tiền chênh lệch 1 tre*/
        scope.balance.getTiendicho_all = function() {
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){
                angular.forEach(foods, function(food,food_id){
                    var thanhtien = $['*'](food.thucmuatheodvt_balance,food.price);
                    rs = $['+'](rs,thanhtien);
                });
            });
            return rs;
        };
        scope.balance.getTongchenh = function(){
            var service_all = $['*'](scope.getTiendichvu(),scope.row.sotre);
            var tienan_all = $['*'](scope.row.tien1tre,scope.row.sotre);
            var tiendicho_all = scope.balance.getTiendicho_all();
            return $['-']($['-'](tienan_all, tiendicho_all), service_all);
        };
        scope.balance.getTienchenhlech = function(){
            var tiendicho1tre = scope.balance.getTiendicho1tre();
            var service = scope.getTiendichvu();
            return scope.round($['-']($['-'](scope.row.tien1tre, tiendicho1tre), service));
        };
        scope.balance.totalCalculator = function(){
            var thanhtien1nhom = 0;
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if(food.exports) {
                        angular.forEach(food.exports, function(fd, food_id_price){
                            thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));
                        });
                    }else{
                        thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](food.thucmuatheodvt, food.price));
                    }
                });
            });
            var service = scope.getTiendichvu();
            var tongtien_dichvu = $['*'](service, scope.row.sotre);
            var tien_bo_tro = scope.row.tien_bo_tro || 0;
            var surplus_end = 0;
            if (scope.surplus) {
                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {
                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];
                }
            }
            scope.balance.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);
            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, tien_bo_tro);
            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, surplus_end);
        };
        scope.balance.isApply = function(){
            for(var meal_key in scope.balance.data){
                for(var food_id in scope.balance.data[meal_key]) {
                    console.log(scope.balance.data[meal_key][food_id].name, scope.balance.data[meal_key][food_id].luong1tre, scope.datagrid.data[meal_key][food_id].luong1tre)
                    if(scope.balance.data[meal_key][food_id].luong1tre != scope.datagrid.data[meal_key][food_id].luong1tre){
                        return false;
                    }
                }
            }
            return true;
        };
        
        /*Tự động cân dối về lượng*/
        scope.balance.runQuantity = function(){
            var buasang_norm = scope.getNormBuasang();
            var max_i = (scope.selected.group.biggest_rate+buasang_norm.biggest_rate - scope.selected.group.smallest_rate-buasang_norm.smallest_rate)/2;
            var max_protein = (scope.selected.group.protein_max - scope.selected.group.protein_min)/2
            var max_fat = (scope.selected.group.fat_max - scope.selected.group.fat_min)/2
            var max_sugar = (scope.selected.group.sugar_max - scope.selected.group.sugar_min)/2
            if(max_i>20){
                max_i = 20;
            }
            if(scope.balance.caloRate()==1 && count(scope.balance.plgRate().dat) == 3){
                return true;
            }
            for(var test=0; test<=10;test++) {
                for(var j=0; j<=max_i;j++) {
                    for(var i=0; i<=max_i; i++){
                        for(var k=0;k<=max_i;k++){
                            if(!scope.balance.whileNotPassQuantity(i,j,k)) {
                                                                
                            }/*else if(count(scope.balance.plgRate().dat) == 3){
                                return true;
                            }*/
                        }
                    }
                }
                if(count(scope.balance.plgRate().dat) != 3 && scope.balance.caloRate()==1) {
                    for(var i=0; i<=max_protein;i++) {
                        for(var j=0; j<=max_fat; j++){
                            for(var k=0;k<=max_sugar;k++){
                                if(!scope.balance.whileNotPassPLG(i,j,k)) {
                                    
                                }
                            }
                        }
                    }
                }
            }
        };
        scope.balance.getTile_PLG_food = function(food){

        };
        
        scope.balance.onChangeThucmua1tretheodvt = function(item){
            var thucmua1tretheodvt = Number(item.thucmua1tretheodvt);
            var luong1tretheodvt = thucmua1tretheodvt;
            if(item.extrude_factor){
                luong1tretheodvt = $['/'](thucmua1tretheodvt,(1+1/(100/item.extrude_factor - 1)));
            }
            item.luong1tre_balance = $['*'](luong1tretheodvt,item.gam_exchange);
        };

        scope.balance.whileNotPassPLG = function(i,j,k) {
            var check = true;
            var plg = scope.balance.getTile_PLG();
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;
            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;
            if(plg.protein<scope.selected.group.protein_min || plg.protein>scope.selected.group.protein_max){
                var tile_canbang = (scope.selected.group.protein_min + scope.selected.group.protein_max)/2;
                var tile_them = 0;
                if(plg.protein<scope.selected.group.protein_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = scope.selected.group.protein_min-plg.protein + (tile_canbang-scope.selected.group.protein_min)/2 + i;
                }else{
                    tile_them = -1*(plg.protein-scope.selected.group.protein_max + (tile_canbang-scope.selected.group.protein_min)/2)-i;
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.proteinChange(quantity_need);
                check = false;
            }
            plg = scope.balance.getTile_PLG();
            if(plg.fat<scope.selected.group.fat_min || plg.fat>scope.selected.group.fat_max){
                var tile_canbang = (scope.selected.group.fat_max + scope.selected.group.fat_min)/2;
                var tile_them = 0;
                if(plg.fat<scope.selected.group.fat_min){ /*Đạm thấp hơn nên cần bổ sung thê*/
                    tile_them = scope.selected.group.fat_min-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/
                }else{
                    tile_them = -1*(plg.fat-scope.selected.group.fat_max)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.fatChange(quantity_need);
                check = false;
            }
            plg = scope.balance.getTile_PLG();
            if(plg.sugar<scope.selected.group.sugar_min || plg.sugar>scope.selected.group.sugar_max){
                var tile_them = 0;
                if(plg.sugar<scope.selected.group.sugar_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = scope.selected.group.sugar_min-plg.sugar+k;
                }else{
                    tile_them = -1*(plg.sugar-scope.selected.group.sugar_max)-k;
                }
                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;
                scope.balance.sugarChange(quantity_need);
                check = false;
            }
            return check;
        };
        scope.balance.whileNotPassQuantity = function(i,j,k) {
            var check = true;
            var protein = scope.balance.getProtein();
            var plg = {};
            plg.protein = protein/(scope.selected.group.nutritions.animal_protein + scope.selected.group.nutritions.vegetable_protein)*100;
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;
            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;
            if(plg.protein<smallest_rate || plg.protein>biggest_rate){
                var tile_canbang = (biggest_rate + smallest_rate)/2;
                var tile_them = 0;
                if(plg.protein<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = smallest_rate-plg.protein + (tile_canbang-smallest_rate)/2 + i;
                }else{
                    tile_them = -1*(plg.protein-biggest_rate + (tile_canbang-smallest_rate)/2)-i;
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.proteinChange(quantity_need);
                check = false;
            }
            var fat = scope.balance.getFat();
            plg.fat = fat/( scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat )*100;
            if(plg.fat<smallest_rate || plg.fat>biggest_rate){
                var tile_canbang = (biggest_rate + smallest_rate)/2;
                var tile_them = 0;
                if(plg.fat<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thê*/
                    tile_them = smallest_rate-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/
                }else{
                    tile_them = -1*(plg.fat-biggest_rate)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.fatChange(quantity_need);
                check = false;
            }

            var sugar = scope.balance.getSugar();
            plg.sugar = sugar/scope.selected.group.nutritions.sugar*100;
            if(plg.sugar<smallest_rate || plg.sugar>biggest_rate){
                var tile_them = 0;
                if(plg.sugar<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = smallest_rate-plg.sugar+k;
                }else{
                    tile_them = -1*(plg.sugar-biggest_rate)-k;
                }
                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;
                scope.balance.sugarChange(quantity_need);
                check = false;
            }
            return check;
        };
        scope.balance.sugarChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Gạo','Đường','Kẹo','Bánh khảo chay','Bánh','Bột mì tinh (bột năng)','Bột dong lọc','Bột khoai','Trân châu sắn','Bột bán','Si rô','Mạch nha','Miến (bún tàu), Hủ tiếu khô','Mật ong','Mứt cam'];
            var food_ids = ['1179','1178','588','612'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if((indexOf_array(food.name,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.sugar>=15*/ 
                             || food.nutritions.sugar*4>food.nutritions.fat*9 && food.nutritions.sugar>food.nutritions.protein) {
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    })
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.sugar<foods[j].nutritions.sugar){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.sugar);
                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.sugar/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = food.luong1tre/food.gam_exchange;
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.fatChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Dầu','Mỡ','Tủy xương bò','Bơ','mayonnaise'];
            var food_ids = ['907','908','909','910','911','912','913','914','915','1288','917','916'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if((indexOf_array(food.food_id,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.fat >= 5 || food.nutritions.fat>=40*/
                                || food.nutritions.sugar*4<food.nutritions.fat*9 && food.nutritions.fat*9>food.nutritions.protein*4){
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    })
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.fat<foods[j].nutritions.fat){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.fat);

                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.fat/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.proteinChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Tôm','cá','Mắm','bò','trâu','mực','thịt','trứng'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if(indexOf_array(food.name,names) /*&& food.nutritions.protein>=5 || food.nutritions.protein >= 20*/ 
                                || food.nutritions.protein*4>food.nutritions.fat*9 && food.nutritions.sugar<food.nutritions.protein){
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    });
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.protein<foods[j].nutritions.protein){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.protein);

                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.protein/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.sumCalo = function(foods){
            var rs = 0;
            angular.forEach(foods, function(food,food_id){
                if(!scope.apdungcongthuc){
                    rs += $['*'](scope.co_cau_chuan.protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                        + $['*'](scope.co_cau_chuan.fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                        + $['*'](scope.co_cau_chuan.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)
                }else{
                    rs += $['*'](scope.co_cau.protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                        + $['*'](scope.co_cau.fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                        + $['*'](scope.co_cau.sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)
                }
            });
            return rs;
        };
        scope.balance.getTotal_nutritions = function(){
            var rs = {
                dam: 0, beo:0, duong: 0, calo:0
            };
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                if(scope.dinhduong_calc[warehouse_id]){
                    angular.forEach(kho, function(food,food_id){
                        rs.dam = $['+'](rs.dam,$['*'](food.luong1tre_balance,(food.nutritions.protein/100)));
                        rs.beo = $['+'](rs.beo,$['*'](food.luong1tre_balance,(food.nutritions.fat/100)));
                        rs.duong = $['+'](rs.duong,$['*'](food.luong1tre_balance,(food.nutritions.sugar/100)));
                    })
                }
            });
            if(!scope.apdungcongthuc){
                rs.calo = scope.co_cau_chuan.protein * rs.dam + scope.co_cau_chuan.fat * rs.beo + scope.co_cau_chuan.sugar * rs.duong;
            }else{
                rs.calo = scope.co_cau.protein * rs.dam + scope.co_cau.fat * rs.beo + scope.co_cau.sugar * rs.duong;
            }
            return rs;
        };
        scope.balance.getCaloAll = function(){
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,food_id){
                    rs += scope.getCalo(food, 'luong1tre_balance');
                })
            });
            return rs;
        };
        /*  Tính lượng khẩu phần (calo) đạt hay chưa */
        scope.balance.caloRate = function(){
            var value = 0;
            var calo = round(scope.sumCaloMeals(scope.balance.meals), 0);
            var rate = scope.getNormSelected();
            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                value = 1;
            } else {
                if (calo < rate.smallest_rate) {
                    value = 0;
                } else {
                    value = 2;
                }
            }

            return value;
        };
        scope.balance.caloRateBind = function(){
            var value = scope.balance.caloRate();
            if(value == 1){
                text = 'Đạt';
                class_color = '';
            }else if(value == 0){
                text = 'Chưa đạt';
                class_color = 'color-red';
            }else{
                text = 'Vượt quá định mức';
                class_color = 'btn-color-blue';
            }
            return {text: text, 'class': class_color, value: value};
        };
        /* Tính tổng đạm*/
        scope.balance.getProtein = function(){
            var animal_protein = 0;
            var vegetable_protein = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(row,index){
                    if(warehouse_id == 2){
                        if(row.is_meat == 1) {   /*Nếu là động vật*/
                            animal_protein += row.nutritions.protein*row.luong1tre_balance/100;
                        }else{
                            vegetable_protein += row.nutritions.protein*row.luong1tre_balance/100;
                        }
                    }
                });
            });
            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';
            var value = ( animal_protein + vegetable_protein ) ;
            return value;
        };
        /* Tính toán tỉ lệ đạt béo*/
        scope.balance.getFat = function(){
            var animal_fat = 0;
            var vegetable_fat = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(row,index){
                    if(warehouse_id == 2){
                        if(row.is_meat == 1) {   /*Nếu là động vật*/
                            animal_fat += row.nutritions.fat*row.luong1tre_balance/100;
                        }else{
                            vegetable_fat += row.nutritions.fat*row.luong1tre_balance/100;
                        }
                    }
                });
            });
            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';
            var value = ( animal_fat + vegetable_fat ) ;
            return value;
        };
        /* Tính tỉ lệ từng loại thành phần dinh dưỡng*/
        scope.balance.getSugar = function(){
            var value = 0;
            if(scope.selected.group.nutritions) {
                if(!scope.selected.group || !scope.selected.group.nutritions.sugar) return value;
                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                    angular.forEach(kho, function(row,index){
                        if(warehouse_id == 2){
                            value += row.nutritions.sugar*row.luong1tre_balance/100;
                        }
                    });
                });
            }
            return value;
        };
        
        /*Tính tỉ lệ PLG*/
        scope.balance.getTile_PLG = function(name){
            var rs = {
                protein: 0,
                fat: 0,
                sugar: 0
            };
            var tongcalo = 0;
            angular.forEach(scope.balance.data, function(foods, warehouse_id){
                if ( scope.row.meal_selection[warehouse_id].selected ) {
                    angular.forEach(foods, function(food,food_id){
                        if(!scope.apdungcongthuc){
                            rs.protein += scope.co_cau_chuan.protein * food.luong1tre_balance*food.nutritions.protein/100;
                            rs.fat += scope.co_cau_chuan.fat * food.luong1tre_balance*food.nutritions.fat/100;
                            rs.sugar += scope.co_cau_chuan.sugar * food.luong1tre_balance*food.nutritions.sugar/100;
                        }else{
                            rs.protein += scope.co_cau.protein * food.luong1tre_balance*food.nutritions.protein/100;
                            rs.fat += scope.co_cau.fat * food.luong1tre_balance*food.nutritions.fat/100;
                            rs.sugar += scope.co_cau.sugar * food.luong1tre_balance*food.nutritions.sugar/100;
                        }
                    })
                }
            });
            tongcalo = rs.protein + rs.fat + rs.sugar;
            rs.protein = scope.round(rs.protein/tongcalo*100,1);
            rs.fat = scope.round(rs.fat/tongcalo*100,1);
            rs.sugar = scope.round(rs.sugar/tongcalo*100,1);
            return rs;
        };
        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */
        scope.balance.plgRate = function(){
            var rs = {};
            var tile = scope.getTile_PLG(scope.balance.meals);
            var tile_dat = [];
            var tile_chuadat = [];
            var tile_vuotqua = [];
            if(tile.protein>=scope.selected.group.protein_min && tile.protein<=scope.selected.group.protein_max){
                tile_dat.push({define:'protein',name: 'Chất đạm'});
            }else if(tile.protein<scope.selected.group.protein_min) {
                tile_chuadat.push({define:'protein',name: 'Chất đạm'})
            }else{
                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})
            }
            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){
                tile_dat.push({define:'fat',name: 'Chất béo'});
            }else if(tile.fat<scope.selected.group.fat_min) {
                tile_chuadat.push({define:'fat',name: 'Chất béo'})
            }else{
                tile_vuotqua.push({define:'fat',name: 'Chất béo'})
            }
            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){
                tile_dat.push({define:'sugar',name: 'Chất bột'});
            }else if(tile.sugar<scope.selected.group.suga_min) {
                tile_chuadat.push({define:'sugar',name: 'Chất bột'})
            }else{
                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})
            }
            rs = {
                dat: tile_dat,
                chuadat: tile_chuadat,
                vuotqua: tile_vuotqua
            };
            return rs;
        };

        scope.balance.plgRateBind = function(){
            var thanhphan = scope.balance.plgRate();
            var text = '';
            if(count(thanhphan.dat) == 3){
                text = 'Cân đối';
                class_color = '';
            }else{
                text = 'Chưa cân đối';
                class_color = 'color-red';
            }
            return {text: text, 'class': class_color, thanhphan: thanhphan};
        };

        scope.balance.onChange_thucmuatheodvtApply = function(food){
            food.gam_exchange || (food.gam_exchange = 1000);
            var thucmuatheodvt = Number(food.thucmuatheodvt);
            food.thucmua1tretheodvt = scope.round($['/'](thucmuatheodvt, scope.row.sotre),4);
            food.thucmua1nhom = thucmuatheodvt * food.gam_exchange / 1000;
            food.thucan1nhom = food.thucmua1nhom;
            if(food.extrude_factor){
                food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
            }
            food.luong1tre = round($['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000));
            food.thucan1nhom = round(food.thucan1nhom);
            food.thucmua1nhom = round(food.thucmua1nhom);
            food.luong1tre_root = food.luong1tre;
            food.thanhtien1nhom = $['*'](thucmuatheodvt, food.price);
            scope.divide_luong1tre(food);
        };
        scope.balance.foodSelectAll = function() {
            scope.balance.selectallfood = !scope.balance.selectallfood;
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    food.selected = scope.balance.selectallfood;
                })
            });
        };
        scope.balance.luong1treOnChange = function(food){
            food.luong1tre_balance = Number(food.luong1tre_balance);
            var luong1tretheodvt = $['/'](food.luong1tre_balance,food.gam_exchange);
            var thucmuatheodvt = luong1tretheodvt;
            if(food.extrude_factor){
                thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
            }
            food.thanhtien_balance = $['*'](thucmuatheodvt,food.price);
        };
        scope.balance.thanhtienOnChange = function(food){
            food.tiendicho1tre_balance = Number(food.tiendicho1tre_balance);
            var thucmuatheodvt = $['/'](food.tiendicho1tre_balance,food.price);
            var luong1tretheodvt = thucmuatheodvt;
            if(food.extrude_factor){
                luong1tretheodvt = thucmuatheodvt/(1+1/(100/food.extrude_factor - 1));
            }
            food.luong1tre_balance = $['*'](luong1tretheodvt, food.gam_exchange);
        };
        /*  Trả ra số phần quy định đã chọn */
        scope.balance.getDinhmucSophanSelected = function() {
            var rs = [];
            angular.forEach(scope.selected.group.dinhmuc, function(dinhmuc,key){
                if(dinhmuc.selected){
                    rs.push(key);
                }
            });
            return rs;
        };
        scope.balance.warehouseSelectedOnClick = function(meal) {
            scope.warehouseSelectedOnClick(meal);
            scope.balance.init();
        };
        scope.balance.getSelectBySophan = function(foods){
            var rs = {};
            var sophan = scope.balance.getDinhmucSophanSelected();
            if(sophan.length==0){
                return foods;
            }
            angular.forEach(foods, function(food,index){
                var check_sophan = false;
                angular.forEach(sophan, function(key,index){
                    if(food[key]){
                        rs[food.food_id] = food;
                    }
                })                
            });
            return rs;
        };
        /*  Trả ra mảng tất cả thực phẩm được chọn theo kho  */
        scope.balance.getFoodOfWarehouse = function(warehouse_id){
            var foods = {};
            if(scope.selected.balance_warehouse[warehouse_id]){
                foods = scope.balance.getSelectBySophan(scope.balance.data[warehouse_id]);
            }
            return foods;
        };

        scope.balance.onCheckedAllSophan = function(){
            scope.balance.dinhmucSelectall = !scope.balance.dinhmucSelectall;
            angular.forEach(scope.selected.group.dinhmuc, function(item,index){
                item.selected = scope.balance.dinhmucSelectall
            });
        };
        scope.balance.isCheckedAllSophan = function(){
            var rs = [];
            angular.forEach(scope.selected.group.dinhmuc, function(item,index){
                if(item.selected){
                    rs.push(true);
                }
            });
            if(rs.length == count(scope.selected.group.dinhmuc)){
                return true;
            }else{
                return false;
            }
        };
        scope.balance.showPartPLGInfo = function(){
            $.menu_planning.showPartPLGInfo_balance();
        }
    }
};
    
$(document).ready(function(){ 
    $(document).mouseup(function (e)
    {
        var container = $(".tab-main-index");
        if($(window).width()<500){
            if (!container.is(e.target) && container.has(e.target).length === 0)
            {
                container.hide();
            }
        }
        
    });
    hv();
    $(window).resize(function() {
        hv();
    })
    $(document).mouseup(function (e){
        var container = $(".item-setup-mc-new");
     
        if (!container.is(e.target) && container.has(e.target).length === 0)
        {
            container.hide();
        }
    });

    var url = '{{ url() }}/single/{{$config->project}}';
    if(window.location.href != url){
        $(".content-qt").hide("slow");
    }
    if($(window).height() < 650)
    {
        $('.content-qt').css({"height":$(window).height(),"overflow":"auto"});
    }
    
    $('#spinner-container').addClass('spinner'+rand(1,4));
    
    $(document).mouseup(function (e){
        var container = $(".quy-trinh");
        if (!container.is(e.target) && container.has(e.target).length === 0)
        {
            $(".content-qt").hide("slow");
        }
    });
    $('.btn-quy-trinh').click(function(){
        if($(window).height() < 650){
            $('.content-qt').css({"height":$(window).height(),"overflow":"auto"});
        }
        $(".content-qt").toggle("slow", function() {});
    })
        
    $( "a" ).click(function() {
        $('.logo-dd-new img').css({"display":"block","width":"100%"});
        $('.main-content').css({"z-index":"3"});
    });
    if(window.location.href != "http://localhost/pms/single/{{$config->project}}"){
        $('.logo-dd-new img').css({"display":"block","width":"100%"});
        $('.main-content').css({"z-index":"3"});
    };
    $('.bottom-hidden').click(function(){       
        $(".bottom-content").animate({
            top:($(window).height()-60)+"px",
        },700);
        setTimeout(function(){
            $(".bottom-content").toggle();
            $('.footer').css({"display":"none"});
        },800)
    });

    $(window).scroll(function(e){
        scroll1();
    });
    
    var hienquytrinh = getCookie('hienquytrinh');
    if(!hienquytrinh){
        hienquytrinh = 0;
    }
    
    if(hienquytrinh == 0){
        $(".content-qt").hide("slow");

    }else{
        $("#tudonghien12").attr("checked", true);
    }

    
    var visitor = getCookie('visitor11234');

    if(!visitor){
        visitor = 0;
    }
    visitor = Number(visitor);
    visitor++;
    
    setCookie('visitor11234',visitor,18000);
    if(visitor<4 && false){
        $('#dialog-tphcm').dialog({
            title: '',
            width: 650,
            height: 500,
            closed: false,
            cache: false,
            modal: true,
            onOpen : function (ele) {
                $(ele).show();
                $('.window-shadow').css("display","none");
                
                $('.window').css({"background":"rgba(0,0,0,0)","border":"0px","box-shadow":"none"});
                var btn_close = $('<div style="top: 62px;right: 44px;position: absolute;font-size:17px; opacity: 1;" class="close"><img src="{{ asset("/images/close.png") }}"></div>');

                $('#dialog').css({"background":"rgba(0,0,0,0)","border":"0px"}).append(btn_close);
                btn_close.click(function(){
                    $('#dialog').dialog("close");
                })
                $('.window-mask').click(function(){
                    $('#dialog').dialog("close");
                })
            }
        })
    }
});
function bottomShow(){
    $(".bottom-content").toggle();
    $(".bottom-content").animate({
        top:"0px"
    },700);
    $('.footer').css({"display":"block"});
}
function hideqt(){
    $(".content-qt").hide("slow");
}
function scroll1() {
    var sticky = $('.menu-bc'),
        scroll = $(window).scrollTop(),
        Wheight = $(window).height();
    if (scroll>Wheight-80) 
        sticky.addClass('fixed');
    else sticky.removeClass('fixed');
}
function tudonghien(el){
    if($("#tudonghien12:checked").length){
        setCookie('hienquytrinh',1,18000);
    }else{
        setCookie('hienquytrinh',0,18000);
    }
}
function tabMain() {
    if($(window).width()<500){
        $('.tab-main-index').toggle();
    }   
}
function hv(){
    setTimeout(function(){
        var navmain =  $(".nav-main-content").width();
        var inmain = $(".in-main").width();
        var w2out = navmain + 20 + (inmain - navmain)/2;
        if($(window).width() < 420){
            w2out = 5;
            $('.item-setup-mc-new').css("top","40px");
        }
        if($(window).width() < 769 && $(window).width() > 420){
            w2out = navmain + 40;
        }
        $('.item-setup-mc-new').css("left", w2out+"px");
    }, 250);
}
function mcNew() {
    $('.item-setup-mc-new').toggle();
}
angular_app.controller('payoutController', ['$scope', function ($scope) {
    $scope.control = 'payout';
    $scope.templates = {};
    $scope.fee = {};
    $scope.init = function () {
        var date = new Date();
        var month = date.getMonth() + 1;
        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                $scope.months = data.months;
                $scope.month = month.toString();
                $scope.year = data.months[0].year;

                $scope.feeConfigs = data.feeConfigs;
                $scope.fee.id = '';
                $scope.rows = [];
                $scope.selected = {};
            })
        });
    };
    $scope.getRows = function () {
        process($CFG.project + '/' + $scope.control + '/list?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data.data;
                $scope.rows = data.rows;
                $scope.fee.beginSurplus = data.begin_surplus;
                $scope.fee.count = 0;
                $scope.fee.check = '';
                /*Set lại là 0 khi gọi lại để đếm số thứ tự*/
            })
        });
    };

    $scope.monthChange = function () {
        $scope.getRows();
    };

    $scope.feeChange = function () {
        $scope.getRows();
    };

    $scope.sumCollection = function () {
        var rs = 0;
        if ($scope.rows) {
            angular.forEach($scope.rows, function (row, index) {
                if (row.fee !== "" && row.fee > 0) {
                    rs = $['+'](rs, row.fee);
                }
            })
        }
        return rs;
    };
    $scope.sumExpenditure = function () {
        var rs = 0;
        if ($scope.rows) {
            angular.forEach($scope.rows, function (row, index) {
                rs = $['+'](rs, row.cost_item);
            })
        }
        return rs;
    };
    $scope.getEndSurplus = function () {
        var rs = 0;
        $scope.fee.beginSurplus || ($scope.fee.beginSurplus = 0);
        rs = $['+'](rs, $scope.fee.beginSurplus);
        rs = $['+'](rs, $scope.sumCollection());
        rs = $['-'](rs, $scope.sumExpenditure());
        return rs;
    };
    $scope.rowSelect = function (row) {
        if (!row) {
            $scope.selected.rowAdd = true;
            angular.forEach($scope.rows, function (item, index) {
                item.selected = false;
            });
        } else {
            angular.forEach($scope.rows, function (item, index) {
                item.selected = false;
            });
            $scope.selected.rowAdd = false;
            row.selected = true;
        }
    };

    $scope.rowAdd = function () {
        var tmp = $scope.rows;
        var row = {
            day: '',
            cost_item: 0,
            explain: '',
            fee: 0,
            is_fee: false,
            number_cost: "",
            number_receipts: "",
        };
        tmp.push(row);
        $scope.rows = tmp;
    };

    $scope.delete = function () {
        var tmp = $scope.rows;
        if($scope.fee.check === '')
            alert("Vui lòng chọn một dòng!");
        else{
            tmp.splice($scope.fee.check, 1);
            $scope.rows = tmp;
        }
    };

    $scope.refresh = function (){
        var msg = '<div style="margin-left: 42px"><p>Bạn chắc chắn?</p><p>Hành động này sẽ cập nhật lại dữ liệu và các <span style="color: red">khoản chi hiện tại sẽ bị mất</span>!</p></div>';
        $.messager.confirm('Cảnh báo', msg, function(r){
            if (r){
                process($CFG.project + '/' + $scope.control + '/delete?year=' + $scope.year + '&month=' + $scope.month + '&feeId=' + $scope.fee.id, {async: true}, function (resp) {
                    $scope.getRows();
                });
            }
        });
    };

    $scope.save = function () {
        var data = {
            month: {
                month: $scope.month,
                year: $scope.year
            },
            fee_category_id: $scope.fee.id,
            rows: $scope.rows,
            begin_surplus: $scope.fee.beginSurplus,
            end_surplus: $scope.getEndSurplus(),
            fees: $scope.sumCollection(),
            spent: $scope.sumExpenditure(),
            async: true
        };
        var urls = [$CFG.project, 'fee_report', 'saveSoquytienmat'];
        process(urls.join('/'), data, function () {
            $scope.getRows();
            alert("Lưu thành công!");
        });
    };

    $scope.onTick = function (index) {
        $scope.fee.check = index;
        $scope.fee.cost = $scope.rows[index].cost_item;
    };

    $scope.getCount = function (fee) {
        if (fee == 0)
            $scope.fee.count += 1;
        return $scope.fee.count;
    };

    /*Validate*/
    $scope.dayChange = function (row) {
        var days = (new Date($scope.year, $scope.month, 0)).getDate();
        if (row.day <= 0)
            row.day = 1;
        if (row.day > days)
            row.day = days;
    };

    $scope.moneyChange = function (row) {
        if (row.cost_item <= 0)
            row.cost_item = 0 - row.cost_item;
    };
}]);
angular_app.controller('exportController', ['$scope', function ($scope) {
    $scope.control = 'export';
    $scope.templates = {

    };
    $scope.init = function () {
        var date = new Date();
        var month = date.getMonth() + 1;
        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                var dateExport = [];
                data.dateExport.forEach(function (element) {
                    dateExport.push({
                        'date': getDate(element.date)
                    });
                });
                $scope.months = data.months;
                $scope.month = month.toString();
                $scope.dateExport = dateExport;
            });
        });
    };
    $scope.monthChange = function () {
        var month = $scope.month;
        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                var dateExport = [];
                data.dateExport.forEach(function (element) {
                    dateExport.push({
                        'date': getDate(element.date)
                    });
                });
                $scope.dateExport = dateExport;
            });
        });
    }
}]);
angular_app.controller('receiptController', ['$scope', function ($scope) {
    $scope.control = 'receipt';
    $scope.templates = {
        form: $CFG.project + '/' + $scope.control + '/form.html',
        addToVote: $CFG.project + '/' + $scope.control + '/add-to-vote.html',
        print: $CFG.project + '/' + $scope.control + '/print.html',
    };
    $scope.init = function () {
        process($CFG.project + '/' + $scope.control + '/init', {async: true}, function (resp) {
            $scope.$apply(function () {
                $scope.date = dateboxOnSelect();

                $scope.suppliers = resp.data.suppliers;
                $scope.warehouses = resp.data.warehouses;
                $scope.mapMeasure = resp.data.mapMeasure;

                $scope.suppliers.forEach(function (supplier, index) {
                    $scope.suppliers[index].id = supplier.id;
                });

                $scope.mapWarehouses = {};
                $scope.warehouses.forEach(function (warehouse) {
                    $scope.mapWarehouses[warehouse.id] = warehouse.name;
                });

                $scope.mapSuppliers = {};
                $scope.suppliers.forEach(function (supplier) {
                    $scope.mapSuppliers[supplier.id] = supplier.name;
                });

                /*Khởi tạo table*/
                var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + $scope.control + '/list';
                $.dm_datagrid.init(url, $scope.control, '', $scope.columns, $scope.options);
                $scope.table = $('#tbl_' + $scope.control);

                /*Thông tin lưu khi chọn thực phẩm*/
                $scope.foodId = '';
                $scope.price = 0;
                $scope.food = {};
                $scope.measure = '';
            })
        });
    };

    $scope.columns = [[
        {
            field: 'check', formatter: function (value, row) {
                if ((row.vote_id === null || row.vote_id === '')  && row.is_legal !== 2)
                    return '<input id="check-id-' + row.id + '" type="checkbox">';
                return '';
            }
        },
        {title: 'Điểm trường', field: 'school_point', width: 40, align: 'center'},
        {title: 'Phiếu', field: 'vote_name', sortable: true, align: 'left', width: 40},
        {
            title: 'Thực phẩm', field: 'name', sortable: true, align: 'left', width: 120,
            formatter: function (value, row) {
                if(row.is_legal === 2)
                    return value + ' <a title="Tồn" class="fa fa-info color-green"></a>';
                return value;
            }
        },
        {
            title: 'Kho', field: 'warehouse_id', align: 'left', width: 50, formatter: function (value) {
                return $scope.mapWarehouses[value];
            }
        },
        {title: 'Số lượng', field: 'quantity', width: 90, align: 'center', editor: 'textbox'},
        {
            title: 'Đơn giá',
            field: 'price',
            width: 100,
            editor: 'numberbox',
            align: 'center',
            formatter: function (value) {
                return digit_grouping(value);
            }
        },
        {
            title: 'Nhà cung cấp', field: 'supplier_id', width: 100, sortable: true, formatter: function (value) {
                return $scope.mapSuppliers[value];
            }
        },
        {
            title: 'Đơn vị tính', field: 'measure_id', width: 50, align: 'center', formatter: function (value) {
                return $scope.mapMeasure[value];
            }
        }
    ]];
    $scope.options = {
        enableFilter: [
            {
                field: 'school_point',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'storages.school_point');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'storages.school_point',
                                op: 'equal',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'vote_name',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'votes.name');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'votes.name',
                                op: 'beginwith',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'name',
                type: 'textbox',
                options: {
                    panelHeight: 'auto',
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'foods.name');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'foods.name',
                                op: 'beginwith',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'warehouse_id',
                type: 'combobox',
                options: {
                    panelHeight: 'auto',
                    data: [{value: '', text: 'Tất cả'}, {value: 1, text: 'Kho sáng'}, {value: 2, text: 'Kho trưa'}],
                    onChange: function (value) {
                        if (value === '') {
                            $scope.table.datagrid('removeFilterRule', 'warehouse_id');
                        } else {
                            $scope.table.datagrid('addFilterRule', {
                                field: 'warehouse_id',
                                op: 'equal',
                                value: value
                            });
                        }
                        $scope.table.datagrid('doFilter');
                    }
                }
            },
            {
                field: 'measure_id',
                type: 'label'
            },
            {
                field: 'supplier_id',
                type: 'label'
            },
            {
                field: 'price',
                type: 'label'
            },
            {
                field: 'quantity',
                type: 'label'
            },
            {
                field: 'check',
                type: 'label'
            },
        ],
        view: groupview,
        groupField: 'date',
        groupFormatter: function (id, rows) {
            var objectDate = new Date(rows[0].date);
            var dateFomart = objectDate.getDate() + "/" + (objectDate.getMonth() + 1) + "/" + objectDate.getFullYear();
            return 'Ngày nhập ' + dateFomart + ': <i>' + rows.length + '</i>';
        },
    };

    $scope.form = function () {
        $.dm_datagrid.showAddForm({
                module: $CFG.project + '/' + $scope.control,
                action: 'form',
                title: 'Quản lý nhập kho',
                size: size.wide,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.templates.form,
                onshown: function () {
                    process($CFG.project + '/' + $scope.control + '/form', {async: true}, function (resp) {
                        $scope.$apply(function () {
                            var data = resp.data;
                            $scope.mapFoods = data.mapFoods;
                            $scope.mapFoodById = {};
                            $scope.mapFoods.forEach(function (food) {
                                $scope.mapFoodById[food.id] = food.name;
                            });
                            delete $scope.mapFoods;

                            $scope.vote = data.vote;
                            $scope.dateDisabled = false;
                            /*Ngày hiện tại*/
                            $scope.school = data.school;

                            /*add*/
                            $scope.supplierId = '';
                            $scope.warehouseId = '2';
                            $scope.quantity = 0;
                            $scope.selected = {};
                            $scope.rows = [];
                        })
                    })
                },
                cancel: function () {
                    $("#tbl_" + $scope.control).datagrid('reload');
                }
            }
        );
    };

    /* 2. Thay đổi số phiếu*/
    $scope.formInputChange = function () {
        var post = {
            'vote': $scope.vote
        };
        process($CFG.project + '/' + $scope.control + '/formInputChange', {post: post, async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                $scope.dateDisabled = false;
                if (data.id !== -1)
                    $scope.dateDisabled = true;

                $scope.date = getDate(data.date);
                if (data.rows.length > 0) {
                    data.rows.forEach(function (row, index) {
                        data.rows[index].quantity = parseFloat(row.quantity);
                        data.rows[index].price = parseFloat(row.price);
                    });
                }
                $scope.rows = data.rows;
            });
        });
    };

    /* 3. Chọn thực phẩm */
    $scope.selectFood = function (food) {
        var url = $CFG.remote.base_url + '/doing/dinhduong/dish/getFoodDetailById';
        var data = {async: true, id: food.id};
        process(url, data, function (resp) {
            if (!resp) return;
            else {
                $scope.$apply(function () {
                    angular.forEach(resp, function (food) {
                        $scope.food = food;
                        $scope.foodId = food.food_id;
                        $scope.measure = food.measure_name;
                        $scope.price = food.price;
                    });
                });
            }
        }, function () {
        }, false);
    };

    /* 4. Lưu */
    $scope.formAdd = function () {
        var post = {
            'vote': $scope.vote,
            'date': $scope.date,
            'food_id': $scope.foodId,
            'warehouse_id': $scope.warehouseId,
            'quantity': $scope.quantity,
            'price': $scope.price,
            'supplier': $scope.supplierId,
            'food': $scope.food
        };
        process($CFG.project + '/' + $scope.control + '/formAdd', {post: post, async: true}, function (resp) {
            if (resp.result === 'success') {
                $scope.$apply(function () {
                    post.id = parseInt(resp.data.id);
                    post.warehouse_id = parseInt(post.warehouse_id);
                    post.food_name = post.food.name;
                    post.supplier_id = post.supplier;
                    post.measure_id = post.food.measure_id;
                    post.school_point = $scope.school;
                    post.exported = false;
                    $scope.rows.push(post);
                });
            }
        });
    };

    $scope.formEdit = function (row) {
        process($CFG.project + '/' + $scope.control + '/formEdit', {post: row}, function (resp) {
            if (resp.result === 'success') {
                alert('Sửa thành công');
            }
        });
    };

    $scope.del = function (index, row) {
        if (!row) {
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>', ' - Dữ liệu sau khi xóa sẽ không thể khôi phục.'];
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function (r) {
            if (r) {
                process($CFG.project + '/' + $scope.control + '/del', {ids: row.id}, function () {
                    $scope.$apply(function () {
                        $scope.rows.splice(index, 1);
                    });
                })
            }
        });
    };
    $scope.showDetail = function (storage_id) {
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + 'storage',
                action: 'detail',
                title: 'Chi tiết xuất kho',
                content: function (element) {
                    loadForm($CFG.project + '/' + 'storage', 'detail', {storage_id: storage_id}, function (resp) {
                        $scope.$apply(function ($scope) {
                            var form = '<div >'+resp+'</div>';
                            $(element).html($scope.compile(form,$scope));
                            if(typeof callback === 'function'){
                                callback($scope);
                            }
                        });
                    })
                }
            },
            function (resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
                    $scope.formInputChange();
                }
            }
        );
    };

    $scope.selectDate = function(date){
        $scope.date = date;
    };
    $scope.addToVote = function () {
        var rows = $scope.table.datagrid('getRows');

        const ADD = 'add';
        const MANY_DATE = 'many-date';
        const NULL = 'null';
        const INIT = 'init';

        var check = ADD;
        var date = INIT;
        for (var i in rows) {
            if(rows.length < 1){
                check = NULL;
                break;
            }
            if ($('input#check-id-' + rows[i].id).prop('checked')) {
                if(date === INIT)
                    date = rows[i].date;
                else {
                    if(date !== rows[i].date){
                        check = MANY_DATE;
                        break;
                    }
                }
            }
        }
        switch (check) {
            case ADD:
                $.dm_datagrid.showAddForm({
                    module: $CFG.project + '/' + $scope.control,
                    action: 'addToVote',
                    title: 'Chọn phiếu',
                    size: size.small,
                    fullScreen: false,
                    showButton: false,
                    scope: $scope,
                    content: $scope.templates.addToVote,
                    onshown: function () {
                        process($CFG.project + '/' + $scope.control + '/addToVote', {async: true, data: {date: date}}, function (resp) {
                            $scope.$apply(function () {
                                var data = resp.data;
                                $scope.votes = data.votes;
                            })
                        })
                    },
                    cancel: function () {
                        $("#tbl_" + $scope.control).datagrid('reload');
                    }
                });
                break;
            case MANY_DATE:
                alert("Chỉ được chọn các thực phẩm cùng ngày để thêm vào phiếu");
                break;
            default:
                alert("Hãy chọn một dòng");
                break;
        }
    };

    $scope.saveToVote = function (voteId) {
        if(voteId === ''){
            alert("Vui lòng chọn một phiếu!");
        }
        else {
            var ids = [];
            var rows = $scope.table.datagrid('getRows');
            for (var i in rows) {
                if ($('input#check-id-' + rows[i].id).prop('checked')) {
                    ids.push(rows[i].id);
                }
            }
            var post = {
                voteId : voteId,
                ids: ids,
            };
            process($CFG.project + '/' + $scope.control + '/saveToVote', {async: true, post: post}, function (resp) {
                if(resp.result === 'success')
                    alert('Thêm thành công!');
            })
        }
    };

    $scope.print = function () {
        $scope.begin = dateboxOnSelect();
        $scope.end = dateboxOnSelect();
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + $scope.control,
            action: 'in',
            title: 'In phiếu nhập kho',
            size: size.normal,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.templates.print,
        });
    }
}]);
window.angular_app.controller('menu_adjustController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){
    $scope.project = 'dinhduong';
    $scope.control = 'menu_adjust';
    $scope.form_id = 'menu_adjust-total';
    $scope.dialog = undefined;
    $scope.controller = {
        templates: {
            addForm: $scope.project + '/' + $scope.control + '/add.html',
            marketbillForm: $scope.project + '/' + $scope.control + '/marketbillForm.html',
            editServicePrice: $scope.project + '/' + $scope.control + '/edit_service_price.html',
            duplicateToMenuPlanning: $scope.project + '/' + $scope.control + '/duplicate_to_menu_planning.html',
            quantityInfo: $scope.project + '/' + $scope.control + '/quantity_info.html',
            nutritionInfo: $scope.project + '/' + $scope.control + '/nutrition_info.html',
            changeDish: $scope.project + '/' + $scope.control + '/change_dish.html',
            shareWithAdmin: $scope.project + '/' + $scope.control + '/share_with_admin.html',
            addMenuPlanning: $scope.project + '/' + $scope.control + '/add_menu_planning.html',
            meal: $scope.project + '/' + $scope.control + '/meal.html',
            mealOfDish: $scope.project + '/' + $scope.control + '/meal_of_dish.html',
            mealOfDishUnlock: $scope.project + '/' + $scope.control + '/meal_of_dish_unlock.html',
            addFood: $scope.project + '/' + $scope.control + '/add_food.html',
            addDish: $scope.project + '/' + $scope.control + '/add_dish.html',
            separateFood: $scope.project + '/' + $scope.control + '/separate_food.html',
            balance: $scope.project + '/' + $scope.control + '/balance.html',
            balanceMoney: $scope.project + '/' + $scope.control + '/balance_money.html',
            edit_number_children: $scope.project + '/' + $scope.control + '/edit_number_children.html',
            edit_tien_bo_tro: $scope.project + '/' + $scope.control + '/edit_tien_bo_tro.html',
            formCopy: $scope.project + '/' + $scope.control + '/copy.html',
            printForm: $scope.project + '/' + $scope.control + '/print-form.html',
        }
    };
    $scope.processData = processData;
    $scope.getTongluong = getTongluong;
    $scope.onChange_soluong = onChange_soluong;
    $scope.getTongTien1Nhom = getTongTien1Nhom;
    $scope.getTongTien = getTongTien;
    $scope.onSelectDish = onSelectDish;
    $scope.showAddDish = showAddDish;
    $scope.addDishApply = addDishApply;
    $scope.showAddFood = showAddFood;
    $scope.onSelectAddFood = onSelectAddFood;
    $scope.appendFoodToMeal = appendFoodToMeal;
    $scope.showFormTotal = showFormTotal;
    $scope.closeFormTotal = closeFormTotal;
    $scope.showChangeDish = showChangeDish;
    $scope.changeDishApply = changeDishApply;
    $scope.foodSelected = foodSelected;
    $scope.addFoodToTempDish = addFoodToTempDish;
    $scope.formEditSotreOfPoints = formEditSotreOfPoints;
    $scope.formEditTienbotroOfPoints = formEditTienbotroOfPoints;
    $scope.balanceShow = balanceShow;
    $scope.sumPayForGroup = sumPayForGroup;
    $scope.sumPayForAll = sumPayForAll;
    $scope.modifiedSotres = modifiedSotres;
    $scope.applySotres = applySotres;
    $scope.marketbillPrintForm = marketbillPrintForm;
    $scope.warehouseSelectedOnClick = warehouseSelectedOnClick;
    $scope.fn_copyForm = fn_copyForm;   /* Mở form sao chép cân đối khẩu phần trong phòng hoặc các điểm trường khác  */
    $scope.fn_selectAdjustPoint = fn_selectAdjustPoint;   /* Tải thực đơn của điểm trường đã chọn */
    $scope.initGrid = initGrid;   /* Khởi tạo grid  */
    $scope.onChange_food_name = onChange_food_name;   /* Sửa tên thực phẩm  */
    $scope.divide_pointThucmuatheodvt = divide_pointThucmuatheodvt;   /* Chia thực mua về từng điểm trường theo tỉ lệ trẻ  */
    $scope.isPointTogether = isPointTogether;   /*   Kiểm tra nhiều điêm trường dùng thực đơn chung   */
    $scope.onChange_thucmuatheodvts = onChange_thucmuatheodvts; /*  Lượng thực mua ở điêm trường thay đổi */
    $scope.selected = {};
    $scope.table = {
        rows: [],
        groupColNames: {
            soluong: {name: 'SL (ĐVT)', title: 'Thực mua cả nhóm theo đơn vị tính'},
            soluong_kg: {name: 'SL (KG)', title: 'Thực mua cả nhóm theo Kg'},
            dongia: {name: 'Đơn giá', title: 'Giá thực phẩm theo đơn vị tính'},
            thanhtien: {name: 'Thành tiền', title: 'Thành tiền cả nhóm nhóm'}
        }
    };
    /*
    * Kiểm tra điều kiện là đơn vị có nhiều điểm trường và lên thực đơn chung
    * */
    function isPointTogether() {
        var rs = false;
        if ($scope.school_point) {
            if (count($scope.school_point.points) > 1 && $scope.school_point.together === 1) {
                rs = true;
            }
        }
        return rs;
    };
    /*
    * Sự kiện thực mua ở điểm trường bị thay đối
    * */
    function onChange_thucmuatheodvts(food, point, calc) {
        food.thucmuatheodvt = 0;
        angular.forEach(food.thucmuatheodvts, function (val, point) {
            food.thucmuatheodvt = $['+'](food.thucmuatheodvt, val);
        });
        $scope.onChange_thucmuatheodvt(food, !calc, true);
    }
    /*
    * Tách thực mua theo đơn vị tính ra các điểm trường theo tỉ lệ sĩ số trẻ
    * */
    function divide_pointThucmuatheodvt(food, group_adjust) {
        if ($scope.isPointTogether()) {
            var thucmuc = Number(food.thucmuatheodvt);
            food.thucmuatheodvts = {};
            var tong_tre = 0;
            angular.forEach(group_adjust.row.sotres, function (val, point) {
                tong_tre += val;
            });
            var thucmua = 0;
            var p_min = {point: 1, value: 10000};
            var p_max = {point: 1, value: 0};
            angular.forEach(group_adjust.row.sotres, function (val, point) {
                if (val == 0 || thucmuc == 0) {
                    food.thucmuatheodvts[point] = 0;
                    return;
                }
                var tile = val/tong_tre;
                food.thucmuatheodvts[point] = $scope.safeRound($['*'](tile, thucmuc));
                thucmua = $['+'](thucmua, food.thucmuatheodvts[point]);
                if (p_min.value > food.thucmuatheodvts[point]) {
                    p_min.point = point;
                    p_min.value = food.thucmuatheodvts[point];
                }
                if (p_max.value < food.thucmuatheodvts[point]) {
                    p_max.point = point;
                    p_max.value = food.thucmuatheodvts[point];
                }
            });
            var so_du = $['-'](thucmuc, thucmua);
            if (so_du > 0) {
                food.thucmuatheodvts[p_min.point] = $['+'](food.thucmuatheodvts[p_min.point], so_du);
            } else if (so_du < 0){
                food.thucmuatheodvts[p_max.point] = $['+'](food.thucmuatheodvts[p_max.point], so_du);
            }
        }
    };
    /*
    * Sửa tên đi chợ
    * */
    function onChange_food_name(food) {
        for (var f of food.foods) {
            f.name_old = f.name;
            f.name = food.name;
        }
    };
    /*  Khi kích vào select chọn kho */
    /*  Việc chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/
    function warehouseSelectedOnClick(meal) {
        meal.selected = !meal.selected;
        var check_not_null = true;
        angular.forEach($scope.row.meal_selection, function(item, index){
            if(item.selected){
                check_not_null = false;
            }
        });
        if(check_not_null){
            meal.selected = true;
        }else{
            $scope.totalCalculator();
        }
    };
    function fn_copyForm (points) {
        $scope.copy = {
            list: points,
            selected: {}
        };
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/'+self.module,
            action:'',
            title:'Sao chép thực đơn',
            size: size.normal,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.controller.templates.formCopy
        });
    };
    function fn_selectAdjustPoint() {
        $scope.loadDataOfDay(null, $scope.copy.selected);
    }
    function initGrid (month) {
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,$scope.control,'list'];
        $.dm_datagrid.init(
            urls.join('/'),
            $scope.control, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    { field:'ck', checkbox: true },
                    { title:'Ngày', field:'date', width: 90},
                    { title:'Nhóm trẻ', field:'group_names', width:250},
                    { title:'Tên thực đơn theo nhóm trẻ', field:'menu_planning_names', width: 250 },
                    { title:'Số trẻ', field:'set', width: 70 },
                    { title:'Tiền ăn', field:'money', width: 120 },
                    { title:'', field:'school_point', width: 20, formatter: function (value, row) {
                            return '<div id="datagrid-view-' + [row.day, row.month, row.schoolyear].join('-') + '"></div>';
                        } },
                    { title:'Phiếu kê chợ', field:'marketbill_unit_id', width:70, formatter: function(value, row){
                            return '<div id="datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-') + '"></div>';
                        } }
                ]
            ],
            {
                onDblClickRow: function(rowIndex, rowData) {
                    $scope.selected.date = rowData.date;
                    $scope.addjustForm($scope.selected.date);
                }, onSelect: function(index, row) {
                    $scope.$apply(function ($scope) {
                        $scope.selected.date = row.date;
                    });
                }, onUnselect: function (index,row) {
                    $scope.$apply(function ($scope) {
                        $scope.selected.date = $.menu_adjust.date;
                    });
                }, onLoadSuccess: function(data) {
                    setTimeout(function () {
                        angular.forEach(data.rows, function (row, ind) {
                            var btn = '<span class="glyphicon glyphicon-eye-open btn-over-green" title="Mở thực đơn" style="height: 14px; margin: 0 3px;" ng-click="addjustForm(\'' + row.date + '\')"></span>';
                            $('div#datagrid-view-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                            btn = '<span class="glyphicon glyphicon-pencil btn-over-green" title="Sửa phiếu kê chợ" style="height: 14px; margin: 0 3px;" ng-click="marketbillForm(\'' + row.date + '\')">Sửa</span>';
                            $('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                            if ($scope.isPointTogether() && !$scope.sys.configs.phieukecho_temple_point_old) {
                                btn = '<a href="' +
                                    $CFG.remote.base_url+'/report/' + $CFG.project+'/marketbill/pointsTogether?date=' + row.date +
                                    '&type=1"" target="_blank"><span class="glyphicon glyphicon-print btn-over-green" title="Hiển thị phiếu kê chợ" style="height: 14px; margin: 0 3px;">In</span></a>';
                                $('div#datagrid-marketbill-' + [row.day, row.month, row.schoolyear].join('-')).append($scope.compile(btn, $scope));
                            }
                        });
                    });
                },
                queryParams: {month: month},
                pageList: [31],
                pageSize: 31
            }
        );
    };
    /*
    * Mở pupup danh sách biểu in liên quan tới phiếu kê chợ; sổ kiểm thực 3 bước
    * */
    function marketbillPrintForm () {
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/'+self.module,
            action:'change_dish',
            title:'Sửa món ăn',
            size: 400,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.project + '/marketbill/list-print-preview.html',
            onShown: function(element, dialogRef){

            }
        });
    };
    /*Tổng tiền từng nhóm trẻ*/
    function sumPayForGroup (foods, group_id) {
        var rs = 0;
        angular.forEach(foods, function (food, food_id) {
            rs += $scope.getTongTien1Nhom(food.groups[group_id]);
        });
        return rs;
    };
    /*Tổng tiền tất cả các nhóm*/
    function sumPayForAll (foods) {
        var rs = 0;
        angular.forEach(foods, function (food, food_id) {
            rs += $scope.getTongTien(food);
        });
        return rs;
    };
    /*Hiển thị form xem thông tin hoặc thay đổi món ăn khác*/
    function showChangeDish (meal, dish_old){
        $scope.selected.dish = dish_old;
        $scope.selected.meal = meal;
        var self = $.menu_adjust;
        $scope.foodAddeds = {};
        angular.forEach(dish_old.ingredient, function (food, index) {
            if (!food.quantity) {
                food.quantity = food.quantity_edit;
            }
            food.deleted = false;
        });
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/'+self.module,
            action:'change_dish',
            title:'Sửa món ăn',
            size: size.wide,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.controller.templates.changeDish,
            onShown: function(element, dialogRef){

            }
        });
    };
    function showFormTotal () {
        $scope.selected.food = undefined;

        $.dm_datagrid.showEditForm({
            module: $CFG.project+'/menu_adjust',
            action:'',
            title:'Cân đối khẩu phần',
            scope: $scope,
            size: size.wide,
            showButton: false,
            fullScreen: true,
            askBeforeClose: false,
            content: $scope.project+'/'+$scope.control+'/total.html',
            onShown: function (dialog) {
                $scope.processData($scope.menu_adjust.group_adjusts);
            }
        });
    };

    function closeFormTotal () {
        if($scope.dialog) {
            $scope.dialog.close();
        }else{
            dialogClose();
        }
    };
    /*Chọn thực phẩm để thêm thành món*/
    function onSelectAddFood (food_select) {
        if(food_select) {
            var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
            var data = {async: true,id: food_select.id};
            process(url, data, function(resp) {
                if(!resp) return;
                var food = resp[0];
                $scope.$apply(function(){
                    food.dish_name = food.name.replace('Gạo','Cơm').replace('gạo','cơm');
                    food.quantity = 10;
                    if($scope.inventory[$scope.selected.meal.warehouse_id]){
                        if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id]){
                            if($scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].value>0) {
                                food.price = $scope.inventory[$scope.selected.meal.warehouse_id][food.food_id].price;
                            }
                        }
                    }
                    $scope.selected.food_add = food;
                });
            },function(){}, false);
        }
    };
    /*Tạo thực phẩm thành món mới theo tên thực phẩm*/
    function appendFoodToMeal (food, meal){
        var dish = {
            id: 'food_id_'+food.food_id,
            name: food.dish_name,
            tcp: food.tcp,
            ingredient: {}
        }
        food.quantity || (food.quantity = 0);
        food.quantity = Number(food.quantity);
        dish.ingredient[food.food_id] = food;
        if(!meal.dishes[dish.id]){
            $scope.addFoodFromDish(meal,dish);
            $scope.datagrid.data = {};
            angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
                angular.forEach(meal_.dishes, function(dish_,ind){
                    $scope.addFoodFromDish(meal_,dish_,true);
                })
            });
            $scope.selected.group_adjust.data = $scope.datagrid.data;
            angular.forEach($scope.datagrid.data, function(foods,warehouse_id){
                angular.forEach(foods, function(food,food_id){
                    $scope.onChange_luong1tre(food);
                });
            });
            $scope.totalCalculator();
        }else{

        }
        $scope.selected.food_tmp = undefined;
        $scope.selected.food_add = undefined;
    }
    /*  Mở form thêm thực phẩm */
    function showAddFood (meal){
        $scope.selected.meal = meal;
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/'+self.module,
            action:'add_food',
            title:'Thêm mới',
            size: size.small,
            fullScreen: false,
            showButton: false,
            scope: $scope,
            content: $scope.controller.templates.addFood,
            onShown: function(element, dialogRef){

            }
        });
    };
    function foodSelected (food_selected){
        /*Chua bit làm gi*/
    }
    /*Mở form nhập số trẻ từng điểm trường*/
    function formEditSotreOfPoints () {
        $scope.tmp || ($scope.tmp = {});
        $scope.tmp.thucmuaCalc = true;
        $scope.tmp.sotres = {};
        angular.forEach($scope.row.sotres, function (val, point) {
            $scope.tmp.sotres[point] = val;
        });
        $.dm_datagrid.showEditForm(
            {
                module: $CFG.project+'/menu_adjust',
                action:'edit',
                title:'Chỉnh sửa',
                size: 450,
                showButton: false,
                draggable: true,
                scope: $scope,
                content: $scope.controller.templates.edit_number_children,
                onShown: function(element) {
                }
            }
        );
    };
    /*
    * Cập nhật sự thay đổi số trẻ từng điểm trường
    * */
    function applySotres() {
        $scope.row.sotre_old = $scope.row.sotre;
        $scope.row.sotre = 0;
        angular.forEach($scope.tmp.sotres, function (val, point) {
            $scope.row.sotre = $['+']($scope.row.sotre, val);
            val = Number(val);
            $scope.row.sotres[point] = val;
        });
        // console.log($scope.tmp.thucmuaCalc, typeof $scope.tmp.thucmuaCalc, $scope.row.sotres, $scope.tmp.sotres);
        $scope.onChangeSotre($scope.row, $scope.tmp.thucmuaCalc);
    }
    /*
    * Kiểm tra số trẻ từng điểm trường đã bị thay đổi chưa
    * */
    function modifiedSotres() {
        var rs = false;
        angular.forEach($scope.tmp.sotres, function (val, point) {
            if (val != $scope.row.sotres[point]) {
                rs = true;
            }
        });
        return rs;
    }
    /*Mở form nhập tiền bổ trợ từng điểm trường*/
    function formEditTienbotroOfPoints (){
        $.dm_datagrid.showEditForm(
            {
                module: $CFG.project+'/menu_adjust',
                action:'edit',
                title:'Chỉnh sửa',
                size: size.small,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.edit_tien_bo_tro,
                onShown: function(element) {
                }
            }
        );
    };
    /*Mở form cân đối tự động*/
    function balanceShow (){
        var self = $.menu_adjust;
        $.dm_datagrid.showAddForm({
                module: $CFG.project+'/'+ $scope.control,
                action:'balance',
                title:'Cân đối',
                size: size.wide,
                fullScreen: true,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.balance,
                onShown: function(element,dialogRef){
                    setTimeout(function () {
                        $scope.$apply(function () {
                            $.balance.init($scope);
                        });
                    });
                }
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
    };
    /*Chọn thực phẩm vào món*/
    function addFoodToTempDish (food_selected){
        if(food_selected) {
            var url = $CFG.remote.base_url+'/doing/dinhduong/menu_adjust/foods';
            var data = {async: true,id: food_selected.id};
            process(url, data, function(resp) {
                if(!resp) return;
                var food = resp[0];
                $scope.$apply(function(){
                    var id = food.food_id;
                    // food.quantity = 0;
                    // $scope.menu_adjust.food = undefined;
                    food.quantity = 10;
                    $scope.foodAddeds[id] = food;
                    // scope.selected.dish.ingredient[id] = food;
                    // $('#food').combobox('clear');
                    // $scope.menu_adjust.dish_selected = null;
                    $scope.selected.foodAdded = undefined;
                });
            },function(){}, false);
        }
    };
    /*  Mở form thêm món ăn */
    function showAddDish (meal){
        var meals = $scope.selected.group_adjust.meals;
        var group_id = $scope.selected.group_adjust.group_id;
        $scope.foodAddeds = {};
        $scope.selected.dish_tmp = undefined;
        $scope.selected.dish = undefined;
        $scope.selected.meal = meal;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/menu_adjust',
                action:'add_dish',
                title:'Thêm mới',
                size: size.wide,
                fullScreen: false,
                showButton: false,
                scope: $scope,
                content: $scope.controller.templates.addDish,
                onShown: function(element, dialogRef) {
                    $scope.selected.dish = {};
                    var arr_meals = Object.values(meals);
                    $.dm_datagrid.combobox('meal', arr_meals,{
                        valueField: 'define',
                        textField: 'name',
                        value: meal.define,
                        height: 24,
                        panelHeight: 'auto',
                        mode: 'local',
                        onSelect: function(meal, element) {
                            $.menu_adjust.combobox_load_dish($scope, meal, group_id);
                        },
                        queryParams: {},
                        width: 200
                    });
                }
            },
            function(resp){
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
                    $("#tbl_" + self.module).datagrid('reload');
                }
            }
        );
    };
    /*Cập nhật lại món sau khi sửa vào thực đơn*/
    function changeDishApply (meal, dish_old){
        dish_old || (dish_old = $scope.selected.dish);
        dish_old.ingredient = Object.assign(dish_old.ingredient, $scope.foodAddeds);
        var dish_new = dish_old;
        /*Xóa món ăn cũ đi*/
        /*Xóa thực phẩm đã bị xóa trong món ăn*/
        var tmp_foods = {};
        angular.forEach(dish_new.ingredient, function(food, food_id){
            if(food && !food.deleted) {
                if (typeof food.quantity_edit ==='undefined') {
                    food.quantity_edit = Number(food.quantity);
                }
                tmp_foods[food.food_id] = clone(food);
            }
        });
        dish_new.ingredient = tmp_foods;
        /*Thêm món ăn mới vào*/
        meal || (meal = $scope.selected.meal);
        meal.dishes[dish_new.id] = dish_new;
        /*
        * Chỗ này dữ liệu cũ để dish.id + ' ' nên bị lặp món -> cập nhật lại món bị nhảy gấp đôi lượng
        * Chuẩn hóa lại ds món teo id gốc;
        * */
        var dishes = {};
        angular.forEach(meal.dishes, function (dish, dish_id) {
            dishes[dish.id] = dish;
        });
        meal.dishes = dishes;
        $scope.datagrid.data = {};
        angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
            angular.forEach(meal_.dishes, function(dish_,ind){
                $scope.addFoodFromDish(meal_, dish_, true);
            })
        });
        $scope.selected.group_adjust.data = $scope.datagrid.data;
        console.log('KQ',$scope.selected.group_adjust.data);
        dialogClose();
        $scope.totalCalculator();
    };

    function addDishApply (){
        var dish = $scope.selected.dish;
        var meal_define = $scope.selected.meal.define;
        dish.ingredient = Object.assign(dish.ingredient, $scope.foodAddeds);
        /*Cập nhật món mới vào thực đơn*/
        $scope.addFoodFromDish($scope.selected.group_adjust.meals[meal_define], dish);
        $scope.datagrid.data = {};
        angular.forEach($scope.selected.group_adjust.meals, function(meal_,meal_define){
            angular.forEach(meal_.dishes, function(dish_,ind){
                $scope.addFoodFromDish(meal_,dish_,true);
            });
        });
        $scope.selected.group_adjust.data = $scope.datagrid.data;
        angular.forEach($scope.datagrid.data, function(foods, warehouse_id){
            angular.forEach(foods, function(food, food_id){
                $scope.onChange_luong1tre(food);
            });
        });
        $scope.totalCalculator();
        dialogClose();
    }
    function onChange_soluong (group) {
        group.food.thucmuatheodvt = group.soluong;
        $scope.onChange_thucmuatheodvt(group.food);
    }
    function getTongTien(food) {
        var rs = 0;
        angular.forEach(food.groups, function (group, group_id) {
            var tongnhom = getTongTien1Nhom(group);
            rs = $['+'](rs, tongnhom);
        });
        return rs;
    }
    function getTongTien1Nhom(group) {
        var rs = 0;
        if(group) {
            if (group.food.exports == undefined) {
                rs = $['+'](rs, group.thanhtien);
            } else {
                angular.forEach(group.food.exports, function (exp, food_id_price) {
                    rs = $['+'](rs, $['*'](exp.quantity, exp.price));
                });
            }
        }
        return rs;
    }
    function getTongluong(food) {
        var rs = 0;
        // if(console.log(food))
        angular.forEach(food.groups, function (group, group_key) {
            rs += parseFloat(group.soluong);
        });
        return rs;
    }
    /*
    * xử lý dữ liệu gộp form
    * dataAll = selected.group_adjusts : các thực đơn
    * */
    function processData (dataAll) {
        $scope.table.rows = {};
        $scope.table.groups = {};
        var fds = {};
        angular.forEach(dataAll, function (group, group_id) {
            var group_key = group.menu_plannings[0].group_key;
            $scope.table.groups[group_key] = {
                name: group.name,
                title: ''
            };
            angular.forEach(group.menu_plannings[0].data, function (kho, meal_key) {
                var warehouse_id = 2;
                if (meal_key == 1) {
                    warehouse_id = group.menu_plannings[0].meals['buasang'].warehouse_id;
                } else if (meal_key == 3) {
                    warehouse_id = group.menu_plannings[0].meals['buatoi'].warehouse_id;
                }
                fds[meal_key] || (fds[meal_key] = {});
                angular.forEach(kho, function (food, food_id) {
                    fds[meal_key][food_id] || (fds[meal_key][food_id] = {
                        name: food.name,
                        price: food.price,
                        groups: {}
                    });
                    fds[meal_key][food_id].groups[group_key] || (fds[meal_key][food_id].groups[group_key] = {
                        soluong: food.thucmuatheodvt,
                        soluong_kg: food.thucmua1nhom,
                        thanhtien: food.thucmuatheodvt * food.price,
                        food: food
                    });
                });
            });
        });
        $scope.table.rows = fds;
    };
    function onSelectDish (row) {
        process($CFG.remote.base_url + '/doing/dinhduong/menu_adjust/dishs', {id: row.id, async: true}, function (resp) {
            if (resp) {
                row = resp;
                row.quantity_edit = row.quantity;
                $scope.menu_planning.ignore_ids = '';
                var kt = 0;
                angular.forEach(row.ingredient, function (food, key) {
                    food.name_edit || (food.name_edit = food.name);
                    $scope.menu_planning.ignore_ids = $scope.menu_planning.ignore_ids + ',' + key;
                    if (food.quantities) {
                        if (count(food.quantities) == 0) {
                            food.quantities = {};
                        }
                        if (food.quantities[$scope.row.group_id] > 0) {
                            food.quantity = food.quantities[$scope.row.group_id];
                        }
                    }
                    if (food.quantity > 0) kt++;
                });
                if (!kt) {
                    if (!count(row.ingredient)) {
                        alert('Món ăn không có thực phẩm');
                    }
                    if (!kt) {
                        alert('Món ăn không có thực phẩm nào có lượng dành cho nhóm trẻ đang chọn.\n Hãy kiểm tra lại lượng của món ăn theo nhóm trẻ.');
                    }
                }
                /*Kiểm tra thực phẩm có thêm ngoài không thì đẩy lại vào*/
                // if (dish) { /*Nếu là thêm mới thì không có món được truyền vào*/
                //     if (dish.id === row.id) {
                //         angular.forEach(dish.ingredient, function (food, food_id) {
                //             food.name_edit = food.name;
                //             food.deleted = false;
                //             if (!row.ingredient[food_id]) {
                //                 row.ingredient[food_id] = food;
                //             }
                //         });
                //     }
                // $('#dish').combobox('clear');
                $scope.$apply(function () {
                    $scope.selected.dish = row;
                });
            }
        }, function () {
        }, false);
    }
}]);
angular_app.controller('menu_planningController', ['$scope','$routeParams','$compile','MyCache',function($scope,$routeParams,$compile,MyCache,$filter,$cookies){
    $scope.project = 'dinhduong';
    $scope.control = 'menu_planning';
    $scope.systemConfig = {};
    $scope.systemConfig.module = 'menu_planning';
    $scope.warehouseSelectedOnClick = warehouseSelectedOnClick;
    $scope.onChange_food_name = onChange_food_name;
    /*  Khi kích vào select chọn kho */
    /*  Việc chọn kho bắt buộc phải có ít nhất 1 kho được chọn*/
    function warehouseSelectedOnClick(meal) {
        meal.selected = !meal.selected;
        var check_not_null = true;
        angular.forEach($scope.row.meal_selection, function(item, index){
            if(item.selected){
                check_not_null = false;
            }
        });
        if(check_not_null){
            meal.selected = true;
        }else{
            $scope.totalCalculator();
        }
    };
    /*
    * Sửa tên đi chợ
    * */
    function onChange_food_name(food) {
        for (var f of food.foods) {
            f.name_old = f.name;
            f.name = food.name;
        }
    };
}]);
//# sourceMappingURL=app.min.js.map
