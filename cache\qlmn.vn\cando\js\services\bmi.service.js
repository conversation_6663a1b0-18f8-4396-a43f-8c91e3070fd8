(function BmiService(app, $CFG) {
    'use strict';

    app.service('BmiService', BmiService);

    function BmiService(BMI_LABELS) {
        var self = this;

        var BMI_FOR_WEIGHT = $CFG.metaData.weights;
        var BMI_FOR_HEIGHT = $CFG.metaData.heights;
        var BMI_FOR_AGE = $CFG.metaData.ages;
        var BMI_LABELS_RESULT = $CFG.metaData.labels;

        function getResultFromId(student, type) {
            switch (type) {
                case 'weight':
                    return _.find(BMI_LABELS_RESULT, {id: student.weight_result});
                case 'height':
                    return _.find(BMI_LABELS_RESULT, {id: student.height_result});
                case 'bmi':
                case 'conclusion':
                    return _.find(BMI_LABELS_RESULT, {id: student.bmi_result});
                default:
                    throw new Error('Invalid parameter');
            }
        }

        self.getBmiResult = function getBmiResult(student,check_zscore) {
            var result = _.find(BMI_FOR_AGE, {gender: student.gender, month: student.month_old});

            if (!result) {
                return 0;
            }

            if(student.height < 0 || student.weight < 0 || !student.height || !student.weight) {
                return 0;
            }

            

            /*if(student.weight_result == BMI_LABELS.BMI_FOR_WEIGHT.NORMAL && student.height_result == BMI_LABELS.BMI_FOR_HEIGHT.NORMAL)
            {
                return BMI_LABELS.BMI_FOR_AGE.NORMAL;
            }*/

            if(student.month_old<=60 && student.month_old >= 24 && check_zscore) {
                if (student.bmi > 3) {
                return BMI_LABELS.BMI_FOR_AGE.OBESITY;
                }
                if (student.bmi > 2 && student.bmi <= 3) {
                    return BMI_LABELS.BMI_FOR_AGE.OVERWEIGHT;
                }
                if (student.bmi >= -2 && student.bmi <= 2) {
                    return BMI_LABELS.BMI_FOR_AGE.NORMAL;
                }
                if(student.bmi >= -3 && student.bmi < -2) {
                    return BMI_LABELS.BMI_FOR_AGE.THINNESS;
                }
                if(student.bmi < -3) {
                    return BMI_LABELS.BMI_FOR_AGE.SEVERE_THINNESS;
                }
            }
            else {
                
                if( student.month_old > 60 ) {
                    if (student.bmi > result.SD2) {
                        return BMI_LABELS.BMI_FOR_AGE.OBESITY;
                    }
                    if (student.bmi > result.SD1 && student.bmi <= result.SD2) {
                        return BMI_LABELS.BMI_FOR_AGE.OVERWEIGHT;
                    }
                    if (student.bmi >= result.SD2neg && student.bmi <= result.SD1) {
                        return BMI_LABELS.BMI_FOR_AGE.NORMAL;
                    }
                    if (student.bmi < result.SD2neg && student.bmi >= result.SD3neg) {
                        return BMI_LABELS.BMI_FOR_AGE.THINNESS;
                    }
                
                    if (student.bmi < result.SD3neg && student.bmi > 0) {
                        return BMI_LABELS.BMI_FOR_AGE.SEVERE_THINNESS;
                    }
                }
                else {
                    if (student.bmi > result.SD3) {
                        return BMI_LABELS.BMI_FOR_AGE.OBESITY;
                    }
                    if (student.bmi > result.SD2 && student.bmi <= result.SD3) {
                        return BMI_LABELS.BMI_FOR_AGE.OVERWEIGHT;
                    }
                    if (student.bmi >= result.SD2neg && student.bmi <= result.SD2) {
                        return BMI_LABELS.BMI_FOR_AGE.NORMAL;
                    }
                    if (student.bmi < result.SD2neg && student.bmi >= result.SD3neg) {
                        return BMI_LABELS.BMI_FOR_AGE.THINNESS;
                    }
    
                    if (student.bmi < result.SD3neg && student.bmi > 0) {
                        return BMI_LABELS.BMI_FOR_AGE.SEVERE_THINNESS;
                    }
                }
            }


            return 0;
        };

        self.getWeightResult = function getWeightResult(student) {
            var result = _.find(BMI_FOR_WEIGHT, {gender: student.gender, month: student.month_old});

            if (!result) {
                return 0;
            }

            if (student.weight > result.SD3) {
                return BMI_LABELS.BMI_FOR_WEIGHT.OBESITY;
            }

            if (student.weight > result.SD2 && student.weight <= result.SD3) {
                return BMI_LABELS.BMI_FOR_WEIGHT.OVERWEIGHT;
            }

            if (student.weight >= result.SD2neg && student.weight <= result.SD2) {
                return BMI_LABELS.BMI_FOR_WEIGHT.NORMAL;
            }

            if (student.weight < result.SD2neg && student.weight >= result.SD3neg) {
                return BMI_LABELS.BMI_FOR_WEIGHT.THINNESS;
            }

            if (student.weight < result.SD3neg && student.weight > 0) {
                return BMI_LABELS.BMI_FOR_WEIGHT.SEVERE_THINNESS;
            }

            return 0;
        };

        self.getHeightResult = function getHeightResult(student) {
            var result = _.find(BMI_FOR_HEIGHT, {gender: student.gender, month: student.month_old});

            if (!result) {
                return 0;
            }

            if (student.height > result.SD3) {
                return BMI_LABELS.BMI_FOR_HEIGHT.OVER_HEIGHT2;
            }

            if (student.height > result.SD2 && student.height <= result.SD3) {
                return BMI_LABELS.BMI_FOR_HEIGHT.OVER_HEIGHT1;
            }

            if (student.height >= result.SD2neg && student.height <= result.SD2) {
                return BMI_LABELS.BMI_FOR_HEIGHT.NORMAL;
            }
            if (student.height < result.SD2neg && student.height >= result.SD3neg) {
                return BMI_LABELS.BMI_FOR_HEIGHT.THINNESS;
            }
            if (student.height < result.SD3neg && student.height > 0) {
                return BMI_LABELS.BMI_FOR_HEIGHT.SEVERE_THINNESS;
            }

            return 0;
        };

        self.getResultCodeFromId = function getResultCodeFromId(student, type) {
            return _.get(getResultFromId(student, type), 'code', '');
        };

        self.getResultTextFromId = function getResultTextFromId(student, type) {
            return _.get(getResultFromId(student, type), 'value', '');
        };

        self.getResultCodeFromLabelId = function getResultCodeFromLabelId(id) {
            return _.get(
                _.find(BMI_LABELS_RESULT, {id: id}),
                'code',
                ''
            );
        };

        return self;
    }

    BmiService.$inject = [
        'BMI_LABELS',
    ];

})(window.angular_app, window.$CFG);
