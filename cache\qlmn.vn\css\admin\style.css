
.header-title {
	float: left;
	font-size: 14px;
	font-weight: bold;
	height: 100%;
}
.dropdown-menu{
	margin: 0;
}
.fcn-main-content-after .dropdown-menu{
	top: unset;
    bottom: 100%;
}
.btn-primary{
	background-color: #95cdfd;
	color: #3a3a6b;
}
.btn-primary span{
	margin-right: 4px;
}
.btn.btn-primary{
    border: 1px solid #86b4bc;
}
.btn-group .btn.btn-primary{
    padding: 4px 8px;
    border-radius: 25px;
}
.btn-group{
	float: right;
	padding-top: 3px;
	padding-right: 3px;
}
.function-kh-ct button.btn-primary:hover{
	background-color: #95cdfd;
	color: #dc4971;
	text-shadow: 2px 2px 2px rgba(122, 122, 122, 1);
}
.l-btn{
	/*background: linear-gradient(to bottom,#b4f2f2 0,#76d3de 100%);*/
}
.title-kh-ct{
	height: auto;
	display: inline-table;
	width: 100%;
}
.option-schoolyear a span{
	opacity: 0;
}
.option-schoolyear.active a span{
	opacity: 1;
}
.option-schoolyear.active a{
	color: #0eadad;
}
.school-year-kh .option-schoolyear:hover a{
	color: #dc4971;
	text-shadow: 2px 2px 2px rgba(122, 122, 122, 1);
}
.option-schoolyear:hover a span{
	opacity: 1;
}
.option-schoolyear.active:hover a{
	color: #0eadad;
	text-shadow: none;
}
/*css trang con*/
.menu-name-cha{
	background: url('images/tit-bg-menu-con.png') no-repeat;
    line-height: 40px;
    /*padding: 0px 30px 0px 15px;*/
    padding-right: 40px;
    background-size: 100% 100%;
    color: #FFF
}
.menu-name-cha a{
	float: left;
	padding-top: 10px !important;
}
.menu-name-cha img{
	width: 20px;
	float: left;
}
.menu-name-cha p{
	float: left;
	margin: 0px !important
	/*white-space: nowrap;*/
}
.menu-name-con{
	padding-top: 3px;
}
.menu-name-con a{
	padding: 5px 10px !important;
}
.menu-name-con a:hover{
	background: rgba(0, 0, 0, 0) !important;
	color: #dc4971;
	text-shadow: 2px 2px 2px rgba(122, 122, 122, 1);

}

.datagrid-toolbar{
	padding:0px;
}
#active{
	
}
#active a{
	background: url(images/bg-select.png) no-repeat !important;
	background-size: 200% 100% !important;
	background-position: center center !important;
	border-radius: 20px; 
	color: red;
}
.style-menu-main{
	left: 100%;
	top: 0;
	/*display: block;*/
}
.icon-hover img{
	width: 20px !important;
}
.icon-hover{
	/*display: none;*/
	opacity: 0.0;
	position: absolute;
    z-index: 4;
    /*top: -21px;*/
    top: 0px;
    /*width: 20px !important;*/
    height: 20px;
    margin: auto;
    margin-top: 0px;
    /*margin-left: 40%;*/
    transition: all 0.2s ease-in-out;
    transition-property: top, margin-top, opacity;
  	transition-duration: 0.1s, 0.1s, 0.2s;
  	transition-delay: 0s, 0.1s, 0s;
}
.menu-name-con:hover .icon-hover{
	opacity: 1.0;
	top: -30px;
	margin-top: 20px;
	/*animation-name: slideup;*/
  	/*animation-duration: 3s;
  	animation-timing-function: ease-out;*/
}
@-webkit-keyframes swinging{
    0%{-webkit-transform: rotate(10deg);}
    50%{-webkit-transform: rotate(-5deg)}
    100%{-webkit-transform: rotate(10deg);}
}
 
@keyframes swinging{
    0%{transform: rotate(10deg);}
    50%{transform: rotate(-5deg)}
    100%{transform: rotate(10deg);}
}
.header-title .open{
	color: red;
}
.icon-hover img{
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
    -webkit-animation: swinging 0.2s ease-in-out forwards infinite;
    animation: swinging 0.2s ease-in-out forwards infinite;
}
.menu-main-option a:hover{
	background: rgba(0, 0, 0, 0) !important;
	color: red;
}
.menu-main-option ul{
	background: #c7d83c;
    border-radius: 0px;
    border: 0px;
}
.menu-main-option ul li{
	padding: 5px 10px;
	color: #FFF;
}
.menu-main-option ul li a{
	color: #FFF;
}
.menu-main-option .open a{
	color: #333;
}
.style-menu-main{
	/*display: block;*/
	background: #FFF !important;
}
.style-menu-main li{
	position: relative;
	margin-left: 20px;
	color: black;
	border-left: 3px solid #c5d63a;
}
.style-menu-main li a{
	color: #333 !important;
}
.style-menu-main li a:hover{
	background :rgba(0, 0, 0, 0) !important;
	border-radius: 0px !important;
	color: #c5d63a !important; 
}
.circle-menu{
	border: 1px solid #c5d63a;
    position: absolute;
    top: 13px;
    left: -7px;
    width: 10px;
    height: 10px;
    background: #FFF;
    padding: 5px;
    -moz-border-radius: 100px;
    -webkit-border-radius: 100px;
    border-radius: 100px
}
.style-menu-main li:hover .circle-menu{
	background: #567800;
}
.modal-header{
	padding: 7px;
}
.bootstrap-dialog-header a.btn-dialog-fullscreen{
	color: #c2ced2;
	font-size: 13px;
	float: right;
	margin-top: 5px;
}
.bootstrap-dialog-header a.btn-dialog-fullscreen:hover{
	color: #dc4971;
	text-shadow: 2px 2px 2px rgba(122, 122, 122, 1);
}
.modal-dialog.full-screen{
	width: 100%;
}
.bootstrap-dialog .btn-dialog-fullscreen .glyphicon-resize-small{
	display: none;
}
.modal-dialog.full-screen .btn-dialog-fullscreen .glyphicon-resize-small{
	display: block;
}
.modal-dialog.full-screen .btn-dialog-fullscreen .glyphicon-fullscreen{
	display: none;
}
.modal-header .close {
    padding: 0 5px;
    font-size: 30px;
}
.bootstrap-dialog-title{
	padding: 2px;
}
.form-horizontal .form-group {
    margin-right: 0px;
    margin-left: 0px;
}
/*norm css*/
.control-label-norm{
	text-align: left !important;
}
.easyui-accordion .panel{
	margin-bottom: 1px;
} 
label.valid:after {
	content: " (*)"
}
input.valid-star:after {
	content: " (*)";
}
.panel.window.messager-window{
    background: #84b5f1;
}
.panel-heading {
    padding: 0px;
    height: 37px;
}
.panel-heading .panel-title{
    padding: 10px;
    cursor: pointer;
}
.collapse.in {
    padding-top: 10px;
}
.valid-red{
	color: red;
}
.valid-i{
	font-style: italic;
}
.header-search{
	display: inline-flex;
	width: 100%;
	background-color: #f5f5db;
	padding: 5px;
}
.hover-pointer,.panel-body > .combobox-item{
	cursor: pointer;
}

.view-content {
    display: inherit;
    height: 100%;
}

.datagrid-header .datagrid-cell span {
    font-size: 12px;
    font-weight: bold;
    text-shadow: 2px 2px 2px rgba(142, 142, 142, 1);
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    background-color: #f9f9f9;
}
/*//////////////////////////////////////////////////////*/

html,body{
    height: 100%;
	overflow: hidden;
}

*{
	margin:0px;
	padding:0px;
	outline: 0 !important;
}
input, textarea {
    /* margin: 0 3px; */
    padding: 3px 5px;
}
input, textarea, select{
	border: 1px solid #e28fa6;
    border-radius: 5px;
}
input:focus, textarea:focus{
	box-shadow: 0 0 5px #8fe2a6;
	border:1px solid #8fe2a6;
}

@font-face{
    font-family: 'fontRegular';
    src: url('../fonts/VNF-LOBSTER.TTF');
}
.top-header { width:100%; height:40px; }
.header { margin-bottom:2px; height: 100%;}
.menu-text { padding-left: 20px; }
.content { margin-bottom:10px; }
.popup_body { padding:15px; }
/*.form-horizontal { padding:0px; }*/
.form-horizontal .form-group { margin-left:0; margin-right:0; }
.form-horizontal .form-group .form-control { font-size:12px; height:35px; }
.form-group .control-label { padding-left:0; padding-right:20px; }
.form-control{padding:0 10px;}
.content [class^="col-"] {
    background-color: white;
    border: 1px solid rgba(86, 61, 124, 0.2);
    padding-bottom: 5px;
    padding-top: 5px;
}
.form-group {
    margin-bottom: 7px;
    padding: 5px 0;
}
form .panel {
    margin-bottom: 20px;
}
.container{
	position: relative;
    height: 100%;
    /*padding-top: 30px;*/
    /*padding-bottom: 10px;*/
}
body > .container{
	padding-bottom: 70px !important;
}
.main-menu{
	/*position: absolute;*/
	/*top: 0;*/
}
.container-data{
    height: 100%;
    position: relative;
    display: inline-block;
    width: 100%;
    /*padding-bottom:40px; */
}
.content [class^="col-"] input[type='checkbox'] {
    vertical-align: top;
}
.checkbox_highlight { background:blue; }
a:hover { text-decoration:none; }
code { white-space: normal; }
.fromEdit{padding-right:50px;}
.theodoi li{position: relative;}
.sub-menu-student{position: absolute;top:0px;display:none;}
.theodoi:hover .sub-menu-student{display:block;}
.ht_clone_top, .ht_clone_left, .ht_clone_corner { display: none !important; }
.handsontable td {
    line-height: 30px !important;
}
.handsontable th {
    text-align: left;
}
.ace-nav {
    height: 100%;
    margin: 0 !important;
}
.ace-nav > li {
    float: left;
    height: 28px;
    line-height: 23px;
    padding: 0;
    position: relative;
}
.nav > li > a {
    display: block;
    padding: 4px 15px;
    position: relative;
}
.ace-nav > li > a {
    color: #2B7DBC;
}
#tb {
    height: auto;
    padding: 5px 18px;
    text-align: right;
}

.layout-panel-center .panel-tool {
    right: 19px;
    width: auto;
}

input[type="checkbox"]{
    margin: 0;
}
.layout-panel-center .datagrid-header td, .layout-panel-center .datagrid-body td, .layout-panel-center .datagrid-footer td {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    margin: 0;
    padding: 0;
}
.layout-panel-west .datagrid-cell{
    padding: 0 8px;
}
.datagrid-htable, .datagrid-btable, .datagrid-ftable {
    border-collapse: separate;
    color: #222;
}
.datagrid-row { height: 30px; }
.datagrid-row-over td { background:#D0E5F5; color:#000; }
.datagrid-row-selected td { background:#f3f48d; color:#000; }
.tree-title {
    padding: 0 5px;
}
.panel-title {
    margin-left: 5px;
}
.l-btn-plain-selected, .l-btn-plain-selected:hover {
    background: none repeat scroll 0 0 #9cc8f7;
}
.panel.layout-panel.layout-panel-north {
    height: 100%;
}
{
    padding-top: 30px;
    position: relative;
}
.panel.datagrid{
    height: 100%;
    position: relative;
    /*padding-top: 30px;*/
}
.panel-body{
    padding: 0px;
}
.panel-header{
    /*position: absolute;*/
    top: 0;
    left: 0;
    width: 100%;
}
.panel-body.panel-body-noheader.layout-body {
    height: 100% !important;
	overflow: hidden !important;
}
.easyui-tabs.tabs-container {
    height: 100% !important;
}
.easyui-tabs.tabs-container {
    height: 100% !important;
    /*padding-bottom: 48px !important;*/
}
.tabs-panels .panel {
	height: 100% !important;
	width: 100% !important;
}
.tabs-panels {
	width: 100% !important;
    background: none repeat scroll 0 0 #efefef !important; 
    height: 97% !important;
}
/*.panel-body.panel-body-noheader.panel-body-noborder {
    height: 100% !important;
	overflow-y: auto !important;
}*/
#window-working{
	position: fixed; left:2px; z-index:9000; cursor: pointer; bottom: 2px;
	opacity: 0.5;
}
#window-working:hover{
	opacity: 1;
}
#window-working em{
	margin-right: 4px;
	font-size: 16px;
}
#window-working em:hover{
	color: #ff9c01;
	font-weight: bold;
	text-shadow: 0.1em 0.1em #333
}
.panel-body.panel-body-noheader.panel-body-noborder.panel-noscroll {
    width: 100% !important;
	height: 100% !important;
}
.datagrid-wrap.panel-body.panel-body-noheader.panel-body-noborder {
    height: 100% !important;
	width: 100% !important;
}
.panel, not(.panel.window){
	width: 100% !important;
}
.datagrid-pager.pagination > table {
    float: right;
}
/*change to buton showl0g error -bye daotc */
.messager-button .l-btn {
    width:100px;
}
.datagrid-group{
	background: #ccffbb;
}
table#frm,table.mform{
	width: 100%;
}
table#frm td,table.mform td{
	padding: 3px;
}
.btn_upload{
	padding: 0px 7px;
}
.fileinput-button {
    overflow: hidden;
    position: relative;
}
.fileinput-button label:hover {
    color: #ff9c01;
}
.btn{
	padding: 6px 6px;
}
text.in-recycle{
	color: #ff00ff !important;
}
.datagrid-toolbar.in-recycle{
	background: #ffffae !important;
}
text.office-uncake{
	color: #9f521f;
}
.panel-body {
    width: 100% !important;
}
.colgroup-module{
	box-shadow: 4px 0 3px -2px rgba(50, 50, 50, 0.75);
    height: 100% !important;
    padding: 0 5px;
}
/*Cho nay Huynn them vao ^_^ */
.tabs-header,
.tabs-tool {
  background:url('../images/nav_vb.png') no-repeat center center; 
  background-repeat: repeat-x;
  height: 40px;
}
.tabs {
    border-style: none;
    border-width: 0 0 1px;
    height: 35px !important;
    list-style-type: none;
    margin: 0;
    padding: 0 0 0 28px;
    width: 5000px;
    background-color: #77ab22;
    padding-top:2px;
}
.datagrid-header, .datagrid-td-rownumber {
    background-color: #77ab22;
}
.datagrid-header, .datagrid-header-inner{
    background-color: #dedede;
}
.datagrid-view1 .datagrid-header-row td:first-child {
    background-color: #77ab22;
5}
.l-btn-icon-left .l-btn-icon {
    left: 0;
}
.l-btn-icon-left .l-btn-text {
    margin: 0 0px 0 20px;
}
.datagrid-btable .l-btn-left {
    display: inline-block;
    height: 25px;
    margin: 0;
    overflow: hidden;
    padding: 0 0 0 5px;
    position: relative;
    vertical-align: middle;
    min-width: 25px;
}
.l-btn-icon-left .l-btn-text:empty {
    margin: 0;
}
.tabs-title {
    font-size: 13px;
}
.tabs-inner{
    height: 37px !important;
    line-height: 33px !important; 
}
.tabs-header {
    border-style: none;
}
.datagrid-header .datagrid-cell span {
    font-size: 11px;
}
.tabs li a.tabs-inner {
    background-color: inherit;
     /*background-color: #77ab22;*/
    border-style: none;
    color: #fafafa;
    font-weight: bold;
}
.tabs li a.tabs-inner:hover {
    /*background:url('../images/icons/bg_menu.png') no-repeat center center;
    background-repeat: repeat-x;*/
    height: 34px !important;
    border-radius: 5px 5px 0 0;
    /*border-style: none;*/
}
.tabs li.tabs-selected a.tabs-inner {
    border-radius: 5px 5px 0 0;
    text-shadow: 0.1em 0.1em 0.2em white;
    box-shadow: 0 -177px 64px -182px rgba(0, 0, 0, 0.75) inset;
    color: #404040;
    height: 34px !important;
    border: none;
}
.l-btn-icon {
    display: inline-block;
    font-size: 1px;
    height: 100%;
    line-height: 25px;
    margin-top: 0px;
    position: absolute;
    top: 0;
    width: 20px;
}
.datagrid-row:nth-child(2n+1) {
    background: #fff none repeat scroll 0 0;
}
.datagrid-row:nth-child(2n+2) {
    background: #eff none repeat scroll 0 0;
}
.datagrid-header-row .datagrid-cell > span {
    color: #fff;
    font-weight: bold;
    text-shadow: 0.1em 0.1em 0.2em black;
}
.datagrid-header-row > td {
    background: #aaa none repeat scroll 0 0;
}
tr.vb_unreaded{
    font-weight: bold;
}
tr.vb_meetdeadlines_reply{
    color: #ff44ff;
}
tr.vb_timeout_reply{
    color: #f00;
}
tr.vb_color_red{
    color: red !important;
}
.easyui-linkbutton.l-btn.l-btn-small.l-btn-plain.l-btn-disabled.l-btn-plain-disabled {
    display: none;
}
#menu ul li a span.tabs-note{
    color: red;
    padding-left: 3px;
}
#menu ul li a span.tabs-note > text:after{
    content: ')';
}
#menu ul li a span.tabs-note > text:before{
    content: '(';
}
div.vb-first-line{
    padding: 3px;
}
div.vb-first-line:before{
    content: ' * ';
}
.logo-icon{
    margin-top: 3px;
    margin-left: 7px;
    width: 35px;
    height: 20px;
    padding-top: 40px;
    float: left;
    background: url(../images/fly.png) no-repeat;
    background-size: 90%;
}
.h5-slogan{
    font-family: Verdana;
    font-size: 17px;
    color: #77ab22;
}
.h5-slogan a{
    color: #77ab22;
}
/*Tabs bootstrap*/
.nav-tabs { border-bottom: 2px solid #DDD; }
.nav-tabs > li.active > a, .nav-tabs > li.active > a:focus, .nav-tabs > li.active > a:hover { border-width: 0; }
.nav-tabs > li > a { border: none; color: #666; }
.nav-tabs > li.active > a, .nav-tabs > li > a:hover { border: none; color: #4285F4 !important; background: transparent; }
.nav-tabs > li > a::after { content: ""; background: #4285F4; height: 2px; position: absolute; width: 100%; left: 0px; bottom: -1px; transition: all 250ms ease 0s; transform: scale(0); }
.nav-tabs > li.active > a::after, .nav-tabs > li:hover > a::after { transform: scale(1); }
.tab-nav > li > a::after { background: #21527d none repeat scroll 0% 0%; color: #fff; }
/*.tab-pane { padding: 15px 0; }*/
/*.tab-content{padding:20px}*/
.card {background: #FFF none repeat scroll 0% 0%; box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3); margin-bottom: 30px; }
.number-school{
    width: 100%;
    display: inline-block;
}
#contentinput label,#contentinput input{
    margin-top: 8px;

}
.active-p{
    background: url('images/active-p.png') no-repeat;
    background-position: 11px;
    color: #297bcf;
    background-color: #FFF;
}
.function-project{
    width: 100%;
    display: inline-block;
}
.function-project h5{
    width: 40%;
    float: left;
    text-align: left;
}
.main-menu{
    background: #3a99e9;
    border-top: 3px solid #79c4f7;
}
.main-menu ul li a{
    color: #FFF;
}
.dropdown-menu li a{
    color: inherit !important;
}
.ace-nav-detail{
    background: #e2f0fc;
}
.ace-nav-detail a{
    color: #484a4b !important;
    padding: 2px 15px !important; 
}
.form-group-detail{
    margin: 0px;
    padding: 0px;
}
#active a{
    color: #f47f20 !important;
}
.glyphicon-detail{
    font-size: 10px;
    margin-right: 5px;
}
.function-project h5{
    color: #2e74dc;
}
.glyphicon-title{
    color: #dc2e42;
}
.color-orange{
    color: orange;
}