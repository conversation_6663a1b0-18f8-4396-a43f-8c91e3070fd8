(function DashboardController(app) {
    'use strict';

    app.controller('<PERSON>board<PERSON>ontroller', DashboardController);

    /**
     * @property {Object} studentBmiForm
     */
    function DashboardController($scope, $http, CourseService, CommonService, AuthService, ACTIVE_MONTHS, StudentService, BmiService, $uibModal) {
        var vm = this;

        $scope.sys.module.title = 'Cân đo học sinh';
        $scope.api_url = $scope.$CFG.remote.base_url;
        $scope.student = {};
        vm.filters = {
            keyword: $CFG.keyword || '',
            month: $CFG.active_month || 0,
            school_year: $CFG.active_year || (new Date()).getFullYear(),
            course_id: $CFG.active_course_id || 0,
            malnutrition_last_month: $CFG.active_malnutrition || false,
            no_bmi_sdd_weight_rule: $CFG.no_bmi_sdd_weight_rule ? true : false,
            no_bmi_under60_month_rule: $CFG.no_bmi_under60_month_rule ? true : false,
            no_is_not_weight_height: $CFG.no_is_not_weight_height ? true : false,
            get_weight_height_not_normal: $CFG.get_weight_height_not_normal ? true : false,
            show_modal_report_multi_month: $CFG.show_modal_report_multi_month ? true : false,
            hidden_student_off_month: $CFG.hidden_student_off_month ? true : false,
            district : $CFG.district || '',
            unitId: $CFG.unit_id || '',
        };

        vm.random = Math.random()*100;
        vm.showBC = ["97","01","14","40","56","14", "46"].includes($CFG.province);
        $scope.detachedZScore = ["40", "14"].includes($CFG.province);
        vm.paginate = {
            maxSize: 5,
            limit: 50,
            page: 1,
            total: 0,
            perPages: [15, 30, 50, 100],
        };

        vm.courses = [];

        vm.years = CommonService.generateSchoolYear();

        vm.bmi_result_hacks = [
            {id: 0, title: ''},
            {id: 5, title: "SDD+ (SDD gầy còm thể nặng)"},
            {id: 4, title: "SDD (SDD gầy còm)"},
            {id: 3, title: "BT (Bình thường)"},
            {id: 1, title: "TC (Thừa cân)"},
            {id: 2, title: "BP (Béo phì)"}
        ];

        vm.months = _.map(ACTIVE_MONTHS, function (month) {
            return { id: month, title: _.padStart(month, 2, '0') };
        });

        vm.months.unshift({ id: 0, title: 'Chọn tháng' });

        vm.changed = false;

        $scope.uploadProgress = 0;

        $scope.sortByStudentId = false;

        $scope.uploadFileTest = function() {
            // console.log($);
        }

        $scope.importBmiData = [];
        $scope.importBmiValidateErrors = [];
        $scope.fileSelected = null;

        $scope.onClickFileUploadInput = function(event) {
            if(!vm.filters.month) {
                event.preventDefault();
                Swal.fire({
                    text: 'Vui lòng chọn tháng muốn nhập dữ liệu!',
                    type: "warning",
                });
            }
        };

        $scope.uploadFileExcel = function() {
            if(!vm.filters.month) {
                Swal.fire({
                    text: 'Vui lòng chọn tháng muốn nhập dữ liệu!',
                    type: "warning",
                });

                document.getElementById('excelFileInput').value = '';

                return;
            }

            $scope.$apply(function() {
                $scope.importBmiData = [];
                $scope.fileSelected = null;
                $scope.importBmiValidateErrors = [];
            });

            const file = document.getElementById('excelFileInput').files[0];
            const formData = new FormData();
            formData.append('file', file);
            formData.append('school_year', vm.filters.school_year + '');
            formData.append('month', vm.filters.month + '');

            const xhr = new XMLHttpRequest();

            const url = $CFG.remote.base_url+ '/measures/api/students/excel/upload-file-import-bmi';
            xhr.open('POST', url);
            xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="X-CSRF-TOKEN"]').attr('content'));
            xhr.send(formData);

            xhr.onload = function() {
                if (xhr.status === 200) {
                    document.getElementById('excelFileInput').value = '';

                    const jsonResponse = JSON.parse(xhr.responseText);

                    $scope.$apply(function() {
                        $scope.importBmiData = jsonResponse['extractedData'] ?? [];
                        $scope.fileSelected = file;
                    });
                } else {
                    Swal.fire({
                        title: "Lỗi !",
                        text: 'Có lỗi xảy ra trong quá trình tải file lên!',
                        type: "error",
                    });
                }

                console.log('Upload file successfully')
                document.getElementById('excelFileInput').value = '';
            };
        };

        $scope.downloadBmiTemplate = async function downloadBmiTemplate() {
            try {
                const baseUrl = $CFG.remote.base_url+ '/measures/api/students/excel/download-import-bmi-template';
                const params = new URLSearchParams();

                params.append('course_id', vm.filters.course_id + '');
                params.append('month', vm.filters.month + '');
                params.append('school_year', vm.filters.school_year + '');

                const urlDownload = new URL(baseUrl);
                urlDownload.search = params.toString();

                const response = await fetch(urlDownload.toString(), {

                });

                if(response.ok) {
                    const contentDisposition = response.headers.get('content-disposition');
                    console.log(contentDisposition, response.headers.get('vt-file-name'));

                    const blob = await response.blob();
                    const urlBlob = window.URL.createObjectURL(blob);
                    const downloadElement = document.createElement('a');

                    downloadElement.style.display = 'none';
                    downloadElement.href = urlBlob;
                    downloadElement.download = response.headers.get('vt-file-name') || 'danh-sach-can-do.xlsx';

                    document.body.appendChild(downloadElement);

                    downloadElement.click();

                    window.URL.revokeObjectURL(urlBlob);

                    Swal.fire({
                        text: "Download success!",
                        type: "success"
                    });
                } else {
                    const json = await response.json();
                    console.error(json.errors);
                }
            } catch (e) {
                console.error(e.message);
                Swal.fire({
                    text: "Download failed!",
                    type: "error"
                });
            }
        }

        $scope.saveImportBmiData = async function saveImportBmiData() {
           try {
               statusloading((new Date()).getTime());

               const response = await StudentService.saveImportBmi(
                   vm.filters.month,
                   vm.filters.school_year,
                   $scope.importBmiData
               );

               if(response.ok) {
                   Swal.fire({
                       title: "Thành công!",
                       text: 'Nhập dữ liệu từ file Excel thành công!',
                       type: "success"
                   });

                   $("#excelModal").modal("hide");
                   $('#excelFileInput').val('');

                   $scope.importBmiData = [];
                   $scope.importBmiValidateErrors = [];

                   vm.reload();
               } else if(response.status === 500) {
                   Swal.fire({
                       title: "Lỗi!",
                       text: 'Đã có lỗi xảy ra trong khi thêm dữ liệu từ file Excel!',
                       type: "error"
                   });

                   $scope.importBmiData = [];
                   // $scope.importBmiValidateErrors = [];

                   $("#excelModal").modal("hide");
                   $('#excelFileInput').val('');
               }
           } catch (e) {
               $("#excelModal").modal("hide");
               $('#excelFileInput').val('');

               $scope.importBmiData = [];
               $scope.importBmiValidateErrors = [];

               Swal.fire({
                   title: "Lỗi!",
                   text: 'Đã có lỗi xảy ra trong khi thêm dữ liệu từ file Excel!',
                   type: "error"
               });
           } finally {
               statusloadingclose();
           }
        }

        $scope.downloadImportBmiTemplate = function() {
            if(!vm.filters.month) {
                Swal.fire({
                    text: 'Vui lòng chọn tháng muốn nhập dữ liệu!',
                    type: "warning",
                });

                return;
            }

            const baseUrl = $CFG.remote.base_url+ '/measures/api/students/excel/download-import-bmi-template';

            const params = new URLSearchParams();

            params.append('course_id', vm.filters.course_id + '');
            params.append('month', vm.filters.month + '');
            params.append('school_year', vm.filters.school_year + '');
            params.append('sort_by_student_id', $scope.sortByStudentId);

            const url = new URL(baseUrl);

            console.log(url);
            url.search = params.toString();

            window.location.href = url.toString();
        }

        vm.save_tmp = function(key,student){
            if(student[key] != student['old_'+key]) {
                vm.save();
            }
        }
        vm.save_bmi_result_hack = function(key, student){
            vm.save();
        }

        CourseService.fetchCourses().then(function (courses) {
            if (!AuthService.isSuperAdmin()) {
                vm.filters.course_id = _.get(courses, '[0]courses.data[0].id', -1);
            }

            if (AuthService.isSuperAdmin()) {
                courses.unshift({ id: 0, name: 'Toàn trường' });
            } else {
                courses.unshift({ id: -1, name: 'Chọn lớp' });
            }

            vm.courses = _.assign({}, courses);
            _.each (vm.courses , (grade, i) => {
                if ('courses' in grade) {
                    if ('data' in vm.courses[i].courses)
                        vm.courses[i].courses.data = vm.courses[i].courses.data.sort(sortCourses)
                }
            })
        });

        fetchStudents();

        vm.currentStudent = [];

        function sortCourses(a, b) {
            if (a.name < b.name) return -1;
            if (a.name > b.name) return 1;
            return 0;
        }

        vm.onViewModal = function (student, isAllHistoriesModal = '') {
            vm.currentStudent = student;
            const student_id = student.id;
            if(isAllHistoriesModal != '') {

            }
            const api = isAllHistoriesModal == '' ? "" : "/histories";

            $scope.bmiData = {
                id: 'bmi-chart'+ isAllHistoriesModal,
                x_title: 'Tháng',
                y_title: 'Chỉ số BMI',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.heightData = {
                id: 'height-chart' + isAllHistoriesModal,
                x_title: 'Tháng',
                y_title: 'Chiều cao (cm)',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.weightData = {
                id: 'weight-chart' + isAllHistoriesModal,
                x_title: 'Tháng',
                y_title: 'Cân nặng (kg)',
                xAxis: [],
                yAxis: {min:0, max:0},
                series_data: {}
            };

            $scope.getStudentById = function(){
                var data = {
                    type: 'GET',
                    async: true,
                    filters: {
                        school_year: vm.filters.school_year,
                        month: vm.filters.month,
                        birthday: student.birthday
                    }
                };
                process($scope.api_url + '/measures/api/students'+api+'/' + student_id, data, function (resp) {
                    const studentData = resp.student.data;
                    if(isAllHistoriesModal != '') {
                        $scope.studentAll = resp.student.data;
                    }
                    else
                        $scope.student = resp.student.data;
                    //draw chart by BMI
                    $scope.bmiData.xAxis = $scope.generateXAxis(studentData);
                    $scope.bmiData.series_data = $scope.generateSeriesData($CFG.metaData.ages, studentData, $scope.bmiData.xAxis, 'bmi');
                    $scope.bmiData.yAxis = $scope.generateYAxis($scope.bmiData.series_data.SDD3.data, $scope.bmiData.series_data.TC3.data, studentData.histories);
                    $scope.generateSeriesDataTC3($scope.bmiData.series_data, $scope.bmiData.yAxis);
                    $scope.chart($scope.bmiData);
                    
                    //draw chart by height
                    $scope.heightData.xAxis = $scope.generateXAxis(studentData);
                    $scope.heightData.series_data = $scope.generateSeriesData($CFG.metaData.heights, studentData, $scope.heightData.xAxis, 'height');
                    $scope.heightData.yAxis = $scope.generateYAxisByHeight($scope.heightData.series_data.SDD3.data, $scope.heightData.series_data.TC3.data, studentData.histories);
                    $scope.generateSeriesDataTC3($scope.heightData.series_data, $scope.heightData.yAxis);
                    $scope.chart($scope.heightData);

                    //draw chart by weight
                    $scope.weightData.xAxis = $scope.generateXAxis(studentData);
                    $scope.weightData.series_data = $scope.generateSeriesData($CFG.metaData.weights, studentData, $scope.weightData.xAxis, 'weight');
                    $scope.weightData.yAxis = $scope.generateYAxisByWeight($scope.weightData.series_data.SDD3.data, $scope.weightData.series_data.TC3.data, studentData.histories);
                    $scope.generateSeriesDataTC3($scope.weightData.series_data, $scope.weightData.yAxis);
                    $scope.chart($scope.weightData);
                    $scope.$apply();
                });
            };
            $scope.getStudentById();
            $scope.setValue = function(min, max, data) {
                let tmp = {};
                const hsg = (data[max] - data[min]) / (max - min);
                for(let i = min+1; i < max; i++) {
                    tmp[i] = data[min] + hsg * (i - min);
                }
                return tmp;
            }
            $scope.chart = function (data) {
                if('student' in data.series_data) {
                    let arr_range = [];
                    for(let i = 0; i <= data.series_data.student.data.length - 1; i++) {
                        if(data.series_data.student.data[i] != null) {
                            arr_range.push(i);
                        }
                    }
                    for(let i = 0; i < arr_range.length - 1; i++) {
                        if(arr_range[i +1] - arr_range[i] >= 1) {
                            let a = $scope.setValue(arr_range[i], arr_range[i +1], data.series_data.student.data);
                            Object.keys(a).forEach(v => {
                                data.series_data.student.data[v] = parseFloat(a[v].toFixed(3));
                            })
                        }
                    }
                }
                Highcharts.chart(data.id, {
                    chart: {
                        type: 'area',
                    },
                    title: '',
                    xAxis: {
                        title: {
                            text: data.x_title
                        },
                        categories: data.xAxis,
                        gridLineWidth: 0.5,
                    },
                    yAxis: {
                        title: {
                            text: data.y_title
                        },
                        gridLineWidth: 0.5,
                        tickPositioner: function () {
                            var positions = [],
                                tick = (data.yAxis.min < 0) ? 0 : data.yAxis.min,
                                increment = data.yAxis.tickIncrement || 5;

                            if (data.yAxis.min !== null && data.yAxis.max !== null) {
                                for (tick; tick <= data.yAxis.max; tick += increment) {
                                    positions.push(tick);
                                }
                            }
                            return positions;
                        }
                    },
                    legend: {enabled: false},
                    tooltip: {
                        formatter: function() {
                            return this.y;
                        }
                    },
                    exporting: false,
                    credits: false,
                    plotOptions: {
                        area: {
                            // stacking: 'percent',
                            lineColor: '#333333',
                            lineWidth: 1,
                            marker: {
                                enabled: false,
                                symbol: 'circle',
                                radius: 0,
                                states: {
                                    hover: {
                                        enabled: false
                                    }
                                }
                            }
                        },
                        series: {
                            lineWidth: 1,
                            states: {
                                inactive: {
                                    opacity: 1
                                }
                            }
                        }
                    },

                    series: [
                        data.series_data.TTC3_moreC3,
                        data.series_data.TC3,
                        data.series_data.TC2,
                        // data.series_data.BT,
                        data.series_data.SDD2,
                        data.series_data.SDD3,
                        data.series_data.student,

                    ]
                });
            };

            $scope.generateXAxis = function (student) {
                return _.map(student.histories, 'month_old');
            };
            $scope.hasHistory = function (item) {
                return item.weight || item.height || item.bmi || item.conclusion_text;
            };

            $scope.generateSeriesData = function(bmi, student, xAxis, chart_type){
                let arr_bmi = _.filter(bmi, function (o) {
                    return (o.gender == student.gender && (o.month >= xAxis[0] && o.month <= xAxis[xAxis.length-1]));
                });

                let data = {
                    SDD3: {name: 'SDD 3', index: 6, data:[], color: '#FF6600'},
                    SDD2: {name: 'SDD 2', index: 5, data:[], color: '#FF9900'},
                    // BT: {name: 'BT', data:[]},
                    TC2: {name: 'TC 2', index: 4,  data:[],color: '#00CCFF'},
                    TC3: {name: 'TC 3', index: 3, data:[],color: '#FFFF99'},
                    TTC3_moreC3: {name: '', index: 2, data:[],color: chart_type == 'bmi'? '#99FF99' : '#FFFF99'},
                    student: {name: 'student', type: 'line', data:[],color: '#FF3399'},
                };

                //generate chart data of SDD & TC
                _.each(arr_bmi, function (value, key) {
                    data.SDD3.data.push(value.SD3neg);
                    data.SDD2.data.push(value.SD2neg);
                    // data.BT.data.push(value.SD1);
                    if(chart_type == 'bmi'){
                        data.TC2.data.push(value.SD1);
                        data.TC3.data.push(value.SD2);
                    }else{
                        data.TC2.data.push(value.SD2);
                        data.TC3.data.push(value.SD3);
                    }
                });

                //sort asc by bmi
                data.SDD3.data.sort(function(a, b){return a - b});
                data.SDD2.data.sort(function(a, b){return a - b});
                // data.BT.data.sort(function(a, b){return a - b});
                data.TC2.data.sort(function(a, b){return a - b});
                data.TC3.data.sort(function(a, b){return a - b});

                data.student.data = $scope.generateStudentData(student, chart_type);

                return data;
            };

            //generate student data
            $scope.generateStudentData = function (student, chart_type) {
                let student_data = [];
                let key = isAllHistoriesModal == '' ?  'month' : 'month_old';
                if(chart_type == 'bmi'){
                    _.each(student.histories, function (o) {
                        student_data[o[key]] = o.bmi;
                    });
                }
                if(chart_type == 'height'){
                    _.each(student.histories, function (o) {
                        student_data[o[key]] = o.height;
                    });
                }
                if(chart_type == 'weight'){
                    _.each(student.histories, function (o) {
                        student_data[o[key]] = o.weight;
                    });
                }
                let arr_data = [];
                if(isAllHistoriesModal == '') {
                    for(let i=9; i<=20; i++){
                        if(student_data[i] == undefined && i > 12){
                            arr_data.push(student_data[i % 12] || null);
                        }else{
                            arr_data.push(student_data[i]||null);
                        }
                    }
                }
                else {
                    student_data.forEach(v => {
                        if(v) {
                            arr_data.push(v);
                        }
                        else arr_data.push(null);
                    })
                }
                return _.valuesIn(arr_data);
            };

            $scope.generateYAxis = function (SDD3, TC3, histories) {
                let min_bmi_student = _.minBy(histories, function(o) { return o.bmi; });
                let yAxis = {min:0, max:0};
                
                if (min_bmi_student != null) {
                    let min_yAxis = (min_bmi_student.bmi < SDD3[0])?min_bmi_student.bmi:SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                    let max_bmi_student = _.maxBy(histories, function(o) { return o.bmi; });
                    let max_yAxis = (max_bmi_student.bmi > TC3[TC3.length-1])?max_bmi_student.bmi:TC3[TC3.length-1];

                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                } else {
                    let min_yAxis = SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                    let max_yAxis = TC3[TC3.length-1];
                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                }
                return yAxis;
            };

            //generate yAxis by height
            $scope.generateYAxisByHeight = function (SDD3, TC3, histories) {
                let yAxis = {min:0, max:0, tickIncrement: 10};
                let min_bmi_student = _.minBy(histories, function(o) { return o.height; });

                if (min_bmi_student != null) {
                    let min_yAxis = (min_bmi_student.height < SDD3[0])?min_bmi_student.height:SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                    let max_bmi_student = _.maxBy(histories, function(o) { return o.height; });
                    let max_yAxis = (max_bmi_student.height > TC3[TC3.length-1])?max_bmi_student.height:TC3[TC3.length-1];
                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                } else {
                    let min_yAxis = SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;
                    let max_yAxis = TC3[TC3.length-1];
                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                }

                return yAxis;
            };

            //generate yAxis by weight
            $scope.generateYAxisByWeight = function (SDD3, TC3, histories) {
                let min_bmi_student = _.minBy(histories, function(o) { return o.weight; });
                let yAxis = {min:0, max:0};

                if (min_bmi_student != null) {
                    let min_yAxis = (min_bmi_student.weight < SDD3[0])?min_bmi_student.weight:SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                    let max_bmi_student = _.maxBy(histories, function(o) { return o.weight; });
                    let max_yAxis = (max_bmi_student.weight > TC3[TC3.length-1])?max_bmi_student.weight:TC3[TC3.length-1];
                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                } else {
                    let min_yAxis = SDD3[0];
                    yAxis.min = (Math.floor((Math.floor(min_yAxis)-10)/10))*10 + 5;

                    let max_yAxis = TC3[TC3.length-1];
                    yAxis.max = (Math.ceil((Math.floor(max_yAxis)+10)/10))*10 + 5;
                }

                return yAxis;
            };

            $scope.generateSeriesDataTC3 = function (seriesData, yAxis) {
                let tc3Data = [];
                if(isAllHistoriesModal == '') {
                    for(let i = 0; i<12; i++){
                        tc3Data.push(yAxis.max) ;
                    }
                }
                else{
                    for(let i = 0; i< seriesData.TC3.data.length; i++){
                        tc3Data.push(yAxis.max) ;
                    }
                }
                seriesData.TTC3_moreC3.data = tc3Data;
            }
            // $('#dlg').dialog('open');
        };

        vm.onViewModalAllHistories = function (student) {

        }

        vm.onSchoolYearChanged = function onSchoolYearChanged() {
            $CFG.active_year = vm.filters.school_year;
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onMonthChanged = function onMonthChanged() {
            $CFG.active_month = vm.filters.month;
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onCourseChanged = function onCourseChanged() {
            $CFG.active_course_id = vm.filters.course_id;
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onKeywordChanged = function onKeywordChanged() {
            $CFG.keyword = vm.filters.keyword;
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.onMalnutritionChanged = function onMalnutritionChanged() {
            $CFG.active_malnutrition = vm.filters.malnutrition_last_month;
            vm.paginate.page = 1;
            fetchStudents();
        };
        vm.onSddBmiRuleChanged = function onSddBmiRuleChanged() {
            $CFG.no_bmi_sdd_weight_rule = vm.filters.no_bmi_sdd_weight_rule;
            StudentService.changeConfig('no_bmi_sdd_weight_rule', vm.filters.no_bmi_sdd_weight_rule);
        };

        vm.onWHNormalChanged = function onWHNormalChanged() {
            $CFG.get_weight_height_not_normal = vm.filters.get_weight_height_not_normal;
            StudentService.changeConfig('get_weight_height_not_normal', vm.filters.get_weight_height_not_normal);
        };

        vm.onSMRMMChanged = function () {
            $CFG.show_modal_report_multi_month = vm.filters.show_modal_report_multi_month;
            StudentService.changeConfig('show_modal_report_multi_month', vm.filters.show_modal_report_multi_month);
        }

        vm.onHiddenStudentOffMonthChanged = function () {
            $CFG.hidden_student_off_month = vm.filters.hidden_student_off_month;
            StudentService.changeConfig('hidden_student_off_month', vm.filters.hidden_student_off_month);
        }

        vm.onUnder60MonthBmiRuleChanged = function onUnder60MonthBmiRuleChanged() {
            $CFG.no_bmi_under60_month_rule = vm.filters.no_bmi_under60_month_rule;
            StudentService.changeConfig('no_bmi_under60_month_rule', vm.filters.no_bmi_under60_month_rule);
        };

        vm.onIsNotWeightHeightRuleChanged = function onIsNotWeightHeightRuleChanged() {
            $CFG.no_is_not_weight_height = vm.filters.no_is_not_weight_height;
            StudentService.changeConfig('no_is_not_weight_height', vm.filters.no_is_not_weight_height);
        };

        vm.bmiResultToCode = function bmiResultToCode(student, type) {
            return BmiService.getResultCodeFromId(student, type);
        };

        vm.bmiResultToText = function bmiResultToText(student, type) {
            return BmiService.getResultTextFromId(student, type);
        };

        vm.onWeightOrHeightChanged = function onWeightOrHeightChanged(student) {
            student.bmi = student.calculateBmi(vm.filters.no_bmi_under60_month_rule);
            student.z_score = student.calculateBmi(true);
            student.weight_result = BmiService.getWeightResult(student);
            student.height_result = BmiService.getHeightResult(student);
            student.bmi_result = BmiService.getBmiResult(student,vm.filters.no_bmi_under60_month_rule);
            student.conclusion = student.bmi_result;
            student.conclusion_text = BmiService.getResultTextFromId(student, 'conclusion');

            if (student.month_old < 24) {
                if (student.bmi_result_hack) {
                    student.bmi_result = student.bmi_result_hack;
                }
                student.conclusion = student.bmi_result;
                student.conclusion_text = BmiService.getResultTextFromId(student, 'conclusion');
                student.bmi = ''; // Gan BMI ve 0
            }



            vm.changed = true;
        };

        vm.onNoteChanged = function onNoteChanged(student) {
            vm.changed = true;
        }

        vm.reset = function reset(student, type) {
            switch (type) {
                case 'weight':
                    student.resetWeight();
                    break;
                case 'height':
                    student.resetHeight();
                    break;
                case 'note':
                    student.resetNote();
                    break;
                default:
                    throw new Error('Invalid parameter');
            }

            vm.onWeightOrHeightChanged(student);
        };

        vm.pageChanged = function pageChanged() {
            fetchStudents();
        };

        vm.onPageLimitChanged = function onPageLimitChanged() {
            vm.paginate.page = 1;
            fetchStudents();
        };

        vm.reload = function reload() {
            vm.paginate.page = 1;
            vm.changed = false;
            fetchStudents(false);
        };

        vm.save = function save() {
            if (vm.studentBmiForm.$invalid) {
                return;
            }

            StudentService.store(vm.filters.month, vm.filters.school_year, vm.students)
                .then(function (status) {
                    if (status) {
                        //vm.reload();
                    }
                })
                .finally(function () {
                    vm.changed = false;
                });
        };

        vm.downloadExcel = function downloadExcel() {
            location.href = StudentService.generateDownloadExcelUrlForDashboard(vm.filters);
        };

        vm.downloadExcelSDD = function downloadExcelOptions() {
            location.href = StudentService.generateDownloadExcelUrlForDashboardSDD(vm.filters);
        };

        vm.downloadExcelSDDAllYear = function downloadExcelYear() {
            location.href = StudentService.generateDownloadExcelUrlForDashboardSDDAllYear(vm.filters);
        };

        vm.downloadExcelWeigthAndHeigth = function downloadExcelWeigthAndHeigth(date) {
            location.href = StudentService.generateDownloadExcelUrlForDashboardWeigthAndHeigth(Object.assign(vm.filters, { date : date }));
        };

        vm.showModalReportWH = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'choose_month_modal_content_1.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    // $ctrl.date = new Date();
                    $ctrl.error = false;
                    $ctrl.error_conclusion = false;

                    $ctrl.months = vm.months;
                    $ctrl.month_selected = {
                        1 : 0,
                        2 : 0,
                        3 : 0,
                        4 : 0,
                        5 : 0,
                    };

                    $ctrl.show = function (type) {
                        let arr_month = [];
                        for(let i = 1; i <= 5; i++ ) {
                            if($ctrl.month_selected[i] && !arr_month.includes($ctrl.month_selected[i])){
                                arr_month.push($ctrl.month_selected[i]);
                            }
                        }
                        location.href = StudentService.generateDownloadExcelUrlForDashboardWeigthAndHeigth(Object.assign(vm.filters, { months : JSON.stringify(arr_month) }));
                        vm.filters.months = null;
                    }

                    $ctrl.changeMonth = function (time,month_selected,error) {
                        let count = 0;
                        if($ctrl[month_selected][time]) {
                            for(let i = parseInt(time)- 1; i >= 1; i--) {
                                if(!$ctrl[month_selected][i]) {
                                    count ++;
                                    $ctrl[error] = true;
                                }
                            }
                            for(let i = 1; i <= 5; i++ ) {
                                if($ctrl[month_selected][i] && $ctrl[month_selected][i+1]){
                                    let index1 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i]);
                                    let index2 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i+1]);
                                    if(index1 > index2){
                                        count ++;
                                        $ctrl[error] = true;
                                    }
                                }
                            }
                            
                        }
                        if (count == 0) $ctrl[error] = false;
                    }

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        }

        vm.formChooseMonthSonLa = function() {
            var modalInstance = $uibModal.open({
                templateUrl: 'choose_month_modal_content_2.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    // $ctrl.date = new Date();
                    $ctrl.error = false;
                    $ctrl.error_conclusion = false;

                    $ctrl.months = vm.months; 
                    $ctrl.month = "0";

                    $ctrl.show = function () {
                        let url = $CFG.remote.base_url+'/dinhduong/cando/rpt_follow_weight_height?schoolyear='+vm.filters.school_year+'&course_id='+vm.filters.course_id+'&month='+$ctrl.month+'&page=1';
                        window.open(url)
                    }

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        }

        vm.formChooseMonth = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'choose_month_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    // $ctrl.date = new Date();
                    $ctrl.error = false;
                    $ctrl.error_conclusion = false;

                    $ctrl.months = vm.months;
                    $ctrl.month_selected = {
                        1 : 0,
                        2 : 0,
                        3 : 0,
                        4 : 0,
                        5 : 0,
                    };

                    $ctrl.months_conclusion = {
                        1 : 0,
                        2 : 0,
                        3 : 0,
                    };

                    $ctrl.show = function (type) {
                        let arr_month = [];
                        for(let i = 1; i <= 5; i++ ) {
                            if($ctrl.month_selected[i] && !arr_month.includes($ctrl.month_selected[i])){
                                arr_month.push($ctrl.month_selected[i]);
                            }
                        }
                        let arr_month_conclusion = [];
                        for(let i = 1; i <= 3; i++ ) {
                            if($ctrl.months_conclusion[i] && !arr_month_conclusion.includes($ctrl.months_conclusion[i])){
                                arr_month_conclusion.push($ctrl.months_conclusion[i]);
                            }
                        }
                        let url = '';
                        if(type == 1 )
                            url = $CFG.remote.base_url+'/dinhduong/cando/rpt_follow_weight_height?schoolyear='+vm.filters.school_year+'&course_id='+vm.filters.course_id+'&months='+JSON.stringify(arr_month)+'&months_conclusion='+JSON.stringify(arr_month_conclusion);
                        else
                            url = $CFG.remote.base_url+'/dinhduong/cando/rpt_follow_disease?schoolyear='+vm.filters.school_year+'&course_id='+vm.filters.course_id+'&months='+JSON.stringify(arr_month)+'&months_conclusion='+JSON.stringify(arr_month_conclusion);
                        window.open(url)
                    }

                    $ctrl.changeMonth = function (time,month_selected,error) {
                        let count = 0;
                        if($ctrl[month_selected][time]) {
                            for(let i = parseInt(time)- 1; i >= 1; i--) {
                                if(!$ctrl[month_selected][i]) {
                                    count ++;
                                    $ctrl[error] = true;
                                }
                            }
                            for(let i = 1; i <= 5; i++ ) {
                                if($ctrl[month_selected][i] && $ctrl[month_selected][i+1]){
                                    let index1 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i]);
                                    let index2 = vm.months.findIndex(v => v.id == $ctrl[month_selected][i+1]);
                                    if(index1 > index2){
                                        count ++;
                                        $ctrl[error] = true;
                                    }
                                }
                            }
                            
                        }
                        if (count == 0) $ctrl[error] = false;
                    }

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        };

        vm.formExportWeigthAndHeigth = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'exports_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    $ctrl.date = new Date();

                    $ctrl.opened = false;

                    $ctrl.open = function () {
                        $ctrl.opened = !$ctrl.opened;
                    };

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.exports = function () {
                        vm.downloadExcelWeigthAndHeigth($('#txt_date_export').val());
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                }
            }, function () {
                // no action
            });
        };

        function fetchStudents(clear) {
            clear = _.defaultTo(clear, true);

            if (clear) {
                vm.students = [];
            }

            if (!vm.filters.school_year || !vm.filters.month || vm.filters.course_id < 0) {
                return;
            }

            StudentService
                .fetchStudents(vm.paginate.page, vm.paginate.limit, vm.filters)
                .then(function (response) {
                    vm.students = response.students;
                    vm.paginate.total = response.total;
                    vm.unit_level = response.user.level;
                });
        }

        vm.isInvalid = function isInvalid(id) {
            var control = vm.studentBmiForm[id];

            return control.$invalid;
        };

        /* Chuyển đổi dữ liệu vào cpu cando */
        vm.convertToCpuCando = function() {
            var data = {
                month: vm.filters.month,
                schoolyear: vm.filters.school_year,
                students: vm.students,
            };
            var url = $CFG.remote.base_url+ '/doing/report/csdl_nganh_v2/convert_cpu_cando';
            return $http.post(url, JSON.stringify(data), {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    Swal.fire({
                        title: "Thành công",
                        text: 'Chuyển đổi dữ liệu vào bảng chờ đồng bộ thành công!',
                        type: "success",
                    });
                })
                .catch(function () {
                    Swal.fire({
                        title: "Lỗi!",
                        text: 'Có lỗi xảy ra trong quá chuyển đổi dữ liệu!',
                        type: "error",
                    });
                })
                .finally(function () {
                    
                });
        };
        
        // chức năng này chỉ dùng cho trường mnanhson và chỉ hiện chức năng ở tháng 7
        vm.copyDataFromMonth6 = function() {
            statusloading((new Date()).getTime());
            var data = {
                unit_id: vm.filters.unitId,
                month: vm.filters.month,
                school_year: vm.filters.school_year,
                students: vm.students,
            };
            var url = $CFG.remote.base_url+ '/measures/api/students/copy-data-from-month-6';
            return $http.post(url, JSON.stringify(data), {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function (response) {
                    if (response.data.result == 'success') {
                        Swal.fire({
                            title: "Thành công",
                            text: 'Copy dữ liệu tháng 6 thành công!',
                            type: "success",
                        });
                        fetchStudents();
                        statusloadingclose();
                    } else {
                        Swal.fire({
                            title: "Lỗi!",
                            text: 'Có lỗi xảy ra trong quá trình copy dữ liệu!',
                            type: "error",
                        });
                        statusloadingclose();
                    }
                });
        };

    }

        DashboardController.$inject = [
            '$scope',
            '$http',
            'CourseService',
            'CommonService',
            'AuthService',
            'ACTIVE_MONTHS',
            'StudentService',
            'BmiService',
            '$uibModal'
        ];
    }) (window.angular_app);
