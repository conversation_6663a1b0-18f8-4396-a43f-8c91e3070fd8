<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
	<meta charset="utf-8">
	<base href="http://localhost:3000/single/">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link rel="stylesheet" href="http://localhost:3000/build/build/css/style.min-b0affb6d2f.css?v=3">
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<!-- Google Tag Manager -->
	<script type="text/javascript">
	  var _gaq = _gaq || [];
	  		_gaq.push(['_setAccount', 'UA-*********-3']);
	  	  _gaq.push(['_trackPageview']);	
	  (function() {
	    var ga = document.createElement('script');
		ga.type = 'text/javascript'; ga.async = true;
	    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
	    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
	  })();
	</script>
    <script type="text/javascript">
        $CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
            local: {base_url: "http://localhost:3000/js/admin"},
            remote: {base_url: "http://localhost:3000"},
            template: {base_url: "http://localhost:3000/templates"},
            project: 'dinhduong',
            namhoc: '2024',
            round_number_config: "",
            digit_grouping_char: "",
            spinController: {},
            unit_id: '51461',
            admin: 1,
            self_id: '',
            username: 'd.hd.mnquangvt',
            level: '4',
            province: '97',
            school_points: (function () {
                return 1;
            })(),
            is_vin: false,
			api_base_url_ura: 'https://ura.edu.vn/api/v1/',
			help_base_url: "https://storage.ura.edu.vn"
        };
	</script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/socket.io.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/load-image.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/canvas-to-blob.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/textAngular.min.js"></script>
	<script type="text/javascript" src="http://localhost:3000/build/build/js/vendor.min-77199f347f.js"></script>
		    <script type="text/javascript" src="http://localhost:3000/build/build/js/app.min-a0250d91ae.js"></script>
	</head>
<body ng-app="angular_app" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController" id="mainContentController">
		<div class="fix-main">
			<div class="header">
				<div class="img-tree"><img src="http://localhost:3000/css/dinhduong/images/tree.png"></div>
				<div class="logo-kh" ng-show="menu.page=='index'">
					<!-- <img src="http://localhost:3000/css/dinhduong/images/logo_dd_new2.png"> -->
				</div>
				<div class="school-year school-year-kh">
					<div role="navigation" class="navbar-buttons navbar-header">
	                    <ul class="nav ace-nav">
	                    		                    	<li class="light-blue">
	                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
	                                <span class="school-year-view">
	                                    <strong>2024-2025</strong>
	                                </span>
	                                <i class="ace-icon fa fa-caret-down"></i>
	                            </a>
	                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
	                            		                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2019','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2019-2020
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2020','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2020-2021
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2021','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2021-2022
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2022','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2022-2023
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2023','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2023-2024
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear active">
	                                    <a href="javascript: void(0);" >
	                                        <span class="glyphicon glyphicon-check" style="width: 20px;"></span>
	                                        2024-2025
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2025','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2025-2026
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2026','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2026-2027
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2027','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2027-2028
	                                    </a>
	                                </li>
	                                	                                <li class="option-schoolyear ">
	                                    <a href="javascript: void(0);"  onclick="$.selectSchoolYear('2028','dinhduong')" >
	                                        <span class="glyphicon glyphicon-ok-circle" style="width: 20px;"></span>
	                                        2028-2029
	                                    </a>
	                                </li>
	                                	                            </ul>
	                        </li>
	                        <li class="light-blue">
	                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
	                                <span class="user-info">
	                                    <strong style="color: #3b5844;">
	                                    Tài khoản
	                                    </strong>
	                                </span>
	                                <i class="ace-icon fa fa-caret-down"></i>
	                            </a>
	                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
	                                <li ng-click="detailUserForm();">
	                                    <a href="javascript: void(0);" >
	                                        <span class="glyphicon glyphicon-user"></span>
	                                        d.hd.mnquangvt
	                                         (<span id="onlineTogether">  </span>)
	                                    </a>
	                                </li>
			                        	                                <li class="li-admin">
		                        		<a  href="http://localhost:3000/admin"><i style="color: #363636;" title="Quản trị" class="glyphicon glyphicon-cog"></i>
		                        			Quản trị
		                        		</a>
			                        </li>
			                        			                        									<li>
										<a href="javascript: void(0);" ng-click="detailUserSignForm();">
											<span class="glyphicon glyphicon-edit"></span>
											Thông tin chữ kí ảnh
										</a>
									</li>
									<li>
										<a href="javascript: void(0);" ng-click="detailUserSignReport();">
											<span class="glyphicon glyphicon-edit"></span>
											Ký báo cáo
										</a>
									</li>
																		
									<li>
										<a target="_blank" href="http://localhost:3000/report/dashboard/index">
											<span class="glyphicon glyphicon-signal"></span>
											Trang thống kê
										</a>
									</li>
										                                <li>
	                                    <a href="javascript: void(0);" onclick="form_edit_pass()">
	                                        <span class="glyphicon glyphicon-lock"></span>
	                                        Đổi mật khẩu
	                                    </a>
	                                </li>
	                                <li class="divider"></li>
	                                <li>
	                                    <a href="http://localhost:3000/logout" tabindex="-1">
	                                        <i class="ace-icon fa fa-power-off" style="color: #ff0000;" ></i>
	                                        Đăng xuất
	                                    </a>
	                                </li>
	                            </ul>
	                        </li>
	                        <li class="light-blue">
	                            <a class="dropdown-toggle" href="#" data-toggle="dropdown">
	                            	<samp class="li-eye-image">
	                            		<img  title="Hướng dẫn" src="http://localhost:3000/css/dinhduong/images/support.gif">
	                            	</samp>
	                            </a>
	                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
	                                <li>
	                                    <a target="_blank" href="http://localhost:3000/css/dinhduong/files/QuyTrinhThucHien.pdf?v=2">
	                                      Quy trình thực hiện
	                                    </a>
	                                </li>
	                                									<li>
	                                    <a target="_blank" href="http://localhost:3000/css/dinhduong/files/dinhluong100tre.pdf?v=3">
	                                      Định lượng 100 trẻ
	                                    </a>
	                                </li>
	                                	                                <li>
	                                    <a target="_blank" href="http://localhost:3000/css/dinhduong/files/HDSD-KHAUPHANDINHDUONG-RUTGON.pdf?v=1">
	                                       HDSD - Dinh dưỡng
	                                    </a>
	                                </li>
	                                <li>
	                                    <a target="_blank" href="https://docs.google.com/document/d/1-SuV6PQaBzBV7AeIIxSx-oT5CPiLPXfw/edit?tab=t.0">
	                                       HDSD - Thu chi
	                                    </a>
	                                </li>
	                                <li>
	                                	<span style="padding-left: 20px;color: #333333;">Video dinh dưỡng</span>
	                                    <ul class="ul-child-help">
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/dangnhap.mp4'}}">
			                                       Đăng nhập
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/nhapkho.mp4'}}">
			                                       Nhập kho
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/taomonan.mp4'}}">
			                                       Tạo món ăn
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/taothucdon.mp4'}}">
			                                       Tạo thực đơn
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/dieuchinhthucpham.mp4'}}">
			                                       Điều chỉnh thực phẩm
			                                    </a>
	                                    	</li>
											<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/bieumauthongke.mp4'}}">
			                                       Biểu mẫu thống kê
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/cauhinh.mp4'}}">
			                                       Cấu hình
			                                    </a>
	                                    	</li>
	                                    </ul>
	                                </li>
	                                <li>
	                                	<span style="padding-left: 20px;color: #333333;">Video thu chi</span>
	                                    <ul class="ul-child-help">
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/taokhoilop.mp4'}}">
			                                       Tạo khối lớp
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/themhocsinh.mp4'}}">
			                                       Thêm học sinh
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/cauhinhngaynghi.mp4'}}">
			                                       Cấu hình ngày nghỉ
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/danhmuckhoanthu.mp4'}}">
			                                       Danh mục khoản thu
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/thietlapkhoanthu.mp4'}}">
			                                       Thiết lập khoản thu
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/diemdanhhocsinh.mp4'}}">
			                                       Điểm danh học sinh
			                                    </a>
	                                    	</li>
	                                    	<li>
	                                    		<a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/thuphi/thuphi_baocao.mp4'}}">
			                                       Thu phí - Báo cáo
			                                    </a>
	                                    	</li>
	                                    </ul>
	                                </li>
	                                	                                <li>
	                                    <a href="http://localhost:3000/css/dinhduong/files/hotrotuxa.exe">
	                                       Tải hỗ trợ từ xa
	                                    </a>
	                                </li>
	                            </ul>
	                        </li>
	                        
	                        <li class="li-home-image">
	                        	<a href="http://localhost:3000"><img title="Trở về trang chủ" src="http://localhost:3000/css/dinhduong/images/logo_pms_new.png"></a>
	                        </li>
	                    </ul>
	                </div>
					<!-- <div class="notification-khgd">
						<img src="http://localhost:3000/css/dinhduong/images/thongbao.png">
						<div class="num-notification">1</div>
					</div> -->
					<!-- <div text-angular="text-angular" name="htmlcontent" ng-model="htmlcontent" ta-disabled='disabled' ta-options="toolbar_helper"></div> -->
				</div>
			</div>
			<div class="content" style="">
				<div class="main-content">
					<div class="body-main{{ (menu.page == 'children'?' show-view':'') }}" style="">
						<div id="header-title" class="header-title" ng-include="menu.templates.group" ng-show="menu.page!='index'">
							Menu trong trang chính
						</div>
						<div class="angular-view-container" ng-view="" ng-show="menu.page!='index'">
							<div style="width: 270px; margin: auto;margin-top: 250px; color: orange; font-size: 30px;">
								Đang xử lý ...
							</div>
						</div>
						<div class="in-main-content {{menu.page=='index'?'in-main':'in-main-after'}}" ng-show="menu.page=='index'">
							<!-- <div class="title-dd" ng-show="menu.page=='index'"><p>Quản lý dinh dưỡng</p></div> -->
							<div ng-include="menu.templates.top.index">
								Menu chính
							</div>
						</div>
						<div ng-include="menu.templates.bottom[menu.page]" style="position: absolute;bottom: 0px;width: 100%;">
							Menu chân trang
						</div>
					</div>
					<div class="mouse-mc">
						<img src="http://localhost:3000/css/dinhduong/images/mouse.png">
					</div>
				</div>
			</div>
			<div class="problem-view-container">
				<div class="bottom-show {{menu.page=='index'?'':'bottom-show-after'}}" onclick="bottomShow()">
					<img title="Những vấn đề thường gặp" src="http://localhost:3000/css/dinhduong/images/qs.png">
					<p>Những vấn đề thường gặp</p>
				</div>
				<div class="bottom-content">
					<div class="in-bc">
						<div class="menu-bc">
							<div class="title-menu-bc">
								<i class="bottom-hidden glyphicon glyphicon-remove-circle" title="Đóng"></i>
								<h4>Những vấn đề thường gặp</h4>
							</div>
							<div class="main-menu-bc">
								<ul>
									<li><a href="">Kho</a></li>
									<li><a href="">Thực đơn</a></li>
									<li><a href="">Tính tiền ăn</a></li>
									<li><a href="">Quản lý thu chi</a></li>
									<li><a href="">Quản lý học sinh</a></li>
									<li><a href="">Danh mục</a></li>
								</ul>
							</div>
						</div>
						<div class="content-bc">
							
						</div>
					</div>
				</div>
			</div>

						<div class="quy-trinh">
				<div class="content-qt">
					<div class="title-qt">
						<h5>quy trình rút gọn thao tác phần mềm khẩu phần dinh dưỡng</h5>
						<label id="lb-tudong" class="checkbox-inline"><input type="checkbox" id="tudonghien12" name="tudonghien12" onchange="tudonghien(this)">Tự động hiện lần sau</label>
					</div>
					<div class="main-qt">
						<div class="top-main-qt">
							<div class="title-main-qt">
								<h5>Thao tác thực hiện 1 lần</h5>
								<p>Hãy thực hiện bước này khi mới <span>sử dụng phần mềm lần đầu </span>bạn nhé. Khâu này giúp chúng ta ...</p>
							</div>
							<div class="content-main-qt content-main-tt">
								<div class="item-main-tt">
									<a href="http://localhost:3000/single/dinhduong/norm" onclick="hideqt()">
										<div class="top-item-tt">
											<img src="http://localhost:3000/css/dinhduong/images/dichvu.png">
										</div>
										<div class="bottom-item-tt">
											<p class="number-item-tt">
												1
											</p>
											<p class="content-item-tt">
												Chọn Cơ cấu dinh dưỡng phù hợp với tiền ăn của trường
											</p>
										</div>
									</a>
								</div>
								
								<div class="item-main-tt">
									<a href="http://localhost:3000/single/dinhduong/dish" onclick="hideqt()">
										<div class="top-item-tt">
											<img src="http://localhost:3000/css/dinhduong/images/monan.png">
										</div>
										<div class="bottom-item-tt">
											<p class="number-item-tt">
												2
											</p>
											<p class="content-item-tt">
												Tạo món ăn cho trường
											</p>
										</div>
									</a>
								</div>
								<div class="item-main-tt">
									<a href="http://localhost:3000/single/dinhduong/dish_storage" onclick="hideqt()">
										<div class="top-item-tt">
											<img src="http://localhost:3000/css/dinhduong/images/thuvienmonanchiase.png">
										</div>
										<div class="bottom-item-tt">
											<p class="number-item-tt">
												3
											</p>
											<p class="content-item-tt">
												Vào thư viện món ăn chia sẻ để tìm món ăn cho trường
											</p>
										</div>
									</a>
								</div>
							</div>
						</div>
						<div class="bottom-main-qt">
							<div class="title-main-qt">
								<h5>Thao tác thường xuyên</h5>
							</div>
							<div class="content-main-qt content-main-tx">
								<div class="item-main-tx" style="margin-left: 100px;">
									<a href="http://localhost:3000/single/dinhduong/storage" onclick="hideqt()">
										<p class="number-item-tx">1</p>
										<p class="title-item-tx">Nhập kho</p>
										<p class="link-item-tx"><a href="http://localhost:3000/single/dinhduong/storage" onclick="hideqt()">Quản lý kho > Nhập kho</a></p>
										<p class="nd-item-tx">Thực hiện khi phát sinh nhập kho</p>
									</a>
								</div>
								<div class="dau-main-tx">
									<i class="glyphicon glyphicon-menu-right"></i>
								</div>
								<div class="item-main-tx">
									<a href="http://localhost:3000/single/dinhduong/menu_adjust" onclick="hideqt()">
										<p class="number-item-tx">2</p>
										<p class="title-item-tx">Cân đối khẩu phần</p>
										<p class="link-item-tx"><a href="http://localhost:3000/single/dinhduong/menu_adjust" onclick="hideqt()">Khẩu phần dinh dưỡng > Điều chỉnh thực phẩm</a></p>
										<p class="nd-item-tx"><span>1. </span>Tạo thực đơn (hoặc sử dụng thực đơn mẫu) để cân đối khẩu phần trước khi đi chợ</p>
										<p class="nd-item-tx"><span>2. </span>Sau khi đi chợ thì điểu chỉnh lại để tiền ăn trong ngày chính xác.</p>
									</a>
								</div>
								<div class="dau-main-tx">
									<i class="glyphicon glyphicon-menu-right"></i>
								</div>
								<div class="item-main-tx">
									<a href="http://localhost:3000/single/dinhduong/menu_report" onclick="hideqt()">
										<p class="number-item-tx">3</p>
										<p class="title-item-tx">Xuất biểu mẫu thống kê</p>
										<p class="link-item-tx"><a href="http://localhost:3000/single/dinhduong/menu_report" onclick="hideqt()">Khẩu phần dinh dưỡng > Báo cáo thống kê</a></p>
										<p class="nd-item-tx"><span>1. </span>Phiếu kê chợ</p>
										<p class="nd-item-tx"><span>2. </span>Tiếp phẩm tươi</p>
										<p class="nd-item-tx"><span>3. </span>Tiếp phẩm khô</p>
										<p class="nd-item-tx"><span>4. </span>Sơ chế biến</p>
										<p class="nd-item-tx"><span>5. </span>Lưu hủy mẫu</p>
										<p class="nd-item-tx"><span>6. </span>Tính tiền ăn</p>
										<p class="nd-item-tx"><span>7. </span>Calo tuần</p>
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="btn-quy-trinh">
					<img src="http://localhost:3000/css/dinhduong/images/btnquytrinh.png" title="Quy trình rút gọn thao tác phần mềm khẩu phần dinh dưỡng">
				</div>
			</div>
					</div>
	</div>
  	<div id="tructuyen" style="position: fixed;bottom: 3px; left: 3px; width: 60px;z-index: 100;"></div>
	<!-- Thông báo tổng đài -->
	<div style="display: none; position: relative;" id="dialog-tphcm">
    	<img class="img-tb-login" src="http://localhost:3000/images/thong-bao-910.png?_=376">
  	</div>
  	<!-- Template menu -->
  	  	<!-- Hiện báo đang xử lý -->
  	<div id="statusloading-mash" style="display: ;">
        <div class="spinner-container-loading">
            <div class="spinner-container btn-hover spinner3" id="spinner-container">
                <div id="time-progress-bar"></div>
            </div> 
            <span class="spinner-text">Đang xử lý ...</span>
        </div>
    </div>
<script type="text/ng-template" id="menu_top_index.htm">
		<div class="menu-main-content">
		<ul class="nav nav-tabs nav-main-content" ng-if="count(menu.data.top)>1">
			<i class="glyphicon glyphicon-align-justify show-tab-main" onclick="tabMain()"></i>
			<div class="tab-main-index" ng-repeat="(index,menu_top) in menu.data.top">
			  	<li class="{{(menu_top.name==menu.selected.name?'active':'')}} li-hover li{{index}}" ng-click="menu.selected=menu_top" id="{{menu_top.icon}}">
			  		<span data-toggle="tab" href="#nav{{index}}" ng-bind="menu_top.name" onclick="tabMain()"></span>
			  	</li>
			</div>
			<div class="nav-main-content-copy"></div>
			<div class="menu-cauhinh-container dropdown">
				<div class="setup-mc-new dropdown-toggle" ng-bind="menu.data.bottom[0].name" data-toggle="dropdown">Cấu hình</div>
				<div class="dropdown-menu">
					<li ng-repeat="(index,item) in menu.data.bottom[0].children" style="width: 100%;">
						<a class="a-click" ng-href="{{$CFG.remote.base_url+'/'+item.path}}" style="padding: 3px 10px;">
							<img ng-src="http://localhost:3000/css/dinhduong/images/{{item.icon}}.png" style="width: 20px;">
							{{item.name}}
						</a>
					</li>
				</div>
			</div>
		</ul>
		<div class="tab-content">
			<div ng-repeat="(index,menu_top) in menu.data.top">
				<ul class="child-nav-main" ng-repeat="child in menu_top.children">
					<div id="nav{{index}}" class="tab-pane fade {{(menu_top.name==menu.selected.name?'in active':'')}}">
						<div class="item-child-nav-main" id="item-child-nav-main{{$index}}">
							<a class="b-click" ng-href="{{$CFG.remote.base_url+'/'+child.path}}">
								<div class="thumb-chid-icon">
									<img ng-src="http://localhost:3000/css/dinhduong/images/{{child.icon}}.png">
								</div>
						    	<h4 ng-bind="child.name"></h4>
					    	</a>
						</div>
				  	</div>

				</ul>
		  	</div>
		</div>
	</div>
	<style type="text/css">
		.thongbao-thaydoi-menu{
		    margin: 0 auto;
		    height: 50px;
		    width: 650px;
		    text-align: justify;
		    /* float: left; */
		    padding-top: 15px;
		    clear: both;
		}
		.menu-cauhinh-container{
			position: absolute;
		    left: 100%;
		    white-space: nowrap;
		    margin-left: 10px;
		    margin-top: 7px;
		}
		.menu-cauhinh-container li{
			width: 100%;
			padding: 3px 5px;
			color: #333;
		}
		.menu-cauhinh-container .dropdown-menu{
			background: #eaffcf;
		}
		.menu-cauhinh-container li:hover{

		}
	</style>
	</script>
<script type="text/ng-template" id="menu_bottom_children.htm"></script>
<script type="text/ng-template" id="menu_bottom_index.htm"></script>
<style type="text/css">
	.li-eye-image img{
		width: 17px;
	}
	.div-menu-con, .quy-trinh, .support-video{
		// display: none !important;
	}
	.body-main {
		padding-bottom: 0px !important;;
	}
</style>
</body>
</html>
