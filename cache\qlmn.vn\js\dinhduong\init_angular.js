angular_app = angular.module("angular_app",['ngRoute','ngResource','ngCookies','ngAnimate','textAngular', 'ngStorage'])
.config(['$routeProvider','$locationProvider',function ($routeProvider,$locationProvider) {
    $locationProvider.html5Mode(true);
    $routeProvider
    .when('/dinhduong', {
        template: ''+'<input type="hidden" value="">',
        controller: function($scope) {
            $scope.menu.page = 'index';
            $scope.$CFG.spinController = {};
            dialogCloseAll();
            /*Chạy biểu tượng tổng đâì hỗ trợ xuống góc bên phải*/
            angular.element(document).ready(function(){
                setTimeout(function(){
                    $('.content-info-help').addClass('content-info-help-running');
                },200);
            })
        }
    })
    .when('/dinhduong/view/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                'list.html'
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/dinhduong/view/:module/:action', {
        templateUrl: function(request){
            console.log(request);
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                'templates',
                $CFG.project,
                request.module,
                request.action+'.html'
            ];
            delete request.module;
            delete request.action;
            var req = ['?_='+Math.random().toString().split('.')[1]];
            for(var key in request) {
                req.push(key+'='+request[key]);
            }
            return urls.join('/')+req.join('&');
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose();
        }
    })
    .when('/dinhduong/:module/:action', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action
            ];
            return urls.join('/')+'/'+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose();
        }
    })
    .when('/dinhduong/:module', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                'list'
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .when('/dinhduong/:module/:action/:id', {
        templateUrl: function(request){
            statusloading('tmp_layout');
            var urls = [
                $CFG.remote.base_url,
                $CFG.project,
                request.module,
                request.action,
                request.id
            ];
            return urls.join('/')+'?_='+Math.random().toString().split('.')[1];
        },
        controller: function($scope,$routeParams) {
            $scope.menu.page = 'children';
            statusloadingclose('tmp_layout');
        }
    })
    .otherwise({redirectTo:'/dinhduong'})
}]).config(['$compileProvider', function($compileProvider){
    $compileProvider.debugInfoEnabled(true);
}]).run(['$rootScope',  function ($rootScope) {
    var element = $('#overlay_account_expiry_notification');
    var projectMenu = $('#project_menu_header');

    $rootScope.$on('$routeChangeSuccess', function($event, current) {
        if (angular.isUndefined(current.params.module)) {
            element.show();
            projectMenu.show();
        } else {
            element.hide();
            projectMenu.hide();
        }
    });
    }]);
