<div class="tbl_container" ng-controller="menu_adjustController" id="menu_adjustController">
	<div class="tbl-container-header header-kh-ct" id="tb_menu_adjust_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="menu_adjust"></div>
			<div class="function-kh-ct">
			<!-- <div class="header-search" id="header-search" > -->
				<span class="icon-searching glyphicon" ng-show="keysearch_group || keysearch_area || keysearch_category || keysearch_name" onclick="$.menu_adjust.cleanSearch()">
                	<span class="glyphicon glyphicon-filter hover-pointer" title="Bỏ tìm kiếm" >
                            <span class="glyphicon glyphicon-remove"></span>
                	</span>
                </span>
				<div style="float: left;">
					<input id="name"  style="width: 150px !important;height: 2.5rem;margin-top: 0.5rem;border-radius: 0px;margin-right: 0.8rem;font-size: 1.2rem;" class="form-control" placeholder="Tên món ăn" ng-model="selected.keysearch_name" ng-blur="reloadDatagrid()">
				</div>
				<form id="frm-storage" >
				    <div style="float: left;  margin-top: -0.35rem;" class="" >
				    	<label class="control-label">Tháng/năm </label>
				    	<select ng-model="selected.month" ng-options="item as item.name for item in menu_adjust.months track by item.id" ng-change="reloadDatagrid()">
				    		<option value="">Chọn tháng</option>
				    	</select>
				    </div>
				    				</form>
			<!-- </div> -->
									<div>
						<div class="dropdown dropdown-setting">
							<button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
								<i class="fa fa-cog fa-spin icon-setting"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-right h400 w300" style="overflow-y:auto;">
								<li>
									<input type="checkbox" title="Hiện thực đơn đã xóa" ng-model="menu_adjust.showDelete" ng-click="reloadDatagrid()"> Hiện thực đơn đã xóa
								</li>
								<li style="border-top:1px solid #ccc; padding:5px;">
									Tiền 1 trẻ mặc định trong CĐKP: <input type="text" placeholder="15000" inf-configs="sys.configs.cdkp_tien1tre_default" inf-id="cdkp_tien1tre_default" style="width: 80px; border: 1px dotted green;" >
								</li>
																								<li style="border-top:1px solid #ccc; padding-top:5px;">
									<input type="checkbox" title="Ẩn cột thời gian tạo/sửa" inf-configs="sys.configs.cdkp_hide_created_at" inf-id="cdkp_hide_created_at" ng-true-value="1" ng-false-value="0"> Ẩn cột thời gian Tạo/cập nhật CĐKP?
								</li>
								<li style="color:orange; border-top:1px solid #ccc; padding-top:5px;" ng-if="sys.configs.cdkp_edit_tpc_exist_stta !== undefined || 0">
									<input type="checkbox" title="Cho phép tích lại TPC ở CĐKP khi đã có STTA?" inf-configs="sys.configs.cdkp_edit_tpc_exist_stta" inf-id="cdkp_edit_tpc_exist_stta" ng-true-value="1" ng-false-value="0"> Cho phép tích lại TPC ở CĐKP khi đã có STTA?
								</li>
								<li style="color:orange; border-top:1px solid #ccc; padding-top:5px;" ng-if="sys.configs.pkc_allow_del_exist_stta !== undefined || 0">
									<input type="checkbox" title="Cho phép trường tự xóa Phiếu kê chợ ở Biểu mẫu thống kê khi đã có STTA?" inf-configs="sys.configs.pkc_allow_del_exist_stta" inf-id="pkc_allow_del_exist_stta" ng-true-value="1" ng-false-value="0"> Cho phép trường tự xóa Phiếu kê chợ ở Biểu mẫu thống kê khi đã có STTA?
								</li>
																															</ul>
						</div>
						<div class="btn-group" ng-if="menu_adjust.showDelete">
							<button type="button" onclick="$.menu_adjust.restore()" class="btn btn-primary">
								<span class="fa fa-undo"></span>Khôi phục
							</button>
						</div>
					</div>
																<div class="btn-group btn-head" ng-if="!menu_adjust.showDelete">
					<button type="button" ng-click="addjustForm()" class="btn btn-primary">
						<span class="glyphicon glyphicon-plus"></span>Cân đối thực đơn ngày
					</button>
										<button type="button" class="btn btn-primary" ng-disabled="0" onclick="$.menu_adjust.del()" title="Xóa các dòng đã chọn">
							<span class="glyphicon glyphicon-remove-circle"></span>Xóa
						</button>
													</div>
			</div>
			<div class="support-video">
                <a target="_blank" href="https://www.youtube.com/watch?v=XrjS-Na2Lcg">
                    <img src="http://localhost:3000/images/icon_hotro1.gif">
                </a>
            </div>
		</div>
	</div>
	<div id="tbl_menu_adjust"></div>
</div>
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/menu_adjust.css?=1547056525" />
<script src="http://localhost:3000/js/dinhduong/menu_adjust.js?_=809002882"></script>
<script type="text/javascript">
	$.menu_adjust.init('9');
	function editPKC() {
		if($('.datagrid-marketbill-editable').css('display') == 'none'){
			$(".datagrid-marketbill-editable").css("display", "block");
		}else{
			$(".datagrid-marketbill-editable").css("display", "none");
		}
	}
</script>
<style type="text/css">
	.container-price{
		display: inline-flex;
		padding: 1px 5px;
		border: 1px solid #bbb;
		background: #fff;
		border-radius: 3px;
		padding-right: 0px;
	}
	.menu_adjust_date {
		padding-bottom: 30px;
	}
	.container-price > input{
		width: 70px;
		padding: 0px;
		border: unset;
	}
	.container-price > label{
		border-left: 1px solid #ccc;
		padding: 2px 6px;
		margin: 2px;
		color: #969696;
	}
	.save_together_price-disable .uncheck, .save_together_price-disable .uncheck input{
		display: none;
	}
	#frm-storage{
		width: 35%;
		float: left;
		margin-top: 10px;
	}
	.support-video{
		/* width: 120px; */
		position: fixed;
		bottom: 4px;
		right: 0px;
		z-index: 6666;
	}
	#
</style> 
<style>
	/* Kiểu của Dropdown Button */
	.dropbtn-menu {
		padding: 4px 8px;
		color: #3a3a6b;
		font-size: 14px;
		border: none;
		cursor: pointer;
		border: 1px solid #86b4bc;
		border-radius: 25px;
		font-weight: 400;
		text-align: center;
		white-space: nowrap;
    	vertical-align: middle;
		background-color: #fff;
	}
	
	/* Vùng chứa <div> - cần thiết để định vị nội dung thả xuống */
	.dropdown {
	  position: relative;
	  display: inline-block;
	}
	
	/* Dropdown Content (ẩn bởi mặc định) */
	.dropdown-content {
	  display: none;
	  position: absolute;
	  background-color: #fff;
	  min-width: 160px;
	  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
	  z-index: 1;
	}
	
	/* Liên kết nằm trong dropdown */
	.dropdown-content a {
	  color: black;
	  padding: 12px 16px;
	  text-decoration: none;
	  display: block;
	  margin-top: 5px;
	}
	
	/* Thay đổi màu của liên kết thả xuống khi di chuột */
	.dropdown-content a:hover {
		background-color: #86b4bc;
	}
	
	/* Hiển thị dropdown menu khi di chột */
	.dropdown:hover .dropdown-content {
	  display: block;
	}
	
	/* Thay đổi màu nền của nút dropdown khi nội dung thả xuống được hiển thị*/
	.dropdown:hover .dropbtn {
	  background-color: #86b4bc;
	}
	.btn-border{
		border-radius: 1% !important;
	}
	</style>
