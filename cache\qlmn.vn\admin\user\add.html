<form class="form-horizontal" id="frm-login" role="form" >
				<div class="form-group" style="padding-top: 10px;">
			<div class="col-md-6">
				<label class="col-md-5 control-label color-orange">H<PERSON> tên</label>
				<div class="col-md-7">
					<input class="form-control" id="ten" name="Họ tên" width="100%" >
				</div>
			</div>
			<div class="col-md-6">
				<label class="col-md-5 control-label color-orange">T<PERSON><PERSON> k<PERSON>ản</label>
				<div class="col-md-7">
										<div class="user-admin-container" style="">
						<label id="user-admin">d.hd.mnquangvt.</label>
					</div>
										<input class="form-control" id="tai_khoan" width="100%">
				</div>
			</div>
		</div>
		<div class="form-group">
			<div class="col-md-6">
				<label class="col-md-5 control-label"><PERSON><PERSON><PERSON><PERSON> tho<PERSON>i</label>
				<div class="col-md-7">
					<input class="form-control" id="dien_thoai" width="100%" >
				</div>
			</div>
			<div class="col-md-6">
				<label class="col-md-5 control-label">Email</label>
				<div class="col-md-7">
					<input class="form-control" id="email" width="100%" >
				</div>
			</div>
		</div>
		<div class="form-group">
			
			<div class="col-md-6">
				<label class="col-md-5 control-label">Chọn khối</label>
				<div class="col-md-7">
					<select name="bloc_age_id" id="bloc_age_id" style="height: 31px;width: 164px;" onchange="changeBloc()">
						<option value="">-Chọn khối-</option>
																			<option value="1">Nh&agrave; trẻ 3-12 th&aacute;ng</option>
													<option value="2">Nh&agrave; trẻ 12-24 th&aacute;ng</option>
													<option value="3">Nh&agrave; trẻ 24-36 th&aacute;ng</option>
													<option value="4">Mẫu gi&aacute;o b&eacute; 3-4 tuổi</option>
													<option value="5">Mẫu gi&aacute;o nhỡ 4-5 tuổi</option>
													<option value="6">Mẫu gi&aacute;o lớn 5-6 tuổi</option>
																	</select>
				</div>
			</div>
			<div class="col-md-6">
							<input type="" hidden="" value="1" name="" id="diem_truong">
						</div>
		</div>
		<div class="form-group">
			<div class="col-md-6">
				<label class="col-md-5 control-label">Lớp học</label>
				<div class="col-md-7">
					<select name="course_id" id="course_id" style="height: 31px;width: 164px;">
						<option value="">Chọn lớp học</option>
																							</select>
				</div>
			</div>
			<div class="col-md-6">
				<label class="col-md-5 control-label"></label>
				<div class="col-md-7">
					<label class="checkbox-inline"> <input type="checkbox" id="status">Kích hoạt sử dụng</label>
				</div>
			</div>
		</div>
		<div class="form-group">
			<div class="col-md-6">
				<label class="col-md-5 control-label"></label>
				<div class="col-md-7">
					<label class="checkbox-inline"> <input type="checkbox" id="must_change_pass" checked="">Đổi mật khẩu lần đầu đăng nhập</label>
				</div>
			</div>
			<div class="col-md-6">
				<label class="col-md-5 control-label">Loại tài khoản</label>
				<div class="col-md-7">
					<select id="user_type" style="margin-top:8px;">
													<option value="0">(Trường) Gi&aacute;o vi&ecirc;n</option>
													<option value="1">(Trường) Quản trị</option>
											</select>
				</div>
			</div>
		</div>
		<div class="col-md-12" style="border-top: 1px dotted #666;padding-top: 15px;">
			<label class="col-md-12" style="text-align: center;">Chọn nhóm quyền</label>
		</div>
				<div class="form-group" id="container-tabs">
			<ul class="nav nav-tabs" style="margin-bottom: 10px;">
				<li class="active"><a data-toggle="tab" href="#dinhduong">C&ocirc;ng t&aacute;c b&aacute;n tr&uacute;</a></li>
			</ul>
			<div class="tab-content">
				<div id="dinhduong" class="tab-pane fade in active">
			    				    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="671">
				        	To&agrave;n quyền (b&aacute;n tr&uacute;)
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="672">
				        	Kế to&aacute;n Dinh dưỡng
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="673">
				        	Kế to&aacute;n Thu chi
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="674">
				        	Gi&aacute;o vi&ecirc;n
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="675">
				        	Bộ phận VP
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="847">
				        	Số phần
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="926">
				        	C&aacute;n bộ Y tế
				        </label>
				    </div>
			        			    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="992">
				        	Hiệu ph&oacute; Chuy&ecirc;n m&ocirc;n
				        </label>
				    </div>
			        			    </div>
			    			</div>
		</div>
				<div class="form-group" id="container-tabs">
			<ul class="nav nav-tabs" style="margin-bottom: 10px;">
				<li class="active"><a data-toggle="tab" href="#admin">Quản trị</a></li>
			</ul>
			<div class="tab-content">
				<div id="admin" class="tab-pane fade in active">
			    				    	<div class="col-md-4">
				        <label class="checkbox-inline>
				        	" 
				        	>
				        	<input id="user_permission_group" type="checkbox" value="777">
				        	Th&ecirc;m/Sửa Users
				        </label>
				    </div>
			        			    </div>
			    			</div>
		</div>
			</form>

<script type="text/javascript">
	var courses = [];
var unit_staffs = [];

    function changeUnitStaff(){
		var unit_staff_id = $("#unit_staff").val();
		if(unit_staff_id > 0 && unit_staffs[unit_staff_id]!=undefined){
			var staff = unit_staffs[unit_staff_id];
			$("#ten").val(staff.fullname);
			$("#tai_khoan").val(staff.fullname_en);
			$("#dien_thoai").val(staff.phone);
		}
	}
	function changeBloc(){
		var bloc_age_id = $("#bloc_age_id").val();
		var unit_staff_id = $("#unit_staff").val();
		if(bloc_age_id > 0){
			$("#course_id").html("");
			$("#course_id").append("<option>Chọn lớp học</option>");
			for(let v of courses){
				if(v.bloc_age_id == bloc_age_id){
					$("#course_id").append("<option value="+v.id+">"+v.name+"</option>");
				}
			}
		}else{
			$("#course_id").html("");
			$("#course_id").append("<option>Chọn lớp học</option>");
			for(let v of courses){
				$("#course_id").append("<option value="+v.id+">"+v.name+"</option>");
			}
		}
	}
</script>
<style type="text/css">

.user-admin-container{
	float: left;
	margin-top: 8px;
	margin-left: 3px;
	color: #bcbcbc;
}
label#user-admin{
	/*float: left;
	margin-top: 8px;
	position: absolute;
	margin-left: 3px;
	color: #bcbcbc;*/
}
</style>