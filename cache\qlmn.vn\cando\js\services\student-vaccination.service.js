(function (window, app) {
    'use strict';

    app.service('StudentVaccinationService', StudentVaccinationService);

    function StudentVaccinationService($http, $q, $httpParamSerializerJQLike, APP_CONFIG) {
        var self = this;

        self.fetchStudents = function fetchStudents(page, limit, filters) {
            page = page || 1;
            limit = limit || 15;
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-vaccinations';
            var options = {
                params: {filters: filters, page: page, limit: limit},
                paramSerializer: '$httpParamSerializerJQLike',
            };

            // FIXME: can we write a new better loading spinner service?
            statusloading((new Date()).getTime());

            return $http.get(url, options)
                .then(function (response) {
                    var students = _.get(response, 'data.students', []);
                    var total = _.get(response, 'data.total', 0);
                    var vaccinations = _.get(response, 'data.vaccinations', []);
                    var user = _.get(response, 'data.user', {});

                    return {
                        total: total,
                        students: students,
                        vaccinations: vaccinations,
                        user: user,
                    };
                })
                .catch(function () {
                    return {
                        total: 0,
                        students: [],
                        vaccinations: [],
                        user: {},
                    };
                })
                .finally(function () {
                    // FIXME: can we write a new better loading spinner service?
                    statusloadingclose();
                });
        };

        self.store = function store(studentId, vaccinationId, value, schoolYear, month, note = '') {
            statusloading((new Date()).getTime());
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-vaccinations';

            var data = {
                student_id: studentId,
                vaccination_id: vaccinationId,
                value: value,
                school_year: schoolYear,
                month: month,
                note: note
            };

            return $http.post(url, data, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function () {
                    return true;
                })
                .catch(function () {
                    return false;
                })
                .finally(function () {
                    statusloadingclose();
                });
            
        };

        self.detail = function store(studentId) {
            statusloading((new Date()).getTime());
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/student-vaccinations/detail';

            var data = {
                student_id: studentId
            };

            return $http.post(url, data, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}})
                .then(function (response) {
                    var data = _.get(response, 'data.data', []);
                    return {
                        data : data
                    };
                })
                .catch(function () {
                    return {
                        data : []
                    };
                })
                .finally(function () {
                    statusloadingclose();
                });
        };

        return self;
    }

    StudentVaccinationService.$inject = [
        '$http',
        '$q',
        '$httpParamSerializerJQLike',
        'APP_CONFIG',
    ];

})(window, window.angular_app);
