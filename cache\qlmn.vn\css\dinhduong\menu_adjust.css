.layout-accordion{
	width: 100%;
	min-height: 300px;
}
.modal-body{
	padding: 0px;
}
.layout-accordion > .col{
	height: 100%;
}
.layout-accordion .col-left label {
    margin-bottom: 0px;
}
.layout-accordion .table{
	/*background: #fff;*/ 
	margin-bottom: 0px;
}
#col-left-hidden{
	padding-left: 35px;
	position: relative;
}
#col-left-hidden > .col-left{
	position: absolute;
	left: 0;
	top: 0;
	width: 35px;
	padding: 0px;
}
#col-left-hidden > .col-left .panel-content{
	display: none;
}
.layout-accordion .table > thead{
	border-bottom: 2px solid #ddd;
    /*font-size: 11px;*/
}
.layout-accordion .table > thead th{
	text-align: center;
}
.layout-accordion .table .row-head-number th.col-head {
    height: 25px;
    font-weight: initial;
}

.layout-accordion .table>tfoot>tr>td, 
.layout-accordion .table>tfoot>tr>th{
    padding: 3px;
}

.layout-accordion .table>thead>tr>td,
.layout-accordion .table>thead>tr>th {
    padding: 3px;
    border-bottom-width: 1px;
    border-left: 1px solid #ccc;
}

.layout-accordion .table.food-lists{
	border-bottom: 1px solid #ccc;
	border-right: 1px solid #ccc;
}
.layout-accordion .table.food-lists>tbody>tr#title span.icon{
	position: absolute;
    height: 24px;
    width: 20px;
    left: 7px;
    bottom: 0;
    background-size: 82% !important;
    background-repeat: no-repeat !important;
    background-position-y: 6px !important;
}
.layout-accordion .table.food-lists>tbody>tr#title span.icon.icon-1{
	background: url("../../images/khosang-icon.png");
}
.layout-accordion .table.food-lists>tbody>tr#title span.icon.icon-2{
	background: url("../../images/khotrua-icon.png");
}
.layout-accordion .table.food-lists>thead{
	border-top: 1px solid #ccc;
}
.layout-accordion .table.food-lists>tbody tr#title td{
	border-left:0px; 
	position: relative;
	padding-left: 3px;
}
.layout-accordion .table.food-lists>tbody tr#item{
	/*background: #fff;*/
}
.layout-accordion .table.food-lists>tbody div.btn-panel{
	float: left; padding-left: 15px;
	display: none;
}
.layout-accordion .table.food-lists>tbody:hover div.btn-panel{
	display: block;
}
.layout-accordion .table.food-lists>tbody.selected tr#title{
	
}
.layout-accordion .table.food-lists>tbody.selected tr#item{
	/*border-left: 2px solid rgb(253, 73, 73);*/
}
.layout-accordion .container-table .table{
	/*-webkit-box-shadow: inset 0px 0px 3px 2px rgba(117,117,117,0.66);
	-moz-box-shadow: inset 0px 0px 3px 2px rgba(117,117,117,0.66);
	box-shadow: inset 0px 0px 3px 2px rgba(117,117,117,0.66);*/
	border:1px solid #ccc;
}
.layout-accordion .table>tbody>tr>td, 
.layout-accordion .table>tbody>tr>th{
	padding: 0px;
    border-left: 1px solid #ccc;
}
.layout-accordion .table>tbody>tr .cell-label-container {
    width: 100%;
    height: 100%;
    min-height: 18px;
    padding: 3px 3px 0px 3px;
    /*font-size: 11px;*/
}
.layout-accordion .table tbody td{
	vertical-align: middle;
}
.layout-accordion .col-right{
	background: #ECECEC;
	border-left: 1px solid #aaa;
}
.layout-accordion .cell-right-top{
	background: #fff none repeat scroll 0 0;
    border-bottom: 1px solid #ccc;
    padding-top: 10px;
}
.layout-accordion .panel-heading{
	margin-top: 0px;
	min-height: 28px;
	color: #fff;
    background-color: #7ac54b;
}
.layout-accordion h4.panel-title{
	font-size: 14px;
	font-weight: unset;
	padding: 5px;
}
.layout-accordion .table tr.row-head.row-head-title {
    background: #e5f6d9;
}
.layout-accordion{}
.layout-accordion{
	font-size: 14px;
}
.layout-accordion .table.thanhphandinhduong{
	background: #fff;
	border: 1px solid #ccc;
}
.layout-accordion .table.thanhphandinhduong tbody tr td .cell-label-container{
	text-align: center;
}
.layout-accordion .table.thanhphandinhduong tbody tr td.col-1 .cell-label-container{
	text-align: left;
}
.layout-accordion .table.thanhphandinhduong tbody td.col-1 > .cell-label-container{
	font-weight: bold;
}
.layout-accordion input[type="number"]{
	padding-right: 0;
}
.layout-accordion label.control-label:after{
	content: ":";
}
.layout-accordion input.input-edit-field{
	padding: 0px 2px;
    height: 20px;
    /*font-size: 11px;*/
    width: 100%;
    border: none;
}
.layout-accordion input.input-edit-field:focus{
	outline: red;
	outline-width: 1px;
	outline-style: dotted; 
}
.layout-accordion input.form-control{
	padding: 3px;
    height: 22px;
    /*font-size: 11px;*/
    width: 100%;
    padding-top: 4px;
}
.no-padding{
	padding: 0;
}
.no-padding-left{
	padding-left: 0; 
}
.no-padding-right{
	padding-right: 0; 
}
tr.row-editabled td{
    background: #edffa5;
}
.cell-edit-container{
	/*display: none;*/
}
.layout-accordion tr td{
	padding: 0px;
}
td#cell-editabled{
	position: relative;
}
tr.row-editabled td#cell-editabled .cell-edit-container{
	display: block;
}
tr.row-editabled td#cell-editabled .cell-label-container{
	display: none;
}
.layout-accordion td.congthuc-calo-container{
	padding: 5px;
}
.layout-accordion td.congthuc-calo-container > input.input-edit-field{
	height: 24px; width: 50px;border-radius: 15px; text-align: center;
}
.form-grade {
    margin-bottom: 10px;
}
.col-right>.form-grade{
	padding-right: 10px;
	padding-left: 10px;
}
.table-title-thanhphandinhduong{
	text-align: left;
}
.frm-group {
    margin-bottom: 0px;
}
.panel-button-top{
	padding: 0px;
	margin: 5px 0;
}
.panel-button-top button{
	border: none;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 13px;
    color: #fff;
    margin-right: 10px;
}
.group-list-inserted{
	
}
.group-list-inserted>.btn-delete-group{
	left: 1px;
    /*position: absolute;*/
    padding: 1px 0;
    font-size: 14px;
}
.group-list-inserted:hover .btn-delete-group{
	display: block;
}
.btn-delete-group:hover span{
	background: #ccc;
}
#nhomtrecandoi{
	min-height: 22px;
}
#nhomtrecandoi > .btn-add{
	position: absolute;
    right: 0px;
    font-size: 14px;
    padding: 3px;
    margin-top: -7px;
}
#nhomtrecandoi > .btn-add:hover{
	background: #ccc;
}
.table.food-lists tbody tr#title{
	background: #f6f6f6;
}
.table.food-lists tbody tr td.col-1 .btn-inline{
	display: none;
	font-size: 13px;
	color: red;
}
.table.food-lists tbody tr:hover td.col-1 .btn-inline{
	display: block;
}
.table.food-lists tbody tr:hover td.col-1 .col-numner{
	display: none;
}
.title-bua-an{
	font-size: 12px;
    font-weight: bold;
    border-bottom: 1px solid #ccc;
    padding: 3px;
    background: rgb(235, 239, 234);
}
.col.panel-group.col-left{
	padding: 0;
}
.row-dinhmuc1ngay,.row-cocauapdung {
	background: rgb(176,196,222);
}
.row-tiledat,.row-tileplg {
	background: rgb(255, 209, 34);
}
#thongtinthucdon{
	padding: 0 5px;
}
.tbl-danhgiabuaan thead,.tbl-danhgiabuaan tbody{
	border: 1px solid #ccc;
}
.tbl-danhgiabuaan thead tr{
	background: #eef6fd;
}
.tbl-danhgiabuaan tfoot td{
	border: 0px !important;
}
.table.food-lists-of-dish{
	border: 1px solid #ccc;
	font-size: 12px;
}
.table.food-lists-of-dish tr td{
	padding: 3px !important;
}
.table.food-lists-of-dish thead tr th, .table.food-lists-of-dish tbody tr td{
	border-right: 1px solid #ccc;
}
.table.food-lists-of-dish thead tr th{
	text-align: center;
}
ul.food-of-meal{
	-webkit-padding-start: 16px; /*Chrome*/
	padding-inline-start: 16px;	/*Firefox*/
}
ul.food-of-meal li{
	padding-right: 33px;
	position: relative;
	list-style-type: decimal !important;
	padding-bottom: 3px;
}
ul.food-of-meal li .panel-button{
	width: 32px;
    position: absolute;
    top: 0;
    right: 3px;
    text-align: right;
}
label.fa-icon-checkbox{
	margin-top: 2px;
}
.form-group.container-warehouse{
	text-align: center;
}
.tbl-balance .col1, .tbl-balance .col2, .tbl-balance .col3 {
    border-right: 1px solid #ccc;
}
.tbl-balance {
    border: 1px solid #ccc;
}

.color-inventory{
	color: #7ac54b;
}
.color-inventory-empty{
	color: #ff6060;
}
.color-inventory-warning{
	color: #ffe200;
}
.bg-color-inventory{
	background: #7ac54b;
}
.bg-color-inventory-empty{
	background: #ff6060;
}
.bg-color-inventory-warning{
	background: #ffe200;
}
.table.food-lists th, .table.food-lists td {
    padding: 0 3px;
    text-align: center;
    vertical-align: middle;
}
.del-food:hover{
	cursor: pointer;
	color: red;
}