.hightlight-lowgray > tr:nth-child(2n+1){
	background: #f5f5f5;
}
.hightlight-middlegray > tr:nth-child(2n+1){
	background: #dcdcdc;
}
.hightlight-gray > tr:nth-child(2n+1){
	background: #ccc;
}
.only-print{
    display: none;
}
.only-print-2{
    display: none;
}
.text-report{
    font-size: 15px;
}
.container-print{
    width: 100%;
    padding: 40px 30px 0px 30px;
    font-size: 17px;
}
.text-left1{
    text-align: left;
}
.text-center1{
    text-align: center;
}
.text-uppercase{
    text-transform: uppercase;
}
.title-report{
    text-align: center;
    font-weight: bold;
    font-size: 22px;
    text-transform: uppercase;
}
.text-bold{
    font-weight: 700;
}
@media print{
    .print-hidden{
        display: none;
        height: 0;
    }
    .fontsize-12{
        font-size: 12px !important;
    }
    .fontsize-13{
        font-size: 13px !important;
    }
    .text-report{
        font-size: 13px !important;
    }
    .fontsize-14{
        font-size: 14px !important;
    }
    .fontsize-16{
        font-size: 16px !important;
    }
    .title-report{
        font-size: 16px !important;
    }
    .only-print{
        display: block;
    }
    .only-print-2{
        display: inline-block;
    }
    .container-print{
        padding: 20px 20px 20px 25px;
    }
}
.draggable-container{
    width: auto;
    padding-right: 30px;
    /*height: 33px;*/
    position: fixed !important;
    top: 0;
    left: 30px;
    background-image: url("../../images/move-icon.png");
    background-size: 25px;
    background-repeat: no-repeat;
    background-position: right;
    z-index: 9999;
}
/*Style new print*/


.btn-by-vt{
    color: #2d752e;
    background-color: #f7dddd !important;

}
.btn-by-vt span{
    margin-right: 5px;
}
.btn-by-vt:hover {
    color: red;
}
.btn-fix-style1{
    position: fixed;
    top:2px;
    left:2px;
    z-index: 5555;
}
.header-print{
    width: 100%;
    display: inline-block;
}
/*Style table*/
.td-w-3{
    width: 3%;
}
.td-w-4{
    width: 4%;
}
.td-w-5{
    width: 5%;
}
.td-w-6{
    width: 6%;
}
.td-w-7{
    width: 7%;
}
.td-w-8{
    width: 8%;
}
.td-w-9{
    width: 9%;
}
.td-w-10{
    width: 10%;
}
.td-w-12{
    width: 12%;
}
.td-w-14{
    width: 14%;
}
.td-w-15{
    width: 15%;
}
.td-w-17{
    width: 17%;
}
.td-w-18{
    width: 18%;
}
.td-w-20{
    width: 20%;
}
.td-w-22{
    width: 22%;
}
.td-w-25{
    width: 25%;
}
.td-w-26{
    width: 26%;
}
.td-w-28{
    width: 28%;
}
.td-w-30{
    width: 30%;
}
.style-title-report{
    text-align: center;
    font-weight: bold;
    font-size: 22px;
}

.no-padding{
    padding: 0px;
}
.drag-new-style{
    border: 1px solid #DDD;
    border-radius: 5px;
    background-color: #e3fdf8;
}
.mg-right-5{
    margin-right: 5px; 
}
.btn-print{
    padding: 4px 11px !important;
}
.print-style-radio{
    padding: 0px 5px 0px 5px;
    float: left;
    line-height: 30px;
}
.print-style-radio label{
    padding: 0px;
}
input.print-hidden, .print-hidden input[type="text"]{
    border: 1px dotted !important;
    color: #7ac54b;
}
textarea.print-hidden{
    border: 1px dotted !important;
    color: #7ac54b;
}
.hover-pointer,.panel-body > .combobox-item{
    cursor: pointer;
}

.btn-color-gray{
    color: gray;
    cursor: pointer;
}
.btn-color-red{
    color: red;
    cursor: pointer;
}
.btn-color-green{
    color: #7ac54b;
    cursor: pointer;
}
.btn-color-blue{
    color: #428bca;
    cursor: pointer;
}
.btn-over-red:hover, .btn-over-red:hover *{
    cursor: pointer;
    color: red !important;
}
.setting-container{
    width: 30px; 
    height: 30px; 
    padding: 5px; 
    position: absolute; 
    right: 100%;
    z-index: 200;
}
.setting-container .form-group{
    display: inline-block;
    margin: 7px 0;
}
.fa-spin{
    width: 17px; 
    height: 19px; 
    font-size: 20px;
}
.icon-setting[aria-expanded="true"]{
    color: #2a99f9;
}
#setting.panel-collapse .panel.panel-default{
    border: 3px solid #2a99f9;
}
#setting.panel-collapse .panel-title{
    padding: 10px;
    height: 34px;
    background: #2a99f9;
}
.table-signature{
    width: 100%;
    border: 0;
}
.table-signature td{
    border: 0;
}
.table.noboder{
    border: none;
    /*border-color: rgba(0,0,0,0);*/
}
.table.noboder th,
.table.noboder td{
    border: none;
    /*border-color: rgba(255,255,255,0);*/
    padding: 0;
}