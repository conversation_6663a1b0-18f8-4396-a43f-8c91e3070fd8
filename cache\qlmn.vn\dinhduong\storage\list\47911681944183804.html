<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_storage_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="storage"></div>
			<div class="function-kh-ct">
				<form id="frm-storage">
                    <select ng-options= "item as item.name for item in storage.warehouses track by item.id" ng-model="storage.warehouse" id="warehouse_id" ng-change="storage.warehouseChange()" style="height: 25px;" class="btn-small">
                        <option value="">Tất cả</option>
                    </select>
                                                                        <input type="" name="school_point" id="school_point" value="1" hidden="" ng-model="storage.school_point">
                                                                <select ng-options= "item as item.name for item in storage.suppliers track by item.id" ng-model="storage.supplier" id="supplier_id" ng-change="storage.supplierChange()" style="height: 25px; width:120px;" class="btn-small">
                        <option value="">-Tất cả NCC-</option>
                    </select>
                    <div class="time-warehousing">
                        <span class="time-text mR0">Thời gian</span>
                        <input date-box="storage.start" style="width: 80px;"
                               title="từ ngày" class="input-custom-style"/>
                        <span class="line"></span>
                        <input date-box="storage.end" style="width: 80px;"
                               title="đến ngày" class="input-custom-style"/>

                        <input ng-model="storage.keysearch"
                               class="form-control search-input-warehouse"
                               placeholder="Tên thực phẩm hoặc mã thực phẩm">
                        <button class="h25" ng-click="storage.warehouseSearch()">Tìm kiếm</button>
                    </div>
                    <span class="container-warehouse" ng-click="storage.filterDateChange()">
                        <span class="icon icon-filter-enable" title="Lọc dữ liệt theo ngày" ng-show="!storage.opt_searching_date"></span>
                        <span class="icon icon-filter-disable" title="Bỏ lọc dữ liệu theo ngày" ng-show="storage.opt_searching_date"></span>
                    </span>
                    <div class="btn-group" ng-if="!sys.configs.cfg_point_storage_1 || sys.configs.cfg_point_storage_1==1">
                        <button type="button" ng-click="storage.formAddStorage()" ng-disabled="0" class="btn btn-primary"><span class="glyphicon glyphicon-plus"></span>Thêm</button>
                        <button type="button" class="btn btn-primary" onclick="$.storage.delMultiple()">
                            <span class="glyphicon glyphicon-remove-circle"></span>Xóa
                        </button>
                        <a ng-if="storage.date_select" class="btn btn-primary" id="export_print" type="button" href="http://localhost:3000/dinhduong/storage/exportPhieuNhapKho?date={{storage.date_select}}&supplier={{storage.supplier.id}}&warehouse_id={{storage.warehouse.id}}" target="_blank">
                            <span class="glyphicon glyphicon-export"></span>In
                        </a>                        
                    </div>
                    <div id="export-dialog" style="display: none;">
                        <div class="col-md-12" style="padding: 10px;">
                            <div class="col-md-5">
                                <div class="row">
                                    <span>Chọn kho : </span>
                                    <select id="export_type" style="height: 25px;">
                                        <option value=""> Tất cả </option>
                                        <option value="1"> Kho sáng </option>
                                        <option value="2"> Kho trưa </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4" style="text-align: center;">
                                <input id="ngay_xuat" data-options="formatter:myformatter,parser:myparser">
                            </div>
                            <div class="col-md-3">
                                <div id="btn-inphieuthu" class="col-md-6">
                                    <a class="btn btn-info" ta href="http://localhost:3000/storage/exportPhieuNhapKho?date=" target="_blank">
                                        <span class="glyphicon glyphicon-print fa-2x" style="font-size: 20px;margin-top: 3px;"></span>
                                    </a>
                                </div>
                                <div id="btn-xuatfile" class="col-md-6">
                                    <a id="btn_export" title="Xuất file excel"><img src="../images/icons/exel-icon2.png" style="width: 27px;"></a>
                                </div>
                            </div>
                        </div>
                    </div>
				</form>

			</div>
            <div class="support-video">
                <a target="_blank" href="https://www.youtube.com/watch?v=jzPSNGAIW2c">
                    <img src="http://localhost:3000/images/icon_hotro1.gif">
                </a>
            </div>
		</div>
	</div>
	<div id="tbl_storage"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/storage.js"></script>
<script type="text/javascript">
    $.storage.init();
	function changeStyle(){
		var so_luong = $('#so_luong').val();
        var warehouse = $('#warehouse').val();
		var ngay_nhap_kho = $('#ngay_nhap_kho').val();
        var ma_thuc_pham = $('#ma_thuc_pham').val();
		var don_gia = $('#don_gia').val();
		if(so_luong > 0 && warehouse !='' && ngay_nhap_kho !='' && ma_thuc_pham !='' && don_gia != ''){
			$('.btn-add.add-new').css('opacity',1);
		}else{
			$('.btn-add.add-new').css('opacity',0.3);
		}
	}
	
    var url = $CFG.remote.base_url +"/doing/dinhduong/storage/";
	$.dm_datagrid.combobox('ma_thuc_pham',url+'foods',{
        valueField: 'id',
        textField: 'name',
        value:[],
        mode: 'remote',
        onSelect: function(row, element) {
            $('input#don_gia').val(row.price);
        	$('input#don_gia2').val(digit_grouping(row.price));
        	$.each($.storage.measures, function(index,item){
        		if(item.id == row.measure_id) {
        			$('#label-measure').html(item.name);
        			return;
        		}
        	});
            $("#nha_cung_cap").combobox('reload',url+'suppliers?id='+row.supplier);
            $('#nha_cung_cap').combobox('select',row.supplier);
            changeStyle();
        },onLoadSuccess: function(data) {
        	
        },
        queryParams: {},
        width: 150,
        height: 25
    });
    $("#nha_cung_cap").combobox({
        valueField:'id',
        textField:'name',
        panelHeight:'auto',
        mode: 'local',
        onSelect: function(row, element) {
        },
        queryParams: {},
        width: 100,
        height: 25
    });
    var date = new Date();
    var dd = [date.getDate(),(date.getMonth()+1),date.getFullYear()];
    $.storage.keysearch_date = date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
    $('.btn-export').on('click', function () {
        var month = $("input[name='month']").val();
        var year = $("input[name='year']").val();
        if(month === '' || year === '')
            alert("Dữ liệu nhập không được trống!");
        else{
            var url = $CFG.remote.base_url+'/'+$CFG.project+'/storage/excelGetListMoney?month='+ month + '&year=' + year;
            $(this).attr('href', url);
        }
    });

</script>
<style type="text/css">
#frm-storage .container-warehouse{
    position: relative;
    display: inline-flex;
    height: 20px;
    width: 20px;
}
#frm-storage .icon{
    height: 20px;
    width: 20px;
    display: inline-block;
    position: absolute;
    margin-top: 7px;
    margin-left: 2px;
}
.container-price{
	display: inline-flex;
	padding: 1px 5px;
	border: 1px solid #bbb;
	background: #fff;
	border-radius: 3px;
	padding-right: 0px;
	height: 25px;
}
.container-price > input{
	width: 70px;
	padding: 0px;
	border: unset;
}
.container-price > label{
	border-left: 1px solid #ccc;
	padding: 2px 6px;
	margin: 2px;
	color: #969696;
}
#frm-storage #opt-searching-enable{
    float: right;
    margin-top: 5px;
}
#frm-storage{
	margin-top: 10px;
	margin-bottom: 10px;
    padding: 0 5px;
}
.btn-add{
	height: 25px;
	padding: 3px 12px;
	background: #69adde;
	margin-bottom: 1px;
	outline: none;
	color: #FFF;
	opacity: 0.3; 
}
.btn-add span{
	color: #FFF !important;
}

.container-field-measure input.form-control{
    padding: 3px;
    height: 22px;
    font-size: 11px;
    width: 100%;
    padding-top: 4px;
    height: 25px;
}
.container-field-measure{
    position: relative;
}
.container-price-new{
    margin-right: 5px;
}
#btn-xuatfile:hover{
    cursor: pointer;
}

.btn-small {
    display: inline-block;
    padding: 2px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
.time-warehousing {
    display: inline-block;
    margin-left: 20px;
}
.time-warehousing span.time-text{
    font-weight: bold;
    font-size: 1.1em;
    margin-right: 15px;
}
.time-warehousing span.textbox {
    margin: 0px 5px;
}
span.line {
    display: inline-table;
    width: 10px;
    height: 1px;
    background: black;
    margin-bottom: 3px;
}
.time-warehousing .search-input-warehouse {
    display: inline-block;
    width: 145px;
    height: 27px;
    border-radius: 0px;
    font-size: 11px;
}
.time-warehousing .h25 {
    margin-left: 5px;
    background: white;
    border: 1px solid #c6c6c6;
   // outline: none;
}
</style>

