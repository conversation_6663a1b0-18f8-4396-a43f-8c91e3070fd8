<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
    <meta charset="utf-8">
    <base href="http://localhost:3000/single/">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
    	<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
    <!-- Fonts -->
    <link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="http://localhost:3000/build/build/css/style.min-b0affb6d2f.css?v=3">
    <link rel="stylesheet" href="http://localhost:3000/css/common.css?=533775794">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!-- 1. Load libraries -->
    <!-- Polyfill for older browsers -->
    <!-- 2. Configure SystemJS -->
    <!-- Google Tag Manager -->
    <script type="text/javascript">
        var _gaq = _gaq || [];
                _gaq.push(['_setAccount', 'UA-*********-3']);
                _gaq.push(['_trackPageview']);
        (function () {
            var ga = document.createElement('script');
            ga.type = 'text/javascript';
            ga.async = true;
            ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ga, s);
        })();
    </script>
    <script type="text/javascript">
        $CFG = {
			co_cau: 'cocau_chuan',
            co_cau_from: '01/01/2010',
            local: {base_url: "http://localhost:3000/js/admin"},
            remote: {base_url: "http://localhost:3000"},
            template: {base_url: "http://localhost:3000/templates"},
            balance_api: 'http://localhost:3000/api/balancer/calculate',
            project: 'dinhduong',
            namhoc: '2026',
            round_number_config: "",
            digit_grouping_char: "",
            spinController: {},
            unit_id: parseInt('51461'),
            admin: parseInt('1'),
            administrator: parseInt('0'),
            self_id: '',
            username: 'd.hd.mnquangvt',
            level: '4',
            is_skyline: '',
            province: parseInt('97'),
			district: '97_974',
            user: {
                name: '',
                phone: '',
                email: '',
            },
            school_point: +'1',
            school_points: +'1',
            school_point_together: +'0',
            is_vin: +'false',
            record_delete_show_captcha: +'2',
            dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin-top: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			marked_close_notifier: '1',
            api_base_url_ura: 'https://ura.edu.vn/api/v1/'
        };
    </script>
    <script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/library/moment.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/socket.io.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/load-image.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/uploader/js/canvas-to-blob.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/angular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/js/textAngular.min.js"></script>
    <script type="text/javascript" src="http://localhost:3000/build/build/js/vendor.min-77199f347f.js"></script>
                        <script type="text/javascript" src="http://localhost:3000/js/common.js?_=532325997"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/init_angular.js?_=1725651735"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/my-angular.js?_=564076327"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/main-angular.js?_=661891770"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/balance_money.js?_=1664866072"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/balance.js?_=928824752"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/index.js?_=13962911"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/payout.js?_=1627249681"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/export.js?_=1708788461"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/receipt.js?_=228376209"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_adjust-controller.js?_=1141773464"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_planning-controller.js?_=1384657625"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/dinhduong/menu_adjust/total-group-meal-controller.js?_=785301218"></script>
                    <script type="text/javascript" src="http://localhost:3000/js/library.js?=1954196254"></script>
</head>
<body ng-app="angular_app" ng-controller="appController" style="background:none">
<div class="full-container" ng-controller="mainContentController" id="mainContentController">
    <div class="angular-view-container" ng-view="" ng-show="menu.page!='index'" abc style="padding-top:0!important;padding-left: 1px">
    </div>
</div>

<!-- Hiện báo đang xử lý -->
<div id="statusloading-mash" style="display: unset;">
    <div class="spinner-container-loading">
        <div class="spinner-container btn-hover spinner3" id="spinner-container">
            <div id="time-progress-bar"></div>
        </div>
        <span class="spinner-text">Đang xử lý ...</span>
    </div>
</div>
<style type="text/css">
    .li-eye-image img {
        width: 17px;
    }
	.expire_soon_notify {
		-webkit-animation: my 700ms infinite;
		-moz-animation: my 700ms infinite;
		-o-animation: my 700ms infinite;
		animation: my 700ms infinite;
	}
</style>
<script type="text/javascript" src="http://localhost:3000/js/dinhduong/thong_bao.js?1754640151"></script>
<script>
    $('body').on('click', '.panel button.captcha-refresh-btn', function () {
        var img = $(this).prev();
        $(this).prev().prop('src', img.prop('src').toString().replace(/t=(.*?)&/, 't=' + (new Date()).getTime() + '&'));
    });
			//form_edit_pass();
	</script>
</body>
</html>
