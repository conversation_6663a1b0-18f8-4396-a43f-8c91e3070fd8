<link rel="stylesheet" type="text/css" ng-href="http://localhost:3000/css/dinhduong/menu_report.css?_=376" />
<link rel="stylesheet" type="text/css" ng-href="http://localhost:3000/css/dinhduong/config_holiday.css?=376" />
<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_menu_report_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="menu_report"></div>
		</div>
		<div class="function-kh-ct head-gum" >
            <div class="date-gum">
				<label>Tháng: </label>
                <select ng-options="item as (item.month<10?'0'+item.month:item.month)+'/'+item.year for item in menu_report.months track by item.month" ng-model="menu_report.selected.month" style="width: 150px;height: 25px;text-align: center;" ng-change="menu_report.monthChange()" class="btn-small"></select>
                <span class="fa fa-refresh btn-over-red" title="Tải lại tháng {{menu_report.selected.month.month}}" ng-click="menu_report.monthChange()"></span>
            </div>
            <div class="btn-group" style="position: absolute;right: 4px; top: 0;">
            	<ul class="nav navbar-nav">
					<li class="dropdown">
						<a href="{{$CFG.remote.base_url+'/'+$CFG.project+'/menu_report/printForm_Thucdontuan?type=1'}}" target="_blank">
							<i class="color-red fa fa-calendar" title="Xem và in"></i> Thực đơn tuần
						</a>
					</li>
					<li class="dropdown">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
							<span class="glyphicon glyphicon-print"></span> 
							Sổ mẫu <span class="caret"></span></a>
						<ul class="dropdown-menu">
							<li title="">
            					<a href="http://localhost:3000/documents/sys_templates/so/so_kiem_thuc_3_buoc.xlsx?_=1607480915" title="">Kiểm thực 3 bước</a>
							</li>
							<li title="">
            					<a href="http://localhost:3000/documents/sys_templates/so/so_tinh_khau_phan_an.doc?_=1365623689" title="">Tính khẩu phần ăn</a>
            				</li>
							<li title="">
            					<a href="http://localhost:3000/documents/sys_templates/so/so_quan_ly_bua_an_cbgvnv.docx?_=1018965562" title="">QL bữa ăn CBGVNV</a>
            				</li>
							<li ng-if="0" style="color:orange;padding-left: 21px;border-top: 1px solid #ccc;padding-top: 5px;">
            					<input type="checkbox" inf-configs="sys.configs.stta_bkthucdon_report" inf-id="stta_bkthucdon_report" ng-true-value="1" ng-false-value="0"> Mở thêm mẫu Bảng kê thực đơn trong STTA</span>
            				</li>
							<li ng-if="0" style="color:orange;padding-left: 21px;border-top: 1px solid #ccc;padding-top: 5px;">
            					<input type="checkbox" inf-configs="sys.configs.stta_not_re_calculate_surplus_end" inf-id="stta_not_re_calculate_surplus_end" ng-true-value="1" ng-false-value="0"> Ko tính lại số dư cuối ngày trong STTA theo từng thực phẩm nếu đã tính STTA</span>
            				</li>
            				<li style="color:orange;padding-left: 21px;border-top: 1px solid #ccc;padding-top: 5px;">
            					<input type="checkbox" inf-configs="sys.configs.pkc_detach_group_kt3b" inf-id="pkc_detach_group_kt3b" ng-true-value="1" ng-false-value="0"> Tách nhóm trẻ trước khi xem/in PKC, Kiểm thực 3 bước</span>
            				</li>
            				<li style="color:orange;padding-left: 21px;border-top: 1px solid #ccc;padding-top: 5px;">
            					<input type="checkbox" inf-configs="sys.configs.pkc_show_bangkecho" inf-id="pkc_show_bangkecho" ng-true-value="1" ng-false-value="0"> Mở thêm mẫu Bảng đi chợ trong danh sách PKC</span>
            				</li>
            			</ul>
            		</li>
            	</ul>
            </div>
		</div>
		<div class="support-video">
            <a target="_blank" href="https://www.youtube.com/watch?v=fkFepsrN6OA">
                <img src="http://localhost:3000/images/icon_hotro1.gif">
            </a>
        </div>
	</div>

	<div class="noidung content-menu-report row">
		<!-- <div class="row" style="height: 100%;"> -->
			<div class="col-md-4 col-sm-4 col-xs-12 menu-report-phieukecho col-reports">
				<div class="motkhoi">
					<div class="tdkhoi">PHIẾU KÊ CHỢ</div>
					<div class="ndkhoi tr-noidung tr-nd-select">
						<table class="table-maket-menu" style="width: 100%">
							<tbody>
								<tr style="height: 30px" ng-repeat="(key, market) in menu_report.marketbill" title="Tạo lúc {{market.is_bill == 1 ? market.updated_at : ''}}">
									<td class="td-date" style="width: 75px;">
										<span ng-bind="market.date"></span>
									</td>
									<td style="text-align: right;">
										<button type="button" class="btn-link btn-outline" ng-click="menu_report.phieukecho(market,1)" title="Xem phiếu trước đi chợ">
		                                    <i class="fa fa-eye" aria-hidden="true"></i></span> Xem
		                                </button>
		                            </td>
		                            <td style="width: 70px;">
												                            	<button ng-if="market.is_bill == 1" type="button" class="btn-link btn-outline hover-pointer color-red btn-over-red" ng-click="menu_report.delphieukecho(market.date,market)" title="Xóa và cập nhật lại phiếu kê chợ từ điều chỉnh">
		                                    <span class="glyphicon glyphicon-trash"></span> Xóa
		                                </button>
		                                		                            </td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-sm-4 col-xs-12 menu-report-sotinhtienan col-reports">
				<div class="motkhoi">
					<div class="tdkhoi">
						<div style="text-align: center;">SỔ TÍNH TIỀN <span ng-click="menu_report.unlock=true">ĂN</span></div>
						<div style="padding: 9px;font-size: 13px;">
							<ul class="nav navbar-nav">
								<li class="dropdown">
									<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true" style="padding: 0px;">
										<span class="glyphicon glyphicon-print"></span>
										Biểu tổng hợp <span class="caret"></span>
									</a>
									<ul class="dropdown-menu">
										<li title="In biên lai cho danh sách lọc">
											<a target="_blank" class="btn-link btn-outline" href="" ng-click="cash_book.form_theodoichatluongbuaan()" title="Tổng hợp theo dõi chất lượng bữa ăn cả tháng">
					                            Theo dõi chất lượng bữa ăn
					                        </a>
										</li>
										<li>
											<a target="_blank" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=4&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Chất lượng bữa ăn - Kiểm kê cuối tháng">
					                            Chất lượng bữa ăn - Kiểm kê cuối tháng
					                        </a>
										</li>
										<li>
											<a target="_blank" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=5&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Quyết toán tiền ăn cuối tháng">
					                            Quyết toán tiền ăn
					                        </a>
										</li>
										<li ng-if="['40','22','33'].includes($CFG.province) || $CFG.administrator == 1" title="NA">
											<a target="_blank" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=8&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Quyết toán tiền ăn cuối tháng">
					                            Quyết toán tiền ăn chi tiết (6.1E)
					                        </a>
										</li>
										<li>
											<a target="_blank" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=7&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Thống kê tiền dịch vụ cả tháng">
					                            Tiền dịch vụ
					                        </a>
										</li>
										<li>
											<a class="icon-in btn-link btn-outline" ng-click="exportListFood()">
												Bảng kê thực phẩm
											</a>
										</li>
										<li>
											<a target="_blank" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=9&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Bảng kê chi tiết tiền ăn">
					                            Bảng kê chi tiết tiền ăn
					                        </a>
										</li>
																				<li>
											<a href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/menuReport/nutritionTable'}}" target="_blank"  class="icon-in btn-link btn-outline">
												Bảng cân đối dinh dưỡng
											</a>
										</li>
																				<li ng-if="sys.configs.bctk_bktienan_show || 0">
											<a target="_blank"class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=10&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Bảng kê tiền ăn từng ngày theo tháng">
												Bảng kê ăn theo tháng 											</a>
										</li>
										<li style="color:orange;">
											<a target="_blank" style="color:orange;" class="btn-link btn-outline" href="{{[$CFG.remote.base_url,'report',$CFG.project,'cash_book'].join('/')}}/reportPreview?type=11&date={{menu_report.selected.month.year+'-'+menu_report.selected.month.month+'-1'}}" title="Tổng hợp suất ăn theo STTA">
					                            Tổng hợp suất ăn theo STTA
					                        </a>
										</li>
																														<li>
											<a class="icon-in btn-link btn-outline" onClick="formFoodList()">Bảng kê chi mua thực phẩm</a>
											<div id="food-list" style="display: none;padding: 10px;"
												ng-init="begin=dateboxOnSelect();end=getDate();warehouse='2';type='1'">
												<table>
													<tr>
														<th>Từ</th>
														<th>
															<input date-box="begin" style="width: 100px;"
																title="thời gian bắt đầu" class="input-custom-style"/>
														</th>
														<th>Đến</th>
														<th>
															<input date-box="end" style="width: 100px;"
																title="thời gian kết thúc" class="input-custom-style"/>
														</th>
														<th></th>
														<th></th>
													</tr>
													<tr>
														<th>Kho</th>
														<th>
															<select ng-model="warehouse" title="Chọn kho"
																	class="input-custom-style" style="width: 100px">
																<option value="1,2" selected>Tất cả</option>
																<option value="1">Kho sáng</option>
																<option value="2">Kho trưa</option>
															</select>
														</th>
														<th>
															Excel
														</th>
														<th>
															<select ng-model="type" title="Chọn mẫu"
																	class="input-custom-style" style="width: 100px">
																<option value="1">Mẫu 1</option>
																<option value="2">Mẫu 2</option>
															</select>
														</th>
														<th></th>
														<th></th>
													</tr>
													<tr>
														<th colspan="6">
															<input type="checkbox" ng-model="sys.configs.skipReturnSupplier"
																inf-configs="sys.configs.skipReturnSupplier"
																inf-id="skipReturnSupplier" ng-true-value="1" ng-false-value="0">
															<span style="padding-left: 23px;">Bỏ lượng nhập đã trả nhà cung cấp</span>
														</th>
													</tr>
													<tr>
														<td></td>
														<td>
															<a href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/menu_report/foodList?warehouse='+ warehouse + '&amp;begin=' + begin + '&amp;end=' + end + '&amp;skipReturnSupplier=' + sys.configs.skipReturnSupplier }}"
															target="_blank" class="btn btn-default" style="width: 100px">
																<i class="fa fa-print"></i>Xem
															</a>
														</td>
														<td></td>
														<td>
															<a href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/menu_report/foodListExcel?warehouse='+ warehouse + '&amp;begin=' + begin + '&amp;end=' + end + '&type=' + type + '&amp;skipReturnSupplier=' + sys.configs.skipReturnSupplier }}"
															target="_blank" class="btn btn-default">
																<i class="fa fa-download"></i>Tải file excel
															</a>
														</td>
														<td></td>
														<td></td>
													</tr>
												</table>
											</div>
										</li>
									</ul>
								</li>
							</ul>
						</div>
					</div>
					<div class="ndkhoi tr-noidung tr-nd-select">
						<table class="table-maket-menu" style="width: 100%">
							<tbody>
								<tr style="height: 30px" ng-repeat="(key, cash_book) in menu_report.cash_books" title="Tạo lúc {{cash_book.created_at}}">
									<td class="td-date">
										<span ng-bind="cash_book.date"></span>
									</td>
									<td style="text-align: right;">
										<div ng-if="cash_book.left_unit_id && (cash_book.enabled || menu_report.unlock)">
											<button type="button" class="btn-link btn-outline" ng-click="menu_report.sotinhtienan(cash_book.date)" title="Xem sổ ngày {{cash_book.date}}">
			                                    <i class="fa fa-eye" aria-hidden="true"></i> Xem
			                                </button>
														                                <button type="button" class="btn-link btn-outline hover-pointer" ng-disabled="0" ng-style="(!cash_book.allow_delete?{'color':'gray'}:{})" ng-class="{'color-red btn-over-red':cash_book.allow_delete}" ng-click="menu_report.delsotinhtienan(cash_book.date)" title="Xóa sổ từ ngày {{cash_book.date}}">
			                                    <span class="hover-pointer glyphicon glyphicon-trash"></span> Xóa
			                                </button>
			                                										</div>
																				<div ng-if="!cash_book.left_unit_id && (cash_book.enabled || menu_report.unlock)">
											<button type="button" class="btn-link btn-outline" ng-click="menu_report.sotinhtienan(cash_book.date)" title="Tính tiền ăn ngày {{cash_book.date}}">
			                                    <span class="glyphicon glyphicon-edit" ></span> Tính tiền ăn
			                                </button>
										</div>
																				<div ng-if="cash_book.warning && !menu_report.unlock" title="Sổ tính tiền ăn phải thực hiện lần lượt các ngày để đảm bảo số liệu chính xác. Xóa các ngày phía sau để tiếp tục.">
											<i class="fa fa-exclamation-triangle color-orange" aria-hidden="true"></i>
											<i>Sai quy trình</i>
										</div>
		                            </td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<div class="col-md-4 col-sm-4 col-xs-12 menu-report-calotuan col-reports" id="container-calculate_calo_week">
				<div class="motkhoi">
					<div class="tdkhoi">
						<div style="text-align: center;">
							CALO TUẦN
						</div>
						<div style="padding: 3px 6px;">
							<div class="kho-m" style="float: left; padding: 4px;" ng-repeat="(index,item) in menu_report.warehouses">
								<label class="checkbox-inline">
									<input type="checkbox" ng-model="item.selected"> <text ng-bind="item.name"></text>
								</label>
							</div>
						</div>
					</div>
					<div class="ndkhoi tr-noidung tr-nd-select calender-content cal-content-mr">
						<div ng-repeat="(index,group) in menu_report.group_arr">

							<div class="group-name-mr"><p ng-bind="group.name"></p>
								<div ng-if="index != 0">
									<i id="cl-{{group.id}}" class="glyphicon glyphicon-chevron-down" ng-click="menu_report.changeCL(group.id,index)"></i>
									<i id="lc-{{group.id}}" class="glyphicon glyphicon-chevron-up" ng-click="menu_report.changeLC(group.id,index)" style="display: none;"></i>
								</div>
								<div ng-if="index == 0">
									<i id="cl-{{group.id}}" class="glyphicon glyphicon-chevron-down" ng-click="menu_report.changeCL(group.id,index)" style="display: none;"></i>
									<i id="lc-{{group.id}}" class="glyphicon glyphicon-chevron-up" ng-click="menu_report.changeLC(group.id,index)"></i>
								</div>
							</div>
							<table class="table-calender table-hidden {{group.showtable}}" id="table-{{group.id}}">
								<thead>
									<tr class="head-weekdays">
										<th><label>Th.2</label></th>
										<th><label>Th.3</label></th>
										<th><label>Th.4</label></th>
										<th><label>Th.5</label></th>
										<th><label>Th.6</label></th>
										<th><label>Th.7</label></th>
										<th><label>CN</label></th>
									</tr>
								</thead>
								<tbody>
									<tr ng-repeat="(rol_index,rows) in group.val" ng-if="menu_report.check_ndt(rows)">
										<td ng-repeat="(col_index,col) in rows" class="{{(col.month != menu_report.calender.month?'day-disabled':'day-enable')}}" 
											id="{{(col.selected&&col.month == menu_report.calender.month?'day-selectedd':'')}}" >
											<div class="day-content {{col.class}}" ng-click="col.selected = !col.selected">
												<label ng-bind="col.day" class="title-day"></label>
												<span class="icon-checked glyphicon glyphicon-check" ng-hide="true||col.selected"></span>
												<input class="checkbox-day" type="checkbox" ng-model="col.selected" ng-hide="true">
											</div>
										</td>
										<td>
											<label id="{{rows.disable}}" class="btn-calo-week " ng-click="menu_report.calotuan(rows,menu_report.calender,rows.disable,group.id)"><span class="glyphicon glyphicon-print"></span>In</label>
										</td>
									</tr>

								</tbody>
							</table>
						</div>
					</div>
					
				</div>
			</div>
		<!-- </div> -->
	</div>
</div>
<div id="dialog-report-tkthutrongthang"></div>
<div id="tbl_menu_report"></div>

<script src="http://localhost:3000/js/dinhduong/cash_book/main.js?_=1960787307"></script>
<script src="http://localhost:3000/js/dinhduong/menu_report.js?_=1190946698"></script>
<script src="http://localhost:3000/js/dinhduong/marketbill.js?_=1466787995"></script>
<script src="http://localhost:3000/js/dinhduong/calculate_calo_week.js?_=1941762101"></script>
<script type="text/javascript">
    function exportExcelListMoney(){
        $('#export-money').dialog({
            title: 'Tiền hàng theo nhà cung cấp',
            width: 320,
            height: 120,
            closed: false,
            cache: false,
            modal: true ,
        });
    };

    $('.btn-export').on('click', function () {
        var start = $("input[name='date-start']").val();
        var end = $("input[name='date-end']").val();
        var type = $("select[name='type']").val();
        console.log(type);
        if(start === '' || end === '')
            alert("Dữ liệu nhập không được trống!");
        else{
            var url = $CFG.remote.base_url+'/'+$CFG.project+'/menu_report/excelGetListMoney?type='+type+'&date-start='+ start + '&date-end=' + end;
            $(this).attr('href', url);
        }
    });

	$.menu_report.init();
	function formFoodList() {
        $('#food-list').dialog({
            title: 'Bảng kê thực phẩm',
            width: 380,
            height: 230,
            closed: false,
            cache: false,
            modal: true,
        });
    }
	// $.menu_report.init();
	$(document).ready(function(){
		// alert($('.tbl_container').height());
		height = $('.tbl_container').height() - 200;
		$(".cal-content-mr").css("max-height", height+"px");
		$(".tr-noidung").css("max-height", height+"px");
		
	});
</script>
<style type="text/css">
	.content-menu-report .table-maket-menu td{
		font-size: 13px !important;
		padding: 2px 3px;
	}
	.content-menu-report .table-maket-menu tr:nth-child(2n+1){
		background: #e4f2f8;
	}
	.table-maket-menu  tr{
		height: 25px;
	}
	.color-blue{
		color: #337ab7;
	}
	.td-w-40 button:hover{
		color: red;
		text-decoration: none;
	}
	.group-name-mr{
		padding: 5px;
	    background: #69adde;
	    color: #FFF;
	    width: 100%;
	    display: inline-block; 
	    /*border-radius: 5px;*/
	    margin-bottom: 1px;
	}
	.group-name-mr p{
		width: 90%;
		float: left;
		margin-bottom: 0px;
		font-size: 14px;
	}
	.group-name-mr i{
		width: 10%;
		float: left;
	}
	.group-name-mr i:hover{
		cursor: pointer;
	}
	.cal-content-mr{
		/*max-height: 350px;*/
    	overflow: auto;
	}
	.table-hidden{
		display: none;
	}
	.show-table{
		display: table;
	}
	/*.table-calender tr th label{
		font-size: 13px;
	}*/
	
	.bg-green{
		background: #caaff2;
		border-radius: 5px;
	}
	.soquytienmat{
		max-height: 100%;
	}
	.function-kh-ct{
		position: relative;
	}
	.support-video{
		width: 120px;
	    position: fixed;
	    bottom: 4px;
	    right: 0px;
	    z-index: 6666;
	}
	.config-hover:hover{
		cursor: pointer;
	}
	.tbl_container {
		padding-top: {{$CFG.is_gokids == 1 ?0:30}}px !important;
	}
	#food-list table th {
        min-width: 40px;
        padding-right: 10px;
        padding-bottom: 10px;
    }
</style>