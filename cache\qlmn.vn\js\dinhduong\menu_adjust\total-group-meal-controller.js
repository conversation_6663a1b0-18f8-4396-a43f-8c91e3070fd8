((angular_app)=>{
    var scope = null;
    angular_app.controller('TotalGroupMealController', ['$scope', function ($scope) {
        scope = $scope;
        scope.total = {};
        scope.key_arr = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q'];
        $scope.foodAddeds = {};
        scope.init = ()=>{
            var meals = {};
            var groups = {};
            var foods = {};
            angular.forEach(scope.menu_adjust.group_adjusts, (group, group_id)=>{
                groups[group_id] = {
                    group_id: 0,
                    sotre: group.menu_plannings[0].row.sotre,
                    name: group.name
                };
                angular.forEach(group.menu_plannings[0].meals, (meal, meal_define)=>{
                    meals[meal_define] || (meals[meal_define] = {
                        name: meal.name,
                        foods: {},
                        dishes: {},
                    });
                    angular.forEach(meal.dishes, (dish)=>{
                        meals[meal_define].dishes[dish.id] || (meals[meal_define].dishes[dish.id] = {
                            foods: {},
                            name: dish.name,
                        });
                        angular.forEach(dish.ingredient, (food)=>{
                            foods[food.id] = food;
                            food.quantity = food.quantity_edit;
                            meals[meal_define].foods[food.id] || (meals[meal_define].foods[food.id] = {
                                id: food.id,
                                measure_id: food.measure_id,
                                name: food.name,
                                quantity: 0,
                                groups: {},
                                price: food.price,
                                price_kg: food.price_kg
                            });
                            meals[meal_define].foods[food.id].groups[group_id] || (meals[meal_define].foods[food.id].groups[group_id] = {
                                quantity: 0,
                                foods: []
                            });
                            meals[meal_define].foods[food.id].groups[group_id].quantity = $['+'](meals[meal_define].foods[food.id].groups[group_id].quantity, food.quantity_edit);
                            meals[meal_define].foods[food.id].quantity = $['+'](meals[meal_define].foods[food.id].quantity, food.quantity_edit);
                            meals[meal_define].foods[food.id].groups[group_id].foods.push(food);
                            
                            //theo mon an
                            meals[meal_define].dishes[dish.id].foods[food.id] || (meals[meal_define].dishes[dish.id].foods[food.id] = {
                                id: food.id,
                                measure_id: food.measure_id,
                                name: food.name,
                                quantity: 0,
                                groups: {},
                                price: food.price,
                                price_kg: food.price_kg,
                                thucmua1tre: 0,
                                thucmua1nhom: 0,
                            });
                            meals[meal_define].dishes[dish.id].foods[food.id].groups[group_id] = {
                                quantity: food.quantity_edit,
                                foods: [],
                            };
                            var thucmua1tre = $['/'](food.quantity_edit, 1-food.extrude_factor/100);
                            var thucmua1nhom = $['/']($['*'](thucmua1tre, group.menu_plannings[0].row.sotre), food.gam_exchange);
                            meals[meal_define].dishes[dish.id].foods[food.id].quantity = $['+'](meals[meal_define].dishes[dish.id].foods[food.id].quantity, food.quantity_edit);
                            meals[meal_define].dishes[dish.id].foods[food.id].groups[group_id].thucmua1tre = round(thucmua1tre, 5);
                            meals[meal_define].dishes[dish.id].foods[food.id].groups[group_id].thucmua1nhom = round(thucmua1nhom, 5);
                            meals[meal_define].dishes[dish.id].foods[food.id].groups[group_id].foods.push(food);
                            meals[meal_define].dishes[dish.id].foods[food.id].thucmua1tre = $['+'](meals[meal_define].dishes[dish.id].foods[food.id].thucmua1tre, thucmua1tre);
                            meals[meal_define].dishes[dish.id].foods[food.id].thucmua1nhom = $['+'](meals[meal_define].dishes[dish.id].foods[food.id].thucmua1nhom, thucmua1nhom);
                        })
                    })
                });
                var tmp_data = {};
                angular.forEach(meals, (meal, meal_define)=>{
                    var wh_id = $scope.getMenu_infomation(meal_define).warehouse_id;
                    tmp_data[wh_id] || (tmp_data[wh_id] = {});
                    angular.forEach(meal.foods, (food, food_id)=>{
                        if (food.groups[group_id]) {
                            if (group.menu_plannings[0].data[wh_id][food_id]) {
                                tmp_data[wh_id][food_id] || (tmp_data[wh_id][food_id] = {
                                    food_id: food_id,
                                    thucmuatheodvt: group.menu_plannings[0].data[wh_id][food_id].thucmuatheodvt,
                                    foods: []
                                });
                                if (food.groups[group_id]) {
                                    tmp_data[wh_id][food_id].foods.push(food.groups[group_id]);
                                }
                            }
                        }
                    })
                });
                angular.forEach(tmp_data, (foods)=>{
                    angular.forEach(foods, (food) =>{
                        scope.divideThucmua1nhom(group.menu_plannings[0].row.sotre, food.thucmuatheodvt, food.foods);
                    });
                });
            });
            var tmp_meals = {};
            angular.forEach(scope.menu_informations, function(menu_info) {
                angular.forEach(meals, function(meal, meal_define) {
                    if(meal_define == menu_info.define) {
                        tmp_meals[meal_define] = meal;
                    }
                });
            });
            $scope.total.groups = groups;
            $scope.total.meals = tmp_meals;
            $scope.total.foods = foods;
            angular.forEach($scope.total.groups, function(total_group, gr_id) {
                $scope.total.groups[gr_id].tienmua1nhom = {};
                $scope.total.groups[gr_id].tienmua1tre = {};
            });
            angular.forEach($scope.total.meals, (meal, meal_define)=>{
                if (count(meal.foods)>0) {
                    angular.forEach(meal.foods, (food, food_id) => {
                        food.thucmua1nhom = 0;
                        food.thucmua1tre = 0;
                        angular.forEach(food.groups, (group, group_id) => {
                            // scope.quantityChange(group, group_id, food_id);
                            food.thucmua1nhom = $['+'](food.thucmua1nhom, group.thucmua1nhom);
                            food.thucmua1tre = $['+'](food.thucmua1tre, group.thucmua1tre);
                            group.thucmua1tre = $['*'](group.thucmua1tre, scope.total.foods[food_id].gam_exchange);
                        });
                    });
                } else {
                    delete $scope.total.meals[meal_define];
                }
                angular.forEach(meal.dishes, function(dish, dish_id) {
                    if (count(dish.foods)>0) {
                        angular.forEach(dish.foods, (food, food_id) => {
                            angular.forEach(food.groups, (group, group_id) => {                                
                                $scope.total.groups[group_id].tienmua1nhom[meal_define] || ($scope.total.groups[group_id].tienmua1nhom[meal_define]=0);
                                $scope.total.groups[group_id].tienmua1nhom[meal_define] = $['+']($scope.total.groups[group_id].tienmua1nhom[meal_define], group.thucmua1nhom*food.price);
                                $scope.total.groups[group_id].tienmua1tre[meal_define] || ($scope.total.groups[group_id].tienmua1tre[meal_define]=0);
                                $scope.total.groups[group_id].tienmua1tre[meal_define] = $['+']($scope.total.groups[group_id].tienmua1tre[meal_define], group.thucmua1tre*food.price_kg/1000);
                            });
                        });
                    } else {
                        delete meal.dishes[dish_id];
                    }
                });
            });
        };
		scope.downloadExcel = ()=>{};
        scope.downloadWord = ()=>{
            Export.file('TotalGroupMealController', 'tong_hop', 'doc');
        };
        scope.printTotalByMealForm = ()=>{
            $.dm_datagrid.showAddForm({
                action:'',
                title:'Xuất file',
                size: size.small,
                fullScreen: true,
                showButton: false,
                scope: scope,
                content: $scope.project + '/menu_adjust_total/total-group-meal-print.html?_='
            });
        };
        scope.printTotalByDishForm = ()=>{
            $.dm_datagrid.showAddForm({
                action:'',
                title:'Xuất file',
                size: size.small,
                fullScreen: true,
                showButton: false,
                scope: scope,
                content: $scope.project + '/menu_adjust_total/total-group-dish-print.html?_='
            });
        };
        scope.printById = (id)=>{
            // Export.Table.setBorder(id, 'boder');
            $.staticFile.printForm(id);
        };
        scope.divideThucmua1nhom = (sotre, tong, foods)=>{
            if (count(foods) == 1) {
                foods[0].thucmua1nhom = tong;
                foods[0].thucmua1tre = round(foods[0].thucmua1nhom/sotre,5);
            } else {
                /*  Tính tổng lượng 1 trẻ chuẩn theo món ăn */
                var tong_chuan = 0;
                angular.forEach(foods, function (food, index) {
                    food.quantity = Number(food.quantity);
                    tong_chuan = $['+'](tong_chuan, food.quantity);
                });
                /* tính tỉ lệ thực phẩm từng */
                var tong_phan = 0;
                var food_max = foods[0];
                angular.forEach(foods, function (food, index) {
                    var tile = food.quantity / tong_chuan;
                    var soluong = tile * tong;
                    soluong = scope.round(soluong, 5);
                    food.thucmua1nhom = soluong;
                    food.thucmua1tre = round(food.thucmua1nhom/sotre,5);
                    tong_phan = $['+'](tong_phan, soluong);
                    if (food_max.quantity <food.quantity){
                        food_max = food;
                    }
                });
                /*cộng nốt số dư của các lần làm tròn vào thực phẩm đầu tiên*/
                if (tong_phan != tong) {
                    food_max.thucmua1nhom = $['+'](food_max.thucmua1nhom, $['-'](tong, tong_phan));
                    food_max.thucmua1tre = round(food_max.thucmua1nhom/sotre,5);
                }
            }
        };
        scope.quantityChange = (food, group_id, food_id)=>{
            food.thucan1nhom = $['*'](food.quantity, scope.total.groups[group_id].sotre);
            food.thucmua1nhom = food.thucan1nhom;
            if(scope.total.foods[food_id].extrude_factor > 0){
                food.thucmua1nhom = $['/']($['*'](100, food.thucan1nhom),$['-'](100, scope.total.foods[food_id].extrude_factor));
            }
            food.thucmua1tre = round(food.thucmua1nhom/scope.total.groups[group_id].sotre, 5);
            food.thucmua1nhom = round(food.thucmua1nhom/scope.total.foods[food_id].gam_exchange, 5);
        };
        scope.thucmua1treChange = (food, group_id, food_id)=>{
            food.thucmua1nhom = $['*'](food.thucmua1tre, scope.total.groups[group_id].sotre);
            food.thucan1nhom = food.thucmua1nhom;
            if(scope.total.foods[food_id].extrude_factor > 0){
                food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*scope.total.foods[food_id].extrude_factor);
            }
            food.quantity = round($['/'](food.thucan1nhom, scope.total.groups[group_id].sotre),5);
            food.thucmua1nhom = round(food.thucmua1nhom/scope.total.foods[food_id].gam_exchange, 5);
        };
        scope.thucmua1nhomChange = (food, group_id, food_id)=>{
            food.thucmua1tre = $['/'](food.thucmua1nhom*scope.total.foods[food_id].gam_exchange, scope.total.groups[group_id].sotre);
            food.quantity = food.thucmua1tre;
            if(scope.total.foods[food_id].extrude_factor > 0){
                food.quantity = $['-'](food.thucmua1tre, food.thucmua1tre/100*scope.total.foods[food_id].extrude_factor);
            }
            food.quantity = round(food.quantity,5);
            food.thucmua1tre = round(food.thucmua1tre,5);
        };
        scope.quantityGroupChange = (food, group_id) => {
            food.thucmua1nhom = 0;
            scope.quantityChange(food.groups[group_id], group_id, food.id);
            angular.forEach(food.groups, (group)=>{
                food.thucmua1nhom = $['+'](food.thucmua1nhom, group.thucmua1nhom);
            });
            scope.applyChange(food, group_id);
        };
        scope.thucmua1nhomGroupChange = (food, group_id) => {
            food.thucmua1nhom = 0;
            scope.thucmua1nhomChange(food.groups[group_id], group_id, food.id);
            angular.forEach(food.groups, (group)=>{
                food.thucmua1nhom = $['+'](food.thucmua1nhom, group.thucmua1nhom);
            });
            scope.applyChange(food, group_id);
        };
        scope.thucmua1treGroupChange = (food, group_id) => {
            food.thucmua1nhom = 0;
            scope.thucmua1treChange(food.groups[group_id], group_id, food.id);
            angular.forEach(food.groups, (group)=>{
                food.thucmua1nhom = $['+'](food.thucmua1nhom, group.thucmua1nhom);
            });
            scope.applyChange(food, group_id);
        };
        scope.onChangeGroup = (group_id)=>{
            if (scope.row.group_id != group_id){
                setTimeout(()=>{
                    scope.menu_planningSelected(scope.menu_adjust.group_adjusts[group_id].menu_plannings[0], true);
                    scope.$apply();
                });
            }
        };
        scope.applyChange = (f, group_id)=>{
            var quantites = [];
            angular.forEach(f.groups[group_id].foods, (food, index)=>{
                quantites[index] = food.quantity_edit;
            });
            quantites = scope.divide(f.groups[group_id].quantity, quantites);
            angular.forEach(f.groups[group_id].foods, (food, index)=>{
                food.quantity_edit = quantites[index];
                food.quantity = quantites[index];
                food.quantities = scope.divide(food.quantity, food.quantities);
                food.changed = true;
            });
            angular.forEach(scope.menu_adjust.group_adjusts[group_id].menu_plannings[0].data, (foods)=>{
                for (var food_id of Object.keys(foods)){
                    var kt = false;
                    for (var food of foods[food_id].foods){
                        if (food.changed){
                            kt = true;
                        }
                        food.changed = false;
                    }
                    if (kt) {
                        foods[food_id].luong1tre = 0;
                        angular.forEach(foods[food_id].foods, (food)=>{
                            foods[food_id].luong1tre = $['+'](foods[food_id].luong1tre, food.quantity_edit);
                        });
                        scope.onChange_luong1tre(foods[food_id], true);
                    }
                }
            });
            scope.totalCalculator(scope.menu_adjust.group_adjusts[group_id].menu_plannings[0].data);
            angular.forEach(scope.total.groups, function(total_group, gr_id) {
                scope.total.groups[gr_id].tienmua1nhom = {};
                scope.total.groups[gr_id].tienmua1tre = {};
            });
            angular.forEach(scope.total.meals, (meal, meal_define)=>{
                angular.forEach(meal.dishes, function(dish, dish_id) {
                    if (count(dish.foods)>0) {
                        angular.forEach(dish.foods, (food, food_id) => {
                            angular.forEach(food.groups, (group, group_id) => {                                
                                $scope.total.groups[group_id].tienmua1nhom[meal_define] || ($scope.total.groups[group_id].tienmua1nhom[meal_define]=0);
                                $scope.total.groups[group_id].tienmua1nhom[meal_define] = $['+']($scope.total.groups[group_id].tienmua1nhom[meal_define], group.thucmua1nhom*food.price);
                                $scope.total.groups[group_id].tienmua1tre[meal_define] || ($scope.total.groups[group_id].tienmua1tre[meal_define]=0);
                                $scope.total.groups[group_id].tienmua1tre[meal_define] = $['+']($scope.total.groups[group_id].tienmua1tre[meal_define], group.thucmua1tre*food.price_kg/1000);
                            });
                        });
                    } else {
                        delete meal.dishes[dish_id];
                    }
                });
            });
        };
    }]);
})(angular_app);
