    
$(document).ready(function(){ 
    $(document).mouseup(function (e)
    {
        var container = $(".tab-main-index");
        if($(window).width()<500){
            if (!container.is(e.target) && container.has(e.target).length === 0)
            {
                container.hide();
            }
        }
        
    });
    hv();
    $(window).resize(function() {
        hv();
    })
    $(document).mouseup(function (e){
        var container = $(".item-setup-mc-new");
     
        if (!container.is(e.target) && container.has(e.target).length === 0)
        {
            container.hide();
        }
    });

    var url = '{{ url() }}/single/{{$config->project}}';
    if(window.location.href != url){
        $(".content-qt").hide("slow");
    }
    if($(window).height() < 650)
    {
        $('.content-qt').css({"height":$(window).height(),"overflow":"auto"});
    }
    
    $('#spinner-container').addClass('spinner'+rand(1,4));
    
    $(document).mouseup(function (e){
        var container = $(".quy-trinh");
        if (!container.is(e.target) && container.has(e.target).length === 0)
        {
            $(".content-qt").hide("slow");
        }
    });
    $('.btn-quy-trinh').click(function(){
        if($(window).height() < 650){
            $('.content-qt').css({"height":$(window).height(),"overflow":"auto"});
        }
        $(".content-qt").toggle("slow", function() {});
    })
        
    $( "a" ).click(function() {
        $('.logo-dd-new img').css({"display":"block","width":"100%"});
        $('.main-content').css({"z-index":"3"});
    });
    if(window.location.href != "http://localhost/pms/single/{{$config->project}}"){
        $('.logo-dd-new img').css({"display":"block","width":"100%"});
        $('.main-content').css({"z-index":"3"});
    };
    $('.bottom-hidden').click(function(){       
        $(".bottom-content").animate({
            top:($(window).height()-60)+"px",
        },700);
        setTimeout(function(){
            $(".bottom-content").toggle();
            $('.footer').css({"display":"none"});
        },800)
    });

    $(window).scroll(function(e){
        scroll1();
    });
    
    var hienquytrinh = getCookie('hienquytrinh');
    if(!hienquytrinh){
        hienquytrinh = 0;
    }
    
    if(hienquytrinh == 0){
        $(".content-qt").hide("slow");

    }else{
        $("#tudonghien12").attr("checked", true);
    }

    
    var visitor = getCookie('visitor11234');

    if(!visitor){
        visitor = 0;
    }
    visitor = Number(visitor);
    visitor++;
    
    setCookie('visitor11234',visitor,18000);
    if(visitor<4 && false){
        $('#dialog-tphcm').dialog({
            title: '',
            width: 650,
            height: 500,
            closed: false,
            cache: false,
            modal: true,
            onOpen : function (ele) {
                $(ele).show();
                $('.window-shadow').css("display","none");
                
                $('.window').css({"background":"rgba(0,0,0,0)","border":"0px","box-shadow":"none"});
                var btn_close = $('<div style="top: 62px;right: 44px;position: absolute;font-size:17px; opacity: 1;" class="close"><img src="{{ asset("/images/close.png") }}"></div>');

                $('#dialog').css({"background":"rgba(0,0,0,0)","border":"0px"}).append(btn_close);
                btn_close.click(function(){
                    $('#dialog').dialog("close");
                })
                $('.window-mask').click(function(){
                    $('#dialog').dialog("close");
                })
            }
        })
    }
});
function bottomShow(){
    $(".bottom-content").toggle();
    $(".bottom-content").animate({
        top:"0px"
    },700);
    $('.footer').css({"display":"block"});
}
function hideqt(){
    $(".content-qt").hide("slow");
}
function scroll1() {
    var sticky = $('.menu-bc'),
        scroll = $(window).scrollTop(),
        Wheight = $(window).height();
    if (scroll>Wheight-80) 
        sticky.addClass('fixed');
    else sticky.removeClass('fixed');
}
function tudonghien(el){
    if($("#tudonghien12:checked").length){
        setCookie('hienquytrinh',1,18000);
    }else{
        setCookie('hienquytrinh',0,18000);
    }
}
function tabMain() {
    if($(window).width()<500){
        $('.tab-main-index').toggle();
    }   
}
function hv(){
    setTimeout(function(){
        var navmain =  $(".nav-main-content").width();
        var inmain = $(".in-main").width();
        var w2out = navmain + 20 + (inmain - navmain)/2;
        if($(window).width() < 420){
            w2out = 5;
            $('.item-setup-mc-new').css("top","40px");
        }
        if($(window).width() < 769 && $(window).width() > 420){
            w2out = navmain + 40;
        }
        $('.item-setup-mc-new').css("left", w2out+"px");
    }, 250);
}
function mcNew() {
    $('.item-setup-mc-new').toggle();
}