$.menu_planning = {
    module: 'menu_planning',
    module_filter: '',
    scope: undefined,
    groups: [],
    init: function (module_filter) {
        var self = $.menu_planning;
        if (module_filter) {
            self.module_filter = module_filter;
        }
        process('dinhduong/' + self.module + self.module_filter + '/special_categories', {}, function (resp) {
            $.menu_planning.services = resp.services;
            $.menu_planning.groups = resp.groups;
            $.menu_planning.warehouses = resp.warehouses;
            $.menu_planning.measures = resp.measures;
            $.menu_planning.menu_informations = resp.menu_informations;
            $.menu_planning.inventory = resp.inventory;
            $.menu_planning.school_points = resp.school_points;
            $.menu_planning.sogd = resp.sogd;
            $.menu_planning.initAngular($.menu_planning.loadTable);
        }, null, false);
    },
    loadTable: function (scope) {
        if (!$.menu_planning.scope) {
            $.menu_planning.scope = scope;
        }
        var self = $.menu_planning;
        var urls = [$CFG.remote.base_url, 'doing', $CFG.project, self.module + self.module_filter, 'list' + '?status=' + $('#status').val()];
        var fields = [{field: 'ck', title: 'STT', checkbox: true, rowspan: 2},
        {field: 'id', title: 'Mã thực đơn', rowspan: 2,with:100}, {
            title: 'Tên thực đơn',
            field: 'name',
            sortable: true,
            width: 150,
            rowspan: 2
        }, {
            title: 'Các món ăn', field: 'name_dishes', width: 200, rowspan: 2, formatter: function (value, row, index) {
                var names = value.split(':|:');
                value = [];
                var dem = 0;
                for (var i in names) {
                    if (names[i].trim() != '') {
                        dem++;
                        value.push((dem) + '. ' + names[i]);
                    }
                }
                return value.join('<br/>');
            }
        }, /*{ title:'Tiền ăn', field:'student_numbers', width:60}, { title:'Ngày sửa', field:'updated_at', width:70,sortable:true,formatter:function(value,row,index){value=row.date;return value;}} { title:'Tiền ăn'}*/ {
            title: 'Tiền',
            field: 'money',
            colspan: 2,
            align: 'center'
        }, {title: 'Đánh giá', field: 'comment', width: 150, colspan: 2, align: 'center'}];
        var fields1 = [{
            title: 'Ăn 1 trẻ',
            field: 'money_on_children',
            width: 50,
            align: 'right'
        }, {
            title: 'Chênh 1 trẻ',
            field: 'money_difference_on_child',
            width: 50,
            align: 'right',
            sortable: true
        }, {
            title: 'Lượng', field: 'evaluation', width: 50, align: 'center', formatter: function (value) {
                if (value == 0) {
                    value = '<span class="color-red">Chưa đạt</span>'
                } else if (value == 1) {
                    value = 'Đạt';
                } else if (value == 2) {
                    value = '<span class="color-green">Vượt quá định mức</span>';
                }
                return value;
            }
        }, {
            title: 'Chất', field: 'evaluation_calo', width: 50, align: 'center', formatter: function (value) {
                if (value == 0) {
                    value = '<span class="color-red">Chưa cân đối</span>';
                } else if (value == 1) {
                    value = '<span class="">Cân đối</span>';
                }
                return value;
            }
        },];
        if (scope.menu_planning.school_points > 1 && self.module_filter == '') {
            fields.push({title: 'Điểm trường', field: 'school_point', width: 80, sortable: true, rowspan: 2, align: 'center'});
        }
        fields.push({
            title: 'Chia sẻ',
            field: 'shared',
            width: 50,
            sortable: true,
            rowspan: 2,
            align: 'center',
            formatter: function (value, row) {
                if (value + '' === '1') {
                    value = `<a style="font-size:14px;" class="btn over-orange" onclick="$.menu_planning.applySharing(` + row.id + `,0)" title="Bỏ chia sẻ"><i class="fa fa-check-circle-o"></i></a>`;
                } else {
                    value = `<a style="font-size:14px;" class="btn over-orange" onclick="$.menu_planning.applySharing(` + row.id + `,1)" title="Chia sẻ"><i class="fa fa-circle-o over-orange"></i></a>`;
                }
                return value;
            }
        });
        fields.push({
            title: 'Ngày cập nhật',
            field: 'update_at',
            width: 62,
            sortable: true,
            align: 'center',
            rowspan: 2,
            formatter: function (value, row) {
                return moment(row.updated_at).format('DD/MM/YYYY HH:mm:ss');
            }
        });
        var exts = {
            view: groupview, groupField: 'group_id', groupFormatter: function (id, rows) {
                var val = '';
                $.each($.menu_planning.groups, function (index, item) {
                    if (index == rows[0].group_id) {
                        val = item.name;
                    }
                });
                return val + ':<i>' + rows.length + '</i>';
            }, onDblClickRow: function (rowIndex, rowData) {
                $.menu_planning.scope.editForm();
            }
        };
        if (self.module_filter != '') {
            exts.rowStyler = function (index, row) {
                if (row.copy_status == 1) {
                    return 'color:blue;font-weight:bold;';
                }
            };
            fields.push({title: 'Tài khoản', field: 'username', width: 100, rowspan: 2});
            fields.push({
                title: '#',
                field: 'status',
                rowspan: 2,
                width: 30,
                align: 'center',
                formatter: function (value, row) {
                    value = `<a onclick="$.menu_planning.filter_copyForm(` + row.id + `)" title"Tạo bản sao vào thư viện mẫu"> <span class="bootstrap-dialog-button-icon glyphicon glyphicon-duplicate"></span> </a>`;
                    return value;
                }
            });
        }
        $.dm_datagrid.init(urls.join('/'), 'menu_planning', /*Định nghĩa thẻ để đổ dữ liệu ra:tbl_{this.module}*/ '', /*Tiêu đề cho bảng dữ liệu*/ [fields, fields1], exts);
    },
    applySharing: function (id, value) {
        var self = $.menu_planning;
        var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter + '/applySharing';
        var data = {async: true, data: {id: id, shared: value}};
        process(url, data, function (resp) {
            if (resp.result == 'success') {
                var rows = $('#tbl_menu_planning').datagrid('getRows');
                var row = {};
                var index = -1;
                angular.forEach(rows, function (r, ind) {
                    if (r.id == id) {
                        index = ind;
                        row = r;
                    }
                });
                row.shared = value;
                $('#tbl_menu_planning').datagrid('deleteRow', index);
                $('#tbl_menu_planning').datagrid('insertRow', {index: index, row});
            }else {
                $.messager.alert('Thông báo.', 'Bạn không có quyền này!');
            }
        }, null, false);
    },share2: function(){ // CHIA SẺ
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = $CFG.dialog_captcha('share_dish');
        var msg = '<div style = "font-size: 14px">Bạn có chắc muốn chia sẻ?</div>' + captchaForm;
        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="share_dish_captcha"]').val();
                process($CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter +'/share',{ids: ids,captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.menu_planning.share2();
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    },
    filter_copyForm: function (id) {
        setTimeout(function () {
            angular.element($('#menu_planningController')).scope().$apply(function (scope) {
                $.dm_datagrid.showAddForm({
                    module: $CFG.project + '/menu_planning',
                    action: 'add',
                    title: 'Sao chép vào thư viện',
                    size: size.small,
                    fullScreen: false,
                    showButton: false,
                    content: function (element) {
                        var data = $('#tbl_menu_planning').datagrid('getRows');
                        var name = '';
                        angular.forEach(data, function (row, index) {
                            if (row.id == id) {
                                name = row.name;
                            }
                        });
                        scope.filter.item_copy = {id: id, name: name, shared: 0};
                        var html = `<div style="height:200px;padding:15px;"> <div class="form-group"> <label class="col-md-12"> Tên thực đơn</label> <input class="" ng-model="filter.item_copy.name"> </div> <div class="form-group"> <label class="checkbox-inline"> <input type="checkbox" ng-model="filter.item_copy.shared" ng-true-value="1" ng-false-value="0"> Chia sẻ </label> </div> <div style="width:100%;height:5px;margin:7px 0;border-top:1px solid #777;margin-top:20px;"></div> <div class="form-group"> <button class="btn btn-primary" id="btn-save" ng-click="filter.copy(filter.item_copy);"> <span class="bootstrap-dialog-button-icon glyphicon glyphicon-floppy-disk"></span> Lưu </button> </div> </div>`;
                        $(element).html(scope.compile(html, scope))
                    }
                });
            });
        });
    },
    loadInventory: function (scope) {
    },
    showPartPLGInfo_balance: function (scope) { /* Hiển thị bảng chi tiết calo từng bữa ăn */
        $('#export-dialog-plg-view-balance').dialog({
            title: '<div style="padding-left:5px;font-size:14px">Đánh giá về chất theo số phần sau cân đối</div>',
            width: 500,
            height: 350,
            closed: false,
            cache: false,
            modal: true,
            onOpen: function (ele) {
                $(ele).show();
            }
        });
    },
    initAngular: function (callback) {
        setTimeout(function () {
            angular.element($('#menu_planningController')).scope().$apply(function (scope) {
                scope.configs = window.sysConfigs.configs;
                if(scope.configs.editFoodNamePermission == undefined) {
                    scope.configs.editFoodNamePermission = 1;
                }
                scope.meal_defines = {
                    buasang: 1,
                    buatrua: 2,
                    buaxe: 2,
                    buaphu: 2,
                    buatoi: 3
                };
                
                /* Tải lại bảng danh sách */
                scope.reloadDatagrid = function(){
                    $.menu_planning.initAngular($.menu_planning.loadTable);
                };

                scope.restore = function () {
                    var self = this;
                    var ids = [];
                    var rows_selected = {};
                    var check_school_point = true;
                    var school_point = $CFG.school_point;

                    $.each($("#tbl_menu_planning").datagrid('getSelections'), function (index, row) {
                        if (row.school_point!=$CFG.school_point) {
                            check_school_point = false;
                            school_point = row.school_point;
                        }
                        ids.push(row.id);
                        rows_selected[row.id] = row;
                    });
            
                    var captcha = $CFG.dialog_captcha('delete_menu_planning');
                    
                    if (ids.length == 0) {
                        $.messager.alert('Thông báo', 'Hãy chọn một dòng!');
                        return;
                    }
                    
                    $.messager.confirm('Xác nhận', '<div style="font-size:14px">Xác nhận khôi phục?</div>' + captcha, function (r) {
                        if (r) {
                            $.dm_datagrid.del($CFG.project + '/' + 'menu_planning', {ids , captcha: $('.panel [name="delete_menu_planning_captcha"]').val(), type: 'restore'}, function (resp) {
                                if (resp.result === 'success') {
                                    $("#tbl_menu_planning").datagrid('reload');
                                }
                            });
                        }
                    });
                }

                scope.buasang_norm = {min: 15, max: 20, smallest_rate: 0, biggest_rate: 0};
                scope.inventory = $.menu_planning.inventory;
                scope.menu_planning = {
                    warehouses: $.menu_planning.warehouses,
                    groups: $.menu_planning.groups,
                    measures: $.menu_planning.measures,
                    styleContainerThongkeTPDD: {},
                    prices: {},
                    services: $.menu_planning.services,
                    school_points: $.menu_planning.school_points,
                    sogd: $.menu_planning.sogd
                };
                scope.menu_informations = $.menu_planning.menu_informations;
                scope.selected || (scope.selected = {});
                scope.prices = {};
                scope.apdungcongthuc = true;
                scope.dinhduong_calc = {1: false, 2: true};
                scope.tong = {
                    tien: 0,
                    animal_protein: 0,
                    vegetable_protein: 0,
                    animal_fat: 0,
                    vegetable_fat: 0,
                    sugar: 0,
                    calo: 0,
                    calo_congthuc: 0
                };
                scope.row = {
                    sotre: 11,
                    tien1tre: 15000,
                    meal_selection: {
                        1: {selected: false, name: 'Ăn sáng', visible: true},
                        2: {selected: true, name: 'Ăn chính', visible: true},
                        3: {selected: false, name: 'Ăn tối', visible: false}
                    }
                };
                scope.selected = {
                    group: $.menu_planning.groups[0],
                    warehouse: 1,
                    balance_warehouse: {},
                    day: 0,
                    date: dateboxOnSelect(new Date())
                };
                scope.keysearch = {name: '', group_id: ''};
                angular.forEach(scope.menu_planning.warehouses, function (item, index) {
                    if (index == 2) {
                        scope.selected.balance_warehouse[index] = true;
                    } else {
                        scope.selected.balance_warehouse[index] = false;
                    }
                });
                scope.lengthSelectDish = 0;
                scope.detailByMeal = {};
                scope.detailByDish = {};
                scope.date = dateboxOnSelect(new Date());
                /*Phần này để tạm sau này định nghĩa theo nhóm sẽ bỏ đi*/
                angular.forEach($.menu_planning.groups, function (item, index) {
                    item.dinhmuc || (item.dinhmuc = {});
                    angular.forEach(scope.dinhmuc, function (dm, key) {
                        item.dinhmuc[key] = {'name': dm.name, 'value': item[key]};
                    });
                });
                if (typeof callback === 'function') {
                    callback(scope);
                }
                /* Xây dựng khung bảng biểu mẫu cho danh sách chức năng*/
                scope.datagrid = {
                    fields: [/* Định nghĩa các cột */ {
                        field: 'luong1tre',
                        title: 'Lượng 1 trẻ (g)',
                        style: {width: 90},
                        type: 'number',
                        class: 'btn-color-blue',
                        editable: true,
                        onChange: function (item) {
                            scope.onChange_luong1tre(item);
                            scope.foodRounds(item, 'luong1tre');
                            scope.divide_luong1tre(item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'price_kg',
                        type: 'number',
                        style: {width: 90},
                        title: 'Đơn giá (đ/kg)',
                        editable: true,
                        onChange(item) {
                            scope.onChange_price_kg(scope.datagrid.data, item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'thucan1nhom',
                        title: 'Thực ăn 1 nhóm (kg)',
                        style: {width: 90},
                        class: 'btn-color-blue',
                        type: 'number',
                        editable: true,
                        onChange: function (item) {
                            scope.onChange_thucan1nhom(item);
                            scope.foodRounds(item, 'thucan1nhom');
                            scope.divide_luong1tre(item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'extrude_factor',
                        width: 120,
                        title: 'Hệ số thải bỏ (%)',
                        style: {width: 90},
                    }, {
                        field: 'thucmua1nhom',
                        title: 'Thực mua 1 nhóm (kg)',
                        style: {width: 90},
                        class: 'btn-color-blue',
                        type: 'number',
                        editable: true,
                        onChange: function (item) {
                            scope.onChange_thucmua1nhom(item);
                            scope.foodRounds(item, 'thucmua1nhom');
                            scope.divide_luong1tre(item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'thucmuatheodvt',
                        title: 'Thực mua theo ĐVT',
                        style: {width: 90},
                        class: 'btn-color-blue',
                        type: 'number',
                        editable: true,
                        onChange: function (item) {
                            scope.onChange_thucmuatheodvt(item);
                            scope.foodRounds(item, 'thucmuatheodvt');
                            scope.divide_luong1tre(item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'measure_id', title: 'ĐVT', style: {width: 50}, formatter: function (value, row) {
                            angular.forEach($.menu_planning.measures, function (item, index) {
                                if (value == item.id) {
                                    value = item.name;
                                    return;
                                }
                            });
                            return value;
                        }
                    }, {
                        field: 'thucmua1tretheodvt',
                        title: 'Thực mua 1 trẻ theo ĐVT',
                        style: {width: 90},
                        class: 'btn-color-blue',
                        type: 'number',
                        editable: true,
                        onChange: function (item) {
                            scope.onChange_thucmua1tretheodvt(item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'price',
                        title: 'Đơn giá theo ĐVT',
                        style: {width: 90},
                        type: 'number',
                        editable: true,
                        onChange(item) {
                            scope.onChange_price(scope.datagrid.data, item);
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }, {
                        field: 'thanhtien1nhom',
                        title: 'Tổng tiền 1 nhóm (vnđ)',
                        style: {width: 110},
                        type: 'number',
                        formatter: function (value, row) { /*var value=scope.round(row.price * row.thucmuatheodvt,0);return value;*/
                        }
                    }/*, { field:'gam_exchange', title:'Quy đổi ra gam', formatter:function(value, row){ row.gam_exchange || (row.gam_exchange=1);return row.gam_exchange;}}*/],
                    getValue: function (field, row) { /* Bổ trợ cho hiển thị dạng số thập phân ra bảng */
                        var rs = '';
                        rs = this.formatter(field, row[field.field], row);
                        if (field.type == 'number') {
                            rs = scope.digit_grouping(scope.round(rs));
                        }
                        return rs;
                    },
                    formatter: function (field, value, row) { /* Định dạng dữ liệu cho từng ceil được gọi từ định nghĩa cột */
                        var self = this;
                        if (field != null) {
                            if (typeof field.formatter === 'function') {
                                value = field.formatter(value, row);
                            }
                        }
                        return value;
                    },
                    data: {},
                    getStylesHead: function (field) {
                        var styles = {};
                        var width = field.width;
                        if (width != undefined) {
                            width = width + '';
                            if (width.split('%') == 1) {
                                width = width + 'px';
                            }
                            styles.width = width;
                        }
                        return styles;
                    },
                    getStyles: function (field) { /* Bổ trợ tạo css cho ceil */
                        var styles = {};
                        var width = field.width;
                        if (width != undefined) {
                            width = width + '';
                            if (width.split('%') == 1) {
                                width = width + 'px';
                            }
                            styles.width = width;
                        }
                        return styles;
                    },
                    getTiendicho1tre: function () {
                        var rs = 0;
                        angular.forEach(scope.datagrid.data, function (kho, id) {
                            angular.forEach(kho, function (row, index) {
                                rs += row.tiendicho1tre;
                            });
                        });
                        return rs;
                    }
                };
                scope.datagrid.data = {};
                scope.auto_update_extrude_factor = false;
                scope.menu_planning.codinh_luong1tre = true;
                scope.library = {from_data: 0};
                scope.onChange_keysearchGroup = function (value) {
                    var name = scope.keysearch.name;
                    var group_id = scope.keysearch.group_id;
                    if (name && group_id) {
                        $.dm_datagrid.doSearch('tbl_menu_planning', {
                            name: {op: "contains", value: name},
                            group_id: {op: "equal", value: group_id}
                        }, 'and');
                    } else if (name) {
                        $.dm_datagrid.doSearch('tbl_menu_planning', {name: {op: "contains", value: name}}, 'and');
                    } else if (group_id) {
                        $.dm_datagrid.doSearch('tbl_menu_planning', {group_id: {op: "equal", value: group_id}}, 'and');
                    } else {
                        $.dm_datagrid.doSearch('tbl_menu_planning', {name: "clear", group_id: "clear"});
                    }
                };
                scope.menu_planning.onchangeKeysearchName = function (keysearch) {
                    // var name = scope.keysearch.name;
                    // var group_id = scope.keysearch.group_id;
                    // var name_dishes = scope.keysearch.name_dishes;
                    // var conditionObj = {name: "clear", group_id: "clear", name_dishes: "clear",id:"clear"};
                    // if (name) {
                    //     conditionObj['name'] = {op: "contains", value: name};
                    //     conditionObj['id'] = {op: "contains", value: name};
                    //     // setTimeout(function(){
                    //     // },700)
                    // }
                    // if (group_id) {
                    //     conditionObj['group_id'] = {op: "equal", value: group_id};
                    //     // $.dm_datagrid.doSearch('tbl_menu_planning', conditionObj, 'and');
                    // }
                    // if (name_dishes) {
                    //     conditionObj['name_dishes'] = {op: "contains", value: name_dishes};
                    //     // $.dm_datagrid.doSearch('tbl_menu_planning', conditionObj, 'and');
                    // }
                    $.dm_datagrid.doSearch('tbl_menu_planning', {name:"contains" ,group_id:"equal",name_dishes:"contains"}, 'and');

                    
                };
                scope.getTien_an_sang = function(){
                    var rs = 0;
                    if (scope.datagrid.data[1]) {
                        angular.forEach(scope.datagrid.data[1], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.getTien_an_chinh = function(){
                    var rs = 0;
                    if (scope.datagrid.data[2]) {
                        angular.forEach(scope.datagrid.data[2], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.getTien_an_toi = function(){
                    var rs = 0;
                    if (scope.datagrid.data[3]) {
                        angular.forEach(scope.datagrid.data[3], function (food, food_id) {
                            rs += food.thanhtien1nhom;
                        });
                    }
                    return rs;
                };
                scope.onChange_price = function (data, food_change) {
                    food_change.gam_exchange || (food_change.gam_exchange = 1000);
                    var price = Number(food_change.price);
                    food_change.price_kg = scope.round($['/']($['*'](food_change.price, 1000), food_change.gam_exchange));
                    data || (data = scope.datagrid.data);
                    angular.forEach(data, function (kho, meal_key) {
                        var warehouse_id = scope.getMenu_infomationByMeal_key(meal_key).warehouse_id;
                        var inventory = scope.inventory[warehouse_id];
                        inventory || (inventory = {});
                        angular.forEach(kho, function (food, food_id) {
                            if (food.food_id == food_change.food_id && (!inventory[food_id] || scope.allowChangePriceWarehouse(meal_key, food_id))) {
                                food.price = food_change.price;
                                food.price_kg = food_change.price_kg;
                                angular.forEach(food.foods, function (fd, dish_id) {
                                    fd.price_kg = food_change.price_kg;
                                    fd.price = food_change.price;
                                })
                            }
                        })
                    });
                    food_change.thanhtien1nhom = scope.round($['*'](food_change.price, food_change.thucmuatheodvt));
                    food_change.tiendicho1tre = scope.round($['*'](food_change.thucmua1tretheodvt, food_change.price));
                    scope.totalCalculator();
                };
                /* Hàm mở form sao chép thực đơn về thư viện mẫu*/
                scope.library.formCopyToLibrary = function () {
                    var data = {dataType: 'json', async: true, type: 'admin'};
                    scope.library.row = {};
                    angular.forEach(scope.row, function (value, index) {
                        scope.library.row[index] = value;
                    });
                    scope.library.row.group_id = undefined;
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        title: 'Thư viện thực đơn mẫu',
                        draggable: false,
                        fullScreen: false,
                        showButton: false,
                        size: size.small,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'library', data, function (resp) {
                                scope.$apply(function () {
                                    scope.library.groups = resp.data.groups;
                                    scope.library.to_groups = scope.menu_planning.groups;
                                    $(element).html(scope.compile(resp.html, scope));
                                })
                            });
                        }
                    });
                };
                scope.library.copyToAdmin = function () {
                    var data = {
                        meals: JSON.stringify(scope.menu_planning.meals),
                        data: JSON.stringify(scope.datagrid.data),
                        row: scope.library.row,
                        services: scope.selected.services,
                        rate: scope.caloRate(),
                        type: 'add'
                    };
                    data.row.group_key = scope.selected.group['group_key'];
                    data.async = true;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/menu_planning/libraries';
                    process(url, data, function (resp) {
                        dialogClose();
                    });
                };
                /* Hàm mở form danh sách thực đơn mẫu */
                scope.library.formMenu_planning = function () {
                    var data = {dataType: 'json', async: true, type: 'menu_planning'};
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        title: 'Thư viện thực đơn mẫu',
                        draggable: false,
                        fullScreen: false,
                        showButton: false,
                        size: size.wide,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'library', data, function (resp) {
                                scope.$apply(function () {
                                    scope.library.from_groups = resp.data.groups;
                                    scope.library.phong_groups = resp.data.phong_groups;
                                    scope.library.to_groups = scope.menu_planning.groups;
                                    $(element).html(scope.compile(resp.html, scope));
                                    setTimeout(function () {
                                        var height = $('html').height() - 100;
                                        element.find('#list-menu_planning').css({
                                            'max-height': round(height / 2, 0),
                                            'overflow': 'auto'
                                        });
                                        element.find('#list-dishes').css({
                                            'max-height': round(height / 2, 0),
                                            'overflow': 'auto'
                                        });
                                    }, 400);
                                });
                            })
                        }
                    });
                };
                scope.library.from_dataClick = function (value) {
                    if (scope.library.from_data != value) {
                        scope.library.from_data = value;
                        scope.library.from_group_id = undefined;
                        scope.library.to_group_id = undefined;
                        scope.library.unit = undefined;
                        scope.library.unit_groups = {};
                        if (value == 2) {

                        }
                    }
                };
                scope.library.formDish = function (dishes) {
                    var data = {dataType: 'json', async: true, type: 'dish'};
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        title: 'Các món ăn trong thực đơn mẫu đã sao chép',
                        draggable: false,
                        fullScreen: false,
                        showButton: false,
                        size: size.wide,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'library', data, function (resp) {
                                scope.$apply(function () {
                                    scope.library.dishes = dishes;
                                    $(element).html(scope.compile(resp.html, scope));
                                })
                            });
                        }
                    });
                };
                scope.library.onChangeUnit = function () {
                    scope.library.from_group_id = undefined;
                    scope.library.to_group_id = undefined;
                    scope.library.lists = [];
                    scope.library.dishes = [];
                    /*Tải nhóm trẻ đơn vị đang chọn*/
                    var data = {type: 'groups', async: true, unit_id: scope.library.unit.id};
                    process('dinhduong/menu_planning/libraries', data, function (resp) {
                        scope.$apply(function () {
                            scope.library.unit_groups = resp.rows;
                        });
                    }, null, false);
                };
                scope.reloadLibraries = function (group_id) {
                    if (group_id) {
                        scope.library.dishes = {};
                        var data = {
                            group_id: group_id,
                            async: true,
                            from_data: scope.library.from_data,
                            unit: scope.library.unit,
                            type: 'list'
                        };
                        process('dinhduong/menu_planning/libraries', data, function (resp) {
                            scope.$apply(function () {
                                scope.library.lists = resp;
                                angular.forEach(scope.library.lists, function (item, ind) {
                                    item.meals = JSON.parse(item.meals);
                                    var dishes = [];
                                    angular.forEach(item.meals, function (meal) {
                                        var tmp = [];
                                        angular.forEach(meal.dishes, function (dish, dish_id) {
                                            tmp.push(dish.name);
                                        });
                                        if (tmp.length > 0) {
                                            dishes.push(meal.name + ' (' + tmp.join(', ') + ')');
                                        }
                                    });
                                    if (dishes.length) {
                                        item.dishes = dishes;
                                    }
                                });
                            });
                        }, null, false);
                    }
                };
                scope.library.itemSelect = function (item) {
                    if (item.selected) {
                        item.name_new = item.name;
                    }
                    //scope.library.to_group_id = undefined;
                    /* Tạo danh sách món ăn cần sao chép */
                    scope.library.dishes = {};
                    angular.forEach(scope.library.lists, function (item, id) {
                        if (item.selected) {
                            angular.forEach(item.meals, function (meal, meal_define) {
                                angular.forEach(meal.dishes, function (dish, dish_id) {
                                    if ((dish_id + '').split('_').length == 1) {
                                        scope.library.dishes[dish_id] = {
                                            dish_id: dish_id,
                                            name: dish.name,
                                            selected: 1
                                        };
                                    }
                                })
                            })
                        }
                    })
                };
                scope.library.to_groupChange = function (group_id) {
                    if (group_id) {
                        scope.library.checkNameExists();
                    }
                };
                scope.library.checkNameExists = function () {
                    var selected = [];
                    angular.forEach(scope.library.lists, function (item, id) {
                        if (item.selected) {
                            selected.push({id: id, name: item.name_new});
                        }
                    });
                    if (count(selected) > 0 && scope.library.to_group_id) {
                        process('dinhduong/menu_planning/libraries', {
                            selected: selected,
                            type: 'exists',
                            group_id: scope.library.to_group_id,
                            async: true
                        }, function (resp) {
                            scope.$apply(function () {
                                var ids = resp.ids;
                                angular.forEach(scope.library.lists, function (item, id) {
                                    if (in_array(Number(id), ids)) {
                                        item.exists = true;
                                    } else {
                                        item.exists = false;
                                    }
                                })
                            })
                        }, null, false);
                    }
                };
                scope.library.copy = function () {
                    var items = {};
                    angular.forEach(scope.library.lists, function (item, id) {
                        if (item.selected) {
                            items[id] = {name: item.name_new};
                        }
                    });
                    var dishes = {};
                    angular.forEach(scope.library.dishes, function (item, id) {
                        if (item.selected) {
                            dishes[parseInt(id)] = item
                        }
                    });
                    var data = {
                        items: items,
                        type: 'copy',
                        group_id: scope.library.to_group_id,
                        from_data: scope.library.from_data,
                        unit: scope.library.unit,
                        dishes: dishes,
                        async: true
                    };
                    if (count(items) > 0 && scope.library.to_group_id) {
                        process('dinhduong/menu_planning/libraries', data, function (resp) {
                            $('#tbl_menu_planning').datagrid('reload');
                            scope.$apply(function () {
                                scope.library.to_group_id = undefined;
                            })
                        });
                    }
                };

                /* Copy toàn bộ dữ liệu thực đơn mẫu */
                scope.library.copyAll = function () {
                    var data = {
                        type: 'copy_all',
                        from_group_id: scope.library.from_group_id,
                        to_group_id: scope.library.to_group_id,
                        from_unit_id: scope.library.unit.unit_id,
                        async: true
                    };
                    $.messager.confirm('Xác nhận', 'Bạn có chắc chắn muốn sao chép toàn bộ thực đơn mẫu của nhóm đã chọn?', function(r){
                        if (r){
                            process('dinhduong/menu_planning/copy_all', data, function (resp) {
                                alert('Đã copy xong! Vui lòng kiểm tra lại!');
                            });
                        }
                    });
                };
                scope.library.checkListExists = function () {
                    var ids = [];
                    angular.forEach(scope.library.lists, function (item, id) {
                        if (item.selected && item.exists) {
                            ids.push(id);
                        }
                    });
                    if (ids.length > 0) {
                        return true;
                    }
                    return false;
                };
                scope.onSelectAddFood = function (food_select) {
                    if (food_select) {
                        var url = $CFG.remote.base_url + '/doing/dinhduong/menu_planning/foods';
                        var data = {async: true, id: food_select.id};
                        process(url, data, function (resp) {
                            if (!resp) return;
                            var food = resp[0];
                            scope.$apply(function () {
                                food.dish_name = food.name.replace('Gạo', 'Cơm').replace('gạo', 'cơm');
                                food.quantity = 10;
                                if (scope.inventory[scope.selected.meal.warehouse_id]) {
                                    if (scope.inventory[scope.selected.meal.warehouse_id][food.food_id]) {
                                        if (scope.inventory[scope.selected.meal.warehouse_id][food.food_id].value > 0) {
                                            food.price = scope.inventory[scope.selected.meal.warehouse_id][food.food_id].price;
                                        }
                                    }
                                }
                                scope.menu_planning.food_add = food;
                                scope.menu_planning.selected.food = undefined;
                            });
                        }, function () {
                        }, false);
                        $('#food').combobox('clear');
                    } else {
                    }
                };
                scope.addFoodToTempDish = (food_selected) => {
                    if(food_selected) {
                        //kt = true;
                        // for(var food_index in scope.foodsAdded) {
                        //     var food = scope.foodsAdded[food_index];
                        //     if(food_selected.id == food.food_id) {
                        //         kt = false;
                        //     }
                        // }
                        // for(var food_index in scope.selected.dish.ingredient) {
                        //     var food = scope.selected.dish.ingredient[food_index];
                        //     if(food_selected.id == food.food_id) {
                        //         kt = false;
                        //     }
                        // }
                        //if(kt) {
                            var url = $CFG.remote.base_url + '/doing/dinhduong/menu_adjust/foods';
                            var data = {async: true, id: food_selected.id};
                            process(url, data, function (resp) {
                                if (!resp) return;
                                var food = resp[0];
                                scope.$apply(function () {
                                    var id = food.food_id;
                                    food.quantity = 10;
                                    food.quantity_edit = 10;
                                    scope.foodsAdded[id] = food;
                                    scope.selected.foodAdded = null;
                                    angular.forEach(food.nutritions, function (val, nuti_key) {
                                        delete food[nuti_key];
                                    });
                                });
                            }, function () {
                            }, false);
                        //}
                    }
                };
                scope.initFormDish = function () {
                    scope._temp = {random: Math.random().toString().split('.')[1]}
                };
                scope.initFormAddFood = function () {
                    scope.menu_planning.selected || (scope.menu_planning.selected = {});
                    scope.menu_planning.selected.food_ids = [];
                    scope.menu_planning.selected.food = undefined;
                };
                scope.keyUpFood = function (e, index, warehouse_id, name) {
                    var id_input = '';
                    if (e.which == 13 || e.which == 40) { /*Enter*/
                        /*ArrowDown*/
                        if (index < count(scope.datagrid.data[warehouse_id]) - 1) {
                            index += 1;
                            id_input = [name, warehouse_id, index].join('_');
                        }
                    } else if (e.which == 38) { /*ArrowUp*/
                        if (index > 0) {
                            index -= 1;
                            id_input = [name, warehouse_id, index].join('_');
                        }
                    }
                    if (id_input != '') {
                        $('input#' + id_input).focus();
                    }
                };
                scope.onChange_price_kg = function (data, food_change) {
                    food_change.gam_exchange || (food_change.gam_exchange = 1000);
                    var price_kg = Number(food_change.price_kg);
                    food_change.price = $['/']($['*'](price_kg, food_change.gam_exchange), 1000);
                    data || (data = scope.datagrid.data);
                    angular.forEach(data, function (kho, index) {
                        angular.forEach(kho, function (food, index) {
                            if (food.food_id == food_change.food_id) {
                                food.price = food_change.price;
                                food.price_kg = price_kg;
                                angular.forEach(food.foods, function (fd, dish_id) {
                                    fd.price_kg = food_change.price_kg;
                                    fd.price = food_change.price;
                                })
                            }
                        })
                    });
                    food_change.thanhtien1nhom = scope.round($['*'](food_change.price, food_change.thucmuatheodvt));
                    food_change.tiendicho1tre = scope.round($['*'](food_change.thucmua1tretheodvt, food_change.price));
                    scope.totalCalculator();
                };
                scope.onFocus_sotre = function () {
                    var data = scope.datagrid.data;
                    angular.forEach(data, function (kho, index) {
                        angular.forEach(kho, function (food, index) {
                            food.thucmua1tretheodvt_old = food.thucmuatheodvt / scope.row.sotre;
                        });
                    });
                };
                scope.onChange_thanhtien1nhom = function (food, ignore_cal) {
                    if (food.price > 0) {
                        food.thucmuatheodvt = $['/'](food.thanhtien1nhom, food.price);
                        food.thucmuatheodvt_view = round(food.thucmuatheodvt);
                        food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt, scope.row.sotre);
                        food.thucmua1nhom = $['/']($['*'](food.thucmuatheodvt, food.gam_exchange), 1000);
                        food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                        food.thucan1nhom = food.thucmua1nhom;
                        if (food.extrude_factor) {
                            food.thucan1nhom = $['-'](food.thucmua1nhom, $['*']($['/'](food.thucmua1nhom, 100), food.extrude_factor));
                        }
                        food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                        food.luong1tre_root = food.luong1tre;
                        food.tiendicho1tre = $['/'](food.thanhtien1nhom, scope.row.sotre);
                        scope.foodRounds(food, 'thucmuatheodvt');
                        scope.divide_luong1tre(food);
                        if (!ignore_cal) {
                            scope.totalCalculator(scope.datagrid.data);
                        }
                    }
                };
                scope.onChange_Sotre = function () {
                    scope.row.sotre_old || (scope.row.sotre_old = scope.row.sotre);
                    var sotre_old = scope.row.sotre_old;
                    if (scope.row.sotre + '' == 'Infinity') {
                        scope.row.sotre = 0;
                    }
                    if (scope.row.sotre == 0 || scope.row.sotre_old == scope.row.sotre) {
                        return;
                    }
                    var sotre = scope.row.sotre;
                    var data = scope.datagrid.data;
                    var sotre_chenh = scope.row.sotre - scope.row.sotre_old;
                    angular.forEach(data, function (kho, index) {
                        angular.forEach(kho, function (food, index) {
                            if (!scope.menu_planning.codinhthucmua) { /* Cố định lượng 1 trẻ */
                                food.thucmuatheodvt = $['*'](food.thucmua1tretheodvt_old, scope.row.sotre);
                                scope.onChange_thucmuatheodvt(food);
                            } else {
                                scope.onChange_thucmuatheodvt(food);
                            }
                        });
                    });
                    scope.totalCalculator();
                    scope.row.sotre_old = scope.row.sotre;
                };
                scope.onChange_luong1tre = function (food, ignore_cal, callback) {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    food.luong1tretheodvt = $['/'](food.luong1tre, food.gam_exchange);
                    food.thucan1nhom = $['/']($['*'](food.luong1tre, scope.row.sotre), 1000);
                    food.thucmua1nhom = food.thucan1nhom;
                    if (food.extrude_factor) {
                        food.thucmua1nhom = $['/']($['*'](100, food.thucan1nhom), $['-'](100, food.extrude_factor));
                    }
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucmuatheodvt = $['*']($['/'](food.thucmua1nhom, food.gam_exchange), 1000);
                    if (typeof callback === 'function') {
                        callback(food);
                    }
                    food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt, scope.row.sotre);
                    food.thanhtien1nhom = $['*'](food.price, food.thucmuatheodvt);
                    scope.foodRounds(food, 'luong1tre');
                    scope.divide_luong1tre(food);
                    if (!ignore_cal) {
                        scope.totalCalculator();
                    }
                };
                scope.onChange_thucan1nhom = function (food) {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucan1nhom = Number(food.thucan1nhom);
                    food.thucmua1nhom = thucan1nhom;
                    if (food.extrude_factor) {
                        food.thucmua1nhom = $['/']($['*'](100, food.thucan1nhom), $['-'](100, food.extrude_factor));
                    }
                    food.thucmuatheodvt = $['*']($['/'](food.thucmua1nhom, food.gam_exchange), 1000);
                    food.thucmua1tretheodvt = $['/'](food.thucmuatheodvt, scope.row.sotre);
                    food.thucmua1nhom = food.thucmuatheodvt * food.gam_exchange / 1000;
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food, 'thucan1nhom');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.totalCalculator();
                };
                scope.onChange_thucmua1tre = (food) => {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1tre = Number(food.thucmua1tre);
                    food.thucmua1nhom = $['/']($['*'](thucmua1tre, scope.row.sotre), 1000);
                    food.thucmuatheodvt = $['*']($['/'](food.thucmua1nhom, food.gam_exchange), 1000);
                    food.thucmua1tretheodvt = scope.round($['/'](food.thucmuatheodvt, scope.row.sotre), 4);
                    food.thucan1nhom = food.thucmua1nhom;
                    if (food.extrude_factor) {
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom / 100 * food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food, 'thucmua1tre');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.totalCalculator();
                }
                /*Thay đổi thực mua một nhóm theo kg*/
                scope.onChange_thucmua1nhom = function (food) {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1nhom = Number(food.thucmua1nhom);
                    food.thucmua1tre = $['*']($['/'](thucmua1nhom, scope.row.sotre), 1000);
                    food.thucmuatheodvt = $['*']($['/'](thucmua1nhom, food.gam_exchange), 1000);
                    food.thucmua1tretheodvt = scope.round($['/'](food.thucmuatheodvt, scope.row.sotre), 4);
                    food.thucan1nhom = food.thucmua1nhom;
                    if (food.extrude_factor) {
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom / 100 * food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food, 'thucmua1nhom');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.totalCalculator();
                };
                scope.onBlur_extrude_factor = function (food) {
                    food.extrude_factor = Number(food.extrude_factor);
					scope.extrudeFactorBlur(food);
                    if (food.extrude_factor != food.extrude_factor_root) {
                        if (scope.sys.configs.cdkp_auto_update_to_tp_truong) {
                            scope.updateExtrude_factor(food);
                        } else {
                            $.messager.confirm('Xác nhận', '<div style="font-size:14px">Bạn vừa thay đổi hệ số thải bỏ. Có muốn cập nhật hệ số mới vào thực phẩm trường?<br/></div>', function (r) {
                                if (r) {
                                    scope.updateExtrude_factor(food);
                                }
                            });
                        }
                    }
                };

                /* Cập nhật HSTB, DVT, Quy đổi gam về TP trường */
                scope.updateUnitFoodDetail = function(food, key){
                    var new_value = food[key];
                    var params = {
                        id: food.food_id,
                        key: key,
                        value: new_value
                    };
                    process('dinhduong/menu_planning/updateUnitFoodDetail', params, function(resp){
                        food[key+'_root'] = food[key];
                    },null,false);
                };

				scope.extrudeFactorBlur = function(food){
					// Cap nhat he so thai bo trong data
					angular.forEach(scope.menu_planning.data, function (kho) {
						angular.forEach(kho, function (fd) {
							if(fd.food_id === food.food_id){
								fd.extrude_factor = Number(food.extrude_factor);
								angular.forEach(fd.foods, function (fd_meal) {
									fd_meal.extrude_factor = Number(food.extrude_factor);
								});
							}
						});
					});
					// Cap nhat he so thai bo trong mon an
					angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (fdish_meal, fd_food_id) {
								if(fd_food_id == food.food_id){
									fdish_meal.extrude_factor = Number(food.extrude_factor);
								}
                            });
                        });
                    });
                };
                scope.onChange_group = function () {
                    var i;
                    for (i in scope.menu_planning.groups) {
                        if (scope.row.group_id == scope.menu_planning.groups[i].id) {
                            scope.selected.group = scope.menu_planning.groups[i];
                            if (scope.menu_planning.services[scope.row.group_id]) {
                                scope.row.services = scope.menu_planning.services[scope.row.group_id].services;
                            }
                        }
                    }
                };
                scope.onChange_tien1tre = function () {
                    scope.totalCalculator();
                };
                scope.onChange_extrude_factor = function (food) {
                    scope.onChange_thucmuatheodvt(food);
                };
                scope.onChange_gam_exchange = function (food) {
                    scope.onChange_price(scope.datagrid.data, food);
                    scope.onChange_thucmuatheodvt(food);
                    angular.forEach(food.foods, function (fd, ind) {
                        fd.gam_exchange = food.gam_exchange;
                    })
                };

                // Kiểm tra dữ liệu thay đổi & bật popup
                scope.gam_exchangeBlur = function(food){
                    if(food.gam_exchange != food.gam_exchange_root) {
                        if(scope.sys.configs.cdkp_auto_update_to_tp_truong){
                            scope.updateUnitFoodDetail(food, 'gam_exchange');
                        }else{
                            $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Bạn vừa thay đổi quy đổi gam. Có muốn cập nhật giá trị mới vào thực phẩm gốc?<br/></div>', function(r){
                                if (r){
                                    scope.updateUnitFoodDetail(food, 'gam_exchange');
                                }
                            });
                        }
                    }
                };

                scope.updateExtrude_factor = function (food) {
                    var params = {id: food.food_id, value: food.extrude_factor};
                    process('dinhduong/menu_planning/updateExtrude_factor', params, function (resp) {
                        food.extrude_factor_root = food.extrude_factor;
                    }, null, false);
                };
                scope.onChange_thucmua1tretheodvt = function (food) {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmua1tretheodvt = Number(food.thucmua1tretheodvt);
                    food.thucmuatheodvt = $['*'](thucmua1tretheodvt, scope.row.sotre);
                    food.thucmua1nhom = food.thucmuatheodvt * food.gam_exchange / 1000;
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucan1nhom = food.thucmua1nhom;
                    if (food.extrude_factor) {
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom / 100 * food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    scope.foodRounds(food, 'thucmua1tretheodvt');
                    food.thanhtien1nhom = $['*'](food.thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    scope.totalCalculator();
                };
                /*Thay đổi thực mua một nhóm theo đơn vị tính*/
                scope.onChange_thucmuatheodvt = function (food, calc) {
                    food.gam_exchange || (food.gam_exchange = 1000);
                    var thucmuatheodvt = Number(food.thucmuatheodvt);
                    food.thucmua1tretheodvt = $['/'](thucmuatheodvt, scope.row.sotre);
                    food.thucmua1nhom = thucmuatheodvt * food.gam_exchange / 1000;
                    food.thucmua1tre = $['*']($['/'](food.thucmua1nhom, scope.row.sotre), 1000);
                    food.thucan1nhom = food.thucmua1nhom;
                    if (food.extrude_factor) {
                        food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom / 100 * food.extrude_factor);
                    }
                    food.luong1tre = $['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000);
                    food.luong1tre_root = food.luong1tre;
                    scope.foodRounds(food, 'thucmuatheodvt');
                    food.thanhtien1nhom = $['*'](thucmuatheodvt, food.price);
                    scope.divide_luong1tre(food);
                    if (!calc) {
                        scope.totalCalculator();
                    }
                };
                scope.onChange_tiendicho1tre = function (food) {
                };
                scope.getNormBuasang = function () {
                    var rs = {biggest_rate: 0, smallest_rate: 0};
                    if (scope.row.meal_selection[1].selected) {
                        if (count(scope.menu_planning.meals.buasang.dishes) > 0) {
                            if (scope.selected.group) {
                                var chenh = scope.selected.group.biggest_rate - scope.selected.group.smallest_rate;
                                if (scope.selected.group.group_children == 0) {
                                    rs.biggest_rate = 80 - scope.selected.group.biggest_rate;
                                } else {
                                    rs.biggest_rate = 70 - scope.selected.group.biggest_rate;
                                }
                                if (rs.biggest_rate < 0) {
                                    rs.biggest_rate = 0;
                                } else {
                                    if (rs.biggest_rate >= 15) {
                                        rs.smallest_rate = 10;
                                    } else if (rs.biggest_rate >= 10) {
                                        rs.smallest_rate = 5;
                                    } else {
                                    }
                                }
                            }
                        }
                    }
                    return rs;
                };
                scope.getNormBuachinh = function () {
                    var rs = {biggest_rate: 0, smallest_rate: 0};
                    if (scope.row.meal_selection[2].selected) {
                        if (scope.selected.group) {
                            rs.biggest_rate = scope.selected.group.biggest_rate;
                            rs.smallest_rate = scope.selected.group.smallest_rate;
                        }
                    }
                    return rs;
                };
                scope.getNormSelected = function () {
                    var rs = {biggest_rate: 0, smallest_rate: 0};
                    if (scope.row.meal_selection[1].selected) {
                        if (scope.selected.group) {
                            rs.smallest_rate += scope.selected.group.nutritions.calo_rate_morning_low;
                            rs.biggest_rate += scope.selected.group.nutritions.calo_rate_morning;
                        }
                    }
                    if (scope.row.meal_selection[2].selected) {
                        if (scope.selected.group) {
                            rs.smallest_rate += scope.selected.group.nutritions.calo_rate_low;
                            rs.biggest_rate += scope.selected.group.nutritions.calo_rate;
                        }
                    }
                    return rs;
                };

                // Kiểm tra & cảnh báo lượng 1 trẻ chưa chính xác?
                scope.check_error_quantity_edit = function (food){
                    if(food.quantity_edit!=food.quantity && food.gam_exchange>0 && food.quantity_edit>0){
                        var luong1tre = food.thucmua1tretheodvt*food.gam_exchange/1000;
                        if(luong1tre==food.quantity && food.quantity/food.quantity_edit==2) {
                            return true;
                        }
                    }
                    return false;
                }

                /*
                * Tính toán bảng khẩu phần dinh dưỡng
                * */
                scope.calcNutrition = function (meals) { /* Tính toán dữ liệu để đưa ra bảng dinh dưỡng */
                    meals || (meals = scope.menu_planning.meals);
                    var rs = {
                        tien: 0,
                        animal_protein: 0,
                        vegetable_protein: 0,
                        animal_fat: 0,
                        vegetable_fat: 0,
                        sugar: 0,
                        calo: 0
                    };
                    angular.forEach(meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                if ( scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                                    food.nutritions || (food.nutritions = {});
                                    food.nutritions.protein || (food.nutritions.protein = 0);
                                    food.nutritions.fat || (food.nutritions.fat = 0);
                                    food.nutritions.sugar || (food.nutritions.sugar = 0);
                                    food.nutritions.calo || (food.nutritions.calo = 0);
                                    if (food.is_meat + '' == '1' || food.is_meat + '' == 'true') {
                                        food.is_meat = true;
                                    } else {
                                        food.is_meat = false;
                                    }
                                    //Kiểm tra & xử lý t/h lượng 1 trẻ lưu chưa chính xác
                                    if(scope.sys.configs.tdm_check_error_quantity_edit){
                                        if(scope.check_error_quantity_edit(food)){
                                            food.quantity_edit = food.quantity;
                                        }
                                    }
                                    if (food.is_meat) { /*Nếu là động vật*/
                                        rs.animal_protein += $['*'](food.nutritions.protein, food.quantity_edit) / 100;
                                        rs.animal_fat += $['*'](food.nutritions.fat, food.quantity_edit) / 100;
                                    } else {
                                        rs.vegetable_protein += $['*'](food.nutritions.protein, food.quantity_edit) / 100;
                                        rs.vegetable_fat += $['*'](food.nutritions.fat, food.quantity_edit) / 100;
                                    }
                                    rs.sugar += $['*'](food.nutritions.sugar, food.quantity_edit) / 100;
                                    rs.calo += scope.getCalo(food, 'quantity_edit');
                                }
                            });
                        });
                    });
                    return rs;
                };
                /*
                * Tính toán tổng tiền
                * */
                scope.totalCalculator = function (data) { /* Tính toán dữ liệu để đưa ra bảng dinh dưỡng */
                    scope.row.thanhtien1nhom = 0;
                    data || (data = scope.datagrid.data);
                    angular.forEach(data, function (kho, warehouse_id) {
                        angular.forEach(kho, function (food, index) {
                            if (!food.thucmuatheodvt) {
                                var thucan1nhom = $['/']($['*'](food.luong1tre, scope.row.sotre), 1000);
                                var thucmua1nhom = thucan1nhom;
                                if (food.extrude_factor) {
                                    thucmua1nhom = $['/']($['*'](100, thucan1nhom), $['-'](100, food.extrude_factor));
                                }
                                food.thucmuatheodvt = round($['*']($['/'](thucmua1nhom, food.gam_exchange), 1000));
                            }
                            if(!food.thucmua1tre) {
                               food.thucmua1tre = round($['/']($['*'](food.thucmuatheodvt, food.gam_exchange), scope.row.sotre), 4);
                            }
                            
                            food.thanhtien1nhom = scope.round($['*'](food.thucmuatheodvt, food.price),2);
                            scope.row.thanhtien1nhom = $['+'](scope.row.thanhtien1nhom, food.thanhtien1nhom);
                        });
                    });
                    var service = scope.getTiendichvu(scope.row.group_id);
                    scope.row.tongtien_dichvu = $['*'](service, scope.row.sotre);
                    scope.row.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), scope.row.thanhtien1nhom), scope.row.tongtien_dichvu);
                    scope.tong = scope.calcNutrition(scope.menu_planning.meals);
                };

                scope.divide_luong1tre = function (item) { /* Chia tỉ lệ thay đổi của lượng 1 trẻ cho các thực phẩm từng món ăn liên quan */
                    /* Tính tổng lượng 1 trẻ chuẩn theo món ăn */
                    if (count(item.foods) == 1) {
                        angular.forEach(item.foods, function (fd, ind) {
                            fd.quantity_edit = item.luong1tre;
                        });
                    } else {
                        if (count(item.foods) == 1) {
                            var first = item.foods[Object.keys(item.foods)[0]];
                            first.quantity_edit = Number(item.luong1tre);
                            return;
                        }
                        var tong_chuan = 0;
                        var food_max = {quantity: 0};
                        angular.forEach(item.foods, function (food, dish_id) {
                            if (Number(food.quantity == 0)) {
                                food.quantity = 10;
                            }
                            tong_chuan = $['+'](tong_chuan, food.quantity);
                            if (food_max.quantity < food.quantity) {
                                food_max = food;
                            }
                        });
                        var tong_them = $['-'](item.luong1tre, tong_chuan);
                        /*Nếu có thực phẩm số lượng mặc định bằng 0 và số thêm dư thì gán số thêm này cho các thực phẩm này*/
                        var tong_phan = 0;
                        var da_them = 0;
                        angular.forEach(item.foods, function (food, index) {
                            var tile = $['/'](food.quantity, tong_chuan);
                            var them = scope.round($['*'](tong_them, tile), 8);
                            da_them = $['+'](da_them, them);
                            food.quantity_edit = $['+'](food.quantity, them);
                            /*tong_them -= food.quantity;*/
                        });
                        /*cộng nốt số dư của các lần làm tròn vào thực phẩm đầu tiên*/
                        if (tong_them - da_them != 0) {
                            food_max.quantity_edit = scope.round($['+']($['-'](tong_them, da_them), food_max.quantity_edit), 8);
                        }
                    }
                };
                scope.roundBuy = function () {
                    angular.forEach(scope.datagrid.data, function (items, key) {
                        angular.forEach(items, function (item, key) {
                            thucmuatheodvt = Number(item.thucmuatheodvt);
                            if (thucmuatheodvt < 0.005) {
                                thucmuatheodvt = 0.01;
                            } else if (thucmuatheodvt < 0.07) {
                                thucmuatheodvt = round(thucmuatheodvt, 2);
                            } else {
                                thucmuatheodvt = round(thucmuatheodvt, 1)
                            }
                            item.thucmuatheodvt = thucmuatheodvt;
                            scope.onChange_thucmuatheodvt(item);
                            scope.divide_luong1tre(item);
                            scope.foodRounds(item, 'thucmuatheodvt');
                        });
                    });
                    scope.totalCalculator();
                };
                scope.round_thucmuatheodvt = function (value, num) {
                    num || (num = 1);
                    var value_arr = (value + '').split('.');
                    if (value_arr[1] != undefined) {
                        var thanhphan = Number('0.' + value_arr[1]);
                        if (thanhphan < 1) {
                            if (thanhphan < 0.00005) {
                                thanhphan = 0.0001;
                            } else if (thanhphan < 0.0005) {
                                thanhphan = 0.001;
                            } else if (thanhphan < 0.005) {
                                thanhphan = 0.01;
                            } else if (thanhphan <= 0.06) {
                                thanhphan = scope.round(thanhphan, 2);
                            } else {
                                thanhphan = scope.round(thanhphan, num);
                            }
                        } else {
                            if (thanhphan <= 0.05) {
                                thanhphan = 0.0;
                            } else {
                                thanhphan = scope.round(thanhphan, num);
                            }
                        }
                    }
                    return value;
                };
                scope.foodSelected = function (food_selected, menuignore_ids) {
                    if (food_selected) {
                        if (!scope.lengthSelectDish) {
                            scope.lengthSelectDish = Object.keys(scope.selected.dish.ingredient).length
                        }
                        var url = $CFG.remote.base_url + '/doing/dinhduong/menu_planning/foods';
                        var data = {async: true, id: food_selected.id};
                        process(url, data, function (resp) {
                            if (!resp) return;
                            var food = resp[0];
                            scope.$apply(function () {
                                var id = food.food_id;
                                food.quantity = 0;
                                scope.foodAdded[id] = food;
                                food.name_edit = food.name;
                                $('#food' + scope._temp.random).combobox('clear');
                                scope.menu_planning.dish_selected = null;
                            });
                        }, function () {
                        }, false);
                    }
                };
                scope.delFood = function (food_id, type) {
                    switch (type) {
                        case 'old':
                            angular.forEach(scope.selected.dish.ingredient, function (dish, key) {
                                if (food_id == key) {
                                    delete scope.selected.dish.ingredient[key];
                                }
                            });
                            break;
                        case 'new':
                            angular.forEach(scope.foodAdded, function (dish, key) {
                                if (food_id == key) {
                                    delete scope.foodAdded[key];
                                }
                            });
                            break;
                    }
                };
                scope.getWarehouse_ids = function () {
                    var rs = [];
                    angular.forEach(scope.row.meal_selection, function(meal, meal_define){
                        if(meal.selected && meal.visible) {
                            rs.push(meal_define);
                        }
                    });
                    return rs;
                };
                scope.menu_planning.getRow = function () {
                    var rs = undefined;
                    if ($("#tbl_menu_planning").length) {
                        rs = $("#tbl_menu_planning").datagrid("getSelected");
                    }
                    return rs;
                };
                scope.openDialogPrint = function () {
                    var self = this;
                    var urls_export = [$CFG.remote.base_url, $CFG.project, 'menu_planning', 'exportPrint'];
                    var btn = $('<a id="btn_preview_lenthupham" title="In"><span class="glyphicon glyphicon-print fa-2x"></span></a>');
                    var link = $('<a href="" title="Xem"><span class="glyphicon glyphicon-print fa-2x"></span></a>');
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        title: 'Chọn ngày in',
                        draggable: true,
                        fullScreen: false,
                        showButton: false,
                        size: 350,
                        scope: scope,
                        content: $CFG.project + '/menu_planning/print.html'
                    });
                };
                scope.menu_planning.sumCalo = function (meal) {
                    var rs = 0;
                    angular.forEach(scope.datagrid.data, function (kho, warehouse_id) {
                        angular.forEach(kho, function (food, food_id) {
                            if (!scope.apdungcongthuc) {
                                rs += $['*'](scope.co_cau(null).protein, food.luong1tre) * (food.nutritions.protein / 100) + $['*'](scope.co_cau(null).fat, food.luong1tre) * (food.nutritions.fat / 100) + $['*'](scope.co_cau(null).sugar, food.luong1tre) * (food.nutritions.sugar / 100)
                            } else {
                                rs += $['*'](scope.co_cau(null).protein, food.luong1tre) * (food.nutritions.protein / 100) + $['*'](scope.co_cau(null).fat, food.luong1tre) * (food.nutritions.fat / 100) + $['*'](scope.co_cau(null).sugar, food.luong1tre) * (food.nutritions.sugar / 100)
                            }
                        })
                    });
                    scope.tong.calo = rs;
                    return rs;
                };
                scope.sumCaloFood = function (food, key) {
                    var rs = 0;
                    key || (key = 'quantity_edit');
                    if (!scope.apdungcongthuc) {
                        rs += $['*'](scope.co_cau(null).protein, food[key]) * (food.nutritions.protein / 100) + $['*'](scope.co_cau(null).fat, food[key]) * (food.nutritions.fat / 100) + $['*'](scope.co_cau(null).sugar, food[key]) * (food.nutritions.sugar / 100)
                    } else {
                        rs += $['*'](scope.co_cau(null).protein, food[key]) * (food.nutritions.protein / 100) + $['*'](scope.co_cau(null).fat, food[key]) * (food.nutritions.fat / 100) + $['*'](scope.co_cau(null).sugar, food[key]) * (food.nutritions.sugar / 100)
                    }
                    return rs;
                };
                scope.getStyleInventory = function (warehouse_id, food) {
                    var food_id = food.food_id;
                    var style = {};
                    scope.inventory[warehouse_id] || (scope.inventory[warehouse_id] = {});
                    if (scope.inventory[warehouse_id][food_id]) {
                        if (!scope.inventory[warehouse_id][food_id].gomarket) { /*Nếu tồn kho nhỏ hơn giới hạn tồn thì tô màu đỏ*/
                            if (scope.inventory[warehouse_id][food_id].value > 0 && scope.inventory[warehouse_id][food_id].value <= food.min_inventory) {
                                style['border-left'] = '4px solid #ffe200';
                            } else if (scope.inventory[warehouse_id][food_id].value <= 0) {
                                scope.inventory[warehouse_id][food_id].value = 0;
                                style['border-left'] = '4px solid #ff6060';
                            } else {
                                style['border-left'] = '4px solid #7ac54b';
                            }
                        } else { /*Đi chợ*/
                        }
                    }
                    return style;
                };
                /*Chia lại thực phẩm theo tỉ lệ từng bữa*/
                scope.mealsProcess = function (foods) {
                    angular.forEach(foods, function (food, index) {
                        if (food.thucmuatheodvt) {
                            scope.onChange_thucmuatheodvt(food);
                        } else {
                            scope.onChange_luong1tre(food);
                        }
                    })
                };
                /*scope.delDish_backup = function (meal, dish) { /!*Đánh dấu thực phẩm phải xóa*!/
                    angular.forEach(scope.datagrid.data, function () {

                    });
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        food.delete = true;
                    });
                    /!* Xóa thực phẩm trong kho theo món ăn*!/
                    var tmp_foods = {};
                    angular.forEach(scope.datagrid.data, function (foods, meal_key) {
                        angular.forEach(foods, function (food, food_id) {
                            var fds = [];
                            angular.forEach(food.foods, function (fd, index) {
                                if (fd.delete != true) {
                                    fds.push(fd);
                                } else {
                                    food.luong1tre -= fd.quantity_edit;
                                }
                            });
                            food.foods = fds;
                            if (food.luong1tre > 0 && fds.length > 0) {
                                tmp_foods[food_id] = food;
                            }
                        });
                    });
                    scope.datagrid.data[meal.warehouse_id] = tmp_foods;
                    angular.forEach(meal.dishes, function (ds, ds_id) {
                        if (ds.id == dish.id) {
                            delete meal.dishes[ds_id];
                        }
                    });
                    /!* Xóa món ăn*!/
                    scope.totalCalculator(scope.datagrid.data);
                };*/
                scope.delDish = function (meal, dish, isConfirm = true) { /*Đánh dấu thực phẩm phải xóa*/
                    if (isConfirm) {
                        var r = confirm('Bạn có chắc chắn xóa?');
                        if (!r) return;
                    }
                    angular.forEach(scope.datagrid.data, function (data) {
                        angular.forEach(data, function (food) {
                            angular.forEach(food.foods, function (f) {
                                f.deleted = false;
                            });
                        });
                    });
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        food.deleted = true;
                    });
                    /* Xóa thực phẩm trong kho theo món ăn*/
                    var tmp_foods = {};
                    angular.forEach(scope.datagrid.data, function (foods, meal_key) {
                        var data = {};
                        angular.forEach(foods, function (food, food_id) {
                            var fds = [];
                            var luong1tre = 0;
                            angular.forEach(food.foods, function (fd, index) {
                                if (!fd.deleted) {
                                    fds.push(fd);
                                    luong1tre = $['+'](luong1tre, fd.quantity_edit);
                                }
                            });
                            if(luong1tre != food.luong1tre) {
                                food.luong1tre = luong1tre;
                                scope.onChange_luong1tre(food, true);
                            }
                            food.foods = fds;
                            if (count(fds) > 0) {
                                data[food_id] = food;
                            }
                        });
                        scope.datagrid.data[meal_key] = data;
                    });
                    var dishes = {};
                    var i = 0;
                    angular.forEach(meal.dishes, function (ds, ds_id) {
                        if (ds.id != dish.id) {
                            dishes[ds_id+''+i] = ds;
                        }
                    });
                    meal.dishes = dishes;
                    /* Xóa món ăn*/
                    scope.totalCalculator(scope.datagrid.data);
                };
                scope.showMealsCaloInfo = function () {
                    var self = this;
                    $.dm_datagrid.showAddForm({
                        module: '',
                        title: 'Thông tin món ăn',
                        draggable: true,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            var html = ``;
                            loadForm($CFG.project + '/menu_planning', 'quantity_info', {type: 0}, function (html) {
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(html, scope));
                                    })
                                })
                            });
                        }
                    });
                };
                scope.showNutritionInfo = function () {
                    var self = this;
                    $.dm_datagrid.showAddForm({
                        module: '',
                        title: 'Thông tin món ăn',
                        draggable: true,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            var html = ``;
                            loadForm($CFG.project + '/menu_planning', 'quantity_info', {type: 1}, function (html) {
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(html, scope));
                                    })
                                })
                            });
                        }
                    });
                };
                scope.showPartPLGInfo = function () {
                    $.dm_datagrid.showAddForm({
                        module: '',
                        title: 'Thông tin món ăn',
                        draggable: true,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            var html = ``;
                            loadForm($CFG.project + '/menu_planning', 'quantity_info', {type: 1}, function (html) {
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(html, scope));
                                    })
                                })
                            });
                        }
                    });
                };
                scope.detailByMeal.quantityChange = function (food_change) {
                    food_change.quantity = food_change.quantity_edit;
                    angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                food.quantity = food.quantity_edit;
                            });
                        });
                    });
                    angular.forEach(scope.datagrid.data, function (kho, warehouse_id) {
                        angular.forEach(kho, function (food, food_id) {
                            food.luong1tre = 0;
                            angular.forEach(food.foods, function (fd, ind) {
                                food.luong1tre = $['+'](food.luong1tre, fd.quantity);
                            });
                            scope.onChange_luong1tre(food, true, false);
                        });
                    });
                    scope.totalCalculator();
                };
                scope.detailByMeal.nutritionChange = function () {
                    scope.row.nutrition_fixed = 1;
                    /*Trường hợp người dùng tự sửa lại lượng chất trong trực phẩm*/
                    scope.totalCalculator();
                };
                scope.detailByMeal.formShow = function (type) {
                    var meals = scope.menu_planning.meals;
                    var data = {};
                    scope.nutritions = {};
                    var first = 0;
                    var title = 'Thông tin thực phẩm theo bữa';
                    if (type) {
                        title = 'Thông tin thực phẩm từng món';
                    }
                    angular.forEach(meals, function (meal, meal_define) {
                        data[meal_define] = {
                            name: meal.name,
                            define: meal.define,
                            warehouse_id: meal.warehouse_id,
                            foods: {}
                        };
                        if (count(meal.dishes) > 0 && first == 0) {
                            meal.is_header = true;
                            first++;
                        }
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                if (typeof food_id != 'undefined') {
                                    scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                                    scope.nutritions[food_id].protein || (scope.nutritions[food_id].protein = 0);
                                    scope.nutritions[food_id].fat || (scope.nutritions[food_id].fat = 0);
                                    scope.nutritions[food_id].sugar || (scope.nutritions[food_id].sugar = 0);
                                    if (!data[meal_define].foods[food_id]) {
                                        var fd = {
                                            food_id: food_id,
                                            name: food.name,
                                            price: food.price,
                                            quantity: food.quantity,
                                            nutritions: scope.nutritions[food_id],
                                            foods: [],
                                            quantity_edit: 0
                                        };
                                        data[meal_define].foods[food_id] = fd;
                                    }
                                    data[meal_define].foods[food_id].quantity_edit = $['+'](data[meal_define].foods[food_id].quantity_edit, food.quantity_edit);
                                    food.nutritions = scope.nutritions[food_id];
                                    data[meal_define].foods[food_id].foods.push(food);
                                }
                            });
                        });
                    });
                    scope.detailByMeal.meals = data;
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        action: 'formMealDetail',
                        title: title,
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'formMealDetail', {type: type}, function (resp) {
                                var body_h = $('body').height() - 300;
                                var form = '<div style="padding:10px;text-align:center;bachground:#ececec">' + resp + '</div>';
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(form, scope));
                                    });
                                });
                                setTimeout(function () {
                                    $('.detail-meal-contaner.container-scroll').scroll(function () {
                                        var top = $(this).children().position().top;
                                        $(this).find('thead.keep').css({'margin-top': -1 * top});
                                    }).css({'max-height': body_h});
                                }, 400)
                            });
                        }
                    });
                };
                scope.detailByMeal.getPLG = function (foods) {
                    var rs = 0;
                    if (foods) {
                        angular.forEach(foods, function (food, food_id) {
                            rs += scope.co_cau(null).protein * (food.quantity_edit * food.nutritions.protein / 100) + scope.co_cau(null).fat * (food.quantity_edit * food.nutritions.fat / 100) + scope.co_cau(null).sugar * (food.quantity_edit * food.nutritions.sugar / 100)
                        });
                    }
                    return rs;
                };
                /*
                * Tính cơ cấu chuẩn P:L:G
                * */
                scope.getPLGTotal = function (nutritions) {
                    var rs = 0;
                    if (nutritions) {
                        rs += scope.co_cau(null).protein * $['+'](nutritions.animal_protein, nutritions.vegetable_protein);
                        rs += scope.co_cau(null).fat * $['+'](nutritions.animal_fat, nutritions.vegetable_fat);
                        rs += $['*'](scope.co_cau(null).sugar, nutritions.sugar);
                    }
                    return rs;
                };
                scope.getCaloOfNutrition = function (nutrition) {
                    var rs = 0;
                    if (nutrition) {
                        rs += scope.co_cau(null).protein * nutrition.protein + scope.co_cau(null).fat * nutrition.fat + scope.co_cau(null).sugar * nutrition.sugar;
                    }
                    return rs;
                };
                scope.detailByMeal.getCalo = function (foods) {
                    var rs = 0;
                    if (foods) {
                        angular.forEach(foods, function (food, food_id) {
                            rs += scope.co_cau(null).protein * food.nutritions.protein + scope.co_cau(null).fat * food.nutritions.fat + scope.co_cau(null).sugar * food.nutritions.sugar;
                        });
                    }
                    return rs;
                };
                scope.detailByMeal.sumPLG = function (foods, plg_name) {
                    var rs = 0;
                    angular.forEach(foods, function (food, food_id) {
                        food.nutritions[plg_name] || (food.nutritions[plg_name] = 0);
                        rs = $['+'](rs, food.nutritions[plg_name]);
                    });
                    return rs;
                };
                scope.detailByMeal.sumCaloAll = function () {
                    var rs = 0;
                    angular.forEach(scope.detailByMeal.meals, function (meal, meal_define) {
                        angular.forEach(meal.foods, function (food, food_id) {
                            rs += scope.getCalo(food, 'quantity_edit');
                        });
                    });
                    return rs;
                };
                scope.menu_planning.getTile_cocauapdung = function (name) {
                    var value = 0;
                    if (!scope.selected.group) return '-';
                    if (name == 'protein') {
                        value = $['*'](scope.co_cau(null).protein, (scope.tong.animal_protein + scope.tong.vegetable_protein));
                    }
                    if (name == 'fat') {
                        value = $['*'](scope.co_cau(null).fat, (scope.tong.animal_fat + scope.tong.vegetable_fat));
                    }
                    if (name == 'sugar') {
                        value = $['*'](scope.co_cau(null).sugar, scope.tong.sugar);
                    }
                    return value / scope.tong.calo * 100;
                };
                scope.sumCaloMeals = function (meals) {
                    var rs = 0;
                    meals || (meals = scope.menu_planning.meals);
                    angular.forEach(meals, function(meal, meal_define) {
                        if ( scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                            rs += scope.sumCaloMeal(meal);
                        }
                    });
                    return rs;
                };
                scope.sumCaloMeal = function (meal) {
                    var rs = 0;
                    if (meal) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            if (dish) {
                                rs += scope.sumCaloDish(dish.ingredient);
                            }
                        });
                    }
                    return rs;
                };
                scope.getMeasure = function (id) {
                    var rs = {};
                    angular.forEach(scope.menu_planning.measures, function (measure, index) {
                        if (measure.id + '' == id + '') {
                            rs = measure;
                            return;
                        }
                    });
                    return rs;
                };

                /*Tính tỉ lệ PLG*/
                scope.getTile_PLG = function (meals) {
                    meals || (meals = scope.menu_planning.meals);
                    var name = 'tong';
                    var rs = {protein: 0, fat: 0, sugar: 0};
                    angular.forEach(meals, function (meal, meal_define) {
                        angular.forEach(meal.dishes, function (dish, dish_id) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                if ( scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) {
                                    food.nutritions || (food.nutritions = {});
                                    food.nutritions.protein || (food.nutritions.protein = 0);
                                    food.nutritions.fat || (food.nutritions.fat = 0);
                                    food.nutritions.sugar || (food.nutritions.sugar = 0);
                                    food.nutritions.calo || (food.nutritions.calo = 0);

                                    rs.protein += scope.co_cau(null).protein * food.quantity_edit * food.nutritions.protein / 100;
                                    rs.fat += scope.co_cau(null).fat * food.quantity_edit * food.nutritions.fat / 100;
                                    rs.sugar += scope.co_cau(null).sugar * food.quantity_edit * food.nutritions.sugar / 100;
                                }
                            });
                        });
                    });
                    var tongcalo = rs.protein + rs.fat + rs.sugar;
                    rs.protein = scope.round(rs.protein / tongcalo * 100, 1);
                    rs.fat = scope.round(rs.fat / tongcalo * 100, 1);
                    rs.sugar = scope.round(rs.sugar / tongcalo * 100, 1);
                    return rs;
                };
                /* Kiểm tra đạt cả 2 quyết định 777 và hn */
                scope.ktDat2TT = function (nutrition_name) {
                    var rs = {
                        level: 0,
                        title: 'Khuyến nghị:' + scope.selected.group.tt28_old[nutrition_name + '_min'] + ' - ' + scope.selected.group.tt28_old[nutrition_name + '_max']
                    };
                    if (scope.selected.group.is_tt28_old) {
                        var tiles = scope.getTile_PLG();
                        if (tiles[nutrition_name]) {
                            var tile = tiles[nutrition_name];
                            var tile_old = scope.selected.group.tt28_old;
                            var tile_new = scope.selected.group;
                            var min = tile_old[nutrition_name + '_min'];
                            var min_under = min;
                            if (min < tile_new[nutrition_name + '_min']) {
                                min = tile_new[nutrition_name + '_min'];
                            } else {
                                min_under = tile_new[nutrition_name + '_min'];
                            }
                            var max = tile_old[nutrition_name + '_max'];
                            var max_over = max;
                            if (max > tile_new[nutrition_name + '_max']) {
                                max = tile_new[nutrition_name + '_max'];
                            } else {
                                max_over = tile_new[nutrition_name + '_max'];
                            }
                            if (tile >= tile_new[nutrition_name + '_min'] && tile <= tile_new[nutrition_name + '_max'] && tile >= tile_old[nutrition_name + '_min'] && tile <= tile_old[nutrition_name + '_max']) {
                                rs.level = 0;
                            } else if (tile >= tile_new[nutrition_name + '_min'] && tile <= tile_new[nutrition_name + '_max']) {
                                rs.level = 1;
                            } else if (tile >= tile_old[nutrition_name + '_min'] && tile <= tile_old[nutrition_name + '_max']) {
                                rs.level = 2;
                            } else {
                                rs.level = 0;
                            }
                        }
                    }
                    return rs;
                };
                scope.ktDat2TTMain = function () {
                    var rs = {level: 0, title: 'Xem chi tiết thông tin về chất'};
                    var plg = [];
                    var nutrition = scope.ktDat2TT('protein');
                    if (nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    nutrition = scope.ktDat2TT('fat');
                    if (nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    nutrition = scope.ktDat2TT('sugar');
                    if (nutrition.level == 1) {
                        plg.push('Đạm');
                    }
                    if (plg.length > 0) {
                        rs.title = 'TỈ lệ ' + plg.join(', ') + ' chưa đáp ứng theo khung SGD Hà Nội.', rs.level = 1;
                    }
                    return rs;
                };
                /* Tính tỉ lệ chất dd (PLG) đạt hay chưa */
                scope.plgRate = function (type) {
                    type || (type = '');
                    var tile = scope.getTile_PLG();
                    var tile_dat = [];
                    var tile_chuadat = [];
                    var tile_vuotqua = [];
                    if (scope.selected.group) {
                        if (tile.protein >= scope.selected.group.protein_min && tile.protein <= scope.selected.group.protein_max) {
                            tile_dat.push({define: 'protein', name: 'Chất đạm'});
                        } else if (tile.protein < scope.selected.group.protein_min) {
                            tile_chuadat.push({define: 'protein', name: 'Chất đạm'})
                        } else {
                            tile_vuotqua.push({define: 'protein', name: 'Chất đạm'})
                        }
                        if (tile.fat >= scope.selected.group.fat_min && tile.fat <= scope.selected.group.fat_max) {
                            tile_dat.push({define: 'fat', name: 'Chất béo'});
                        } else if (tile.fat < scope.selected.group.fat_min) {
                            tile_chuadat.push({define: 'fat', name: 'Chất béo'})
                        } else {
                            tile_vuotqua.push({define: 'fat', name: 'Chất béo'})
                        }
                        if (tile.sugar >= scope.selected.group.sugar_min && tile.sugar <= scope.selected.group.sugar_max) {
                            tile_dat.push({define: 'sugar', name: 'Chất bột'});
                        } else if (tile.sugar < scope.selected.group.sugar_min) {
                            tile_chuadat.push({define: 'sugar', name: 'Chất bột'})
                        } else {
                            tile_vuotqua.push({define: 'sugar', name: 'Chất bột'})
                        }
                    } else {
                    }
                    return {dat: tile_dat, chuadat: tile_chuadat, vuotqua: tile_vuotqua}
                };
                scope.plgRateBind = function () {
                    var thanhphan = scope.plgRate();
                    var text = '';
                    var dat = 0;
                    if (thanhphan.dat.length == 3) {
                        text = 'Cân đối';
                        class_color = '';
                        dat = 1;
                    } else {
                        text = 'Chưa cân đối';
                        class_color = 'color-red';
                    }
                    return {text: text, 'class': class_color, thanhphan: thanhphan, dat: dat};
                };
                scope.getGroupSelected = function () {
                    if (!scope.selected.group) {
                        for (var i in scope.menu_planning.groups) {
                            scope.selected.group = scope.menu_planning.groups[i];
                            break;
                        }
                    }
                    return scope.selected.group;
                };
                /* Tính lượng khẩu phần (calo) đạt hay chưa */
                scope.caloRate = function () {
                    var value = 0;
                    var calo = round(scope.sumCaloMeals(scope.menu_planning.meals), 0);
                    var rate = scope.getNormSelected();
                    if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                        value = 1;
                    } else {
                        if (calo < rate.smallest_rate) {
                            value = 0;
                        } else {
                            value = 2;
                        }
                    }

                    return value;
                };
                scope.caloRateBind = function () {
                    var value = scope.caloRate();
                    if (value == 1) {
                        text = 'Đạt';
                        class_color = '';
                    } else if (value == 0) {
                        text = 'Chưa đạt';
                        class_color = 'color-red';
                    } else {
                        text = 'Vượt quá định mức';
                        class_color = 'color-green';
                    }
                    return {text: text, 'class': class_color, value: value};
                };
                scope.sumQuantityDish = function (foods, key) {
                    var rs = 0;
                    key || (key = 'quantity_edit');
                    angular.forEach(foods, function (food, food_id) {
                        food[key] || (food[key] = food.quantity);
                        rs += food[key];
                    });
                    return rs;
                };
                scope.sumCaloDish = function (foods, key) {
                    var rs = 0;
                    key || (key = 'quantity_edit');
                    if(count(foods) == 0) {
                        foods = {};
                    }
                    //foods = Object.assign(clone(foods), scope.foodsAdded);
                    angular.forEach(foods, function (food, food_id) {
                        food[key] || (food[key] = 0);
                        if (food.nutritions) {
                            food.nutritions.protein || (food.nutritions.protein = 0);
                            food.nutritions.fat || (food.nutritions.fat = 0);
                            food.nutritions.sugar || (food.nutritions.sugar = 0);
                            rs += $['*'](scope.co_cau(null).protein,
                                food[key]) * (food.nutritions.protein / 100) + $['*'](scope.co_cau(null).fat,
                                food[key]) * (food.nutritions.fat / 100) + $['*'](scope.co_cau(null).sugar,
                                food[key]) * (food.nutritions.sugar / 100)
                        }
                    });
                    return rs;
                };
                scope.showDish = function (meal, dish) {
                    scope.selected || (scope.selected = {});
                    scope.selected.meal = meal;
                    scope.selected.dish = dish;
                    scope.selected.foodAdded = null;
                    scope.foodsAdded = {};
                    scope.showDishEdit(meal, dish);
                };
                scope.showDishOfFood = function (meal, dish) {
                    scope.selected.food = dish.ingredient[Object.keys(dish.ingredient)[0]];
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        action: 'change_dish',
                        title: 'Thêm mới',
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'edit_food', {}, function (resp) {
                                var form = '<div style="padding:20px;text-align:center;">' + resp + '</div>';
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(form, scope));
                                    })
                                })
                            });
                        },
                        buttons: [{
                            id: 'btn-exit',
                            icon: 'glyphicon glyphicon-cancel',
                            label: 'Đóng',
                            cssClass: 'btn-primary',
                            action: function (dialogRef) {
                                dialogRef.close();
                            }
                        }]
                    }, function (resp) {
                        if (typeof callback === 'function') {
                            callback(resp);
                        } else {
                            $("#tbl_" + self.module).datagrid('reload');
                        }
                    });
                };
                scope.showDishEdit = function (meal, dish) {
                    var self = this;
                    var meals = scope.menu_planning.meals;
                    var group_id = scope.row.group_id;
                    scope.lengthSelectDish = 0;
                    angular.forEach(scope.selected.dish.ingredient, function (food) {
                        food.deleted = false;
                        food.quantity_old = food.quantity;
                    });
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/' + self.module,
                        action: 'change_dish',
                        title: 'Thêm mới',
                        size: size.wide,
                        scope: scope,
                        fullScreen: false,
                        showButton: false,
                        draggable: true,
                        content: 'dinhduong/menu_planning/change_dish.html'
                    }, function (resp) {
                        if (typeof callback === 'function') {
                            callback(resp);
                        } else {
                            $("#tbl_" + self.module).datagrid('reload');
                        }
                    });
                };
                scope.changeDishApply = function () {
                    var food_changed = {};
                    for(var index in scope.selected.dish.ingredient) {
                        var food = scope.selected.dish.ingredient[index];
                        if (food.deleted || food.quantity != food.quantity_old){
                            food_changed[food.food_id] = food;
                        }
                        if (food.deleted) {
                            delete scope.selected.dish.ingredient[index];
                        }
                    }
                    for(var food_index in scope.foodsAdded) {
                        var food = scope.foodsAdded[food_index];
                        food_changed[food.food_id] = scope.foodsAdded[food_index];
                        scope.selected.dish.ingredient[food.food_id] = scope.foodsAdded[food_index];
                    }
					if(scope.sys.configs.cdkp_re_sorting_dish) {
						scope.selected.dish.addedAt = (new Date()).getTime();
					}
                    var meal_key = 2;
                    if (scope.selected.meal.define == 'buatoi') {
                        meal_key = 3;
                    } else if (scope.selected.meal.define == 'buasang') {
                        meal_key = 1;
                    }
                    scope.datagrid.data[meal_key] || (scope.datagrid.data[meal_key] = {});
                    angular.forEach(scope.datagrid.data[meal_key], function (food) {
                        var food_id = food.food_id;
                        if (typeof food_changed[food_id] !== 'undefined') {
                            var fds = [];
                            angular.forEach(food.foods, function (fd, ind) {
                                if (!fd.deleted) {
                                    fds.push(fd);
                                }
                            });
                            if(typeof scope.foodsAdded[food_id] != "undefined") {
                                fds.push(scope.foodsAdded[food_id]);
                                delete scope.foodsAdded[food_id];
                            }
                            if (fds.length > 0) {
                                food.foods = fds;
                                food.changed = true;
                            } else {
                                delete scope.datagrid.data[meal_key][food_id];
                            }
                        }
                    });
                    angular.forEach(scope.foodsAdded, function (food, food_id) {
                        scope.datagrid.data[meal_key][food_id] = clone(food);
                        scope.datagrid.data[meal_key][food_id].changed = true;
                        food.quantity_edit = food.quantity;
                        scope.datagrid.data[meal_key][food_id].foods = [food];
                    });
                    angular.forEach(scope.datagrid.data[meal_key], function (food) {
                        if (food.changed) {
                            food.luong1tre = 0;
                            angular.forEach(food.foods, function (fd) {
                                food.luong1tre = $['+'](food.luong1tre, fd.quantity_edit);
                            });
                            scope.onChange_luong1tre(food, true, 'luong1tre');
                        }
                    });
                    scope.totalCalculator();
                    dialogClose();
                };
                scope.showChangeDish = function (meal, dish_old) {
                    scope.selected.dish = dish_old;
                    angular.forEach(scope.selected.dish.ingredient, function (food, food_id) {
                        food.name_edit || (food.name_edit = food.name);
                    });
                    if ((dish_old.id + '').split('food_id').length > 1) {

                    } else {
                        $.menu_planning.showChangeDish(scope, meal, dish_old, function (dish_new, dialogRef) {
                            if (dish_old.id == dish_new.id) {
                                dish_new = clone(dish_old);
                            }
                            /*Xóa món ăn cũ đi*/
                            scope.delDish(meal, dish_old, false);
                            /*Xóa thực phẩm đã bị xóa trong món ăn*/
                            var tmp_foods = {};
                            angular.forEach(dish_new.ingredient, function (food, food_id) {
                                if (!food.deleted) {
                                    if (food.quantities) {
                                        food.quantities[scope.row.group_id] = Number(food.quantity);
                                    }
                                    if (typeof food.quantity_edit === 'undefined') {
                                        food.quantity_edit = Number(food.quantity);
                                    }
                                    tmp_foods[food_id] = food;
                                }
                            });
                            dish_new.ingredient = tmp_foods;
                            /*Thêm món ăn mới vào*/
                            meal.dishes[dish_new.id] = dish_new;
                            scope.datagrid.data = {};
                            angular.forEach(scope.menu_planning.meals, function (meal_, meal_define) {
                                angular.forEach(meal_.dishes, function (dish_, ind) {
                                    scope.addFoodFromDish(meal_, dish_);
                                });
                            });
                            scope.totalCalculator(scope.datagrid.data);
                        });
                    }
                };
                /* Tính toán tỉ lệ đạt của đạm*/
                scope.getTile_dat_protein = function () {
                    var name = 'tong';
                    if (!scope.selected.group || !scope.selected.group.nutritions) return '-';
                    var nutritions = scope.selected.group.nutritions;
                    var plg = scope[name];
                    var value = (scope[name].animal_protein + scope[name].vegetable_protein) / (nutritions.animal_protein + nutritions.vegetable_protein) * 100;
                    return scope.round(value, 2);
                };
                /* Tính toán tỉ lệ đạt béo*/
                scope.getTile_dat_fat = function () {
                    var name = 'tong';
                    if (!scope.selected.group || !scope.selected.group.nutritions) return '-';
                    var nutritions = scope.selected.group.nutritions;
                    var value = (scope[name].animal_fat + scope[name].vegetable_fat) / (nutritions.animal_fat + nutritions.vegetable_fat) * 100;
                    return scope.round(value, 2);
                };
                /* Tính toán tỉ lệ đạt đường*/
                scope.getTile_dat_sugar = function () {
                    var name = 'tong';
                    if (!scope.selected.group || !scope.selected.group.nutritions) return '-';
                    var nutritions = scope.selected.group.nutritions;
                    var value = scope[name].sugar / nutritions.sugar * 100;
                    return scope.round(value, 2);
                };
                /* Tính toán tỉ lệ đạt calo*/
                scope.getTile_dat_calo = function () {
                    var name = 'tong';
                    if (!scope.selected.group || !scope.selected.group.nutritions) return '-';
                    var nutritions = scope.selected.group.nutritions;
                    var value = scope[name].calo / nutritions.calo * 100;
                    return round(value, 2);
                };
                /* Tính tỉ lệ từng loại thành phần dinh dưỡng*/
                scope.getTiletungloai = function (name) {
                    var value = 0;
                    if (scope.selected.group) {
                        if (scope.selected.group.nutritions) {
                            if (!scope.selected.group || !scope.selected.group.nutritions[name]) {
                                return value;
                            }
                            var tongloai = scope.tong[name];
                            if (name == 'calo') {
                                tongloai = scope.tong.calo;
                            }
                            value = scope.tong[name] / scope.selected.group.nutritions[name];
                        }
                    }
                    return value * 100;
                };
                /* Tính tỉ lệ theo động thực vật */
                scope.getTile_dongthucvat = function (name) {
                    var value = 0;
                    if (!scope.selected.group) return '-';
                    if (name == 'animal_protein') {
                        value = $['/'](scope.tong.animal_protein, (scope.tong.animal_protein + scope.tong.vegetable_protein));
                    }
                    if (name == 'vegetable_protein') {
                        value = $['/'](scope.tong.vegetable_protein, (scope.tong.animal_protein + scope.tong.vegetable_protein));
                    }
                    if (name == 'animal_fat') {
                        value = $['/'](scope.tong.animal_fat, (scope.tong.animal_fat + scope.tong.vegetable_fat));
                    }
                    if (name == 'vegetable_fat') {
                        value = $['/'](scope.tong.vegetable_fat, (scope.tong.animal_fat + scope.tong.vegetable_fat));
                    }
                    return value * 100;
                };
                /* Mở form thêm món ăn */
                scope.showAddDish = function (meal) {
                    var group_id = scope.row.group_id;
                    $.menu_planning.showAddDish(scope, meal, function (meal_define, dish, dialogRef) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                scope.menu_planning.dish_selected = undefined;
                                scope.menu_planning.ignore_ids = '';
                                scope.addFoodFromDish(scope.menu_planning.meals[meal_define], dish);
                                scope.datagrid.data = {};
                                angular.forEach(scope.menu_planning.meals, function (meal_, meal_define) {
                                    angular.forEach(meal_.dishes, function (dish_, ind) {
                                        scope.addFoodFromDish(meal_, dish_);
                                    })
                                });
                                angular.forEach(scope.datagrid.data, function (foods, warehouse_id) {
                                    angular.forEach(foods, function (food, food_id) {
                                        angular.forEach(food.foods, function (fd, ind) {
                                            fd.delete = false;
                                        });
                                        scope.onChange_luong1tre(food);
                                    })
                                });
                                scope.totalCalculator(scope.datagrid.data);
                            }, 0);
                            $.menu_planning.combobox_load_dish(scope, scope.menu_planning.meals[meal_define], group_id);
                        });
                    });
                };
                /* Mở form thêm thực phẩm */
                scope.showAddFood = function (meal) {
                    scope.selected.meal = meal;
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/menu_planning',
                        action: 'add_food',
                        title: 'Thêm mới',
                        size: size.small,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            loadForm($CFG.project + '/menu_planning', 'add_food', {}, function (resp) {
                                var form = '<div aria-modal="true" style="padding:20px;text-align:center;">' + resp + '</div>';
                                $(element).html(scope.compile(form, scope));
                            });
                        }
                    });
                };
                scope.appendFoodToMeal = function (food, meal) {
                    var fd = {};
                    for (var i in food) {
                        fd[i] = food[i];
                    }
                    food = fd;
                    scope.menu_planning.selected.food = undefined;
                    var dish = {id: 'food_id_' + food.food_id, name: food.dish_name, ingredient: {}, addedAt: (new Date).getTime()};
                    food.quantity || (food.quantity = 10);
                    food.quantity = Number(food.quantity);
                    dish.ingredient[food.food_id] = food;
                    if (!meal.dishes[dish.id]) {
                        scope.addFoodFromDish(meal, dish);
                        angular.forEach(scope.datagrid.data, function (foods, warehouse_id) {
                            angular.forEach(foods, function (food, food_id) {
                                scope.onChange_luong1tre(food);
                            });
                        });
                        scope.totalCalculator(scope.datagrid.data);
                    } else {
                    }
                };
                scope.getCalo = function (food, field) {
                    field || (field = 'luong1tre');
                    var sl = parseFloat(food[field]);
                    var rs = scope.co_cau(null).protein * (sl * food.nutritions.protein / 100) + scope.co_cau(null).fat * (sl * food.nutritions.fat / 100) + scope.co_cau(null).sugar * (sl * food.nutritions.sugar / 100)
                    return rs;
                };
                scope.getMenu_infomation = function (define) {
                    var rs = {};
                    for (var item of scope.menu_informations) {
                        if (define == item.define) {
                            rs = item;
                            break;
                        }
                    }
                    return rs;
                };
                scope.getMenu_infomationByMeal_key = function(meal_key) {
                    var rs = {};
                    var meal_define = 'buasang';
                    for (var i in scope.meal_defines) {
                        if (scope.meal_defines[i] == meal_key) {
                            meal_define = i;
                            break;
                        }
                    }
                    for (var item of scope.menu_informations) {
                        if (meal_define == item.define) {
                            rs = item;
                            break;
                        }
                    }
                    return rs;
                };
                scope.getColorForTableCalo = function (meal) {
                    var rs = {class: '', title: ''};
                    if (scope.selected.group && meal) {
                        if (scope.selected.group.nutritions) {
                            var value = scope.sumCaloMeal(meal) / scope.selected.group.nutritions.calo * 100;
                            var m = scope.getMenu_infomation(meal.define);
                            if (m.norm_min <= value && m.norm_max >= value) {
                                rs.title = "Đạt";
                            } else if (m.norm_min > value) {
                                rs.class = 'color-red';
                                rs.title = "Chưa đạt";
                            } else {
                                rs.title = "Vượt quá định mức";
                                rs.class = 'color-green';
                            }
                        }
                    }
                    return rs;
                };
                /* Nhóm thực phẩm xuất kho và đi chợ */
                /* Nhóm xuất kho lên trên cùng ds */
                scope.orderByExport = function () {
                    var data = {};
                    angular.forEach(scope.datagrid.data, function (foods, warehouse_id) {
                        data[warehouse_id] = {};
                        var fds = [];
                        scope.inventory[warehouse_id] || (scope.inventory[warehouse_id] = {});
                        for (var food_id in foods) {
                            if (scope.inventory[warehouse_id][food_id]) {
                                fds.push(foods[food_id]);
                            }
                        }
                        for (var food_id in foods) {
                            if (!scope.inventory[warehouse_id][food_id]) {
                                fds.push(foods[food_id]);
                            }
                        }
                        for (var i = 0; i < fds.length; i++) {
                            data[warehouse_id][fds[i].food_id + ' '] = fds[i];
                        }
                    });
                    scope.datagrid.data = data;
                };
                /* Xóa tất cả thực phẩm theo kho. Tạm thời không dùng*/
                scope.delAllFoodInWarehouse = function (kho) {
                    if (!kho) {
                        kho = scope.selected.warehouse;
                    }
                    delete scope.datagrid.data[kho];
                    scope.datagrid.data[kho] = [];
                };
                /* Xóa tất cả thực phẩm đang có trong danh sách. Tạm thời không dùng */
                scope.delAllFood = function (kho) {
                    if (kho) {
                        scope.delAllFoodInWarehouse(kho);
                    } else {
                        scope.datagrid.data = [];
                    }
                };
                scope.getTiendichvu = function (group_id) {
                    var rs = 0;
                    if (scope.row.services) {
                        angular.forEach(scope.row.services, function (service, ind) {
                            if (typeof service === 'object') {
                                if (!service.price) {
                                    service.price = 0;
                                }
                                rs += Number(service.price);
                            }
                        });
                    }
                    return rs;
                };
                scope.addService = function () {
                    scope.row.services.push({name: '', price: 0})
                };
                scope.delService = function (index) {
                    var rs = [];
                    angular.forEach(scope.row.services, function (service, ind) {
                        if (ind != index) {
                            rs.push(service);
                        }
                    });
                    scope.row.services = rs;
                    scope.totalCalculator();
                };
                scope.editServicePrice = function (group_id) {
                    $.dm_datagrid.showEditForm({
                        module: $CFG.project + '/menu_planning',
                        action: 'edit',
                        title: 'Chỉnh sửa',
                        size: size.small,
                        showButton: false,
                        content: function (element) {
                            loadForm($CFG.project + '/menu_planning', 'editServicePrice', {async: true}, function (resp) {
                                scope.$apply(function () {
                                    $(element).html(scope.compile(resp, scope));
                                });
                            });
                        }
                    });
                };
                scope.getTiendicho1tre = function () {
                    var rs = 0;
                    angular.forEach(scope.datagrid.data, function (kho, index) {
                        angular.forEach(kho, function (food, index) {
                            rs = $['+'](rs, food.tiendicho1tre);
                        });
                    });
                    return scope.round(rs, 0);
                };
                scope.getTienchenhlech = function () {
                    var tiendicho1tre = scope.getTiendicho1tre();
                    var tiendichvu = scope.getTiendichvu(scope.row.group_id);
                    var tienchenhlech = $['-']($['-'](scope.row.tien1tre, tiendicho1tre), tiendichvu);
                    return tienchenhlech;
                };
                scope.getTongtienchenhlech = function () {
                    return $['*'](scope.getTienchenhlech(), scope.row.sotre);
                };
                /*Thay đổi kiểu lưu giá thực phẩm chung hay riêng từng mẫu thực đơn*/
                scope.applyFoodPrice = function (el) {
                    process('dinhduong/menu_planning/applyfoodprice', {value: el.value}, function (resp) {
                        if (resp.result != 'success') {
                            if (el.value == 0) {
                                el.value = 1;
                            } else {
                                el.value = 0
                            }
                        }
                    }, function () {
                        if (el.value == 0) {
                            el.value = 1;
                        } else {
                            el.value = 0;
                        }
                    })
                };

                scope.foodRounds = function (food, ignores) {
                    if(typeof ignores === 'string'){
                        ignores = [ignores];
                    }
                    // food.thucmuatheodvt_view = food.thucmuatheodvt;
                    var keys = [
                        /*'luong1tre',*/
                        'gam_exchange',
                        'price','price_kg',
                        'luong1tretheodvt',
                        'thucan1nhom',
                        'thucmua1nhom',
                        'thucmua1tre',
                        'thucmuatheodvt_view',
                        'tiendicho1tre',
                        'thucmuatheodvt'
                    ];
                    angular.forEach(keys, function(key,index){
                        if(!in_array(key,ignores)) {
                            food[key] = scope.round(food[key], 4);
                        }
                    });
                    if(!in_array('thucmua1tretheodvt',ignores)){
                        food['thucmua1tretheodvt'] = scope.round(food['thucmua1tretheodvt'],5);
                    }
                };
                scope.addFoodFromDish = function (meal, dish) {
                    var $tmp_foods = {};
                    scope.nutritions || (scope.nutritions = {});
                    if (meal != undefined && dish != undefined && scope != undefined) {
                        var meal_key = 2;
                        if (meal.define == 'buatoi') {
                            meal_key = 3;
                        } else if (meal.define == 'buasang') {
                            meal_key = 1;
                        }
                        var warehouse_id = scope.getMenu_infomation(meal.define).warehouse_id;
                        var foods = {};
                        angular.forEach(dish.ingredient, function (food, food_id) {
                            if (food.quantities && !food.quantity) {
                                if (food.quantities[scope.row.group_id] > 0) {
                                    food.quantity = food.quantities[scope.row.group_id];
                                }
                            }
                            foods[food_id] = food;
                        });
                        dish.ingredient = foods;
                        angular.forEach(dish.ingredient, function (food, food_id) {
                            scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                            food.meal_define = meal.define;
                            food.nutritions = scope.nutritions[food_id];
                            scope.inventory[warehouse_id] || (scope.inventory[warehouse_id] = {});
                            if (scope.inventory[warehouse_id][food_id]) {
                                if (!food.price) {
                                    food.price = scope.inventory[warehouse_id][food_id].price;
                                }
                            }
                            scope.datagrid.data[meal_key] || (scope.datagrid.data[meal_key] = {});
                            food.gam_exchange || (food.gam_exchange = 1000);
                            if (typeof food.quantity_edit === 'undefined') {
                                food.quantity_edit = Number(food.quantity);
                            }
                            food.food_id || (food.food_id = food_id);
                            food.price_kg = $['/']($['*'](food.price, 1000), food.gam_exchange);
                            if (!scope.datagrid.data[meal_key][food_id]) {
                                var row = clone(food);
                                row.foods = [];
                                /*Cập nhật giá theo nhập kho nếu trong kho còn*/
                                if (scope.inventory[warehouse_id]) {
                                    if (scope.inventory[warehouse_id][food_id]) {
                                        row.price = scope.inventory[warehouse_id][food_id].price;
                                    }
                                }
                                row.price_kg = $['/']($['*'](row.price, 1000), row.gam_exchange);
                                scope.datagrid.data[meal_key][food_id] = row;
                            }
                            // var luong1tre = scope.datagrid.data[meal_key][food_id].luong1tre;
                            // scope.datagrid.data[meal_key][food_id].luong1tre = $['+'](luong1tre, food.quantity_edit);
                            var key = meal.define + '_' + dish.id;
                            scope.datagrid.data[meal_key][food_id].foods.push(food);
                            $tmp_foods[food_id] = scope.datagrid.data[meal_key][food_id];
                        });
                        var dishes = {};
                        angular.forEach(meal.dishes, function (ds, ds_id) {
                            dishes[ds.id] = ds;
                        });
                        meal.dishes = dishes;
                        meal.dishes[dish.id] = dish;
                        angular.forEach(scope.datagrid.data, function (foods, meal_key) {
                            angular.forEach(foods, function (food, food_id) {
                                food.luong1tre = 0;
                                angular.forEach(food.foods, function (fd, ind) {
                                    food.luong1tre = $['+'](food.luong1tre, fd.quantity_edit);
                                });
                                scope.onChange_luong1tre(food, true);
                            });
                        });
                    }
                    return $tmp_foods;
                };
                scope.getMenu_infomation = function (define) {
                    var rs = {};
                    angular.forEach(scope.menu_informations, function (menu_info, ind) {
                        if (menu_info.define == define) {
                            rs = menu_info;
                            return;
                        }
                    });
                    return rs;
                };

                function mapMenuMeals(collection) {
                    angular.forEach(collection, function (meal) {
                        var minTime = _.chain(meal.dishes)
                            .map('addedAt')
                            .compact()
                            .min()
                            .value() || (new Date).getTime();

                        _.forEachRight(meal.dishes, function (dish) {
                            if (angular.isUndefined(dish.addedAt)) {
                                dish.addedAt = minTime -= 1000;
                            }
                        });
                    });

                    return collection;
                }

                scope.editForm = function () {
                    var self = $.menu_planning;
                    var row = $("#tbl_menu_planning").datagrid('getSelected');
                    if (!row) {
                        $.messager.alert('Thông báo.', 'Phải chọn một dòng!');
                        return;
                    }
                    scope.menu_planning.form_add = false;
                    $.balance.init(scope);
                    $.dm_datagrid.showEditForm({
                        module: $CFG.project + '/menu_planning',
                        action: 'edit',
                        title: 'Chỉnh sửa',
                        size: size.wide,
                        fullScreen: true,
                        showButton: false,
                        content: function (element) {
                            loadForm($CFG.project + '/' + self.module + self.module_filter, 'edit', {
                                id: row.id,
                                dataType: 'json'
                            }, function (resp) {
                                scope.menu_planning.data = resp.data;
                                scope.menu_planning.meals = mapMenuMeals(resp.data.meals);
                                scope.selected.meals = resp.data.meals;
                                scope.prices = {};
                                scope.prices_kg = {};
                                scope.nutritions = {};
                                scope.datagrid.data = resp.row.data;
                                /*Nếu như kho ở cấu hình bữa không khớp với cấu hình của thực đơn thì tổng lại ds thực phẩm trong bữa*/
                                var warehouse_diffrent = false;
                                angular.forEach(scope.menu_informations, function (item, index) {
                                    var meal_key = scope.meal_defines[item.define];
                                    if (!resp.row.data[meal_key] && scope.menu_planning.meals && count(scope.menu_planning.meals[item.define].dishes) > 0) {
                                        warehouse_diffrent = true;
                                    }
                                    if (scope.menu_planning.meals) {
                                        scope.menu_planning.meals[item.define].warehouse_id = item.warehouse_id;
                                    }
                                });
                                scope.row.id = resp.row.id;
                                scope.row.group_id = resp.row.group_id;
                                scope.row.shared = resp.row.shared;
                                scope.row.nutrition_fixed = resp.row.nutrition_fixed;
                                scope.row.mathucdon = resp.row.name;
                                scope.row.sotre = Number(resp.data.together.sotre);
                                scope.row.sotre_old = scope.row.sotre;
                                scope.row.tien1tre = Number(resp.data.together.tien1tre);
                                scope.row.services = resp.data.together.services;
                                scope.row.tienchenhlech1tre = Number(resp.data.together.tienchenhlech1tre);
                                scope.row.tongtienchenhlech = Number(resp.data.together.tongtienchenhlech);
								scope.row.school_point = resp.row.school_point;
                                if (resp.row.meal_selection){
                                    scope.row.meal_selection = resp.row.meal_selection;
                                }
                                scope.menu_planning.services[scope.row.group_id] || (scope.menu_planning.services[scope.row.group_id] = {});
                                scope.menu_planning.services[scope.row.group_id].services || (scope.menu_planning.services[scope.row.group_id].services = {});
                                if (resp.row.services) {
                                    var svs = [];
                                    angular.forEach(resp.row.services, function (sv, ind) {
                                        if (typeof sv === 'object') {
                                            svs.push(sv);
                                        }
                                    });
                                    resp.row.services = svs;
                                    scope.menu_planning.services[scope.row.group_id].services = resp.row.services;
                                }
                                scope.onChange_group();
                                angular.forEach(scope.menu_planning.groups, function (item, index) {
                                    if (item.id == scope.row.group_id) {
                                        scope.selected.group = item;
                                    }
                                });
                                /*Nếu khác kho thì cập nhật lại ds thực đơn ở bữa luôn*/
                                if (warehouse_diffrent) {
                                    scope.datagrid.data = {};
                                    angular.forEach(scope.menu_planning.meals, function (meal_, meal_define) {
                                        angular.forEach(meal_.dishes, function (dish_, ind) {
                                            scope.addFoodFromDish(meal_, dish_);
                                        });
                                    });
                                } else {
                                    if (scope.datagrid.data) {
                                        var data_tmp = {};
                                        angular.forEach(scope.datagrid.data, function (foods, warehouse_id) {
                                            if (foods + '' != 'null') {
                                                data_tmp[warehouse_id] = {};
                                                angular.forEach(foods, function (food, food_id) {
                                                    angular.forEach(food.nutritions, function (val, nuti_key) {
                                                        delete food[nuti_key];
                                                    });
                                                    scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                                                    if (food + '' != 'null') {
                                                        food.foods = [];
                                                        food.nutritions = scope.nutritions[food_id];
                                                        data_tmp[warehouse_id][food_id] = food;
                                                        scope.onChange_price(scope.datagrid.data, food);
                                                    }
                                                })
                                            }
                                        });
                                        scope.datagrid.data = data_tmp;
                                        angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                                            var warehouse_id = meal.warehouse_id;
                                            var meal_key = scope.meal_defines[meal_define];
                                            var wh = scope.datagrid.data[meal_key];
                                            if (wh) {
                                                angular.forEach(meal.dishes, function (dish, dish_id) {
                                                    if(!dish.ingredient || count(dish.ingredient) == 0) {
                                                        dish.ingredient = {};
                                                    }
                                                    var key = meal_define + '_' + dish_id;
                                                    angular.forEach(dish.ingredient, function (food, food_id) {
                                                        angular.forEach(food.nutritions, function (val, nuti_key) {
                                                            delete food[nuti_key];
                                                        });
                                                        scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                                                        food.nutritions = scope.nutritions[food_id];
                                                        if (wh[food_id]) {
                                                            if (!wh[food_id].name_edit) {
                                                                wh[food_id].name_edit = food.name;
                                                            }
                                                            wh[food_id].extrude_factor_root = wh[food_id].extrude_factor;
                                                            wh[food_id].gam_exchange || (wh[food_id].gam_exchange = 1000);
                                                            wh[food_id].gam_exchange_root = wh[food_id].gam_exchange;
                                                            if (!wh[food_id].price) {
                                                                wh[food_id].price = food.price;
                                                            }
                                                            wh[food_id].nutritions = food.nutritions;
                                                            wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price, 1000), wh[food_id].gam_exchange));
                                                            wh[food_id].foods.push(food);
                                                            var inventory = scope.inventory[meal_key];
                                                            inventory || (inventory = {});
                                                            if (inventory[food_id]) {
                                                                var fi = inventory[food_id];
                                                                if (fi.food_name) {
                                                                    wh[food_id].name = fi.food_name;
                                                                }
                                                                if (fi.extrude_factor != food.extrude_factor) {
                                                                    if (fi.extrude_factor) {
                                                                        wh[food_id].extrude_factor = fi.extrude_factor;
                                                                    }
                                                                    if (fi.gam_exchange) {
                                                                    }
                                                                    scope.onChange_thucmuatheodvt(wh[food_id]);
                                                                } else {
                                                                    //scope.onChange_luong1tre(wh[food_id]);
                                                                }
                                                                wh[food_id].gam_exchange || (wh[food_id].gam_exchange = 1000);
                                                                if(!scope.allowChangePriceWarehouse(meal_key, food_id)) {
                                                                    wh[food_id].price = Number(wh[food_id].price);
                                                                    wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price, 1000), wh[food_id].gam_exchange));
                                                                }else{
                                                                    wh[food_id].price = food.price;
                                                                    wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price, 1000), wh[food_id].gam_exchange));
                                                                }
                                                                wh[food_id].thanhtien1nhom = scope.round($['*'](wh[food_id].price, wh[food_id].thucmuatheodvt));
                                                                wh[food_id].tiendicho1tre = scope.round($['*'](wh[food_id].thucmua1tretheodvt, wh[food_id].price));
                                                                if (fi.measure_id) {
                                                                    wh[food_id].measure_id = fi.measure_id;
                                                                }
                                                            }
                                                            scope.foodRounds(wh[food_id], 'thucmuatheodvt');
                                                        }
                                                    });
                                                });
                                            }
                                        });
                                        angular.forEach(scope.datagrid.data, function (foods, meal_key) {
                                            var warehouse_id = scope.getMenu_infomationByMeal_key(meal_key).warehouse_id;
                                            var inventory = scope.inventory[warehouse_id];
                                            inventory || (inventory = {});
                                            angular.forEach(foods, function (food, food_id) {
                                                if(count(food.foods) == 0) {
                                                    delete foods[food_id];
                                                    return;
                                                }
                                                if (!food.price) {
                                                    food.price = 0;
                                                    food.price_kg = 0;
                                                }
                                                if (inventory[food_id] && !scope.allowChangePriceWarehouse(meal_key, food_id)) {
													if (!scope.sys.configs.tmd_old_not_check_inventory) {
														food.price = inventory[food_id].price;
													}
                                                }
                                                scope.onChange_price(scope.datagrid.data, food);
                                                scope.prices[food_id] = food.price;
                                                scope.prices_kg[food_id] = food.price_kg;
                                            });
                                        });
                                    } else {
                                        scope.datagrid.data = {};
                                        var tmp_foods = {};
                                        angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                                            angular.forEach(meal.dishes, function (dish, dish_id) {
                                                scope.addFoodFromDish(meal, dish);
                                            });
                                        });
                                    }
                                }
                                scope.totalCalculator();
                                $(element).html(scope.compile(resp.html, scope));
                            });
                        }
                    }, function (resp) {
                        $("#tbl_" + self.module).datagrid('reload');
                    });
                };
                scope.filter = {
                    count: 0,
                    enable: false,
                    sogd: {selected: undefined, lists: scope.menu_planning.sogd},
                    phonggd: {selected: undefined, lists: {}},
                    truong: {selected: undefined, lists: {}},
                    chat: 0,
                    luong: 0,
                    price_min: 0,
                    price_max: 50000,
                    price_diff: 5000
                };
                scope.delFoodOfGrid = function(warehouse_id, food, food_id) {
                    food_id = food.food_id;
                        angular.forEach(food.foods, function(food, ind){
                            food.deleted = true;
                        });
                        delete scope.datagrid.data[warehouse_id][food_id];
                };
                scope.filter.count = 0;
                scope.filter.enable = false;
                scope.filter.copy = function (item) {
                    var self = $.menu_planning;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter + '/copy';
                    var data = {async: true, data: item};
                    process(url, data, function (resp) {
                        $('#tbl_menu_planning').datagrid('reload');
                    }, null, false);
                };
                scope.filter.start = function (option) {
                    scope.filter.enable = !scope.filter.enable;
                    statusloading();
                    if (option == 'dish_name') {
                        scope.filter.runDishName();
                    } else {
                        scope.filter.run();
                    }
                };
                scope.filter.runDishName = function (data) {
                    self = $.menu_planning;
                    if (!scope.filter.enable) {
                        return;
                    }
                    if (data == undefined || data == '') {
                        data = [];
                    }
                    process($CFG.project + '/' + self.module + self.module_filter + '/saveName', {
                        async: true,
                        data: data
                    }, function (resp) {
                        if (resp.length > 0) {
                            var rs = {};
                            angular.forEach(resp, function (item, index) {
                                var dish_names = [];
                                angular.forEach(item.data.meals, function (meal, ind2) {
                                    angular.forEach(meal.dishes, function (dish, ind3) {
                                        if (ind3 + '' != 'undefined' && dish.name + '' != 'undefined') {
                                            dish_names.push(dish.name);
                                        }
                                    });
                                });
                                rs[item.id] = {id: item.id, dishes: dish_names};
                            });
                            scope.filter.runDishName(rs);
                            scope.$apply(function () {
                                scope.filter.running = true;
                                scope.filter.count += resp.length;
                            });
                        } else {
                            statusloadingclose();
                            scope.$apply(function () {
                                scope.filter.running = false;
                            })
                        }
                    }, function () {
                        scope.filter.runDishName();
                    }, false, true);
                };
                scope.filter.run = function (data) {
                    self = $.menu_planning;
                    if (!scope.filter.enable) {
                        return;
                    }
                    if (data == undefined || data == '') {
                        data = [];
                    }
                    process($CFG.project + '/' + self.module + self.module_filter + '/save', {
                        async: true,
                        data: data
                    }, function (resp) {
                        if (resp.length > 0) {
                            var rs = {};
                            angular.forEach(resp, function (row, index) {
                                scope.filter.parseData(row);
                                rs[row.id] = {
                                    id: row.id,
                                    group_key: scope.menu_planning.groups[row.group_id].group_key,
                                    evaluation: scope.caloRate(),
                                    evaluation_calo: scope.plgRateBind().dat,
                                    money_difference_on_child: round(scope.row.tongtienchenhlech / scope.row.sotre, 3)
                                }
                            });
                            scope.filter.run(rs);
                            scope.$apply(function () {
                                scope.filter.running = true;
                                scope.filter.count += resp.length;
                            })
                        } else {
                            statusloadingclose();
                            scope.$apply(function () {
                                scope.filter.running = false;
                            })
                        }
                    }, function () {
                        scope.filter.run();
                    }, false, true);
                };
                scope.filter.parseData = function (resp) {
                    scope.menu_planning.data = resp.data;
                    scope.menu_planning.meals = resp.data.meals;
                    scope.prices = {};
                    scope.prices_kg = {};
                    scope.nutritions = {};
                    scope.datagrid.data = resp.row.data;
                    angular.forEach(scope.menu_informations, function (item, index) {
                        item.value = resp.data.together[item.define];
                    });
                    scope.row.id = resp.row.id;
                    scope.row.group_id = resp.row.group_id;
                    scope.row.shared = resp.row.shared;
                    scope.row.nutrition_fixed = resp.row.nutrition_fixed;
                    scope.row.mathucdon = resp.row.name;
                    scope.row.sotre = Number(resp.data.together.sotre);
                    scope.row.sotre_old = scope.row.sotre;
                    scope.row.tien1tre = Number(resp.data.together.tien1tre);
                    scope.row.tienchenhlech1tre = Number(resp.data.together.tienchenhlech1tre);
                    scope.row.tongtienchenhlech = Number(resp.data.together.tongtienchenhlech);
                    if (!resp.row.services) {
                        resp.row.services = {}
                    }
                    scope.menu_planning.services[scope.row.group_id] || (scope.menu_planning.services[scope.row.group_id] = {});
                    scope.menu_planning.services[scope.row.group_id].services || (scope.menu_planning.services[scope.row.group_id].services = {});
                    if (resp.row.services && resp.row.services + '' != 'null') {
                        scope.menu_planning.services[scope.row.group_id].services = resp.row.services;
                    }
                    scope.row.services = resp.row.services;
                    scope.onChange_group();
                    angular.forEach(scope.menu_planning.groups, function (item, index) {
                        if (item.id == scope.row.group_id) {
                            scope.selected.group = item;
                        }
                    });
                    if (scope.datagrid.data) {
                        var data_tmp = {};
                        angular.forEach(scope.datagrid.data, function (kho, warehouse_id) {
                            if (kho + '' != 'null') {
                                data_tmp[warehouse_id] = {};
                                angular.forEach(kho, function (food, food_id) {
                                    scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                                    if (food + '' != 'null') {
                                        food.foods = [];
                                        food.nutritions = scope.nutritions[food_id];
                                        data_tmp[warehouse_id][food_id] = food;
                                        scope.onChange_price(scope.datagrid.data, food);
                                    }
                                })
                            }
                        });
                        scope.datagrid.data = data_tmp;
                        angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                            var warehouse_id = meal.warehouse_id;
                            var wh = scope.datagrid.data[warehouse_id];
                            if (wh) {
                                angular.forEach(meal.dishes, function (dish, dish_id) {
                                    var key = meal_define + '_' + dish_id;
                                    angular.forEach(dish.ingredient, function (food, food_id) {
                                        scope.nutritions[food_id] || (scope.nutritions[food_id] = food.nutritions);
                                        food.nutritions = scope.nutritions[food_id];
                                        if (wh[food_id]) {
                                            if (!wh[food_id].name_edit) {
                                                wh[food_id].name_edit = food.name;
                                            }
                                            wh[food_id].extrude_factor_root = wh[food_id].extrude_factor;
                                            wh[food_id].gam_exchange || (wh[food_id].gam_exchange = 1000);
                                            wh[food_id].gam_exchange_root = wh[food_id].gam_exchange;
                                            if (!wh[food_id].price) {
                                                wh[food_id].price = food.price;
                                            }
                                            wh[food_id].nutritions = food.nutritions;
                                            wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price, 1000), wh[food_id].gam_exchange));
                                            wh[food_id].foods.push(food);
                                            var inventory = scope.inventory[warehouse_id];
                                            inventory || (inventory = {});
                                            if (inventory[food_id]) {
                                                var fi = inventory[food_id];
                                                if (fi.food_name) wh[food_id].name = fi.food_name;
                                                if (fi.extrude_factor != food.extrude_factor || fi.gam_exchange != food.gam_exchange) {
                                                    if (fi.extrude_factor) {
                                                        wh[food_id].extrude_factor = fi.extrude_factor;
                                                    }
                                                    if (fi.gam_exchange) {
                                                        wh[food_id].gam_exchange = fi.gam_exchange;
                                                    }
                                                    scope.onChange_thucmuatheodvt(wh[food_id]);
                                                } else {
                                                    scope.onChange_luong1tre(wh[food_id]);
                                                }
                                                wh[food_id].gam_exchange || (wh[food_id].gam_exchange = 1000);
                                                wh[food_id].price = Number(wh[food_id].price);
                                                wh[food_id].price_kg = scope.round($['/']($['*'](wh[food_id].price, 1000), wh[food_id].gam_exchange));
                                                wh[food_id].thanhtien1nhom = scope.round($['*'](wh[food_id].price, wh[food_id].thucmuatheodvt));
                                                wh[food_id].tiendicho1tre = scope.round($['*'](wh[food_id].thucmua1tretheodvt, wh[food_id].price));
                                                if (fi.measure_id) {
                                                    wh[food_id].measure_id = fi.measure_id;
                                                }
                                            }
                                            scope.foodRounds(wh[food_id], 'thucmuatheodvt');
                                        }
                                    })
                                });
                            }
                        });
                        angular.forEach(scope.datagrid.data, function (foods, warehouse_id) {
                            angular.forEach(foods, function (food, food_id) {
                                if (!food.price) {
                                    food.price = 0;
                                    food.price_kg = 0;
                                }
                                scope.prices[food_id] = food.price;
                                scope.prices_kg[food_id] = food.price_kg;
                            });
                        });
                        angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                            var warehouse_id = meal.warehouse_id;
                            var wh = scope.datagrid.data[warehouse_id];
                            if (wh) {
                                angular.forEach(meal.dishes, function (dish, dish_id) {
                                    var key = meal_define + '_' + dish_id;
                                    angular.forEach(dish.ingredient, function (food, food_id) {
                                        if (wh[food_id]) {
                                            food.extrude_factor = wh[food_id].extrude_factor;
                                            food.gam_exchange = wh[food_id].gam_exchange;
                                            food.price = scope.prices[food_id];
                                            food.price_kg = scope.prices_kg[food_id];
                                        }
                                    })
                                })
                            }
                        })
                    } else {
                        scope.datagrid.data = {};
                        var tmp_foods = {};
                        angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                            angular.forEach(meal.dishes, function (dish, dish_id) {
                                scope.addFoodFromDish(meal, dish);
                            });
                        });
                        angular.forEach(scope.datagrid.data, function (kho, warehouse_id) {
                            angular.forEach(kho, function (food, food_id) {
                                scope.onChange_luong1tre(food, true, false);
                            })
                        })
                    }
                    angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                        meal.name = scope.getMenu_infomation(meal_define).name;
                    });
                    angular.forEach(scope.nutritions, function (nutritions, food_id) {
                        angular.forEach(nutritions, function (value, name) {
                            nutritions[name] = Number(value);
                        });
                    });
                    scope.totalCalculator();
                };
                scope.filter.onChangeSogd = function () {
                    var self = $.menu_planning;
                    scope.filter.phonggd.lists = [];
                    scope.filter.phonggd.selected = undefined;
                    scope.filter.truong.lists = [];
                    scope.filter.truong.selected = undefined;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter + '/getChild';
                    var data = {async: true, id: ''};
                    if (scope.filter.sogd.selected) {
                        data.parent_id = scope.filter.sogd.selected;
                    }
                    process(url, data, function (resp) {
                        scope.$apply(function () {
                            scope.filter.phonggd.lists = resp;
                        })
                    }, null, false, false);
                    scope.filter.doSearch();
                };
                scope.filter.onChangePhonggd = function () {
                    var self = $.menu_planning;
                    scope.filter.truong.lists = [];
                    scope.filter.truong.selected = undefined;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter + '/getChild';
                    var data = {async: true, id: ''};
                    if (scope.filter.phonggd.selected) {
                        data.parent_id = scope.filter.phonggd.selected;
                    }
                    process(url, data, function (resp) {
                        scope.$apply(function () {
                            scope.filter.truong.lists = resp;
                        })
                    }, null, false, false);
                    scope.filter.doSearch();
                };
                scope.filter.onChangeTruong = function () {
                    scope.filter.doSearch();
                };
                scope.filter.doSearch = function () {
                    var self = $.menu_planning;
                    var queryParams = $('#tbl_menu_planning').datagrid('options').queryParams;
                    if (scope.filter.sogd.selected) {
                        queryParams.sogd = scope.filter.sogd.selected;
                    } else {
                        delete queryParams.sogd;
                    }
                    if (scope.filter.phonggd.selected) {
                        queryParams.phonggd = scope.filter.phonggd.selected;
                    } else {
                        delete queryParams.phonggd;
                    }
                    if (scope.filter.truong.selected) {
                        queryParams.truong = scope.filter.truong.selected;
                    } else {
                        delete queryParams.truong;
                    }
                    queryParams.chat = scope.filter.chat ? 1 : 0;
                    queryParams.luong = scope.filter.luong ? 1 : 0;
                    queryParams.price_min = scope.filter.price_min;
                    queryParams.price_max = scope.filter.price_max;
                    queryParams.price_diff = scope.filter.price_diff;
                    $('#tbl_menu_planning').datagrid('load', queryParams);
                };
                scope.getDataForm = function (element) { /* Lấy dữ liệu trên form để lưu */
                    scope.row.tienchenhlech1tre = round(scope.row.tongtienchenhlech / scope.row.sotre, 3);
                    scope.row.group_key = scope.selected.group['group_key'];
                    var grid = clone(scope.datagrid.data);
                    angular.forEach(grid, function (foods) {
                        angular.forEach(foods, function (food) {
                            food.foods = [];
                        });
                    });
                    var data = {
                        meals: JSON.stringify(scope.menu_planning.meals),
                        data: JSON.stringify(grid),
                        row: scope.row,
                        services: scope.selected.services,
                        rate: scope.caloRateBind().value,
                        rate_calo: scope.plgRateBind().dat
                    };
                    return data;
                };
                scope.isMealsEmpty = function () {
                    var rs = true;
                    for (var meal_define in scope.menu_planning.meals) {
                        if (count(scope.menu_planning.meals[meal_define].dishes) > 0) {
                            rs = false;
                            break;
                        }
                    }
                    return rs;
                };
                scope.importAllFoodInDay = function () {
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/menu_planning',
                        action: 'add',
                        title: 'Thêm thực phẩm',
                        size: size.wide,
                        fullScreen: true,
                        showButton: true,
                        content: function (element) {
                            loadForm($CFG.project + '/menu_planning', 'addFoodForDay', {}, function (resp) { /* Tải form html mẫu */
                                var id_form = 'popup_' + (Math.random() + '').split('.')[1];
                                var html = '<div id="' + id_form + '">' + resp + '</div>';
                                $(element).html(scope.compile(html, scope));
                                setTimeout(function () {
                                    var h = scope.window.screen.height;
                                    $('div#' + id_form).css({height: h - 100, overflow: 'auto'});
                                }, 400);
                                scope.foodofday = {foods: {}, meals: {}, ignore_ids: '', selected: ''};
                                angular.forEach(scope.menu_planning.meals, function (meal, meal_define) {
                                    scope.foodofday.meals[meal_define] = {lists: [{name: ''}], add: true};
                                });
                                scope.foodofday.foodSelected = function (food, menuignore_ids) {
                                    scope.foodofday.ignore_ids = '';
                                    scope.foodofday.selected = undefined;
                                    var id = food.food_id;
                                    food.quantity = 10;
                                    scope.foodofday.foods[id] = food;
                                    angular.forEach(scope.foodofday.foods, function (dish, key) {
                                        scope.foodofday.ignore_ids = scope.foodofday.ignore_ids + ',' + key;
                                    });
                                    scope.foodofday.onChange_luong1tre(food);
                                };
                                scope.foodofday.delFood = function (id) {
                                    if (id) {
                                        delete scope.foodofday.foods[id];
                                    }
                                };
                                scope.foodofday.onChange_luong1tre = function (food) {
                                    food.gam_exchange || (food.gam_exchange = 1000);
                                    var thucan1nhom = $['/']($['*'](food.quantity, scope.row.sotre), 1000);
                                    var thucmua1nhom = thucan1nhom;
                                    if (food.extrude_factor) {
                                        thucmua1nhom = $['/']($['*'](100, thucan1nhom), $['-'](100, food.extrude_factor));
                                    }
                                    food.thucmuatheodvt = scope.round($['*']($['/'](thucmua1nhom, food.gam_exchange), 1000));
                                };
                                scope.foodofday.onChange_thucmuatheodvt = function (food) {
                                    food.gam_exchange || (food.gam_exchange = 1000);
                                    var thucmuatheodvt = Number(food.thucmuatheodvt);
                                    var thucmua1tretheodvt = $['/'](thucmuatheodvt, scope.row.sotre);
                                    var thucmua1nhom = $['/']($['*'](thucmuatheodvt, food.gam_exchange), 1000);
                                    var thucan1nhom = thucmua1nhom;
                                    if (food.extrude_factor) {
                                        thucan1nhom = $['-'](thucmua1nhom, thucmua1nhom / 100 * food.extrude_factor);
                                    }
                                    food.quantity = $['*']($['/'](thucan1nhom, scope.row.sotre), 1000);
                                };
                                scope.foodofday.checkNameMealEmpty = function (lists) {
                                    var rs = false;
                                    for (var i in lists) {
                                        if (lists[i].name == '') {
                                            return true;
                                        }
                                    }
                                };
                                scope.foodofday.delMeal = function (meal, index) {
                                    var tmp = [];
                                    angular.forEach(meal.lists, function (item, i) {
                                        if (i != index) {
                                            tmp.push(item);
                                        }
                                    });
                                    if (tmp.length == 0) {
                                        tmp.push({name: ''});
                                    }
                                    meal.lists = tmp;
                                }
                            })
                        },
                        buttons: [{
                            /* Định nghĩa nút lưu lại */
                            id: 'btn-save',
                            icon: 'glyphicon glyphicon-floppy-disk',
                            label: 'Áp dụng',
                            cssClass: 'btn-primary',
                            action: function (dialogRef) {
                                var dishes = {};
                                var dem = 1;
                                var dish_add_food = {};
                                angular.forEach(scope.foodofday.meals, function (menu, meal_define) {
                                    angular.forEach(menu.lists, function (meal, index) {
                                        var name = meal.name;
                                        if (name) {
                                            dishes[meal_define] || (dishes[meal_define] = {});
                                            dishes[meal_define][dem] = {id: 'allin_' + dem, name: name, ingredient: {}};
                                            dish_add_food = dishes[meal_define][dem];
                                            dem++;
                                        }
                                    })
                                });
                                if (count(dishes) == 0) {
                                    alert('Phải nhập ít nhất một tên món ăn vào ít nhất 1 bữa.');
                                    return;
                                }
                                if (count(scope.foodofday.foods) == 0) {
                                    alert('Phải có ít nhất một thực phẩm trong danh sách.');
                                    return;
                                }
                                if (dishes['buatrua']) {
                                    dish_add_food = dishes['buatrua'][Object.keys(dishes['buatrua'])[0]];
                                }
                                angular.forEach(scope.foodofday.foods, function (food, food_id) {
                                    food.quantity || (food.quantity = 10);
                                    food.quantity = Number(food.quantity);
                                    if (food.quantity <= 0) {
                                        food.quantity = 0;
                                    }
                                    dish_add_food.ingredient[food_id] = food;
                                });
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        angular.forEach(dishes, function (menu, meal_define) {
                                            angular.forEach(menu, function (dish, dish_id) {
                                                var meal = scope.menu_planning.meals[meal_define];
                                                if (meal) {
                                                    scope.addFoodFromDish(meal, dish);
                                                }
                                            });
                                        });
                                        angular.forEach(scope.datagrid.data, function (kho, warehouse_id) {
                                            angular.forEach(kho, function (food, food_id) {
                                                scope.onChange_luong1tre(food);
                                            });
                                        });
                                        scope.totalCalculator();
                                    });
                                });
                                dialogRef.close();
                            }
                        }]
                    });
                };
                scope.addAction = function () {
                    var self = $.menu_planning;
                    data = scope.getDataForm($('#frm-add-menu_planning'));
                    data.async = true;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/' + self.module + self.module_filter + '/add';
                    process(url, data, function (resp) {
                        if (resp.result == "success") {
                            $.messager.alert('Thông báo', 'Thêm thành công!');
                            $('#tbl_menu_planning').datagrid('reload');
                            dialogCloseAll();
                        }
                    });
                };
                scope.editAction = function () {
                    var data = scope.getDataForm($('#frm-add-menu_planning'));
                    data.row = scope.row;
                    data.async = true;
                    var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/menu_planning/edit';
                    process(url, data, function (resp) {
                        if (resp.result == "success") {
                            dialogCloseAll();
                            $('#tbl_menu_planning').datagrid('reload');
                        }
                    });
                };
                scope.addForm = function (clean_data) {
                    if (!clean_data) {
                        dialogCloseAll();
                    }
                    scope.menu_planning.form_add = true;
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project + '/menu_planning',
                        action: 'add',
                        title: 'Thêm mới',
                        size: size.wide,
                        fullScreen: true,
                        showButton: false,
                        content: function (element) {
                            loadForm($CFG.project + '/menu_planning', 'add', {}, function (resp) { /* Tải form html mẫu */
                                scope.nutritions = {};
                                $(element).html(scope.compile(resp, scope));
                                if (clean_data) {
                                    scope.menu_planning.meals = {};
                                    scope.datagrid.data = {};
                                }
                                if (!scope.row.group_id) {
                                    var i;
                                    for (i in scope.menu_planning.groups) {
                                        if (count(scope.menu_planning.groups[i].nutritions)) {
                                            scope.row.group_id = scope.menu_planning.groups[i].id;
                                            /*scope.row.sotre = scope.menu_planning.groups[i].student_amount;*/
                                            scope.row.sotre = 100;
                                            scope.row.tien1tre = scope.menu_planning.groups[i].money_of_children;
                                            scope.selected.group = scope.menu_planning.groups[i];
                                            break;
                                        }
                                    }
                                }
                                scope.onChange_group();
                                scope.menu_planning.meals || (scope.menu_planning.meals = {});
                                angular.forEach(scope.menu_informations, function (menu_information) {
                                    if (!scope.menu_planning.meals[menu_information.define]) {
                                        scope.menu_planning.meals[menu_information.define] = {
                                            define: menu_information.define,
                                            name: menu_information.name,
                                            warehouse_id: menu_information.warehouse_id,
                                            dishes: {}
                                        };
                                    }
                                });
                                scope.selected.meals = scope.menu_planning.meals;
                                scope.onChange_tien1tre();
                                $(element).find('#mathucdon').focus();
                            })
                        }
                    });
                };

                scope.deleteDish = function (food_id, warehouse_id) {
                    var r = confirm('Bạn có chắc chắn xóa?');
                    if (!r) return;
                    var i, j, k;
                    for (i in scope.menu_planning.meals) {
                        if (warehouse_id == scope.menu_planning.meals[i].warehouse_id) {
                            for (j in scope.menu_planning.meals[i].dishes) {
                                if (scope.menu_planning.meals[i].dishes && count(scope.menu_planning.meals[i].dishes) > 0) {
                                    for (k in scope.menu_planning.meals[i].dishes[j].ingredient) {
                                        if (k == food_id) delete scope.menu_planning.meals[i].dishes[j].ingredient[k];
                                    }
                                }
                            }
                        }
                    }
                    if (scope.datagrid.data[warehouse_id]) {
                        for (i in scope.datagrid.data[warehouse_id]) {
                            if (i == food_id) {
                                delete scope.datagrid.data[warehouse_id][food_id];
                                break;
                            }
                        }
                    }
                    scope.totalCalculator();
                };
                scope.onChange_measure = function (food) {
                    var i;
                    process('dinhduong/menu_adjust/updateMeasureId', {
                        measure_id: food.measure_id,
                        food_id: food.food_id,
                        async: true
                    }, function (resp) {
                        if (resp.result == "success") {
                            for (i in food.foods) {
                                food.foods[i].measure_id = food.measure_id;
                            }
                        }
                    })
                };
                $.balance.init(scope);
                $.balance_money.init(scope);

                scope.arrayWrap = function (collection) {
                    if (typeof collection === 'object') {
                        collection = _.reduce(collection, function (arr, item) {
                            arr.push(item);

                            return arr;
                        }, []);

                        collection = _.orderBy(collection, ['addedAt'], 'asc');

                        return collection;
                    }

                    return collection;
                };
                scope.onApplyPrice = function () {
                    process($CFG.project + '/unit_food_detail/getMapPrice', {}, function (resp) {
                        if(resp.map){
                            scope.mapPrice = resp.map;
                            scope.applyPrice(scope.mapPrice);
                            $.messager.alert('Thông báo', 'Đã hoàn thành!');
                        }
                    });
                };

                scope.applyPrice = function (map) {
                    var mapStorage = {};
                    angular.forEach(scope.inventory, function (foods) {
                        angular.forEach(foods, function (food, foodId) {
                            mapStorage[foodId] = true;
                        });
                    });
                    var exist = (scope.selected.group || {}).meals || -1;
                    if (exist !== -1) {
                        angular.forEach(scope.selected.meals, function (meal) {
                            angular.forEach(meal.dishes, function (dish) {
                                angular.forEach(dish.ingredient, function (food, id) {
                                    if (map[id] && !mapStorage[id]) {
                                        food.price = map[id];
                                        food.price_kg = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                                        food.thanhtien1nhom = scope.round($['*'](food.price, food.thucmuatheodvt));
                                        food.tiendicho1tre = scope.round($['*'](food.thucmua1tretheodvt, food.price));
                                    }
                                });
                            });
                        });
                        angular.forEach(scope.datagrid.data, function (warehouses) {
                            angular.forEach(warehouses, function (food, id) {
                                if (map[id] && !mapStorage[id]) {
                                    food.price = map[id];
                                    food.price_kg = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                                    food.thanhtien1nhom = scope.round($['*'](food.price, food.thucmuatheodvt));
                                    food.tiendicho1tre = scope.round($['*'](food.thucmua1tretheodvt, food.price));
                                }
                            });
                        });
                    }
                    scope.totalCalculator();
                };
            });
        }, 0);
    },
    cleanSearch: function () {
        angular.element($("#frm-storage")).scope().$apply(function (scope) {
            scope.keysearch.name = '';
            scope.keysearch.group_id = '';
            scope.keysearch.name_dishes = '';
            $('input#group_id').combobox('clear');
            $('input#name').val('');
            $('input#name_dishes').val('');
            var queryParams = $('#tbl_menu_planning').datagrid('options').queryParams;
            if (queryParams.filterRules) {
                delete queryParams.filterRules;
                delete queryParams.filter_type;
                $('#tbl_menu_planning').datagrid('load', queryParams);
            }
        });
    },
    dishViewDetail: function () {
        var self = this;
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + self.module,
            title: 'Thông tin món ăn',
            draggable: true,
            fullScreen: false,
            showButton: false,
            content: function (element, dialogRef) {
                var html = '<div ng-include="' + $CFG.remote.base_url + '/templates/dish-view-detail.html' + '"></div>';
                loadForm($CFG.project + '/' + self.module, 'dishViewDetail', {}, function (html) {
                    $.menu_planning.angular(element, html, function (scope) {
                    });
                });
            }
        });
    },
    combobox_load_dish: function (scope, meal, group_id, dish) {
        var disk_ids = [];
        angular.forEach(meal.dishes, function (item, dish_id) {
            if (!dish || dish.id != dish_id) disk_ids.push(dish_id);
        });
        var cmb_option = {
            valueField: 'id',
            textField: 'name',
            value: [],
            height: 24,
            panelHeight: 'auto',
            mode: 'remote',
            onSelect: function (row, element) {
                var self = this;
                process($CFG.remote.base_url + '/doing/dinhduong/menu_planning/dishs', {
                    id: row.id,
                    async: true
                }, function (resp) {
                    if (resp) {
                        row = resp;
                        var kt = 0;
                        scope.foodAdded = {};
                        scope.menu_planning.ignore_ids = '';
                        angular.forEach(row.ingredient, function (food, key) {
                            food.name_edit || (food.name_edit = food.name);
                            scope.menu_planning.ignore_ids = scope.menu_planning.ignore_ids + ',' + key;
                            if (food.quantities) {
                                if (food.quantities[scope.row.group_id]) {
                                    food.quantity = food.quantities[scope.row.group_id];
                                }
                            }
                            if (food.quantity) kt++;
                        });
                        if (!count(row.ingredient)) alert('Món ăn không có thực phẩm');
                        if (!kt) {
                            alert('Món ăn không có thực phẩm nào có lượng dành cho nhóm trẻ đang chọn.\n Hãy kiểm tra lại lượng của món ăn theo nhóm trẻ.');
                        }
                        /*Kiểm tra thực phẩm có thêm ngoài không thì đẩy lại vào*/
                        if (dish) { /*Nếu là thêm mới thì không có món được truyền vào*/
                            if (dish.id === row.id) {
                                angular.forEach(dish.ingredient, function (food, food_id) {
                                    food.name_edit = food.name;
                                    food.deleted = false;
                                    if (!row.ingredient[food_id]) {
                                        row.ingredient[food_id] = food;
                                    }
                                });
                            } else {
                                angular.forEach(row.ingredient, function (food, food_id) {
                                    if (food.quantities) {
                                        if (food.quantities[scope.row.group_id] > 0) {
                                            food.quantity = food.quantities[scope.row.group_id];
                                        }
                                    }
                                })
                            }
                        }
                        if (scope.selected.dish.id != row.id) {
                            $('#dish').combobox('clear');
                            scope.$apply(function () {
                                scope.selected.dish = row;
                            });
                        }
                    }
                }, function () {
                }, false);
            },
            onChange: function (newval, oldval, el) {
            },
            queryParams: {group_id: group_id, disk_ids: disk_ids},
            width: 200
        };
        if (dish) {
            cmb_option.value = dish.id;
        } else {
            scope.$apply(function () {
                scope.selected.dish = {};
            });
        }
        $.dm_datagrid.combobox('dish', $CFG.remote.base_url + '/doing/dinhduong/menu_planning/dishs'+(scope.from_meal_share?'?meal_share=1':''), cmb_option);
    }, showChangeDish: function (scope, meal_selected, dish_old, callback) {
        var self = this;
        var meals = scope.menu_planning.meals;
        var group_id = scope.row.group_id;
        scope.foodAdded = {};
        scope.lengthSelectDish = 0;
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + self.module,
            action: 'change_dish',
            title: 'Thêm mới',
            size: size.wide,
            fullScreen: false,
            showButton: false,
            content: function (element, dialogRef) {
                loadForm($CFG.project + '/' + self.module, 'change_dish', {id: self.id}, function (resp) {
                    $.menu_planning.angular(element, resp, function (scope) {
                        scope.menu_planning.ignore_ids = '';
                        scope.menu_planning.dish_selected = undefined;
                        angular.forEach(dish_old.ingredient, function (food, index) {
                            food.deleted = false;
                        });
                        $.menu_planning.combobox_load_dish(scope, meal_selected, group_id, dish_old);
                        scope.changeDishApply = function () {
                            dish_old.ingredient = Object.assign(dish_old.ingredient, scope.foodAdded);
                            if (typeof callback === 'function') {
                                setTimeout(function () {
                                    scope.$apply(function () {
                                        callback(dish_old, dialogRef);
                                    });
                                }, 0)
                            }
                            dialogRef.close();
                        }
                    });
                })
            }
        }, function (resp) {
            if (typeof callback === 'function') {
                callback(resp);
            } else {
                $("#tbl_" + self.module).datagrid('reload');
            }
        });
    },
    showAddDish: function (scope, meal_selected, callback) {
        var self = this;
        var meals = scope.menu_planning.meals;
        var group_id = scope.row.group_id;
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + self.module,
            action: 'add_dish',
            title: 'Thêm mới',
            size: size.wide,
            fullScreen: false,
            showButton: false,
            content: function (element, dialogRef) {
                loadForm($CFG.project + '/' + self.module, 'add_dish', {id: self.id}, function (resp) {
                    $.menu_planning.angular(element, resp, function (scope) {
                        scope.menu_planning.ignore_ids = '';
                        scope.menu_planning.dish_selected = undefined;
                        scope.foodAdded = {};
                        var arr_meals = [];
                        angular.forEach(meals, function (item, meal_define) {
                            arr_meals.push(item);
                        });
                        $.dm_datagrid.combobox('meal', arr_meals, {
                            valueField: 'define',
                            textField: 'name',
                            value: meal_selected.define,
                            height: 24,
                            panelHeight: 'auto',
                            mode: 'local',
                            onSelect: function (meal, element) {
                                setTimeout(function () {
                                    $.menu_planning.combobox_load_dish(scope, meal, group_id);
                                });
                            },
                            onChange: function (newval, oldval, el) {
                            },
                            queryParams: {},
                            width: 200
                        });
                        scope.addDishApply = function () {
                            if (typeof callback === 'function') {
                                scope.selected.dish.ingredient = Object.assign(scope.selected.dish.ingredient, scope.foodAdded);
                                scope.selected.dish = Object.assign({}, scope.selected.dish, {addedAt: (new Date).getTime()});
                                var meal_id = $('#meal').combobox('getValue');
                                callback(meal_id, scope.selected.dish, dialogRef);
                            }
                            if(count(scope.selected.dish)){
                                if(scope.copy_meal && scope.from_meal_share) {
                                    process('dinhduong/dish_storage/share', {dish_info: JSON.stringify(scope.selected.dish)}, function () {
                                    }, false);
                                }
                            }
                        }
                        scope.reloadDishes = function() {
                            $('#dish').combobox('clear');
                            $('#dish').combobox('reload', $CFG.remote.base_url+'/doing/dinhduong/menu_planning/dishs'+(scope.from_meal_share?'?meal_share=1':''));
                            scope.selected.dish = {};
                        }
                        scope.addMealForm = function() {
                            $.dm_datagrid.showAddForm(
                                {
                                    module: $CFG.project + '/dish/',
                                    action: 'add',
                                    title: 'Thêm mới món ăn',
                                    showButton: false,
                                    fullScreen: true,
                                    size: size.wide,
                                    content: function (element) {
                                        loadForm($CFG.project + '/dish','add', {}, function (resp) {
                                            var form = '<div >'+resp+'</div>';
                                            angular.element($('#menu_planningController')).scope().$apply(function(scope){
                                                $(element).html(scope.compile(form,scope));
                                                scope.dish = {
                                                    foods: {},
                                                    row: {},
                                                    selected: {},
                                                };
                                                scope.cache || (scope.cache = {});
                                                scope.cache.foods = undefined;
                                                scope.dish.selected.food_ids = [];
                                                scope.dish.number_child = 100;
                                                scope.dish.number_childs = {};
                                                process('dinhduong/dish/getCat','',function(resp){
                                                    scope.dish.groups = resp.groups;
                                                    scope.dish.categories = resp.categories;
                                                    scope.dish.areas = resp.areas;
                                                    angular.forEach(scope.dish.groups, function (index, value) {
                                                        scope.dish.number_childs[value.id] = 100;
                                                    });
                                                    scope.dish.group_radio = scope.dish.groups[Object.keys(scope.dish.groups)[0]].id;
                                                },false,false);
                                                scope.dish.checkGroupSelected = function(){
                                                    var tam = 0;
                                                    angular.forEach(scope.dish.groups,function(group,index){
                                                        if(group.selected == true){
                                                            tam ++;
                                                        }
                                                    });
                                                    return tam;
                                                };
                                                scope.dish.onSelectFood = function(food) {
                                                    if(!scope.dish.foods[food.id]) {
                                                        var url = $CFG.remote.base_url+'/doing/dinhduong/dish/getFoodDetailById';
                                                        var data = {async: true,id: food.id};
                                                        process(url, data, function(resp){
                                                            if(!resp) return;
                                                            scope.$apply(function(){
                                                                scope.dish.selected.food_ids = [];
                                                                angular.forEach(scope.dish.foods, function(item, food_id){
                                                                    scope.dish.selected.food_ids.push(food_id);
                                                                });
                                                                angular.forEach(resp, function(item, ind){
                                                                    scope.dish.selected.food_ids.push(item.food_id);
                                                                    scope.dish.foods[item.food_id] = item;
                                                                })
                                                            });
                                                        },function(){}, false);
                                                    }else{
                                                        scope.dish.selected.food_ids = [];
                                                        angular.forEach(scope.dish.foods, function(item, food_id){
                                                            scope.dish.selected.food_ids.push(food_id);
                                                        });
                                                        angular.forEach(resp, function(item, ind){
                                                            scope.dish.selected.food_ids.push(item.food_id);
                                                            scope.dish.foods[item.food_id] = item;
                                                        })
                                                    }
                                                };
                                                scope.dish.onChangeRadio = function(group_id){
                                                    angular.forEach(scope.dish.foods, function(food, food_id){
                                                        food.quantity = food.quantities[group_id];
                                                        food.eat_a_group = scope.rounds(food.quantities[group_id]*scope.dish.number_child/1000);
                                                        food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                                                        food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                                                        food.quantity_for_measure = scope.rounds(food.quantities[group_id]/food.gam_exchange);
                                                        food.calo_for_one = scope.sumCaloFood(food);
                                                    });
                                                    scope.dish.number_childs || (scope.dish.number_childs = {});
                                                    if (scope.dish.number_childs[group_id]) {
                                                        scope.dish.number_child = scope.dish.number_childs[group_id];
                                                    } else {
                                                        scope.dish.number_child = 100;
                                                        scope.dish.number_childs[group_id] = 100;
                                                    }
                                                };
                                                scope.sumCaloFood = function(food,key) {
                                                    key || (key = 'quantity');
                                                    var rs = $['*'](scope.co_cau(null).protein,food[key])*(food.nutritions.protein/100)
                                                        + $['*'](scope.co_cau(null).fat,food[key])*(food.nutritions.fat/100)
                                                        + $['*'](scope.co_cau(null).sugar,food[key])*(food.nutritions.sugar/100);
                                                    return round(rs,2);
                                                };
                                                scope.dish.delAllFood = function(){
                                                    scope.dish.foods = {};
                                                };
                                                scope.dish.delFood = function(id){
                                                    var tam = {};
                                                    var food_ids = [];
                                                    scope.dish.selected.food_ids =[];
                                                    $.each(scope.dish.foods,function(food_id,food){
                                                        if(food_id != id){
                                                            tam[food_id] = food; 
                                                            food_ids.push(food_id);
                                                        }
                                                    });
                                                    scope.dish.selected.food_ids = food_ids;
                                                    scope.dish.foods = tam;
                                                };
                                                scope.dish.onChangeOneG = function(item,group_id){
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        if(item.tam == false){
                                                            angular.forEach(scope.dish.groups,function(group,index){
                                                                if(group.selected == true){
                                                                    item.quantities[group.id] = item.quantity;
                                                                }
                                                            })
                                                        }
                                                        item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                        item.calo_for_one = scope.sumCaloFood(item);
                                
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeOneGArr = function(item,group_id,foods){
                                                    scope.dish.group_radio = group_id;
                                                    if(!item.tam){
                                                        angular.forEach(item.quantities, function(value, grp_id){
                                                            if(group_id != grp_id){
                                                                item.quantities[grp_id] = item.quantities[scope.dish.group_radio];
                                                            }
                                                        })
                                                    }
                                                    if(scope.dish.group_radio == undefined){
                                                        item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                        item.calo_for_one = scope.sumCaloFood(item);
                                                    }else if(foods){
                                                        angular.forEach(foods, function(food, food_id){
                                                            food.quantity = food.quantities[scope.dish.group_radio];
                                                            food.eat_a_group = scope.rounds(food.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                                            food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                                                            food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                                                            food.quantity_for_measure = scope.rounds(food.quantities[scope.dish.group_radio]/food.gam_exchange);
                                                            food.calo_for_one = scope.sumCaloFood(food);
                                                        })
                                                    }else{
                                                        item.quantity = item.quantities[scope.dish.group_radio];
                                                        item.eat_a_group = scope.rounds(item.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                        item.quantity_for_measure = scope.rounds(item.quantities[scope.dish.group_radio]/item.gam_exchange);
                                                        item.calo_for_one = scope.sumCaloFood(item);
                                                    }
                                                };
                                                scope.dish.onChangeEAG = function(item){
                                                    var group_id = scope.dish.group_radio;
                                                    var check_group = scope.dish.checkGroupSelected();
                                                     
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        if(check_group != 0 && group_id != undefined){
                                                            if(item.quantities == undefined){
                                                                item.quantities = [];
                                                            }
                                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                                            item.quantity = item.quantities[group_id]
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }else{
                                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeBAG = function(item){
                                                    var group_id = scope.dish.group_radio;
                                                    var check_group = scope.dish.checkGroupSelected();
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        if(check_group !=0 && group_id != undefined){
                                                            if(item.quantities == undefined){
                                                                item.quantities = [];
                                                            }
                                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                                            item.quantity = item.quantities[group_id];
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }else{
                                                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) * 100;
                                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }
                                
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeBAD = function(item){
                                                    var group_id = scope.dish.group_radio;
                                                    var check_group = scope.dish.checkGroupSelected();
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        if(check_group !=0 && group_id != undefined){
                                                            if(item.quantities == undefined){
                                                                item.quantities = [];
                                                            }
                                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                                                            item.quantity = item.quantities[group_id]
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }else{
                                                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) + 100;
                                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }
                                
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeOneDVT = function(item){
                                                    var group_id = scope.dish.group_radio;
                                                    var check_group = scope.dish.checkGroupSelected();
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        if(check_group !=0 && group_id != undefined){
                                                            if(item.quantities == undefined){
                                                                item.quantities = [];
                                                            }
                                                            item.quantities[group_id] = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                                                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                                                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.quantity = item.quantities[group_id];
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }else{
                                                            item.quantity = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                                                            item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                            item.calo_for_one = scope.sumCaloFood(item);
                                                        }
                                
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeEF = function(item){
                                                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                                                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                                                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                                        item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                                                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                                        item.calo_for_one = scope.sumCaloFood(item);
                                                    }else{
                                                        alert("Vui lòng nhập số lượng trẻ");
                                                    }
                                                };
                                                scope.dish.onChangeNC = function(){
                                                    angular.forEach(scope.dish.foods,function(food,index){
                                                        scope.dish.onChangeOneG(food);
                                                    });
                                                    if (scope.dish.group_radio) {
                                                        scope.dish.number_childs || (scope.dish.number_childs = {});
                                                        scope.dish.number_childs[scope.dish.group_radio] = scope.dish.number_child;
                                                    }else{
                                                        for (var i in scope.dish.number_childs) {
                                                            scope.dish.number_childs[i] = scope.dish.number_child;
                                                        }
                                                    }
                                                };
                                                scope.dish.quantityFocus = function(food,group_id){
                                                    food.tam = false;
                                                    food.quantities || (food.quantities = {})
                                                    angular.forEach(scope.dish.groups,function(group,index){
                                                        if(group.selected == true){
                                                            if(food.quantities[group.id]){
                                                                food.tam = true;
                                                            }else{
                                                                food.quantities[group.id] = 0;
                                                            }
                                                        }
                                                    });
                                                    scope.dish.onChangeOneGArr(food,group_id,scope.dish.foods);
                                                };
                                                scope.rounds = function(value) {
                                                    if(typeof value != 'number') {
                                                        value = parseFloat(value);
                                                    }
                                                    value = Math.round(value*1000)/1000;
                                                    return value;
                                                };
                                                scope.dish.uploadDone = function(image){
                                                    if(image){
                                                        $('.image-last').show();
                                                        $('.image-first').hide();
                                                        if(count(image.errors)>0){
                                                            var html = [];
                                                            angular.forEach(image.errors, function(value,index){
                                                                html.push(value);
                                                            });
                                                            $.dm_datagrid.show({title: 'Thông báo', message: html.join('<br/>')});
                                                        }
                                                    }
                                                };
                                                scope.dish.sum_calo = function(){
                                                    var group_id = scope.dish.group_radio;
                                                    var check_group = scope.dish.checkGroupSelected();
                                                    // console.log(group_id,check_group);
                                                    var sum_calo = 0;
                                                    angular.forEach(scope.dish.foods,function(food,idex){
                                                        if(food.nutritions.calo == undefined){
                                                            food.nutritions.calo = 0;
                                                        }
                                                        food.quantities || (food.quantities={});
                                                        food.quantity || (food.quantity=0);
                                                        food.quantities[group_id] || (food.quantities[group_id] = 0);
                                                        sum_calo += scope.sumCaloFood(food);
                                                    });
                                                    return round(sum_calo,2);
                                                };
                                                scope.keyUpFood = function(e,food_id,name,group_id){
                                                    var input = '';
                                                    newfoods = [];
                                                    index = 0;
                                                    angular.forEach(scope.dish.foods,function(food,f_id){
                                                        newfoods[index] = food;
                                                        index ++;
                                                    })
                                                    angular.forEach(newfoods,function(newfood,index){
                                                        if(newfood.food_id == food_id){
                                                            if(e.which == 13 || e.which == 40){
                                                                if(index < count(newfoods)-1){
                                                                    index = index+1;
                                                                    input = [name,newfoods[index].food_id].join('_');
                                                                }
                                                            }else if(e.which == 38){
                                                                if(index>0){
                                                                    index = index-1;
                                                                    input = [name,newfoods[index].food_id].join('_');
                                                                }
                                                            }
                                                        }
                                                    })
                                                    if(input != ''){
                                                        $('input#'+input).focus();
                                                    }
                                                };
                                            });
                                        })
                                    },
                                    buttons: [{
                                        id: 'btn-save',
                                        icon: 'glyphicon glyphicon-floppy-disk',
                                        label: 'Lưu',
                                        cssClass: 'btn-primary',
                                        action: function (dialogRef) {
                                            var data = {};
                                            angular.forEach(scope.dish.foods,function(food,index){
                                                food['number_child'] = scope.dish.number_child;
                                                food['sum_calo'] = scope.dish.sum_calo();
                                            });
                                            data.foods = JSON.stringify(scope.dish.foods);
                                            data.number_childs = scope.dish.number_childs;
                                            let formData = getSubmitForm('frm-dish-add', true);
                                            let group = Object.values(scope.dish.groups).filter(item => item.selected == true).map(item => item.id).toString();
                                            let area = scope.dish.areas.filter(item => item.selected == true).map(item => item.id).toString();
                                            let category = scope.dish.categories.filter(item => item.selected == true).map(item => item.id).toString();
                                            formData.push({id: 'group', value: group})
                                            formData.push({id: 'area', value: area})
                                            formData.push({id: 'category', value: category})
                                            data.together = arrayToJson(formData)
                                            var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/dish/add';
                                            process(url, data, function (resp) {
                                                if (resp.result == "success") {
                                                    alert("Bạn đã thêm món ăn thành công");
                                                    dialogRef.close();
                                                    scope.reloadDishes();
                                                }
                                            });
                                        }
                                    }]
                                },
                                
                                function(resp){
                                    if(typeof callback === 'function') {
                                        callback(resp);
                                    }else{
                                        $("#tbl_"+self.module).datagrid('reload');
                                    }
                                }
                            );
                        }
                    });
                })
            }
        }, function (resp) {
            if (typeof callback === 'function') {
                callback(resp);
            } else {
                $("#tbl_" + self.module).datagrid('reload');
            }
        });
    },
    del: function () { /* XÓA */
        var self = this;
        var ids = [];
        var rows_selected = {};
		var check_school_point = true;
		var school_point = $CFG.school_point;
        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
			if (row.school_point!=$CFG.school_point) {
				check_school_point = false;
				school_point = row.school_point;
			}
            ids.push(row.id);
            rows_selected[row.id] = row;
        });

        var captcha = $CFG.dialog_captcha('delete_menu_planning');
		
		if (!check_school_point) {
			alert('Trong các thực đơn bạn chọn, có thực đơn của điểm trường ' + school_point + ', bạn ko thể sửa hay xóa được!');
			return;
		}

        if (ids.length == 0) {
            $.messager.alert('Thông báo', 'Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style="font-size:14px">Nếu xóa thực đơn mẫu, bạn sẽ không thể khôi phục được dữ liệu của thực đơn mẫu này! Hãy chắc chắn bạn muốn xóa?</div>' + captcha, function (r) {
            if (r) {
                $.dm_datagrid.del($CFG.project + '/' + self.module, {ids , captcha: $('.panel [name="delete_menu_planning_captcha"]').val()}, function (resp) {
                    if (resp.result === 'success') {
                        $("#tbl_" + self.module).datagrid('reload');
                    }
                });
            }
        });
    },
    exportXML: function () {
        var self = this;
        var ids = [];
        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
            ids.push(row.id);
        });
        var group_id = $("#nhomtre").val();
        group_id = group_id.replace('number:', '');
        if (ids.length !== 0 && group_id>=1) {
            window.open("/dinhduong/menu_planning/exportXML?group_id="+group_id+"&ids=" + ids.join(','));
        } else {
            $.messager.alert('Thông báo!', 'Bạn hãy chọn 1 độ tuổi & món ăn để xuất!');
        }
    },
    angular: function (element, resp, callback, dialogRef) { /* Biên dịch html để chạy angularjs*/
        var form = '<div >' + resp + '</div>';
        angular.element($('#menu_planningController')).scope().$apply(function (scope) {
            $(element).html(scope.compile(form, scope));
            if (typeof callback === 'function') {
                callback(scope);
            }
        });
    },
    getDataForm: function (element) { /* Lấy dữ liệu trên form để lưu */
        var scope = angular.element($(element)).scope();
        var data = {
            meals: JSON.stringify(scope.menu_planning.meals),
            data: JSON.stringify(scope.datagrid.data),
            services: scope.row.services,
            row: scope.row,
            rate: scope.caloRate(),
            rate_calo: scope.plgRateBind().dat
        };
        return data;
    },
    angularBalance_money: function (scope) {
    },
    reportShowForm: function () {
        var self = this;
        var urls_export = [$CFG.remote.base_url, $CFG.project, 'menu_planning', 'exportPrint'];
        var btn = $('<a id="btn_preview_kqkhauphandinhduong" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span></a>');
        $('#export-dialog-kqkhauphandinhduong').dialog({
            title: 'Chọn ngày in',
            width: 400,
            height: 200,
            closed: false,
            cache: false,
            modal: true,
            onOpen: function (ele) {
                $(ele).show();
                var selectedrow = $("#tbl_menu_planning").datagrid("getSelected");
                var row = selectedrow;
                if (!row) {
                    alert('Không tìm thấy dữ liệu.');
                    return;
                }
                var row_id = row.id;
                var date = dateboxOnSelect(new Date());
                $('#btn_preview_kqkhauphandinhduong .btn-content-form').html('').append(btn);
                btn.printPage({
                    url: urls_export.join('/') + '?id=' + row_id + '&date=' + date + '&preview=1&apdungcongthuc=' + (scope.apdungcongthuc ? 1 : 0),
                    attr: "href",
                    message: "Phiếu xuất kho đang được tạo ..."
                });
            }
        });
    }
};
