$.grade = {
    module: 'grade',
    id: '', /*Mã project*/
    init: function(id,project) {
    	var self = this;
    	self.id = id;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
        	urls.join('/') + '?status=1', 
        	this.module, /*Đ<PERSON>nh nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Ti<PERSON>u đ<PERSON> cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
				{ title:'Mã khối', field:'id', width:70, sortable:true },
               	{ title:'Tên khối', field:'name', width:300, sortable:true },
				{ title:'Trạng thái', field:'status', width:70, formatter: function (value, row, index) {
						var label = 'Hiện';
						if(value==0){
							label = '<span style="color:orange">Ẩn</span>';
						}else if(value==-1){
							label = '<span style="color:red"><PERSON><PERSON> xóa</span>';
						}
						return label;
                    }
				},
				{ title:'<PERSON><PERSON><PERSON> tạo', field:'created_at_1', width:80 },
				{ title:'Ngày cập nhật', field:'updated_at_1', width:80 }
            ]],
            {
                view: groupview,
                groupField: 'group_id',
                groupFormatter: function(id,rows){
                    return rows[0].group_name + ': <i>' + rows.length+'</i>';
                },onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }, onLoadSuccess:function(data){
                    if($CFG.is_gokids==1) {
                        $('.datagrid-view').height($('.datagrid-view').height() - 30);
                        $('.datagrid-body').height($('.datagrid-body').height() - 30);
                    }
                }
			}
        );
        $.grade.initAngular();
    }, showAddForm: function(callback) { 
    	var self = this;
        $.dm_datagrid.showAddForm(
			{
				module: $CFG.project+'/'+self.module,
				action:'add',
				title:'Thêm mới khối lớp học',
				content: function(element){
					loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
						$(element).html(resp);
					})
				},
				cb: function(dt){
					var value = base64Decode(dt[0].value);
					if (value.trim().length == 0){
						var note = {
							title: 'Thông báo',
							message:'Vui lòng nhập tên khối.',
							buttons:[{
								label: 'Đóng',
								icon: 'glyphicon glyphicon-log-out',
								action: function(dialog){
									dialog.close();
								}
							}]
						};
						$.dm_datagrid.show(note);
						return false;
					}
					return true;
				}
			},
			function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    }, showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
					content: function(element){
						loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
							$(element).html(resp);
						})
					}
				},
				function(resp){
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Phải chọn một dòng!');
	    }
       
    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = $CFG.dialog_captcha('grade');
        var msg = '<div style = "font-size: 14px">Chắc chắn xóa ?</div>' + captchaForm;

        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="grade_captcha"]').val();
                process($CFG.project+'/'+self.module+'/del',{ids: ids, captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.grade.del();
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    }, initAngular: function(){
    	var self = $.grade;
    	setTimeout(function () {
	    	angular.element($('#tb_'+self.module+'_detail')).scope().$apply(function (scope) {
	    		scope.status = 1;
				scope.statusChange = function(){
					var urls = [
			            $CFG.remote.base_url, 'doing',
			            $CFG.project, self.module,
			            'list?status=' + scope.status
			        ];
			        urls = urls.join('/');
			        $("#tbl_" + self.module).datagrid({url: urls});
				}
	    	});
		}, 0);
    }
}
