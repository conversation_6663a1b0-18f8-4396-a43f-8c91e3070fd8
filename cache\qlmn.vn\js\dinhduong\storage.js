$.storage = {
    project: 'dinhduong',
    module: 'storage',
    // templates: function () {
    //     return {
    //         addStorage: 'templates/' + this.project + '/' + this.module + '/add-storage.html'
    //     }
    // },
    id: '', /*Mã project*/
    measures: null,
    warehouses: null,
    init: function(id,project) {
        var self = this;
        self.id = id;
        /*Tải đơn vị tính và danh sach kho trước*/
        process($CFG.project+'/'+self.module+'/special_categories',{},function(resp){
            self.measures = resp.data.measures;
            self.warehouses = resp.data.warehouses;
            self.school_points = resp.data.school_points;
            self.suppliers = resp.data.suppliers;
            self.mapSuppliers = {};
            self.suppliers.forEach(function (supplier) {
                self.mapSuppliers[supplier.id] = supplier.name;
            });
            self.sp_s = resp.data.sp_s;
            self.datagrid();
            $.storage.initAngular();
        },null,false);
       
    },initAngular: function(callback){
        var self = this;
        setTimeout(function(){
            angular.element($('#mainContentController')).scope().$apply(function (scope) {
                $.storage.scope = scope;
                scope.project = self.project;
                // scope.templates = self.templates();
                // scope.loadTemplates(self.templates());
                scope.storage || (scope.storage = {});
                scope.storage.module = self.module;
                scope.storage.addstorage = {};
                scope.storage.addstorage.rows = [];
                scope.storage.opt_searching_warehouse = true;
                scope.storage.opt_searching_date = true;
                scope.storage.number_school_points = self.school_points;
                scope.storage.sp_s = self.sp_s;
                scope.storage.warehouses = self.warehouses;
                scope.storage.suppliers = self.suppliers;
                scope.storage.measures = self.measures;
                scope.storage.keysearch_date = dateboxOnSelect(new Date());
                scope.storage.school_point = '';
                scope.storage.schoolPoints = self.school_points;
                scope.tmp = {};
                scope.mapMeasures = {};
                scope.add = {};
                scope.rowStorage = {};
                scope.tmp.warehouse_id = '';
                scope.storage.date_select = false;
                scope.storage.warehouse = scope.storage.warehouses[1];
                angular.forEach(scope.storage.number_school_points,function(number_school_point,index){
                    // console.log(number_school_point.id);
                    if(scope.storage.sp_s == number_school_point.id){
                        scope.storage.school_point = number_school_point;
                    }
                });
                angular.forEach(scope.storage.warehouses,function(warehouse,index){
                    if(warehouse.id == 2){
                        scope.tmp.warehouse = warehouse;
                    }
                });
                angular.forEach(scope.storage.suppliers,function(supplier,index){
                    if(supplier.id == 1){
                        scope.tmp.supplier = supplier;
                    }
                });
                angular.forEach(scope.storage.measures,function(measure,index){
                    if(measure.id == 1){
                        scope.tmp.measure = measure;
                    }
                    scope.mapMeasures[measure.id] = measure;
                });

                scope.storage.changeYear = function(){
                    scope.storage.monthHT = [
                        {id:'1'},
                        {id:'2'},
                        {id:'3'},
                        {id:'4'},
                        {id:'5'},
                        {id:'6'},
                        {id:'7'},
                        {id:'8'},
                        {id:'9'},
                        {id:'10'},
                        {id:'11'},
                        {id:'12'}
                    ];
                };
                scope.storage.filterDateChange = function(){
                    scope.storage.opt_searching_date = !scope.storage.opt_searching_date;
                    $.storage.doSearch(scope);
                };
                scope.keysearch_dateChange = function(date){
                    $.storage.doSearch(scope);
                };
                scope.storage.school_pointChange = function(data){
                    $.storage.doSearch(scope);
                };
                scope.storage.supplierChange = function(data){
                    $.storage.doSearch(scope);
                };
                scope.storage.warehouseChange = function(data){
                    $.storage.doSearch(scope);
                    var so_luong = $('#so_luong').val();
                    var warehouse = $('#warehouse').val();
                    var ngay_nhap_kho = $('#ngay_nhap_kho').val();
                    var ma_thuc_pham = $('#ma_thuc_pham').val();
                    var don_gia = $('#don_gia').val();
                    if(so_luong !='' && warehouse !='' && ngay_nhap_kho !='' && ma_thuc_pham !='' && don_gia != ''){
                        $('.btn-add.add-new').css('opacity',1);
                    }else{
                        $('.btn-add.add-new').css('opacity',0.3);
                    }
                };
                scope.storage.warehouseSearch = function () {

                    var date_start = scope.storage.start;
                    var date_end = scope.storage.end;
                    var keysearch = scope.storage.keysearch;

                    var queryParams = $('#tbl_storage').datagrid('options').queryParams;

                    var pattern =/^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/;
                    if(pattern.test(date_start) && pattern.test(date_end))
                    {
                        queryParams.date_start = date_start;
                        queryParams.date_end = date_end;
                    }
                    else
                    {
                        queryParams.date_start = '';
                        queryParams.date_end = '';
                    }
                    queryParams.keysearch = keysearch;
                    $('#tbl_storage').datagrid({'load': queryParams});
                };
                scope.storage.formAddStorage = function(){
                    $.dm_datagrid.showAddForm({
                        module: $CFG.project+'/'+self.module,
                        action:'addstorage',
                        title:'Nhập kho',
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        content: function(element,dialogRef){
                            // scope.fee_report.loadSoquytienmat(fee_category_id);
                            loadForm($CFG.project+'/'+self.module,'addstorage', {}, function(resp){
                                var html = "<div>"+resp+"</div>";
                                $(element).html(scope.compile(html,scope));
                            });
                          }
                        },
                        function(resp){
                          if(typeof callback === 'function') {
                              callback(resp);
                          }else{
                              $("#tbl_"+self.module).datagrid('reload');
                          }
                        }
                    );
                };
                scope.storage.delAddStorage = function(index){
                    if(typeof index ==='number' && scope.storage.addstorage.rows){
                        var rows = [];
                        angular.forEach(scope.storage.addstorage.rows, function(row,ind){
                            if(ind != index){
                                rows.push(row);
                            }
                        })
                        scope.storage.addstorage.rows = rows;
                    }
                };
                scope.storage.addRow = function(row){
                    // console.log(row);
                    var vals = ['warehouse','food','price','quantity','measure','supplier'];
                    var check_vals = ['warehouse','food','quantity'];
                    var tmp_row = {};
                    var text_er = '';
                    var check_empty = true;
                    if(row){
                        angular.forEach(vals,function(key,index) {
                            if(row[key]+'' != '0' && row[key] != '' && row[key] != undefined){
                                check_empty = false;
                            }
                            tmp_row[key] = row[key];
                        });
                        angular.forEach(check_vals,function(key,index) {
                            if(row[key]+'' == '0' || row[key] == '' || row[key] == undefined){
                                var name_key = '';
                                if(key == 'warehouse'){
                                    name_key = "Kho";
                                }
                                if(key == 'food'){
                                    name_key = "Thực phẩm";
                                }
                                if(key == 'quantity'){
                                    name_key = "Số lượng";
                                }
                                text_er += name_key + ', ';
                                check_empty = true;
                            }
                        });
                        
                        
                        if(!check_empty){ /*    Nếu có dữ liệu thì thêm mới*/
                            tmp_row.selected = true;
                            tmp_row.is_add = false;
                            row.selected = false;
                            scope.storage.addstorage.rows || (scope.storage.addstorage.rows=[]);
                            scope.storage.addstorage.rows.push(tmp_row);
                            row['food'] = '';
                            row['price'] = '';
                            row['quantity'] = '';
                            var rows = scope.storage.addstorage.rows;
                            for(var i=0; i<rows.length - 1;i++) {
                                for(var j=i+1; j<rows.length;j++) {
                                    if(rows[i].date>rows[j].date){
                                        var tmp = rows[i];
                                        rows[i] = rows[j];
                                        rows[j] = tmp;
                                    }
                                }
                            }
                            scope.storage.addstorage.rows = rows;
                            scope.storage.saveAddStorage();
                        }else{
                            alert("Những trường bắt buộc phải nhập : " + text_er);
                        }
                    }
                };
                scope.storage.onChange_day = function(row){
                    var days = 15;
                    if(row.date == 0){
                        row.date = 1;
                    }else if(row.date>days){
                        row.date = days;
                    }
                };
                scope.storage.rowSelect = function(row){
                    if(!row){
                        // scope.storage.selected.rowAdd = true;
                        if(scope.storage.addstorage.rows) {
                            angular.forEach(scope.storage.addstorage.rows, function(item,index){
                                item.selected = false;
                            });
                        }
                    }else{
                        angular.forEach(scope.storage.addstorage.rows, function(item,index){
                            item.selected = false;
                        });
                        // scope.storage.selected.rowAdd = false;
                        // row.selected = true;
                    }
                };
                scope.storage.foodSelect = function(row){
                    // console.log(row);
                    
                    scope.tmp.price = row.price;
                    scope.tmp.measure_name = row.measure_name;
                   // console.log(scope.tmp.measure_name);
                };

                scope.storage.foodSelectNew = function (row) {
                    process($CFG.remote.base_url + '/doing/dinhduong/storage/foods', {
                        async: true,
                        id: row.id
                    }, function (resp) {
                        if (count(resp)>0) {
                            scope.add.price = resp[0].price;
                            scope.add.measure_name = resp[0].measure_name;
                            scope.add.price = resp[0].price;
                            scope.add.supplier = resp[0].supplier;
                            $('#txt-supplier').val(scope.add.supplier);
                        }
                    },null, false, true);
                };

                scope.storage.AddStorageNew = function(){
                    var id = $('#food_combobox').combobox('getValue');
                    var name = $('#food_combobox').combobox('getText');
                    var data = $('#food_combobox').combobox('getData');
                    var food = scope.storage.food;
                    angular.forEach(data, function(item, ind){
                        if(id == item.id) {
                            food = item;
                        }
                    })
                    var date = scope.storage.keysearch_date;
                    var warehouse = scope.storage.warehouse;
                    var quantity = scope.add.quantity;
                    var school_point = scope.storage.school_point;
                    var has_receipt = scope.storage.has_receipt;
                    var price = scope.add.price;
                    var supplier = scope.add.supplier;
                    var check_null = false;
                    var text_er = "";
                    var is_save_ncc = $('#chk_save_ncc_tptruong').is(':checked')? 1: 0;

                    if(food == '' || food == undefined || food == null || food+'' == '0'){
                        check_null = true;
                        text_er += "Tên thực phẩm, ";
                    }
                    if(date == '' || date == undefined || date == null || date+'' == '0'){
                        check_null = true;
                        text_er += "Ngày nhập kho, ";
                    }
                    if(warehouse == '' || warehouse == undefined || warehouse == null || warehouse+'' == '0'){
                        check_null = true;
                        text_er += "Kho, ";
                    }
                    if(quantity == '' || quantity == undefined || quantity == null || quantity+'' == '0'){
                        check_null = true;
                        text_er += "Số lượng, ";
                    }

                    if(check_null == false){
                        var data = {
                            date: date,
                            food: food,
                            warehouse_id: warehouse.id,
                            quantity: quantity,
                            school_point: school_point,
                            price: price,
                            supplier: supplier,
                            has_receipt: has_receipt,
                            is_save_ncc: is_save_ncc
                        };
                        if(scope.storage.expiry){
                            data.expiry = scope.storage.expiry;
                            data.detail = true;
                        }
                        if(scope.add.preserved){
                            data.preserved = scope.add.preserved;
                            data.detail = true;
                        }
                        if(scope.add.license){
                            data.license = scope.add.license;
                            data.detail = true;
                        }
                        if(scope.add.pass){
                            data.pass = scope.add.pass;
                            data.detail = true;
                        }
                        if(scope.add.note){
                            data.note = scope.add.note;
                            data.detail = true;
                        }
                        var urls = [$CFG.project,'storage','saveAddStorage'];
                        process(urls.join('/'),data,function(resp){
                            if(resp.result == "success"){
                                alert("Đã thêm thành công");
                                scope.add.price = 0;
                                scope.add.quantity = 0;
                                scope.storage.food = '';
                                $('#food_combobox').combobox('clear').next().find('input').focus();
                                scope.add.supplier = '';
                                $("#tbl_"+self.module).datagrid('reload');
                            }
                        });
                    }else{
                        alert("Những trường không được để trống : " + text_er);
                    }
                };
                scope.storage.saveAddStorage = function(){
                    if(count(scope.storage.addstorage.rows) == 0){
                        alert("Không thêm mới được! Vui lòng kiểm tra lại ");
                    }else{
                        var html = ``;
                        var err = false;
                        var quantity = ``;
                        angular.forEach(scope.storage.addstorage.rows, function(item,index){
                            if(item.quantity+'' == '0' || item.quantity == '' || item.quantity == undefined){
                                err = true;
                                quantity = ` Số lượng -`;
                            }
                        });
                        if(err == true){
                            alert("Vui lòng nhập (" + quantity + " )" );  
                        }else{
                            var is_save_ncc = $('#chk_save_ncc_tptruong').is(':checked')? 1: 0;
                            var data = {
                                date: scope.storage.keysearch_date,
                                school_point: scope.storage.school_point.id,
                                rows: scope.storage.addstorage.rows,
                                is_save_ncc: is_save_ncc
                            }
                            var urls = [$CFG.project,'storage','saveAddStorage'];
                            process(urls.join('/'),data,function(resp){
                                if(resp.result == "success"){
                                    alert("Đã thêm thành công");
                                    scope.storage.addstorage.rows = '';
                                    $("#tbl_"+self.module).datagrid('reload');
                                }
                            });
                        }
                    }
                   
                }
                scope.storage.changeMonth = function(){
                    if(scope.storage.warehouse == undefined){
                        scope.storage.warehouse = 2;
                    }
                    var ym = [
                        year = scope.storage.year,
                        month = scope.storage.month,
                        warehouse = scope.storage.warehouse
                    ];
                    // console.log(ym);
                    // console.log(scope.storage.warehouse);
                    if (month == undefined || year == undefined) {
                        alert("Vui lòng chọn tháng cần xem sổ kho")
                    }else{
                        process('dinhduong/storage/gumshoe',{params:ym},function(resp){
                        
                            scope.storage.gumshoes = resp.data.gumshoes;
                            scope.storage.gumshoe_first = resp.data.gumshoe_first;
                            scope.storage.gumshoenews = {};
                            angular.forEach(scope.storage.gumshoes, function(gumshoe, key) {
                                var newkey = key.slice(8,10);
                                if(newkey < 10){
                                    newkey = key.slice(9,10);
                                }
                                scope.storage.gumshoenews[newkey] = gumshoe;
                            });
                        },null,false);
                    }
                    
                };

                /*Details*/
                scope.storage.showExtend = false;

                /*1. Click btn*/
                scope.storage.formVote = function () {
                    $.dm_datagrid.showAddForm({
                            module: $CFG.project + '/' + self.module,
                            action: 'formVote',
                            title: 'Quản lý nhập kho',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            content: function (element) {
                                process($CFG.project+'/'+self.module+'/formVote', {async: true}, function(resp){
                                    var html = scope.getTemplate(scope.templates.addStorage, scope);
                                    element.html(html);

                                    scope.storage.voteName = resp.voteName;
                                    scope.storage.dateDisabled = false; /*Mở ngày nhập*/
                                    scope.storage.date =  dateboxOnSelect(); /*Ngày hiện tại*/
                                    scope.storage.selectSchoolPoint =  resp.selectSchoolPoint;

                                    /*add*/
                                    scope.storage.foodIdAdd = '';
                                    scope.storage.warehouseAdd = '2';
                                    scope.storage.quantityAdd = 0;
                                    scope.storage.priceAdd = 0;
                                    scope.storage.supplierAdd = '';
                                    scope.storage.schoolPointAdd = resp.schoolPointAdd;

                                    scope.storage.rows = [];
                                    scope.storage.selected = {};
                                    scope.storage.measure = '';
                                    scope.storage.food = {};


                                })
                            }
                        },
                        function (resp) {
                            if (typeof callback === 'function') {
                                callback(resp);
                            } else {
                                $("#tbl_" + self.module).datagrid('reload');
                            }
                        }
                    );
                };

                /* 2. Thay đổi số phiếu*/
                scope.storage.voteChange = function () {
                    var post = {
                        'voteName' : scope.storage.voteName
                    };
                    process($CFG.project+'/'+self.module+'/voteChange', {post: post, async: true}, function (resp) {
                        $.storage.scope.$apply(function () {
                            var data = resp.data;

                            scope.storage.dateDisabled = false;
                            if(data.id !== -1){
                                scope.storage.dateDisabled = true;
                            }

                            scope.storage.id = data.id;
                            scope.storage.date = getDate(data.date);
                            if(data.rows.length > 0){
                                data.rows.forEach(function (row, index) {
                                    data.rows[index].quantity = parseFloat(row.quantity);
                                    data.rows[index].price = parseFloat(row.price);
                                    data.rows[index].measures = scope.mapMeasures[row.measure_id].name;
                                });
                            }
                            scope.storage.rows = data.rows;
                            // console.log('VoteChange',data.rows);

                        });
                    });
                };

                /* 3. Chọn thực phẩm */
                scope.storage.onSelectFood = function (food) {
                    var url = $CFG.remote.base_url + '/doing/dinhduong/dish/getFoodDetailById';
                    var data = {async: true, id: food.id};
                    process(url, data, function (resp) {
                        if (!resp) return;
                        scope.$apply(function () {
                            angular.forEach(resp, function (food) {
                                scope.storage.food = food;
                                scope.storage.foodIdAdd = food.food_id;
                                scope.storage.measure = food.measure_name;
                            });
                        })
                    }, function () {
                    }, false);
                };

                /* 4. Lưu */
                scope.storage.saveStorage = function () {
                    var post = {
                        'voteName' : scope.storage.voteName,
                        'date' : scope.storage.date,
                        'food_id' : scope.storage.foodIdAdd,
                        'warehouse_id' : scope.storage.warehouseAdd,
                        'quantity' : scope.storage.quantityAdd,
                        'price' : scope.storage.priceAdd,
                        'supplier' : scope.storage.supplierAdd,
                        'school_point' : scope.storage.schoolPointAdd,
                        'food' : scope.storage.food
                    };
                    // console.log('POST', post);
                    process($CFG.project+'/'+self.module+'/saveStorage', {post: post, async: true}, function (resp) {
                        if(resp.result === 'success') {
                            $.storage.scope.$apply(function () {
                                post.id = parseInt(resp.data.id);
                                post.food_name = post.food.name;
                                post.warehouse_id = parseInt(post.warehouse_id);
                                post.supplier_id = parseInt(post.supplier);
                                post.measures = post.food.measure_name;
                                scope.storage.rows.push(post);
                                alert("Thêm thành công!")
                                // console.log('Row add', post);
                            });
                        }
                        else
                            alert("Không thể thêm vui lòng kiểm tra lại!");
                    });
                };
                
                scope.storage.clickEdit = function (row) {
                    process($CFG.project+'/'+self.module+'/editStorage', {post: row}, function(resp){
                        if(resp.result === 'success') {
                            alert('Sửa thành công');
                        }
                    });
                };

                scope.storage.clickDel = function (index, row) {
                    if(!row){
                        alert('Không tìm thấy dữ liệu.');
                        return;
                    }
                    var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>', ' - Dữ liệu sau khi xóa sẽ không thể khôi phục.'];
                    // msg.push(' - Dữ liệu sau khi xóa sẽ không thể khôi phục.');
                    $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
                        if (r){
                            process($CFG.project+'/'+self.module+'/del',{ids: row.id},function(resp){
                                scope.storage.rows.splice(index, 1);
                            })
                        }
                    });
                };
                scope.storage.showDetail = function(storage_id) {
                    var self = this;
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            action:'detail',
                            title:'Chi tiết xuất kho',
                            content: function(element){
                                loadForm($CFG.project+'/'+self.module,'detail', {storage_id: storage_id}, function(resp){
                                    $.storage.angular(element,resp,function(scope){
                                    });
                                })
                            }

                        },
                        function(resp){
                            if(typeof callback === 'function') {
                                callback(resp);
                            }else{
                                scope.storage.voteChange();
                            }
                        }
                    );
                };
                scope.storage.saveDetail = function () {
					if(scope.storageDetail.supplier_show) {
						scope.storageDetail.supplier_id = $('#txt-supplier-id').val();
					}
					if(scope.storageDetail.is_exported) {
						scope.storageDetail.storage_date = '';
					}
                    scope.storageDetail.is_save_ncc = $('#chk_save_ncc_tptruong').is(':checked')? 1: 0;
                    process('dinhduong/storage/saveDetail', scope.storageDetail, function () {
						if(scope.storageDetail.is_exported==0 || scope.storageDetail.supplier_show) {
							$("#tbl_"+self.module).datagrid('reload');
						}
						alert("Cập nhật thành công!");
                    });
                }
            });
        });
    }, datagrid: function(){
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
            urls.join('/'), 
            self.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { 
                    field:'ck', 
                    title: 'STT', 
                    checkbox: true 
                },
                { 
                    title:'Tên thực phẩm', 
                    field:'name', 
                    width:250, 
                    sortable:true, 
                    formatter: function(value, row){
                        value = `<span title="ID: `+row.id+`; Thực phẩm chuyển năm. Mã nhập kho gốc: `+row.storage_id+`; Ngày tạo gốc: `+row.origin_created_at+`;  Ngày sửa gốc: `+row.origin_updated_at+`">`+value+`</span>`;
                        if(row.is_legal == 2) {
                            value += `<span class="hover-pointer glyphicon glyphicon-info-sign color-orange" style="margin-left:5px;"></span>`;
                        }
                        return value;
                    } 
                },
                {
                    title:'Mã thực phẩm',
                    field: 'food_id',
                    width: 100
                },
                { 
                    title:'Kho lưu', 
                    field:'warehouse_id', 
                    width:100, 
                    sortable:true, 
                    formatter: function(value,row){
                        $.each(self.warehouses,function(index,item){
                            if(item.id == value){
                                value = item.name;
                                return;
                            }
                        })
                        return value;
                    },editor:{
                        type:'combobox',
                        options:{
                            valueField:'id',
                            textField:'name',
                            data: $.storage.warehouses,
                            required:true
                        }
                    } 
                },
                { 
                    title:'Số lượng', 
                    field:'quantity', 
                    width:60, 
                    align: 'center', 
                    formatter: function(value,row,index){
                        value = digit_grouping(Number(value));
                        return value;
                    }, editor: {
                        type:'numberbox'
                        ,
                        options:{
                            min: 0,
                            precision: 5,
                            formatter: function(value) {
                                return parseFloat(value);
                            }
                        }
                    } 
                },
                { 
                    title:'Đơn giá', 
                    field:'price', 
                    width:70, 
                    align: 'right', 
                    formatter: function(value,row,index){
                        value = digit_grouping(Number(value));
                        return value;
                    } , editor: {
                        type:'numberbox'
                        ,
                        options:{
                            min: 0,
                            precision: 5,
                            formatter: function(value) {
                                return parseFloat(value);
                            }
                        }
                    }
                },
                { 
                    title:'Thành tiền', 
                    field:'total', 
                    width:74, 
                    align: 'right', 
                    formatter: function(value,row,index){
                        value = digit_grouping(round(Number(row.quantity * row.price)));
                        return value;
                    } , editor: {
                        type:'numberbox'
                        ,
                        options:{
                            min: 0,
                            precision: 5,
                            formatter: function(value) {
                                return parseFloat(value);
                            }
                        }
                    }
                },
                { 
                    title:'Ngày nhập', 
                    field:'date', 
                    width:80,  /*editor: {type:'datebox','data-options':{parser:converdate,formatter:myformatter}},*/ align: 'center', formatter: function(value){
                        // var d = new Date(value);
                        // console.log(value);
                        // return d.getDate() + "/" + (d.getMonth()+1) + "/" + d.getFullYear();
                        return value;
                    } 
                },
                { 
                    title:'Nhà cung cấp', 
                    field:'supplier_id', 
                    width:100, 
                    sortable:true, 
                    formatter: function(value,row){
                        $.each(self.suppliers,function(index,item){
                            if(item.id  == value){
                                value = item.name;
                                return;
                            }else if(value == 0){
                                value = " ";
                            }
                            
                        })
                        return value;
                    },editor:{
                        type:'combobox',
                        options:{
                            valueField:'id',
                            textField:'name',
                            data: $.storage.suppliers,
                            //required:true
                        }
                    } 
                },
                { 
                    title:'Đơn vị tính', 
                    field:'measure_id', 
                    width:60, 
                    align: 'center', 
                    formatter: function(value,row){
                        $.each(self.measures,function(index,item){
                            if(value != null){
                                if(item.id == value){
                                    value = item.name;
                                    return;
                                }
                            }else{
                                value = row.food_measure_id;
                                if(item.id == value){
                                    value = item.name;
                                    return;
                                }
                            }

                        })
                        return value;
                    } 
                },
                { 
                    title:'Điểm trường', 
                    field:'school_point', 
                    width:64,
                    align: 'center'
                },
                { 
                    title:'Chức năng', 
                    field:'abc', 
                    width:80,  
                    align: 'center', 
                        formatter: function(value,row,index){
                        // console.log(row)
                        if(row.quantity != row.inventory){
                            var btn = `<label onclick="$.storage.showDetailForm(`+row.id+`)" class='hover-pointer' style="color:color:#3c763d; margin-bottom:0px; font-size: 15px;" title="Xem chi tiết xuất kho">
                                        <span class="fa fa-eye" style="color:#3c763d;"></span>
                                    </label> <label onclick="$.storage.showEditDetail(`+row.id+`, `+"'"+row.name+"'"+`, 1)" class='hover-pointer' style="color:blue; margin-bottom:0px; font-size: 15px;padding: 0 5px;" title="Sửa chi tiết nhập kho">
                                        <span class="fa fa-pencil" style="color:blue;"></span>
                                    </label>`;
                            return btn;
                        }
                        if (row.editing && row.is_lock==0) {
                            if(row.is_legal == 2) {
                                return '';
                            }else{
                                var s = `<button onclick="$.storage.saverow(`+index+`)" class='hover-pointer' style="color:blue; margin-bottom:0px; border:none; background:none;" title="Lưu">
                                            <span class="hover-pointer glyphicon glyphicon-floppy-disk" style="color:blue;"></span> Lưu
                                        </button>`;
                                var c = `<label onclick="$.storage.cancelEditor(`+index+`)" class='hover-pointer' style="color:#ff8400;">
                                            <span class="hover-pointer glyphicon glyphicon-floppy-remove" style="color:red; margin-left:5px;"></span> Hủy
                                            </label>`;
                                return s+c;
                            }
                        } else {
                            var disabled = '';
                            if (row.is_lock==1){
                                disabled = ' disabled="disabled" ';
                            }
                            var btn_del =   `<button onclick="$.storage.del(`+index+`)" `+disabled+` class='hover-pointer' style="color:red; margin-bottom:0; font-size: 15px; border:none; background:none;" title="Xóa">
                                                <span class="fa fa-trash-o"></span>
                                            </button>`;
                            var btn_save =  `<label onclick="$.storage.showEditDetail(`+row.id+`, `+"'"+row.name+"'"+`, 0)" class='hover-pointer' style="color:blue; margin-bottom:0px; font-size: 15px;padding: 0 5px;" title="Sửa chi tiết nhập kho">
                                                <span class="fa fa-pencil" style="color:blue;"></span>
                                            </label>`;
                            return btn_del + btn_save;
                                    
                        }
                    }
                },
                {
                    title: 'Cập nhật lúc',
                    field: 'updated_at',
                    width: 120,
                    align: 'center',
                    formatter: function (value) {
                        var dateTime = new Date(value);
                        return moment(dateTime).format('DD/MM/YYYY, HH:mm:ss');
                    }
                },
            ]],
            {
                view: groupview,
                groupField: 'date',
                queryParams: {
                    filterRules: [
                        {field: 'warehouse_id', op: 'equal', value: 2},
                        {field: 'school_point', op: 'equal', value: self.sp_s}
                        // {field: id, op: op.op, value: op.value},
                    ]
                },
                groupFormatter: function(id,rows){
                    var total = _.sumBy(rows, function(row) {
                        return Number(row.quantity * row.price);
                    });
                    var total_element = $('td[field="total"]').first();
                    var left = total_element.position().left;
                    var width = total_element.width();

                    return '<div style="position: relative">' +rows[0].date + ': <i>' + rows.length+'</i><span style="text-align:right;padding-right:4px;position:absolute;left:' + left + 'px;width:'+width+ 'px">' + digit_grouping(round(total)) + '</span></div>';
                }, onSelect: function(index,row) {
                    $('#export_print').prop('disabled',false).css('opacity','1');

                    if(row.inventory==row.quantity && row.is_legal != 2 && row.is_lock==0) {
                        $('#tbl_storage').datagrid('beginEdit',index);
                        // row.editing = true;
                        $('#export_print').prop('disabled',false).css('opacity','1');
                    }
                    angular.element($('#mainContentController')).scope().$apply(function (scope) {
                        scope.storage.date_select = row.date;
                    }); 
                }, onUnselect: function(index, row){
                    var rows = $('#tbl_storage').datagrid('getSelections');
                    angular.element($('#mainContentController')).scope().$apply(function (scope) {
                        scope.storage.date_select = undefined;
                    }); 
                }, onBeforeEdit:function(index,row){
                    if(row.is_lock==0){
                        row.editing = true;
                    }
                    $(this).datagrid('refreshRow', index);
                }, onBeginEdit:function(rowIndex){
                    var editors = $('#tbl_storage').datagrid('getEditors', rowIndex);
                    var n1 = $(editors[1].target);
                    var n2 = $(editors[2].target);
                    var n3 = $(editors[3].target);
                    n3.numberbox('setValue',n1.numberbox('getValue')*n2.numberbox('getValue'));
                    n1.add(n2).numberbox({
                        onChange:function(){
                            var cost = n1.numberbox('getValue')*n2.numberbox('getValue');
                            n3.numberbox('setValue',cost);
                        }
                    })
                }, onDblClickRow: function(index,row){

                }, onAfterEdit:function(index,row){
                    row.editing = false;
                    $(this).datagrid('refreshRow', index);
                    $('#export_print').prop('disabled',true).css('opacity','0.3');
                }, onCancelEdit:function(index,row){
                    row.editing = false;
                    $(this).datagrid('refreshRow', index);
                    $('#export_print').prop('disabled',true).css('opacity','0.3');
                }, onLoadSuccess:function(data){
                    $('#export_print').prop('disabled',true).css('opacity','0.3');
					setTimeout(function(){
						if($CFG.is_gokids==1) {
							$('.datagrid-view').height($('.datagrid-view').height() - 30);
							$('.datagrid-body').height($('.datagrid-body').height() - 30);
						}
					},1000);
                }
            }
        );
    }, cancelEditor:function(index){
        $('#tbl_storage').datagrid('cancelEdit', index);
    }, saverow: function(index){
        var self = this;
        var item = {};
        var ed = $('#tbl_storage').datagrid('getEditor', {
                index: index,
                field: 'warehouse_id'
            });
        item.warehouse_id = $(ed.target).combobox('getValue');
        ed = $('#tbl_storage').datagrid('getEditor', {
                index: index,
                field: 'supplier_id'
            });
        item.supplier_id = $(ed.target).combobox('getValue');
        ed = $('#tbl_storage').datagrid('getEditor', {
                index: index,
                field: 'price'
            });
        item.price = $(ed.target).combobox('getText');
        ed = $('#tbl_storage').datagrid('getEditor', {
                index: index,
                field: 'quantity'
            });
        item.quantity = $(ed.target).combobox('getText');
        // ed = $('#tbl_storage').datagrid('getEditor', {
        //         index: index,
        //         field: 'date'
        //     });
        item.date = '';
        var data = $('#tbl_storage').datagrid('getData');
        if(!data.rows){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var row = data.rows[index];
        if(!row){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        item.id = row.id;
        var action = $CFG.project+'/storage/edit';
        process(action, {data: item}, function(resp){
            if(resp.result == 'success') {
                $('#tbl_storage').datagrid('endEdit', index);
                // $.storage.cancelEditor(index);
            }
        });        
    }, addAction: function(id){
        if(!id) return;
        if($('.btn-add.add-new').css('opacity')+'' != '1'){
            return;
        }
        var self = this;
        var data = arrayToJson(getSubmitForm(id,true));
        var action = $CFG.project+'/storage/add';
        process(action, {data:data}, function(resp){
            if(resp.result == 'success') {
                $("#tbl_"+self.module).datagrid('reload');
                $('#ma_thuc_pham').combobox('clear');
                $('.btn-add.add-new').css('opacity',0.3);
            }
        });
    },showGumshoeForm: function(callback) { 
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'add',
                title:'Theo dõi sổ kho',
                fullScreen: true,
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'gumshoe', {}, function(resp){
                        $.storage.angular(element,resp,function(scope){
                            scope.storage.yearHT = [
                                {id:'2020'},
                                {id:'2019'},
                                {id:'2018'},
                                {id:'2017'},
                                {id:'2016'},
                                {id:'2015'},
                                {id:'2014'}
                            ];
                        });
                        // $(element).html(resp);
                    })
                }
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    },angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
        angular.element($('#mainContentController')).scope().$apply(function (scope) {
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }, doSearch: function(){
        setTimeout(function(){
            var scope = $.storage.scope;
            if(!scope){
                return;
            }
            var opt_search = {};
            if(scope.storage.warehouse){
               
                opt_search.warehouse_id = {value: scope.storage.warehouse.id,op:'equal'};
            }
            if(scope.storage.supplier){
               
                opt_search.supplier_id = {value: scope.storage.supplier.id,op:'equal'};
            }
            if(scope.storage.school_point){
                // if(scope.storage.school_point != undefined ){
                        opt_search.school_point = {value: scope.storage.school_point.id, op:'equal'};
                // }
            } 
/*            if(scope.storage.keysearch_date){
                if(scope.storage.opt_searching_date){
                    var date_arr = scope.storage.keysearch_date.split('/');
                    opt_search.date = {value: date_arr[2]+'-'+date_arr[1]+'-'+date_arr[0],op:'equal'};
                }
            }*/
            if(count(opt_search)>0){
                $.dm_datagrid.doSearch('tbl_storage',opt_search,'and');
            }else{
                $.dm_datagrid.doSearch('tbl_storage',{},'and');
            }
        },300)
    }, 
    del: function(index){ // XÓA
        var self = this;
        var data = $('#tbl_storage').datagrid('getData');
        if(!data.rows){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var row = data.rows[index];
        if(!row){
            alert('Không tìm thấy dữ liệu.');
            return;
        }
        var msg = ['<div style = "font-size: 14px">Chắc chắn xóa ?</div>'];
        msg.push(' - Dữ liệu sau khi xóa sẽ không thể khôi phục.');
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function(r){
            if (r){
                process($CFG.project+'/'+self.module+'/del',{ids: row.id},function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                })
            }
        });
        setTimeout(function(){
            $('#tbl_storage').datagrid('cancelEdit', index);

        },0); 
    },
    delMultiple:function(){
        var self = this;
        var ids = [];
        var rows_selected = {};
        var editting = [];

        $.each($("#tbl_" + self.module).datagrid('getSelections'), function (index, row) {
            if(row.quantity != row.inventory){
                row.editing = 0;
            }else{
                if(row.is_lock != 1){
                    row.editing = (row.is_legal==2)? 2: 3;
                }else{
                    row.editing = 1;
                }
            }
            ids.push(row.id)
            editting.push(row.editing)
            rows_selected[ids] = row;
        });
        var filter_edit_0 = editting.includes(0);
        var filter_edit_1 = editting.includes(1);
        var filter_edit_2 = editting.includes(2);
        if(filter_edit_1){
            $.messager.alert('Thông báo', 'Chức năng quản lý kho đang bị khoá theo năm nên không xoá được!');
            return; 
        }
        if(filter_edit_0){
            $.messager.alert('Thông báo', 'Có thực phẩm đã xuất kho trong danh sách chọn, nên không được xóa!');
            return; 
        }
        if(filter_edit_2){
            $.messager.alert('Thông báo', 'Có thực phẩm chuyển tồn trong danh sách chọn, nên không được xóa!');
            return; 
        }
        var captcha = $CFG.dialog_captcha('delete_menu_planning');
        if (ids.length == 0) {
            $.messager.alert('Thông báo', 'Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style="font-size:14px">Nếu xóa thực phẩm, bạn sẽ không thể khôi phục được dữ liệu của thực phẩm này! Hãy chắc chắn bạn muốn xóa?</div>' + captcha, function (r) {
            if (r) {
                $.dm_datagrid.del($CFG.project + '/' + self.module + '/delMultiple', {ids , captcha: $('.panel [name="delete_menu_planning_captcha"]').val()}, function (resp) {
                    if (resp.result === 'success') {
                        $("#tbl_" + self.module).datagrid('reload');
                    }
                });
            }
        });
    },
    exportFormAction :  function (id){
        var self = this;
        var urls_export = [$CFG.remote.base_url,$CFG.project,'storage','exportExcelPhieuNhapKho'];
        $('#export-dialog').dialog({
            title: 'In phiếu nhập kho',
            width: 400,
            height: 200,
            closed: false,
            cache: false,
            modal: true ,
            onOpen : function (ele) {
                $(ele).show();
                var selectedrow = $("#tbl_storage").datagrid("getSelected");
                var row = selectedrow ;
                if(!row){
                    alert('Không tìm thấy dữ liệu.');
                }
                var f_date  = row.date;
                var ss = f_date.split('/');
                var selected_date = parseInt(ss[2],10)+"-"+parseInt(ss[1],10)+"-"+parseInt(ss[0],10);
                var date;
                var type;
                $("#ngay_xuat").datebox({
                    width:100,
                    onSelect: function(date){
                        selected_date = date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
                    }
                });
                $('#ngay_xuat').datebox('setValue',f_date);
                $('#btn_export').click(function(){
                    var nguoi_lap_phieu  = $('#nguoi_lap_phieu').val();
                    var nguoi_giao_hang  = $('#nguoi_giao_hang').val();
                    var thu_kho  = $('#thu_kho').val();
                    var ke_toan_truong  = $('#ke_toan_truong').val();
                    if(typeof selected_date == 'undefined') {
                        alert('Vui lòng chọn ngày xuất') ;
                        return;
                    }
                    date  = selected_date;
                    type  = $('#export_type').val();
                    process($CFG.project+'/'+self.module+'/update_print_info',
                        {type:2,nguoi_lap_phieu:nguoi_lap_phieu,nguoi_giao_hang:nguoi_giao_hang,thu_kho:thu_kho,ke_toan_truong:ke_toan_truong},
                        function(data){
                            location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                        },
                        function(data){
                            location = urls_export.join('/')+'?type='+type+'&date='+date+'&preview=0';
                        },
                        false);
                });
                $.getScript($CFG.remote.base_url+'/js/jQuery-printPage-plugin/jquery.printPage.js').done(function(){
                    $('#btn_preview').click(function(){
                        if(typeof selected_date == 'undefined') {
                            alert('Vui lòng chọn ngày xuất') ;
                            return;
                        }
                        date  = selected_date;
                        type  = $('#export_type').val();
                        $('#btn_preview').printPage({
                            url: urls_export.join('/')+'?type='+type+'&date='+date+'&preview=1',
                            attr: "href",
                            message:"Phiếu nhập kho đang được tạo ..."
                        });
                    });
                });
            }
        });
    },
    showDetailForm: function(storage_id) {
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'detail',
                title:'Chi tiết xuất kho',
                // showButton : false,
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'detail', {storage_id: storage_id}, function(resp){
                        $.storage.angular(element,resp,function(scope){
                        });
                    })
                } 
                
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
    },
    showEditDetail: function (storage_id, name, is_exported) {
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + self.module,
                action: 'editDetail',
                title: 'Chi tiết nhập kho',
                fullScreen: false,
                showButton: false,
                content: function (element) {
                    process('dinhduong/storage/editDetail', {storage_id: storage_id}, function (resp) {
                        $.storage.angular(element, resp.html, function (scope) {
                            scope.storageDetail = resp.detail || {};
                            scope.storageDetail.id = storage_id;
                            scope.storageDetail.name = name;
							scope.storageDetail.is_exported = is_exported;
                        });
                    })
                }

            },
            function (resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
                    $("#tbl_" + self.module).datagrid('reload');
                }
            }
        );
    },
};







