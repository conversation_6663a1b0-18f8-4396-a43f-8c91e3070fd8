(function($, doc){
    $.user_log = {
        module: 'user_log',
        init: function() {
            var self = this;
            var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
            $.dm_datagrid.init(
                urls.join('/'),
                this.module, /*<PERSON><PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
                '', /*Tiêu đề cho bảng dữ liệu*/
                [[
                    { title:'User ID', field:'user_id', width:60, sortable:true },
                    { title:'Username', field:'username', width:150, sortable:true },
                    { title:'Project', field:'project', width:100, sortable:true},
                    { title:'Controller', field:'controller', width:120, sortable:true },
                    { title:'Action', field:'action', width:120, sortable:true },
                    { title:'IP', field:'ip', width:80, sortable:true },
					{ title:'SP', field:'is_super', width:60, sortable:true, align:'center'},
					{ title:'Note', field:'note', width:150, sortable:false },
                    { title:'Log time', field:'log_time', width:120, sortable:true },
                ]]
            );
             $.user_log.initAngular();
        },initAngular: function(){
            var self = this;
            setTimeout(function(){
                angular.element($('body')).scope().$apply(function(scope){
                    $.user_log.scope = scope;
                    scope.Search = function () {
                        var date_start = scope.user.start;
                        var date_end = scope.user.end;
                        var keysearch = scope.user.keysearch;
                        var queryParams = $("#tbl_"+self.module).datagrid('options').queryParams;
    
                        var pattern =/^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/;
                        if(pattern.test(date_start) && pattern.test(date_end))
                        {
                            queryParams.date_start = date_start;
                            queryParams.date_end = date_end;
                        }
                        else
                        {
                            queryParams.date_start = '';
                            queryParams.date_end = '';
                        }
                        queryParams.keysearch = keysearch;
                        $("#tbl_"+self.module).datagrid({'load': queryParams});
                    };
                })
            })

        }
    }
})($, document);