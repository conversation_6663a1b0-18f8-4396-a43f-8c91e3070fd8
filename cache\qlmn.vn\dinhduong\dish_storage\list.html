<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_dish_storage_detail">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="dish_storage"></div>
			<div class="function-kh-ct" style="position: relative;">
				<div class="header-search" id="header-search">
                    <span class="icon-searching glyphicon" ng-show="keysearch_unit || keysearch_name || keysearch_group || keysearch_area || keysearch_category" onclick="$.dish_storage.cleanSearch()">
                        <span class="glyphicon glyphicon-filter hover-pointer" title="Bỏ tìm kiếm" >
                            <span class="glyphicon glyphicon-remove"></span>
                        </span>
                        
                    </span>
                    <input id="name" field="dishes.name" class="form-control" placeholder="Tên món ăn" ng-model="keysearch_name" ng-change="dish_storage.onChangeKeysearchName(keysearch_name)" ng-model-options="{debounce: 1000}">
                    <input style="margin-top: 10px;" id="group_ids" field="dishes.group_ids" placeholder="Chọn độ tuổi" ng-model="keysearch_group">
                    <input style="margin-top: 10px;" id="area_ids" field="dishes.area_ids" placeholder="Chọn vùng miền" ng-model="keysearch_area">
                    <input style="margin-top: 10px;" id="category_ids" field="dishes.category_ids" placeholder="Chọn danh mục món" ng-model="keysearch_category">
                                        <label class="checkbox-inline" style="margin-top: 10px; margin-left: 10px;">
                        <input type="checkbox" id="other_unit" field="other_unit" value="1" placeholder="Món ăn chia sẻ từ các trường" ng-model="keysearch_other_unit" ng-click="dish_storage.onChangeOtherUnit()">Từ các trường
                    </label>
                    
                    <div class="btn-group" style="position: absolute;right: 4px;">
                                                <button type="button" class="btn btn-primary" onclick="$.dish_storage.showShareForm()">
                            <span class="glyphicon glyphicon-copy"></span>Sao chép
                        </button>
                    </div>
                </div>
			</div>
			
		</div>
		
	</div>
	<div id="tbl_dish_storage"></div>
</div>
<script src="http://localhost:3000/js/dinhduong/dish_storage.js?_=1"></script>
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/dish_storage.css?_=1">
<script type="text/javascript">
    $(function(){
        $.dish_storage.init();
    });
	var url = $CFG.remote.base_url +"/doing/dinhduong/dish_storage/";
</script>
<style type="text/css">
    .combo{
        margin-top: 5px;
        margin-left: 3px;
    }
    .btn-timkiem{
        height: 25px;
        margin-top: 5px;
        padding: 1px 7px;
    }
    #name, #unit{
        height: 25px;
        margin-top: 5px;
        border-radius: 0px;
        width: 140px;
        margin-right: 3px;
    }
</style>

