$.balance_money = {
    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */
    	scope.balance_money = {
            fields:[
                { field: 'name', title: '<PERSON><PERSON><PERSON> thực phẩm', width: 200 },
                { field: 'luong1tre_tmp', title: '<PERSON><PERSON>ợng (g)'},
                { field: 'price_kg', type:'number', title: 'Đơn giá (đ/kg)'},
                { field: 'price', title: 'Đơn giá theo ĐVT', type:'number'}
            ], getValue: function(field, row){
                return row[field.field];
            }, formatter: function(field, value, row){
                return value;
            }, data: scope.datagrid.data,
            getStyles: function(field) {
                var styles = {};
                var width = field.width;
                if(width != undefined){
                    width = width + '';
                    if(width.split('%') == 1){
                        width = width + 'px';
                    }
                    styles.width = width;
                }
                return styles;
            }
        };
        scope.balance_moneyShow = function () {
            dialogClose();
            $.dm_datagrid.showAddForm({
                title: '<PERSON><PERSON> đối tiền',
                size: size.wide,
                fullScreen: true,
                scope: scope,
                showButton: false,
                content: $CFG.template.base_url + '/dinhduong/balance/balance_money.html',
                onShown: function () {
                    scope.balance_money.init();
                }, reload: function () {
                    scope.balance_money.init();
                }
            });
        };
        scope.balance_money.selectallfood = false;
        scope.balance.money_min = 5;
        scope.balance.money_minChange = function(){
            if(scope.balance.money_min<1){
                scope.balance.money_min = 1;
            }
        };
        scope.balance_money.apply = function(){
            scope.balance.data = scope.balance_money.data;
            scope.balance.meals = scope.balance_money.meals;
            scope.balance.apply();
            dialogClose();
        };
        scope.balance_money.toZero = function(step) {
            
        };
        scope.balance_money.init = function() {
            scope.balance_money.data = clone(scope.balance.data);
            scope.balance_money.meals = clone(scope.balance.meals);
            scope.balance_money.foods = [];
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id){
                    if (scope.balance.data[meal_key][food_id].exports) {
                        food.exports = clone(scope.balance.data[meal_key][food_id].exports);
                    }
                    food.tiendieuchinh = 0;
                    food.thucmuatheodvt_old = scope.balance.data[meal_key][food_id].thucmuatheodvt;
                    food.money_selected = false;
                    food.foods = [];
                });
            });
            angular.forEach(scope.balance_money.meals, function (meal, meal_define) {
                var meal_key = scope.meal_defines[meal_define];
                angular.forEach(meal.dishes, function (dish, dish_id) {
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        if (!scope.balance_money.data[meal_key][food_id]) {
                            scope.balance_money.data[meal_key][food_id] = clone(food);
                            scope.balance_money.data[meal_key][food_id].foods = [];
                            scope.balance_money.data[meal_key][food_id].luong1tre = 0;
                            scope.balance_money.data[meal_key][food_id].thucmuatheodvt = 0;
                            scope.balance_money.data[meal_key][food_id].thucmua1nhom = 0;
                        }
                        scope.balance_money.data[meal_key][food_id].luong1tre = $['+'](scope.balance_money.data[meal_key][food_id].luong1tre, food.quantity_edit);
                        scope.balance_money.data[meal_key][food_id].foods.push(food);
                    });
                });
            });
            scope.balance_money.selectallfood = false;
            scope.balance_money.totalCalculator();
        };
        /*  Tính lượng khẩu phần (calo) đạt hay chưa */
        scope.balance_money.caloRate = function(){
            var value = 0;
            var calo = round(scope.sumCaloMeals(scope.balance_money.meals), 0);
            var rate = scope.getNormSelected();
            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                value = 1;
            } else {
                if (calo < rate.smallest_rate) {
                    value = 0;
                } else {
                    value = 2;
                }
            }

            return value;
        };
        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */
        scope.balance_money.plgRate = function(){
            var rs = {};
            var tile = scope.getTile_PLG(scope.balance_money.meals);
            var tile_dat = [];
            var tile_chuadat = [];
            var tile_vuotqua = [];
            if(tile.protein >= scope.selected.group.protein_min && tile.protein <= scope.selected.group.protein_max){
                tile_dat.push({define:'protein',name: 'Chất đạm'});
            }else if(tile.protein<scope.selected.group.protein_min) {
                tile_chuadat.push({define:'protein',name: 'Chất đạm'})
            }else{
                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})
            }
            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){
                tile_dat.push({define:'fat',name: 'Chất béo'});
            }else if(tile.fat<scope.selected.group.fat_min) {
                tile_chuadat.push({define:'fat',name: 'Chất béo'})
            }else{
                tile_vuotqua.push({define:'fat',name: 'Chất béo'})
            }
            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){
                tile_dat.push({define:'sugar',name: 'Chất bột'});
            }else if(tile.sugar<scope.selected.group.suga_min) {
                tile_chuadat.push({define:'sugar',name: 'Chất bột'})
            }else{
                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})
            }
            rs = {
                dat: tile_dat,
                chuadat: tile_chuadat,
                vuotqua: tile_vuotqua
            };
            return rs;
        };
        scope.balance_money.plgRateBind = function(){
            var thanhphan = scope.balance_money.plgRate();
            var text = '';
            if(count(thanhphan.dat) == 3){
                text = 'Cân đối';
                class_color = '';
            }else{
                text = 'Chưa cân đối';
                class_color = 'color-red';
            }
            return {text: text, 'class': class_color, thanhphan: thanhphan};
        };
        scope.balance_money.caloRateBind = function(){
            var value = scope.balance_money.caloRate();
            if(value == 1){
                text = 'Đạt';
                class_color = '';
            }else if(value == 0){
                text = 'Chưa đạt';
                class_color = 'color-red';
            }else{
                text = 'Vượt quá định mức';
                class_color = 'btn-color-blue';
            }
            return {text: text, 'class': class_color, value: value};
        };
        scope.balance_money.foodSelectAll = function() {
            scope.balance_money.selectallfood = !scope.balance_money.selectallfood;
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food, food_id){
                    food.money_selected = scope.balance_money.selectallfood;
                })
            });
            scope.balance_money.foodSelectedChange();
        };
        scope.balance_money.foodClickSelected = function(meal_key_select, food){
            var foods = [];
            food.money_selected = !food.money_selected;
            for(var meal_key in scope.balance_money.data){
                fs = scope.balance_money.data[meal_key];
                for(var food_id in fs){
                    var food = fs[food_id];
                    food.tiendieuchinh = 0;
                    food.thucmuatheodvt = scope.balance.data[meal_key][food_id].thucmuatheodvt;
                    food.thanhtien1nhom = scope.datagrid.data[meal_key][food_id].thanhtien1nhom;
                    if(scope.row.meal_selection[meal_key].selected && food.money_selected){
                        foods.push(food);
                    }
                }
            }
            console.log(food.name, foods);
            scope.balance_money.foods = foods;
            scope.balance_money.run(foods);
            scope.balance_money.applyQuantity(foods);
            scope.balance_money.totalCalculator();
        };
        scope.balance_money.isApply = function(){
            var rs = false;
            angular.forEach(scope.balance_money.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    if(food.money_selected && Number(food.tiendieuchinh)!=0){
                        rs = true;
                    }
                });
            });
            return rs;
        };
        scope.balance_money.isNegative = function(){
            var rs = true;
            scope.balance_money.foods || (scope.balance_money.foods = []);
            for (var food of scope.balance_money.foods) {
                if(Number(food.thanhtien1nhom) < 0){
                    rs = false;
                    break;
                }
            }
            return rs;
        };
        scope.balance_money.onChange_tiendieuchinh = function(fd){
            var fds = [];
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    if(food.money_selected && food.food_id != fd.food_id){
                        food.tiendieuchinh = 0;
                        fds.push(food);
                    }
                });
            });
            money = $['-'](scope.balance_money.tienchenhlech1tre , fd.tiendieuchinh);
            scope.balance_money.run(fds, money);
            scope.balance_money.applyQuantity();
            scope.balance_money.totalCalculator();
        };
        scope.balance_money.autoSelectForm = function() {
            $.dm_datagrid.showAddForm({
                title: 'Chọn thưc phẩm muốn cố định',
                size: size.wide,
                fullScreen: true,
                scope: scope,
                showButton: false,
                content: $CFG.template.base_url + '/dinhduong/balance/balance_money_option.html'
            });
        };
        scope.balance_money.autoSelectStart = function() {

        };
        scope.balance_money.applyQuantity = function() {
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if (food.price && food.money_selected) {
                        food.thanhtien1nhom = $['+'](scope.datagrid.data[meal_key][food_id].thanhtien1nhom, food.tiendieuchinh);
                        food.thucmuatheodvt = $['/'](food.thanhtien1nhom, food.price);
                        scope.onChange_thucmuatheodvt(food, true);
                    }
                });
            });
        };
        scope.balance_money.totalCalculator = function(){
            var thanhtien1nhom = 0;
            angular.forEach(scope.balance_money.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if(food.exports) {
                        if (count(food.exports) == 1) {
                            thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);
                        } else {
                            angular.forEach(food.exports, function(fd, food_id_price){
                                thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));
                            });
                        }
                    }else{
                        thanhtien1nhom = $['+'](thanhtien1nhom, food.thanhtien1nhom);
                    }
                });
            });
            var service = scope.getTiendichvu();
            var tongtien_dichvu = $['*'](service, scope.row.sotre);
            var tien_bo_tro = scope.row.tien_bo_tro || 0;
            var surplus_end = 0;
            if (scope.surplus) {
                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {
                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];
                }
            }
            scope.balance_money.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);
            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, tien_bo_tro);
            scope.balance_money.tongtienchenhlech = $['+'](scope.balance_money.tongtienchenhlech, surplus_end);
            console.log('thanhtien1nhom', thanhtien1nhom);
        };
        scope.balance_money.run = function(foods) {
            var money = scope.row.tongtienchenhlech;
            if(foods.length > 0){
                if(scope.balance_money.money_min<=0){
                    scope.balance_money.money_min = 1;
                }
                var tong_tien = 0;
                angular.forEach(foods, function(food, index){
                    tong_tien = $['+'](tong_tien , food.thanhtien1nhom);
                });
                var tong_tien_chia = 0;
                var food_max = {tiendieuchinh:0};
                var da_tru = 0;
                var food_max = {tiendieuchinh:0};
                angular.forEach(foods, function(food, index){
                    var tile = food.thanhtien1nhom/tong_tien;
                    var ext = $['*'](tile , money);
                    var ext_round = scope.round(ext,0);
                    food.tiendieuchinh = ext_round;
                    if (food.tiendieuchinh )
                    da_tru = $['+'](da_tru, ext_round);
                    if(food_max.tiendieuchinh <= food.tiendieuchinh){
                        food_max = food;
                    }
                });
                if(money != da_tru){
                    var con_lai = $['-'](money, da_tru);
                    food_max.tiendieuchinh = $['+'](food_max.tiendieuchinh, con_lai);
                }
            }
        };
    }
};
