(function DiseaseService(app, $CFG) {
    'use strict';

    app.service('DiseaseService', ['$http', 'APP_CONFIG', diseaseService]);

    function diseaseService($http, APP_CONFIG) {
        function getDiseaseOfSchool(change_unit_id = 1, grade_id = 0, course_id = 0) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/diseases?r='+rand()+'&change_unit_id='+change_unit_id + '&grade_id=' + grade_id + '&course_id=' + course_id;

            return $http.get(url)
                .then(function (response) {
                    return _.get(response, 'data', {});
                })
                .catch(function () {
                    return [];
                });
        }

        function updateById(id, props) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/diseases/' + id;

            var data = {
                name: props.name,
                code: props.code,
                grade_ids: props.grade_ids
            };

            statusloading((new Date()).getTime());

            return $http.put(url, data).then(function () {
                return true;
            }).catch(function () {
                return false;
            }).finally(function () {
                statusloadingclose();
            });
        }

        function addNew(props) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/diseases';

            var data = {
                name: props.name,
                code: props.code,
            };

            statusloading((new Date()).getTime());

            return $http.post(url, data, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}}).then(function () {
                return true;
            }).catch(function () {
                return false;
            }).finally(function () {
                statusloadingclose();
            });
        }

        function add(props) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/diseases/add';

            statusloading((new Date()).getTime());

            return $http.post(url, props, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}}).then(function () {
                return true;
            }).catch(function () {
                return false;
            }).finally(function () {
                statusloadingclose();
            });
        }

        function destroyById(id) {
            var url = APP_CONFIG.API.ENDPOINT + '/measures/api/diseases/' + id;

            statusloading((new Date()).getTime());

            return $http.delete(url, {headers: {'Content-Type': 'application/json','X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')}}).then(function () {
                return true;
            }).catch(function () {
                return false;
            }).finally(function () {
                statusloadingclose();
            });
        }

        return {
            getDiseaseOfSchool: getDiseaseOfSchool,
            updateById: updateById,
            addNew: addNew,
            destroyById: destroyById,
            add: add,
        };
    }
})(window.angular_app, window.$CFG);
