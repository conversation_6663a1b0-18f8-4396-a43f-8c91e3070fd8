$.balance = {
    project: 'dinh<PERSON><PERSON>',
    init: function(scope) {    /* Dựng form cân đối lại thực đơn trong danh sách */
        scope.balance = {};
        scope.balance.sophan = {
            ngucoc: {name:'<PERSON><PERSON> cốc', foods:{}},
            traicay: {name:'<PERSON><PERSON><PERSON><PERSON> cây', foods:{}},
            rau: {name:'<PERSON><PERSON>', foods:{}},
            damdv: {name:'<PERSON><PERSON> cốc', foods:{}},
            khac: {name:'<PERSON><PERSON><PERSON><PERSON>', foods:{}}
        };
        scope.balance.templates = {
            show: this.project + '/balance/balance.html',
            change_money: this.project + '/balance/change_money_of_child.html',
            keepBoundMeal: this.project + '/balance/keep_bound_meal.html'
        };
        // scope.loadTemplates(scope.templates, scope);
        scope.balance.selectallfood = false;
        scope.balance.checkautofortype = false;
        scope.balance.keeping_recipes = true;
        scope.balance.for_money = true;
        scope.balance.for_quantity = true;
        scope.balance.for_quanlity = true;
        scope.balance.init = function() {
            scope.balance.data = clone(scope.datagrid.data);
            scope.balance.meals = clone(scope.selected.meals);
            scope.balance.build_data();
            scope.balance.totalCalculator();
        };
        scope.balance.build_data = function() {
            var data = {};
            angular.forEach(scope.balance.data, function (foods, meal_key) {
                data[meal_key] = {};
                angular.forEach(foods, function (food, food_id) {
                    food.foods = [];
                    data[meal_key][food_id] = food;
                });
            });
            angular.forEach(scope.balance.meals, function (meal, meal_define) {
                var meal_key = scope.meal_defines[meal_define];
                angular.forEach(meal.dishes, function (dish, dish_id) {
                    angular.forEach(dish.ingredient, function (food, food_id) {
                        data[meal_key] || (data[meal_key] = clone(food));
                        if (data[meal_key] == undefined) {
                            delete dish.ingredient[food_id];
                        } else if (data[meal_key][food_id] == undefined) {
                            delete dish.ingredient[food_id];
                        }else{
                            data[meal_key][food_id].foods.push(food);
                        }
                    });
                });
            });
            // angular.forEach(data, function (foods, wh_id) {
            //     angular.forEach(foods, function (food, food_id) {
            //         scope.onChange_luong1tre(food, true);
            //     });
            // });
            scope.balance.data = data;
        };
        scope.balance.getFoodSelected = function(){
            var rs = {};
            scope.balance.nutritions || (scope.balance.nutritions = {});
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food,food_id) {
                    if(!scope.balance.nutritions[food_id]) {
                        scope.balance.nutritions[food_id] = food.nutritions;
                    }else{
                        food.nutritions = scope.balance.nutritions[food_id];
                    }
                    if(food.selected){
                        rs[meal_key] || (rs[meal_key] = {});
                        rs[meal_key][food_id] || (rs[meal_key][food_id] = 0);
                        rs[meal_key][food_id] += food.luong1tre;
                    }
                });
            });
            return rs;
        };
        scope.balance.getData = function(meals, is_meals) {
            /*Đoạn này viết để lấy cấu trúc thực đơn*/
            scope.balance.nutritions = {};
            scope.menu_adjust || (scope.menu_adjust = {});
            var date = scope.menu_adjust.date;
            var keep_foods = scope.balance.getFoodSelected(); /* Danh sách thực phẩm cố định lượng*/
            var nutritions = scope.balance.nutritions;
            var tmps = {};
            var tmp_foods = {};
            var total_calo = 0;
            meals || (meals = scope.selected.meals);
            angular.forEach(meals, function(meal, meal_define) {
                tmps[meal_define] = [];
                if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;
                tmp_foods[meal.warehouse_id] || (tmp_foods[meal.warehouse_id]= {});
                var meal_key = 2;
                if (meal_define == 'buasang') {
                    meal_key = 1;
                } else if (meal_define == 'buatoi') {
                    meal_key = 3;
                }
                angular.forEach(meal.dishes, function(dish, dish_id) {
                    var tmp_meals = [];
                    angular.forEach(dish.ingredient, function(food, food_id) {
                        if(!nutritions[food_id]) {
                            nutritions[food_id] = food.nutritions;
                        }else{
                            food.nutritions = nutritions[food_id];
                        }
                        food.gam_exchange || (food.gam_exchange = 1000);
                        var f = {name: food_id};
                        if(food.extrude_factor){
                            food.extrude_factor = Number(food.extrude_factor);
                            if(food.extrude_factor >= 100) {
                                food.extrude_factor = 50;
                            }
                        }
                        f.quantity = Number(food.quantity_edit);
                        if(f.quantity < 0) {
                            f.quantity = 0;
                        }
                        if(food.price_kg) {
                            f.price = food.price_kg;
                        }else{
                            f.price = scope.round($['/']($['*'](food.price, 1000), food.gam_exchange));
                        }
                        if(!f.price){
                            f.price = 0;
                        }
                        f.protein = $['*'](food.nutritions.protein * 10 , scope.co_cau(null, date).protein);
                        f.fat = $['*'](food.nutritions.fat * 10 , scope.co_cau(null, date).fat);
                        f.sugar = $['*'](food.nutritions.sugar * 10 , scope.co_cau(null, date).sugar);
                        f.PLG = $['+']($['+'](f.protein , f.fat) , f.sugar);    /*(Kcal)*/
                        total_calo += (
                                f.quantity * food.nutritions.protein / 100 * scope.co_cau(null, date).protein +
                                f.quantity * food.nutritions.fat / 100 * scope.co_cau(null, date).fat +
                                f.quantity * food.nutritions.sugar / 100 * scope.co_cau(null, date).sugar
                            );
                        if(food.extrude_factor > 0){
                            f.price = (100*f.price)/(100 - food.extrude_factor);
                            var gam = (100*100)/(100 - food.extrude_factor);
                        }
                        if(in_array_key(food_id, keep_foods[meal_key])) {
                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){
                                if(food_id+'' == f_id+''){
                                    f['i_' + meal_key + '_' + f_id] = 1.0;
                                }else{
                                    f['i_' + meal_key + '_' + f_id] = 0.0;
                                }
                            });
                        }else{
                            angular.forEach(keep_foods[meal_key], function(quantity, f_id){
                                f['i_' + meal_key + '_' + f_id] = 0.0;
                            });
                        }
                        tmp_meals.push(f);
                        // if(!tmp_foods[meal_key][food_id]) {
                        //     tmp_foods[meal_key][food_id] = {
                        //         id: food_id,
                        //         name: food.name,
                        //         extrude_factor: food.extrude_factor,
                        //         gam_exchange: food.gam_exchange,
                        //         quantity: 0,
                        //         price: food.price,
                        //         quantity_real: 0
                        //     }
                        // }
                        // tmp_foods[meal_key][food_id].quantity = $['+'](tmp_foods[meal_key][food_id].quantity,food.quantity_edit);
                        // tmp_foods[meal_key][food_id].quantity_real = $['+'](tmp_foods[meal_key][food_id].quantity_real,f.quantity);
                    });
                    dish.lower_bound = Number(dish.lower_bound);
                    dish.upper_bound = Number(dish.upper_bound);
                    if(!dish.lower_bound){
                        dish.lower_bound = 5
                    }
                    if(!dish.upper_bound){
                        dish.upper_bound = 500
                    }
                    if(dish.upper_bound < dish.lower_bound){
                        dish.upper_bound = dish.lower_bound;
                    }
                    var tmp_disk = {
                        dish: dish_id,
                        lower_bound: dish.lower_bound/1000,
                        upper_bound: dish.upper_bound/1000,
                        ingredients: tmp_meals
                    };
                    if(count(tmp_meals)==0) {
                        tmp_disk.lower_bound = 0;
                    }
                    tmps[meal_define].push(tmp_disk);
                });
            });
            scope.menu_adjust || (scope.menu_adjust={});
            var date = scope.menu_adjust.date;
            var group_id = scope.row.group_id;
            var group_selected = scope.getGroupSelected();
            var nutritions = scope.selected.group.nutritions;
            var protein = (nutritions.animal_protein + nutritions.vegetable_protein) * scope.co_cau(null, date).protein;
            var fat = (nutritions.animal_fat + nutritions.vegetable_fat) * scope.co_cau(null, date).fat;
            var sugar = nutritions.sugar * scope.co_cau(null, date).sugar;
            var calo = protein+fat+sugar;
            var asym = 0.5;
            /*Kiểm tra và tính thêm tỉ lệ bữa sáng nếu có*/
            var tmp_meal = {
                buasang_min: $['/']($['*'](group_selected.meals.buasang.min + asym, nutritions.calo) , 100),
                buasang_max: $['/']($['*'](group_selected.meals.buasang.max - asym , nutritions.calo) , 100),
                buatrua_min: $['/']($['*'](group_selected.meals.buatrua.min + asym , nutritions.calo) , 100),
                buatrua_max: $['/']($['*'](group_selected.meals.buatrua.max - asym , nutritions.calo) , 100),
                buaxe_min: $['/']($['*'](group_selected.meals.buaxe.min + asym , nutritions.calo) , 100),
                buaxe_max: $['/']($['*'](group_selected.meals.buaxe.max - asym , nutritions.calo) , 100),
                buaphu_min: $['/']($['*'](group_selected.meals.buaphu.min + asym , nutritions.calo) , 100),
                buaphu_max: $['/']($['*'](group_selected.meals.buaphu.max - asym , nutritions.calo) , 100)
            };
            asym = 0.5;
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = 0;
            var biggest_rate = 0;
            if(scope.dinhduong_calc[1]){
                smallest_rate += buasang_norm.smallest_rate;
                biggest_rate += buasang_norm.biggest_rate;
            }
            if(scope.dinhduong_calc[2]){
                smallest_rate += scope.selected.group.smallest_rate;
                biggest_rate += scope.selected.group.biggest_rate;
            }
            var tmp_norm = {
                protein_min: $['/']($['*'](protein , smallest_rate + asym) , 100),
                fat_min: $['/']($['*'](fat , smallest_rate + asym) , 100),
                sugar_min: $['/']($['*'](sugar , smallest_rate + asym) , 100),
                protein_max: $['/']($['*'](protein , biggest_rate - asym) , 100),
                fat_max: $['/']($['*'](fat , biggest_rate - asym) , 100),
                sugar_max: $['/']($['*'](sugar , biggest_rate - asym) , 100),
                calo_min: 0,
                calo_max: 0
            };
            if(scope.dinhduong_calc[1]){
                tmp_norm.calo_min += nutritions.calo_rate_morning_low;
                tmp_norm.calo_max += nutritions.calo_rate_morning;
            }
            if(scope.dinhduong_calc[2]){
                tmp_norm.calo_min += nutritions.calo_rate_low;
                tmp_norm.calo_max += nutritions.calo_rate;
            }

            var tmp_quantity = {
                protein_min: (scope.selected.group.protein_min + asym)/100,
                fat_min: (scope.selected.group.fat_min + asym)/100,
                sugar_min: (scope.selected.group.sugar_min + asym)/100,
                protein_max: (scope.selected.group.protein_max - asym)/100,
                fat_max: (scope.selected.group.fat_max - asym)/100,
                sugar_max: (scope.selected.group.sugar_max - asym)/100
            };
            var service_price = scope.getTiendichvu();
            var data = {
                keeping_recipes: (scope.balance.keeping_recipes?1:0),
                menu_price: scope.row.tien1tre - service_price,
                input_menu: tmps,
                constraints: []
            };
            if(scope.balance.for_money) {}
            if(scope.balance.for_quantity) {
                data.constraints.push({"name": "PLG", "lower_bound": tmp_norm.calo_min+1, "upper_bound": tmp_norm.calo_max-1, "type": "nominal", denominator: "","meal":"all"});
            }
            if(scope.balance.for_quanlity) {
                data.constraints.push({name: 'protein', lower_bound: tmp_quantity.protein_min, upper_bound: tmp_quantity.protein_max,type:'percentage',denominator: "PLG",meal:"all"});
                data.constraints.push({name: 'fat', lower_bound: tmp_quantity.fat_min, upper_bound: tmp_quantity.fat_max,type:'percentage',denominator: "PLG",meal:"all"});
                data.constraints.push({name: 'sugar', lower_bound: tmp_quantity.sugar_min, upper_bound: tmp_quantity.sugar_max,type:'percentage',denominator: "PLG",meal:"all"});
            }

            if(is_meals){
                angular.forEach(tmps, function(meal, meal_define){
                    var kt = false;
                    for(var i in tmps[meal_define]) {
                        if(count(tmps[meal_define][i].ingredients)>0){
                            kt = true;
                            break;
                        }
                    }
                    if(kt){
                        data.constraints.push({
                            name: 'PLG', 
                            lower_bound: tmp_meal[meal_define+'_min'], 
                            upper_bound: tmp_meal[meal_define+'_max'], 
                            type:'nominal', 
                            denominator: "", 
                            meal: meal_define 
                        });
                    }
                });
            }
            /*  Thêm điều kiện cố định thực phẩm    */
            if(count(keep_foods)>0) {
                angular.forEach(keep_foods, function(foods, meal_key){
                    angular.forEach(foods, function(quantity, food_id){
                        data.constraints.push({
                            name: 'i_' + meal_key + '_' + food_id,
                            lower_bound: quantity/1000,
                            upper_bound: quantity/1000,
                            type: "nominal",
                            denominator: "",
                            meal: "all"
                        });
                    });
                });
            }
            return data;
        };
        scope.balance.alertData = function(meals){
            var data = scope.balance.getData(meals,true);
            var rand = (Math.random()+'').split('.')[1];
            scope.balance.data_tmp = data;
            var html = '<div><div>{{balance.data_tmp}}</div></div>';
            $.dm_datagrid.show({
                title: 'Menu input for processing',
                message: '<div id="' + rand + '" style="padding: 15px; min-height: 350px; overflow-x: auto;"></div>'
            }, function() {
                setTimeout(function() {
                    scope.$apply( function() {
                        $('#'+rand).html(scope.compile(html));
                    });
                });
            });
        };
        scope.balance.alertEnd = function(resp, msgs) {
            msgs || (msgs = []);
            var status = resp.status;
            switch (status){
                case 0:
                    break;
                case 1:
                    break;
                case 2:
                    msgs.push('Tiền ăn quá thấp. Đã giảm lượng thực phẩm tối đa để tiền thiếu ít nhất');
                    break;
                case 3:
                    msgs.push('Tiền ăn quá cao. Đã tăng lượng thực phẩm tối đa để tiền thừa ít nhất.');
                    break;
                case 4:
                    msgs.push('Công thức (tỉ lệ thực phẩm) bị thay đổi, dẫn đến món ăn có thể không nấu được.');
                    break;
                default:
                    msgs.push('Hệ thống không thể cân đối.');
                    msgs.push('Hãy kiểm tra và chọn lại thực đơn đầu vào.');
                    msgs.push('Hoặc lựa chọn tiện ích khác nếu có.');
                    break;
            }
            /*Kiểm tra có thực phẩm nào giá bằng 0 để đưa thông báo*/
            var tmp_foods = {};
            angular.forEach(scope.datagrid.data, function(foods, warehouse_id){
                angular.forEach(foods,function(food,food_id){
                    if(food.price == 0){
                        tmp_foods[food_id] = food;
                    }
                })
            });
            var foods = [];
            angular.forEach(tmp_foods, function(food,food_id){
                foods.push(food.name);
            });
            if(foods.length) {
                msgs.push('Một số thực phẩm chưa có đơn giá hãy kiểm tra lại để đảm bảo việc cân đối được chính xác : '+foods.join(', '));
            }
            if(count(msgs)>0) {
                $.dm_datagrid.show({
                    size: size.small,
                    title: 'Thông báo',
                    message: '<div style="padding:15px;min-height:250px;overflow-x: auto"> - '+msgs.join('<br/> - ')+'</div>'
                });
            }
        };
        scope.balance.processAction = function(meals) {
            meals || (meals = scope.balance.meals);
            var tien1tre;
            var canh_bao = (scope.row.meal_selection[1].selected != scope.row.meal_selection[2].selected);
            if (canh_bao) {
                angular.forEach(scope.row.meal_selection, function (meal, meal_key) {
                    if (!meal.selected && meal.visible) {
                        if (count(scope.balance.data[meal_key]) == 0) {
                            canh_bao = false;
                        }
                    }
                });
            }
            if (canh_bao) {
                scope.balance.meals = meals;
                $.dm_datagrid.showAddForm({
                    module: $CFG.project + '/' + self.module,
                    title: 'Tiền ăn 1 trẻ',
                    draggable: true,
                    fullScreen: false,
                    showButton: false,
                    scope: scope,
                    content: scope.balance.templates.change_money,
                });
            }else{
                scope.balance.send(meals);
            }
        };
        scope.balance.send = function(meals, tien1tre) {
            meals || (meals = scope.balance.meals);
            var data = scope.balance.getData(meals,true);
            data.input_menu = JSON.stringify(data.input_menu);
            data.constraints = JSON.stringify(data.constraints);
            data.async = true;
            if (tien1tre) {
                data.menu_price = tien1tre;
            }
            var url = $CFG.balance_api;
            var check_post_ok = false;
            process(url, data, function(resp) {
                check_post_ok = true;
                if(in_array(resp.status, [1])) {
                    scope.balance.process(resp);
                    var msgs = [];
                    msgs.push('Thực đơn đã được cân đối.');
                    scope.balance.alertEnd(resp, msgs);
                } else {
                    data = scope.balance.getData(meals);
                    data.input_menu = JSON.stringify(data.input_menu);
                    data.constraints = JSON.stringify(data.constraints);
                    if (tien1tre) {
                        data.menu_price = tien1tre;
                    }
                    data.async = true;
                    process(url, data, function(resp) {
                        var msgs = [];
                        if(in_array(resp.status, [1,2,3])){
                            if(in_array(resp.status, [1,2,3])){
                                msgs.push('Thực đơn đã được cân đối..');
                            }
                            scope.balance.process(resp);
                            console.log('calo theo bữa: not ok.');
                        }else{
                            msgs.push('thực đơn không cân đối được.');
                        }
                        scope.balance.alertEnd(resp, msgs);
                    },null,false);
                }
            }, null, false);
            setTimeout(function () {
                if(check_post_ok == false){
                    var msgs = [];
                    resp = {status: -1};
                    scope.balance.alertEnd(resp, msgs);
                }
            }, 3000)
        };
        scope.balance.process = function(resp) {
            setTimeout(function () {
                scope.$apply(function () {
                    var keep_foods = scope.balance.getFoodSelected();
                    var tmp_fds = {};
                    var meals = resp.output_menu;
                    var status = resp.status;
                    var root_meals = scope.balance.meals;
                    angular.forEach(meals, function(meal, meal_define) {
                        if (count(meal) > 0 ) {
                            if ( !scope.row.meal_selection[scope.meal_defines[meal_define]].selected ) return;
                            var meal_key = 2;
                            if (meal_define == 'buasang') {
                                meal_key = 1;
                            } else if (meal_define == 'buatoi') {
                                meal_key = 3;
                            }
                            angular.forEach(meal, function(dish, ind) {
                                angular.forEach(dish.ingredients, function (food, ind1) {
                                    if (keep_foods[meal_key]) {
                                        if (keep_foods[meal_key][food.food_id]) {
                                            return;
                                        }
                                    }
                                    if (root_meals[meal_define].dishes[dish.dish].ingredient[food.name]) {
                                        root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity = food.quantity * 1000;
                                        root_meals[meal_define].dishes[dish.dish].ingredient[food.name].quantity_edit = food.quantity * 1000;
                                    }
                                });
                            });
                        }
                    });
                    scope.balance.build_data();
                    angular.forEach(scope.balance.data, function(foods, meal_key) {
                        angular.forEach(foods, function (food, food_id) {
                            food.foods = [];
                            if (keep_foods[meal_key]) {
                                if (keep_foods[meal_key][food.food_id]) {
                                    return;
                                }
                            }
                            food.luong1tre = 0;
                        });
                    });
                    angular.forEach(root_meals, function (meal, meal_define) {
                        var meal_key = 2;
                        if (meal_define == 'buasang') {
                            meal_key = 1;
                        } else if (meal_define == 'buatoi') {
                            meal_key = 3;
                        }
                        angular.forEach(meal.dishes, function(dish, ind) {
                            angular.forEach(dish.ingredient, function (food, food_id) {
                                scope.balance.data[scope.meal_defines[meal.define]][food_id].foods.push(food);
                                if (keep_foods[meal_key]) {
                                    if (keep_foods[meal_key][food.food_id]) {
                                        return;
                                    }
                                }
                                scope.balance.data[scope.meal_defines[meal.define]][food_id].luong1tre += food.quantity_edit;
                            });
                        });
                    });
                    angular.forEach(scope.balance.data, function(foods, meal_key) {
                        angular.forEach(foods, function (food, food_id) {
                            if (keep_foods[meal_key]) {
                                if (keep_foods[meal_key][food.food_id]) {
                                    return;
                                }
                            }
                            scope.onChange_luong1tre(food, true);
                        });
                    });
                    scope.balance.totalCalculator();
                });
            });
        };
        scope.balance.roundThucmuatheo_dvt = function(food,status){
            var num = 0;
            var price = food.price+'';
            if(status==1) {
                if(food.thucmuatheodvt <= 0.00005){
                    food.thucmuatheodvt = 0.0001;
                }
                if(price.split('.').length==0){
                    num = 0;
                }else{
                    price += 'aa';
                    if(price.split('0000aa').length==2){
                        num = 4;
                    }else if(price.split('000aa').length==2){
                        if(food.thucmuatheodvt<=0.0005){
                            food.thucmuatheodvt = 0.001;
                        }
                        num = 3;
                    }else if(price.split('00aa').length==2){
                        if(food.thucmuatheodvt<=0.005){
                            food.thucmuatheodvt = 0.01;
                        }
                        num = 2;
                    }else if(price.split('0aa').length==2){
                        if(food.thucmuatheodvt<=0.05){
                            food.thucmuatheodvt = 0.1;
                        }
                        num = 1;
                    }
                    if(num==0 && food.thucmuatheodvt<0.5){
                    }else{
                        food.thucmuatheodvt = round(food.thucmuatheodvt, num);
                    }
                }
            } else {
                food.thucmuatheodvt = scope.round_thucmuatheodvt(food.thucmuatheodvt, 2);
                if((price.replace('0aa')+'aa').split('0aa').length==1 && food.thucmuatheodvt > 0.1){
                    food.thucmuatheodvt = round(food.thucmuatheodvt, 1);
                }else if(price.split('0aa').length==1 && food.thucmuatheodvt > 1){
                }
            }
            return food;
        };

        scope.balance.apply = function() {
            var foods = {};
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id){
                    scope.datagrid.data[meal_key][food_id] = food;
                    scope.onChange_thucmuatheodvt(food, true);
                    if (food.luong1tre == 0) {
                        food.luong1tre = 0.05;
                        scope.onChange_luong1tre(food, true);
                    }
                });
            });
            angular.forEach(scope.balance.meals, function (meal, meal_define) {
                scope.selected.meals[meal_define] = meal;
                angular.forEach(meal.dishes, function (dish, ind) {
                    angular.forEach(dish.ingredient, function (fd, ind1) {
                        fd.quantity = round(fd.quantity, 3);
                    });
                });
            });
            scope.totalCalculator();
            scope.balance.init();
        };
        scope.balance.toZeroMoney = function(){
            var chenh = round(scope.balance.getTongchenh(),1);
            if(Math.abs(chenh) == 0 || Math.abs(chenh) > 4000) { 
                return;
            }
            /*Tìm kiếm thực phẩm giá cao nhất mà có số 0 đăng sau lớn hơn hoặc bằng 3*/
            var foods = {};
            angular.forEach(scope.datagrid.data,function(fds, warehouse_id){
                angular.forEach(fds,function(fd,food_id){
                    var price = fd.price+'aa';
                    if(fd.price > 0 && price.split('0000aa').length==2 || price.split('000aa').length==2){
                        foods[food_id] = fd;
                    }
                })
            });
            var fds = [];
            angular.forEach(foods,function(food,food_id){
                fds.push(food);
            });
            if(fds.length>1){
                /*Sắp xếp giảm dần theo số lượng thực phẩm*/
                for(var i=0; i<fds.length-1; i++) {
                    for(var j=i+1; j<fds.length; j++){
                        if(fds[i].thucmuatheodvt_balance<fds[j].thucmuatheodvt_balance){
                            var tmp = fds[i];
                            fds[i] = fds[j];
                            fds[j] = tmp;
                        }
                    }
                }
                /*Lấy 1 nửa ds thực phẩm thôi*/
                foods = [];
                for(var i=0; i<fds.length/2; i++){
                    foods.push(fds[i]);
                }
                /*sắp xếp ds giảm dần theo giá*/
                for(var i=0; i<foods.length-1; i++) {
                    for(var j=i+1; j<foods.length; j++){
                        
                        if(foods[i].price<foods[j].price){
                            var tmp = foods[i];
                            foods[i] = foods[j];
                            foods[j] = tmp;
                        }
                    }
                }
            }
            if(foods[0]){
                scope.balance.addThucmuaForFood(foods[0],chenh);
                scope.balance.totalCalculator(); 
                var thucmua_arr = foods[0].thucmuatheodvt_balance+''.split('.');
                if(thucmua_arr.length == 2){
                    if(thucmua_arr[1].length>4){}
                }
                chenh = round(scope.balance.getTongchenh(),1);
            }
        };
        scope.balance.addThucmuaForFood = function(food,chenh) {
            var thucmua_chenh = $['/'](chenh,food.price);
            food.thucmuatheodvt_balance = Math.abs($['+'](food.thucmuatheodvt_balance,thucmua_chenh));
        };
        scope.balance.addThucmuaForFood1 = function(food,chenh) {
            for(var i=1; i<=5; i++){
                var per = i*2;
                var thucmua_chenh = $['/']($['/'](chenh,food.price),per);
                food.thucmuatheodvt_balance = $['+'](food.thucmuatheodvt_balance,thucmua_chenh);
                scope.balance.totalCalculator();
                chenh = scope.balance.getTongchenh();
            }
        };
        scope.balance.formKeepBound_meal = function(module,meals){
            angular.forEach(meals, function(meal,meal_define){
                angular.forEach(meal.dishes, function(dish, dish_id){
                    if(!dish.lower_bound){
                        dish.lower_bound = 5;
                    }
                    if(!dish.upper_bound){
                        dish.upper_bound = 500;
                    }
                });
            });
            $.dm_datagrid.showAddForm(
                {
                    module: $CFG.project+'/'+module,
                    action:'balance',
                    title:'Cân đối',
                    size: 800,
                    fullScreen: false,
                    showButton: false,
                    content: function(element){
                        $(element).html(scope.getTemplate(scope.balance.templates.keepBoundMeal));
                    }
                },
                function(resp){
                    
                }
            );
        };
        scope.balance.show = function(){
            scope.balance.init();
            var nutritions = scope.selected.group.nutritions;
            if(nutritions+'' === 'null') {
                var msg = ['Không thể cân đối vì chưa cấu hình định mức dinh dưỡng cho nhóm trẻ đang chọn.'];
                msg.push('<a href="'+$CFG.remote.base_url+'/single/'+$CFG.project+'/norm">Vào cấu hình định mức</a>');
                $.dm_datagrid.show({
                    title: 'Thông báo',
                    message: '<div style="padding:15px;height:100px;">'+msg.join('<br/>')+'</div>'
                });
                return;
            }
            $.dm_datagrid.showAddForm(
                {
                    action: 'balance',
                    title: 'Cân đối dinh dưỡng tự động',
                    size: size.wide,
                    scope: scope,
                    fullScreen: true,
                    showButton: false,
                    content: scope.balance.templates.show
                }
            );
        };
        scope.balance.runAuto = function(){
            showSpinner('balance_auto');
            /*Kiểm tra nếu có số phần nào đó bằng 0 thì chạy theo lượng như bản cũ*/
            /*ngược lại cân đối theo số phần*/
            var sophan = scope.balance.getCaloFor_SoPhan();
            var kt = true;
            for(var i in sophan){
                if(sophan[i] == 0){
                    kt = false;
                    break;
                }
            }
            if(kt && false){
                scope.balance.runSophan();
            }else{
                scope.balance.runQuantity();
                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                    angular.forEach(kho, function(food,food_id){
                        if(food.luong1tre_balance+'' == 'Infinity'){
                            food.luong1tre_balance = food.luong1tre;
                        }else if(food.luong1tre_balance == 0){
                            food.luong1tre_balance == 0.01;
                        }
                        scope.balance.onChange_luong1tre(food);
                    })
                })
            }
            hiddenSpinner('balance_auto');
        }
        /*Tổng hợp lượng theo số phần*/
        scope.balance.getCaloFor_SoPhan = function(){
            var ngucoc = 0;
            var traicay = 0;
            var rau = 0;
            var damdv = 0;
            scope.menu_adjust || (scope.menu_adjust={});
            var date = scope.menu_adjust.date;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,index){
                    if(warehouse_id == 2){
                        var calo = $['*'](scope.co_cau(null, date).protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                            + $['*'](scope.co_cau(null, date).fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                            + $['*'](scope.co_cau(null, date).sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)

                        if(food.ngucoc){
                            ngucoc = $['+'](ngucoc,calo);
                        }else if(food.traicay){
                            traicay = $['+'](traicay,calo);
                        }else if(food.rau){
                            rau = $['+'](rau,calo);
                        }else if(food.damdv){
                            damdv = $['+'](damdv,calo);
                        }
                    }
                });
            });
            return {ngucoc: ngucoc, traicay: traicay, rau: rau, damdv: damdv};
        };

        scope.balance.getFoodsFor_SoPhan = function(){
            scope.balance.sophan = {
                ngucoc: {name:'Ngũ cốc',foods:{}},
                traicay: {name:'Trái cây',foods:{}},
                rau: {name:'Rau',foods:{}},
                damdv: {name:'Ngũ cốc',foods:{}},
                khac: {name:'Khác',foods:{}}
            };
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,food_id){
                    if(scope.selected.balance_warehouse[warehouse_id]) {
                        if(!food.selected) {
                            if(food.ngucoc){
                                scope.balance.sophan.ngucoc.foods[food_id] = food;
                            }else if(food.traicay){
                                scope.balance.sophan.traicay.foods[food_id] = food;
                            }else if(food.rau){
                                scope.balance.sophan.rau.foods[food_id] = food;
                            }else if(food.damdv){
                                scope.balance.sophan.damdv.foods[food_id] = food;
                            }else{
                                scope.balance.sophan.khac.foods[food_id] = food;
                            }
                        }
                    }
                });
            });
        };

        scope.balance.onChange_luong1tre = function(food) {
            scope.onChange_luong1tre(food, true);
            scope.balance.totalCalculator();
        };
        scope.balance.onChange_thucmuatheodvt = function(food){
            scope.onChange_thucmuatheodvt(food, true);
            scope.balance.totalCalculator();
        };
        /*Tổng tiền chênh lệch 1 tre*/
        scope.balance.getTiendicho1tre = function() {
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){
                angular.forEach(foods, function(food,food_id){
                    rs = $['+'](rs,food.thanhtien1nhom_balance);
                });
            });
            return scope.round($['/'](rs,scope.row.sotre));
        };
        /*Tổng tiền chênh lệch 1 tre*/
        scope.balance.getTiendicho_all = function() {
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(foods,warehouse_id){
                angular.forEach(foods, function(food,food_id){
                    var thanhtien = $['*'](food.thucmuatheodvt_balance,food.price);
                    rs = $['+'](rs,thanhtien);
                });
            });
            return rs;
        };
        scope.balance.getTongchenh = function(){
            var service_all = $['*'](scope.getTiendichvu(),scope.row.sotre);
            var tienan_all = $['*'](scope.row.tien1tre,scope.row.sotre);
            var tiendicho_all = scope.balance.getTiendicho_all();
            return $['-']($['-'](tienan_all, tiendicho_all), service_all);
        };
        scope.balance.getTienchenhlech = function(){
            var tiendicho1tre = scope.balance.getTiendicho1tre();
            var service = scope.getTiendichvu();
            return scope.round($['-']($['-'](scope.row.tien1tre, tiendicho1tre), service));
        };
        scope.balance.totalCalculator = function(){
            var thanhtien1nhom = 0;
            angular.forEach(scope.balance.data, function(foods, meal_key){
                angular.forEach(foods, function(food, food_id) {
                    if(food.exports) {
                        angular.forEach(food.exports, function(fd, food_id_price){
                            thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](fd.quantity, fd.price));
                        });
                    }else{
                        thanhtien1nhom = $['+'](thanhtien1nhom, $['*'](food.thucmuatheodvt, food.price));
                    }
                });
            });
            var service = scope.getTiendichvu();
            var tongtien_dichvu = $['*'](service, scope.row.sotre);
            var tien_bo_tro = scope.row.tien_bo_tro || 0;
            var surplus_end = 0;
            if (scope.surplus) {
                if (scope.surplus.end && scope.configs.calc_chenh_lech_dau_ngay) {
                    surplus_end = scope.surplus.end[scope.selected.group_adjust.group_id];
                }
            }
            scope.balance.tongtienchenhlech = $['-']($['-']($['*'](scope.row.tien1tre, scope.row.sotre), thanhtien1nhom), tongtien_dichvu);
            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, tien_bo_tro);
            scope.balance.tongtienchenhlech = $['+'](scope.balance.tongtienchenhlech, surplus_end);
        };
        scope.balance.isApply = function(){
            for(var meal_key in scope.balance.data){
                for(var food_id in scope.balance.data[meal_key]) {
                    if(scope.balance.data[meal_key][food_id].luong1tre != scope.datagrid.data[meal_key][food_id].luong1tre){
                        return false;
                    }
                }
            }
            return true;
        };
        
        /*Tự động cân dối về lượng*/
        scope.balance.runQuantity = function(){
            var buasang_norm = scope.getNormBuasang();
            var max_i = (scope.selected.group.biggest_rate+buasang_norm.biggest_rate - scope.selected.group.smallest_rate-buasang_norm.smallest_rate)/2;
            var max_protein = (scope.selected.group.protein_max - scope.selected.group.protein_min)/2
            var max_fat = (scope.selected.group.fat_max - scope.selected.group.fat_min)/2
            var max_sugar = (scope.selected.group.sugar_max - scope.selected.group.sugar_min)/2
            if(max_i>20){
                max_i = 20;
            }
            if(scope.balance.caloRate()==1 && count(scope.balance.plgRate().dat) == 3){
                return true;
            }
            for(var test=0; test<=10;test++) {
                for(var j=0; j<=max_i;j++) {
                    for(var i=0; i<=max_i; i++){
                        for(var k=0;k<=max_i;k++){
                            if(!scope.balance.whileNotPassQuantity(i,j,k)) {
                                                                
                            }/*else if(count(scope.balance.plgRate().dat) == 3){
                                return true;
                            }*/
                        }
                    }
                }
                if(count(scope.balance.plgRate().dat) != 3 && scope.balance.caloRate()==1) {
                    for(var i=0; i<=max_protein;i++) {
                        for(var j=0; j<=max_fat; j++){
                            for(var k=0;k<=max_sugar;k++){
                                if(!scope.balance.whileNotPassPLG(i,j,k)) {
                                    
                                }
                            }
                        }
                    }
                }
            }
        };
        scope.balance.getTile_PLG_food = function(food){

        };
        
        scope.balance.onChangeThucmua1tretheodvt = function(item){
            var thucmua1tretheodvt = Number(item.thucmua1tretheodvt);
            var luong1tretheodvt = thucmua1tretheodvt;
            if(item.extrude_factor){
                luong1tretheodvt = $['/'](thucmua1tretheodvt,(1+1/(100/item.extrude_factor - 1)));
            }
            item.luong1tre_balance = $['*'](luong1tretheodvt,item.gam_exchange);
        };

        scope.balance.whileNotPassPLG = function(i,j,k) {
            var check = true;
            var plg = scope.balance.getTile_PLG();
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;
            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;
            if(plg.protein<scope.selected.group.protein_min || plg.protein>scope.selected.group.protein_max){
                var tile_canbang = (scope.selected.group.protein_min + scope.selected.group.protein_max)/2;
                var tile_them = 0;
                if(plg.protein<scope.selected.group.protein_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = scope.selected.group.protein_min-plg.protein + (tile_canbang-scope.selected.group.protein_min)/2 + i;
                }else{
                    tile_them = -1*(plg.protein-scope.selected.group.protein_max + (tile_canbang-scope.selected.group.protein_min)/2)-i;
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.proteinChange(quantity_need);
                check = false;
            }
            plg = scope.balance.getTile_PLG();
            if(plg.fat<scope.selected.group.fat_min || plg.fat>scope.selected.group.fat_max){
                var tile_canbang = (scope.selected.group.fat_max + scope.selected.group.fat_min)/2;
                var tile_them = 0;
                if(plg.fat<scope.selected.group.fat_min){ /*Đạm thấp hơn nên cần bổ sung thê*/
                    tile_them = scope.selected.group.fat_min-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/
                }else{
                    tile_them = -1*(plg.fat-scope.selected.group.fat_max)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.fatChange(quantity_need);
                check = false;
            }
            plg = scope.balance.getTile_PLG();
            if(plg.sugar<scope.selected.group.sugar_min || plg.sugar>scope.selected.group.sugar_max){
                var tile_them = 0;
                if(plg.sugar<scope.selected.group.sugar_min){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = scope.selected.group.sugar_min-plg.sugar+k;
                }else{
                    tile_them = -1*(plg.sugar-scope.selected.group.sugar_max)-k;
                }
                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;
                scope.balance.sugarChange(quantity_need);
                check = false;
            }
            return check;
        };
        scope.balance.whileNotPassQuantity = function(i,j,k) {
            var check = true;
            var protein = scope.balance.getProtein();
            var plg = {};
            plg.protein = protein/(scope.selected.group.nutritions.animal_protein + scope.selected.group.nutritions.vegetable_protein)*100;
            var buasang_norm = scope.getNormBuasang();
            var smallest_rate = scope.selected.group.smallest_rate + buasang_norm.smallest_rate;
            var biggest_rate = scope.selected.group.biggest_rate + buasang_norm.biggest_rate;
            if(plg.protein<smallest_rate || plg.protein>biggest_rate){
                var tile_canbang = (biggest_rate + smallest_rate)/2;
                var tile_them = 0;
                if(plg.protein<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = smallest_rate-plg.protein + (tile_canbang-smallest_rate)/2 + i;
                }else{
                    tile_them = -1*(plg.protein-biggest_rate + (tile_canbang-smallest_rate)/2)-i;
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.proteinChange(quantity_need);
                check = false;
            }
            var fat = scope.balance.getFat();
            plg.fat = fat/( scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat )*100;
            if(plg.fat<smallest_rate || plg.fat>biggest_rate){
                var tile_canbang = (biggest_rate + smallest_rate)/2;
                var tile_them = 0;
                if(plg.fat<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thê*/
                    tile_them = smallest_rate-plg.fat+j;/*+tile_canbang-scope.selected.group.smallest_rate;*/
                }else{
                    tile_them = -1*(plg.fat-biggest_rate)-j;/*+tile_canbang-scope.selected.group.smallest_rate);*/
                }
                var quantity_need = (scope.selected.group.nutritions.animal_fat + scope.selected.group.nutritions.vegetable_fat)/100*tile_them;
                scope.balance.fatChange(quantity_need);
                check = false;
            }

            var sugar = scope.balance.getSugar();
            plg.sugar = sugar/scope.selected.group.nutritions.sugar*100;
            if(plg.sugar<smallest_rate || plg.sugar>biggest_rate){
                var tile_them = 0;
                if(plg.sugar<smallest_rate){ /*Đạm thấp hơn nên cần bổ sung thêm*/
                    tile_them = smallest_rate-plg.sugar+k;
                }else{
                    tile_them = -1*(plg.sugar-biggest_rate)-k;
                }
                var quantity_need = (scope.selected.group.nutritions.sugar)/100*tile_them;
                scope.balance.sugarChange(quantity_need);
                check = false;
            }
            return check;
        };
        scope.balance.sugarChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Gạo','Đường','Kẹo','Bánh khảo chay','Bánh','Bột mì tinh (bột năng)','Bột dong lọc','Bột khoai','Trân châu sắn','Bột bán','Si rô','Mạch nha','Miến (bún tàu), Hủ tiếu khô','Mật ong','Mứt cam'];
            var food_ids = ['1179','1178','588','612'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if((indexOf_array(food.name,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.sugar>=15*/ 
                             || food.nutritions.sugar*4>food.nutritions.fat*9 && food.nutritions.sugar>food.nutritions.protein) {
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    })
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.sugar<foods[j].nutritions.sugar){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.sugar);
                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.sugar/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = food.luong1tre/food.gam_exchange;
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.fatChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Dầu','Mỡ','Tủy xương bò','Bơ','mayonnaise'];
            var food_ids = ['907','908','909','910','911','912','913','914','915','1288','917','916'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if((indexOf_array(food.food_id,names) || in_array(food_id+'',food_ids)) /*&& food.nutritions.fat >= 5 || food.nutritions.fat>=40*/
                                || food.nutritions.sugar*4<food.nutritions.fat*9 && food.nutritions.fat*9>food.nutritions.protein*4){
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    })
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.fat<foods[j].nutritions.fat){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.fat);

                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.fat/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.proteinChange = function(soluong){
            /* Lọc ra thực phẩm là động vật hoặc có tên là có tên giống với mảng định nghĩa cứng */
            var names = ['Tôm','cá','Mắm','bò','trâu','mực','thịt','trứng'];
            var foods = [];
            var gam_total = 0;
            angular.forEach(scope.balance.data, function($foods,warehouse_id){
                if(scope.selected.balance_warehouse[warehouse_id]){
                    angular.forEach($foods, function(food,food_id){
                        if(!food.selected){
                            if(indexOf_array(food.name,names) /*&& food.nutritions.protein>=5 || food.nutritions.protein >= 20*/ 
                                || food.nutritions.protein*4>food.nutritions.fat*9 && food.nutritions.sugar<food.nutritions.protein){
                                foods.push(food);
                                if(food.luong1tre_balance+'' == 'Infinity'){
                                    food.luong1tre_balance = food.luong1tre
                                }
                                gam_total += food.luong1tre_balance;
                            }
                        }
                    });
                }
            });
            /*Sắp xếp giảm dần theo hàm lượng đạm*/
            /*Sắp xếp giảm dần theo calo*/
            for(var i=0;i<foods.length-1; i++){
                for(var j=i+1;j<foods.length;j++){
                    if(foods[i].nutritions.protein<foods[j].nutritions.protein){
                        var tmp = foods[i];
                        foods[i] = foods[j];
                        foods[j] = tmp;
                    }
                }
            }
            var soluong_used = 0;
            angular.forEach(foods, function(food,id){
                var tile = food.luong1tre_balance/gam_total;
                var need = tile*soluong;
                var gam_need = Math.ceil(100*need/food.nutritions.protein);

                if(food.luong1tre_balance+gam_need>=0.5){
                    soluong_used += Math.ceil(food.nutritions.protein/100*gam_need);
                    food.luong1tre_balance += gam_need;
                    if(soluong<0){
                        food.luong1tre_balance = Math.floor(food.luong1tre_balance);
                    }else{
                        food.luong1tre_balance = Math.ceil(food.luong1tre_balance);
                    }

                    var luong1tretheodvt = $['/'](food.luong1tre,food.gam_exchange);
                    var thucmuatheodvt = luong1tretheodvt;
                    if(food.extrude_factor){
                        thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
                    }
                    food.thanhtien_balance = $['*'](thucmuatheodvt,food.price_kg);
                }
            });
        };
        scope.balance.sumCalo = function(foods){
            var rs = 0;
            scope.menu_adjust || (scope.menu_adjust={});
            var date = scope.menu_adjust.date;
            angular.forEach(foods, function(food,food_id){
                rs += $['*'](scope.co_cau(null, date).protein, food.luong1tre_balance)*(food.nutritions.protein/100)
                    + $['*'](scope.co_cau(null, date).fat, food.luong1tre_balance)*(food.nutritions.fat/100)
                    + $['*'](scope.co_cau(null, date).sugar, food.luong1tre_balance)*(food.nutritions.sugar/100)
            });
            return rs;
        };
        scope.balance.getTotal_nutritions = function(){
            var rs = {
                dam: 0, beo:0, duong: 0, calo:0
            };
            scope.menu_adjust || (scope.menu_adjust={});
            var date = scope.menu_adjust.date;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                if(scope.dinhduong_calc[warehouse_id]){
                    angular.forEach(kho, function(food,food_id){
                        rs.dam = $['+'](rs.dam,$['*'](food.luong1tre_balance,(food.nutritions.protein/100)));
                        rs.beo = $['+'](rs.beo,$['*'](food.luong1tre_balance,(food.nutritions.fat/100)));
                        rs.duong = $['+'](rs.duong,$['*'](food.luong1tre_balance,(food.nutritions.sugar/100)));
                    })
                }
            });
            rs.calo = scope.co_cau(null, date).protein * rs.dam + scope.co_cau(null, date).fat * rs.beo + scope.co_cau(null, date).sugar * rs.duong;
            return rs;
        };
        scope.balance.getCaloAll = function(){
            var rs = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(food,food_id){
                    rs += scope.getCalo(food, 'luong1tre_balance');
                })
            });
            return rs;
        };
        /*  Tính lượng khẩu phần (calo) đạt hay chưa */
        scope.balance.caloRate = function(){
            var value = 0;
            var calo = round(scope.sumCaloMeals(scope.balance.meals), 0);
            var rate = scope.getNormSelected();
            if (calo >= rate.smallest_rate && calo <= rate.biggest_rate) {
                value = 1;
            } else {
                if (calo < rate.smallest_rate) {
                    value = 0;
                } else {
                    value = 2;
                }
            }

            return value;
        };
        scope.balance.caloRateBind = function(){
            var value = scope.balance.caloRate();
            if(value == 1){
                text = 'Đạt';
                class_color = '';
            }else if(value == 0){
                text = 'Chưa đạt';
                class_color = 'color-red';
            }else{
                text = 'Vượt quá định mức';
                class_color = 'btn-color-blue';
            }
            return {text: text, 'class': class_color, value: value};
        };
        /* Tính tổng đạm*/
        scope.balance.getProtein = function(){
            var animal_protein = 0;
            var vegetable_protein = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(row,index){
                    if(warehouse_id == 2){
                        if(row.is_meat == 1) {   /*Nếu là động vật*/
                            animal_protein += row.nutritions.protein*row.luong1tre_balance/100;
                        }else{
                            vegetable_protein += row.nutritions.protein*row.luong1tre_balance/100;
                        }
                    }
                });
            });
            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';
            var value = ( animal_protein + vegetable_protein ) ;
            return value;
        };
        /* Tính toán tỉ lệ đạt béo*/
        scope.balance.getFat = function(){
            var animal_fat = 0;
            var vegetable_fat = 0;
            angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                angular.forEach(kho, function(row,index){
                    if(warehouse_id == 2){
                        if(row.is_meat == 1) {   /*Nếu là động vật*/
                            animal_fat += row.nutritions.fat*row.luong1tre_balance/100;
                        }else{
                            vegetable_fat += row.nutritions.fat*row.luong1tre_balance/100;
                        }
                    }
                });
            });
            if(!scope.selected.group || !scope.selected.group.nutritions) return '-';
            var value = ( animal_fat + vegetable_fat ) ;
            return value;
        };
        /* Tính tỉ lệ từng loại thành phần dinh dưỡng*/
        scope.balance.getSugar = function(){
            var value = 0;
            if(scope.selected.group.nutritions) {
                if(!scope.selected.group || !scope.selected.group.nutritions.sugar) return value;
                angular.forEach(scope.datagrid.data, function(kho,warehouse_id){
                    angular.forEach(kho, function(row,index){
                        if(warehouse_id == 2){
                            value += row.nutritions.sugar*row.luong1tre_balance/100;
                        }
                    });
                });
            }
            return value;
        };
        
        /*Tính tỉ lệ PLG*/
        scope.balance.getTile_PLG = function(name){
            var rs = {
                protein: 0,
                fat: 0,
                sugar: 0
            };
            var tongcalo = 0;
            scope.menu_adjust || (scope.menu_adjust={});
            var date = scope.menu_adjust.date;
            angular.forEach(scope.balance.data, function(foods, warehouse_id){
                if ( scope.row.meal_selection[warehouse_id].selected ) {
                    angular.forEach(foods, function(food,food_id){
                        rs.protein += scope.co_cau(null, date).protein * food.luong1tre_balance*food.nutritions.protein/100;
                        rs.fat += scope.co_cau(null, date).fat * food.luong1tre_balance*food.nutritions.fat/100;
                        rs.sugar += scope.co_cau(null, date).sugar * food.luong1tre_balance*food.nutritions.sugar/100;
                    })
                }
            });
            tongcalo = rs.protein + rs.fat + rs.sugar;
            rs.protein = scope.round(rs.protein/tongcalo*100,1);
            rs.fat = scope.round(rs.fat/tongcalo*100,1);
            rs.sugar = scope.round(rs.sugar/tongcalo*100,1);
            return rs;
        };
        /*  Tính tỉ lệ chất dd (PLG) đạt hay chưa */
        scope.balance.plgRate = function(){
            var rs = {};
            var tile = scope.getTile_PLG(scope.balance.meals);
            var tile_dat = [];
            var tile_chuadat = [];
            var tile_vuotqua = [];
            if(tile.protein>=scope.selected.group.protein_min && tile.protein<=scope.selected.group.protein_max){
                tile_dat.push({define:'protein',name: 'Chất đạm'});
            }else if(tile.protein<scope.selected.group.protein_min) {
                tile_chuadat.push({define:'protein',name: 'Chất đạm'})
            }else{
                tile_vuotqua.push({define:'protein',name: 'Chất đạm'})
            }
            if(tile.fat>=scope.selected.group.fat_min && tile.fat<=scope.selected.group.fat_max){
                tile_dat.push({define:'fat',name: 'Chất béo'});
            }else if(tile.fat<scope.selected.group.fat_min) {
                tile_chuadat.push({define:'fat',name: 'Chất béo'})
            }else{
                tile_vuotqua.push({define:'fat',name: 'Chất béo'})
            }
            if(tile.sugar>=scope.selected.group.sugar_min && tile.sugar<=scope.selected.group.sugar_max){
                tile_dat.push({define:'sugar',name: 'Chất bột'});
            }else if(tile.sugar<scope.selected.group.suga_min) {
                tile_chuadat.push({define:'sugar',name: 'Chất bột'})
            }else{
                tile_vuotqua.push({define:'sugar',name: 'Chất bột'})
            }
            rs = {
                dat: tile_dat,
                chuadat: tile_chuadat,
                vuotqua: tile_vuotqua
            };
            return rs;
        };

        scope.balance.plgRateBind = function(){
            var thanhphan = scope.balance.plgRate();
            var text = '';
            if(count(thanhphan.dat) == 3){
                text = 'Cân đối';
                class_color = '';
            }else{
                text = 'Chưa cân đối';
                class_color = 'color-red';
            }
            return {text: text, 'class': class_color, thanhphan: thanhphan};
        };

        scope.balance.onChange_thucmuatheodvtApply = function(food){
            food.gam_exchange || (food.gam_exchange = 1000);
            var thucmuatheodvt = Number(food.thucmuatheodvt);
            food.thucmua1tretheodvt = scope.round($['/'](thucmuatheodvt, scope.row.sotre),4);
            food.thucmua1nhom = thucmuatheodvt * food.gam_exchange / 1000;
            food.thucan1nhom = food.thucmua1nhom;
            if(food.extrude_factor){
                food.thucan1nhom = $['-'](food.thucmua1nhom, food.thucmua1nhom/100*food.extrude_factor);
            }
            food.luong1tre = round($['*']($['/'](food.thucan1nhom, scope.row.sotre), 1000));
            food.thucan1nhom = round(food.thucan1nhom);
            food.thucmua1nhom = round(food.thucmua1nhom);
            food.luong1tre_root = food.luong1tre;
            food.thanhtien1nhom = $['*'](thucmuatheodvt, food.price);
            scope.divide_luong1tre(food);
        };
        scope.balance.foodSelectAll = function() {
            scope.balance.selectallfood = !scope.balance.selectallfood;
            angular.forEach(scope.balance.data, function(kho,warehouse_id){
                var foods = scope.balance.getFoodOfWarehouse(warehouse_id);
                angular.forEach(foods, function(food,food_id){
                    food.selected = scope.balance.selectallfood;
                })
            });
        };
        scope.balance.luong1treOnChange = function(food){
            food.luong1tre_balance = Number(food.luong1tre_balance);
            var luong1tretheodvt = $['/'](food.luong1tre_balance,food.gam_exchange);
            var thucmuatheodvt = luong1tretheodvt;
            if(food.extrude_factor){
                thucmuatheodvt = $['*'](luong1tretheodvt,(1+1/(100/food.extrude_factor - 1)));
            }
            food.thanhtien_balance = $['*'](thucmuatheodvt,food.price);
        };
        scope.balance.thanhtienOnChange = function(food){
            food.tiendicho1tre_balance = Number(food.tiendicho1tre_balance);
            var thucmuatheodvt = $['/'](food.tiendicho1tre_balance,food.price);
            var luong1tretheodvt = thucmuatheodvt;
            if(food.extrude_factor){
                luong1tretheodvt = thucmuatheodvt/(1+1/(100/food.extrude_factor - 1));
            }
            food.luong1tre_balance = $['*'](luong1tretheodvt, food.gam_exchange);
        };
        /*  Trả ra số phần quy định đã chọn */
        scope.balance.getDinhmucSophanSelected = function() {
            var rs = [];
            angular.forEach(scope.selected.group.dinhmuc, function(dinhmuc,key){
                if(dinhmuc.selected){
                    rs.push(key);
                }
            });
            return rs;
        };
        scope.balance.warehouseSelectedOnClick = function(meal) {
            scope.warehouseSelectedOnClick(meal);
            scope.balance.init();
        };
        scope.balance.getSelectBySophan = function(foods){
            var rs = {};
            var sophan = scope.balance.getDinhmucSophanSelected();
            if(sophan.length==0){
                return foods;
            }
            angular.forEach(foods, function(food,index){
                var check_sophan = false;
                angular.forEach(sophan, function(key,index){
                    if(food[key]){
                        rs[food.food_id] = food;
                    }
                })                
            });
            return rs;
        };
        /*  Trả ra mảng tất cả thực phẩm được chọn theo kho  */
        scope.balance.getFoodOfWarehouse = function(warehouse_id){
            var foods = {};
            if(scope.selected.balance_warehouse[warehouse_id]){
                foods = scope.balance.getSelectBySophan(scope.balance.data[warehouse_id]);
            }
            return foods;
        };

        scope.balance.onCheckedAllSophan = function(){
            scope.balance.dinhmucSelectall = !scope.balance.dinhmucSelectall;
            angular.forEach(scope.selected.group.dinhmuc, function(item,index){
                item.selected = scope.balance.dinhmucSelectall
            });
        };
        scope.balance.isCheckedAllSophan = function(){
            var rs = [];
            angular.forEach(scope.selected.group.dinhmuc, function(item,index){
                if(item.selected){
                    rs.push(true);
                }
            });
            if(rs.length == count(scope.selected.group.dinhmuc)){
                return true;
            }else{
                return false;
            }
        };
        scope.balance.showPartPLGInfo = function(){
            $.menu_planning.showPartPLGInfo_balance();
        }
    }
};
