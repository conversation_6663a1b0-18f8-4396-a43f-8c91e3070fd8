$.gumshoe = {
    module: 'gumshoe',
    init: function() {
        var self = this;
        process($CFG.project+'/'+self.module+'/special',{},function(resp){
            self.months = resp.data.months;
            self.configs = resp.data.configs;
            self.suppliers = resp.data.suppliers;
            self.mapSuppliers = {};
            self.suppliers.forEach(function (supplier) {
                self.mapSuppliers[supplier.id] = supplier.name;
            });
            $.gumshoe.initAngular();
        },null,false);
        
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                $.gumshoe.scope = scope;
                scope.gumshoe || (scope.gumshoe = {});
                scope.gumshoe.columns = [];
                scope.gumshoe.months = self.months;
                scope.configs = self.configs;
                scope.gumshoe.suppliers = self.suppliers;
                var d = new Date();
                var month1 = d.getMonth() + 1;
                // var year1 = d.getFullYear();
                var month_now;
                if(month1 < 6){
                    month_now = '0'+month1;
                }else if(month1 < 10){
                    month_now = '0'+month1;
                }else{
                    month_now = month1;
                }
                scope.gumshoe.style = {
                    col: {day: 120,food: 70}
                };
                angular.forEach(scope.gumshoe.months, function(month, key) {
                    if(month_now+'/'+(month.year) == month.name){
                        scope.gumshoe.monthSelected = scope.gumshoe.months[key];
                        month_now = month_now+'/'+(month.year);
                    }                    
                });
                // if(month_now){
                //     var ntht = [
                //         my = {name:month_now},
                //         warehouse = 2
                //     ];
                //     process('dinhduong/gumshoe/list',{params:ntht, async: true},function(resp){
                //         scope.$apply(function(){
                //             scope.gumshoe.gumshoes = resp.data.gumshoes;
                //             scope.gumshoe.gumshoe_first = resp.data.gumshoe_first;
                //             scope.gumshoe.gumshoenews = {};
                //             var tmp_gumshoes = {};
                //             angular.forEach(scope.gumshoe.gumshoes, function(gumshoe, key) {
                //                 var newkey = key.slice(8,10);
                //                 if(newkey < 10){
                //                     newkey = key.slice(9,10);
                //                 }
                //                 tmp_gumshoes[newkey] = [];
                //                 angular.forEach(gumshoe, function(gum,food_id_price){
                //                     tmp_gumshoes[newkey].push({value: gum.nhap});
                //                     tmp_gumshoes[newkey].push({value: gum.xuat});
                //                     tmp_gumshoes[newkey].push({value: gum.ton});
                //                 })
                //                 // console.log(gumshoe);
                //                 // angular.forEach(gumshoe, function(gum, key) {
                //                 //     // console.log(gum.nhap);
                //                 //     if (gum.nhap == 0) {
                //                 //         gum.nhap = digit_grouping(gum.nhap)+'44242';
                //                 //     }
                //                 // })
                //                 // scope.gumshoe.gumshoenews[newkey] = gumshoe;
                //             });
                //             scope.gumshoe.gumshoenews = tmp_gumshoes;
                //             // console.log(scope.gumshoe.gumshoenews);
                //             angular.forEach(scope.gumshoe.gumshoe_first, function(value,name){
                //                 scope.gumshoe.columns.push({food_name: name, field: 'nhap', title: 'Nhập'});
                //                 scope.gumshoe.columns.push({food_name: name, field: 'xuat', title: 'Xuất'});
                //                 scope.gumshoe.columns.push({food_name: name, field: 'ton', title: 'Tồn'});
                //             })
                //         });
                //     }, null, false);
                // }
                scope.gumshoe.changeMonthVT = function(){
                    // console.log(scope.gumshoe.warehouse);
                    if(scope.gumshoe.warehouse == undefined || !$("#khosang_id").is(':checked')){
                        scope.gumshoe.warehouse = 2;
                    }
                    var ntht = [
                        my = scope.gumshoe.monthSelected,
                        warehouse = scope.gumshoe.warehouse
                    ];
                    process('dinhduong/gumshoe/list',{ params:ntht, async: true },function(resp){
                        scope.$apply(function(){
                            scope.gumshoe.columns = [];
                            scope.gumshoe.gumshoes = resp.data.gumshoes;
                            scope.gumshoe.gumshoe_first = resp.data.gumshoe_first;
                            scope.gumshoe.gumshoenews = {};
                            scope.endOfMonth = resp.data.endOfMonth;
                            var tmp_gumshoes = {};
                            angular.forEach(scope.gumshoe.gumshoes, function(gumshoe, key) {
                                var newkey = key.slice(8,10);
                                if(newkey < 10){
                                    newkey = key.slice(9,10);
                                }
                                tmp_gumshoes[newkey] = [];
                                angular.forEach(gumshoe, function(gum,food_id_price){
                                    tmp_gumshoes[newkey].push({value: gum.nhap});
                                    tmp_gumshoes[newkey].push({value: gum.xuat});
                                    tmp_gumshoes[newkey].push({value: gum.ton});
                                })
                                // console.log(gumshoe);
                                // angular.forEach(gumshoe, function(gum, key) {
                                //     // console.log(gum.nhap);
                                //     if (gum.nhap == 0) {
                                //         gum.nhap = digit_grouping(gum.nhap)+'44242';
                                //     }
                                // })
                                // scope.gumshoe.gumshoenews[newkey] = gumshoe;
                            });
                            scope.gumshoe.gumshoenews = tmp_gumshoes;
                            // console.log(scope.gumshoe.gumshoenews);
                            angular.forEach(scope.gumshoe.gumshoe_first, function(value,name){
                                scope.gumshoe.columns.push({food_name: name, field: 'nhap', title: 'Nhập'});
                                scope.gumshoe.columns.push({food_name: name, field: 'xuat', title: 'Xuất'});
                                scope.gumshoe.columns.push({food_name: name, field: 'ton', title: 'Tồn'});
                            })
                        });
                    }, null, false);
                }
                scope.gumshoe.changeMonthVT();
                scope.totalImportForNccForm = function(){
                    scope.tmp_importncc = {
                        from: '1/'+scope.gumshoe.monthSelected.name
                    };
                    var m = scope.gumshoe.monthSelected.name.split('/');
                    var endday = new Date(m[1],parseInt(m[0]),0);
                    scope.tmp_importncc.to = endday.getDate()+'/'+m[0]+'/'+m[1];
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            action:'add',
                            title:'Tổng hợp kho theo nhà cung cấp',
                            size: size.normal,
                            showButton: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module+'/popup_nhapkhotheoncc.html','', {}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp,scope));
                                    })
                                })
                            }
                        },
                        function(resp){
                        }
                    );
                };

                scope.totalExportBySupplierForm = function(){
                    scope.tmp_importncc = {
                        from: '1/'+scope.gumshoe.monthSelected.name
                    };
                    var m = scope.gumshoe.monthSelected.name.split('/');
                    var endday = new Date(m[1],parseInt(m[0]),0);
                    scope.tmp_importncc.to = endday.getDate()+'/'+m[0]+'/'+m[1];
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            action:'add',
                            title:'Tổng hợp xuất kho theo nhà cung cấp',
                            size: size.normal,
                            showButton: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/'+$CFG.project+'/'+self.module+'/total-export-by-supplier.html','', {}, function(resp){
                                    scope.$apply(function(){
                                        $(element).html(scope.compile(resp,scope));
                                    })
                                })
                            }
                        },
                        function(resp){
                        }
                    );
                };

                scope.followStorageExport = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            title:'Sổ theo dõi nhập - xuất',
                            // size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/dinhduong/dialog/nhap-kho-theo-nam.html','', {async: true}, function(resp){
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(resp,scope));
                                    })
                                });
                            }
                        }
                    );
                };

                scope.stockReport = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            title:'Tổng hợp báo cáo',
                            // size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/dinhduong/stock-report/stock-report.html','', {async: true}, function(resp){
                                    scope.$apply(function () {
                                        /*Set thông tin cho form*/
                                        scope.begin = getCurrentDate().first;
                                        scope.end = getCurrentDate().last;
                                        scope.warehouse = '2';
                                        scope.type = 'default';
                                        scope.isPrint = false;
                                        scope.isExport = false;
                                        $(element).html(scope.compile(resp,scope));
                                    })
                                });
                            }
                        }
                    );
                };

                scope.changeType = function(type){
                    var isExport = [
                        'tableIOI',
                        'stockTake',
                        'warehousing',
                        'stockByFood',
                        'stockByDay',
                        'warehouseCard',
                        'inventory',
                        'stockDetail'
                    ];
                    var isPrint = [
                        'stockDetail',
                        'followStock',
                        'receipt',
                        'export',
                        'stockTakeResources',
                        'exportPoints',
                        'stockTake'
                    ];
                    scope.isExport = isExport.indexOf(type) > -1;
                    scope.isPrint = isPrint.indexOf(type) > -1;
                };

                scope.signatureCheck = function($event, type){
                    var method = ['stockByDay'];
                    if(method.indexOf(type) === -1)
                        return true;
                    else{
                        $event.preventDefault();
                        scope.signature();
                    }
                };

                scope.signature = function(){
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project+'/'+self.module,
                            title:'Thêm chữ kí',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            content: function(element){
                                loadForm($CFG.remote.base_url+'/tmp/dinhduong/stock-report/signature.html','', {async: true}, function(resp){
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(resp,scope));
                                    })
                                });
                            }
                        }
                    );
                };

                scope.gumshoe.selectWarehouse = function(warehouse){
                    scope.gumshoe.warehouse = warehouse;
                };
                scope.gumshoe.getHeightHeader = function(){
                    var h = $('#thead-gumshoe').height();
                    return h;
                }
               
                $('#tbl-gum-scroll').scroll(function() {
                    var left = $(this).children().position().left;
                    var top = $(this).children().position().top;
                    $('#tbl-gum-scroll table thead').css({top:-1*top});
                    $('#tbl-gum-scroll table div.col1-static').css({left:-1*left});
                })

            });
        });
    },
    angular: function(element,resp,callback,dialogRef) {
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope) {
            $(element).html(scope.compile(form,scope));
            if (typeof callback === 'function') {
                callback(scope);
            }
        });
    }
}
