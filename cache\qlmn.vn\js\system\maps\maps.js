var maps_items = [];
var item_img_index = 0;
var div_map_popup = 'div_map_popup';
app.controller('mapsController', ['$scope', function ($scope) {
    $scope.control = 'maps';
    $scope.rows = {};
    $scope.init = function () {
        axios.post($domain + '/' + $scope.control + '/init', {project_id: getURLParameter('m')}).then(function (response) {
            $scope.$apply(function () {
                response.data.rows.forEach(function (row) {
                    $scope.rows[row.merge_id] = row;
                });
				maps_items = $scope.rows;
                $scope.trees = buildTree(clone($scope.rows));
                $scope.isAdmin = response.data.isAdmin;
                $scope.project_id = response.data.project_id;
            });
        });
    }();

    $scope.getTreeDefault = function () {
        return {
            id: null,
            parent: null,
            name: '',
            url: '',
            desc: '',
			images: '',
			tech_desc: '',
			status: 0
        };
    };

    $scope.tree = $scope.getTreeDefault();

    $scope.add = function (parent) {
        $scope.tree = $scope.getTreeDefault();
        parent ? $scope.tree.parent = parent : $scope.tree.parent = null;
        var option = {
            title: 'Thêm sơ đồ chức năng',
            size: BootstrapDialog.SIZE_WIDE,
            type: BootstrapDialog.TYPE_SUCCESS,
        };
        dialog.init($scope, option, $domain + '/tmp/system/maps/form.html')
    };

    $scope.edit = function (tree) {
        $scope.tree = clone(tree);
        delete $scope.tree.children;
        var option = {
            title: 'Sửa sơ đồ chức năng',
            size: BootstrapDialog.SIZE_WIDE,
            type: BootstrapDialog.TYPE_SUCCESS,
        };
        dialog.init($scope, option, $domain + '/tmp/system/maps/form.html')
    };

    $scope.validate = function () {
        var errors = [];
        if (!$scope.tree.name) {
            errors.push('Tên không được trống!');
        }
        if (errors.length) {
            $.messager.alert('Thông báo', errors.join('<br/>'));
        } else {
            return true;
        }
    };

    $scope.desc = function (desc) {
        var option = {
            title: '<b>Thông tin</b>',
            size: BootstrapDialog.SIZE_WIDE,
            type: BootstrapDialog.TYPE_DEFAULT,
            message: desc,
            onshown: true
        };
        dialog.init($scope, option)
    };

    $scope.save = function () {
        if ($scope.validate()) {
            var data = $scope.tree;
            data['project_id'] = $scope.project_id;
			var old_merge_id = data['merge_id'];
			delete data['merge_id'];
            axios.post($domain + '/' + $scope.control + '/save', {data: data}).then(function (response) {
                $scope.updateTrees(response.data.id, false, data['order_id'], old_merge_id);
                $('.close').click();
            });
        }
    };

    $scope.del = function (id, order_id) {
        $.messager.confirm('Xác nhận', 'Bạn chắc chắn xóa chứ?', function (result) {
            if (result) {
                axios.post($domain + '/' + $scope.control + '/del', {id: id}).then(function () {
                    $scope.updateTrees(id, true, order_id, 0);
                });
            }
        });
    };

    $scope.updateTrees = function (id, isDel, order_id, old_merge_id) {
        isDel = isDel || false;
		merge_id = order_id+'_'+id;
		if(parseInt(order_id)<10) {
			merge_id = '0'+parseInt(order_id)+'_'+id;
		}
		$scope.tree.merge_id = merge_id;
        if (merge_id in $scope.rows) {
            if (isDel) {
                delete $scope.rows[merge_id];
            } else {
                $scope.rows[merge_id] = $scope.tree;
            }
        } else {
			if (old_merge_id!=undefined && old_merge_id!=0) {
				delete $scope.rows[old_merge_id];
			}
            $scope.tree.id = id;
            $scope.rows[merge_id] = $scope.tree;
        }
        var trees = buildTree(clone($scope.rows));
        $scope.$apply(function () {
            $scope.trees = trees;
        });
    };
	/* Mo popup maps chuc nang */
	$scope.openMapPopup = function (item) {
		if (!item) return;
		if (!item.id) return;
		item_img_index = 0;
		var width = 800;
		var img_hint = '';
		var h = window.innerHeight;
		var map_images = getMapItemImages(item);
		if(map_images.length>0) {
			img_hint = '(Ảnh '+(item_img_index+1)+'/'+map_images.length+')';
		}
		var divhtml = '<div class="map-contain-thong-bao"><div class="map-dialog-thong-bao" style="position: relative;"><style>' + getOpenNoticeStyle() + '</style>' +
			'<div class="map-tb-dialog-content" style="max-height:'+(h-100)+'px'+'; min-height:50%; min-width:80%; overflow-y: auto; padding-bottom: 20px;"><p><label style="float:left; padding-left:10px; font-weight:bold; color:#353535;font-family: sans-serif;padding-top: 5px;">CHỨC NĂNG: '+item.name+' <span id="img_hint">'+img_hint+'</span></label><label class="map-btn-dialog-close" title="Đóng"><span>&#8855;</span> Đóng</label></p>' +
			'<p style="width: 100%; margin-top: 30px;text-align: left; padding: 10px; color:#353535;font-family: sans-serif;font-size:15px; white-space:pre-line;">'+item.desc+'</p>';
		if(map_images.length>0) {
			divhtml += '<img id="img_notification_show" style="width: 90%; cursor:pointer;" src="' + map_images[0] + '" onclick="changeMapImage('+item.id+',\''+item.merge_id+'\')" title="Click vào ảnh để xem thêm ảnh tiếp theo nếu thông báo có nhiều ảnh">';
		}
		divhtml += '</div></div></div>';
		var divElement = $(divhtml);
		$('#'+div_map_popup).append(divElement);
		$('.map-btn-dialog-close').click(function () {
			divElement.remove();
		});
	};
}]);

// Get map_images by pms or gokids
function getMapItemImages(item) {
	var map_images = [];
	var tmp_images = item.images;
	if(is_map_gokids) {
		tmp_images = item.gk_images;
	}
	if (Array.isArray(tmp_images)) {
		map_images = tmp_images;
	}else{
		tmp_images += '';
		if (tmp_images!='' && tmp_images!="null") {
			map_images = tmp_images.split("\n")
		}
	}
	return map_images;
}

// Tang giam, thay doi anh tren thong bao pop up
function changeMapImage(id, merge_id){
	var item = maps_items[id];
	if (item==undefined) {
		item = maps_items[merge_id];
	}
	var map_images = getMapItemImages(item);
	if (map_images.length>0){
		item_img_index ++;
		if (item_img_index > (map_images.length-1)) {
			item_img_index = 0;
		}
		$('#img_hint').text('(Ảnh '+(item_img_index+1)+'/'+map_images.length+')');
		$('#img_notification_show').attr("src", map_images[item_img_index]);
	}
}

/* Lay style mo popup thong bao */
function getOpenNoticeStyle() {
    var style = `
        .map-dialog-thong-bao{
            margin-top: 20px;
        }
        .map-dialog-thong-bao .map-btn-dialog-close{
            position: absolute;
            color: #344144;
            font-weight: bold;
            cursor: pointer;
            font-size: 20px;
            right: 15px;
            top: 0px;
        }
        .map-dialog-thong-bao .note-text-auto{
            position: absolute;
            font-style: italic;
            color: orange;
        }
        .map-dialog-thong-bao .map-btn-dialog-close>span{
            font-size: 25px;
        }
        .map-dialog-thong-bao .map-btn-dialog-close:hover{
            color: orange;
        }
        .map-contain-thong-bao{
            position: fixed !important;
            height: 100% !important;
            width: 100% !important;
            background: rgba(100,100,100,0.6) !important;
            box-shadow: unset !important;
            padding: 0px !important;
            text-align: center;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        .window-shadow{
            background: rgba(10,10,10,0.5);
        }
        .map-dialog-thong-bao .map-tb-dialog-content{
            position: relative;
            display: inline-block;
            width: auto;
			background: #e9f5e5;
			border-radius: 5px;
			border: 5px solid #e9f5e5;
			margin:10px;
        }
        .map-dialog-thong-bao .input-checkbox{
            display: block;
            margin-top: -18px;
            cursor: pointer;
        }
        .map-dialog-thong-bao .input-checkbox:hover{
            color: orange;
        }
    `;
    return style;
}