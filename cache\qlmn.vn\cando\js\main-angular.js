angular_app.controller('mainContentController', ['$scope','$routeParams','$compile','MyCache','$filter','$cookies','$templateCache', '$templateRequest', '$sce', '$localStorage','$location',
    function($scope,$routeParams,$compile,MyCache,$filter,$cookies,$templateCache, $templateRequest, $sce, $localStorage, $location){
        $scope.storage = $localStorage;
        $scope.sys = {
            user: $CFG.user,
            year: +(new Date()).getFullYear(),
            schoolName: $CFG.schoolName,
            module: {title: 'Index'},
            years: [],
            boy: 1,
            showNoti: false,
            phong: {},
            truong: {}
        };
        $scope.grades = $CFG.grades;
        $scope.metaData = $CFG.metadata;
        $scope.dashboard = (() => {
            var val = $scope.storage.dashboard;
            val || (val = '{}');
            val = JSON.parse(val);
            if(val.mini_sidebar) {
                val.mini_sidebar = !val.mini_sidebar;
                setTimeout(function () {
                    $('a.nav-link.sidebartoggler').click();
                });
            }
            return val;
        })();
        $scope.setDashboard = function (name, value) {
            $scope.dashboard || ($scope.dashboard = {});
            $scope.dashboard[name] = value;
            $scope.storage.dashboard = JSON.stringify($scope.dashboard);
        };

        $scope.sys.phong.onSelected = function (old) {
            var id = 0;
            if ($scope.sys.phong.selected) {
                id = $scope.sys.phong.selected.id;
            }
            if (id != old.id) {
                old.id = id;
                process($CFG.remote.base_url + '/doing/admin/unit/listTruong', {
                    async: true,
                    id: id,
                    project_id: 2
                }, function (resp) {
                    $scope.$apply(function () {
                        $scope.sys.truong.lists = resp;
                        if ($scope.phong_origin_id != id) {
                            $('#select-school').combobox('clear');
                        } else {
                            $('#select-school').combobox('setValue', $scope.sys.truong.selected.id);
                        }
                    });
                }, function () {
                    $('#select-school').combobox('clear');
                }, false);
            }
        };

        $scope.sys.truong.onSelected = function () {
            var truong = $scope.sys.truong.selected;
            var id = '';
            if (truong) {
                id = truong.id;
            }
            process($CFG.remote.base_url + '/doing/admin/unit/setTruong', {
                async: true,
                id: id,
                project_id: 2
            }, function (resp) {
                statusloading();
                setTimeout(function () {
                    location.reload();
                })
            }, function () {
                $('#select-school').combobox('clear');
            });
        };
        $scope.sys.truong.onLoadSuccess = function () {
            if ($scope.sys.truong.old_id && count($scope.sys.truong.lists) > 0) {
                angular.forEach($scope.sys.truong.lists, function (item, ind) {
                    if (item.id == $scope.sys.truong.old_id) {
                        setTimeout(function () {
                            $('#select-school').val(item.text).next().find('input[type="text"]').val(item.text);
                        }, 300)
                        $scope.sys.truong.old_id = undefined;
                    }
                });
            }
        };
    }])
    .run(function($templateCache){


    });
$(function () {
    processAll([
        [$CFG.remote.base_url + '/measures/api/bmi/for-age',{type: 'GET'},(resp)=>{
            $CFG.metaData.ages = resp.bmi.data;
        },()=>{}, false,false,false],
        [$CFG.remote.base_url + '/measures/api/bmi/for-height',{type: 'GET'},(resp)=>{
            $CFG.metaData.heights = resp.bmi.data;
        },()=>{}, false,false,false],
        [$CFG.remote.base_url + '/measures/api/bmi/for-weight',{type: 'GET'},(resp)=>{
            $CFG.metaData.weights = resp.bmi.data;
        },()=>{}, false,false,false],
        [$CFG.remote.base_url + '/measures/api/common/labels',{type: 'GET'},(resp)=>{
            $CFG.metaData.labels = resp.labels.data;
        },()=>{}, false,false,false],
        [$CFG.remote.base_url + '/measures/api/grades',{type: 'GET'},(resp)=>{
            $CFG.grades = resp.grades.data;
        },()=>{}, false,false,false],
        ['doing/cando/index/getSys', {}, (resp) => {
            $CFG.schoolYear = resp.data.schoolYear;
            $CFG.user = resp.data.user;
            $CFG.schoolName = resp.data.schoolName;
        }, ()=>{}, false, false, false]
    ], function () {
        angular.element(document).ready(function() {
            angular.bootstrap(document, ["angular_app"]);
            setTimeout(function () {
                $('#app-loading-mash').hide();
            });
        });
    });
});