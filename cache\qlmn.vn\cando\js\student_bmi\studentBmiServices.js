(function () {
    'use strict';
    angular_app
        .service('studentBmiServices', studentBmiServices)
        .constant('BMI_LABELS', {
            'BMI_FOR_WEIGHT': {
                'OVERWEIGHT': 36,
                'OBESITY': 37,
                'NORMAL': 38,
                'THINNESS': 39,
                'SEVERE_THINNESS': 40,
            },
            'BMI_FOR_HEIGHT': {
                'OVER_HEIGHT2': 31,
                'OVER_HEIGHT1': 32,
                'NORMAL': 33,
                'THINNESS': 34,
                'SEVERE_THINNESS': 35,
            },
            'BMI_FOR_AGE': {
                'OVERWEIGHT': 1,
                'OBESITY': 2,
                'NORMAL': 3,
                'THINNESS': 4,
                'SEVERE_THINNESS': 5,
            },
        });

    /* @ngInject */
    function studentBmiServices($http, $q, $localStorage, BMI_LABELS) {
        var self = this;

        /* bird date of student not found */
        self.bird_dateNotFound = -1;
        self.bird_dateSmaller = -2; /* aaaaa */
        self.yearAboutSeleted = 5;
        self.superadmin_school = 1;
        self.monthsOfYear = 12;

        self.data = function(course_id){
            return {
                page_class_style: {},
                students: [],
                mirror_students: [],
                empty_data: false,
                page: 1,    /* Trang đang được chọn*/
                total: 0,   /* Tổng số bản ghi*/
                limit: 15,  /* Số dòng trên trang*/
                limits: [15, 25, 30, 50], /* Lựa chọn giới hạn số dòng trên trang*/
                pages: [],  /* Danh sách trang hiển thị*/
                page_count: 0,  /* Tổng số trang*/
                pages_show: 5,  /* Số trang hiển thị tối đa*/
                years: [],
                indexs: [
                    {id: 'sdd', name: 'SDD cân nặng'},
                    {id: 'tc', name: 'SDD chiều cao'},
                    {id: 'dc', name: 'Dư cân'},
                    {id: 'bp', name: 'Béo phì'}
                ],
                filters: {
                    index: '',
                    month: $localStorage.month ? +$localStorage.month : (new Date()).getMonth() + 1,
                    year: $localStorage.year ? +$localStorage.year : (new Date()).getFullYear(),
                    course_id: course_id,
                    keyword: null,
                    bmi_result: null
                }
            };
        };

        //populate dropdown school year
        self.generateSchoolYear = function(schoolYear){
            let arr_year = [];
            _.each(schoolYear, function (e) {
                arr_year.push({id: e, name:e + '-' + (e+1)});
            })
            return arr_year;
        };

        self.yearChange = function($scope){
            $localStorage.year = $scope.data.filters.year;
        };
        self.monthChange = function($scope){
            $localStorage.month = $scope.data.filters.month;
        };

        self.loadStudent = function ($scope, callBack) {
            if($scope.sys.user.superadmin != self.superadmin_school && $scope.sys.user.course_id+'' == 'null') {
                $.dm_datagrid.show({message: 'Tài khoản giáo viên chưa phân lớp.<br> Hãy liên hệ với quản trị trường.'});
                return;
            }
            $scope.data.page = $scope.data.page || 1;
            $scope.data.filters.course_id = $scope.data.filters.course_id || 0;
            var data = {
                type: 'GET',
                async: true,
                page: $scope.data.page,
                limit: $scope.data.limit,
                filters: {
                    month: $scope.data.filters.month,
                    school_year: $scope.data.filters.year,
                    course_id: $scope.data.filters.course_id,
                    keyword: $scope.data.filters.keyword,
                    bmi_result: $scope.data.filters.bmi_result
                }
            };
            process($scope.api_url + '/measures/api/students', data, function (resp) {
                if(resp.result === 'success') {
                    $scope.$apply(function () {
                        resp.students.data || (resp.students.data = []);
                        resp.total || (resp.total = 0);
                        $scope.data.mirror_students = clone(resp.students.data);

                        $scope.data.total = resp.total;
                        $scope.buildPagination();
                        $scope.buildStudent();
                        // self.getResultByWeightAndHeight()
                        $scope.data.students = resp.students.data;
                        _.each($scope.data.students , function (e) {
                            self.setResultByWeightAndHeight(e, $CFG.metaData.ages, $CFG.metaData.heights, $CFG.metaData.weights);
                        });

                        if(typeof callBack == 'function'){
                            callBack($scope.data.students);
                        }
                    });
                }
            });
        };
        self.loadStudentForWeight = function ($scope, callBack) {
            if($scope.sys.user.superadmin != self.superadmin_school && $scope.sys.user.course_id+'' == 'null') {
                $.dm_datagrid.show({message: 'Tài khoản giáo viên chưa phân lớp.<br> Hãy liên hệ với quản trị trường.'});
                return;
            }
            $scope.data.page = $scope.data.page || 1;
            $scope.data.filters.course_id = $scope.data.filters.course_id || 0;

            let data = {
                type: 'GET',
                async: true,
                page: $scope.data.page,
                limit: $scope.data.limit,
                filters: {
                    month: $scope.data.filters.month,
                    school_year: $scope.data.filters.year,
                    course_id: $scope.data.filters.course_id,
                    keyword: $scope.data.filters.keyword,
                    bmi_result: $scope.data.filters.bmi_result
                }
            };
            process($scope.api_url + '/measures/api/reports/bmi-histories', data, function (resp) {
                if(resp.result === 'success') {
                    $scope.$apply(function () {
                        resp.students.data || (resp.students.data = []);
                        resp.total || (resp.total = 0);
                        $scope.data.mirror_students = clone(resp.students.data);
                        $scope.data.students = resp.students.data;
                        $scope.data.total = resp.total;
                        $scope.buildPagination();
                        $scope.buildStudent();
                        $scope.data.empty_data = false;
                        if(typeof callBack == 'function'){
                            callBack($scope.data.students);
                        }
                    });
                }
            }, null, false);
        };

        //get Student info
        self.getStudentById = function ($scope, callBack) {
            if($scope.routerParams.id != undefined) {
                var data = {
                    type: 'GET',
                    async: true,
                    filters: {
                        school_year: $scope.routerParams.year,
                    }
                };
                process($scope.api_url + '/measures/api/students/' + $scope.routerParams.id, data, function (resp) {
                    if (resp.result === 'success') {
                        $scope.$apply(function () {
                            resp.student.data || (resp.student.data = []);
                            $scope.student = resp.student.data;
                            if(typeof callBack == 'function'){
                                callBack($scope.student);
                            }
                        });
                    }
                });

            }
        };

        self.getLabelBmi = function(student, $scope) {
            if(student.weight_result > 0){
                student.labelBmi = _.find($CFG.metaData.labels, function(o) { return o.id == student.weight_result; });
            }else{
                student.labelBmi = 'Chưa xác định';
            }
        };

        self.buildPagination = function ($scope) {
            $scope.data.pages = [];
            $scope.data.page_count = Math.ceil($scope.data.total/$scope.data.limit);
            if( $scope.data.page > $scope.data.page_count ){
                $scope.data.page = $scope.data.page_count;
            }
            var start = Math.ceil($scope.data.pages_show/2);
            var min = $scope.data.page-start;
            var max = $scope.data.page+start;
            if(min<1){
                max -= min;
                min = 1;
            } else {
                if(max>$scope.data.page_count){
                    min += $scope.data.page_count-max;
                    max = $scope.data.page_count+1;
                }
            }
            if(min<1){
                min = 1;
            }
            if(max>$scope.data.page_count){
                max = $scope.data.page_count;
            }
            for(var i = min; i <= max; i++) {
                $scope.data.pages.push(i);
            }
            $scope.data.page_class_style['first-page'] = ($scope.data.page <= 1);
            $scope.data.page_class_style['end-page'] = ($scope.data.page == $scope.data.page_count);
        };

        self.getCourseName = function($scope) {
            for(var grade of $scope.data.grades) {
                for(var course of grade.courses.data) {
                    if ($scope.sys.user.course_id == course.id) {
                        return course.name;
                    }
                }
            }
        };
        self.setPage = function(value, $scope) {
            if(value < 1){
                value = 1;
            }
            if (value > $scope.data.page_count){
                value = $scope.data.page_count;
            }
            if($scope.data.page != value){
                $scope.data.page = value;
            }
            $scope.loadStudent();
        };
        self.calculateBMI = function(student, $scope) {
            student.bmi = round($['/'](student.weight,student.height*student.height),1);
            $scope.studentsChanged();
        };
        self.studentChanged = function(student_index, $scope) {
            return $scope.data.students[student_index].month_old != $scope.data.mirror_students[student_index].month_old
                || $scope.data.students[student_index].weight != $scope.data.mirror_students[student_index].weight
                || $scope.data.students[student_index].height != $scope.data.mirror_students[student_index].height;
        };
        self.studentsChanged = function($scope) {
            $scope.btn_save_disabled = true;
            $scope.btn_cancel_disabled = true;
            for(var i in $scope.data.students) {
                if($scope.studentChanged(i)) {
                    $scope.btn_cancel_disabled = false;
                    $scope.btn_save_disabled = false;
                    return true;
                }
            }
        };

        //set result for weight_result/height_result/bmi_result
        self.setResultByWeightAndHeight = function(student, bmis, bmi_heights, bmi_weights) {
            let arr_data_bmis = _.find(bmis, function (o) {
                return (o.gender == student.gender && o.month == student.month_old);
            });
            let arr_data_heights = _.find(bmi_heights, function (o) {
                return (o.gender == student.gender && o.month == student.month_old);
            });
            let arr_data_weights = _.find(bmi_weights, function (o) {
                return (o.gender == student.gender && o.month == student.month_old);
            });

            //set bmi result
            if(arr_data_bmis != undefined) {
                student.bmi_result = 0;
                if (student.bmi > arr_data_bmis.SD3) {
                    student.bmi_result = BMI_LABELS.BMI_FOR_AGE.OBESITY;
                }
                if (student.bmi > arr_data_bmis.SD2 && student.bmi <= arr_data_bmis.SD3) {
                    student.bmi_result = BMI_LABELS.BMI_FOR_AGE.OVERWEIGHT;
                }
                if (student.bmi >= arr_data_bmis.SD2neg && student.bmi <= arr_data_bmis.SD2) {
                    student.bmi_result = BMI_LABELS.BMI_FOR_AGE.NORMAL;
                }
                if (student.bmi < arr_data_bmis.SD2neg && student.bmi >= arr_data_bmis.SD3neg) {
                    student.bmi_result = BMI_LABELS.BMI_FOR_AGE.THINNESS;
                }
                if (student.bmi < arr_data_bmis.SD3neg && student.bmi > 0) {
                    student.bmi_result = BMI_LABELS.BMI_FOR_AGE.SEVERE_THINNESS;
                }
            }else{
                student.bmi_result = 0;
            }

            //set height result
            if(arr_data_heights != undefined) {
                student.height_result = 0;
                if (student.height >= arr_data_heights.SD2neg) {
                    student.height_result = BMI_LABELS.BMI_FOR_HEIGHT.NORMAL;
                }
                if (student.height < arr_data_heights.SD2neg && student.height >= arr_data_heights.SD3neg) {
                    student.height_result = BMI_LABELS.BMI_FOR_HEIGHT.THINNESS;
                }
                if (student.height < arr_data_heights.SD3neg && student.height > 0) {
                    student.height_result = BMI_LABELS.BMI_FOR_HEIGHT.SEVERE_THINNESS;
                }
            }else{
                student.height_result = 0;
            }

            //set weight result
            if(arr_data_weights != undefined) {
                student.weight_result = 0;
                if (student.weight > arr_data_weights.SD3) {
                    student.weight_result = BMI_LABELS.BMI_FOR_WEIGHT.OBESITY;
                }
                if(student.weight > arr_data_weights.SD2 && student.weight <= arr_data_weights.SD3) {
                    student.weight_result = BMI_LABELS.BMI_FOR_WEIGHT.OVERWEIGHT;
                }
                if(student.weight >= arr_data_weights.SD2neg && student.weight <= arr_data_weights.SD2) {
                    student.weight_result = BMI_LABELS.BMI_FOR_WEIGHT.NORMAL;
                }
                if(student.weight < arr_data_weights.SD2neg && student.weight >= arr_data_weights.SD3neg) {
                    student.weight_result = BMI_LABELS.BMI_FOR_WEIGHT.THINNESS;
                }
                if(student.weight < arr_data_weights.SD3neg && student.weight > 0) {
                    student.weight_result = BMI_LABELS.BMI_FOR_WEIGHT.SEVERE_THINNESS;
                }
            }else{
                student.weight_result = 0;
            }

            student.conclusion = student.bmi_result;
            return student;
        };

        return self;
    }

})();
