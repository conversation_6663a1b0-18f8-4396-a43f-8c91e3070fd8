$(function () {
    var element = 'mark';
    var $results, currentIndex = 0, offsetTop = 100, offsetLeft = 50, currentClass = "focus";

    var highlight = function () {
        var context = $('.context');
        var searchTerm = $("input[name='keyword']").val();
        var options = {
            separateWordSearch: false
        };
        context.unmark({
            done: function () {
                context.mark(searchTerm, Object.assign({}, options, {
                    done: function () {
                        $results = context.find(element);
                        currentIndex = 0;
                        jumpTo();
                    }
                }));
            }
        });
        context.mark(searchTerm, Object.assign({}, options, {element: element}));
    };

    var focus = function () {
        if ($results.length) {
            currentIndex += 1;
            if (currentIndex < 0) {
                currentIndex = $results.length - 1;
            }
            if (currentIndex > $results.length - 1) {
                currentIndex = 0;
            }
            jumpTo();
        }
    };

    var jumpTo = function () {
        if ($results.length) {
            var positionLeft, positionTop,
                $current = $results.eq(currentIndex);
            $results.removeClass(currentClass);
            if ($current.length) {
                $current.addClass(currentClass);
                positionLeft = $current.offset().left - offsetLeft;
                positionTop = $current.offset().top - offsetTop;
                window.scrollTo(positionLeft, positionTop);
            }
        }
    };
	$("#search").click(function() {
		highlight();
	});
	$('#keyword').keyup(function(e){
		if(e.keyCode == 13){
			highlight();
		}
	});
});