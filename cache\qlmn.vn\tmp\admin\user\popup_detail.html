<form class="form-horizontal" role="form" style="display: inline-block; width: 100%;" xmlns="http://www.w3.org/1999/html">
	<div class="form-group col-md-12">
	    <div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
	    	<div style="font-weight: bold; border-bottom:1px dotted green;"><b>THÔNG TIN TRƯỜNG TRÊN PHẦN MỀM QUẢN LÝ MẦM NON PMS</b></div>
	    	<div style="padding-top: 5px;">
      		<label>TÀI KHOẢN: </label> 
          		<span ng-bind="$CFG.username"></span>
        	</div>
        	<div>
			<label>MÃ TRƯỜNG: </label>
			<span ng-bind="$CFG.unit_id"></span>
		</div>
		<div>
			<label>TÊN TRƯỜNG: </label>
			<span >Trường MN QuangVT</span>
		</div>
		<div>
			<label>ĐƠN VỊ QUẢN LÝ: </label>
			<span ng-if="!sys.configs.parentname">PGD Vietec Partner</span>
			<span ng-if="sys.configs.parentname" ng-bind="sys.configs.parentname"></span>
		</div>
		<div style="display:none">
			<label>Link truy cập nhanh: </label>
			<span>http://localhost:3000/admin/User/loginByToken?user=d.hd.mnquangvt&&token=7f9ba2e29a083944b7859bff013187750c222774</span>
		</div>
	</div>
		<div class="form-group col-md-12" style="padding: 0px;" ng-if="$CFG.province==79 || $CFG.is_sync_csdl_nganh">
	    <div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
	    	<div style="font-weight: bold; border-bottom:1px dotted green;"><b>THÔNG TIN & BÁO CÁO TRƯỜNG TRÊN CSDL NGÀNH</b></div>
	    				<div style="padding-top: 5px;" ng-if="!$CFG.cpu_id">
				<label>MÃ TRƯỜNG: </label>
				<span style="margin-left:10px;">0&nbsp;&nbsp;&nbsp;<i class="fa fa-square-o icon-checkbox color-red"></i> Chưa cài đặt</span>
							</div>
						<div style="border-bottom:1px dotted green;" ng-if="$CFG.province==79">
				<label style="border-bottom: 1px dotted #ccc;">BÁO CÁO KẾT QUẢ DINH DƯỠNG MẦM NON {{$CFG.namhoc}}-{{parseInt($CFG.namhoc)+1}}: </label><br/>
				<span><i ng-class="1? 'fa fa-check-square-o icon-checkbox color-green': 'fa fa-square-o icon-checkbox color-red'"></i> Thực đơn (CĐKP)</span><br/>
				<span><i ng-class="1? 'fa fa-check-square-o icon-checkbox color-green': 'fa fa-square-o icon-checkbox color-red'"></i> Sổ kiểm thực 3 bước</span><br/>
				<span><i ng-class="1? 'fa fa-check-square-o icon-checkbox color-green': 'fa fa-square-o icon-checkbox color-red'"></i> Sổ thực ăn (STKPA)</span><br/>
				<span><i ng-class="1? 'fa fa-check-square-o icon-checkbox color-green': 'fa fa-square-o icon-checkbox color-red'"></i> Sổ tính tiền chợ (STTA)</span>
			</div>
			<div style="padding-top: 10px; text-align: center;">
				<a ng-if="$CFG.province==79" class="icon-in" style="font-weight: bold;"
				href="{{$CFG.remote.base_url+'/'+$CFG.project+'/menuReport/menuTracking'}}"
				target="_blank">[XEM BÁO CÁO KẾT QUẢ DINH DƯỠNG MẦM NON]
				</a>
				<a ng-if="$CFG.province!=79 && $CFG.is_sync_csdl_nganh && $CFG.is_principal" class="icon-in" style="font-weight: bold;"
				href="{{$CFG.remote.base_url+'/report/csdl_nganh_v2/list_sync'}}"
				target="_blank">[THÔNG TIN ĐỒNG BỘ CSDL NGÀNH MẦM NON]
				</a>
			</div>
	    </div>
	</div>
	<div class="form-group col-md-12" style="padding: 0px;" ng-if="$CFG.province==56">
		<div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
			<div style="padding-top: 10px; text-align: center;">
				<a class="icon-in" style="font-weight: bold;" ng-if="$CFG.level==4"
				   href="{{$CFG.remote.base_url+'/report/csdl_nganh/reportCSDLNganh'}}"
				   target="_blank">[BÁO CÁO CƠ SỞ DỮ LIỆU NGÀNH]
				</a>
				<a class="icon-in" style="font-weight: bold;" ng-if="$CFG.level<4"
				   href="{{$CFG.remote.base_url+'/report/csdl_nganh/reportCSDLNganhForSP'}}"
				   target="_blank">[BÁO CÁO CƠ SỞ DỮ LIỆU NGÀNH]
				</a>
			</div>
		</div>
	</div>

	<div class="form-group col-md-12" style="padding: 0px;">
		<div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
			<div style="font-weight: bold; border-bottom:1px dotted green;"><b>LOGO TRƯỜNG</b></div>
			<div id="upload_logo">
                <br>
                <p id="show_logo_image">
                </p>
                <div style="display: flex">
                    <input type="file" name="" id="logo_image" accept="image/*" />
                    <button id='btn_upload_logo' class="btn btn-success btn-sm">Upload ảnh logo</button>
                </div>
            </div>
		</div>
	</div>
</form>
<script type="text/javascript">
  	function checkSyncMessage()
	{
    	process('../api/csdl_nganh/check_sync_unit_message',{}, function(resp) {
    		$.messager.alert('THÔNG BÁO', "KẾT QUẢ KIỂM TRA: "+ resp.messsage);
   		});
  	}

	// Tải logo hiện tại khi mở popup
	function loadExistingLogo() {
		$.ajax({
			url: $CFG.remote.base_url + '/doing/admin/user/getUnitLogo',
			type: 'POST',
			data: {
				unit_id: $CFG.unit_id
			},
			headers: {
				'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
			},
			success: function(response) {
				if (response.success && response.url) {
					$('#show_logo_image').html("<img src='" + response.url + "?v=" + new Date().getTime() + "' style='max-width: 200px; max-height: 150px;' />");
					
					// Cập nhật vào $CFG để các phần khác có thể sử dụng
					if (typeof $CFG !== 'undefined' && $CFG.sys && $CFG.sys.configs) {
						$CFG.sys.configs.unit_logo_image = response.url;
					}
				} else {
					$('#show_logo_image').html("<span style='color: #999;'>Chưa có logo</span>");
				}
			},
			error: function() {
				$('#show_logo_image').html("<span style='color: #999;'>Chưa có logo</span>");
			}
		});
	}

	// Xem trước ảnh đã chọn trước khi upload
	$('#logo_image').on('change', function(e) {
		var file = e.target.files[0];
		if (file) {
			// Kiểm tra định dạng file
			var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
			if (allowedTypes.indexOf(file.type) === -1) {
				alert('Chỉ chấp nhận file ảnh định dạng JPG, PNG, GIF!');
				$(this).val('');
				return;
			}

			// Kiểm tra kích thước file (tối đa 5MB)
			if (file.size > 5 * 1024 * 1024) {
				alert('Kích thước file không được vượt quá 5MB!');
				$(this).val('');
				return;
			}

			// Hiển thị xem trước
			var reader = new FileReader();
			reader.onload = function(event) {
				$('#show_logo_image').html("<div style='border: 2px dashed #ddd; padding: 10px; text-align: center;'><img src='" + event.target.result + "' style='max-width: 200px; max-height: 150px;' /><br/><small style='color: #666;'>Xem trước - Nhấn 'Upload ảnh logo' để lưu</small></div>");
			};
			reader.readAsDataURL(file);
		} else {
			loadExistingLogo();
		}
	});

	// Tải logo khi trang được mở
	$(document).ready(function() {
		loadExistingLogo();
	});

	$('#btn_upload_logo').on('click', function(e) {
		e.preventDefault();
		
		var fileInput = $('#logo_image')[0];
		var file = fileInput.files[0];
		
		if (!file) {
			alert('Vui lòng chọn file ảnh để upload!');
			return;
		}

		var formData = new FormData();
		formData.append('file', file);
		formData.append('unit_id', $CFG.unit_id);
		formData.append('user_id', $CFG.user_id);

		// Hiển thị trạng thái đang tải
		var $button = $(this);
		var originalText = $button.text();
		$button.text('Đang upload...').prop('disabled', true);
		
		// Hiển thị thanh tiến trình
		$('#show_logo_image').html('<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Đang upload...</div>');

		$.ajax({
			url: $CFG.remote.base_url + '/doing/admin/user/saveUnitLogo',
			type: 'POST',
			data: formData,
			contentType: false,
			processData: false,
			headers: {
				'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
			},
			success: function(response) {
				if (response.success) {
					// Cập nhật logo hiển thị
					$('#show_logo_image').html("<img src='" + response.url + "?v=" + new Date().getTime() + "' style='max-width: 200px; max-height: 150px;' />");
					
					// Cập nhật cấu hình hệ thống để các phần khác có thể sử dụng
					if (typeof $CFG !== 'undefined' && $CFG.sys && $CFG.sys.configs) {
						$CFG.sys.configs.unit_logo_image = response.url;
					}
					
					alert(response.message);
				} else {
					alert('Lỗi: ' + response.message);
					loadExistingLogo(); // Khôi phục logo trước đó
				}
			},
			error: function(xhr, status, error) {
				console.error('Lỗi upload:', error);
				alert('Có lỗi xảy ra khi upload logo!');
				loadExistingLogo(); // Khôi phục logo trước đó
			},
			complete: function() {
				// Đặt lại trạng thái nút
				$button.text(originalText).prop('disabled', false);
				// Xóa file input
				$('#logo_image').val('');
			}
		});
	});
</script>