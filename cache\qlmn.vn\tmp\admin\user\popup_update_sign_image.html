<style>
    .selected_digital_signs {

    }
</style>

<div data-test="null"></div>

<form class="form-horizontal" role="form" style="display: inline-block; width: 100%;"
      xmlns="http://www.w3.org/1999/html">
    <div class="form-group col-md-12">
        <div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
            <div style="font-weight: bold; border-bottom:1px dotted green;"><b>THÔNG TIN CHỮ KÝ SỐ</b></div>
            <div style="padding-top: 5px;">
                <label>Số CCCD chữ ký số:</label>
                <input id="ip_ca_id" class="form-control" type="text" value="">
            </div>
            <label style="padding-top: 5px;">Chữ ký có thể sử dụng:</label>
            <div style="" id="availableDigitalSignCertsBox" data-current-cert-key="">

            </div>
            <div style="padding-top: 5px;">
                <button id="btn_save_ca_id" class="btn btn-success btn-sm">Lưu lại</button>
            </div>
        </div>
    </div>
    <div class="form-group col-md-12">
        <div class="col-md-12"  style="margin: 5px 0;text-align: left; padding: 10px; border: 1px solid #ccc;">
            <div style="font-weight: bold; border-bottom:1px dotted green;"><b>THÔNG TIN CHỮ KÝ ẢNH</b></div>
            <div id="upload_sign">
                <br>
                <p id="show_sign_image">
                </p>
                <div style="display: flex">
                    <input type="file" name="" id="sign_image" accept="image/*" />
                    <button id='btn_upload' class="btn btn-success btn-sm">Upload ảnh chữ ký</button>
                </div>
            </div>
        </div>
    </div>
    <div style="padding-top: 10px; text-align: center;padding-bottom: 20px;">
        <a class="icon-in" style="font-weight: bold;"
           href="{{$CFG.remote.base_url+'/'+'report/csdl_nganh/managerSigned'}}"
           target="_blank">[XEM LỊCH SỬ KÝ SỐ]
        </a>
    </div>

</form>

<script>
    const UpdateUnitSign = (() => {
        'use strict';

        const API_URL = {
            find: $CFG.remote.base_url + '/doing/admin/user/getUnitSign',
            save: $CFG.remote.base_url + '/doing/admin/user/saveUnitSign',
            saveViettelCAID: $CFG.remote.base_url + '/doing/admin/user/saveViettelCaId',
            getAvailableDigitalSignCerts: $CFG.remote.base_url + '/doing/admin/user/getAvailableDigitalSignCerts'
        }

        const DIGITAL_SIGN_NAME_MAPPING = {
            viettel: 'Viettel CA',
            vnpt: 'VNPT SmartCA',
        }

        const $uploadSign = $('#upload_sign');
        const $imagePreviewWrapper = $('#show_sign_image');
        const $cccdInput = $('#ip_ca_id');
        const $availableDigitalSignCertsBox = $('#availableDigitalSignCertsBox');
        const currentDigitalSignCertKey = $availableDigitalSignCertsBox.data('current-cert-key');

        let availableDigitalSignCerts = [];

        const registerListeners = () => {
            $('#cb_sign_type').on('change', findUnitSignHandler);

            $('#btn_upload').on('click', saveUnitSignHandler);

            $('#btn_save_ca_id').on('click', saveViettelCAIDHandler);

            $('#sign_image').on('change', changeImageHandler);

            let timer = null;

            $cccdInput.on('input', () => {
                // Clear the timer if it's already running
                clearTimeout(timer);

                // Start a new timer
                timer = setTimeout(async () => {
                    await getAvailableDigitalSignCerts($cccdInput.val());
                }, 850);
            });
        }

        const saveViettelCAIDHandler = () => {
            const $checkedRadio = $availableDigitalSignCertsBox.find('input[name="selected_digital_cert"]:checked').first();
            const type = $checkedRadio.data('type');
            const key = $checkedRadio.data('key');

            let selectedDigitalCert = {};

            if(availableDigitalSignCerts[type] !== undefined) {
                const findResult = availableDigitalSignCerts[type][key];
                selectedDigitalCert = findResult ? findResult : {};
            }

            const params = {
                viettel_ca_id: $('#ip_ca_id').val(),
                client_id : $("#client_id").val(),
                client_secret: $("#client_secret").val(),
                selected_digital_cert: selectedDigitalCert
            }

            fetch(API_URL.saveViettelCAID, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                },
                body: JSON.stringify(params)
            })
                .then(response => response.json())
                .then(data => {
                    alert(data['message']);
                });
        }

        const findUnitSignHandler = async function (event) {
            

            $uploadSign.show();

            const params = {
                unit_id: $CFG.unit_id,
                user_id: $CFG.user_id,
                getOne: true
            }

            let unitSign;

            try {
                const response = await fetch(API_URL.find, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*',
                        'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                    },
                    body: JSON.stringify(params)
                });

                const json = await response.json();

                unitSign = json['data'];
            } catch(error) {
                unitSign = null;
            }

            if (unitSign !== null) {
                const urlImage = unitSign.path + '?v=' + new Date().getTime();

                $('#show_sign_image').html("<img src= '"+ urlImage +"' width = '200px'  />");
            } else {
                $('#show_sign_image').html("<span>Chưa có chữ ký</span>")
            }
        };

        const saveUnitSignHandler = async () => {
            const formData = new FormData();

            formData.append('user_id', $CFG.user_id);
            formData.append('unit_id', $CFG.unit_id);
            formData.append('file', $('#sign_image')[0].files[0]);

            try {
                const response = await fetch(API_URL.save, {
                    method: 'POST',
                    headers: {
                        'Access-Control-Allow-Origin': '*',
                        'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                    },
                    body: formData
                });

                const json = await response.json();

                alert(json['message']);
            } catch (error) {
                console.log(error);
            } finally {
                $('#sign_image').val('');
            }
        }

        const changeImageHandler = function (e) {
            $imagePreviewWrapper.html("");

            if (e.target.files.length > 0) {
                $imagePreviewWrapper.html("<img src= '"+ URL.createObjectURL(e.target.files[0]) +"' width = '200px'  />");
            }
        }

        const getAvailableDigitalSignCerts = async (cccd) => {
            const response = await fetch(API_URL.getAvailableDigitalSignCerts, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
                },
                method: 'POST',
                body: JSON.stringify({cccd: cccd})
            });

            const jsonResponse = await response.json();

            if(jsonResponse.success) {
                availableDigitalSignCerts = jsonResponse.data;
            }

            // Check if there are any available digital sign certificates
            if (Object.keys(availableDigitalSignCerts).length > 0) {
                // Generate the HTML for the certificates
                let htmlContent = '<ul class="bg-info" style="margin: 0; padding: 10px; border-radius: 8px;">';
                for (let type in availableDigitalSignCerts) {
                    htmlContent += `<li class="bg-primary" style="padding: 2px;">${DIGITAL_SIGN_NAME_MAPPING[type]}</li>`;
                    for (let key in availableDigitalSignCerts[type]) {
                        const cert = availableDigitalSignCerts[type][key];
                        const checked = key === currentDigitalSignCertKey ? 'checked' : '';

                        htmlContent += `<li class="bg-secondary text-bold cert-item" style="padding: 0 5px 0 5px;">
                            <input type="radio" name="selected_digital_cert" data-key="${key}" data-type="${type}" ${checked}>
                            Serial: ${cert['cks_serial']}
                            <p>HSD: ${formatDateForSigns(cert['cks_valid_from'], cert['cks_valid_to'])}</p>
                        </li>`;
                    }
                }
                htmlContent += '</ul>';

                // Update the HTML of the certificates box
                $('#availableDigitalSignCertsBox').html(htmlContent);
            } else {
                // Update the HTML of the certificates box with an error message
                $('#availableDigitalSignCertsBox').html('Không tồn tại chữ ký số cho CCCD trên');
            }

        }

        const init = async () => {
            registerListeners();
            findUnitSignHandler();

            await getAvailableDigitalSignCerts($cccdInput.val());

            window.addEventListener('storage', event => {
                window.location.reload();
            });
        }

        return {
            init
        }
    })();

    UpdateUnitSign.init();
</script>