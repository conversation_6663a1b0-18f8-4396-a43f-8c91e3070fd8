$.unit_group = {
    module: 'unit_group',
    id: '', /*Mã project*/
    init: function(id,project) {
    	var self = this;
    	self.id = id;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*Đ<PERSON>nh nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
                { title:'Mã nhóm', field:'id', width:200, sortable:true, formatter: function(value, row){
                    var group_id_str = (row.group_id)?row.group_id: '---';
                    return value + ' ('+group_id_str+')';
                } },
                { title:'Tên gốc', field:'group_name', width:500, sortable:true },
                { title:'Tên nhóm hiển thị', field:'name', width:500, sortable:true },
                { title:'Thuộc', field:'group_children', width:500, sortable:true, formatter: function(value, row){
                    if(value == 0){
                        value = 'Nhà trẻ';
                    }else if(value == 1){
                        value = 'Mẫu giáo';
                    }else{
                        value = '';
                    }
                    return value;
                } },
                { title:'Trạng thái', field:'status', width:300, sortable:true, formatter: function(value, row){
                    if(value==1){
                        value = 'Hiện';
                    }else{
                        value = "Ẩn";
                    }
                    return value;
                } },
                
            ]],
            {
                onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }, onLoadSuccess:function(data){
                    if($CFG.is_gokids==1) {
                        $('.datagrid-view').height($('.datagrid-view').height() - 30);
                        $('.datagrid-body').height($('.datagrid-body').height() - 30);
                    }
                }
            }
        );
    }, showAddForm: function(callback) { 
    	var self = this;
        $.dm_datagrid.showAddForm(
			{
				module: $CFG.project+'/'+self.module,
				action:'add',
				title:'Thêm mới',
				content: function(element){
					loadForm($CFG.project+'/'+self.module,'add', {id: self.id}, function(resp){
                        $.unit_group.angular(element,resp,function(scope){
                            scope.group = {};
                            scope.group.name="quangvt";
                        })
						// $(element).html(resp);
					})
				}
			},
			function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
       // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
    }, showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
    	if(row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
					content: function(element){
						loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
							$(element).html(resp);
						})
					}
				},
				function(resp){
                    $("#tbl_"+self.module).datagrid('unselectAll');
					$("#tbl_"+self.module).datagrid('reload');
				}
			);
	    }else{
	    	$.messager.alert('Thông báo.', 'Hãy chọn một dòng!');
	    }
       
    }, del: function(){ // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = $CFG.dialog_captcha('unit_group');
        var msg = '<div style = "font-size: 14px">Chắc chắn xóa ?</div>' + captchaForm;

        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="unit_group_captcha"]').val();
                process($CFG.project+'/'+self.module+'/del',{ids: ids, captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.unit_group.del();
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    }, angular: function(element,resp,callback,dialogRef){
        var form = '<div >'+resp+'</div>';
            // $(element).html(form);
        angular.element($(element)).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }
}
