$.cash_book = {
    module: 'cash_book',
    groups: [],
    getList: function (month) {
        var self = this;
        var urls = [$CFG.remote.base_url, 'doing', $CFG.project, self.module, 'list'];
        $.dm_datagrid.init(
            urls.join('/'),
            this.module, /*Đ<PERSON>nh nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    {field: 'ck', checkbox: true, rowspan: 2},
                    {title: 'Điều chỉnh', field: '', colspan: 3, width: 250},
                    {
                        title: 'Tính tiền ăn',
                        field: 'left_unit_id',
                        rowspan: 2,
                        align: 'center',
                        width: 80,
                        formatter: function (value, row, index) {
                            var html = `<label onclick="$.cash_book.cashForm(` + index + `)" class='hover-pointer color-blue btn-over-red' style="margin-bottom:0px;" title="Xem sổ ngày ` + row.date + `">
                                    <i class="fa fa-eye" aria-hidden="true"></i> Xem
                                </label>`;
                            if (!value) {
                                html = `<label class='hover-pointer btn-over-red color-blue' onclick="$.cash_book.cashForm(` + index + `)" style="margin-bottom:0px;" title="Tính tiền ăn ngày ` + row.date + `">
                                    <span class="glyphicon glyphicon-edit" ></span> Tính tiền ăn
                                </label>`;
                            }
                            return html;
                        }
                    },
                    {
                        title: '', field: 'unit_id', rowspan: 2, width: 50, formatter: function (value, row, index) {
                            var html = ``;
                            if (row.left_unit_id) {
                                html = `
                                <label onclick="$.cash_book.del(` + index + `)" class='hover-pointer color-red btn-over-red' style="margin-bottom:0px;" title="Xóa sổ từ ngày ` + row.date + `">
                                    <span class="hover-pointer glyphicon glyphicon-trash"></span> Xóa
                                </label>`;
                            }
                            return html;
                        }
                    }
                ], [
                {title: 'Ngày', field: 'date', width: 120},
                {
                    title: 'Nhóm trẻ', field: 'grade', width: 250, formatter: function (value, row) {
                        if (typeof row.menu_plannings === 'string') {
                            return row.menu_plannings;
                        } else {
                            var html = [];
                            angular.forEach(row.menu_plannings, function (grade, index) {
                                angular.forEach($.cash_book.groups, function (item, i) {
                                    if (item.id == grade.group_id) {
                                        html.push(item.name);
                                        return;
                                    }
                                })
                            })
                            return html.join(', ');
                        }
                    }
                },
                {
                    title: 'Tên thực đơn theo nhóm trẻ',
                    field: 'grade_ma',
                    width: 250,
                    formatter: function (value, row) {
                        var html = [];
                        if (typeof row.menu_plannings === 'object') {
                            angular.forEach(row.menu_plannings, function (grade, index) {
                                html.push(grade.row.mathucdon)
                            })
                        }
                        return html.join(', ');
                    }
                }/*,
                    { title:'', field:'', width:200, formatter: function (value, row) {
                        value = `<button type="button" class="btn-link btn-outline" onclick="phieukechoShowForm(`+row.date+`)">
                                    <span class="glyphicon glyphicon-copy"></span> Phiếu kê chợ
                                </button>`;
                        return value;
                    } }*/
            ]
            ],
            {
                onDblClickRow: function (rowIndex, rowData) {
                    self.cashForm(rowIndex);
                },
                queryParams: {month: month},
                pageSize: 100
            }
        );
    },
    cashForm: function (index, date, callback) {
        var self = this;
        if (!date) {
            var data = $('#tbl_cash_book').datagrid('getData');

            if (!data.rows) {
                alert('Không tìm thấy dữ liệu.');
                return;
            }
            var row = data.rows[index];
            if (!row) {
                alert('Không tìm thấy dữ liệu.');
            }
            date = row.date;
        }
        $.cash_book.date = date;
        var dialog = null;
        var title = 'Sổ tính tiền ăn ngày ' + date;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: title,
                size: size.wide,
                fullScreen: true,
                showButton: false,
                content: function (element, dialogRef) {
                    dialog = dialogRef;
                    process('dinhduong/cash_book/cash_show', {date: date, async: true}, function (resp) {
                        var data = resp.data;
                        var html = resp.html;
                        if (!data) {
                            alert('Lỗi khi tải dữ liệu!');
                            dialogCloseAll();
                            return;
                        }
                        $.cash_book.angular(element, html, function (scope) {
                            scope.cash_book.selected.date = date;
                            scope.cash_book.menu_plannings = data.menu_plannings;
                            scope.cash_book.markets = data.markets;
                            scope.cash_book.exports = data.exports;
                            scope.cash_book.date = date;
                            scope.cash_book.year = data.year;
                            scope.cash_book.day = data.day;
                            scope.cash_book.surplus = resp.surplus;
                            scope.cash_book.tonghop = resp.tonghop;
                            scope.cash_book.val_type = 0;
                            scope.cash_book.is_view = resp.is_view;
                            scope.cash_book.val_type_month = 0;
                            var date1 = date.split("/");
                            date1 = date1[2] + "-" + date1[1] + "-" + date1[0];
                            scope.cash_book.date1 = date1;
                            /*Tính tiêu chuẩn trong ngày*/
                            scope.cash_book.group_services = data.services;
                            scope.cash_book.total_services = data.total_services;
                            var menu_plannings = {};
                            scope.cash_book.groups = {};
                            angular.forEach(data.groups, function (group, id) {
                                var group_id = group.group_id;
                                if(group.group_id == null) {
                                    group_id = group.id;
                                }
                                scope.cash_book.groups[group_id] = group;
                            });
                            if (scope.cash_book.menu_plannings[0]) {
                                angular.forEach(scope.cash_book.menu_plannings, function (menu_planning, ind) {
                                    menu_plannings[menu_planning.group_id] = menu_planning;
                                });
                                scope.cash_book.menu_plannings = menu_plannings;
                            }
                            menu_plannings = {};
                            if (!resp.is_view) {
                                angular.forEach(scope.cash_book.menu_plannings, function (menu_planning, ind) {
                                    menu_plannings[menu_planning.group_id] = menu_planning;
                                });
                                scope.cash_book.menu_plannings = menu_plannings;
                            } else {
                                menu_plannings = scope.cash_book.menu_plannings;
                            }
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                group_id = parseInt(group_id);
                                var menu_planning = menu_plannings[group_id];
                                if (!menu_planning) {
                                    menu_planning = {
                                        row: {
                                            sotre: 0,
                                            tien1tre: 0
                                        },
                                        group_id: group.group_id
                                    }
                                }
                                scope.cash_book.groups[group_id].row = menu_planning.row;
                                menu_plannings[group_id] = menu_planning;
                                if (menu_planning.row.sotre > 0) {
                                    scope.cash_book.groups[group_id].selected = true;
                                } else {
                                    scope.cash_book.groups[group_id].selected = false;
                                    if(scope.cash_book.groups[group_id].status == 0) {
                                        delete scope.cash_book.groups[group_id];
                                    }
                                }
                            });
                            if (!resp.is_view) {
                                scope.cash_book.imports = [];
                                scope.cash_book.btn_save.enable = false;
                                if (count(data.xuatkho_thieu) > 0) {
                                    angular.forEach(data.xuatkho_thieu, function (foods, warehouse_id) {
                                        angular.forEach(foods, function (food, food_id) {
                                            angular.forEach(food.exports, function (storage) {
                                                food.price = storage.price;
                                            });
                                        });
                                    });
                                    $.cash_book.alertOptionStorage_import(scope, data.xuatkho_thieu, function () {
                                        scope.cash_book.surplusFrom(resp.surplus);
                                        scope.cash_book.processData(scope);
                                    });
                                } else {
                                    scope.cash_book.surplusFrom(resp.surplus);
                                    scope.cash_book.processData(scope);
                                }
                            } else {
                                scope.cash_book.btn_save.enable = true;
                                scope.cash_book.processData(scope);
                            }
                            setTimeout(function () {
                                $.cash_book.configTable(element);
                            }, 700);
                            $(window).resize(function () {
                                $.cash_book.configTable(element);
                            });

                            scope.cash_book.foods = {};
                            scope.cash_book.showFilter = false;
                            angular.forEach(scope.cash_book.exports, function (warehouses) {
                                angular.forEach(warehouses.foods, function (food) {
                                    scope.cash_book.foods[food.food_id] = {
                                        name: food.name,
                                        checked: true
                                    };
                                })
                            });

                            scope.cash_book.getFoodCheck = function(){
                                var arrayChecked = {};
                                angular.forEach(scope.cash_book.foods, function (food, id) {
                                    if(food.checked){
                                        arrayChecked[id] = true;
                                    }else {
                                        delete arrayChecked[id];
                                    }
                                });
                                return JSON.stringify(arrayChecked);
                            };

                            scope.cash_book.save_callback = callback;
                        });
                    });
                }
            }
        );
    }, showFormChonrauqua: function (scope) {
        var self = this;
        var dialog = null;
        var food_ids = [];
        angular.forEach(scope.cash_book.exports, function (warehouse, warehouse_id) {
            if (!warehouse) {
                return;
            }
            angular.forEach(warehouse.foods, function (food, fid) {
                food_ids.push(food.food_id);
            })
        })
        angular.forEach(scope.cash_book.markets.foods, function (food, fid) {
            food_ids.push(food.food_id)
        })
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + self.module,
                action: 'rauqua',
                title: 'Chọn rau quả',
                size: size.wide,
                fullScreen: false,
                // showButton: false,
                // closable: false,
                content: function (element, dialogRef) {
                    dialog = dialogRef;
                    loadForm($CFG.project + '/' + self.module, 'rauqua', {ids: food_ids}, function (resp) {
                        $(element).html(resp);
                    });
                }
            },
            function (resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                }
            }
        );
    }, configTable: function (element) {
        /*cấu hình thanh cuộn cho bảng thực phẩm*/
        var h_form = parseInt($('body').css('height').replace('px', '')) - 75;
        // console.log(h_form)
        var h_head = parseInt($('.table.tbl-container > .thead').css('height').replace('px', ''));
        var h_foot = parseInt($('.table.tbl-container > .tfoot').css('height').replace('px', ''));
        if (h_form - h_head - h_foot < 250) {
            h_form = h_form + 250;
            // $('#frm-add-storage_export > .frm-layout-container').css({height: h_form+10});
        } else {
        }
        $('#frm-add-storage_export > .frm-layout-container').css({height: h_form - 20});
        /* Thiết đặt chiều rộng cố định cho cột các nhóm trẻ*/
        if (element) {
            var colLeft = $(element).find('.form-col-left');
            var w = colLeft.css('width');
            if (w) {
                w = parseInt(w.replace('px', ''));
                colLeft.find('.group-grade-container').css({width: w - 535})
            }
        }
        /* Thiết lập chiều cao cho danh sách món ăn */
        $('.table.tbl-container > .tbody > tr > td').css({height: h_form - h_head - h_foot - 10});
        /* Bắt sự kiện kéo thanh cuộn ngang */
        var footerRight = $('table.tbl-container > .tfoot .container-right > .local-scroll-container');
        var autoscroll = $('table.tbl-container .scroll-container-x');
        if (footerRight.length) {
            footerRight.scroll(function (e) {
                var left = footerRight.children().position().left;
                autoscroll.children().css({'margin-left': left});
            })
        }
    }, chosingImport(scope, food) {

    }, alertOptionStorage_import(scope, xuatkho_thieu, callback) {
        var self = this;
        var dialog = null;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + self.module,
                action: 'option_import',
                title: 'Xử lý thực phẩm không đủ xuất kho',
                size: size.wide,
                fullScreen: true,
                showButton: false,
                closable: false,
                content: function (element, dialogRef) {
                    dialog = dialogRef;
                    loadForm($CFG.project + '/' + self.module, 'option_import', {}, function (resp) {
                        $.cash_book.angular(element, resp, function (scope) {
                            scope.cash_book.xuat_thieu = xuatkho_thieu;
                            angular.forEach(scope.cash_book.xuat_thieu, function (foods, warehouse_id) {
                                angular.forEach(foods, function (food, food_id) {
                                    food.quantity_import = $['-'](food.thucmuatheodvt, food.inventory);
                                    food.quantity_import_root = food.quantity_import;
                                })
                            })

                        });
                    });
                },
                buttons: [
                    {
                        id: 'btn-ok',
                        icon: 'glyphicon glyphicon-ok',
                        label: 'Áp dụng',
                        cssClass: 'btn-primary',
                        action: function (dialogRef) {
                            scope.$apply(function () {
                                scope.cash_book.applyOptionStorage_import(dialogRef, function () {
                                    callback(scope);
                                    setTimeout(function () {
                                        $.cash_book.configTable();
                                    }, 400)
                                });
                            });
                        }
                    },
                    {
                        id: 'btn-cancel',
                        icon: 'glyphicon glyphicon-ok',
                        label: 'Bỏ qua',
                        cssClass: '',
                        action: function (dialogRef) {
                            dialogCloseAll();
                        }
                    }
                ]
            },
            function (resp) {
                // if (typeof callback === 'function') {
                //     callback(resp);
                // } else {
                //     $("#tbl_"+self.module).datagrid('reload');
                // }
            }
        );
    },
    printCash_tiencho: function () {
        var date = $('#ngay_dau').val();
        var type = '';
        process('dinhduong/cash_book/' + type + '?date=', function (resp) {

        })
    },
    init: function (special_categories) {
        if (!special_categories) {
            process('dinhduong/cash_book/special_categories', {}, function (resp) {
                $.cash_book.groups = resp.groups;
                $.cash_book.warehouses = resp.warehouses;
                $.cash_book.measures = resp.measures;
                $.cash_book.menuinforations = resp.menuinforations;
                $.cash_book.months = resp.months;
                $.cash_book.initAngular();
            }, null, false);
        } else {
            $.cash_book.groups = special_categories.groupss;
            $.cash_book.warehouses = special_categories.warehouses;
            $.cash_book.measures = special_categories.measures;
            $.cash_book.menuinforations = special_categories.menuinforations;
            $.cash_book.months = special_categories.months;
            var tmp_warehouses = {};
            angular.forEach($.cash_book.warehouses, function (warehouse, index) {
                tmp_warehouses[warehouse.id] = warehouse;
            });
            $.cash_book.warehouses = tmp_warehouses;
            $.cash_book.initAngular();
        }
    }, initAngular: function () {
        var self = this;
        setTimeout(function () {
            angular.element($('#mainContentController')).scope().$apply(function (scope) {
                scope.cash_book = {
                    groups: $.cash_book.groups,
                    warehouses: $.cash_book.warehouses,
                    measures: $.cash_book.measures,
                    menuinforations: $.cash_book.menuinforations,
                    months: $.cash_book.months,
                    btn_save: {enable: true},
                    suppliers: $.cash_book.suppliers,
                };
                scope.cash_book.keysearch_date = dateboxOnSelect(new Date());
                if (count(scope.cash_book.warehouses) >= 2) {
                    scope.cash_book.warehouses[2].selected = true;
                }
                scope.cash_book.services = [];
                scope.cash_book.selected = {
                    month: null,
                    warehouse: 1,
                    warehouse_ids: {
                        1: true,
                        2: true
                    },
                    warehouse_print: {
                        1: false,
                        2: true
                    }
                };
                scope.cash_book.school_points = {};
                for (var i = 1; i <= $CFG.school_points; i++) {
                    scope.cash_book.school_points[i] = i;
                }
                /*  Tông tiền xuất ăn và tiêu chuẩn trong ngày của các nhóm trẻ*/
                /*  Nếu group_id == undefined là tính cho tổng tất cả */
                scope.cash_book.sumTotal_daChiTrongNgay = function (group_id) {
                    var rs = 0;
                    scope.cash_book.dachitrongngay || (scope.cash_book.dachitrongngay = {all: 0, groups: {}});
                    // console.log(scope.cash_book.dachitrongngay);
                    // return;
                    if (group_id == undefined) {
                        /*Tiền chợ*/
                        if (scope.cash_book.markets) {
                            rs += Number(scope.cash_book.markets.total.all);
                        }
                        /*Tiền xuất kho*/
                        angular.forEach(scope.cash_book.exports, function (item, index) {
                            if (item + '' != 'null') {
                                rs += Number(item.total.all);
                            }
                        });
                        if (scope.cash_book.total_services) {
                            rs += scope.cash_book.total_services.thanhtien;
                            // rs += scope.cash_book.services_total.total; /*Cộng thêm tiền dịch vụ*/
                            // angular.forEach(scope.cash_book.services, function (service, index) {
                            //     rs += Number(service.price);
                            // })
                        }
                    } else {
                        /*Tiền chợ*/
                        if (scope.cash_book.markets && scope.cash_book.markets.total.groups[group_id]) {
                            rs += Number(scope.cash_book.markets.total.groups[group_id]);
                        }
                        /*Tiền xuất kho*/
                        angular.forEach(scope.cash_book.exports, function (item, index) {
                            if (item + '' != 'null') {
                                if (item.total.groups[group_id]) {
                                    rs += Number(item.total.groups[group_id]);
                                }
                            }
                        });
                        if (scope.cash_book.services_total) {
                            rs += scope.cash_book.total_services.groups[group_id];
                            // rs += scope.cash_book.services_total.groups[group_id]; /*Cộng thêm tiền dịch vụ*/
                            // angular.forEach(scope.cash_book.services, function (service, index) {
                            //     rs += Number(service.price);
                            // })
                        }
                    }
                    if (group_id == undefined) {
                        scope.cash_book.dachitrongngay.all = rs;
                    } else {
                        scope.cash_book.dachitrongngay.groups[group_id] = rs;
                    }
                    return rs;
                };
                scope.getMenu_infomationByMeal_key = function (meal_key) {
                    var rs = {};
                    var meal_define = 'buatrua';
                    if (meal_key == 1) {
                        meal_define = 'buasang';
                    } else if (meal_key == 3) {
                        meal_define = 'buatoi';
                    }
                    for (var i in scope.cash_book.menuinforations) {
                        if (scope.cash_book.menuinforations[i] == meal_define) {
                            rs = scope.cash_book.menuinforations[i];
                            break;
                        }
                    }
                    return rs;
                };
                scope.onChange_warehouseSelected = function (index) {
                    var wh = scope.cash_book.getWarehouse_printSelected();
                    if (wh.length == 0) {
                        scope.cash_book.selected.warehouse_ids[index] = true;
                    } else {
                        scope.cash_book.processData();
                    }
                };
                scope.onChange_groupSelected = function (index) {
                    var grs = scope.cash_book.getGroupsSelected();
                    if (grs.length == 0) {
                        scope.cash_book.groups[index].selected = true;
                    } else {
                        scope.cash_book.processData();
                    }
                };
                scope.cash_book.form_theodoichatluongbuaan = function () {
                    scope.cash_book.tmp_theodoichatluongbuaan = 'form_theodoichatluongbuaan.htm';
                    $.dm_datagrid.showEditForm(
                        {
                            module: $CFG.project + '/menu_adjust',
                            action: 'edit',
                            title: 'Chọn biểu in',
                            size: size.small,
                            showButton: false,
                            content: function (element) {
                                loadForm('tmp/' + $CFG.project + '/' + self.module, 'popup_theodoichatluongbuaan.html', {}, function (resp) {
                                    $(element).html(scope.compile(resp, scope));
                                });

                            }
                        }
                    );
                };
                scope.cash_book.sumChicho = function (group_id) {
                    var rs = 0;
                    var group_ids = [];
                    angular.forEach(scope.cash_book.menu_plannings, function (menu_planning, ind) {
                        if (menu_planning.selected) {
                            group_ids.push(menu_planning.group_id);
                        }
                    });
                    angular.forEach(scope.cash_book.markets, function (market, warehouse_id) {
                        if (!scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            return;
                        }
                        if (group_id) {
                            rs = $['+'](rs, market.total.groups[group_id]);
                        } else {
                            angular.forEach(market.total.groups, function (value, group_id) {
                                if (in_array(group_id, group_ids)) {
                                    rs = $['+'](rs, value);
                                }
                            })
                        }
                    });
                    return rs;
                };
                scope.cash_book.sumXuatan = function (group_id) {
                    var rs = 0;
                    if (scope.cash_book.tonghop) {
                        if (scope.cash_book.tonghop.xuatan) {
                            var group_ids = scope.cash_book.getGroupsSelected(true);
                            if (group_id) {
                                if (scope.cash_book.tonghop.xuatan.groups[group_id] != undefined) {
                                    rs = scope.cash_book.tonghop.xuatan.groups[group_id].sotre;
                                }
                            } else {
                                angular.forEach(scope.cash_book.groups, function (grp, grp_id) {
                                    // angular.forEach(scope.cash_book.tonghop.xuatan.groups, function (group, group_id) {
                                    var group = scope.cash_book.tonghop.xuatan.groups[grp_id];
                                    if (in_array(grp_id, group_ids) && group) {
                                        rs = $['+'](rs, group.sotre);
                                    }
                                })
                            }
                        }
                    }
                    return rs;
                };
                scope.cash_book.sumTieuchuan = function (group_id) {
                    var rs = 0;
                    if (scope.cash_book.tonghop) {
                        if (scope.cash_book.tonghop.tieuchuan) {
                            var group_ids = scope.cash_book.getGroupsSelected(true);
                            if (group_id) {
                                if (scope.cash_book.tonghop.xuatan.groups[group_id] != undefined) {
                                    rs = scope.cash_book.tonghop.tieuchuan.groups[group_id].thanhtien;
                                }
                            } else {
                                angular.forEach(scope.cash_book.groups, function (grp, grp_id) {
                                    var group = scope.cash_book.tonghop.tieuchuan.groups[grp_id];
                                    if (in_array(grp_id, group_ids) && group) {
                                        // if ()
                                        rs = $['+'](rs, group.thanhtien);
                                    }
                                })
                            }
                        }
                    }
                    return rs;
                };

                scope.cash_book.cash_save = function (date) {
                    var data = $.cash_book.getDataForm(scope);
                    process('dinhduong/cash_book/cash_save', {data: data, date: date, async: true}, function (resp) {
                        if(resp.result == 'success'){
                            $.menu_report.init(null, function () {
                                dialogClose();
                                $.cash_book.cashForm(null, $.cash_book.date);
                            });
                        }
                    })
                };
                scope.onChange_surplus = function () {
                    scope.cash_book.processData(scope);
                };
                scope.cash_book.surplusShow = function () {
                    scope.cash_book.surplusFrom(scope.cash_book.surplus);
                };
                scope.cash_book.surplusFrom = function (surplus) {
                    var self = $.cash_book;
                    var dialog = null;
                    surplus.begin || (surplus.begin = {});
                    if (!surplus.first_day) {
                        return;
                    }
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project + '/' + self.module,
                            action: 'surplus',
                            title: 'Thay đổi số dư đầu tháng',
                            size: 800,
                            fullScreen: false,
                            showButton: false,
                            closable: true,
                            content: function (element, dialogRef) {
                                dialog = dialogRef;
                                loadForm('tmp/' + $CFG.project + '/' + self.module, 'surplus.html', {}, function (resp) {
                                    $.cash_book.angular(element, resp, function (scope) {
                                        scope.cash_book.surplus = surplus;
                                    });
                                });
                            }
                        },
                        function (resp) {
                            if (typeof callback === 'function') {
                                callback(resp);
                            } else {
                                $("#tbl_" + self.module).datagrid('reload');
                            }
                        }
                    );
                };
                scope.cash_book.sync_market = function (markets) {
                    var rs = {};
                    var total = {all: 0, groups: {}};
                    angular.forEach(markets, function (market, warehouse_id) {
                        var total_in_wh = {all: 0, groups: {}};
                        angular.forEach(market.foods, function (food, food_id) {
                            if (!rs[food_id]) {
                                food['groups'] || (food['groups'] = {});
                                food['groups'][food['group']] = food['thucmuatheodvt'];
                                food['total'] = food['thucmuatheodvt'];
                                food['money'] = food['thucmuatheodvt'] * food['price'];
                                rs[food_id] = food;
                            } else {
                                rs[food_id]['groups'][food['group']] || (rs[food_id]['groups'][food['group']] = 0);
                                rs[food_id]['groups'][food['group']] += food['thucmuatheodvt'];
                                rs[food_id]['total'] += food['thucmuatheodvt'];
                                rs[food_id]['money'] += food['thucmuatheodvt'] * food['price'];
                            }
                            total['groups'][food['group']] || (total['groups'][food['group']] = 0);
                            total['groups'][food['group']] += food['thucmuatheodvt'] * food['price'];
                            total['all'] += food['thucmuatheodvt'] * food['price'];
                            total_in_wh.all += food['thucmuatheodvt'] * food['price'];
                            total_in_wh.groups[food.group] || (total_in_wh.groups[food.group] = 0);
                            total_in_wh.groups[food.group] += food['thucmuatheodvt'] * food['price'];
                        });
                        market.total = total_in_wh;
                    });
                    var dicho = {foods: rs, total: total};
                    return dicho;
                };
                // scope.cash_book.chenhlechcuoingay = function (group_id) {
                //     if (!scope.cash_book.is_view) {
                //         var rs = 0;
                //         scope.cash_book.surplus || (scope.cash_book.surplus = {});
                //         if (scope.cash_book.surplus.begin) {
                //             if (group_id == undefined) {
                //                 var duocchitrongngay = scope.cash_book.sumTieuchuantrongngay() + scope.cash_book.sum(scope.cash_book.surplus.begin);
                //                 rs = duocchitrongngay - scope.cash_book.sumTotal_daChiTrongNgay();
                //             } else {
                //                 scope.cash_book.surplus.begin[group_id] || (scope.cash_book.surplus.begin[group_id] = 0);
                //                 var duocchitrongngay = scope.cash_book.sumTieuchuantrongngay(group_id) + scope.NaN(scope.cash_book.surplus.begin[group_id], 0);
                //                 rs = duocchitrongngay - scope.cash_book.sumTotal_daChiTrongNgay(group_id);
                //                 if (typeof rs === 'number') {
                //                     scope.cash_book.surplus.end[group_id] = rs;
                //                 }
                //             }
                //         }
                //         return rs;
                //     }
                // };
                /*  Tính hàng "Xuất ăn và tiêu chuẩn trong ngày" tổng tiêu chuẩn ăn trong ngày*/
                /*  group_id == undefined tính tổng tất cả các nhóm trẻ cho cột tổng->thành tiền*/
                /*  group_id != undefined tính tổng cột thành tiền theo khối*/
                scope.cash_book.sumTieuchuantrongngay = function (group_id) {
                    var rs = 0;
                    angular.forEach(scope.cash_book.menu_plannings, function (menu_planning, i) {
                        if (typeof group_id !== 'undefined' && menu_planning.group_id !== group_id) {

                        } else {
                            rs += scope.parseInt(menu_planning.row.sotre) * scope.parseInt(menu_planning.row.tien1tre);
                        }
                    });
                    return rs;
                };

                scope.cash_book.menu_planningNotUncheckall = function (index, menu_plannings) {
                    var kt = false;
                    angular.forEach(menu_plannings, function (menu_planning) {
                        if (menu_planning.selected) {
                            kt = true;
                            return;
                        }
                    });
                    if (!kt) {
                        menu_plannings[index].selected = true;
                    }
                };
                scope.cash_book.sum = function (arr) {
                    var rs = 0.0;
                    angular.forEach(arr, function (el, index) {
                        if (typeof el != 'object') {
                            if (el == '') {
                                el = 0.0;
                            }
                            rs += parseInt(el);
                        } else {
                            rs += scope.cash_book.sum(el);
                        }
                    });
                    return rs;
                };
                /* Tải lại bảng danh sách */
                scope.cash_book.reloadDatagrid = function () {
                    var queryParams = $('#tbl_' + self.module).datagrid('options').queryParams;
                    if (queryParams.month != scope.cash_book.selected.month.id) {
                        queryParams.month = scope.cash_book.selected.month.id;
                        $('#tbl_' + self.module).datagrid('load', queryParams);
                    }
                };
                scope.cash_book.getObject = function (ob, value, key) {
                    if (typeof ob !== 'object' || value === undefined) {
                        return {};
                    }
                    key || (key = 'id');
                    rs = {};
                    angular.forEach(ob, function (item, inexd) {
                        if (item[key] == value) {
                            rs = item;
                            return;
                        }
                    });
                    return rs;
                };
                scope.cash_book.tien_bo_troCalc = function () {
                    scope.cash_book.tien_bo_tro = {
                        groups: {},
                        total: 0
                    };
                    angular.forEach(scope.cash_book.menu_plannings, function (mp) {
                        if (mp.id && mp.row) {
                            if (scope.cash_book.groups[mp.group_id]) {
                                if (scope.cash_book.groups[mp.group_id].selected) {
                                    mp.row.tien_bo_tro || (mp.row.tien_bo_tro = 0);
                                    mp.row.tien_bo_tros || (mp.row.tien_bo_tros = {1: mp.row.tien_bo_tro});
                                    mp.row.tien_bo_tro = 0;
                                    angular.forEach(mp.row.tien_bo_tros, function (tien, point) {
                                        mp.row.tien_bo_tro = $['+'](mp.row.tien_bo_tro, tien);
                                        //if(!scope.cash_book.is_view) {
                                            scope.cash_book.surplus.ends || (scope.cash_book.surplus.ends = {});
                                            scope.cash_book.surplus.ends[mp.group_id] || (scope.cash_book.surplus.ends[mp.group_id] = {});
                                            scope.cash_book.surplus.ends[mp.group_id][point] || (scope.cash_book.surplus.ends[mp.group_id][point] = 0);
                                            scope.cash_book.surplus.ends[mp.group_id][point] = $['+'](scope.cash_book.surplus.ends[mp.group_id][point], tien);
                                        //}
                                    });
                                    scope.cash_book.tien_bo_tro.groups[mp.group_id] || (scope.cash_book.tien_bo_tro.groups[mp.group_id] = 0);
                                    scope.cash_book.tien_bo_tro.groups[mp.group_id] = $['+'](scope.cash_book.tien_bo_tro.groups[mp.group_id], mp.row.tien_bo_tro);
                                    scope.cash_book.tien_bo_tro.total = $['+'](scope.cash_book.tien_bo_tro.total, mp.row.tien_bo_tro);
                                }
                            }
                        }
                    });
                    //if(!scope.cash_book.is_view) {
                        angular.forEach(scope.cash_book.tien_bo_tro.groups, function (value, group_id) {
                            scope.cash_book.surplus.end[group_id] || (scope.cash_book.surplus.end[group_id] = 0);
                            scope.cash_book.surplus.end[group_id] = $['+'](scope.cash_book.surplus.end[group_id], value);
                        });
                    //}
                    scope.cash_book.surplus.total_end = 0;
                    angular.forEach(scope.cash_book.surplus.end,  function (value, group_id) {
                        if (scope.cash_book.groups[group_id]) {
                            if (scope.cash_book.groups[group_id].selected) {
                                scope.cash_book.surplus.total_end = $['+'](scope.cash_book.surplus.total_end, value);
                            }
                        }
                    });
                };
                scope.cash_book.processData = function () {
                    if(scope.cash_book.isPointTogether()){
                        return scope.cash_book.processDataPoint();
                    }
                    var tmp_services_total = {groups: {}, total: 0};
                    var tmp_groups = {};
                    var group_ids = [];
                    var services = {};
                    scope.cash_book.tieuchuan = {
                        groups: {},
                        total: {
                            sotre: 0,
                            thanhtien: 0
                        }
                    };
                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        var menu_planning = scope.cash_book.menu_plannings[group_id];
                        if (group.selected) {
                            menu_planning.row.sotre = parseInt(menu_planning.row.sotre);
                            menu_planning.row.tien1tre = parseInt(menu_planning.row.tien1tre);
                            var tmp_services = menu_planning.services;
                            if (!tmp_services) {
                                tmp_services = scope.cash_book.group_services;
                            }
                            angular.forEach(tmp_services, function (service, index) {
                                var service_id = service.service_name + '_' + service.price;
                                var service_name = service.service_name;
                                service_name || (service_name = service.name);
                                tmp_services_total.groups[group_id] || (tmp_services_total.groups[group_id] = 0);
                                tmp_services_total.groups[group_id] = $['+'](tmp_services_total.groups[group_id], $['*'](service.price, menu_planning.row.sotre));
                                tmp_services_total.total += service.price * menu_planning.row.sotre;
                                services[service_id] || (services[service_id] = {
                                    groups: {},
                                    name: service_name,
                                    price: service.price,
                                    sotre: 0,
                                    thanhtien: 0
                                });
                                services[service_id].sotre = $['+'](services[service_id].sotre, menu_planning.row.sotre);
                                services[service_id].thanhtien = $['+'](services[service_id].thanhtien, $['*'](service.price, menu_planning.row.sotre));
                                services[service_id].groups[group_id] || (services[service_id].groups[group_id] = {
                                    price: service.price,
                                    sotre: 0,
                                    thanhtien: 0
                                });
                                services[service_id].groups[group_id].sotre = $['+'](services[service_id].groups[group_id].sotre, menu_planning.row.sotre);
                                services[service_id].groups[group_id].thanhtien = $['+'](services[service_id].groups[group_id].thanhtien, $['*'](service.price, menu_planning.row.sotre));
                            });

                            tmp_groups[group_id] || (tmp_groups[group_id] = {sotre: 0, thanhtien: 0});
                            tmp_groups[group_id].sotre = menu_planning.row.sotre;
                            tmp_groups[group_id].thanhtien = menu_planning.row.sotre * menu_planning.row.tien1tre;
                            group_ids.push(group_id);
                        }
                    });
                    scope.cash_book.total_services = tmp_services_total;
                    scope.cash_book.services = services;
                    scope.cash_book.luykedauthang = {
                        chicho: {groups: {}, total: 0},
                        chikho: {groups: {}, total: 0},
                        tongchi: {groups: {}, total: 0}
                    };
                    angular.forEach(scope.cash_book.tonghop.chicho, function (chicho, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                // angular.forEach(chicho.groups, function (value,group_id) {
                                var value = 0;
                                if (typeof chicho.groups[group_id] === 'object') {
                                    value = chicho.groups[group_id].thanhtien;
                                } else {
                                    value = chicho.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.chicho[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.chicho.groups[group_id] || (scope.cash_book.luykedauthang.chicho.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.chicho.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.chicho.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(scope.cash_book.tonghop.chikho, function (chikho, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                var value = 0;
                                if (typeof chikho.groups[group_id] === 'object') {
                                    value = chikho.groups[group_id].thanhtien;
                                } else {
                                    value = chikho.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.chikho[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.chikho.groups[group_id] || (scope.cash_book.luykedauthang.chikho.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.chikho.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.chikho.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(scope.cash_book.tonghop.tongchi, function (tongchi, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                var value = 0;
                                if (typeof tongchi.groups[group_id] === 'object') {
                                    value = tongchi.groups[group_id].thanhtien;
                                } else {
                                    value = tongchi.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.tongchi[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.tongchi.groups[group_id] || (scope.cash_book.luykedauthang.tongchi.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.tongchi.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(group_ids, function (group_id, ind) {
                        if (scope.cash_book.luykedauthang.chicho.groups[group_id]) {
                            scope.cash_book.luykedauthang.chicho.total = $['+'](scope.cash_book.luykedauthang.chicho.total, scope.cash_book.luykedauthang.chicho.groups[group_id]);
                        }
                        if (scope.cash_book.luykedauthang.chikho.groups[group_id]) {
                            scope.cash_book.luykedauthang.chikho.total = $['+'](scope.cash_book.luykedauthang.chikho.total, scope.cash_book.luykedauthang.chikho.groups[group_id]);
                        }
                        if (scope.cash_book.luykedauthang.tongchi.groups[group_id]) {
                            scope.cash_book.luykedauthang.tongchi.total = $['+'](scope.cash_book.luykedauthang.tongchi.total, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                        }
                    });
                    if (scope.cash_book.tonghop.dichvu) {
                        // = $['+'](scope.cash_book.tonghop.dichvu.total,scope.cash_book.luykedauthang.tongchi.total);
                        angular.forEach(scope.cash_book.tonghop.dichvu.groups, function (thanhtien, group_id) {
                            if (in_array(group_id, group_ids)) {
                                scope.cash_book.luykedauthang.tongchi.groups[group_id] = $['+'](thanhtien, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                                scope.cash_book.luykedauthang.tongchi.total = $['+'](thanhtien, scope.cash_book.luykedauthang.tongchi.total);
                            }
                        });
                    }
                    /*Tính đã chi trong ngày*/
                    scope.cash_book.dachitrongngay = {
                        total: 0,
                        groups: {}
                    };
                    var markets = {};
                    angular.forEach(scope.cash_book.markets, function (market, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            markets[warehouse_id] || (markets[warehouse_id] = {
                                foods: {},
                                total: {
                                    all: 0,
                                    groups: {},
                                    tong: {}
                                }
                            });
                            if (market) {
                                angular.forEach(market.foods, function (food, food_id) {
                                    var fd = {
                                        name: food.name,
                                        measure_id: food.measure_id,
                                        price: food.price,
                                        supplier: food.supplier,
                                        thucmuatheodvt: 0,
                                        groups: {}
                                    };
                                    angular.forEach(scope.cash_book.groups, function (_group, group_id) {
                                        var group = food.groups[group_id];
                                        if (!group) {
                                            group = {thucmuatheodvt: 0};
                                        }
                                        // angular.forEach(food.groups, function (group, group_id) {
                                        if (in_array(group_id, group_ids)) {
                                            fd.groups[group_id] = group;
                                            fd.thucmuatheodvt = $['+'](fd.thucmuatheodvt, group.thucmuatheodvt);
                                            markets[warehouse_id].total.groups[group_id] || (markets[warehouse_id].total.groups[group_id] = 0);
                                            markets[warehouse_id].total.groups[group_id] = $['+'](markets[warehouse_id].total.groups[group_id], $['*'](food.price, group.thucmuatheodvt));
                                        }
                                    });
                                    if (count(fd.groups) > 0) {
                                        markets[warehouse_id].foods[food_id] = fd;
                                    }
                                });
                            }
                        }
                    });
                    var checkFirstDay = scope.menu_report.cash_books[scope.menu_report.cash_books.length - 1].date == $.cash_book.date;
                    if (checkFirstDay) {
                        scope.cash_book.luykedauthang.chicho.total = 0;
                        scope.cash_book.luykedauthang.chikho.total = 0;
                        scope.cash_book.luykedauthang.chicho.groups = {};
                        scope.cash_book.luykedauthang.chikho.groups = {};
                    }
                    angular.forEach(markets, function (market, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            markets[warehouse_id].total.all = 0;
                            angular.forEach(market.total.groups, function (value, group_id) {
                                if (in_array(group_id, group_ids)) {
                                    value = value;
                                    markets[warehouse_id].total.groups[group_id] = value;
                                    markets[warehouse_id].total.all = $['+'](markets[warehouse_id].total.all, value);
                                    if (checkFirstDay || (!checkFirstDay && !scope.cash_book.is_view)) {
                                        scope.cash_book.luykedauthang.chicho.groups[group_id] || (scope.cash_book.luykedauthang.chicho.groups[group_id] = 0);
                                        scope.cash_book.luykedauthang.chicho.groups[group_id] = $['+'](scope.cash_book.luykedauthang.chicho.groups[group_id], value);
                                        scope.cash_book.luykedauthang.chicho.total = $['+'](scope.cash_book.luykedauthang.chicho.total, value);
                                    }
                                }
                            })
                        }
                    });
                    scope.cash_book.gomarkets = markets;
                    /*Tiền xuất kho*/
                    var goexports = {};
                    angular.forEach(scope.cash_book.exports, function (item, warehouse_id) {
                        if (item + '' != 'null') {
                            if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                                goexports[warehouse_id] || (goexports[warehouse_id] = {
                                    foods: {},
                                    total: {
                                        all: 0,
                                        groups: {}
                                    }
                                });
                                angular.forEach(item.foods, function (food, food_id) {
                                    var fd = {
                                        name: food.name,
                                        measure_id: food.measure_id,
                                        price: food.price,
                                        supplier: food.supplier,
                                        thucmuatheodvt: 0,
                                        groups: {}
                                    };
                                    angular.forEach(food.groups, function (objThucmua, group_id) {
                                        if (in_array(group_id, group_ids)) {
                                            if (typeof objThucmua != 'object') {
                                                objThucmua = {thucmuatheodvt: objThucmua};
                                            }else if(typeof objThucmua.thucmuatheodvt.thucmuatheodvt != 'undefined') {
												objThucmua = {thucmuatheodvt: objThucmua.thucmuatheodvt.thucmuatheodvt};
											}
                                            fd.groups[group_id] = objThucmua;
                                            fd.thucmuatheodvt = $['+'](fd.thucmuatheodvt, objThucmua.thucmuatheodvt);
                                            var thanhtien = $['*'](food.price, objThucmua.thucmuatheodvt);
                                            goexports[warehouse_id].total.all = $['+'](goexports[warehouse_id].total.all, thanhtien);
                                            goexports[warehouse_id].total.groups[group_id] || (goexports[warehouse_id].total.groups[group_id] = 0);
                                            goexports[warehouse_id].total.groups[group_id] = $['+'](goexports[warehouse_id].total.groups[group_id], thanhtien);
                                            if (checkFirstDay || (!checkFirstDay && !scope.cash_book.is_view)) {
                                                scope.cash_book.luykedauthang.chikho.groups[group_id] || (scope.cash_book.luykedauthang.chikho.groups[group_id] = 0);
                                                scope.cash_book.luykedauthang.chikho.groups[group_id] = $['+'](scope.cash_book.luykedauthang.chikho.groups[group_id], thanhtien);
                                                scope.cash_book.luykedauthang.chikho.total = $['+'](scope.cash_book.luykedauthang.chikho.total, thanhtien);
                                            }
                                        }
                                    });
                                    if (count(fd.groups) > 0) {
                                        goexports[warehouse_id].foods[food_id] = fd;
                                    }
                                });
                            }
                        }
                    });

                    /*Làm tròn lũy kế chi kho*/
                    scope.cash_book.luykedauthang.chikho.total = scope.cash_book.luykedauthang.chikho.total;
                    angular.forEach(scope.cash_book.luykedauthang.chikho.groups, function (value, group_id) {
                        scope.cash_book.luykedauthang.chikho.groups[group_id] = value;
                    });
                    /*Làm tròn tổng tiền đi chợ*/
                    angular.forEach(goexports, function (warehouse, warehouse_id) {
                        angular.forEach(warehouse.total.groups, function (value, group_id) {
                            goexports[warehouse_id].total.groups[group_id] = value;
                        });
                        goexports[warehouse_id].total.all = warehouse.total.all;
                    });
                    scope.cash_book.goexports = goexports;
                    /*Tiền dịch vụ*/
                    angular.forEach(scope.cash_book.total_services.groups, function (thanhtien, group_id) {
                        if (in_array(group_id, group_ids)) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                            scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                        }
                    });
                    angular.forEach(scope.cash_book.goexports, function (wh, wh_id) {
                        angular.forEach(wh.total.groups, function (thanhtien, group_id) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                            scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                        })
                    });
                    angular.forEach(scope.cash_book.gomarkets, function (wh, wh_id) {
                        angular.forEach(wh.total.groups, function (thanhtien, group_id) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                            scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                        })
                    });
                    scope.cash_book.dachitrongngay.total = 0;
                    angular.forEach(scope.cash_book.dachitrongngay.groups, function (value, group_id) {
                        scope.cash_book.dachitrongngay.groups[group_id] = value;
                        scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, value);
                    });
                    scope.cash_book.surplus.total_begin = 0;
                    scope.cash_book.tieuchuan.groups = tmp_groups;
                    /*Tính chênh lệch cuối ngày*/
                    scope.cash_book.surplus.total_end = 0;
                    var surplus_groups = {};
                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        surplus_groups[group_id] = 0;
                        scope.cash_book.surplus.begin || (scope.cash_book.surplus.begin = {});
                        surplus_groups[group_id] = $['+']($['*'](group.row.sotre, group.row.tien1tre), scope.cash_book.surplus.begin[group_id]);
                        surplus_groups[group_id] = $['-'](surplus_groups[group_id], scope.cash_book.dachitrongngay.groups[group_id]);
                        if (in_array(group_id, group_ids)) {
                            if (!scope.cash_book.surplus.begin[group_id]) {
                                scope.cash_book.surplus.begin[group_id] = 0;
                            }
                            (scope.cash_book.surplus.total_begin) += Number(scope.cash_book.surplus.begin[group_id]);
                            scope.cash_book.surplus.total_end += surplus_groups[group_id];
                            scope.cash_book.tieuchuan.total.sotre += group.row.sotre;
                            scope.cash_book.tieuchuan.total.thanhtien += $['*'](group.row.sotre, group.row.tien1tre);
                        }
                    });
                    scope.cash_book.surplus.end = surplus_groups;
                    scope.cash_book.tien_bo_troCalc();
                };
                scope.cash_book.processDataPoint = function () {
                    var tmp_services_total = {groups: {}, groups_point: {}, total: 0};
                    var tmp_groups = {};
                    var group_ids = [];
                    var services = {};
                    var surplus_points = {};
                    scope.cash_book.tieuchuan = {
                        groups: {},
                        total: {
                            sotre: 0,
                            thanhtien: 0,
                            sotres: {},
                            thanhtiens: {},
                        }
                    };

                    //Khởi tạo dư liệu begins
                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        scope.cash_book.surplus.begins || (scope.cash_book.surplus.begins = {});
                        if(scope.cash_book.surplus.begins[group_id] === undefined){
                            scope.cash_book.surplus.begins[group_id] = {};
                            scope.cash_book.surplus.begins[group_id][1] = scope.cash_book.surplus.begin[group_id];
                        }
                        scope.cash_book.surplus.begins[group_id] || (scope.cash_book.surplus.begins[group_id] = {});
                        angular.forEach(scope.cash_book.school_points, function (val, point) {
                            scope.cash_book.surplus.begins[group_id][point] || (scope.cash_book.surplus.begins[group_id][point] = 0);
                        });
                    });

                    var checkFirstDay = scope.menu_report.cash_books[scope.menu_report.cash_books.length - 1].date == $.cash_book.date;
                    if (checkFirstDay) {
                        scope.cash_book.surplus.begin = {};
                        angular.forEach(scope.cash_book.groups, function (group, group_id) {
                            scope.cash_book.surplus.begin[group_id] = 0;
                            scope.cash_book.surplus.begins || (scope.cash_book.surplus.begins = {});
                            scope.cash_book.surplus.begins[group_id] || (scope.cash_book.surplus.begins[group_id] = {});
                            angular.forEach(scope.cash_book.school_points, function (val, point) {
                                scope.cash_book.surplus.begins[group_id][point] || (scope.cash_book.surplus.begins[group_id][point] = 0);
                                scope.cash_book.surplus.begin[group_id] = $['+'](scope.cash_book.surplus.begin[group_id], scope.cash_book.surplus.begins[group_id][point]);
                            });
                        });
                    }

                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        var menu_planning = scope.cash_book.menu_plannings[group_id];
                        angular.forEach(scope.cash_book.school_points, function (val, point) {
                            menu_planning.row.sotres || (menu_planning.row.sotres = {});
                            menu_planning.row.sotres[point] || (menu_planning.row.sotres[point] = 0);
                        });
                        if (group.selected) {
                            menu_planning.row.sotre = parseInt(menu_planning.row.sotre);
                            menu_planning.row.tien1tre = parseInt(menu_planning.row.tien1tre);
                            var tmp_services = menu_planning.services;
                            if (!tmp_services) {
                                tmp_services = scope.cash_book.group_services;
                            }
                            angular.forEach(tmp_services, function (service, index) {
                                var service_id = service.service_name + '_' + service.price;
                                var service_name = service.service_name;
                                service_name || (service_name = service.name);
                                tmp_services_total.groups[group_id] || (tmp_services_total.groups[group_id] = 0);
                                tmp_services_total.groups[group_id] = $['+'](tmp_services_total.groups[group_id], $['*'](service.price, menu_planning.row.sotre));
                                tmp_services_total.total += service.price * menu_planning.row.sotre;
                                services[service_id] || (services[service_id] = {
                                    groups: {},
                                    groups_point: {},
                                    name: service_name,
                                    price: service.price,
                                    sotre: 0,
                                    thanhtien: 0
                                });
                                services[service_id].sotre = $['+'](services[service_id].sotre, menu_planning.row.sotre);
                                services[service_id].thanhtien = $['+'](services[service_id].thanhtien, $['*'](service.price, menu_planning.row.sotre));
                                services[service_id].groups[group_id] || (services[service_id].groups[group_id] = {
                                    price: service.price,
                                    sotre: 0,
                                    thanhtien: 0
                                });
                                services[service_id].groups[group_id].sotre = $['+'](services[service_id].groups[group_id].sotre, menu_planning.row.sotre);
                                services[service_id].groups[group_id].thanhtien = $['+'](services[service_id].groups[group_id].thanhtien, $['*'](service.price, menu_planning.row.sotre));

                                /*p*/
                                tmp_services_total.groups_point[group_id] || (tmp_services_total.groups_point[group_id] = {});
                                services[service_id].groups_point[group_id] = (services[service_id].groups_point[group_id] || {
                                    price: service.price,
                                    sotres: {},
                                    thanhtiens: {}
                                });
                                angular.forEach(menu_planning.row.sotres, function (child, point) {
                                    tmp_services_total.groups_point[group_id][point] = tmp_services_total.groups_point[group_id][point] || 0;
                                    tmp_services_total.groups_point[group_id][point] = $['+'](tmp_services_total.groups_point[group_id][point], $['*'](service.price, child));

                                    services[service_id].groups_point[group_id].sotres[point] = services[service_id].groups_point[group_id].sotres[point] || 0;
                                    services[service_id].groups_point[group_id].thanhtiens[point] = services[service_id].groups_point[group_id].thanhtiens[point] || 0;
                                    services[service_id].groups_point[group_id].sotres[point] = $['+'](services[service_id].groups_point[group_id].sotres[point], child);
                                    services[service_id].groups_point[group_id].thanhtiens[point] = $['+'](services[service_id].groups_point[group_id].thanhtiens[point], $['*'](service.price, child));
                                })
                            });

                            tmp_groups[group_id] || (tmp_groups[group_id] = {sotre: 0, sotres: {}, thanhtien: 0, thanhtiens: {}});
                            tmp_groups[group_id].sotre = menu_planning.row.sotre;
                            tmp_groups[group_id].thanhtien = menu_planning.row.sotre * menu_planning.row.tien1tre;
                            tmp_groups[group_id].sotres = menu_planning.row.sotres;
                            angular.forEach(menu_planning.row.sotres, function (sotre, point) {
                                tmp_groups[group_id].thanhtiens[point] = sotre * menu_planning.row.tien1tre;
                            });
                            group_ids.push(Number(group_id));
                        }
                    });

                    /*Khởi tạo số dư cuối ngày*/
                    angular.forEach(scope.cash_book.groups, function (grp, group_id) {
                        var mn = scope.cash_book.menu_plannings[group_id];
                        angular.forEach(scope.cash_book.school_points, function (v, point) {
                            surplus_points[group_id] || (surplus_points[group_id] = {});
                            surplus_points[group_id][point] || (surplus_points[group_id][point] = 0);
                            /*  Cộng hôm trước chuyển sang  */
                            surplus_points[group_id][point] = $['+'](surplus_points[group_id][point], scope.cash_book.surplus.begins[group_id][point]);
                            /*  Cộng được ăn trong ngày (Tiền 1 trẻ * số trẻ)*/
                            surplus_points[group_id][point] = $['+'](surplus_points[group_id][point], $['*'](mn.row.sotres[point], mn.row.tien1tre));
                        });
                    });


                    scope.cash_book.total_services = tmp_services_total;
                    scope.cash_book.services = services;
                    scope.cash_book.luykedauthang = {
                        chicho: {groups: {}, total: 0},
                        chikho: {groups: {}, total: 0},
                        tongchi: {groups: {}, total: 0},
                        chichos: {},
                        chikhos: {}
                    };
                    angular.forEach(scope.cash_book.tonghop.chicho, function (chicho, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                var value = 0;
                                if (typeof chicho.groups[group_id] === 'object') {
                                    value = chicho.groups[group_id].thanhtien;
                                } else {
                                    value = chicho.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.chicho[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.chicho.groups[group_id] || (scope.cash_book.luykedauthang.chicho.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.chicho.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.chicho.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(scope.cash_book.tonghop.chikho, function (chikho, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                var value = 0;
                                if (typeof chikho.groups[group_id] === 'object') {
                                    value = chikho.groups[group_id].thanhtien;
                                } else {
                                    value = chikho.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.chikho[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.chikho.groups[group_id] || (scope.cash_book.luykedauthang.chikho.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.chikho.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.chikho.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(scope.cash_book.tonghop.tongchi, function (tongchi, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            angular.forEach(scope.cash_book.groups, function (group, group_id) {
                                var value = 0;
                                if (typeof tongchi.groups[group_id] === 'object') {
                                    value = tongchi.groups[group_id].thanhtien;
                                } else {
                                    value = tongchi.groups[group_id];
                                }
                                value || (value = 0);
                                scope.cash_book.tonghop.tongchi[warehouse_id].groups[group_id] = value;
                                if (in_array(group_id, group_ids)) {
                                    scope.cash_book.luykedauthang.tongchi.groups[group_id] || (scope.cash_book.luykedauthang.tongchi.groups[group_id] = 0);
                                    scope.cash_book.luykedauthang.tongchi.groups[group_id] = $['+'](value, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                                }
                            });
                        }
                    });
                    angular.forEach(group_ids, function (group_id, ind) {
                        if (scope.cash_book.luykedauthang.chicho.groups[group_id]) {
                            scope.cash_book.luykedauthang.chicho.total = $['+'](scope.cash_book.luykedauthang.chicho.total, scope.cash_book.luykedauthang.chicho.groups[group_id]);
                        }
                        if (scope.cash_book.luykedauthang.chikho.groups[group_id]) {
                            scope.cash_book.luykedauthang.chikho.total = $['+'](scope.cash_book.luykedauthang.chikho.total, scope.cash_book.luykedauthang.chikho.groups[group_id]);
                        }
                        if (scope.cash_book.luykedauthang.tongchi.groups[group_id]) {
                            scope.cash_book.luykedauthang.tongchi.total = $['+'](scope.cash_book.luykedauthang.tongchi.total, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                        }
                    });
                    if (scope.cash_book.tonghop.dichvu) {
                        angular.forEach(scope.cash_book.tonghop.dichvu.groups, function (thanhtien, group_id) {
                            if (in_array(group_id, group_ids)) {
                                scope.cash_book.luykedauthang.tongchi.groups[group_id] = $['+'](thanhtien, scope.cash_book.luykedauthang.tongchi.groups[group_id]);
                                scope.cash_book.luykedauthang.tongchi.total = $['+'](thanhtien, scope.cash_book.luykedauthang.tongchi.total);
                            }
                        });
                    }
                    /*Tính đã chi trong ngày*/
                    scope.cash_book.dachitrongngay = {
                        total: 0,
                        groups: {}
                    };
                    var markets = {};
                    angular.forEach(scope.cash_book.markets, function (market, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            markets[warehouse_id] || (markets[warehouse_id] = {
                                foods: {},
                                total: {
                                    all: 0,
                                    alls: {},
                                    groups: {},
                                    tong: {},
                                    tongs: {}
                                }
                            });
                            if (market) {
                                angular.forEach(market.foods, function (food, food_id) {
                                    var fd = {
                                        name: food.name,
                                        measure_id: food.measure_id,
                                        price: food.price,
                                        supplier: food.supplier,
                                        thucmuatheodvt: 0,
                                        thucmuatheodvts: {},
                                        groups: {}
                                    };
                                    angular.forEach(food.groups, function (group, group_id) {
                                        group_id = Number(group_id);
                                        group.thucmuatheodvts || (group.thucmuatheodvts = {1: group.thucmuatheodvt});
                                        if (in_array(group_id, group_ids)) {
                                            fd.groups[group_id] = group;
                                            markets[warehouse_id].total.groups[group_id] || (markets[warehouse_id].total.groups[group_id] = 0);
                                           
                                            angular.forEach(group.thucmuatheodvts, function (val, point) {
                                                var thanhtien = $['*'](val, fd.price) || 0;
                                                markets[warehouse_id].total.groups[group_id] || (markets[warehouse_id].total.groups[group_id] = 0);
                                                markets[warehouse_id].total.groups[group_id] = $['+'](markets[warehouse_id].total.groups[group_id], thanhtien);
                                                markets[warehouse_id].total.all = $['+'](markets[warehouse_id].total.all, thanhtien);
                                                fd.thucmuatheodvt = $['+'](fd.thucmuatheodvt, val);
                                                fd.thucmuatheodvts[point] || (fd.thucmuatheodvts[point] = 0);
                                                fd.thucmuatheodvts[point] = $['+'](fd.thucmuatheodvts[point], val);
                                                scope.cash_book.luykedauthang.chichos[group_id] || (scope.cash_book.luykedauthang.chichos[group_id] = {});
                                                scope.cash_book.luykedauthang.chichos[group_id][point] || (scope.cash_book.luykedauthang.chichos[group_id][point] = 0);
                                                scope.cash_book.luykedauthang.chichos[group_id][point] = $['+'](scope.cash_book.luykedauthang.chichos[group_id][point], thanhtien);
                                                surplus_points[group_id] = surplus_points[group_id] || {}; //Lỗi khi xem dữ liệu cũ
                                                surplus_points[group_id][point] = surplus_points[group_id][point] || 0;
                                                surplus_points[group_id][point] = $['-'](surplus_points[group_id][point], thanhtien); //Số dư cuối
                                            });
                                        }
                                    });
                                    if (count(fd.groups) > 0) {
                                        markets[warehouse_id].foods[food_id] = fd;
                                    }
                                });
                            }
                        }
                    });
                    if (checkFirstDay) {
                        scope.cash_book.luykedauthang.chicho.total = 0;
                        scope.cash_book.luykedauthang.chikho.total = 0;
                        scope.cash_book.luykedauthang.chicho.groups = {};
                        scope.cash_book.luykedauthang.chikho.groups = {};
                    }
                    angular.forEach(markets, function (market, warehouse_id) {
                        if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                            markets[warehouse_id].total.all = 0;
                            angular.forEach(market.total.groups, function (value, group_id) {
                                if (in_array(group_id, group_ids)) {
                                    markets[warehouse_id].total.groups[group_id] = value;
                                    markets[warehouse_id].total.all = $['+'](markets[warehouse_id].total.all, value);
                                    if (checkFirstDay || (!checkFirstDay && !scope.cash_book.is_view)) {
                                        scope.cash_book.luykedauthang.chicho.groups[group_id] || (scope.cash_book.luykedauthang.chicho.groups[group_id] = 0);
                                        scope.cash_book.luykedauthang.chicho.groups[group_id] = $['+'](scope.cash_book.luykedauthang.chicho.groups[group_id], value);
                                        scope.cash_book.luykedauthang.chicho.total = $['+'](scope.cash_book.luykedauthang.chicho.total, value);
                                    }
                                }
                            })
                        }
                    });
                    
                    scope.cash_book.gomarkets = markets;
                    /*Tiền xuất kho*/
                    var goexports = {};
                    angular.forEach(scope.cash_book.exports, function (item, warehouse_id) {
                        if (item + '' != 'null') {
                            if (scope.cash_book.selected.warehouse_ids[warehouse_id]) {
                                goexports[warehouse_id] || (goexports[warehouse_id] = {
                                    foods: {},
                                    total: {
                                        all: 0,
                                        alls: 0,
                                        groups: {}
                                    }
                                });
                                angular.forEach(item.foods, function (food, food_id) {
                                    var fd = {
                                        name: food.name,
                                        measure_id: food.measure_id,
                                        price: food.price,
                                        supplier: food.supplier,
                                        thucmuatheodvt: 0,
                                        thucmuatheodvts: {},
                                        groups: {}
                                    };
                                    angular.forEach(food.groups, function (thucmua, group_id) {
                                        var thucmuaTmp;
                                        if (typeof thucmua === 'object'){
                                            thucmuaTmp = clone(thucmua);
                                        } else {
                                            thucmuaTmp = {
                                                thucmuatheodvt: thucmua
                                            }
                                        }
                                        if (typeof thucmuaTmp.thucmuatheodvts === 'undefined') {
                                            thucmuaTmp.thucmuatheodvts = {1: thucmuaTmp.thucmuatheodvt};
                                        }
                                        thucmuaTmp.thucmuatheodvts = scope.cash_book.checkThucmua(thucmuaTmp.thucmuatheodvt, thucmuaTmp.thucmuatheodvts, true);
                                        if (in_array(group_id, group_ids)) {
                                            fd.groups[group_id] = {
                                                thucmuatheodvt: thucmuaTmp.thucmuatheodvt,
                                                thucmuatheodvts: thucmuaTmp.thucmuatheodvts
                                            };

                                            angular.forEach(fd.groups[group_id].thucmuatheodvts, function (val, point) {
                                                fd.thucmuatheodvt = $['+'](fd.thucmuatheodvt, val);
                                                fd.thucmuatheodvts[point] || (fd.thucmuatheodvts[point] = 0);
                                                fd.thucmuatheodvts[point] = $['+'](fd.thucmuatheodvts[point], val);
                                                var thanhtien = $['*'](food.price, val);
                                                goexports[warehouse_id].total.all = $['+'](goexports[warehouse_id].total.all, thanhtien);
                                                goexports[warehouse_id].total.alls[point] || (goexports[warehouse_id].total.alls[point] = 0);
                                                goexports[warehouse_id].total.alls[point] = $['+'](goexports[warehouse_id].total.alls[point], thanhtien);
                                                goexports[warehouse_id].total.groups[group_id] || (goexports[warehouse_id].total.groups[group_id] = 0);
                                                goexports[warehouse_id].total.groups[group_id] = $['+'](goexports[warehouse_id].total.groups[group_id], thanhtien);

                                                scope.cash_book.luykedauthang.chikhos[group_id] || (scope.cash_book.luykedauthang.chikhos[group_id] = {});
                                                scope.cash_book.luykedauthang.chikhos[group_id][point] || (scope.cash_book.luykedauthang.chikhos[group_id][point] = 0);
                                                scope.cash_book.luykedauthang.chikhos[group_id][point] = $['+'](scope.cash_book.luykedauthang.chikhos[group_id][point], thanhtien);

                                                if (checkFirstDay || (!checkFirstDay && !scope.cash_book.is_view)) {
                                                    scope.cash_book.luykedauthang.chikho.groups[group_id] || (scope.cash_book.luykedauthang.chikho.groups[group_id] = 0);
                                                    scope.cash_book.luykedauthang.chikho.groups[group_id] = $['+'](scope.cash_book.luykedauthang.chikho.groups[group_id], thanhtien);
                                                    scope.cash_book.luykedauthang.chikho.total = $['+'](scope.cash_book.luykedauthang.chikho.total, thanhtien);
                                                }
                                                surplus_points[group_id][point] = $['-'](surplus_points[group_id][point], thanhtien);
                                            });
                                        }
                                    });
                                    if (count(fd.groups) > 0) {
                                        goexports[warehouse_id].foods[food_id] = fd;
                                    }
                                });
                            }
                        }
                    });

                    /*Làm tròn lũy kế chi kho*/
                    angular.forEach(scope.cash_book.luykedauthang.chikho.groups, function (value, group_id) {
                        scope.cash_book.luykedauthang.chikho.groups[group_id] = value;
                    });
                    /*Làm tròn tổng tiền đi chợ*/
                    angular.forEach(goexports, function (warehouse, warehouse_id) {
                        angular.forEach(warehouse.total.groups, function (value, group_id) {
                            goexports[warehouse_id].total.groups[group_id] = value;
                        });
                        goexports[warehouse_id].total.all = warehouse.total.all;
                    });
                    scope.cash_book.goexports = goexports;
                    /*Tiền dịch vụ*/
                    angular.forEach(scope.cash_book.total_services.groups, function (thanhtien_tong, group_id) {
                        if (in_array(group_id, group_ids)) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            var menu_planning = scope.cash_book.menu_plannings[group_id];
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            var thanhtiens = scope.divide(thanhtien_tong, menu_planning.row.sotres);
                            angular.forEach(thanhtiens, function (thanhtien, point) {
                                scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                                scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                                surplus_points[group_id][point] = $['-'](surplus_points[group_id][point], thanhtien);
                            });
                        }
                    });
                    angular.forEach(scope.cash_book.goexports, function (wh, wh_id) {
                        angular.forEach(wh.total.groups, function (thanhtien, group_id) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                            scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                        })
                    });
                    angular.forEach(scope.cash_book.gomarkets, function (wh, wh_id) {
                        angular.forEach(wh.total.groups, function (thanhtien, group_id) {
                            scope.cash_book.dachitrongngay.groups[group_id] || (scope.cash_book.dachitrongngay.groups[group_id] = 0);
                            scope.cash_book.dachitrongngay.groups[group_id] = $['+'](scope.cash_book.dachitrongngay.groups[group_id], thanhtien);
                            scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, thanhtien);
                        })
                    });
                    scope.cash_book.dachitrongngay.total = 0;
                    angular.forEach(scope.cash_book.dachitrongngay.groups, function (value, group_id) {
                        scope.cash_book.dachitrongngay.groups[group_id] = value;
                        scope.cash_book.dachitrongngay.total = $['+'](scope.cash_book.dachitrongngay.total, value);
                    });
                    scope.cash_book.surplus.total_begin = 0;
                    scope.cash_book.tieuchuan.groups = tmp_groups;
                    /*Tính chênh lệch cuối ngày*/
                    scope.cash_book.surplus.total_end = 0;
                    var surplus_groups = {};
                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        surplus_groups[group_id] = (surplus_groups[group_id] || 0);
                        scope.cash_book.surplus.begin || (scope.cash_book.surplus.begin = {});
                        scope.cash_book.surplus.begin[group_id] = scope.cash_book.surplus.begin[group_id] || 0;
                        scope.cash_book.dachitrongngay.groups[group_id] = scope.cash_book.dachitrongngay.groups[group_id] || 0;
                        surplus_groups[group_id] = $['+'](surplus_groups[group_id], scope.cash_book.surplus.begin[group_id]);
                        surplus_groups[group_id] = $['+'](surplus_groups[group_id], $['*'](group.row.sotre, group.row.tien1tre));
                        surplus_groups[group_id] = $['-'](surplus_groups[group_id], scope.cash_book.dachitrongngay.groups[group_id]);
                        group_id = Number(group_id);
                        if (in_array(group_id, group_ids)) {
                            scope.cash_book.surplus.total_begin += Number(scope.cash_book.surplus.begin[group_id]);
                            scope.cash_book.surplus.total_end += surplus_groups[group_id];
                            scope.cash_book.tieuchuan.total.sotre += group.row.sotre;
                            scope.cash_book.tieuchuan.total.thanhtien += $['*'](group.row.sotre, group.row.tien1tre);
                        }
                    });
					scope.sys = window.sysConfigs;
					if(scope.sys.configs.stta_not_re_calculate_surplus_end && scope.cash_book.is_view) {
						scope.cash_book.surplus.end = scope.cash_book.surplus.end || surplus_groups;
					}else{
						scope.cash_book.surplus.end = surplus_groups;
					}
                    scope.cash_book.surplus.ends = surplus_points;

                    scope.cash_book.tien_bo_troCalc();
                };
                scope.cash_book.isPointTogether = function() {
                    if ($CFG.school_points > 1) {
                        if ($CFG.school_point_together == 1) {
                            return true;
                        }
                    }
                    return false;
                };
                scope.cash_book.checkThucmua = function(total, points_val) {
                    var rst = 0;
                    angular.forEach(points_val, function (val, point) {
                        rst = $['+'](rst, val);
                    });
                    if (rst != total) {
                        points_val = scope.divide(total, points_val);
                    }
                    return points_val;
                };
                scope.cash_book.applyOptionStorage_import = function (dialogRef, callback) {
                    /*  Kiểm tra các lựa chọn nhập kho có đủ điều kiện số lượng tối thiểu*/
                    var check_import = [];
                    var check_insert_export = false;
                    angular.forEach(scope.cash_book.xuat_thieu, function (foods, warehouse_id) {
                        angular.forEach(foods, function (food, food_id) {
                            if (food.is_nhapkho === 2) {
                                /*Cho vào đi chợ*/
                                if (!scope.cash_book.markets[warehouse_id]) {
                                    scope.cash_book.markets[warehouse_id] = {
                                        foods: {},
                                        total: {
                                            all: 0,
                                            groups: {},
                                            tong: {}
                                        }
                                    }
                                }
                                var markets = scope.cash_book.markets[warehouse_id].foods;
                                var total = scope.cash_book.markets[warehouse_id].total;
                                markets[food_id] || (markets[food_id] = {
                                    extrude_factor: food.extrude_factor,
                                    food_id: food_id,
                                    gam_exchange: food.gam_exchange,
                                    groups: {},
                                    measure_id: food.measure_id,
                                    name: food.name,
                                    price: food.price,
                                    price_kg: food.price_kg,
                                    luong1tre: 0,
                                    thucmua1nhom: 0,
                                    thucmuatheodvt: 0,
                                    supplier: food.supplier

                                });
                                markets[food_id].luong1tre = $['+'](markets[food_id].luong1tre, food.luong1tre);
                                markets[food_id].thucmua1nhom = $['+'](markets[food_id].thucmua1nhom, food.thucmua1nhom);
                                markets[food_id].thucmuatheodvt = $['+'](markets[food_id].thucmuatheodvt, $['-'](food.thucmuatheodvt, food.inventory));
                                angular.forEach(food.groups, function (item, group_id) {
                                    markets[food_id].groups[group_id] || (markets[food_id].groups[group_id] = {
                                        luong1tre: 0,
                                        thucmua1nhom: 0,
                                        thucmuatheodvt: 0,
                                        warehouses: {}
                                    });
                                    markets[food_id].groups[group_id].thucmuatheodvt = $['+'](markets[food_id].groups[group_id].thucmuatheodvt, item.thucmuatheodvt);
                                    markets[food_id].groups[group_id].warehouses[warehouse_id] || (markets[food_id].groups[group_id].warehouses[warehouse_id] = {
                                        luong1tre: 0,
                                        thucmua1nhom: 0,
                                        thucmuatheodvt: 0
                                    });
                                    markets[food_id].groups[group_id].warehouses[warehouse_id].luong1tre = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].luong1tre, item.luong1tre);
                                    markets[food_id].groups[group_id].warehouses[warehouse_id].thucmua1nhom = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].thucmua1nhom, item.thucmua1nhom);
                                    markets[food_id].groups[group_id].warehouses[warehouse_id].thucmuatheodvt = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].thucmuatheodvt, item.thucmuatheodvt);
                                    total.groups[group_id] || (total.groups[group_id] = 0);
                                    total.groups[group_id] = $['+'](total.groups[group_id], $['*'](item.thucmuatheodvt, food.price));
                                });
                                total.all = $['+'](total.all, $['*'](food.thucmuatheodvt, food.price));
                            }
                            else if (food.is_nhapkho === 1) { /* Nếu thực phẩm chọn nhập kho */
                                if ($['+'](food.quantity_import, food.inventory) < food.thucmuatheodvt) { /*  Kiểm tra số lượng nhập có đủ không */
                                    var alt = food.name + ': cần ' + $['-'](food.thucmuatheodvt, food.inventory);
                                    alt += ' (nhập ' + food.quantity_import + ') thiếu ' + scope.round((food.thucmuatheodvt - food.inventory - food.quantity_import));
                                    alt += '(' + scope.cash_book.getObject(scope.cash_book.measures, food.measure_id).name + ')';
                                    check_import.push(alt);
                                } else {  /*  Nếu nhập số lượng đủ thì cho vào xuất kho */
                                    check_insert_export = true;
                                    if (!scope.cash_book.exports[warehouse_id]) {
                                        scope.cash_book.exports[warehouse_id] = {
                                            export: {},
                                            foods: {},
                                            import_unlegal: [],
                                            total: {
                                                all: 0,
                                                groups: {}
                                            }
                                        }
                                    }
                                    var exps = scope.cash_book.exports[warehouse_id];
                                    var import_unlegal = {
                                        'export': food.thucmuatheodvt - food.inventory,
                                        'price': food.price,
                                        'inventory': food.quantity_import,
                                        'quantity': food.quantity_import,
                                        'quantity_used': 0,
                                        'food_id': food.food_id,
                                        'supplier': food.supplier,
                                        'gam_exchange' : food.gam_exchange,
                                    };
                                    exps.import_unlegal || (exps.import_unlegal = []);
                                    exps.import_unlegal.push(import_unlegal);
                                    angular.forEach(food.exports, function (storage, storage_id) {
                                        if (storage.inventory == 0) {
                                            delete food.exports[storage_id];
                                        } else {
                                            storage.quantity_used = $['-'](storage.quantity, storage.inventory);
                                        }
                                    });
                                    angular.forEach(food.groups, function (group, group_id) {
                                        if (group['thucmuatheodvt'] <= 0) {
                                            return;
                                        }
                                        angular.forEach(food.exports, function (storage, storage_id) {
                                            var inventory = $['-'](storage.quantity, storage.quantity_used);
                                            if (inventory < 0) {
                                                return;
                                            }
                                            var id = food_id + '_' + storage['price'];
                                            if (!exps.foods || count(exps.foods) == 0) {
                                                exps.foods = {}
                                            }
                                            if (!exps.foods[id]) {
                                                exps.foods[id] = {
                                                    'name': food['name'],
                                                    'measure_id': food['measure_id'],
                                                    'food_id': food['food_id'],
                                                    'gam_exchange': food['gam_exchange'],
                                                    'extrude_factor': food['extrude_factor'],
                                                    'price': storage['price'],
                                                    'thucmuatheodvt': 0,
                                                    'supplier': food['supplier'],
                                                    'groups': {}
                                                };
                                            }
                                            exps.foods[id].groups[group_id] || (exps.foods[id].groups[group_id] = {thucmuatheodvt : 0});
                                            exps.foods[id].groups[group_id].thucmuatheodvts = exps.foods[id].groups[group_id].thucmuatheodvts || group.thucmuatheodvts || {};
                                            exps.total || (exps.total = {});
                                            exps.total.groups || (exps.total.groups = {});
                                            exps.total.groups[group_id] || (exps.total.groups[group_id] = 0);
                                            if (inventory < group['thucmuatheodvt']) { /* Nếu lượng tồn nhỏ hơn lượng cần thì xuất hết */
                                                exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], inventory);
                                                exps.foods[id].groups[group_id]['thucmuatheodvt'] = $['+'](exps.foods[id].groups[group_id]['thucmuatheodvt'], inventory);
                                                storage['quantity_used'] = $['+'](storage['quantity_used'], inventory);
                                                food['groups'][group_id].thucmuatheodvt = $['-'](food['groups'][group_id].thucmuatheodvt, inventory);
                                            } else {
                                                exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                                exps.foods[id].groups[group_id]['thucmuatheodvt'] = $['+'](exps.foods[id].groups[group_id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                                storage['quantity_used'] = $['+'](storage['quantity_used'], group['thucmuatheodvt']);
                                                food['groups'][group_id].thucmuatheodvt = $['-'](food['groups'][group_id].thucmuatheodvt, group['thucmuatheodvt']);
                                            }
                                        });
                                    });

                                    angular.forEach(food['groups'], function (group, group_id) {
                                        if (group['thucmuatheodvt'] <= 0) {
                                            return;
                                        }
                                        var inventory = import_unlegal['quantity'] - import_unlegal['quantity_used'];
                                        if (inventory < 0) {
                                            return;
                                        }
                                        exps.total.groups[group_id] || (exps.total.groups[group_id] = 0);
                                        var id = food_id + '_' + import_unlegal['price'];
                                        if (!exps.foods[id]) {
                                            exps.foods[id] = {
                                                'name': food['name'],
                                                'measure_id': food['measure_id'],
                                                'food_id': food['food_id'],
                                                'supplier': food['supplier'],
                                                'gam_exchange': food['gam_exchange'],
                                                'extrude_factor': food['extrude_factor'],
                                                'price': import_unlegal['price'],
                                                'thucmuatheodvt': 0,
                                                'groups': {}
                                            };
                                        }
                                        exps.foods[id].groups[group_id] || (exps.foods[id].groups[group_id] = {thucmuatheodvt: 0});
                                        if (typeof food['groups'][group_id].thucmuatheodvts != 'undefined' && $CFG.school_points > 1 && $CFG.school_point_together == 1) {
                                            exps.foods[id].groups[group_id]['thucmuatheodvts'] = food['groups'][group_id].thucmuatheodvts;
                                        }
                                        if (inventory < group['thucmuatheodvt']) { /* Nếu lượng tồn nhỏ hơn lượng cần thì xuất hết */
                                            exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], inventory);
                                            exps.foods[id].groups[group_id]['thucmuatheodvt'] = $['+'](exps.foods[id].groups[group_id]['thucmuatheodvt'], inventory);
                                            import_unlegal['quantity_used'] = $['+'](import_unlegal['quantity_used'], inventory);
                                        } else {
                                            exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                            exps.foods[id].groups[group_id]['thucmuatheodvt'] = $['+'](exps.foods[id].groups[group_id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                            import_unlegal['quantity_used'] = $['+'](import_unlegal['quantity_used'], group['thucmuatheodvt']);
                                        }
                                    });
                                    angular.forEach(food.exports, function (exp, fid) {
                                        exp.quantity_used = $['-'](exp.quantity, exp.inventory);
                                    });
                                    exps.export || (exps.export = {});
                                    exps.export[food_id] = food.exports;
                                    scope.cash_book.exports[warehouse_id] = exps;
                                    delete foods[food_id];
                                    /*thêm số lượng thực phẩm nhập kho thêm vào foods*/
                                }
                            }
                            else {
                                /*Xuất kho lượng tồn*/
                                if ($['+'](food.quantity_import, food.inventory) < food.thucmuatheodvt) {
                                    var alt = food.name + ': cần ' + $['-'](food.thucmuatheodvt, food.inventory);
                                    alt += ' (nhập ' + food.quantity_import + ') thiếu ' + scope.round((food.thucmuatheodvt - food.inventory - food.quantity_import));
                                    alt += '(' + scope.cash_book.getObject(scope.cash_book.measures, food.measure_id).name + ')';
                                    check_import.push(alt);
                                } else {  /*  Nếu nhập số lượng đủ thì cho vào xuất kho */
                                    check_insert_export = true;
                                    if (!scope.cash_book.exports[warehouse_id]) {
                                        scope.cash_book.exports[warehouse_id] = {
                                            export: {},
                                            foods: {},
                                            import_unlegal: [],
                                            total: {
                                                all: 0,
                                                groups: {}
                                            }
                                        }
                                    }
                                    var exps = scope.cash_book.exports[warehouse_id];
                                    var import_unlegal = {
                                        'export': food.inventory,
                                        'price': food.price,
                                        'inventory': 0,
                                        'quantity': 0,
                                        'quantity_used': 0,
                                        'food_id': food.food_id,
                                        'supplier': food.supplier
                                    };
                                    exps.import_unlegal || (exps.import_unlegal = []);
                                    angular.forEach(food.exports, function (storage, storage_id) {
                                        if (storage.inventory == 0) {
                                            delete food.exports[storage_id];
                                        } else {
                                            storage.quantity_used = $['-'](storage.quantity, storage.inventory);
                                        }
                                    });
                                    angular.forEach(food.groups, function (group, group_id) {
                                        if (group['thucmuatheodvt'] <= 0) {
                                            return;
                                        }
                                        angular.forEach(food.exports, function (storage, storage_id) {
                                            var inventory = $['-'](storage.quantity, storage.quantity_used);
                                            if (inventory < 0) {
                                                return;
                                            }
                                            var id = food_id + '_' + storage['price'];
                                            if (!exps.foods || count(exps.foods) == 0) {
                                                exps.foods = {}
                                            }
                                            if (!exps.foods[id]) {
                                                exps.foods[id] = {
                                                    'name': food['name'],
                                                    'measure_id': food['measure_id'],
                                                    'food_id': food['food_id'],
                                                    'gam_exchange': food['gam_exchange'],
                                                    'extrude_factor': food['extrude_factor'],
                                                    'price': storage['price'],
                                                    'thucmuatheodvt': 0,
                                                    'supplier': food['supplier'],
                                                    'groups': {}
                                                };
                                            }
                                            exps.foods[id].groups[group_id] || (exps.foods[id].groups[group_id] = 0);
                                            exps.total || (exps.total = {});
                                            exps.total.groups || (exps.total.groups = {});
                                            exps.total.groups[group_id] || (exps.total.groups[group_id] = 0);
                                            if (inventory < group['thucmuatheodvt']) { /* Nếu lượng tồn nhỏ hơn lượng cần thì xuất hết */
                                                exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], inventory);
                                                exps.foods[id].groups[group_id] = $['+'](exps.foods[id].groups[group_id], inventory);
                                                storage['quantity_used'] = $['+'](storage['quantity_used'], inventory);
                                                food['groups'][group_id].thucmuatheodvt = $['-'](food['groups'][group_id].thucmuatheodvt, inventory);
                                            } else {
                                                exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                                exps.foods[id].groups[group_id] = $['+'](exps.foods[id].groups[group_id], group['thucmuatheodvt']);
                                                storage['quantity_used'] = $['+'](storage['quantity_used'], group['thucmuatheodvt']);
                                                food['groups'][group_id].thucmuatheodvt = $['-'](food['groups'][group_id].thucmuatheodvt, group['thucmuatheodvt']);
                                            }
                                        });
                                    });

                                    angular.forEach(food['groups'], function (group, group_id) {
                                        if (group['thucmuatheodvt'] <= 0) {
                                            return;
                                        }
                                        var inventory = import_unlegal['quantity'] - import_unlegal['quantity_used'];
                                        if (inventory < 0) {
                                            return;
                                        }
                                        exps.total.groups[group_id] || (exps.total.groups[group_id] = 0);
                                        var id = food_id + '_' + import_unlegal['price'];
                                        if (!exps.foods[id]) {
                                            exps.foods[id] = {
                                                'name': food['name'],
                                                'measure_id': food['measure_id'],
                                                'food_id': food['food_id'],
                                                'supplier': food['supplier'],
                                                'gam_exchange': food['gam_exchange'],
                                                'extrude_factor': food['extrude_factor'],
                                                'price': import_unlegal['price'],
                                                'thucmuatheodvt': 0,
                                                'groups': {}
                                            };
                                        }
                                        exps.foods[id].groups[group_id] || (exps.foods[id].groups[group_id] = 0);
                                        if (inventory < group['thucmuatheodvt']) { /* Nếu lượng tồn nhỏ hơn lượng cần thì xuất hết */
                                            exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], inventory);
                                            exps.foods[id].groups[group_id] = $['+'](exps.foods[id].groups[group_id], inventory);
                                            import_unlegal['quantity_used'] = $['+'](import_unlegal['quantity_used'], inventory);
                                        } else {
                                            exps.foods[id]['thucmuatheodvt'] = $['+'](exps.foods[id]['thucmuatheodvt'], group['thucmuatheodvt']);
                                            exps.foods[id].groups[group_id] = $['+'](exps.foods[id].groups[group_id], group['thucmuatheodvt']);
                                            import_unlegal['quantity_used'] = $['+'](import_unlegal['quantity_used'], group['thucmuatheodvt']);
                                        }
                                        if (exps.foods[id].thucmuatheodvt === 0) {
                                            delete exps.foods[id];
                                        }
                                    });
                                    angular.forEach(food.exports, function (exp, fid) {
                                        exp.quantity_used = $['-'](exp.quantity, exp.inventory);
                                    });
                                    exps.export || (exps.export = {});
                                    exps.export[food_id] = food.exports;
                                    scope.cash_book.exports[warehouse_id] = exps;
                                    delete foods[food_id];


                                    /* Thêm thực phẩm thiếu còn lại đưa vào đi chợ*/
                                    if (!scope.cash_book.markets[warehouse_id]) {
                                        scope.cash_book.markets[warehouse_id] = {
                                            foods: {},
                                            total: {
                                                all: 0,
                                                groups: {},
                                                tong: {}
                                            }
                                        }
                                    }
                                    var markets = scope.cash_book.markets[warehouse_id].foods;
                                    var total = scope.cash_book.markets[warehouse_id].total;
                                    markets[food_id] || (markets[food_id] = {
                                        extrude_factor: food.extrude_factor,
                                        food_id: food_id,
                                        gam_exchange: food.gam_exchange,
                                        groups: {},
                                        measure_id: food.measure_id,
                                        name: food.name,
                                        price: food.price,
                                        price_kg: food.price_kg,
                                        luong1tre: 0,
                                        thucmua1nhom: 0,
                                        thucmuatheodvt: 0,
                                        supplier: food.supplier
                                    });
                                    markets[food_id].luong1tre = $['+'](markets[food_id].luong1tre, food.luong1tre);
                                    markets[food_id].thucmua1nhom = $['+'](markets[food_id].thucmua1nhom, food.quantity_import);
                                    markets[food_id].thucmuatheodvt = $['+'](markets[food_id].thucmuatheodvt, food.quantity_import);
                                    var inventory = food.inventory;
                                    angular.forEach(food.groups, function (item, group_id) {
                                        markets[food_id].groups[group_id] || (markets[food_id].groups[group_id] = {
                                            luong1tre: 0,
                                            thucmua1nhom: 0,
                                            thucmuatheodvt: 0,
                                            warehouses: {}
                                        });
                                        markets[food_id].groups[group_id].luong1tre = $['+'](markets[food_id].groups[group_id].luong1tre, item.luong1tre);
                                        var needGroup = item.thucmuatheodvt;
                                        /*if (item.thucmuatheodvt >= inventory && inventory > 0) {
                                            needGroup = needGroup - inventory;
                                            inventory = 0;
                                        }
                                        if (item.thucmuatheodvt <= inventory) {
                                            inventory = inventory - item.thucmuatheodvt;
                                            needGroup = 0;
                                        }*/
										var needGroupKg = (needGroup*food.gam_exchange)/1000; // Doi tu DVT ra kg, lit
                                        markets[food_id].groups[group_id].thucmua1nhom = $['+'](markets[food_id].groups[group_id].thucmua1nhom, needGroupKg);
                                        markets[food_id].groups[group_id].thucmuatheodvt = $['+'](markets[food_id].groups[group_id].thucmuatheodvt, needGroup);
                                        markets[food_id].groups[group_id].warehouses[warehouse_id] || (markets[food_id].groups[group_id].warehouses[warehouse_id] = {
                                            luong1tre: 0,
                                            thucmua1nhom: 0,
                                            thucmuatheodvt: 0
                                        });
                                        markets[food_id].groups[group_id].warehouses[warehouse_id].luong1tre = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].luong1tre, item.luong1tre);
                                        markets[food_id].groups[group_id].warehouses[warehouse_id].thucmua1nhom = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].thucmua1nhom, item.thucmua1nhom);
                                        markets[food_id].groups[group_id].warehouses[warehouse_id].thucmuatheodvt = $['+'](markets[food_id].groups[group_id].warehouses[warehouse_id].thucmuatheodvt, item.thucmuatheodvt);
                                        total.groups[group_id] || (total.groups[group_id] = 0);
                                        total.groups[group_id] = $['+'](total.groups[group_id], $['*'](item.thucmuatheodvt, food.price));
                                    });
                                    total.all = $['+'](total.all, $['*'](food.thucmuatheodvt, food.price));
                                    delete foods[food_id];
                                }
                            }
                        });
                    });
                    if (check_import.length > 0) {  /*  Cảnh báo thực phẩm chọn nhập kho không đủ lượng cần thiết*/
                        $.alert('<b>Một số thực phẩm chọn nhập kho không đạt mức tối thiểu:</b><br/> - ' + check_import.join('<br/> - '));
                    } else {
                        if (check_insert_export) { /* Nếu có thực phẩm chọn kho thì tính lại tổng toàn bộ và tổng từng nhóm tuổi*/
                            angular.forEach(scope.cash_book.exports, function (warehouse, warehouse_id) {
                                var total = {all: 0, groups: {}};
                                angular.forEach(warehouse.foods, function (food, id) {
                                    angular.forEach(food.groups, function (value, group_id) {
                                        total.all = $['+'](total.all, $['*'](food.price, value.thucmuatheodvt));
                                        total.groups[group_id] || (total.groups[group_id] = 0);
                                        total.groups[group_id] = $['+'](total.groups[group_id], $['*'](food.price, value.thucmuatheodvt));
                                    });
                                });
                                warehouse.total = total;
                            });
                        }
                        dialogRef.close();
                        callback()
                    }
                };
                scope.cash_book.chonRauqua = function () {
                    $.cash_book.showFormChonrauqua(scope);
                };
                scope.cash_book.printCash_bookTypeChange = function (date, type) {
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'cash_book', 'reportPreview'];
                    var btn = $('<a title="Xem và in"><span class="glyphicon glyphicon-print fa-2x">Xem trước</span></a>');
                    var group_ids = scope.cash_book.getGroupsSelected();
                    url_preview = urls_export.join('/') + '?type=' + type + '&warehouse_ids=' + scope.cash_book.getWarehouse_printSelected().join(',') + '&date=' + date + '&preview=1&group_ids=' + group_ids;
                    $('#export-dialog-cashbook #btn-form-link #btn_preview').attr("href", url_preview);
                };
                scope.cash_book.getWarehouse_printSelected = function () {
                    var wh = [];
                    if (scope.cash_book.selected.warehouse_ids[1]) {
                        wh.push(1);
                    }
                    if (scope.cash_book.selected.warehouse_ids[2]) {
                        wh.push(2);
                    }
                    return wh;
                };
                /*Quyết toán trong ngày không được tách kho, phải tính hết*/
                scope.cash_book.printPreviewInday = function () {
                    var urls_export = ['tmp', $CFG.project, 'cash_book'];
                    $.dm_datagrid.showEditForm(
                        {
                            title: 'Chọn kiểu của biểu mẫu Sổ tính khẩu phần ăn của trẻ',
                            size: size.normal,
                            showButton: false,
                            content: function (element) {
                                loadForm(urls_export.join('/'), 'popup_quyettoanngay.html', {async: true}, function (resp) {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(resp, scope));
                                    })
                                });
                            }
                        }
                    );
                    // var wh = scope.cash_book.getWarehouse_printSelected();
                    // var urls_export = [$CFG.remote.base_url,'report',$CFG.project,'cash_book','reportPreviewInday'];
                    // $('#export-dialog-cashbook-inday').dialog({
                    //     title: 'Quyết toán trong ngày',
                    //     width: 500,
                    //     height: 400,
                    //     closed: false,
                    //     cache: false,
                    //     modal: true,
                    //     onOpen : function (ele) {
                    //         $(ele).show();
                    //         var btn = $('<a title="Xem và in" class="btn btn-hover"><span class="glyphicon glyphicon-print fa-2x"></span> In</a>');
                    //         $('#export-dialog-cashbook-inday #btn-form-view').html('').append(btn);
                    //         btn.printPage({
                    //             url: urls_export.join('/')+'?date='+date+'&preview=1',
                    //             attr: "href",
                    //             message:"Phiếu xuất kho đang được tạo ..."
                    //         })
                    //     }
                    // });
                };
                scope.cash_book.warehouse_printChange = function (i, type) {
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'cash_book', 'reportPreview'];
                    var wh = scope.cash_book.getWarehouse_printSelected();
                    if (wh.length == 0) {
                        scope.cash_book.selected.warehouse_print[i] = true;
                    }
                    // var group_ids = [];
                    // angular.forEach(scope.cash_book.menu_plannings, function (menu_planning,ind) {
                    //     if (menu_planning.selected) {
                    //         group_ids.push(menu_planning.group_id);
                    //     }
                    // })
                    // wh = scope.cash_book.getWarehouse_printSelected();
                    // var btn = $('<a title="Xem và in"><span class="glyphicon glyphicon-print fa-2x">In</span></a>');
                    // $('#export-dialog-cashbook #btn-form-view').html('').append(btn);
                    // btn.printPage({
                    //     url: urls_export.join('/')+'?type='+type+'&warehouse_ids='+scope.cash_book.getWarehouse_printSelected().join(',')+'&date='+scope.cash_book.date+'&preview=1&group_ids='+group_ids.join(','),
                    //     attr: "href",
                    //     message:"Phiếu xuất kho đang được tạo ..."
                    // })
                };
                scope.cash_book.printPreviewCashbook = function (date, type) {
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'cash_book', 'reportPreview'];
                    var group_ids = [];
                    angular.forEach(scope.cash_book.menu_plannings, function (menu_planning, ind) {
                        if (menu_planning.selected) {
                            group_ids.push(menu_planning.group_id);
                        }
                    });
                    $('#export-dialog-cashbook').dialog({
                        title: 'In sổ tính tiền ăn',
                        width: 400,
                        height: 200,
                        closed: false,
                        cache: false,
                        modal: true,
                        onOpen: function (ele) {
                            $(ele).show();
                            var type_selected = $('#export-dialog-cashbook input[type="radio"]:checked').val();
                            url_preview = urls_export.join('/') + '?type=' + type_selected + '&warehouse_ids=' + scope.cash_book.getWarehouse_printSelected().join(',') + '&date=' + date + '&preview=1&group_ids=' + scope.cash_book.getGroupsSelected();
                            $('#export-dialog-cashbook #btn-form-link #btn_preview').attr("href", url_preview);

                            var btn = $('<a class="btn btn-hover" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span> In</a>');
                            $('#export-dialog-cashbook #btn-form-view').html('').append(btn);
                            btn.printPage({
                                url: urls_export.join('/') + '?type=' + type + '&warehouse_ids=' + scope.cash_book.getWarehouse_printSelected().join(',') + '&date=' + date + '&preview=1&group_ids=' + group_ids.join(','),
                                attr: "href",
                                message: "Phiếu xuất kho đang được tạo ..."
                            })
                        }
                    });
                };
                scope.cash_book.getGroupsSelected = function (arr) {
                    var group_ids = [];
                    angular.forEach(scope.cash_book.groups, function (group, group_id) {
                        if (group.selected) {
                            group_ids.push(group_id);
                        }
                    });
                    if (arr) {
                        return group_ids;
                    }
                    return group_ids.join(',');
                };
                scope.cash_book.printPreviewcongkhaitaichinh = function (date, type) {
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'cash_book', 'reportPreviewCongkhaitaichinh'];
                    $('#export-dialog-congkhaitaichinh').dialog({
                        title: 'In công khai tài chính',
                        width: 400,
                        height: 200,
                        closed: false,
                        cache: false,
                        modal: true,
                        onOpen: function (ele) {
                            $(ele).show();
                            var btn = $('<a class="btn btn-hover" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span> In</a>');
                            $('#export-dialog-congkhaitaichinh #btn-form-view').html('').append(btn);
                            btn.printPage({
                                url: urls_export.join('/') + '?type=' + type + '&warehouse_ids=' + scope.cash_book.getWarehouse_printSelected().join(',') + '&date=' + date + '&preview=1',
                                attr: "href",
                                message: "Phiếu báo cáo đang được tạo ..."
                            })
                        }
                    });
                };
                scope.cash_book.doichieutiencho = function (date, type) {
                    var urls_export = ['tmp', $CFG.project, 'cash_book'];
                    $.dm_datagrid.showEditForm(
                        {
                            title: 'Chọn mẫu biểu',
                            size: size.normal,
                            showButton: false,
                            content: function (element) {
                                loadForm(urls_export.join('/'), 'popup_doichieutiencho.html', {async: true}, function (resp) {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(resp, scope));
                                    })
                                });
                            }
                        }
                    );
                    // $('#export-dialog-doichieutiencho').dialog({
                    //     title: 'In đối chiếu tiền chợ',
                    //     width: 420,
                    //     height: 350,
                    //     closed: false,
                    //     cache: false,
                    //     modal: true,
                    //     onOpen : function (ele) {
                    //         $(ele).show();
                    //         var btn = $('<a class="btn btn-hover" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x"></span> In</a>');
                    //         $('#export-dialog-doichieutiencho #btn-form-view').html('').append(btn);
                    //         btn.printPage({
                    //             url: urls_export.join('/')+'?type='+type+'&warehouse_ids='+scope.cash_book.getWarehouse_printSelected().join(',')+'&date='+date+'&preview=1',
                    //             attr: "href",
                    //             message:"Phiếu báo cáo đang được tạo ..."
                    //         })
                    //     }
                    // });
                };
                scope.cash_book.warehousePrintChange = function (id, date) {
                    if (!scope.cash_book.selected.warehouse_print[1] && !scope.cash_book.selected.warehouse_print[2]) {
                        scope.cash_book.selected.warehouse_print[id] = true;
                    }
                    var wh = [];
                    if (scope.cash_book.selected.warehouse_print[1]) {
                        wh.push(1);
                    }
                    if (scope.cash_book.selected.warehouse_print[2]) {
                        wh.push(2);
                    }
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'cash_book', 'reportPreviewInday'];
                    var btn = $('<a title="Xem và in"><span class="glyphicon glyphicon-print fa-2x">In</span></a>');
                    $('#export-dialog-cashbook-inday #btn-form-view').html('').append(btn);
                    btn.printPage({
                        url: urls_export.join('/') + '?date=' + date + '&warehouse_ids=' + wh.join(',') + '&preview=1',
                        attr: "href",
                        message: "Phiếu xuất kho đang được tạo ..."
                    })
                };
                /*Thay đổi lựa chọn kiểu in: in hết hay từng kho */
                scope.cash_book.storage_exportTypeChange = function (date, type) {
                    type || (type = '');
                    var btn = $('<a id="btn_preview-form" title="Xem và in"><span class="glyphicon glyphicon-print fa-2x">In</span></a>');
                    $('#export-dialog-storageExport .btn-print-form').html('').append(btn);
                    var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'storage_export', 'exportExcelPhieuXuatKho'];
                    btn.printPage({
                        url: urls_export.join('/') + '?type=' + type + '&date=' + date + '&preview=1',
                        attr: "href",
                        message: "Phiếu xuất kho đang được tạo ..."
                    });
                };
                /*bật popup chọn in xuất kho*/
                scope.cash_book.storageExportForm = function () {
                    scope.sys = window.sysConfigs;
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project + '/' + self.module,
                            title: 'Phiếu xuất kho',
                            size: size.wide,
                            fullScreen: false,
                            showButton: false,
                            content: function (element) {
                                loadForm($CFG.remote.base_url + '/tmp/dinhduong/' + self.module + '/phieu-xuat-kho.html', '', {async: true}, function (resp) {
                                    scope.$apply(function () {
                                        $(element).html(scope.compile(resp, scope));
                                    })
                                });
                            }
                        }
                    );
                };
                scope.cash_book.exportListFood = function () {
                    $.dm_datagrid.showAddForm(
                        {
                            module: $CFG.project + '/' + self.module,
                            title: 'Bảng kê VAT hàng chợ',
                            fullScreen: false,
                            showButton: false,
                            content: function (element) {
                                loadForm($CFG.remote.base_url + '/tmp/dinhduong/menu_report/bang-ke-thuc-pham.html', '', {async: true}, function (resp) {
                                    scope.$apply(function () {
                                        if(!scope.suppliers) {
                                            scope.getSuppliers();
                                        }
                                        /*Set thông tin cho form*/
                                        scope.begin = scope.cash_book.selected.date;
                                        scope.end = scope.cash_book.selected.date;
                                        scope.warehouse = scope.cash_book.getWarehouse_printSelected().join(',');
                                        scope.type = 'listFoodByMarketVat';
                                        scope.showTypeReport = false;
                                        scope.isPrint = true;
                                        $(element).html(scope.compile(resp, scope));
                                    });
                                });
                            }
                        }
                    );
                };
                scope.getSuppliers = function() {
                    process($CFG.project + '/action/getSuppliers', {async: true}, function (resp) {
                        scope.$apply(function () {
                            scope.suppliers = resp.data.map;
                        });
                    });
                }
                scope.cash_book.storageExportStyle = function (style) {
                    var value = scope.print.storage_exportType != '' ? scope.print.storage_exportType : '1,2';
                    scope.cash_book.style = style;
                    if(style == 5)
                    {
                        var checkboxes = document.getElementsByName('check_eat');
                        var result = [];
                        for (var i = 0; i < checkboxes.length; i++) {
                            if (checkboxes[i].checked) {
                                result.push(checkboxes[i].value)
                            }
                          }
                        var food_id = result.join(',')
                        window.open($CFG.remote.base_url + '/dinhduong/stockReport/export?warehouses=' +  value + '&begin=' + scope.cash_book.selected.date + '&end=' + scope.cash_book.selected.date + '&print=' + true + '&food_id='+ food_id,'_blank')
                    }

                };
                scope.cash_book.exportsExcel = function (grouping_char) {
                    var warehouses = [];
                    angular.forEach(scope.cash_book.selected.warehouse_ids, function (value, warehouse_id) {
                        if (value) {
                            warehouses.push(warehouse_id);
                        }
                    });
                    if (scope.cash_book.val_title == null) {
                        scope.cash_book.val_title = 1;
                    }
                    if (scope.cash_book.val_title == 1) {
                        var is_storage = 0; // Ko bao cao
                        if (scope.cash_book.is_storage) {
                            is_storage = 1; // Nhap kho
                        }
                        if (scope.cash_book.is_storage_export) {
                            is_storage = -1; // Xuất kho
                        }
                        var urls_export = $CFG.remote.base_url + '/dinhduong/cash_book/reportPreviewTheonhacungcap?date=' + scope.cash_book.date + '&type=' + scope.cash_book.val_type + '&is_market=' + (scope.cash_book.is_market?1:0) + '&is_storage=' + is_storage;
                    }
                    if (scope.cash_book.val_title == 2) {
                        var month = scope.cash_book.selected.month.month + '/' + scope.cash_book.selected.month.year;
                        var urls_export = $CFG.remote.base_url + '/dinhduong/cash_book/reportPreviewTheothang?date=' + month + '&type=' + scope.cash_book.val_type_month;
                    }
                    if (scope.cash_book.val_title == 3) {
                        var date = document.getElementById("ngay_dau").value;
                        var urls_export = $CFG.remote.base_url + '/dinhduong/cash_book/reportPreviewTheotuan?date=' + date;
                    }
                    if (scope.cash_book.val_title == 4) {
                        var urls_export = $CFG.remote.base_url + '/dinhduong/cash_book/reportPreviewTheoThucPham?date=' + scope.cash_book.date;
                    }
                    if (scope.cash_book.val_title == 5) {
                        var urls_export = $CFG.remote.base_url + '/dinhduong/cash_book/reportPreviewTheoNgay?date=' + scope.cash_book.date;
                    }
                    // window.location.assign(urls_export);
                    return urls_export + '&warehouse_ids=' + warehouses.join(',')+'&grouping_char='+grouping_char;
                }
                $scope.cash_book.export_cash_book_single_sheet = false;
                /*  Lấy tháng mặc định */
                var now = new Date();
                var m = now.getMonth() + 1;
                angular.forEach(scope.cash_book.months, function (month, index) {
                    if (month.id == m) {
                        scope.cash_book.selected.month = month;
                        return;
                    }
                });
                $.cash_book.getList(m);
            });
        }, 0);
    }, /* XÓA */
    del: function (index, date, callback, captcha) {
        var self = this;
        if (!date) {
            var data = $('#tbl_cash_book').datagrid('getData');

            if (!data.rows) {
                alert('Không tìm thấy dữ liệu.');
                return;
            }
            var row = data.rows[index];
            if (!row) {
                alert('Không tìm thấy dữ liệu.');
            }
            date = row.date;
        }
        var msg = ['<div style = "font-size: 14px; font-weight:bold; color:red;">Chắc chắn xóa?</div>'];
        msg.push('<span style="color:red"> - Xóa hết các sổ tính tiền từ ngày <b style="font-size:14px;">(' + date + ')</b>.</span>');
        msg.push('<span style="color:red">  - Xóa hết các thực phẩm nhập kho đã nhập không đúng quy trình theo sổ tính tiền ăn bị xóa.</span>');
		msg.push('<span style="color:red">  - Dữ liệu sau khi xóa không thể khôi phục được.</span>');
        msg.push(captcha);
        $.messager.confirm('Xác nhận', msg.join('<br/>'), function (r) {
            if (r) {
                var data = {captcha: $('[name="delete_cash_book_captcha"]').val()};
                $.dm_datagrid.del($CFG.project + '/' + self.module, date, function (resp) {
                    $("#tbl_" + self.module).datagrid('reload');
                    if (typeof callback == "function") {
                        callback();
                    }
                }, data)
            }
        });
    }, angular: function (element, html, callback) {
        var form = '<div >' + html + '</div>';
        // $(element).html(form);
        angular.element($('#mainContentController')).scope().$apply(function (scope) {
            $(element).html(scope.compile(form, scope));
            if (typeof callback === 'function') {
                setTimeout(function () {
                    scope.$apply(function () {
                        callback(scope, element);
                    });
                });
            }
        });

    }, getDataForm: function (scope) {
        angular.forEach(scope.cash_book.exports, function (warehouse, id) {
            angular.forEach(warehouse.foods, function (food, key) {
                food.groups = scope.cash_book.goexports[id]['foods'][key]['groups'];
            })
        });
        var data = {
            date: scope.cash_book.date,
            surplus: scope.cash_book.surplus,
            menu_plannings: scope.cash_book.menu_plannings,
            services: scope.cash_book.services,
            markets: scope.cash_book.markets,
            total_services: scope.cash_book.total_services,
            tonghop: scope.cash_book.tonghop,
            exports: scope.cash_book.exports,
            dachitrongngay: scope.cash_book.dachitrongngay
        };
        return JSON.stringify(data);
    }, exportForm: function () {
        var urls_export = [$CFG.remote.base_url, 'report', $CFG.project, 'storage_export', 'exportExcelPhieuXuatKho'];
        $('#export-dialog').dialog({
            title: 'Thông báo',
            width: 400,
            height: 200,
            closed: false,
            cache: false,
            modal: true,
            onOpen: function (ele) {
                $(ele).show();
                var selectedrow = $("#tbl_cash_book").datagrid("getSelected");
                var row = selectedrow;
                if (!row) {
                    alert('Không tìm thấy dữ liệu.');
                }
                var date = row.date;
                $('#btn_export').click(function () {

                    var type = $('#export_type').val();
                    location = urls_export.join('/') + '?type=' + type + '&date=' + date + '&preview=0';
                });
                $.getScript($CFG.remote.base_url + '/js/jQuery-printPage-plugin/jquery.printPage.js').done(function () {
                    var selectedrow = $("#tbl_cash_book").datagrid("getSelected");
                    var row = selectedrow;
                    if (!row) {
                        alert('Không tìm thấy dữ liệu.');
                    }
                    var date = row.date;
                    $('#btn_preview').click(function () {
                        var type = $('#export_type').val();
                        $('#btn_preview').printPage({
                            url: urls_export.join('/') + '?type=' + type + '&date=' + date + '&preview=1',
                            attr: "href",
                            message: "Phiếu xuất kho đang được tạo ..."
                        });
                    });

                });
            }
        });
    },
    initCashBook: function () {
        setTimeout(function () {
            angular.element($('#mainContentController')).scope().$apply(function (scope) {
                process('dinhduong/menu_report/cashBook', {}, function (resp) {
                    $.cash_book.groups = scope.menu_report.groups;
                    $.cash_book.warehouses = scope.menu_report.warehouses;
                    $.cash_book.measures = resp.measures;
                    $.cash_book.menuinforations = resp.menuinforations;
                    $.cash_book.months = resp.months;
                    $.cash_book.suppliers = resp.suppliers;
                    $.cash_book.initAngular();
                }, null, false);
            })
        });
    }
};
