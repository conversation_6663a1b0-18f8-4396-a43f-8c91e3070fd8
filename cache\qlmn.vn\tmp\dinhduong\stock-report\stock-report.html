<div class="dialog">
    <table>
        <tr>
            <th>Thời gian</th>
            <td>
                <input date-box="begin" style="width: 100px;"
                       title="thời gian bắt đầu" class="input-custom-style"/>
            </td>
            <td colspan="2">- &nbsp;&nbsp;
                <input date-box="end" style="width: 100px;"
                       title="thời gian kết thúc" class="input-custom-style"/>
            </td>
            <th></th>
            <td><a href="http://localhost:3000/documents/sys_templates/so/so_theo_doi_nhap_xuat_kho.doc?_=1893757132"
                   title="File mẫu">Theo dõi nhập - xuất </a></td>
        </tr>
        <tr>
            <th>Kho</th>
            <td>
                <select ng-model="warehouse" title="Chọn kho"
                        class="input-custom-style" style="width: 100px">
                    <option value="1,2" selected>T<PERSON>t c<PERSON></option>
                    <option value="1">Sáng</option>
                    <option value="2">Trưa</option>
                </select>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <th>
                Báo cáo
            </th>
            <td colspan="3">
                <select ng-model="type" title="Chọn mẫu"
                        class="input-custom-style" style="width: 100%" ng-change="changeType(type)">
                    <option value="default">- Chọn mẫu -</option>
                    <option value="stockDetail">Chi tiết sổ kho</option>
                    <option value="stockTake">Kiểm kê cuối tháng </option>
                    <option value="stockTakeResources">Kiểm kê nguyên liệu (C32 - HD)</option>
                    <option value="warehousing">Nhập/Xuất - Nhập hàng kho</option>
                    <option value="stockByFood">Nhập/Xuất - Theo thực phẩm</option>
                    <option value="stockByDay">Sổ kho</option>
                    <option value="warehouseCard">Thẻ kho</option>
                    <option value="followStock">Theo dõi sổ kho</option>
                    <option value="inventory">Tồn kho</option>
                    <option value="receipt">Phiếu nhập kho (C30 - HD)</option>
                    <option value="export">Phiếu xuất kho (C31 - HD)</option>
                    <option value="exportPoints" ng-if="$CFG.school_points>1 && $CFG.school_point_together==1">Phiếu xuất kho điểm trường</option>
                </select>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="3">
                <a ng-show="isExport" href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/stockReport/' + type + '?warehouses='+ warehouse + '&amp;begin=' + begin + '&amp;end=' + end}}"
                   target="_blank" class="btn btn-default">
                    <i class="fa fa-download"></i> Tải
                </a>
                <a ng-show="isExport" href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/stockReport/' + type + '?warehouses='+ warehouse + '&amp;begin=' + begin + '&amp;end=' + end+'&rpt_type=2'}}"
                   target="_blank" class="btn btn-default" ng-if="type=='stockTake' && $CFG.province=='46'">
                    <i class="fa fa-download"></i> Tải mẫu 2
                </a>
            </td>
            <td>
                <a ng-show="isPrint" href="{{ $CFG.remote.base_url+'/'+$CFG.project+'/stockReport/' + type + '?warehouses='+ warehouse + '&amp;begin=' + begin + '&amp;end=' + end + '&amp;print=' + true}}"
                   target="_blank" class="btn btn-default">
                    <i class="fa fa-print"></i> Xem trước
                </a>
            </td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <th></th>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</div>
<style>
    .dialog {
        padding: 10px;
    }
    .dialog table th {
        min-width: 90px;
        padding-right: 10px;
        padding-bottom: 10px;

    }
</style>