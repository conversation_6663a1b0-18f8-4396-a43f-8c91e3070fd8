{"request": {"url": "https://qlmn.vn/doing/dinhduong/dish/list?status=false&_=1", "method": "POST", "headers": {"connection": "keep-alive", "pragma": "no-cache", "cache-control": "no-cache", "sec-ch-ua-platform": "\"Windows\"", "x-csrf-token": "CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Brave\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/javascript, */*; q=0.01", "dnt": "1", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "sec-gpc": "1", "origin": "http://localhost:3000", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:3000/single/dinhduong/dish/list", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9,vi;q=0.8", "cookie": "authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiZ1hSQ0NVaTNaZGRva2VoQ0NKdExBOFYzdENzdGJqVGJ4akV5czZGNFIxTDFyRlFmT056Y0xZbkFkZFdwbkhwMF9GUko1NVlMNW5RWG1qMkF1enlCYkEifQ..CYZuZQMi3UweO8EqkJVjyQ.2ncZWaXznq5qIH4xTeeqmmbZBLFPbJPJRl6McXg0PuTn9qk2RRqLnc1v6UWEidR_ssdVI1hHQjDZqgDd2RcY4PYDUKHPQAo9yM7nKXCIDoFYwuHNZC1ge7XDXMmET3_XacK9VLp-MEzc9hLOZkZZC6bKBBFHvkCzNycONocxJGOaj7Dbo2S9vGolzr7qBKQFN6b4bE9C1h4fOJUWmxth4D6ZxS3K9GNRWstZQiNEZa6XYIlN2o2MaAiSQne1YNPYv8DpN_3zjeVFMGHn3G-Xbms-dqKrw4epoK2qiO6e_zJslWjoJefe4y-NeTuyby_nXzMPMXozEOl5wMfHv6UNqybbxsIe3EgorhVKEPt07bln0NZrYV-o1XlR5Jd3HViDOIeuiaKwoxJtjRNrj_9gFCemCNyAiKzkr0qtN3oIWwV3L3gORwSphTygFWGE_ICqkqO8EAFCCPDGVGRALAW4rkrdSIn4aD4Eb4WQ9pGGpHb7fwTp8HECCTpMXT0a4hDEZsZNLeaTLGTwRoWzCuKF-o4NPMMzpnQDq7B6wwluJN6gYKhcIuumBPSfrrTBl3PjIzeZhiCVojV1SGT9V7MAjexwCy1gZwaqeYdWOEn47ubQpEotOED_x19o65Izu-5S48CMtD9uTTMgj-sH9ps1QKO7vHQctlLITLhsIVt2Ww_4iqu8N_5Mvb0lLaZjo1O7hXA5nCGFChAr7-UZlHRQf6xPEk4i4-kTzOPukP63-fsSwhdaP4Zo4T4xkCBUJiYbsVLGUSmkzTQgQN3EzJ8LXg.5h8b88G_eJS0BAVbcHsK0i3Bqzsha4iYJC-p4qV7JRk; fblo_1243027207556797=y; __Secure-authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiTDR5b0l4UkJucEgzX2k0RGpIM09Eb2RkUlZlWEFSdWpDLS1WMjdPYlVXVGxVeTJrZG5ibldmYTNWUS0xZXlILVp1RS1HY3VIWnRQSFV3S3VQb2VZVmcifQ..QRZZhtIV7cpbGuQTKgOHNQ.CqTx0b4Fybgll1gcjstmm1p_3bMQtKcyIL9JSZ37BJx_mFdZngcR3pMMyh8AUNYqQevv-O3LM3U7mZLSr35nsY6K4oRMFpPBQwmK9lD8AV6ujg-HUBThADK035rzhHqCsEjoAmbLmbr1bYxoXTXVGHtLyXzYQYD9pluVzE3PYdemvE2ew8CbhYYOolcmVbr4fBMxQeAkL-TT5Q2DQxlX1tHcNyrNv_3CYXLyKLPVSbr6Yu-Huhcaq9tg81iUWzo3gFQHGaOQUxcRZ6_JKvanu2o9pnQnLO__X6OwLbhDVvtvUkL-krLzdPFf74ulqniLKDGC8kEoEZ8BZrgAuBGri328TgiSh3XBio5xm5hLWvrwjYQW9eivnsDE__9akFje_OQ8jkg44mXOZzVw6X37-ixaWD1hjR-UdDLJuqetx0b9ydm_FPMV2xA6tVwvzj-pSTO8Ma0BH-1o0E8Q8WzVgdssnELpfvt6jNVvJbg4z_CZnSvABaWFZHq6VUn1gK6Jszlz_tj1EoBE9RaF6c7W6jWzj2u1rrej5HkZvSzMD26TeCL5-6gQQehnt4xWLboVCH8r95vD5Ktgr4xuqBacDHvBRKobD_40dBJtq1NUveGsC91Q6nzI-2ymzPzFLGVM.NWGigHg5VYjAv3M-fL9KxSOV1Iy8K1wrKed7VKiNmLE; _ga=GA1.1.7460584.1754620091; _gid=GA1.1.1442505230.1754620091; visitor11234=1; _ga_J3K0TBMXZ1=GS2.1.s1754620090$o1$g1$t1754620219$j32$l0$h0; laravel_session=eyJpdiI6ImxVZWFvdG82UGdDakNIOG90djZRRWc9PSIsInZhbHVlIjoia1wvNnNFaDMwYUtVbWppTEJIZVwvRERNOUJGZTNFTWNxT3UwQzNFNlJZaUQ1V041eFUxU3Q3RVJxNkFhMmtBR2VXXC9sYkpreERkdEpXK0w1M09GS1BUTkE9PSIsIm1hYyI6ImE1ZTM3M2U3MjhjZWQ4MGQ3ZjQ0ZTg3M2JmMDkwMjlkNzBkMGVkMzY3OTRlZTY4YjI3MzUyNGQ3MDAzOTI0MzEifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Ijc2TitGQzdZQUNldmhqT0pGOHJDbUE9PSIsInZhbHVlIjoiWTdBbk9hTURnT1oyeTF2ZW5RY3V2VmRqSG15RWxDVkZVdzhkZFZsWHZyVnNuNVhQZDIxRUh4b3RMUmdLdkVzNWxza1hBNWtRR1FxMXYra0lvWGdIM0E9PSIsIm1hYyI6IjA0ZjgzYjkyZWExZWRlZjhkNmY1Njg1OTkwZTNkMmIwM2RlZTJlYjQ2OTZkNmNjNTU3OGE3MGY3NjE5MmVmZTgifQ%3D%3D"}, "body": {"page": "2", "rows": "30"}}, "response": {"status": 200, "headers": {"date": "Fri, 08 Aug 2025 02:31:27 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "vary": "Accept-Encoding", "cache-control": "max-age=43200, public, s-maxage=43200, no-cache", "expires": "Fri, 08 Aug 2025 14:31:27GMT", "set-cookie": "XSRF-TOKEN=eyJpdiI6IkN2dlVCNTRoNzkzeUdyUWJudkVPYlE9PSIsInZhbHVlIjoiREpDWHZcL2ViVDZwcDkyelZXbVNzNTFxTmRlRStBRGxlakE3U1lVSjF3WVZYM014eFRcL0IzWkVuMTZGNDZ6aTNGZFBnK3ZCRDQ1TTZHOHArTisyYkd0Zz09IiwibWFjIjoiOTMwNWJjZDFmOWQxZmVjMTFiZjllNDEzN2I2NDcwNDU0ZjA0MGUyMWMyY2NjMTk1YzI0Y2YyMDNhMDU0YTE5ZSJ9; expires=Fri, 08-Aug-2025 04:31:27 GMT; Max-Age=7200; path=/; Secure; HttpOnly;; Secure; HttpOnly; SameSite=Lax, laravel_session=eyJpdiI6IlZETGRMeVcwd044VjhpYVAxTnlZc1E9PSIsInZhbHVlIjoiV1RYY005YUpLR0dyNlQybFBVd3l0TXRJYm4wOXptSllBck5ZdHB6bFwvdEkwcDZ4cUY0dUswU01XN3plWjlBY0hXMnV4Qm5DeFFzVitHYkwxS0IySlF3PT0iLCJtYWMiOiIzYjJjMDlmZWU4MWY3OWJhYWYzMmY3ZGVmMWExYmZlMDI4MzRjMWMwMGZlYTBlZDUwZjhhYWE5OGRkNDQ2N2RiIn0%3D; path=/; Secure; HttpOnly;; secure; httponly; SameSite=Lax", "x-vietec": "PMS-70", "x-frame-options": "DENY", "content-security-policy": "frame-ancestors 'self';", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-xss-protection": "1; mode=block"}, "body": "eyJwYWdlIjoiMiIsInRvdGFsIjozMSwicm93cyI6W3siaWQiOjE2NTY2NDcxMCwibmFtZSI6IlhcdTAwZjRpIFx1MDExMVx1MWVkNyB4YW5oLCB0aFx1MWVjYnQgZ1x1MDBlMCIsImRlc2NyaXB0aW9uIjoiIiwiZ3JvdXBfaWRzIjoiIiwibnV0cml0aW9uX3NldCI6IiIsInJlZ2lvbl9pZCI6bnVsbCwiYXJlYV9pZHMiOiIiLCJjYXRlZ29yeV9pZHMiOiIiLCJjb250ZW50IjoiIiwiYXZhdGFyIjoiZmlsZXNcL2ltYWdlc1wvZGVtby5qcGciLCJpbmdyZWRpZW50Ijoie1wiNTg2XCI6e1wiZm9vZF9pZFwiOjU4NixcInVuaXRfaWRcIjoyNTM5LFwibmFtZV9iYXNlXCI6XCJHYW8gbmVwXCIsXCJuYW1lXCI6XCJHXFx1MWVhMW8gblxcdTFlYmZwXCIsXCJpc192ZWdldFwiOjAsXCJpc19kcnlcIjowLFwiZXh0cnVkZV9mYWN0b3JcIjowLFwic3VwcGxpZXJcIjoxLFwicHJvZHVjdF9vZlwiOlwiXCIsXCJwYWNrYWdpbmdcIjpcIlwiLFwicHJpY2VcIjoyNjAwMCxcIm1pbl9pbnZlbnRvcnlcIjowLFwibWVhc3VyZV9pZFwiOjEsXCJnYW1fZXhjaGFuZ2VcIjoxMDAwLFwibmd1Y29jXCI6MCxcInJhdVwiOjAsXCJ0cmFpY2F5XCI6MCxcImRhbWR2XCI6MCxcImlzX3N0b3JhZ2VcIjoxLFwiaWRcIjo1ODYsXCJmb29kX25hbWVcIjpcIkdcXHUxZWExbyBuXFx1MWViZnBcIixcImZvb2RfZXh0cnVkZV9mYWN0b3JcIjowLFwiZm9vZF9taW5faW52ZW50b3J5XCI6MCxcImZvb2RfbWVhc3VyZV9pZFwiOjEsXCJmb29kX3ByaWNlXCI6MjUwMDAsXCJudXRyaXRpb25zXCI6e1wicHJvdGVpblwiOjguNixcImZhdFwiOjEuNSxcInN1Z2FyXCI6NzQuNSxcImNhbG9cIjozNDQsXCJjYW54aVwiOjMyLFwiYjFcIjowLjE0fSxcImZvb2RfZ2FtX2V4Y2hhbmdlXCI6MTAwMCxcImlzX21lYXRcIjowLFwiZm9vZF9pc19kcnlcIjowLFwic2VhcmNoX2Zvb2RfbmFtZVwiOlwiR1xcdTFlYTFvIG5cXHUxZWJmcEdhbyBuZXBcIixcIm5hbWVfbGVuZ3RoXCI6NyxcImZvb2RfbmFtZV9sZW5ndGhcIjoxMSxcIm5hbWVfb3JkZXJcIjoxLFwiZmF0XCI6MS41LFwic3VnYXJcIjo3NC41LFwiY2Fsb1wiOjM0NCxcImIxXCI6MC4xNCxcInByb3RlaW5cIjo4LjYsXCJjYW54aVwiOjMyLFwibWVhc3VyZV9uYW1lXCI6XCJLZ1wiLFwidGFtXCI6ZmFsc2UsXCJxdWFudGl0aWVzXCI6W10sXCJlYXRfYV9ncm91cFwiOjAuMzUsXCJidXlfYV9ncm91cFwiOjAuMzUsXCJidXlfYV9kdnRcIjowLjM1LFwicXVhbnRpdHlfZm9yX21lYXN1cmVcIjowLjAzNSxcImNhbG9fZm9yX29uZVwiOjEyMC40LFwicXVhbnRpdHlcIjozNSxcIm51bWJlcl9jaGlsZFwiOjEwLFwic3VtX2NhbG9cIjoyMDAuNjQsXCJ0cGNcIjowLFwic2VsZWN0ZWRcIjpmYWxzZX0sXCI2NjNcIjp7XCJmb29kX2lkXCI6NjYzLFwidW5pdF9pZFwiOjI1MzksXCJuYW1lX2Jhc2VcIjpcIkRhdSB4YW5oXCIsXCJuYW1lXCI6XCJcXHUwMTEwXFx1MWVhZHUgeGFuaFwiLFwiaXNfdmVnZXRcIjowLFwiaXNfZHJ5XCI6MCxcImV4dHJ1ZGVfZmFjdG9yXCI6MixcInN1cHBsaWVyXCI6XCJcIixcInByb2R1Y3Rfb2ZcIjpcIlwiLFwicGFja2FnaW5nXCI6XCJcIixcInByaWNlXCI6NDAwMDAsXCJtaW5faW52ZW50b3J5XCI6MCxcIm1lYXN1cmVfaWRcIjoxLFwiZ2FtX2V4Y2hhbmdlXCI6MTAwMCxcIm5ndWNvY1wiOjAsXCJyYXVcIjowLFwidHJhaWNheVwiOjAsXCJkYW1kdlwiOjAsXCJpc19zdG9yYWdlXCI6MCxcImlkXCI6NjYzLFwiZm9vZF9uYW1lXCI6XCJcXHUwMTEwXFx1MWVhZHUgeGFuaFwiLFwiZm9vZF9leHRydWRlX2ZhY3RvclwiOjIsXCJmb29kX21pbl9pbnZlbnRvcnlcIjowLFwiZm9vZF9tZWFzdXJlX2lkXCI6MSxcImZvb2RfcHJpY2VcIjowLFwibnV0cml0aW9uc1wiOntcInByb3RlaW5cIjoyMy40LFwiZmF0XCI6Mi40LFwic3VnYXJcIjo1My4xLFwiY2Fsb1wiOjMyOCxcImNhbnhpXCI6NjQsXCJiMVwiOjAuNzJ9LFwiZm9vZF9nYW1fZXhjaGFuZ2VcIjoxMDAwLFwiaXNfbWVhdFwiOjAsXCJmb29kX2lzX2RyeVwiOjEsXCJzZWFyY2hfZm9vZF9uYW1lXCI6XCJcXHUwMTEwXFx1MWVhZHUgeGFuaERhdSB4YW5oXCIsXCJuYW1lX2xlbmd0aFwiOjgsXCJmb29kX25hbWVfbGVuZ3RoXCI6MTEsXCJuYW1lX29yZGVyXCI6MSxcImZhdFwiOjIuNCxcInN1Z2FyXCI6NTMuMSxcImNhbG9cIjozMjgsXCJiMVwiOjAuNzIsXCJwcm90ZWluXCI6MjMuNCxcImNhbnhpXCI6NjQsXCJtZWFzdXJlX25hbWVcIjpcIktnXCIsXCJ0YW1cIjpmYWxzZSxcInF1YW50aXRpZXNcIjpbXSxcImVhdF9hX2dyb3VwXCI6MC4wOCxcImJ1eV9hX2dyb3VwXCI6MC4wODIsXCJidXlfYV9kdnRcIjowLjA4MixcInF1YW50aXR5X2Zvcl9tZWFzdXJlXCI6MC4wMDgsXCJjYWxvX2Zvcl9vbmVcIjoyNi4yNCxcInF1YW50aXR5XCI6OCxcIm51bWJlcl9jaGlsZFwiOjEwLFwic3VtX2NhbG9cIjoyMDAuNjQsXCJ0cGNcIjowfSxcIjYzNDNcIjp7XCJmb29kX2lkXCI6NjM0MyxcInVuaXRfaWRcIjoyNTM5LFwibmFtZV9iYXNlXCI6XCJUaGl0IGdhIGNvbmcgbmdoaWVwXCIsXCJuYW1lXCI6XCJUaFxcdTFlY2J0IGdcXHUwMGUwIGNcXHUwMGY0bmcgbmdoaVxcdTFlYzdwXCIsXCJpc192ZWdldFwiOjAsXCJpc19kcnlcIjowLFwiZXh0cnVkZV9mYWN0b3JcIjowLFwic3VwcGxpZXJcIjpcIlwiLFwicHJvZHVjdF9vZlwiOlwiXCIsXCJwYWNrYWdpbmdcIjpcIlwiLFwicHJpY2VcIjo4NTAwMCxcIm1pbl9pbnZlbnRvcnlcIjowLFwibWVhc3VyZV9pZFwiOjEsXCJnYW1fZXhjaGFuZ2VcIjoxMDAwLFwibmd1Y29jXCI6MCxcInJhdVwiOjAsXCJ0cmFpY2F5XCI6MCxcImRhbWR2XCI6MCxcImlzX3N0b3JhZ2VcIjowLFwiaWRcIjo2MzQzLFwiZm9vZF9uYW1lXCI6XCJUaFxcdTFlY2J0IGdcXHUwMGUwIGNcXHUwMGY0bmcgbmdoaVxcdTFlYzdwXCIsXCJmb29kX2V4dHJ1ZGVfZmFjdG9yXCI6NTAsXCJmb29kX21pbl9pbnZlbnRvcnlcIjowLFwiZm9vZF9tZWFzdXJlX2lkXCI6MSxcImZvb2RfcHJpY2VcIjowLFwibnV0cml0aW9uc1wiOntcInByb3RlaW5cIjoyMCxcImZhdFwiOjE1LjIsXCJzdWdhclwiOjAsXCJjYWxvXCI6MjE2LFwiY2FueGlcIjowLFwiYjFcIjowfSxcImZvb2RfZ2FtX2V4Y2hhbmdlXCI6MTAwMCxcImlzX21lYXRcIjoxLFwiZm9vZF9pc19kcnlcIjowLFwic2VhcmNoX2Zvb2RfbmFtZVwiOlwiVGhcXHUxZWNidCBnXFx1MDBlMCBjXFx1MDBmNG5nIG5naGlcXHUxZWM3cFRoaXQgZ2EgY29uZyBuZ2hpZXBcIixcIm5hbWVfbGVuZ3RoXCI6MTksXCJmb29kX25hbWVfbGVuZ3RoXCI6MjUsXCJuYW1lX29yZGVyXCI6OSxcImZhdFwiOjE1LjIsXCJzdWdhclwiOjAsXCJjYWxvXCI6MjE2LFwiYjFcIjowLFwicHJvdGVpblwiOjIwLFwiY2FueGlcIjowLFwibWVhc3VyZV9uYW1lXCI6XCJLZ1wiLFwidGFtXCI6ZmFsc2UsXCJxdWFudGl0aWVzXCI6W10sXCJlYXRfYV9ncm91cFwiOjAuMjUsXCJidXlfYV9ncm91cFwiOjAuMjUsXCJidXlfYV9kdnRcIjowLjI1LFwicXVhbnRpdHlfZm9yX21lYXN1cmVcIjowLjAyNSxcImNhbG9fZm9yX29uZVwiOjU0LFwicXVhbnRpdHlcIjoyNSxcIm51bWJlcl9jaGlsZFwiOjEwLFwic3VtX2NhbG9cIjoyMDAuNjQsXCJ0cGNcIjowfX0iLCJyZWNpcGUiOiIiLCJzdGF0dXMiOjEsInJlYWx0aW1lX3VzZSI6bnVsbCwicHVibGljIjowLCJuYW1lX2Jhc2UiOiJYb2kgZG8geGFuaCwgdGhpdCBnYSIsInVwZGF0ZWRfYXQiOiIyMDI1LTA4LTA2IDE5OjUwOjIwIiwiZ3JvdXBfbmFtZSI6IiIsImFyZWFfbmFtZSI6IiIsImNhdGVnb3J5X25hbWUiOiIiLCJkYXRhIjp7IjU4NiI6eyJmb29kX2lkIjo1ODYsInVuaXRfaWQiOjI1MzksIm5hbWVfYmFzZSI6IkdhbyBuZXAiLCJuYW1lIjoiR1x1MWVhMW8gblx1MWViZnAiLCJpc192ZWdldCI6MCwiaXNfZHJ5IjowLCJleHRydWRlX2ZhY3RvciI6MCwic3VwcGxpZXIiOjEsInByb2R1Y3Rfb2YiOiIiLCJwYWNrYWdpbmciOiIiLCJwcmljZSI6MjYwMDAsIm1pbl9pbnZlbnRvcnkiOjAsIm1lYXN1cmVfaWQiOjEsImdhbV9leGNoYW5nZSI6MTAwMCwibmd1Y29jIjowLCJyYXUiOjAsInRyYWljYXkiOjAsImRhbWR2IjowLCJpc19zdG9yYWdlIjoxLCJpZCI6NTg2LCJmb29kX25hbWUiOiJHXHUxZWExbyBuXHUxZWJmcCIsImZvb2RfZXh0cnVkZV9mYWN0b3IiOjAsImZvb2RfbWluX2ludmVudG9yeSI6MCwiZm9vZF9tZWFzdXJlX2lkIjoxLCJmb29kX3ByaWNlIjoyNTAwMCwibnV0cml0aW9ucyI6eyJwcm90ZWluIjo4LjYsImZhdCI6MS41LCJzdWdhciI6NzQuNSwiY2FsbyI6MzQ0LCJjYW54aSI6MzIsImIxIjowLjE0fSwiZm9vZF9nYW1fZXhjaGFuZ2UiOjEwMDAsImlzX21lYXQiOjAsImZvb2RfaXNfZHJ5IjowLCJzZWFyY2hfZm9vZF9uYW1lIjoiR1x1MWVhMW8gblx1MWViZnBHYW8gbmVwIiwibmFtZV9sZW5ndGgiOjcsImZvb2RfbmFtZV9sZW5ndGgiOjExLCJuYW1lX29yZGVyIjoxLCJmYXQiOjEuNSwic3VnYXIiOjc0LjUsImNhbG8iOjM0NCwiYjEiOjAuMTQsInByb3RlaW4iOjguNiwiY2FueGkiOjMyLCJtZWFzdXJlX25hbWUiOiJLZyIsInRhbSI6ZmFsc2UsInF1YW50aXRpZXMiOltdLCJlYXRfYV9ncm91cCI6MC4zNSwiYnV5X2FfZ3JvdXAiOjAuMzUsImJ1eV9hX2R2dCI6MC4zNSwicXVhbnRpdHlfZm9yX21lYXN1cmUiOjAuMDM1LCJjYWxvX2Zvcl9vbmUiOjEyMC40LCJxdWFudGl0eSI6MzUsIm51bWJlcl9jaGlsZCI6MTAsInN1bV9jYWxvIjoyMDAuNjQsInRwYyI6MCwic2VsZWN0ZWQiOmZhbHNlfSwiNjYzIjp7ImZvb2RfaWQiOjY2MywidW5pdF9pZCI6MjUzOSwibmFtZV9iYXNlIjoiRGF1IHhhbmgiLCJuYW1lIjoiXHUwMTEwXHUxZWFkdSB4YW5oIiwiaXNfdmVnZXQiOjAsImlzX2RyeSI6MCwiZXh0cnVkZV9mYWN0b3IiOjIsInN1cHBsaWVyIjoiIiwicHJvZHVjdF9vZiI6IiIsInBhY2thZ2luZyI6IiIsInByaWNlIjo0MDAwMCwibWluX2ludmVudG9yeSI6MCwibWVhc3VyZV9pZCI6MSwiZ2FtX2V4Y2hhbmdlIjoxMDAwLCJuZ3Vjb2MiOjAsInJhdSI6MCwidHJhaWNheSI6MCwiZGFtZHYiOjAsImlzX3N0b3JhZ2UiOjAsImlkIjo2NjMsImZvb2RfbmFtZSI6Ilx1MDExMFx1MWVhZHUgeGFuaCIsImZvb2RfZXh0cnVkZV9mYWN0b3IiOjIsImZvb2RfbWluX2ludmVudG9yeSI6MCwiZm9vZF9tZWFzdXJlX2lkIjoxLCJmb29kX3ByaWNlIjowLCJudXRyaXRpb25zIjp7InByb3RlaW4iOjIzLjQsImZhdCI6Mi40LCJzdWdhciI6NTMuMSwiY2FsbyI6MzI4LCJjYW54aSI6NjQsImIxIjowLjcyfSwiZm9vZF9nYW1fZXhjaGFuZ2UiOjEwMDAsImlzX21lYXQiOjAsImZvb2RfaXNfZHJ5IjoxLCJzZWFyY2hfZm9vZF9uYW1lIjoiXHUwMTEwXHUxZWFkdSB4YW5oRGF1IHhhbmgiLCJuYW1lX2xlbmd0aCI6OCwiZm9vZF9uYW1lX2xlbmd0aCI6MTEsIm5hbWVfb3JkZXIiOjEsImZhdCI6Mi40LCJzdWdhciI6NTMuMSwiY2FsbyI6MzI4LCJiMSI6MC43MiwicHJvdGVpbiI6MjMuNCwiY2FueGkiOjY0LCJtZWFzdXJlX25hbWUiOiJLZyIsInRhbSI6ZmFsc2UsInF1YW50aXRpZXMiOltdLCJlYXRfYV9ncm91cCI6MC4wOCwiYnV5X2FfZ3JvdXAiOjAuMDgyLCJidXlfYV9kdnQiOjAuMDgyLCJxdWFudGl0eV9mb3JfbWVhc3VyZSI6MC4wMDgsImNhbG9fZm9yX29uZSI6MjYuMjQsInF1YW50aXR5Ijo4LCJudW1iZXJfY2hpbGQiOjEwLCJzdW1fY2FsbyI6MjAwLjY0LCJ0cGMiOjB9LCI2MzQzIjp7ImZvb2RfaWQiOjYzNDMsInVuaXRfaWQiOjI1MzksIm5hbWVfYmFzZSI6IlRoaXQgZ2EgY29uZyBuZ2hpZXAiLCJuYW1lIjoiVGhcdTFlY2J0IGdcdTAwZTAgY1x1MDBmNG5nIG5naGlcdTFlYzdwIiwiaXNfdmVnZXQiOjAsImlzX2RyeSI6MCwiZXh0cnVkZV9mYWN0b3IiOjAsInN1cHBsaWVyIjoiIiwicHJvZHVjdF9vZiI6IiIsInBhY2thZ2luZyI6IiIsInByaWNlIjo4NTAwMCwibWluX2ludmVudG9yeSI6MCwibWVhc3VyZV9pZCI6MSwiZ2FtX2V4Y2hhbmdlIjoxMDAwLCJuZ3Vjb2MiOjAsInJhdSI6MCwidHJhaWNheSI6MCwiZGFtZHYiOjAsImlzX3N0b3JhZ2UiOjAsImlkIjo2MzQzLCJmb29kX25hbWUiOiJUaFx1MWVjYnQgZ1x1MDBlMCBjXHUwMGY0bmcgbmdoaVx1MWVjN3AiLCJmb29kX2V4dHJ1ZGVfZmFjdG9yIjo1MCwiZm9vZF9taW5faW52ZW50b3J5IjowLCJmb29kX21lYXN1cmVfaWQiOjEsImZvb2RfcHJpY2UiOjAsIm51dHJpdGlvbnMiOnsicHJvdGVpbiI6MjAsImZhdCI6MTUuMiwic3VnYXIiOjAsImNhbG8iOjIxNiwiY2FueGkiOjAsImIxIjowfSwiZm9vZF9nYW1fZXhjaGFuZ2UiOjEwMDAsImlzX21lYXQiOjEsImZvb2RfaXNfZHJ5IjowLCJzZWFyY2hfZm9vZF9uYW1lIjoiVGhcdTFlY2J0IGdcdTAwZTAgY1x1MDBmNG5nIG5naGlcdTFlYzdwVGhpdCBnYSBjb25nIG5naGllcCIsIm5hbWVfbGVuZ3RoIjoxOSwiZm9vZF9uYW1lX2xlbmd0aCI6MjUsIm5hbWVfb3JkZXIiOjksImZhdCI6MTUuMiwic3VnYXIiOjAsImNhbG8iOjIxNiwiYjEiOjAsInByb3RlaW4iOjIwLCJjYW54aSI6MCwibWVhc3VyZV9uYW1lIjoiS2ciLCJ0YW0iOmZhbHNlLCJxdWFudGl0aWVzIjpbXSwiZWF0X2FfZ3JvdXAiOjAuMjUsImJ1eV9hX2dyb3VwIjowLjI1LCJidXlfYV9kdnQiOjAuMjUsInF1YW50aXR5X2Zvcl9tZWFzdXJlIjowLjAyNSwiY2Fsb19mb3Jfb25lIjo1NCwicXVhbnRpdHkiOjI1LCJudW1iZXJfY2hpbGQiOjEwLCJzdW1fY2FsbyI6MjAwLjY0LCJ0cGMiOjB9fSwic3VtX2NhbG8iOjIwMC42NH1dfQ=="}}