(function($, doc){
    $.user = {
        module: 'user',
        init: function() {
            var self = this;
            var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
            $.dm_datagrid.init(
                urls.join('/'),
                this.module, /*<PERSON><PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
                '', /*Tiêu đề cho bảng dữ liệu*/
                [[
                    { title:'Họ & Tên', field:'fullname', width:150, sortable:true },
                    { title:'Tà<PERSON> khoản', field:'username', width:150, sortable:true },
                    { title:'Điểm trường', field:'school_point', width:100, sortable:true,align:'center', hidden: $CFG.school_points <= 1},
                    { title:'Nhóm quyền', field:'user_permission_group', width:120, sortable:true },
                    { title:'<PERSON>ớ<PERSON>', field:'course_name', width:120, sortable:true },
					{ title:'<PERSON><PERSON><PERSON><PERSON>/Độ tuổi', field:'bloc_age_name', width:120, sortable:true },
                    { title:'<PERSON><PERSON><PERSON><PERSON> thoại', field:'phone', width:120, sortable:false },
                    { title:'Email', field:'email', width:150, sortable:false },
                    { title:'Trạng thái', field:'status', sortable: true, align:'center',
						formatter:function(value,row,index){
							if(value==0) {
								return '<span class="fa fa-lock fs12"></span>';
							}
						}
					},
                    { field:'action',title:'Action',width:100,align:'center',
                        formatter:function(value,row,index){
                            var EDIT_LINK = '<a href="javascript:void(0)" class="edit-user fa fa-pencil fs11 mR5" data-id="'+ row.id +'" title="Sửa thông tin người dùng"></a>';
                            var CHANGE_PASSWORD_LINK = '<a href="javascript:void(0)" class="change-password-user fa fa-key fs11 mL5 mR5" data-id="'+ row.id +'" title="Đổi mật khẩu"></a>';
							var DELETE_USER_LINK = '<a href="javascript:void(0)" class="delete-user fa fa-trash-o fs11 clrRed mL5" data-id="'+ row.id +'" title="Xóa người dùng"></a>';
                            return EDIT_LINK + "   |   " + CHANGE_PASSWORD_LINK + "   |   " + DELETE_USER_LINK;
                        }
                    }
                ]],
                {
                    width: 500, height: 550
                }
            );
        }, showAddForm: function() {
            var self = this;
            $.dm_datagrid.showAddForm(
                {
                    module: $CFG.project+'/'+self.module,
                    action:'add',
                    title:'Thêm tài khoản',
                    size: size.wide,
                    content: function(element){
                        loadForm($CFG.project+'/'+self.module,'add', {}, function(resp){
                            $(element).html(resp);
                        })
                    }
                },
                function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                }
            );
            // $.dm_datagrid.show_add_form(this.module, 'Thêm mới loại công văn', 500, 140 );
        }, showEditForm: function(e) {
            var self = this;
            var row = $("#tbl_"+self.module).datagrid('getSelected');
            if(row != null) {
                $.dm_datagrid.showEditForm(
                    {
                        module: $CFG.project+'/'+self.module,
                        action:'edit',
                        title:'Sửa tài khoản',
                        size: size.wide,
                        content: function(element){
                            loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
                                console.log(resp);
                                $(element).html(resp);
                            })
                        }
                    },
                    function(resp){
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                );
            }else{
                // $.messager.alert('Thông báo.', 'Phải chọn một dòng!');
                BootstrapDialog.show({
                    title: 'Thông báo',
                    message: '<label class="checkbox-inline" style="color: red">Phải chọn một dòng! </label>',
                    buttons: [{
                        label: 'Đồng ý',
                        icon: 'glyphicon glyphicon-log-out',
                        action: function(dialog){
                            dialog.close();
                        }
                    }]
                });
            }

        }, showAssClassForm: function() {
            var self = this;
            var row = $("#tbl_"+self.module).datagrid('getSelected');
            if(row != null) {
                $.dm_datagrid.showEditForm(
                    {
                        module: $CFG.project+'/'+self.module,
                        action:'assclass',
                        title:'Phân lớp',
                        size: size.wide,
                        content: function(element){
                            loadForm($CFG.project+'/'+self.module,'assclass', {id: row.id}, function(resp){
                                $(element).html(resp);
                            })
                        }
                    },
                    function(resp){
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                );
            }else{
                // $.messager.alert('Thông báo.', 'Phải chọn một dòng!');
                BootstrapDialog.show({
                    title: 'Thông báo',
                    message: '<label class="checkbox-inline" style="color: red">Phải chọn một dòng! </label>',
                    buttons: [{
                        label: 'Đồng ý',
                        icon: 'glyphicon glyphicon-log-out',
                        action: function(dialog){
                            dialog.close();
                        }
                    }]
                });
            }

        }, del: function(){ // XÓA
            var self = this;
            var ids = [];
            $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
                ids.push(row.id);
            })
            if(!ids.length >0){
                // $.messager.alert('Thông báo','Hãy chọn dữ liệu cần xóa.');
                BootstrapDialog.show({
                    title: 'Thông báo',
                    message: '<label class="checkbox-inline" style="color: red">Hãy chọn dữ liệu cần xóa. </label>',
                    buttons: [{
                        label: 'Đồng ý',
                        icon: 'glyphicon glyphicon-log-out',
                        action: function(dialog){
                            dialog.close();
                        }
                    }]
                });
                return '';
            }
            var urls = ['admin','user','del'];
            process(urls.join('/'),{ids: ids.join(',')}, function(resp){
                console.log(resp);
                $("#tbl_"+self.module).datagrid('reload');
            } )
        }
    }

    $(doc).on('click', '.edit-user', function(){
        var id = $(this).data('id');
        $.dm_datagrid.showEditForm(
            {
                module: $CFG.project+'/'+ $.user.module,
                action:'edit',
                title:'Sửa tài khoản',
                size: size.wide,
                content: function(element){
                    loadForm($CFG.project+'/'+ $.user.module,'edit', {id}, function(resp){
                        $(element).html(resp);
                    })
                }
            },
            function(resp){
                $("#tbl_"+ $.user.module).datagrid('reload');
            }
        );
    });

    $(doc).on('click', '.change-password-user', function(){
        var id = $(this).data('id');
        $.dm_datagrid.showEditForm(
            {
                module: $CFG.project+'/'+ $.user.module,
                action:'changePasswordUser',
                title:'Cập nhật tài khoản & mật khẩu',
                size: size.wide,
                content: function(element){
                    loadForm($CFG.project+'/'+ $.user.module, 'changePasswordUser', { id }, function(html){
                        $(element).html(html);
                    })
                }
            },
            function(resp){
                $("#tbl_"+ $.user.module).datagrid('reload');
            }
        );
    });
	$(doc).on('click', '.delete-user', function(){
        var ids = $(this).data('id');
        var urls = ['admin','user','del'];
		var captchaForm = $CFG.dialog_captcha('delete_user');
        var msg = '<div style = "font-size: 14px; color:red;">Hãy chắc chắn tài khoản này không có thành viên nào đang sử dụng trước khi xóa?</div>' + captchaForm;
        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="delete_user_captcha"]').val();
                process(urls.join('/'),{ids: ids, captcha:captcha},function(resp){
                    if (resp.result == 'success') {
						$.user.del();
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    });
})($, document);