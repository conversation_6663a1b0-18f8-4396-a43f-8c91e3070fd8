{"groups": {"101267": {"group_id": 101267, "name": "Trẻ 36 - 72 tháng tu<PERSON>i", "group_children": 1, "price": 0, "block": false}, "101268": {"group_id": 101268, "name": "Trẻ 24 - 36 tháng tu<PERSON>i", "group_children": 0, "price": 0, "block": false}, "101269": {"group_id": 101269, "name": "Trẻ 18 - 24 tháng tu<PERSON>i", "group_children": 0, "price": 0, "block": false}, "101271": {"group_id": 101271, "name": "Trẻ 12 - 18 tháng tu<PERSON>i", "group_children": 0, "price": 0, "block": false}, "101273": {"group_id": 101273, "name": "Trẻ 6 - 12 tháng tu<PERSON>i", "group_children": 0, "price": 0, "block": false}}, "html": "<form class=\"form-horizontal\" id=\"frm-login\" role=\"form\" >\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm valid\"><PERSON><PERSON><PERSON> dịch vụ</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"name\" width=\"100%\" ng-model=\"service.name\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\" ng-if=\"!service.priceDefaultCheck()\">\n\t\t<label class=\"col-md-4 control-label control-label-norm valid\">G<PERSON><PERSON> tiền</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<!-- <input class=\"form-control\" type=\"number\" id=\"price\" width=\"100%\" > -->\n\t\t\t<span class=\"container-field-measure\">\n                <input class=\"form-control color-green\" style=\"width: 150px;position: absolute; left: 0; top: 0; z-index: 1;\" ng-value=\"digit_grouping(row.price)\" placeholder=\"Gi<PERSON> d<PERSON>ch vụ\">\n                <input class=\"form-control color-green input-unfocus-hidden\" style=\"width: 150px;z-index: 2;\" id=\"price\" placeholder=\"Giá dịch vụ\" type-number=\"int\" ng-model=\"row.price\" ng-change=\"service.priceDefaultChange(row.price)\">\n                <label class=\"measure vnd-service color-green\" style=\"z-index: 3;\">VNĐ</label>\n            </span>\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\" ng-repeat=\"(index, group) in service.groups\">\n\t\t<label class=\"col-md-4 control-label control-label-norm valid\" ng-bind=\"group.name\">Tên nhóm trẻ</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<!-- <input class=\"form-control\" type=\"number\" id=\"price\" width=\"100%\" > -->\n\t\t\t<span class=\"container-field-measure\">\n                <input class=\"form-control color-green\" style=\"width: 150px;position: absolute; left: 0; top: 0; z-index: 1;\" ng-value=\"digit_grouping(group.price)\" placeholder=\"Giá dịch vụ\">\n                <input class=\"form-control color-green input-unfocus-hidden\" style=\"width: 150px;z-index: 2;\" id=\"price\" placeholder=\"Giá dịch vụ\" type-number=\"int\" ng-model=\"group.price\" ng-change=\"group.block = true\">\n                <label class=\"measure vnd-service color-green\" style=\"z-index: 3;\">VNĐ</label>\n            </span>\n             <!-- <i style=\"margin-left: 3px;\" class=\"fa fa-lock\" aria-hidden=\"true\" ng-if=\"group.block\"></i> -->\n             <i style=\"margin-left: 3px;\" class=\"fa fa-unlock-alt\" aria-hidden=\"true\" ng-if=\"!group.block\" ng-click=\"group.block=true\"></i>\n\t\t</div>\n\t</div>\n\t\n</form>\n<style type=\"text/css\">\n\t.container-field-measure input.form-control {\n\t    padding: 3px;\n\t    font-size: 14px;\n\t    width: 100%;\n\t    padding-top: 4px;\n\t    height: 30px;\n\t}\n\t.container-field-measure {\n\t    position: relative;\n\t}\n\t.vnd-service{\n\t\tpadding-top: 7px !important;\n    \tfont-size: 12px;\n\t}\n\n</style>\n"}