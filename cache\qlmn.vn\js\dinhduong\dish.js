$.dish = {
    module: 'dish',
    nutritions: null,
    dishtypes: null,
    init:function() {
        process('dinhduong/dish/getCat','',function(resp){
            // var rows = JSON.parse(resp);
            $.each(resp,function(index,value){
                $.dish[index] = value;
            });
            $.dish.arrGroups = [];
            $.each($.dish.groups, function (index, value) {
                $.dish.arrGroups.push({id: value.id, name: value.name})
            });
            $.dm_datagrid.combobox('group_ids', $.dish.arrGroups,{
                valueField: 'id',
                textField: 'name',
                value:[],
                panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_group = row.id;
                    });
                    $.dish.doSearch();
                },
                queryParams: {},
                width: 120,
                height : 25
            });
            $.dm_datagrid.combobox('area_ids',$.dish.areas, {
                valueField: 'id',
                textField: 'name',
                value:[],
                panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_area = row.id;
                    });
                    $.dish.doSearch();
                },
                queryParams: {},
                width: 100,
                height : 25
            });
            $.dm_datagrid.combobox('category_ids',$.dish.categories,{
                valueField: 'id',
                textField: 'name',
                value:[],
                panelHeight: 'auto',
                onSelect: function(row, element) {
                    angular.element($("#header-search")).scope().$apply(function(scope){
                        scope.keysearch_category = row.id;
                    });
                    $.dish.doSearch();
                },
                queryParams: {},
                width: 100,
                height : 25
            });
            
            $.dish.list();
        },null,false)
    }, comboboxReload: function(scope) {
        var self = this;
        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/'+self.module+'/foods';
        var ids = [];
        $.each(scope.dish.foods,function(food_id,food){
            ids.push(food_id); 
        });
        $("#div-ingredient input#ingredient-new").combobox({url: url,queryParams: {ids:ids}}).next().find('input.textbox-text').focus();

    }, buildCombobox : function(scope) {
        return;
        var self = this;
        var input = $(`<input class="form-control" id="ingredient-new" placeholder="Chọn nguyên liệu" width="100%" >`);
        var content_input = $("#div-ingredient");
        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/'+self.module+'/';
        content_input.html('').append(input);
        var ids = [];
        $.each(scope.dish.foods,function(food_id,food){
            ids.push(food_id); 
        });
        var option = {
            valueField:'food_id',
            textField:'name',
            // panelHeight:'auto',
            mode: 'remote',
            onSelect: function(row, element, test) {
            },onLoadSuccess: function(data, element){
                $(element).combobox('panel').children().click(function(e){
                    var name = $(this).html();
                    for(var i in data){
                        if(data[i].name == name){
                            var row = data[i];
                            break;
                        }
                    }
                    if(row){
                        $('.combo-panel > .combobox-item.combobox-item-selected').removeClass('combobox-item-selected');
                        scope.$apply(function(scope){
                            scope.dish.foods[row.food_id] || (scope.dish.foods[row.food_id] = row);
                            $.dish.comboboxReload(scope);
                        });
                    }
                    return false;
                })
                $(element).next().children('input').keydown(function(e){
                    if(e.which == 13){
                        if($('.combo-panel > .combobox-item.combobox-item-selected').length>0){
                            var name = $('.combo-panel > .combobox-item.combobox-item-selected').html();
                            for(var i in data){
                                if(data[i].name == name){
                                    var row = data[i];
                                    break;
                                }
                            }
                            if(row){
                                $('.combo-panel > .combobox-item.combobox-item-selected').removeClass('combobox-item-selected');
                                scope.$apply(function(scope){
                                    scope.dish.foods[row.food_id] || (scope.dish.foods[row.food_id] = row);
                                    $.dish.comboboxReload(scope);
                                });
                            }
                        }
                        return false;
                    }
                })
            },
            queryParams: {ids:ids},
            width: 170,
            height: 26
        };
        $.dm_datagrid.combobox(input,url+'foods',option);
    }, list: function() {
    	var self = this;
    	var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list?status='+$("#status").val()];
        $.dish.initAngular();
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                { field:'ck', checkbox: true },
                { title:'Nhân bản', field:'nb', align: 'center', width:20, formatter: function(value, row){
                        value = `<button type="button" class="btn-link btn-outline" onclick="$.dish.showNBForm(`+row.id+`)">
                                    <span class="glyphicon glyphicon-copy"></span>
                                </button>`;
                        return value;
                } },
                { title:'Mã món ăn', field:'id', width:30, sortable:true },
                { title:'Tên món ăn', field:'name', width:70, sortable:true },
                { title:'Calo / 1 trẻ', field:'sum_calo', width:30, sortable:true },
                { title:'Nhóm tuổi', field:'group_name', width:80, sortable:true },
                { title:'Loại', field:'category_name', width:30, sortable:true },
                { title:'Vùng miền', field:'area_name',width:50, sortable:true },
                { title:'Chia sẻ', field:'public', align: 'center', width:30, sortable:true, formatter: function (value, row) {
                        if (value + '' === '1') {
                            value = `<a style="font-size:14px;" class="btn over-orange" onclick="$.dish.applySharing(` + row.id + `,0)" title="Bỏ chia sẻ"><i class="fa fa-check-circle-o"></i></a>`;
                        } else {
                            value = `<a style="font-size:14px;" class="btn over-orange" onclick="$.dish.applySharing(` + row.id + `,1)" title="Chia sẻ"><i class="fa fa-circle-o over-orange"></i></a>`;
                        }
                        return value;
                    } },
                { title:'Ngày cập nhật', field:'updated_at', align: 'center', sortable:true, formatter: function(value, row){
                    return moment(value).format('DD/MM/YYYY');
                }}
                
            ]],
            {
                view: detailview,
                detailFormatter:function(index,row){
                    
                    var ingredients = JSON.parse(row.ingredient);
                    // console.log(ingredients);
                    row.description = row.description.replace(/&amp;lt;br&amp;gt;/g, '<br>')
                    row.recipe = row.recipe.replace(/&amp;lt;br&amp;gt;/g, '<br>')

                    var html = `<div class="detail-dish-storage">
                                <div class="div-vari">
                                    <div class="name-dds"><label>Tên món ăn : </label><p>`+row.name+`</p></div>
                                    <div class="name-dds"><label>Mô tả : </label><p>`+row.description+`</p></div>
                                    <div class="name-dds"><label>Cách chế biến : </label><p>`+row.recipe+`</p></div>
                                </div>
                                `;
                    html +=`    <div class="div-vari-table">
                                <table class="table-vari">
                                    <tr class="tr-title-vari">
                                        <th class="width-60">TÊN THỰC PHẨM</th>
                                        <th class="width-20">LƯỢNG 1 TRẺ </br> (g)</th>
                                        <th class="width-20">LƯỢNG 1 TRẺ THEO </br> DVT</th>
                                    </tr>
                                    `;
                    $.each(ingredients, function( index, value ) {
                        html +=`<tr class="tr-content-vari">
                                    <th class="width-60">`+value.name+`</th>
                                    <th class="width-20">`+value.quantity+`</th>
                                    <th class="width-20">`+value.quantity_for_measure+`</th>
                                </tr>`;
                    });
                    html +=`</table>`;
                    html +=`</div>`;
                    html +=`</div>`;
                    return html;
                },onExpandRow: function(index,row){

                    
                },onDblClickRow: function(rowIndex, rowData) {
                    self.showEditForm();
                }
			}
        );
    }, buildDetailView: function(index, row, nutritions_data, nutritions_define) {
        var ddv = $('#tbl_dish').datagrid('getRowDetail',index).find('div.detail-view-content');
        if($(ddv).attr('opened')) {
            return;
        }
        $(ddv).attr('opened','true');
        var html = [];
        $.each(nutritions_define, function(i,nutr){});
        ddv.append(html.join(''));
        $('#tbl_dish').datagrid('fixDetailRowHeight',index);
    }, cleanSearch: function() {
        var self = this;
        angular.element($("#header-search")).scope().$apply(function(scope){
            scope.keysearch_name = '';
            $('input#name').val('');
            scope.keysearch_group = '';
            $('input#group_ids').combobox('clear');
            scope.keysearch_area = '';
            $('input#area_ids').combobox('clear');
            scope.keysearch_category = '';
            $('input#category_ids').combobox('clear');
            var queryParams = $("#tbl_"+self.module).datagrid('options').queryParams;
            if(queryParams.filterRules) {
                delete queryParams.filterRules;
                delete queryParams.filter_type;
                $("#tbl_"+self.module).datagrid('load',queryParams);
            }
        });
    }, initAngular: function() {
        var self = this;
        setTimeout(function(){
            angular.element($('#mainContentController')).scope().$apply(function(scope){
                scope.dish = {}
                scope.dish.categories = $.dish.categories;
                scope.dish.groups = $.dish.groups;
                scope.dish.areas = $.dish.areas;
                scope.dish.foods = {};

                scope.dish.keysearch_name_time = 0;
                scope.dish.onChangeKeysearchName = function(keysearch_name){
                    if(scope.dish.keysearch_name_time == 0){
                        scope.dish.keysearch_name_time = 1;
                        setTimeout(function(){
                            if(scope.dish.keysearch_name_time == 1){
                                $.dish.doSearch();
                                scope.dish.keysearch_name_time = 0;
                            }
                        }, 700);
                    }
                };
                scope.dish.onChangeNC = function(){
                    angular.forEach(scope.dish.foods,function(food,index){
                        scope.dish.onChangeOneG(food);
                    });
                    if (scope.dish.group_radio) {
                        scope.dish.number_childs || (scope.dish.number_childs = {});
                        scope.dish.number_childs[scope.dish.group_radio] = scope.dish.number_child;
                    }else{
                        for (var i in scope.dish.number_childs) {
                            scope.dish.number_childs[i] = scope.dish.number_child;
                        }
                    }
                };
                scope.reloadDatagrid = function(){
                    $.dish.list();
                };
                scope.keyUpFood = function(e,food_id,name,group_id){
                    var input = '';
                    newfoods = [];
                    index = 0;
                    angular.forEach(scope.dish.foods,function(food,f_id){
                        newfoods[index] = food;
                        index ++;
                    })
                    angular.forEach(newfoods,function(newfood,index){
                        if(newfood.food_id == food_id){
                            if(e.which == 13 || e.which == 40){
                                if(index < count(newfoods)-1){
                                    index = index+1;
                                    input = [name,newfoods[index].food_id].join('_');
                                }
                            }else if(e.which == 38){
                                if(index>0){
                                    index = index-1;
                                    input = [name,newfoods[index].food_id].join('_');
                                }
                            }
                        }
                    })
                    if(input != ''){
                        $('input#'+input).focus();
                    }
                };
                scope.dish.quantityFocus = function(food,group_id){
                    // console.log(scope.dish.foods);
                    food.tam = false;
                    food.quantities || (food.quantities = {})
                    angular.forEach(scope.dish.groups,function(group,index){
                        if(group.selected == true){
                            if(food.quantities[group.id]){
                                food.tam = true;
                            }else{
                                food.quantities[group.id] = 0;
                            }
                        }
                    });
                    scope.dish.onChangeOneGArr(food,group_id,scope.dish.foods);
                };
                scope.dish.checkGroupSelected = function(){
                    var tam = 0;
                    angular.forEach(scope.dish.groups,function(group,index){
                        if(group.selected == true){
                            tam ++;
                        }
                    });
                    return tam;
                };
                scope.dish.onSelectFood = function(food) {
                    if(!scope.dish.foods[food.id]) {
                        var url = $CFG.remote.base_url+'/doing/dinhduong/dish/getFoodDetailById';
                        var data = {async: true,id: food.id};
                        process(url, data, function(resp){
                            if(!resp) return;
                            scope.$apply(function(){
                                scope.dish.selected.food_ids = [];
                                angular.forEach(scope.dish.foods, function(item, food_id){
                                    scope.dish.selected.food_ids.push(food_id);
                                });
                                angular.forEach(resp, function(item, ind){
                                    scope.dish.selected.food_ids.push(item.food_id);
                                    scope.dish.foods[item.food_id] = item;
                                })
                            });
                        },function(){}, false);
                    }else{
                        scope.dish.selected.food_ids = [];
                        angular.forEach(scope.dish.foods, function(item, food_id){
                            scope.dish.selected.food_ids.push(food_id);
                        });
                        angular.forEach(resp, function(item, ind){
                            scope.dish.selected.food_ids.push(item.food_id);
                            scope.dish.foods[item.food_id] = item;
                        })
                    }
                };
                scope.dish.initFormAdd = function(){
                    scope.dish.selected || (scope.dish.selected = {});
                    scope.dish.selected.food_ids = [];
                    scope.dish.selected.food = undefined;
                };
                scope.dish.onChangeOneG = function(item,group_id){

                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        if(item.tam == false){
                            angular.forEach(scope.dish.groups,function(group,index){
                                if(group.selected == true){
                                    item.quantities[group.id] = item.quantity;
                                }
                            })
                        }
                        item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                        item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = scope.sumCaloFood(item);

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                scope.sumCaloFood = function(food,key) {
                    key || (key = 'quantity');
                    var rs = $['*'](scope.co_cau(null).protein,food[key])*(food.nutritions.protein/100)
                        + $['*'](scope.co_cau(null).fat,food[key])*(food.nutritions.fat/100)
                        + $['*'](scope.co_cau(null).sugar,food[key])*(food.nutritions.sugar/100);
                    return round(rs,2);
                };
                scope.dish.onChangeOneGArr = function(item,group_id,foods){
                    // angular.forEach(scope.dish.groups,function(group,index){
                        // if(scope.dish.group_radio[group.id]){
                            // console.log(scope.dish.group_radio);
                            scope.dish.group_radio = group_id;
                            if(!item.tam){
                                angular.forEach(item.quantities, function(value, grp_id){
                                    if(group_id != grp_id){
                                        item.quantities[grp_id] = item.quantities[scope.dish.group_radio];
                                    }
                                })
                            }
                            if(scope.dish.group_radio == undefined){
                                item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                                // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                                item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                                item.calo_for_one = scope.sumCaloFood(item);
                            }else if(foods){
                                angular.forEach(foods, function(food, food_id){
                                    food.quantity = food.quantities[scope.dish.group_radio];
                                    food.eat_a_group = scope.rounds(food.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                    food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                                    food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                                    food.quantity_for_measure = scope.rounds(food.quantities[scope.dish.group_radio]/food.gam_exchange);
                                    food.calo_for_one = scope.sumCaloFood(food);
                                })
                            }else{
                                item.quantity = item.quantities[scope.dish.group_radio];
                                item.eat_a_group = scope.rounds(item.quantities[scope.dish.group_radio]*scope.dish.number_child/1000);
                                item.buy_a_group = scope.rounds(parseFloat((item.eat_a_group*100)/(100-item.extrude_factor)));
                                item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                                item.quantity_for_measure = scope.rounds(item.quantities[scope.dish.group_radio]/item.gam_exchange);
                                item.calo_for_one = scope.sumCaloFood(item);
                            }
                };
                scope.dish.onChangeRadio = function(group_id){
                    angular.forEach(scope.dish.foods, function(food, food_id){
                        food.quantity = food.quantities[group_id];
                        food.eat_a_group = scope.rounds(food.quantities[group_id]*scope.dish.number_child/1000);
                        food.buy_a_group = scope.rounds(parseFloat((food.eat_a_group*100)/(100-food.extrude_factor)));
                        food.buy_a_dvt = scope.rounds((food.buy_a_group*1000)/food.gam_exchange);
                        food.quantity_for_measure = scope.rounds(food.quantities[group_id]/food.gam_exchange);
                        food.calo_for_one = scope.sumCaloFood(food);
                    });
                    scope.dish.number_childs || (scope.dish.number_childs = {});
                    if (scope.dish.number_childs[group_id]) {
                        scope.dish.number_child = scope.dish.number_childs[group_id];
                    } else {
                        scope.dish.number_child = 100;
                        scope.dish.number_childs[group_id] = 100;
                    }
                };
                scope.dish.onChangeEAG = function(item){
                   
                    var group_id = scope.dish.group_radio;
                    var check_group = scope.dish.checkGroupSelected();
                     
                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        if(check_group != 0 && group_id != undefined){
                            if(item.quantities == undefined){
                                item.quantities = [];
                            }
                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                            item.quantity = item.quantities[group_id]
                            item.calo_for_one = scope.sumCaloFood(item);
                        }else{
                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                            item.calo_for_one = scope.sumCaloFood(item);
                        }
                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                 
                scope.dish.onChangeBAG = function(item){
                    var group_id = scope.dish.group_radio;
                    var check_group = scope.dish.checkGroupSelected();
                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        if(check_group !=0 && group_id != undefined){
                            if(item.quantities == undefined){
                                item.quantities = [];
                            }
                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                            item.quantity = item.quantities[group_id];
                            item.calo_for_one = scope.sumCaloFood(item);
                        }else{
                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) * 100;
                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                            item.calo_for_one = scope.sumCaloFood(item);
                        }

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                scope.dish.onChangeBAD = function(item){
                    var group_id = scope.dish.group_radio;
                    var check_group = scope.dish.checkGroupSelected();
                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        if(check_group !=0 && group_id != undefined){
                            if(item.quantities == undefined){
                                item.quantities = [];
                            }
                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                            item.quantities[group_id] = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            item.quantity_for_measure = scope.rounds(item.quantities[group_id]/item.gam_exchange);
                            item.quantity = item.quantities[group_id]
                            item.calo_for_one = scope.sumCaloFood(item);
                        }else{
                            // item.extrude_factor_ch = parseFloat(item.extrude_factor) + 100;
                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                            item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                            item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                            item.calo_for_one = scope.sumCaloFood(item);
                        }

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                scope.dish.onChangeOneDVT = function(item){
                    var group_id = scope.dish.group_radio;
                    var check_group = scope.dish.checkGroupSelected();
                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        if(check_group !=0 && group_id != undefined){
                            if(item.quantities == undefined){
                                item.quantities = [];
                            }
                            item.quantities[group_id] = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                            item.buy_a_group = scope.rounds(item.buy_a_dvt*item.gam_exchange/1000);
                            item.eat_a_group = scope.rounds(item.buy_a_group-(item.buy_a_group*item.extrude_factor)/100);
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.quantity = item.quantities[group_id];
                            item.calo_for_one = scope.sumCaloFood(item);
                        }else{
                            item.quantity = scope.rounds(item.quantity_for_measure*item.gam_exchange);
                            item.eat_a_group = scope.rounds(item.quantity*scope.dish.number_child/1000);
                            item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                            item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                            item.calo_for_one = scope.sumCaloFood(item);
                        }

                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                scope.dish.onChangeEF = function(item){
                    if(scope.dish.number_child != undefined||scope.dish.number_child != ''){
                        item.quantity = scope.rounds(item.eat_a_group*1000/scope.dish.number_child);
                        // item.extrude_factor_ch = scope.rounds(item.eat_a_group*item.extrude_factor/100);
                        item.buy_a_group = parseFloat((item.eat_a_group*100)/(100-item.extrude_factor));
                        item.buy_a_dvt = scope.rounds((item.buy_a_group*1000)/item.gam_exchange);
                        item.quantity_for_measure = scope.rounds(item.quantity/item.gam_exchange);
                        item.calo_for_one = scope.sumCaloFood(item);
                    }else{
                        alert("Vui lòng nhập số lượng trẻ");
                    }
                };
                scope.dish.sum_calo = function(){
                    var group_id = scope.dish.group_radio;
                    var check_group = scope.dish.checkGroupSelected();
                    // console.log(group_id,check_group);
                    var sum_calo = 0;
                    angular.forEach(scope.dish.foods,function(food,idex){
                        if(food.nutritions.calo == undefined){
                            food.nutritions.calo = 0;
                        }
                        food.quantities || (food.quantities={});
                        food.quantity || (food.quantity=0);
                        food.quantities[group_id] || (food.quantities[group_id] = 0);
                        sum_calo += scope.sumCaloFood(food);
                    });
                    return round(sum_calo,2);
                };
                scope.dish.delFood = function(id){
                    var tam = {};
                    var food_ids = [];
                    scope.dish.selected.food_ids =[];
                    $.each(scope.dish.foods,function(food_id,food){
                        if(food_id != id){
                            tam[food_id] = food; 
                            food_ids.push(food_id);
                        }
                    });
                    scope.dish.selected.food_ids = food_ids;
                    scope.dish.foods = tam;
                };
                scope.dish.delAllFood = function(){
                    scope.dish.foods = {};
                    $.dish.buildCombobox(scope);
                };
                scope.dish.clickGroup = function(groups){
                    // scope.dish.new_groups = [];
                    // angular.forEach(groups,function(group,index){
                    //     if(group.selected == true){
                    //         scope.dish.new_groups[index] = [];
                    //         scope.dish.new_groups[index]['id'] = group.id;
                    //         scope.dish.new_groups[index]['name'] = group.name;
                    //     }
                    // });
                };
                scope.rounds = function(value) {
                    if(typeof value != 'number') {
                        value = parseFloat(value);
                    }
                    value = Math.round(value*1000)/1000;
                    return value;
                };
                scope.dish.uploadDone = function(image){
                    if(image){
                        $('.image-last').show();
                        $('.image-first').hide();
                        if(count(image.errors)>0){
                            var html = [];
                            angular.forEach(image.errors, function(value,index){
                                html.push(value);
                            });
                            $.dm_datagrid.show({title: 'Thông báo', message: html.join('<br/>')});
                        }
                    }
                };
            });
        },0);
    }, showAddForm: function(callback) { 
    	var self = this;
        $.dm_datagrid.showAddForm(
			{
                module: $CFG.project + '/' + self.module,
                action: 'add',
                title: 'Thêm mới',
                showButton: false,
                fullScreen: true,
                size: size.wide,
                content: function (element) {
                    loadForm($CFG.project + '/' + self.module, 'add', {id: self.id}, function (resp) {
                        $.dish.angular(element, resp, function (scope) {
                            scope.dish.foods = {};
                            scope.dish.row = {};
                            scope.cache || (scope.cache = {});
                            scope.cache.foods = undefined;
                            scope.dish.selected.food_ids = [];
                            scope.dish.number_child = 100;
                            scope.dish.number_childs = {};
                            $.each(scope.dish.groups, function (index, value) {
                                scope.dish.number_childs[value.id] = 100;
                            });
                            scope.dish.group_radio = scope.dish.groups[Object.keys(scope.dish.groups)[0]].id;

                            angular.forEach(scope.dish.areas, function(area, index) {
                                area.selected = false;
                            });
                            angular.forEach(scope.dish.groups,function(group,index){
                                group.selected = false;
                            });
                            angular.forEach(scope.dish.categories,function(category,index){
                                category.selected = false;
                            });
                        });
                    })
                },
                buttons: [{
                    id: 'btn-save',
                    icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn-primary',
                    action: function (dialogRef) {

                        let area = $.dish.areas.filter(item => item.selected == true).map(item => item.id).toString()
                        let category = $.dish.categories.filter(item => item.selected == true).map(item => item.id).toString()
                        let group = Object.values($.dish.groups).filter(item => item.selected == true).map(item => item.id).toString()

                        let data = $.dish.getDataForm(dialogRef.getModalBody())
                        let formData = getSubmitForm(dialogRef.getModalBody(), true)
                        formData.push({id: 'area', value: area})
                        formData.push({id: 'category', value: category})
                        formData.push({id: 'group', value: group})
                        data.together = arrayToJson(formData, true);
                        data.together = data.together.replace(/\n/g, '<br>');

                        var url = $CFG.remote.base_url + '/doing/' + $CFG.project + '/dish/add';
                        process(url, data, function (resp) {
                            if (resp.result == "success") {
                                alert("Bạn đã thêm món ăn thành công");
                                dialogRef.close();
                                if (typeof callback === 'function') {
                                    callback(resp);
                                }
                                $("#tbl_" + self.module).datagrid('reload');
                            }
                        });
                    }
                }]
			},
            
			function(resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
				    $("#tbl_"+self.module).datagrid('reload');
                }
			}
		);
    }, showEditForm: function() {
    	var self = this;
    	var row = $("#tbl_"+self.module).datagrid('getSelected');
    	if (row != null) {
	        $.dm_datagrid.showEditForm(
				{
					module: $CFG.project+'/'+self.module,
					action:'edit',
					title:'Chỉnh sửa',
                    size: size.wide,
                    fullScreen: true,
					content: function(element) {
						process($CFG.project+'/'+self.module+'/editForm', {id: row.id, async: true}, function(resp){
							$.dish.angular(element,resp.html,function($scope){
                                $scope.dish.row = resp.row;
                                
                                $scope.dish.row.description = $scope.dish.row.description.replace(/&amp;lt;br&amp;gt;/g, '\n')
                                $scope.dish.row.recipe = $scope.dish.row.recipe.replace(/&amp;lt;br&amp;gt;/g, '\n')

                                if($scope.dish.row.public ==1){
                                    $scope.dish.row.selected = true;
                                }
                                $scope.dish.foods = JSON.parse(resp.row.ingredient);
                                if (resp.row.number_childs) {
                                    $scope.dish.number_childs = JSON.parse(resp.row.number_childs);
                                    for (var i in $scope.dish.number_childs) {
                                        $scope.dish.number_childs[i] = parseInt($scope.dish.number_childs[i]);
                                    }
                                }else{
                                    $scope.dish.number_childs = {};
                                    $.each($scope.dish.groups, function (index, value) {
                                        $scope.dish.number_childs[value.id] = 100;
                                    });
                                }
                                $scope.dish.group_radio = Object.keys($scope.dish.number_childs)[0];
                                $scope.dish.number_child = $scope.dish.number_childs[$scope.dish.group_radio];
                                var group_id = $scope.dish.group_radio;
                                var check_group = $scope.dish.checkGroupSelected();
                                $scope.dish.selected.food_ids = [];
                                angular.forEach($scope.dish.foods,function(food, index){
                                    if(food['tpc'] == 1) {
                                        food.selected = true;
                                    }else{
                                        food.selected = false;
                                    }
                                    if(group_id == undefined) {
                                        food.calo_for_one = $scope.sumCaloFood(food);
                                    }
                                    $scope.dish.selected.food_ids.push(food.food_id);
                                });
                                angular.forEach($scope.dish.areas, function(area, index){
                                    if(in_array(area.id+'',resp.row.area_ids)){
                                        area.selected = true;
                                    }else{
                                        area.selected = false;
                                    }

                                });
                                $scope.dish.new_groups = {};
                                angular.forEach($scope.dish.groups,function(group,index){
                                    if(in_array(group.id+'',resp.row.group_ids)){
                                        group.selected = true;
                                        $scope.dish.new_groups[index] = [];
                                        $scope.dish.new_groups[index]['id'] = group.id;
                                        $scope.dish.new_groups[index]['name'] = group.name;
                                    }else{
                                        group.selected = false;
                                    }
                                });
                                angular.forEach($scope.dish.categories,function(category,index){
                                    if(in_array(category.id+'',resp.row.category_ids)){
                                        category.selected = true;
                                    }else{
                                        category.selected = false;
                                    }
                                });
                            });
						})
					},
                    buttons: [{
                        id: 'btn-save',
                        icon: 'glyphicon glyphicon-floppy-disk',
                        label: 'Lưu',
                        cssClass: 'btn-primary',
                        action: function(dialogRef){

                            let area = $.dish.areas.filter(item => item.selected == true).map(item => item.id).toString()
                            let category = $.dish.categories.filter(item => item.selected == true).map(item => item.id).toString()
                            let group = Object.values($.dish.groups).filter(item => item.selected == true).map(item => item.id).toString()

                            let data = $.dish.getDataForm(dialogRef.getModalBody())
                            let formData = getSubmitForm(dialogRef.getModalBody(),true)
                            formData.push({id: 'area', value: area})
                            formData.push({id: 'category', value: category})
                            formData.push({id: 'group', value: group})
                            data.together = arrayToJson(formData, true);
                            data.together = data.together.replace(/\n/g, '<br>');
                            
                            let url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/dish/edit';
                            process(url, data, function(resp) {
                                if (resp.result == "success") {
                                    dialogRef.close();
                                    if (typeof callback === 'function') {
                                        callback(resp);
                                    }
                                    $("#tbl_"+self.module).datagrid('unselectAll');
                                    $("#tbl_"+self.module).datagrid('reload');
                                }
                            });
                        }
                    }]
				},
				function(resp) {
                    $("#tbl_"+self.module).datagrid('unselectAll');
                    $("#tbl_"+self.module).datagrid('reload');
				}
			);
	    } else {
	    	$.messager.alert('Thông báo.', 'Phải chọn một dòng!');
	    }
       
    }, showNBForm: function(id) {
        var self = this;
        // var row = $("#tbl_"+self.module).datagrid('getSelected');
        if(id != null) {
            $.dm_datagrid.showEditForm(
                {
                    module: $CFG.project+'/'+self.module,
                    action:'edit',
                    title:'Chỉnh sửa',
                    size: size.wide,
                    fullScreen: true,
                    content: function(element){

                        process($CFG.project+'/'+self.module+'/editForm', {id: id}, function(resp){
                            // console.log(resp);
                            $.dish.angular(element,resp.html,function($scope){
                                
                                $scope.dish.row = resp.row;
                                
                                $scope.dish.row.description = $scope.dish.row.description.replace(/&amp;lt;br&amp;gt;/g, '\n')
                                $scope.dish.row.recipe = $scope.dish.row.recipe.replace(/&amp;lt;br&amp;gt;/g, '\n')

                                if($scope.dish.row.public ==1){
                                    $scope.dish.row.selected = true;
                                }
                                // console.log(row);
                                $scope.dish.foods = JSON.parse(resp.row.ingredient);
                                if (resp.row.number_childs) {
                                    $scope.dish.number_childs = JSON.parse(resp.row.number_childs);
                                    for (var i in $scope.dish.number_childs) {
                                        $scope.dish.number_childs[i] = parseInt($scope.dish.number_childs[i]);
                                    }
                                }else{
                                    $scope.dish.number_childs = {};
                                    $.each($scope.dish.groups, function (index, value) {
                                        $scope.dish.number_childs[value.id] = 100;
                                    });
                                }
                                $scope.dish.group_radio = Object.keys($scope.dish.number_childs)[0];
                                $scope.dish.number_child = $scope.dish.number_childs[$scope.dish.group_radio];
                                angular.forEach($scope.dish.foods,function(food,index){
                                    $scope.dish.number_child = food['number_child'];
                                    // console.log(food);
                                    // $scope.dish.
                                    if(food['tpc'] ==1){
                                        food.selected = true;
                                    }else{
                                        food.selected = false;
                                    }
                                })
                                angular.forEach($scope.dish.areas,function(area,index){
                                    // console.log(resp.row.aea_ids);
                                    if(in_array(area.id+'',resp.row.area_ids)){
                                        area.selected = true;
                                    }else{
                                        area.selected = false;
                                    }

                                });
                                angular.forEach($scope.dish.groups,function(group,index){
                                    if (resp.row.group_ids.includes(group.id.toString())) {
                                        group.selected = true;
                                    }
                                });
                                angular.forEach($scope.dish.categories,function(category,index){
                                    if(in_array(category.id+'',resp.row.category_ids)){
                                        category.selected = true;
                                    }else{
                                        category.selected = false;
                                    }
                                });
                                $.dish.buildCombobox($scope);
                            });
                        })
                    },
                    buttons: [{
                    id: 'btn-save',
                    icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn-primary',
                    action: function(dialogRef){
                        // var data = $.dish.getDataForm(dialogRef.getModalBody());
                        // data.together = arrayToJson(getSubmitForm(dialogRef.getModalBody(),true));

                        let area = $.dish.areas.filter(item => item.selected == true).map(item => item.id).toString()
                        let category = $.dish.categories.filter(item => item.selected == true).map(item => item.id).toString()
                        let group = Object.values($.dish.groups).filter(item => item.selected == true).map(item => item.id).toString()

                        let data = $.dish.getDataForm(dialogRef.getModalBody())
                        let formData = getSubmitForm(dialogRef.getModalBody(),true)
                        formData.push({id: 'area', value: area})
                        formData.push({id: 'category', value: category})
                        formData.push({id: 'group', value: group})
                        data.together = arrayToJson(formData, true);
                        data.together = data.together.replace(/\n/g, '<br>');

                        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/dish/nb';
                        process(url, data, function(resp){
                            if(resp.result == "success"){
                                dialogRef.close();
                                if(typeof callback === 'function'){
                                    callback(resp);
                                }
                                $("#tbl_"+self.module).datagrid('unselectAll');
                                $("#tbl_"+self.module).datagrid('reload');
                            }
                        });
                    }
                }]
                },
                function(resp){
                    $("#tbl_"+self.module).datagrid('unselectAll');
                    $("#tbl_"+self.module).datagrid('reload');
                }
            );
        }else{
            $.messager.alert('Thông báo.', 'Phải chọn một dòng!');
        }
       
    }, showShareForm: function(callback) { 
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'share',
                title:'Thêm mới món ăn',
                size: size.wide,
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'share', {id: self.id}, function(resp){
                        $.dish.angular(element,resp,function(){
                        });
                    })
                },
               
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }else{
                    $("#tbl_"+self.module).datagrid('reload');
                }
            }
        );
    }, angular: function(element,resp,callback,dialogRef) {
        var form = '<div >'+resp+'</div>';
        angular.element($('#mainContentController')).scope().$apply(function(scope){
            $(element).html(scope.compile(form,scope));
            if(typeof callback === 'function'){
                callback(scope);
            }
        }); 
        
    }, getDataForm: function(element) {
        var data = {};
        var scope = angular.element($('#mainContentController')).scope();
        angular.forEach(scope.dish.foods,function(food,index){
            food['number_child'] = scope.dish.number_child;
            food['sum_calo'] = scope.dish.sum_calo();
        });
        data.foods = JSON.stringify(scope.dish.foods);
        data.number_childs = scope.dish.number_childs;
        return data;
    }, del: function() { // XÓA
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });

        var captcha = $CFG.dialog_captcha('delete_dish');

        if (Object.keys(rows_selected).length < $CFG.record_delete_show_captcha) captcha = '';

        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Nếu đồng ý xóa, bạn sẽ ko thể khôi phục được các món ăn đã xóa! Hãy chắc chắn bạn muốn xóa?</div>' + captcha, function(r){
            if (r){
                $.dm_datagrid.del($CFG.project+'/'+self.module,{ids , captcha: $('.panel [name="delete_dish_captcha"]').val()},function(resp){
                    if (resp.result === 'success') {
                        $("#tbl_"+self.module).datagrid('unselectAll');
                        $("#tbl_"+self.module).datagrid('reload');
                    }
                })
            }
        });
    }, restore: function() { // KHÔI PHỤC
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });

        var captcha = $CFG.dialog_captcha('delete_dish');

        if (Object.keys(rows_selected).length < $CFG.record_delete_show_captcha) captcha = '';

        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        $.messager.confirm('Xác nhận', '<div style = "font-size: 14px">Xác nhận khôi phục?</div>' + captcha, function(r){
            if (r){
                $.dm_datagrid.del($CFG.project+'/'+self.module,{ids , captcha: $('.panel [name="delete_dish_captcha"]').val(),type: 'restore'},function(resp){
                    if (resp.result === 'success') {
                        $("#tbl_"+self.module).datagrid('unselectAll');
                        $("#tbl_"+self.module).datagrid('reload');
                        // $.messager.alert('Thông báo','Đã khôi phục được '+ids.length+' bản ghi');
                    }
                })
            }
        });
    }, doSearch: function() {
        var self = this;
        $.dm_datagrid.doSearch('tbl_'+self.module,{name:"contains",group_ids:"comma_contains",area_ids:"comma_contains",category_ids:"comma_contains"},'and');
    }, applySharing: function (id, value) {
        var self = $.dish;
        var url = $CFG.remote.base_url+'/doing/'+$CFG.project+'/'+self.module+'/applySharing';
        var data = {async: true, data: {id: id, public: value}};
        process(url, data, function (resp) {
            if (resp.result == 'success') {
                var rows = $('#tbl_dish').datagrid('getRows');
                var row = {};
                var index = -1;
                angular.forEach(rows, function (r, ind) {
                    if (r.id == id) {
                        index = ind;
                        row = r;
                    }
                });
                row.public = value;
                $('#tbl_dish').datagrid('deleteRow', index);
                $('#tbl_dish').datagrid('insertRow', {index: index, row});
            }else {
                $.messager.alert('Thông báo.', 'Bạn không có quyền này!');
            }
        }, null, false);
    }, share2: function() { // CHIA SẺ
        var self = this;
        var ids = [];
        var rows_selected = {};
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
            rows_selected[row.id] = row;
        });
        if(ids.length == 0) {
            $.messager.alert('Thông báo','Hãy chọn một dòng!');
            return;
        }
        var captchaForm = $CFG.dialog_captcha('share_dish');
        var msg = '<div style = "font-size: 14px">Bạn có chắc muốn chia sẻ?</div>' + captchaForm;
        $.messager.confirm('Xác nhận', msg, function(r){
            if (r){
                var captcha = $('input[name="share_dish_captcha"]').val();
                process($CFG.project+'/'+self.module+'/share2',{ids: ids, captcha:captcha},function(resp){
                    if (resp.result == 'success') {
                        $("#tbl_"+self.module).datagrid('reload');
                    } else {
                        $.dish.share2();
                        $.messager.alert('Thông báo', resp.errors);
                    }
                },function(){
                    // TO DO
                },false);
            }
        });
    }
}
