<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/common.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/main-report.css?_=605422678" />
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
	<script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.0/xlsx.full.min.js"></script>

	<style type="text/css">
		*{
			font-family: Nunito_Regular;
			font-size: 13 !important;
		}
		@media  print {
			body > .panel {
				display: none !important;
			}
		}
	</style>
	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
	<script type="text/javascript">
		$CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'dinhduong',
			school_point: +'1',
			unit_id: parseInt('51461'),
            school_points: +'1',
            school_point_together: parseInt('0'),
            is_view_csdl_nganh: true,
            administrator: 1,//parseInt('0'),
			dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			level: '4',
      	};
	</script>
	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>

	<script src="http://localhost:3000/js/common.js?_=374017541"></script>
	<script type="text/javascript">
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
		angular_app_report = angular_app;
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=3761351230257"></script>
	<script src="http://localhost:3000/js/dinhduong/main-angular-report.js?_=1972358650"></script>
	<script type="text/javascript" src="http://localhost:3000/js/library.js?v=576777159"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController">
		<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css">
    <div class="report-container" >

        <div id="content">
            <table class="table table-bordered table-hover" style="width: 97%">
                <thead id="table-header" class="thead-dark">
                    <tr>
                        <th>STT</th>
                        <th>Thông tin CKS</th>
                        <th>Năm học</th>
                        <th>Nhóm</th>
                        <th>Biểu</th>
                        <th>Thời gian</th>
                        <th>File</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                                                        </tbody>
            </table>
            <div id="container">
                <div id="pagination">
                                                                                    <a href="?page=2" class="blocks">&raquo;</a>
                                    </div>
              </div>
            
            
        </div>
        
    </div>
    <style>
        #content {
            text-align: center;
            vertical-align:middle;
        }
        table{
            margin: 20px;
            font-size: 16px;
            width: auto; /* Bảng tự co giãn theo nội dung */
            white-space: nowrap; /* Ngăn chặn quấn dòng */
            overflow-x: auto;
            border-collapse: collapse;
        }
        thead {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 2;
        }
    
        
        th, td{
            padding: 5px;
            
        }
        .btn {
            float: right;
            margin-right: 5px
        }
        #pagination{
            display: flex;
            justify-content: center;
          }
          .blocks{
            width: 40px;
            line-height: 40px;
            text-align: center;
            border: 1px solid #DDDDDD;
            display: inline-block;
            text-decoration: none;
            color: black;
          }
          .blocks:not(:first-child){
            margin-left: 5px;
          }
          .blocks:first-child{
            border-radius: 10px 0 0 10px;
          }
          .blocks:last-child{
            border-radius: 0 10px 10px 0;
          }
          .blocks:hover{
            background-color: #DDDDDD;
          }
          #pagination a.active{
            background-color: #4CAF50;
          }
    </style>
    <script>
        

    </script>
	</div>
</body>
<style>
	@media  print {
		.btn {
		  display: none !important;
		}
	}
</style>
<script>
	var apiUrl = $CFG.remote.base_url + '/doing/admin/user/';
	var url = $CFG.remote.base_url + '/images/signs/' + $CFG.unit_id + '/';
	function getSignConfig(date, module, group_id) {
		var params = {
			module : module,
			date : date,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'getSignConfig',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				if (response.success) {
					let data = JSON.parse(response.data.sign_data);
					Object.keys(data).forEach((v) => {
						$('#'+v).attr('src',data[v]['filename']) ;
						$('#'+v).attr('title',data[v]['time']) ;
					})
					let eleBrs = document.querySelectorAll(".break_line");
					eleBrs.forEach(v => {
						v.style.display = 'none';
					});
				}
			}
		});
	}

	function getSignWithType(date, module, group_id, type) {
		var params = {
			module : module,
			date : date,
			type : type,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'signing',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				alert(response.message)
				if (response.success) {
					$('#'+type).attr('src',response.signInfo.filename);
					$('#'+type).attr('title',response.signInfo.time);
				}
			}
		});
	}
</script>
</html>
