/**
 * anhnt
 */


/**
 * HTML to file
 *
 * File.export();
 * @param {string} element.
 * @param {string} filename.
 * @param {string} type.
 * @param {string} sheet name for excel.
 * @return file.
 *
 * HTML custom
 * background: #cccccc;
 * border: .5pt solid black;
 * font-weight: 700;
 * text-align: center;
 * font-style: italic;
 * width:60pt; width max a4 ~ 480pt
 * vertical-align: middle;
 * mso-number-format:'\@' format cell text;
 * <table style="font-family: 'Times New Roman'">
 */
var Export = {
    uri: 'data:application/vnd.ms-excel;base64,',
    template: '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" ' +
        'xmlns="http://www.w3.org/TR/REC-html40"><meta http-equiv="content-type" content="application/vnd.ms-excel; charset=UTF-8"><head>' +
        '<!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
        '</head><body>{content}</body></html>',
    templateLandscapeOrientation: '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" ' +
        '<head> <meta http-equiv=Content-Type content="text/html; charset=utf-8"> <meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11">'+
        ' <style>@page {mso-page-orientation:landscape;}  --></style>'+
        ' <!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/><x:Print><x:ValidPrinterInfo/>'+
        ' <x:PaperSizeIndex>9</x:PaperSizeIndex></x:Print><x:Selected/>'+
        ' <x:Panes><x:Pane><x:Number>1</x:Number><x:ActiveRow>1</x:ActiveRow></x:Pane></x:Panes><x:ProtectContents>False</x:ProtectContents><x:ProtectObjects>False</x:ProtectObjects>'+
        '<x:ProtectScenarios>False</x:ProtectScenarios></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets>'+
        ' <x:ProtectStructure>False</x:ProtectStructure><x:ProtectWindows>False</x:ProtectWindows></x:ExcelWorkbook>'+
        '</xml><![endif]-->' +
        '</head><body>{content}</body></html>',
    base64: function (s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    },
    format: function (s, c) {
        return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
        })
    },
    file: function (element, filename, type, worksheet, isLandscapeOrientation = false) {
        this.Table.setBorder(element, 'border');
        type = type || 'xls';
        worksheet = worksheet || 'worksheet';
        var content = Remove.class(element, 'excel-hidden');
        content = Remove.class(element, 'print-hidden', content);
        // Escape các giá trị trong bảng để tránh lỗi Formula Injection
        content = this.escapeTableContent(content);
        var ctx = {worksheet: worksheet, content: content};
        var link = document.createElement("a");
        if (link.download !== undefined) {
            let template = isLandscapeOrientation ? this.templateLandscapeOrientation : this.template;
            
            link.setAttribute("href", this.uri + this.base64(this.format(template, ctx)));
            link.setAttribute("download", filename + '.' + type);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            alert('Export file only works in Chrome, Firefox, and Opera!');
        }
    },
    escapeTableContent: function(content) {
        var tempDiv = document.createElement("div");
        tempDiv.innerHTML = content;
        var cells = tempDiv.querySelectorAll("td, th");
    
        cells.forEach(cell => {
            this.sanitizeCell(cell);
        });
    
        return tempDiv.innerHTML;
    },
    
    sanitizeCell: function(cell) {
        let child = cell.firstChild;
        // Bỏ qua các COMMENT_NODE và các ELEMENT_NODE không chứa nội dung
        let next = true;
        while (child && (
            child.nodeType === Node.COMMENT_NODE || 
            (child.nodeType === Node.ELEMENT_NODE && !child.textContent.trim().replace("\n", ""))
            || (child.nodeType === Node.TEXT_NODE && !child.nodeValue.trim().replace("\n", ""))
        )) {
            child = child.nextSibling; // Chuyển sang node kế tiếp
        }

        if (!child) return; // Không có nội dung hợp lệ, thoát

        if (child.nodeType === Node.TEXT_NODE) {
            // Nếu là TEXT_NODE, kiểm tra nội dung
            let value = child.nodeValue.trim();
            if (/^[\t\r=+\-@]/.test(value)) {
                child.nodeValue = "'" + value;
            }
        } else if (child.nodeType === Node.ELEMENT_NODE) {
            // Nếu là ELEMENT_NODE, kiểm tra phần tử con đầu tiên có nội dung
            let firstSubChild = child.firstChild;
            while (firstSubChild && (
                firstSubChild.nodeType === Node.COMMENT_NODE || 
                (firstSubChild.nodeType === Node.ELEMENT_NODE && !firstSubChild.textContent.trim())
            )) {
                firstSubChild = firstSubChild.nextSibling;
            }

            if (firstSubChild && firstSubChild.nodeType === Node.TEXT_NODE) {
                let textValue = firstSubChild.nodeValue.trim();
                if (/^[\t\r=+\-@]/.test(textValue)) {
                    firstSubChild.nodeValue = "'" + textValue;
                }
            }
        }
    },
    Table: {
        setBorder: (elementRoot, className)=>{
            var selector = document.getElementById(elementRoot);
            $(selector).find('table.'+className).each((ind, el)=>{
             $(el).children('tbody,thead').children().each((ind1, tr)=>{
                 $(tr).children().each((ind2, td)=> {
                     if (ind1 === 0) {
                         $(td).css({
                             'border-top': '1pt solid #ccc',
                         });
                     }
                     if (ind2 === 0){
                         $(td).css({
                             'border-left': '1pt solid #ccc',
                         });
                     }
                     $(td).css({
                         'border-bottom': '1pt solid #ccc',
                         'border-right': '1pt solid #ccc'
                     });
                 })
             });
            })
            /*content = content || selector.innerHTML;
            $(selector).find('.' + className).each(function (index, item) {
                content = content.replace(item.outerHTML, '');
            });
            return content;*/
        }
    }
};

var Remove = {
    class: function (elementRoot, className, content) {
        var selector = document.getElementById(elementRoot);
        content = content || selector.innerHTML;
        $(selector).find('.' + className).each(function (index, item) {
            content = content.replace(item.outerHTML, '');
        });
        return content;
    },
    tag: function (elementRoot, tagName, content) {
        var selector = document.getElementById(elementRoot);
        content = content || selector.innerHTML;
        return $(content).not(tagName).html();
    }
};
var Numbers = {

    /**
     * Format a number with grouped thousands
     * @param {number} number
     * @param {string} type "us", "vi"...
     * @return {string} number format
     */

    format: function (number, type = 'us') {
        if (number) {
            return number.toLocaleString(type, {minimumFractionDigits: 0, maximumFractionDigits: 10});
        }
        return '';
    },


    /**
     * Round a number to a decimal
     * @param number
     * @param {number} decimal decimal
     * @return {number} number format
     */

    round: function (number, decimal = 0) {
        number = parseFloat(number);
        return Number(number.toFixed(decimal));
    },

    /**
     * Round a number to a decimal and format
     * @param number
     * @param {number} decimal decimal
     * @param {string} type "us", "vi"...
     * @return {string} number format
     */

    roundFormat: function (number, decimal = 0, type = 'us') {
        number = parseFloat(number);
        return Numbers.format(Number(number.toFixed(decimal)), type);
    }

};

var Url = {
    'url': new URL(window.location.href),
    get: function (param) {
        return this.url.searchParams.get(param);
    }
};

var DTime = {

    format: {
        options: {day: 'numeric', month: 'numeric', year: 'numeric'},
        locales: "vi-US",
    },

    getDaysInMonth: function (month, year) {
        var date = new Date(year, month, 1);
        var days = [];
        while (date.getMonth() === month) {
            days.push(new Date(date));
            date.setDate(date.getDate() + 1);
        }
        return days;
    },

    /**
     * var dates = DTime.getDates('2018-10-01', '2018-10-05');
     * date.toLocaleDateString("vi-US", options) get format
     * */
    getDates: function (to, form) {
        to = new Date(to);
        form = new Date(form);
        var dates = [],
            currentDate = to,
            addDays = function (days) {
                var date = new Date(this.valueOf());
                date.setDate(date.getDate() + days);
                return date;
            };
        while (currentDate <= form) {
            dates.push(currentDate);
            currentDate = addDays.call(currentDate, 1);
        }
        return dates;
    },

    getFormatSQL: function (objDate) {
        var y = objDate.getFullYear();
        var m = objDate.getMonth() + 1;
        var d = objDate.getDate();
        return y + '-' + (m < 10 ? ('0' + m) : m) + '-' + (d < 10 ? ('0' + d) : d);
    }
};

var Arr = {
    /**
     * Round a number to a decimal
     * @param {array} array
     * @param {string} field
     * @param {string} direction
     * @return {array} array sort
     */
    sortBy: function (array, field, direction = 'asc') {
        array.sort(sortFunction);

        function sortFunction(a, b) {
            if (a[field] === b[field]) {
                return 0;
            } else {
                if (direction === 'desc')
                    return (a[field] < b[field]) ? 1 : -1;
                else
                    return (a[field] < b[field]) ? -1 : 1;
            }
        }

        return array;
    },
    onlyUnique: function (value, index, self) {
        return self.indexOf(value) === index;
    },
    unique: function (arr) {
        return arr.filter(Arr.onlyUnique);
    }
};

var PHPExcel = {
    getData: function (attribute, attributeFormat = '', getValue = true) {
        var data = [];
        var selectors = $('[' + attribute + ']');
        var length = selectors.length;
        for (var i = 0; i < length; i++) {
            var selector = selectors[i];
            var cell = selector.getAttribute(attribute);
            var item = {cell: cell};

            if (getValue) {
                var value = selector.innerText;
                if (selector.type === 'text')
                    value = selector.value;
                item.value = value;
            }

            if (attributeFormat)
                item.format = selector.getAttribute(attributeFormat);
            data.push(item)
        }
        return data;
    },
    export: function (data, url = '') {
        url = url || $CFG.remote.base_url + '/doing/process/exportExcel';
        if (data) {
            var form = jQuery('<form action="' + url + '" method="POST" style="display: none"><input type="hidden" name="_token" value="'+$('meta[name="X-CSRF-TOKEN"]').attr('content')+'"></form>');
            jQuery('body').append(form);
            jQuery.each(data, function (key, val) {
                if (typeof val === 'object')
                    val = JSON.stringify(val);
                var input = jQuery('<input type="hidden" name="' + key + '" value=""/>');
                form.append(input);
                input.val(val);
            });
            $('<input type="submit" value="submit">').appendTo(form);
            form.submit();
        }
    }
};

var OObject = {
    size: function (obj) {
        var size = 0, key;
        for (key in obj) {
            if (obj.hasOwnProperty(key)) {
                size++;
            }
        }
        return size;
    },
    calculate: function (obj, type, value, isRound) {
        isRound = isRound || false;
        var fields = Object.keys(obj);
        fields.forEach(function (field) {
            var tmp = 0;
            if (typeof value === 'object') {
                tmp = eval(obj[field] + type + value[field]);
            } else {
                tmp = eval(obj[field] + type + value);
            }
            if (isRound) {
                obj[field] = round(tmp, isRound);
            } else {
                obj[field] = tmp;
            }
        });
        return obj;
    },

};