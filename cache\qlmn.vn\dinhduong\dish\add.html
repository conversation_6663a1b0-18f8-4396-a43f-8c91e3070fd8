<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/dish.css?_=1">
<form class="form-horizontal" id="frm-dish-add" role="form" ng-init="dish.initFormAdd()">
	<div class="tab-main-dish">
		<ul class="nav nav-tabs nav-tab-student">
		  	<li class="active"><span data-toggle="tab" href="#dish-info"><i class="glyphicon glyphicon-leaf"></i> Thông tin món ăn</span></li>
		  	<li><span data-toggle="tab" href="#nl-dish"><i class="glyphicon glyphicon-grain"></i> Nguyên liệu món <b>"{{dish.row.name}}"</b></span></li>
		</ul>
		<div class="tab-content">
			<div id="dish-info" class="tab-pane fade in active">
				<div class="in-dish-info">
					<div class="left-dish col-md-7">
						<div class="row">
							<div style="width: 100%;">
								<div class="form-group border-radius-5 name-dish">
									<div class="text-name-dish col-md-7">
										<div class="title-add-dish-new">
											<label class="col-md-12 control-label valid" style="">Tên món ăn</label>
										</div>
										<div class="content-add-dish">
											<input class="form-control" id="name" width="100%" ng-model="dish.row.name">
											<input class="form-control" type="hidden" id="id" width="100%" ng-model="dish.row.id" ng-value="dish.row.id">
										</div>
									</div>
									<div class="image-name-dish col-md-5">
										<img ng-if="dish.row.avatar" id="" class="image-first" ng-src="{{$CFG.remote.base_url}}/{{dish.row.avatar}}">
										<img ng-if="!dish.row.avatar" id="" class="image-first" ng-src="{{$CFG.remote.base_url}}/files/images/demo.jpg">
										<img class="image-last" hidden="" src="{{$CFG.remote.base_url}}/{{dish.image}}">
										<input class="input-image-dish" file-upload="dish.image" url="{{$CFG.remote.base_url}}/doing/dinhduong/dish/upload" upload-done="dish.uploadDone(dish.image)" size="4" >

										<input hidden="" type="" id="image" name="image" ng-value="dish.image">
										<input ng-if="!dish.row.avatar" hidden="" type="" id="avatar" name="avatar" ng-value="'documents/images/demo.jpg'">
										<input ng-if="dish.row.avatar" hidden="" type="" id="avatar" name="avatar" ng-value="dish.row.avatar">
									</div>
								</div>
								<div class="form-group">
									<div class="title-add-dish-new">
										<label class="col-md-8 control-label">Mô tả</label>
									</div>
									<div class="content-add-dish">
										<textarea class="form-control" style="min-height: 65px;" id="description" width="100%">{{dish.row.description}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<div class="title-add-dish-new">
										<label class="col-md-8 control-label">Cách chế biến</label>
									</div>
									<div class="content-add-dish">
										<textarea style="min-height: 80px;" class="form-control" id="recipe" width="100%">{{dish.row.recipe}}</textarea>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="right-dish col-md-offset-1 col-md-4">
						<div class="row">
							<!-- <div class="form-group">
								<div class="title-add-dish">
									<label class="col-md-8 control-label">Nhóm tuổi</label>
								</div>
								<div class="content-style-dish">
									<div class="col-md-12" ng-repeat="group in dish.groups">
										<label class="checkbox-inline">
											<input type="checkbox" id="group" ng-value="group.id" ng-model="group.selected" ng-click="dish.clickGroup(dish.groups)">
											<text ng-bind="group.name"></text>
										</label>
									</div>
								</div>
							</div> -->
							<div class="form-group">
								<div class="title-add-dish">
									<label class="col-md-8 control-label">Loại món</label>
								</div>
								<div class="content-style-dish">
									<div class="col-md-6" ng-repeat="category in dish.categories">
										<label class="checkbox-inline">
											<input type="checkbox" ng-value="category.id" ng-model="category.selected">
											<text ng-bind="category.name"></text>
										</label>
									</div>
								</div>
							</div>	
							<div class="form-group">
								<div class="title-add-dish">
									<label class="col-md-8 control-label">Vùng miền</label>
								</div>
								<div class="content-style-dish">
									<div class="col-md-6" ng-repeat="area in dish.areas">
										<label class="checkbox-inline">
											<input type="checkbox" ng-value="area.id" ng-model="area.selected">
											<text ng-bind="area.name"></text>
										</label>
									</div>
								</div>
							</div>	
						</div>
					</div>
				</div>
			</div>
			<div id="nl-dish" class="tab-pane fade">
				<div class="form-group">
			    	<div class="content-style-dish">
						<div class="header-dish">
							<div class="form-group">
								<div class="group-title">
									<label class="col-md-8 control-label">Nhóm tuổi</label>
								</div>
								<div class="content-style-dish">
									<div class="col-md-2" ng-repeat="group in dish.groups">
										<label class="checkbox-inline">
											<input type="checkbox" ng-value="group.id" ng-model="group.selected">
											<text ng-bind="group.name"></text>
										</label>
									</div>
								</div>
							</div>
						</div>
						<table class="tb-dish" id="list-food" >
							<thead>
								<tr class="tr-title-dish">
									<th class="td-w-5" rowspan="2">STT</th>
									<th class="td-w-5" rowspan="2">Mã TP</th>
									<th class="td-w-15" rowspan="2">
										<!-- <div id="div-ingredient" class=""></div> -->
										<input combo-box-food="dish.selected.food" data="cache.foods"
											   url="{{$CFG.remote.base_url}}/doing/dinhduong/dish/foods"
											   mode="remote"
											   clear-on-selected="true"
											   on-select="dish.onSelectFood(dish.selected.food)" value-field="id" text-field="name"  placeholder="Tên thực phẩm">
									</th>
									<th class="color-green td-w-15" colspan="{{dish.checkGroupSelected()}}">
										Lượng 1 trẻ (g)
									</th>
									<th class="td-w-7 color-green" rowspan="2">
										Thực ăn 1 nhóm </br>
										(kg)
									</th>
									<th class="td-w-7 color-green" rowspan="2">
										Thực mua 1 nhóm </br>
										(kg)
									</th>
									<th class="td-w-7 color-green" rowspan="2">
										Thực mua theo</br>
										(ĐVT)
									</th>
									<th class="td-w-7 color-green" rowspan="2">
										Lượng 1 trẻ theo</br>
										(ĐVT)
									</th>
									<th class="td-w-5" rowspan="2">
										Calo/ 1 trẻ
									</th>
									<!-- <td class="td-w-5">
										Calo/ 100(g)
									</td> -->
									<th class="td-w-5" rowspan="2">
										ĐVT
									</th>
									<th class="td-w-5" rowspan="2">
										Hệ số thải bỏ</br>
										(%)
									</th>
									<th class="td-w-6" rowspan="2">
										Quy đổi ra gam
									</th>
									<th class="td-w-5" title="Chọn thực phẩm chính cho món ăn" rowspan="2">
										TPC
									</th>
									<th class="td-w-5" rowspan="2">
										<i class="glyphicon glyphicon-trash btn-color-blue del-food" ng-click="dish.delAllFood()" title="Xóa hết danh sách món ăn đã chọn"></i>
									</th>
								</tr>
								<tr>
									<th ng-if="!dish.checkGroupSelected()" class="td-w-5"></th>
									<th ng-repeat="(index,group) in dish.groups" ng-if="group.selected" class="td-w-5">
										<div class="checkbox-inline" ng-click="dish.group_radio = group.id; dish.onChangeRadio(group.id)">
											<span ng-bind="group.name"></span></br>
											<input type="radio" name="group_radio" ng-model="dish.group_radio"  ng-checked="dish.group_radio == group.id" ng-click="dish.onChangeRadio(group.id)">
										</div>								
									</th>
								</tr>
							</thead>
							
							<tbody>
								<tr ng-repeat="(food_id,food) in dish.foods">
									<td class="td-w-5" ng-bind="$index+1"></td>
									<td class="td-w-5" ng-bind="food.food_id"></td>
									<td class="td-w-15">
										<input type="text" ng-model="food.name" id="edit_name{{food_id}}" readonly="true">
										<i ng-if="food.notname" class="glyphicon glyphicon-info-sign" title="Tên gốc : {{food.food_name}}"></i>
									</td>
									<td class="td-w-5" ng-if="!dish.checkGroupSelected()">
										<input class="color-green" ng-focus="dish.quantityFocus(food)" ng-model="food.quantity" id="sgt{{food_id}}" ng-change="dish.onChangeOneG(food)" >
									</td>
									<td ng-repeat="(index_grp,group) in dish.groups" ng-if="group.selected" class="td-w-5">
										<input type-number="float" class="color-green" ng-focus="dish.quantityFocus(food,group.id)" ng-model="food.quantities[group.id]" id="quantity_{{group.id+'_'+food_id}}" ng-change="dish.onChangeOneGArr(food,group.id)" ng-keyup="keyUpFood($event,food_id,'quantity_'+group.id)">
									</td>
									<td class="td-w-6">
										<input type-number="float" ng-model="food.eat_a_group" id="slkg_{{food_id}}" ng-change="dish.onChangeEAG(food)" ng-keyup="keyUpFood($event,food_id,'slkg')">
									</td>
									<td class="td-w-6">
										<input type-number="float" ng-model="food.buy_a_group" id="byg_{{food_id}}" ng-change="dish.onChangeBAG(food)" ng-keyup="keyUpFood($event,food_id,'byg')">
									</td>
									<td class="td-w-6">
										<input type-number="float" ng-model="food.buy_a_dvt" id="dvt_{{food_id}}" ng-change="dish.onChangeBAD(food)" ng-keyup="keyUpFood($event,food_id,'dvt')">
									</td>
									<td class="td-w-6">
										<input type-number="float" ng-model="food.quantity_for_measure" id="qfm_{{food_id}}" ng-change="dish.onChangeOneDVT(food)" ng-keyup="keyUpFood($event,food_id,'qfm')">
									</td>
									<td class="td-w-5">
										<input disabled="true" ng-model="food.calo_for_one">
									</td>
									<!-- <td class="td-w-5">
										<input disabled="true" ng-model="food.nutritions.calo">
									</td> -->
									<td class="td-w-5">
										<input ng-model="food.measure_name">
									</td>
									<td class="td-w-5">
										<input type="number" ng-model="food.extrude_factor" ng-change="dish.onChangeEF(food)">
									</td>
									<td class="td-w-5">
										<input disabled="true" ng-model="food.gam_exchange">
									</td>
									<td class="td-w-5">
										<input type="checkbox" ng-model="food.selected">
									</td>
									<td class="td-w-5">
										<i class="glyphicon glyphicon-trash btn-color-blue del-food" ng-click="dish.delFood(food_id)" title="Xóa thực phẩm"></i>
									</td>
								</tr>
							</tbody>
							<tr style="background: #9DBABD;">
								<td class="td-w-5"></td>
								<td class="td-w-5"></td>
								<td class="td-w-15">Tổng</td>
								<td class="td-w-6" ng-if="!dish.checkGroupSelected()">
								</td>
								<td ng-repeat="(index,group) in dish.groups" ng-if="group.selected">
								</td>
								<!-- <td class="td-w-7"></td> -->
								<td class="td-w-7"></td>
								<td class="td-w-7"></td>
								<td class="td-w-7"></td>
								<td class="td-w-7"></td>
								<td class="td-w-5">
									<input id="sum_calo" ng-value="dish.sum_calo()" disabled="true">
								</td>
								<td class="td-w-5"></td>
								<td class="td-w-5"></td>
								<td class="td-w-5"></td>
								<td class="td-w-5"></td>
							</tr>
						</table>
					</div>
				</div>
			</div>

		</div>
	</div>
	<div class="share-dish">
		<div class="style-share-dish">
			<label>Người lập : </label><label style="margin-left: 5px;color: #8eb326;">d.hd.mnquangvt</label>
		</div>
		<div class="share-dish-right" >
			<label class="checkbox-inline">
				<input style="height: 20px; width: 10%; float: left;" class="form-control" type="checkbox" id="public" width="100%" ng-model="dish.row.selected">
				Chia sẻ món ăn
			</label>
			<div class="checkbox-inline">
				<label>Số lượng trẻ: </label>
				<input style="width: 50px; padding-left: 5px; height: 26px;" type-number="int" id="number-child" ng-model="dish.number_child" ng-change="dish.onChangeNC()"> trẻ
			</div>
		</div>
	</div>
</form>
<script type="text/javascript">
	$(document).ready(function(){
	  	$('#panel-title-top').click(function(){
			$("#panel-title-top i").toggleClass("glyphicon-chevron-down");
			$("#panel-title-top i").toggleClass("glyphicon-chevron-up");
	  	});
	  	$('#panel-title-bottom').click(function(){
	  		$("#panel-title-bottom i").toggleClass("glyphicon-chevron-up");
			$("#panel-title-bottom i").toggleClass("glyphicon-chevron-down");
	  	});
	});
//	$('.image-name-dish').hover(function(){
		$('.container-input-file').show();
//	}, function() {
//    	$('.container-input-file').hide();
//  });
</script>
<style type="text/css">
	.tb-dish{
		font-size: 13px;
	}
	.del-food{
		color: red;
	}
	.textbox-text{
		height: 26px !important;
	}
	.content-student-add input{
		height: 30px;
		border-radius: 0;
	}
	.content-student-add label{
		font-weight: normal;
		/*text-align: left !important;*/
	}
	.modal-body{
		padding: 0px;
	}
	.nav-tabs{
		border-bottom: 1px solid #d0e1ca; 
	}
	.in-dish-info{
		padding: 20px;
		width: 100%;
    	display: inline-block;
	}
	.title-add-dish-new label{
		text-align: left !important; 
		padding-left: 0px !important;
		margin-bottom: 10px !important;
	}
	.image-name-dish img{
		width: 125px;
		height: 105px;
		/*border: 1px solid #DDD;*/
	}
	#name{
		height: 33px;
		width: 250px;
		border: 1px solid #8eb326;
		border-radius: 5px; 
	}
	.border-radius-5{
		border-radius: 5px;
	}
	.content-add-dish textarea{
		border-radius: 5px;
		border-left: 3px solid #8eb326;
		height: 100px !important;
	}
	.file_upload_choise{
		display: none;
	}
	.share-dish{
		width: 100%;
		display: inline-block;
		padding-left: 20px;
		border-top: 1px solid #DDD;
		border-bottom: 1px solid #DDD;
		padding-top: 5px;
		background: #ece0e0;
	}
	.style-share-dish{
		width: 50%;
		float: left;
	}
	.share-dish-right{
		width: 30%;
		float: right;
	}
	.name-dish{
		background: #f7f5cc;
		padding-bottom: 5px;
		padding-top: 5px;
	}
	.combo{
		margin-top: 0px;
		margin-bottom: 5px;
	}
	.image-name-dish{
		position: relative;
	}
	.container-input-file{
		position: absolute;
		z-index: 555;
		top: 0;
		right: 0;
		display: none;
	}
	.td-w-30 input{
		width: 95%;
		float: left;
		margin-right: 0px;
	}
	.td-w-30 i{
		padding-top: 2px;
		width: 5%;
		float: left;
		color: #8585ef;
	}
	.group-title{
		width: 100%;
    	display: inline-block;
   		background: #eef6fd;
	}
	.group-title label{
		text-align: left !important;
	}
	div.checkbox-inline, .radio-inline{
		padding-left: 0px !important;
	}
	.input-image-dish{
		position: absolute;
		width: 100%;
		height: 100%;
	}
	.modal-body > div, .modal-body form, .modal-body .tab-main-dish{
		height: 100%;
	}
	.modal-body .tab-main-dish{
		padding-bottom: 35px;
		overflow-y: auto;
	}
	.modal-body .share-dish{
		bottom: 0;
	}

</style>