(function (app) {
    app.controller('SettingDiseaseController', SettingDiseaseController);

    function SettingDiseaseController($scope, $uibModal, DiseaseService) {
        var vm = this;

        $scope.sys.module.title = '<PERSON>u<PERSON>n lý n<PERSON> bệnh theo dõ<PERSON>';

        vm.diseases = [];

        vm.loading = false;

        vm.grades = [];

        vm.grade_selected = 0;

        // console.log()

        // let tmp = 0;
        vm.grades = _.map($CFG.grades, function (item) {
            return {id: item.id, label: item.name};
        });

        vm.grades.unshift({id: 0, label: 'Tất cả'})

        vm.onGradeChanged = function(){
            fetchDiseases();
        }

        vm.gradesInDisease = function(grade_ids) {
            let grades = [];
            if(grade_ids){
                let arr_grade_ids = grade_ids.toString().split(',');
                grades = _.reduce($CFG.grades, (tmp, item) => {
                    if(arr_grade_ids.includes(item.id.toString()) && item.name) {
                        tmp.push(item.name);
                    }
                    return tmp;
                },[])
            }
            return grades.length ? grades.join(', ') : "Toàn trường";
            
        }

        function fetchDiseases() {
            vm.loading = true;

            DiseaseService.getDiseaseOfSchool(0,vm.grade_selected).then(function (resp) {
                vm.diseases = resp.diseases;
                vm.admin_diseases = resp.admin_diseases;
                vm.is_admin = resp.is_admin;
                vm.unit_name = resp.user.name;
                vm.loaded = true;
                vm.loading = false;
            });
        }

        fetchDiseases();

        vm.showEditForm = function (disease) {
            var modalInstance = $uibModal.open({
                templateUrl: 'update_disease_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance, disease) {
                    var $ctrl = this;

                    $ctrl.disease = angular.copy(disease);
                    $ctrl.grades = _.map($CFG.grades, function (item) {
                        return {id: item.id, label: item.name};
                    });

                    $ctrl.diseaseOrigin = disease;

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.is_checked = function (grade_id) {
                        return disease.grade_ids.includes(grade_id.toString());
                    }

                    $ctrl.save = function () {
                        let grade_selected = [];
                        var ele_grades = $('input[name="grades_edit"]:checked');
                        _.forEach(ele_grades, v => {
                            grade_selected.push(v.value);
                        });
                        $ctrl.disease.grade_ids = grade_selected.join(',');
                        DiseaseService.updateById($ctrl.disease.id, _.pick($ctrl.disease, ['name', 'code', 'grade_ids']))
                            .then(function (successfully) {
                                if (successfully) {
                                    $ctrl.diseaseOrigin = angular.extend($ctrl.diseaseOrigin, $ctrl.disease);
                                    $uibModalInstance.close(true);
                                    fetchDiseases();
                                } else {
                                    alert('Có lỗi trong quá trình xử lý, thầy/cô vui lòng thực hiện lại sau');
                                    $uibModalInstance.close(true);
                                }
                            });
                    };
                },
                controllerAs: '$ctrl',
                resolve: {
                    disease: function () {
                        return disease;
                    },
                },
            });

            modalInstance.result.then(function (response) {
                // no action
            }, function () {
                // no action
            });
        };

        vm.showCreateForm = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'create_disease_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;

                    $ctrl.disease = {name: '', code: '', grade_ids: ''};

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.save = function () {
                        DiseaseService.addNew(_.pick($ctrl.disease, ['name', 'code', 'grade_ids']))
                            .then(function (successfully) {
                                if (successfully) {
                                    $uibModalInstance.close(true);
                                }
                            });
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                    fetchDiseases();
                }
            }, function () {
                // no action
            });
        };

        vm.showAddForm = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'add_disease_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance) {
                    var $ctrl = this;
                    $ctrl.admin_diseases = vm.admin_diseases;
                    $ctrl.diseases = [];
                    $ctrl.grades = _.map($CFG.grades, function (item) {
                        return {id: item.id, label: item.name};
                    });

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.save = function () {
                        let grade_selected = [];
                        var elements = $('input[name="disease"]:checked');
                        var ele_grades = $('input[name="grades"]:checked');
                        _.forEach(ele_grades, v => {
                            grade_selected.push(v.value);
                        });
                        _.forEach(elements, function(element){
                            var key = element.value;
                            var tmp = {
                                name: $ctrl.admin_diseases[key].name,
                                code: $ctrl.admin_diseases[key].code,
                                grade_ids: grade_selected.join(','),
                                origin_id: $ctrl.admin_diseases[key].id
                            };
                            $ctrl.diseases.push(tmp);
                        })
                        DiseaseService.add($ctrl.diseases)
                            .then(function (successfully) {
                                if (successfully) {
                                    $uibModalInstance.close(true);
                                    fetchDiseases();
                                }
                            });
                    };
                },
                controllerAs: '$ctrl',
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                    fetchDiseases();
                }
            }, function () {
                // no action
            });
        };

        vm.showConfirmDeleteModal = function (disease) {
            var modalInstance = $uibModal.open({
                templateUrl: 'delete_disease_modal_content.html',
                backdrop: 'static',
                controller: function ($uibModalInstance, disease) {
                    var $ctrl = this;

                    $ctrl.disease = disease;

                    $ctrl.close = function () {
                        $uibModalInstance.dismiss('cancel');
                    };

                    $ctrl.delete = function () {
                        DiseaseService.destroyById($ctrl.disease.id)
                            .then(function (successfully) {
                                if (successfully) {
                                    $uibModalInstance.close(true);
                                } else {
                                    alert('Có lỗi trong quá trình xử lý, thầy/cô vui lòng thực hiện lại sau');
                                    $uibModalInstance.close(true);
                                }
                            });
                    };
                },
                controllerAs: '$ctrl',
                resolve: {
                    disease: function () {
                        return disease;
                    },
                },
            });

            modalInstance.result.then(function (successfully) {
                if (successfully === true) {
                    fetchDiseases();
                }
            }, function () {
                // no action
            });
        };
    }

    SettingDiseaseController.$inject = [
        '$scope',
        '$uibModal',
        'DiseaseService',
    ];
})(window.angular_app);
