{"html": "<form class=\"form-horizontal\" id=\"frm-login\" role=\"form\" >\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm valid\">Tên nhà cung cấp</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"name\" width=\"100%\" ng-model=\"supplier.name\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm valid\">Số mã hóa</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"old_id\" width=\"100%\" ng-value=\"supplier.old_id\" type=\"hidden\">\n\t\t\t<input class=\"form-control\" id=\"id\" width=\"100%\" ng-model=\"supplier.id\" type-number=\"int\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\">Địa chỉ</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"address\" width=\"100%\" ng-model=\"supplier.address\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\">Tên chủ cửa hàng</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"boss\" width=\"100%\" ng-model=\"supplier.boss\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\">Điện thoại</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"phone\" width=\"100%\" ng-model=\"supplier.phone\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\">Chứng minh thư (CCCD)</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"cmt_cccd\" width=\"100%\" ng-model=\"supplier.cmt_cccd\">\n\t\t</div>\n\t</div>\n\t<div class=\"form-group\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\">Người giao hàng</label>\n\t\t<div class=\"col-md-6\">\n\t\t\t<input class=\"form-control\" id=\"shipper\" width=\"100%\" ng-model=\"supplier.shipper\">\n\t\t</div>\n\t</div>\n\t<div ng-if=\"$CFG.is_show_supplier_sign\" class=\"form-group\" style=\"border: 1px dotted #ccc; padding: 10px 0px 10px 0px;\">\n\t\t<label class=\"col-md-4 control-label control-label-norm\" for=\"files\">Ảnh chữ ký của Nhà cung cấp (nếu có)</label>\n\t\t<div class=\"col-md-6\" style=\"\">\n\t\t\t<input type=\"file\" id=\"supplier_sign_123456\" name=\"supplier_sign_123456\" accept=\"image/*\" class=\"fL\">\n\t\t\t<div class=\"images-preview p-b-5 fL mL10\">\n                            </div>\n\t\t</div>\n\t\t<label class=\"col-md-2\">\n\t\t\t<button class=\"btn btn-default btn-print\" onclick=\"saveSupplierSign('123456')\"><span class=\"glyphicon glyphicon-floppy-disk\"></span>Lưu</button>\n\t\t</label>\n\t</div>\n</form>\n\n<script type=\"text/javascript\">\n    /* Lưu thông tin vào bảng cpu course */\n    function saveSupplierSign(supplier_id){\n    \tvar fileInput = $('#supplier_sign_'+supplier_id)[0];\n        var file = fileInput.files[0];\n        if (file) {\n        \tvar formData = new FormData();\n        \tformData.append('user_id', supplier_id);\n        \tformData.append('key', 'supplier');\n            formData.append('file', file);\n        \t$.ajax({\n                url: $CFG.remote.base_url + '/doing/admin/user/saveUnitSign',\n                type: 'POST',\n                data: formData,\n                contentType: false,\n                processData: false,\n                success: function(response) {\n                    alert('Lưu chữ ký ảnh của nhà cung cấp thành công!')\n                },\n                error: function(jqXHR, textStatus, errorThrown) {\n                    // Log\n                }\n            });\n        }\n    }\n</script>\n", "row": {"id": "123456", "unit_id": "51461", "phone": "", "shipper": "", "boss": "<PERSON><PERSON><PERSON><PERSON>", "address": "Đường Tây Mỗ - <PERSON><PERSON>", "name": "<PERSON><PERSON>ng ty thực phẩm sanh", "cmt_cccd": "", "status": 1, "created_at": "2025-08-06 18:42:18", "updated_at": "2025-08-06 18:42:18", "created_user": "d.hd.mnquangvt", "updated_user": "d.hd.mnquangvt", "realtime_use": null}}