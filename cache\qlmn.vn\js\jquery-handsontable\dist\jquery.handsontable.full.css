/*!
 * Handsontable 0.11.2
 * Handsontable is a simple jQuery plugin for editable tables with basic copy-paste compatibility with Excel and Google Docs
 *
 * Copyright 2012-2014 <PERSON><PERSON>
 * Licensed under the MIT license.
 * http://handsontable.com/
 *
 * Date: Thu Sep 11 2014 23:41:54 GMT+0200 (CEST)
 */

.handsontable {
  position: relative;
}

.handsontable .relative {
  position: relative;
}

.handsontable.htAutoColumnSize {
  visibility: hidden;
  left: 0;
  position: absolute;
  top: 0;
}

.handsontable table,
.handsontable tbody,
.handsontable thead,
.handsontable td,
.handsontable th,
.handsontable div {
  box-sizing: content-box;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
}

.handsontable table.htCore {
  border-collapse: separate;
  /*it must be separate, otherwise there are offset miscalculations in WebKit: http://stackoverflow.com/questions/2655987/border-collapse-differences-in-ff-and-webkit*/
  position: relative;
  /*this actually only changes appearance of user selection - does not make text unselectable
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
  /*user-select: none; /*no browser supports unprefixed version*/
  border-spacing: 0;
  margin: 0;
  border-width: 0;
  table-layout: fixed;
  width: 0;
  outline-width: 0;
  /* reset bootstrap table style. for more info see: https://github.com/handsontable/jquery-handsontable/issues/224 */
  max-width: none;
  max-height: none;
}

.handsontable col {
  width: 50px;
}

.handsontable col.rowHeader {
  /* width: 50px; */
  width: 22px;
}

.handsontable th,
.handsontable td {
  border-right: 1px solid #CCC;
  border-bottom: 1px solid #CCC;
  height: 22px;
  empty-cells: show;
  line-height: 21px;
  padding: 0 4px 0 4px;
  /* top, bottom padding different than 0 is handled poorly by FF with HTML5 doctype */
  background-color: #FFF;
  vertical-align: top;
  overflow: hidden;
  outline-width: 0;
  white-space: pre-line;
  /* preserve new line character in cell */
}

.handsontable td.htInvalid {
  background-color: #ff4c42 !important; /*gives priority over td.area selection background*/
}

.handsontable td.htNoWrap {
  white-space: nowrap;
}

.handsontable th:last-child {
  /*Foundation framework fix*/
  border-right: 1px solid #CCC;
  border-bottom: 1px solid #CCC;
}

.handsontable tr:first-child th.htNoFrame,
.handsontable th:first-child.htNoFrame,
.handsontable th.htNoFrame {
  border-left-width: 0;
  background-color: white;
  border-color: #FFF;
}

.handsontable th:first-child,
.handsontable td:first-child,
.handsontable .htNoFrame + th,
.handsontable .htNoFrame + td {
  border-left: 1px solid #CCC;
}

.handsontable tr:first-child th,
.handsontable tr:first-child td {
  border-top: 1px solid #CCC;
}

.handsontable thead tr:last-child th {
  border-bottom-width: 0;
}

.handsontable thead tr.lastChild th {
  border-bottom-width: 0;
}

.handsontable th {
  background-color: #EBECED;
  color: #222;
  text-align: center;
  font-weight: normal;
  white-space: nowrap;
}

.handsontable thead th {
  padding: 0;
}

.handsontable th.active {
  background-color: #CCC;
}

.handsontable thead th .relative {
  padding: 2px 4px;
}

/* custom */

.handsontable .currentRow {
  background-color: #f3f48d;
}

.handsontable .currentCol {
  background-color: #F9F9FB;
}


/* plugins */

.handsontable .manualColumnMover {
  position: fixed;
  left: 0;
  top: 0;
  background-color: transparent;
  width: 5px;
  height: 25px;
  z-index: 999;
  cursor: move;
}

.handsontable .manualRowMover {
  position: fixed;
  left: -4px;
  top: 0;
  background-color: transparent;
  height: 5px;
  width: 50px;
  z-index: 999;
  cursor: move;
}

.handsontable .manualColumnMoverGuide,
.handsontable .manualRowMoverGuide {
  position: fixed;
  left: 0;
  top: 0;
  background-color: #CCC;
  width: 25px;
  height: 25px;
  opacity: 0.7;
  display: none;
}

.handsontable .manualColumnMoverGuide.active,
.handsontable .manualRowMoverGuide.active {
  display: block;
}

.handsontable .manualColumnMover:hover,
.handsontable .manualColumnMover.active,
.handsontable .manualRowMover:hover,
.handsontable .manualRowMover.active{
  background-color: #88F;
}

/* row + column resizer*/

.handsontable .manualColumnResizer {
  position: fixed;
  top: 0;
  cursor: col-resize;
  z-index: 110;
  width: 5px;
  height: 25px;
}

.handsontable .manualRowResizer {
  position: fixed;
  left: 0;
  cursor: row-resize;
  z-index: 110;
  height: 5px;
  width: 50px;
}

.handsontable .manualColumnResizer:hover,
.handsontable .manualColumnResizer.active,
.handsontable .manualRowResizer:hover,
.handsontable .manualRowResizer.active {
  background-color: #AAB;
}

.handsontable .manualColumnResizerGuide {
  position: fixed;
  right: 0;
  top: 0;
  background-color: #AAB;
  display: none;
  width: 0;
  border-right: 1px dashed #777;
  margin-left: 5px;
}

.handsontable .manualRowResizerGuide {
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: #AAB;
  display: none;
  height: 0;
  border-bottom: 1px dashed #777;
  margin-top: 5px;
}

.handsontable .manualColumnResizerGuide.active,
.handsontable .manualRowResizerGuide.active {
  display: block;
}

.handsontable .columnSorting:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* border line */

.handsontable .wtBorder {
  position: absolute;
  font-size: 0;
}
.handsontable .wtBorder.hidden{
  display:none !important;
}

.handsontable td.area {
  background-color: #EEF4FF;
}

/* fill handle */

.handsontable .wtBorder.corner {
  font-size: 0;
  cursor: crosshair;
}

.handsontable .htBorder.htFillBorder {
  background: red;
  width: 1px;
  height: 1px;
}

.handsontableInput {
  border: 2px solid #5292F7;
  outline-width: 0;
  margin: 0;
  padding: 1px 4px 0 2px;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  -webkit-box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.4);
  box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.4);
  resize: none;
  /*below are needed to overwrite stuff added by jQuery UI Bootstrap theme*/
  display: inline-block;
  color: #000;
  border-radius: 0;
  background-color: #FFF;
  box-sizing: content-box!important;
  /*overwrite styles potentionally made by a framework*/
}

.handsontableInputHolder {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}

.htSelectEditor {
  -webkit-appearance: menulist-button !important;
  position: absolute;
}

/*
TextRenderer readOnly cell
*/

.handsontable .htDimmed {
  color: #777;
}

.handsontable .htSubmenu :after{
    content: '▶';
    color: #777;
    position: absolute;
    right: 5px;
}


/*
TextRenderer horizontal alignment
*/
.handsontable .htLeft{
  text-align: left;
}
.handsontable .htCenter{
  text-align: center;
}
.handsontable .htRight{
  text-align: right;
}
.handsontable .htJustify{
  text-align: justify;
}
/*
TextRenderer vertical alignment
*/
.handsontable .htTop{
  vertical-align: top;
}
.handsontable .htMiddle{
  vertical-align: middle;
}
.handsontable .htBottom{
  vertical-align: bottom;
}

/*
TextRenderer placeholder value
*/

.handsontable .htPlaceholder {
  color: #999;
}

/*
AutocompleteRenderer down arrow
*/

.handsontable .htAutocompleteArrow {
  float: right;
  font-size: 10px;
  color: #EEE;
  cursor: default;
  width: 16px;
  text-align: center;
}

.handsontable td .htAutocompleteArrow:hover {
  color: #777;
}

/*
CheckboxRenderer
*/

.handsontable .htCheckboxRendererInput.noValue {
  opacity: 0.5;
}

/*
NumericRenderer
*/

.handsontable .htNumeric {
  text-align: right;
}

/*
Comment For Cell
*/
.htCommentCell{
  position: relative;
}
.htCommentCell:after{
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-left: 6px solid transparent;
  border-top: 6px solid red;
}

/**
 * Handsontable in Handsontable
 */

.handsontable .handsontable .wtHider {
  padding: 0 0 5px 0;
}

.handsontable .handsontable table {
  -webkit-box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.4);
  box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.4);
}

/**
* Autocomplete Editor
*/
.handsontable .autocompleteEditor.handsontable {
  padding-right: 17px;
}
.handsontable .autocompleteEditor.handsontable.htMacScroll {
  padding-right: 15px;
}


/**
 * Handsontable listbox theme
 */

.handsontable.listbox {
  margin: 0;
}

.handsontable.listbox .ht_master table {
  border: 1px solid #ccc;
  border-collapse: separate;
  background: white;
}

.handsontable.listbox th,
.handsontable.listbox tr:first-child th,
.handsontable.listbox tr:last-child th,
.handsontable.listbox tr:first-child td,
.handsontable.listbox td {
  border-width: 0;
}

.handsontable.listbox th,
.handsontable.listbox td {
  white-space: nowrap;
  text-overflow: ellipsis;
}

.handsontable.listbox td.htDimmed {
  cursor: default;
  color: inherit;
  font-style: inherit;
}

.handsontable.listbox .wtBorder {
  visibility: hidden;
}

.handsontable.listbox tr td.current,
.handsontable.listbox tr:hover td {
  background: #eee;
}

.htContextMenu {
  display: none;
  position: absolute;
  z-index: 1060; /*needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569)*/
}

.htContextMenu .ht_clone_top,
.htContextMenu .ht_clone_left,
.htContextMenu .ht_clone_corner {
  display: none;
}

.ht_clone_top {
  z-index: 101;
}

.ht_clone_left {
  z-index: 102;
}

.ht_clone_corner {
  z-index: 103;
}

.htContextMenu table.htCore {
  outline: 1px solid #bbb;
}

.htContextMenu .wtBorder {
  visibility: hidden;
}

.htContextMenu table tbody tr td {
  background: white;
  border-width: 0;
  padding: 4px 6px 0px 6px;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.htContextMenu table tbody tr td:first-child {
  border: 0;
}

.htContextMenu table tbody tr td.htDimmed{
  font-style: normal;
  color: #323232;
}

.htContextMenu table tbody tr td.current{
  background: rgb(233, 233, 233);
}

.htContextMenu table tbody tr td.htSeparator {
  border-top: 1px solid #bbb;
  height: 0;
  padding: 0;
}

.htContextMenu table tbody tr td.htDisabled {
  color: #999;
}

.htContextMenu table tbody tr td.htDisabled:hover {
  background: white;
  color: #999;
  cursor: default;
}
.htContextMenu table tbody tr td div{
  padding-left: 10px;
}
.htContextMenu table tbody tr td div span.selected{
  margin-top: -2px;
  position: absolute;
  left: 4px;
}

.handsontable td.htSearchResult {
  background: #fcedd9;
  color: #583707;
}

/*
Cell borders
*/
.htBordered{
  /*box-sizing: border-box !important;*/
  border-width: 1px;
}
.htBordered.htTopBorderSolid{
  border-top-style: solid;
  border-top-color: #000;
}
.htBordered.htRightBorderSolid{
  border-right-style: solid;
  border-right-color: #000;
}
.htBordered.htBottomBorderSolid{
  border-bottom-style: solid;
  border-bottom-color: #000;
}
.htBordered.htLeftBorderSolid{
  border-left-style: solid;
  border-left-color: #000;
}

.htCommentTextArea{
  background-color: #FFFACD;
  box-shadow: 1px 1px 2px #bbb;
  font-family: 'Arial';
  -webkit-box-shadow: 1px 1px 2px #bbb;
  -moz-box-shadow: 1px 1px 2px #bbb;

}

/*WalkontableDebugOverlay*/

.wtDebugHidden {
  display: none;
}

.wtDebugVisible {
  display: block;
  -webkit-animation-duration: 0.5s;
  -webkit-animation-name: wtFadeInFromNone;
  animation-duration: 0.5s;
  animation-name: wtFadeInFromNone;
}

@keyframes wtFadeInFromNone {
  0% {
    display: none;
    opacity: 0;
  }

  1% {
    display: block;
    opacity: 0;
  }

  100% {
    display: block;
    opacity: 1;
  }
}

@-webkit-keyframes wtFadeInFromNone {
  0% {
    display: none;
    opacity: 0;
  }

  1% {
    display: block;
    opacity: 0;
  }

  100% {
    display: block;
    opacity: 1;
  }
}
