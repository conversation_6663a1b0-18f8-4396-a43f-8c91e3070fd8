(function CourseService(app, $CFG) {
    'use strict';

    app.service('CourseService', ['$q', courseService]);

    function courseService($q) {
        function fetchCourses() {
            // TODO: can we move the logic get list course here???
            return $q.when(_.clone($CFG.grades));
        }

        return {
            fetchCourses: fetchCourses,
        };
    }
})(window.angular_app, window.$CFG);
