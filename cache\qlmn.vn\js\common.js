
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
    }
});

$(document).ready(function() {
    
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        }
    });
    let chukyanh_width = 0;
    const storedData = localStorage.getItem('chukyanh_width');

    if (storedData) {
        chukyanh_width = JSON.parse(storedData);
    } else {
        chukyanh_width = 150;
        localStorage.setItem('chukyanh_width', chukyanh_width);
    }

    $('#chukyanh_width').val(chukyanh_width);
    $('.chukyanh_sign').css('width', chukyanh_width + 'px');

    $('#chukyanh_width').on('change', function() {
        let value = $(this).val();
        $('.chukyanh_sign').css('width', value + 'px');
        localStorage.setItem('chukyanh_width', value);
    });
});

function downloadExcelFile(url, formData) {
    $.ajax({
        url: url,
        type: 'POST',
        xhrFields: {
            responseType: 'blob'
        },
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function(response, status, xhr) {
            var filename = ""; 
            var disposition = xhr.getResponseHeader('Content-Disposition');
            
            if (disposition && disposition.indexOf('attachment') !== -1) {
                var regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                var matches = regex.exec(disposition);
                if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
            }
            
            var blob = new Blob([response], { type: 'application/vnd.ms-excel' });
            
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
                window.navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement('a');
                var url = window.URL.createObjectURL(blob);
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                
                setTimeout(function() {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }, 100);
            }
        }, 
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus + ': ' + errorThrown);
        },
    });
}

$(function () {
    initStatusLoading();
    $('#statusloading-mash').find('#spinner-container').click(function () {
        $('#statusloading-mash').hide();
    })
    if(typeof Object.values != 'function') {
        Object.values = function (obj) {
            var rs = [];
            if(typeof obj === 'object'){
                for (var i in obj) {
                    rs.push(obj[i]);
                }
            }
            return rs;
        }
    }
});
(function (angular) {
    if (typeof angular != 'undefined') {
        angular.fromJson = function (str) {
            if (str && str.charAt(0) === '"') {
                return JSON.parse(str);
            }
            return str;
        }
    }
})(window.angular);

/*Bắt sự kiện đóng cửa sổ trình duyệt hoặc tải lại trang (F5)*/
var eventCloseWindows = {
    title: '',
    enable: false
};
$(window).on("beforeunload", function () {
    if (eventCloseWindows.enable) {
        return eventCloseWindows.enable ? eventCloseWindows.title : null;
    }
});
/*Kết thúc bắt sự kiện đóng cửa sổ trình duyệt*/
var BootstrapDialog_cache = {};
var statusloadingCache = {};
var refress_request = 1;
var screen_size = {
    width: $(window).width(),
    height: $(window).height()
};
var size = {
    'normal': BootstrapDialog.SIZE_NORMAL,
    'small': BootstrapDialog.SIZE_SMALL,
    'wide': BootstrapDialog.SIZE_WIDE,
    'large': BootstrapDialog.SIZE_LARGE
};
rice_ids = [
    588,589,1250,6011,6241,6363,6370,6587,6588,6731,6762,6793,6801,8260,8266,
    8271,8376,8394,8417,8419,8452,8453,8473,8491,18526,48645,
];
var keyString = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
$.selectSchoolYear = function (schoolyear, project) {
    process($CFG.remote.base_url + '/doing/admin/user/set_schoolyear', {
        namhoc: schoolyear,
        project: project
    }, function (resp) {
        if (resp.result == 'success') {
            location.reload();
        }
    });
}
$.selectSchoolPoint = function (school_point, project) {
    process($CFG.remote.base_url + '/doing/admin/user/set_schoolyear', {
        school_point: school_point,
        project: project
    }, function (resp) {
        if (resp.result == 'success') {
            location.reload();
        }
    });
}
$.timer = function (callback_realtime, m, callback_finish) {
    if (m <= 0) {
        if (typeof callback_finish == 'function') {
            callback_finish();
        }
        return;
    }
    setTimeout(function () {
        m -= 1;
        callback_realtime(m);
        $.timer(callback_realtime, m, callback_finish);
    }, 1000);

}
$.dm_datagrid = {
    init: function (url, module, title, columns, fixOption) { /* init */
        fixOption || (fixOption = {});
        fixOption.width || (fixOption.width = 500);
        fixOption.height || (fixOption.height = 300);
        // fixOption.onDblClickRow || (fixOption.onDblClickRow = 'showEditForm');
        fixOption.idField || (fixOption.idField = 'id');
        fixOption.pageSize || (fixOption.pageSize = 30);
        fixOption.checkOnSelect || (fixOption.checkOnSelect = true);
        fixOption.selectOnCheck || (fixOption.selectOnCheck = true);
        fixOption.ctrlSelect || (fixOption.ctrlSelect = true);
        fixOption.singleSelect || (fixOption.singleSelect = false);
        fixOption.nowrap || (fixOption.nowrap = false);
        fixOption.enableFilter || (fixOption.enableFilter = false);

        if (fixOption.checkOnSelect == 'false' || fixOption.checkOnSelect == false) {
            fixOption.checkOnSelect = false;
        } else {
            fixOption.checkOnSelect = true;
        }
        if (fixOption.selectOnCheck == 'false' || fixOption.selectOnCheck == false) {
            fixOption.selectOnCheck = false;
        } else {
            fixOption.selectOnCheck = true;
        }
        if (fixOption.ctrlSelect == 'false' || fixOption.ctrlSelect == false) {
            fixOption.ctrlSelect = false;
        } else {
            fixOption.ctrlSelect = true;
        }
        if (fixOption.pagination == 'false') {
            fixOption.pagination = false;
        }
        if (fixOption.pagination != false) {
            fixOption.pagination = true;
        }
        if (!jQuery.isArray(fixOption.pageList)) {
            fixOption.pageList = [30, 70, 100, 200];
        }
        module || (module = 'view');
        var exten = '_detail';
        if (url.split('?').length == 1) {
            url += '?';
        }else{
            url += '&';
        }
        var dataoption = {
            title: title,
            type: 'post',
            url: url + "_=" + refress_request,
            width: fixOption.width,
            height: fixOption.height,
            async: false,
            fit: true,
            fitColumns: true,
            idField: fixOption.idField,
            nowrap: fixOption.nowrap,
            filterDelay: 1200,
            autoRowHeight: true,
            remoteFilter: true,
            rownumbers: true,
            pageSize: fixOption.pageSize,
            pageList: fixOption.pageList,
            pagination: fixOption.pagination,
            ctrlSelect: fixOption.ctrlSelect,
            checkOnSelect: fixOption.checkOnSelect,
            selectOnCheck: fixOption.selectOnCheck,
            toolbar: '#tb_' + module + exten,
            columns: columns,
            border: false,
            onLoadSuccess: function (data) {
                $('#tbl_' + module).datagrid('unselectAll');
                if (typeof fixOption.onLoadSuccess === 'function') {
                    fixOption.onLoadSuccess(data);
                }
            },
            onBeforeLoad: function (data) {
                $('#tbl_' + module).datagrid('unselectAll');
            }
        };
        if (typeof fixOption.remoteFilter != 'undefined') {
            dataoption.remoteFilter = fixOption.remoteFilter;
        }
        if (typeof fixOption.onDblClickRow === 'function') {
            dataoption.onDblClickRow = fixOption.onDblClickRow;
        }
        $.each(fixOption, function (index, value) {
            dataoption[index] = value;
        });
        var selector = $('#tbl_' + module);
        selector.datagrid(dataoption);
        if (fixOption.enableFilter !== false) {
            selector.datagrid('enableFilter', fixOption.enableFilter);
        }
    },
    showAddForm: function ($option, callback) {
        var dialog_key = count(BootstrapDialog_cache) + Math.random();
        var op = {
            title: ($option.title || 'Mẫu nhập liệu'),
            size: ($option.size || size.normal),
            message: $('<div style="margin:20px; width:100%;height:50px;"></div>').append(getLoadingHTML())
        };
        if (typeof $option == 'number') {
            op.size = size.wide;
        }
        if ($option.showButton === 'false' || $option.showButton === false) {
            $option.showButton = false;
        } else {
            $option.showButton = true;
        }
        if ($option.fullScreen === 'true' || $option.fullScreen === true) {
            $option.fullScreen = true;
        } else {
            $option.fullScreen = false;
        }
        if ($option.draggable === 'true' || $option.draggable === true) {
            $option.draggable = true;
        } else {
            $option.draggable = false;
        }
        if ($option.noteWarning === 'true' || $option.noteWarning === true || $option.noteWarning === undefined) {
            $option.noteWarning = true;
        } else {
            $option.noteWarning = false;
        }
        var onShown = '';
        if (typeof $option.onshown === 'function') {
            onShown = $option.onshown;
        }
        if (typeof $option.onShown === 'function') {
            onShown = $option.onShown;
        }
        var onShow = '';
        if (typeof $option.onshow === 'function') {
            onShow = $option.onshow;
        }
        if (typeof $option.onShow === 'function') {
            onShow = $option.onShow;
        }
        $option.titleAskBeforeClose = 'Những thay đổi có thể chưa được lưu.';
        if ($option.askBeforeClose != undefined && $option.askBeforeClose != '' && $option.askBeforeClose != 'false' && $option.askBeforeClose != false) {
            if ($option.askBeforeClose == 'true') {
                $option.askBeforeClose = true;
            }
            if (typeof $option.askBeforeClose === 'string') {
                $option.titleAskBeforeClose = $option.askBeforeClose;
                $option.askBeforeClose = true;
            }
            eventCloseWindows.title = $option.titleAskBeforeClose;
            eventCloseWindows.enable = true;
        }
        if ($option.showButton && !$option.buttons) {
            op['buttons'] = [
                {
                    id: 'btn-save',
                    // icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Lưu',
                    cssClass: 'btn btn-success btn-save-grade-admin',
                    action: function (dialogRef) {
                        var data = {};
                        var valid = true;
                        var form_value = getSubmitForm(dialogRef.getModalBody(), true);
                        // console.log(arrayToJson(form_value));
                        // return;
                        if ($option.cb != undefined && typeof ($option.cb) == "function") {
                            valid = $option.cb(form_value);
                        }
                        if (!valid) {
                            return valid;
                        }
                        if (op['data']) {
                            data = op['data'];
                        } else {
                            data = {data: arrayToJson(form_value)};
                        }
                        data.async = true;
                        var url = $CFG.remote.base_url + '/doing/' + $option.module + '/' + $option.action;
                        process(url, data, function (resp) {
                            if (resp.result == "success") {
                                dialogRef.close();
                                if (typeof callback === 'function') {
                                    callback(resp);
                                }
                            }
                        });
                    }
                }, {
                    label: 'Bỏ qua',
                    // icon: 'glyphicon glyphicon-log-out',
                    cssClass: 'btn btn-inverse btn-cancel-grade-admin',
                    action: function (dialog) {
                        dialog.close();
                    }
                }
            ];
            op['closable'] = false;
        } else {
            if ($option.closable == undefined || $option.closable == true || $option.closable == 'true') {
                op['closable'] = true;
            } else {
                op['closable'] = false;
            }
            if ($option.buttons) {
                op.buttons = $option.buttons;
            }
        }
        op.draggable = $option.draggable;
        op['onshown'] = function (dialogRef) {
            dialogRef.askBeforeClose = $option.askBeforeClose;
            BootstrapDialog_cache[dialog_key] = dialogRef;
            if (typeof $option.content === 'function') {
                $option.content(dialogRef.getModalBody(), dialogRef);
            } else if (typeof $option.content == 'string') {
                if ($option.scope != undefined) {
                    dialogRef.scope = $option.scope;
                    dialogRef.url = $option.content;
                    $option.scope.getTemplate($option.content, function (template) {
                        setTimeout(function () {
                            $option.scope.$apply(function () {
                                dialogRef.getModalBody().html($option.scope.compile(template, $option.scope));
                            });
                        });
                    });
                } else {
                    op.message.html($option.content);
                }
            } else {
            }
            setDialog2fullscreen(dialogRef, $option);
            if (typeof onShown == 'function') {
                onShown(dialogRef, dialogRef.getModalBody());
            }
            if (typeof $option.size == 'number') {
                dialogRef.$modalContent.css({width: $option.size, margin: 'auto'});
            }
        };
        op['onshow'] = function (dialogRef) {
            if (typeof onShow == 'function') {
                onShow(dialogRef, dialogRef.getModalBody());
            }
        };
        op['onhide'] = function (dialog) {
            if (dialog.askBeforeClose === true) {
                $.messager.confirm('Đóng cửa sổ chức năng?', '<div style = "font-size: 14px">' + $option.titleAskBeforeClose + '</div>', function (r) {
                    if (r) {
                        dialog.askBeforeClose = false;
                        dialog.close();
                        eventCloseWindows.title = '';
                        eventCloseWindows.enable = false;
                    }
                });
                return false;
            } else {
                if (typeof $option.cancel === 'function') {
                    $option.cancel();
                }
                delete BootstrapDialog_cache[dialog_key];
            }
        };
        if (op['buttons'] && $option.noteWarning) {
            if (op['buttons'].length > 0) {
                op['buttons'].push({
                    id: '',
                    icon: '',
                    label: 'Chú ý: Những mục nhập có tiêu đề màu cam là bắt buộc.',
                    cssClass: 'btn-link pull-left valid-red valid-i'
                });
            }
        }
        setTimeout(()=>{
            BootstrapDialog.show(op);
        });
    },
    showConfirmForm: function ($option, callback) {
        var dialog_key = count(BootstrapDialog_cache) + Math.random();
        var op = {
            title: ($option.title || 'Mẫu nhập liệu'),
            size: ($option.size || size.normal),
            message: $('<div style="margin:20px; width:100%;height:50px;"></div>').append(getLoadingHTML())
        };
        /*if (typeof $option == 'number') {
            op.size = size.wide;
        }*/
        if ($option.showButton === 'false' || $option.showButton === false) {
            $option.showButton = false;
        } else {
            $option.showButton = true;
        }
        /*if ($option.fullScreen === 'true' || $option.fullScreen === true) {
            $option.fullScreen = true;
        } else {
            $option.fullScreen = false;
        }
        if ($option.draggable === 'true' || $option.draggable === true) {
            $option.draggable = true;
        } else {
            $option.draggable = false;
        }
        if ($option.noteWarning === 'true' || $option.noteWarning === true || $option.noteWarning === undefined) {
            $option.noteWarning = true;
        } else {
            $option.noteWarning = false;
        }
        var onShown = '';
        if (typeof $option.onshown === 'function') {
            onShown = $option.onshown;
        }
        if (typeof $option.onShown === 'function') {
            onShown = $option.onShown;
        }
        var onShow = '';
        if (typeof $option.onshow === 'function') {
            onShow = $option.onshow;
        }
        if (typeof $option.onShow === 'function') {
            onShow = $option.onShow;
        }*/
        /*$option.titleAskBeforeClose = 'Những thay đổi có thể chưa được lưu.';
        if ($option.askBeforeClose != undefined && $option.askBeforeClose != '' && $option.askBeforeClose != 'false' && $option.askBeforeClose != false) {
            if ($option.askBeforeClose == 'true') {
                $option.askBeforeClose = true;
            }
            if (typeof $option.askBeforeClose === 'string') {
                $option.titleAskBeforeClose = $option.askBeforeClose;
                $option.askBeforeClose = true;
            }
            eventCloseWindows.title = $option.titleAskBeforeClose;
            eventCloseWindows.enable = true;
        }*/
        if ($option.showButton && !$option.buttons) {
            op['buttons'] = [
                {
                    id: 'btn-save',
                    // icon: 'glyphicon glyphicon-floppy-disk',
                    label: 'Đồng ý',
                    cssClass: 'btn btn-success btn-save-grade-admin',
                    action: function (dialogRef) {
                        var data = {};
                        var valid = true;
                        var form_value = getSubmitForm(dialogRef.getModalBody(), true);
                        if ($option.cb != undefined && typeof ($option.cb) == "function") {
                            valid = $option.cb(form_value);
                        }
                        if (!valid) {
                            return valid;
                        }
                        if (op['data']) {
                            data = op['data'];
                        } else {
                            data = {data: arrayToJson(form_value)};
                        }
                        data.async = true;
                        var url = $CFG.remote.base_url + '/doing/' + $option.module + '/' + $option.action;
                        process(url, data, function (resp) {
                            if (resp.result == "success") {
                                dialogRef.close();
                                if (typeof callback === 'function') {
                                    callback(resp);
                                }
                            }
                        });
                    }
                }, {
                    label: 'Bỏ qua',
                    // icon: 'glyphicon glyphicon-log-out',
                    cssClass: 'btn btn-inverse btn-cancel-grade-admin',
                    action: function (dialog) {
                        dialog.close();
                    }
                }
            ];
            op['closable'] = false;
        } else {
            if ($option.closable == undefined || $option.closable == true || $option.closable == 'true') {
                op['closable'] = true;
            } else {
                op['closable'] = false;
            }
            if ($option.buttons) {
                op.buttons = $option.buttons;
            }
        }
        op.draggable = $option.draggable;
        op['onshown'] = function (dialogRef) {
            dialogRef.askBeforeClose = $option.askBeforeClose;
            BootstrapDialog_cache[dialog_key] = dialogRef;
            if (typeof $option.content === 'function') {
                $option.content(dialogRef.getModalBody(), dialogRef);
            } else if (typeof $option.content == 'string') {
                if ($option.scope != undefined) {
                    dialogRef.scope = $option.scope;
                    dialogRef.url = $option.content;
                    $option.scope.getTemplate($option.content, function (template) {
                        setTimeout(function () {
                            $option.scope.$apply(function () {
                                dialogRef.getModalBody().html($option.scope.compile(template, $option.scope));
                            });
                        });
                    });
                } else {
                    op.message.html($option.content);
                }
            } else {
            }
            setDialog2fullscreen(dialogRef, $option);
            if (typeof onShown == 'function') {
                onShown(dialogRef, dialogRef.getModalBody());
            }
            if (typeof $option.size == 'number') {
                dialogRef.$modalContent.css({width: $option.size, margin: 'auto'});
            }
        };
        /*op['onshow'] = function (dialogRef) {
            if (typeof onShow == 'function') {
                onShow(dialogRef, dialogRef.getModalBody());
            }
        };
        op['onhide'] = function (dialog) {
            if (dialog.askBeforeClose === true) {
                $.messager.confirm('Đóng cửa sổ chức năng?', '<div style = "font-size: 14px">' + $option.titleAskBeforeClose + '</div>', function (r) {
                    if (r) {
                        dialog.askBeforeClose = false;
                        dialog.close();
                        eventCloseWindows.title = '';
                        eventCloseWindows.enable = false;
                    }
                });
                return false;
            } else {
                if (typeof $option.cancel === 'function') {
                    $option.cancel();
                }
                delete BootstrapDialog_cache[dialog_key];
            }
        };
        if (op['buttons'] && $option.noteWarning) {
            if (op['buttons'].length > 0) {
                op['buttons'].push({
                    id: '',
                    icon: '',
                    label: 'Chú ý: Những mục nhập có tiêu đề màu cam là bắt buộc.',
                    cssClass: 'btn-link pull-left valid-red valid-i'
                });
            }
        }*/
        setTimeout(()=>{
            BootstrapDialog.show(op);
        });
    },
    showEditForm: function ($option, callback) {
        this.showAddForm($option, callback);
    },
    del: function (controller, ids, callback, data_extend) {
        if (controller.split('del').length == 1) {
            controller += '/del';
        }
        if (typeof ids === 'array') {
            ids = ids.join(',');
        }
        var data = {ids: ids, async: true};
        if (typeof data_extend == 'object') {
            jQuery.extend(data, data_extend);
        }
        process(controller, data, function (resp) {
            if (resp.result == 'success') {
                if (typeof callback === 'function') {
                    callback(resp);
                }
            }
        })
    },
    combobox: function (id, data, options) {
        new comboboxInit(id, data, options);
    },
    doSearch: function (datagridID, ids, filter_type) {
        if (typeof ids != 'object' || !datagridID) {
            return;
        }
        if (datagridID.split('#').length == 1 && datagridID.split('.').length == 1) {
            datagridID = '#' + datagridID;
        }
        var queryParams = $(datagridID).datagrid('options').queryParams;
        filter_type || (filter_type = 'and');
        var filterRules = [];
        $.each(ids, function (id, op) {
            if (typeof op != 'string') {
                if (op.value != 'clear') {
                    filterRules.push({field: id, op: op.op, value: op.value});
                }
            } else if (op != 'clear') {
                var input = $('#' + id);
                if (input.attr('type') == 'checkbox') {
                    input = $('#' + id + ':checked');
                }
                if (input.length) {
                    if (input.attr('field')) {
                        id = input.attr('field');
                    }
                    op || (op = 'equal');
                    var value = input.val();
                    if (value + '' != '') {
                        filterRules.push({field: id, op: op, value: value});
                    }
                }
            }
        });
        queryParams.filterRules = filterRules;
        queryParams.filter_type = filter_type;
        setTimeout(function () {
            $(datagridID).datagrid('load', queryParams);
        }, 0);
    },
    show: function (options, callback) {
        var dialog_key = count(BootstrapDialog_cache) + Math.random();
        options.message = '<div style="padding: 10px;">' + options.message + '</div>';
        options.onshown = function (dialogRef) {
            BootstrapDialog_cache[dialog_key] = dialogRef;
            if (typeof callback === 'function') {
                callback();
            }
        };
        options.onhide = function () {
            delete BootstrapDialog_cache[dialog_key];
        };
        setTimeout(()=>{
            BootstrapDialog.show(options);
        });
    }
};
$.staticFile = {
    styleSheets: {
        'border': {ignore: []},
        'border-top': {ignore: []},
        'border-left': {ignore: []},
        'border-right': {ignore: []},
        'border-bottom': {ignore: []},
        'padding': {ignore: []},
        'padding-top': {ignore: []},
        'padding-left': {ignore: []},
        'padding-right': {ignore: []},
        'padding-bottom': {ignore: []},
        'margin': {ignore: []},
        'margin-top': {ignore: []},
        'margin-left': {ignore: []},
        'margin-right': {ignore: []},
        'margin-bottom': {ignore: []},
        //'width': {ignore: []},
        'background': {ignore: ['none 0% 0% / auto repeat scroll padding-box border-box rgba(0, 0, 0, 0)']},
        'border-radius': {ignore: []},
        'border-collapse': {ignore: []},
        'border-spacing': {ignore: []},
        'font': {ignore: ['700 14px / 20px "Open Sans", sans-serif']},
        'font-size': {ignore: []},
        'font-family': {ignore: []},
    },
    styleSheetExtent: {
        'color': '',
        'style': '',
        'width': '',
    },
    px2pt: [
        'width', 'height'
    ],
    size2Pt: (val)=>{
        var vals = val.split(' ');
        vals = vals.map(v => {
            if(v.split('px').length == 2){
                v = ((+(v.replace('px', '')))*3/4)+'pt';
            }
            if(v.split('em').length == 2){

            }
            return v;
        });
        val = vals.join(' ');
        return val;
    },
    bindStyleInline: (id) => {
        var contain = $('#'+id);
        if(!contain.hasClass('bind-style')) {
            contain.find('*').not('table *').each((ind, el) => {
                var style_el = $(el).attr('style');
                var css = [];
                var styleSheets = clone($.staticFile.styleSheets);
                if(style_el){
                    for(var val of style_el.split(';')){
                        if(val){
                            val = val.split(':');
                            var style = val[0];
                            var value = val[1].replace(/^\s|\s$/g, '');
                            style = style.split(' ').join('');
                            if($.staticFile.px2pt.includes(style) && value.split('px')){
                                value = $.staticFile.size2Pt(value);
                            }
                            css.push(style + ':' + value);
                            delete styleSheets[style];
                        }
                    }
                }
                $.each(styleSheets, (style, valid) => {
                    if(!valid.ignore.includes($(el).css(style))){
                        var values = $.staticFile.css(el, style);
                        $.each(values, (key, value)=>{
                            if($.staticFile.px2pt.includes(style)){
                                value = $.staticFile.size2Pt(value);
                            }
                            css = [...[key +':'+ value], ...css];
                        });
                    }
                });
                $(el).attr('style', css.join(';'));
            });
            contain.find('table').each((ind0, table)=>{
                $(table).find('th, td').each((ind, el) => {
                    var el_style = $(el).attr('style');
                    var css = [];
                    var styles = clone($.staticFile.styleSheets);
                    if(el_style){
                        for(var val of el_style.split(';')){
                            if(val){
                                val = val.split(':');
                                var style = val[0];
                                var value = val[1].replace(/^\s|\s$/g, '');
                                style = style.split(' ').join('');
                                if($.staticFile.px2pt.includes(style)){
                                    value = $.staticFile.size2Pt(value);
                                }
                                css.push(style + ':' + value);
                                delete styles[style];
                            }
                        }
                    }
                    $.each(styles, (style, valid) => {
                        if(!valid.ignore.includes($(el).css(style))){
                            // var value = $(el).css(style);
                            var values = $.staticFile.css(el, style);
                            // console.log(el, values);
                            $.each(values, (key, value)=>{
                                if($.staticFile.px2pt.includes(style)){
                                    value = $.staticFile.size2Pt(value);
                                }
                                css = [...[key +':'+ value], ...css];
                            });
                            // console.log(el, style, window.getComputedStyle(el, style));

                        }
                    });
                    $(el).attr('style', css.join(';'));
                });
            });
            contain.addClass('bind-style');
        }
    },
    parseLink: (links, callback)=>{
        if(links.length > 0){
            var dem = 0;
            var css = '';
            for(var link of links) {
                $.when($.get(link))
                    .done(function(response) {
                        css += response;
                        dem ++;
                        if(dem == links.length && typeof callback === 'function'){
                            callback(css);
                        }
                    });
            }
        } else if(typeof callback === 'function') {
            callback('');
        }
    },
    css: (el, name) => {
        var value = {};
        if(!$(el).css(name)){
            var styles = window.getComputedStyle(el);
            // console.log(1111, styles)
            $.each($.staticFile.styleSheetExtent, (key, val)=>{
                key = name+'-'+key;
                if(styles[key]){
                    value[key] = styles[key];
                }
            });
        } else {
            value[name] = $(el).css(name);
        }
        return value;
    },
    getLinks: (el) => {
        var links = [];
        el.find('link').each((ind, link)=>{
            links.push($(link).attr('href'))
        });
        return links;
    },
    printForm: (id)=>{
        var el_report = $("#"+id);
        var el_parent = $.staticFile.findParent(el_report);
        var links = $.staticFile.getLinks(el_parent);
        $.staticFile.parseLink(links, (css)=> {
            $.staticFile.bindStyleInline(id);
            el_parent.find('style').each((ind, el_style) => {
                css += $(el_style).html();
            });
            var contents = el_report.html();
            // $('div').html(contents).find('.print-hidden').remove();
            var frame1 = $('<iframe />');
            frame1[0].name = "frame1";
            frame1.css({ "position": "absolute", "top": "-1000000px" });
            $("body").append(frame1);
            var frameDoc = frame1[0].contentWindow ? frame1[0].contentWindow : frame1[0].contentDocument.document ? frame1[0].contentDocument.document : frame1[0].contentDocument;
            // var frameDoc = window.open('', '_blank', 'width=300,height=300');
            frameDoc.document.open();
            //Create a new HTML document.
            frameDoc.document.write('<html><head><title>DIV Contents</title>');
            frameDoc.document.write('</head><body>');
            //Append the external CSS file.
            $.each(links, (ind, link)=>{
                frameDoc.document.write('<link href="'+link+'" rel="stylesheet" type="text/css" />');
            });
            //Append the CSS in style tag.
            el_parent.find('style').each((ind, el_style)=>{
                css += $(el_style).html();
            });
            frameDoc.document.write('<style>'+css+'</style>');
            //Append the DIV contents.
            frameDoc.document.write(contents);
            frameDoc.document.write('</body></html>');
            frameDoc.document.close();
            setTimeout(function () {
                window.frames["frame1"].focus();
                window.frames["frame1"].print();
                frame1.remove();
            }, 500);
        });
    },
    HTML2Doc: (id) => {
        var el_report = $('#'+id);
        var el_parent = $.staticFile.findParent(el_report);
        var links = $.staticFile.getLinks(el_parent);
        $.staticFile.parseLink(links, (css)=>{
            $.staticFile.bindStyleInline(id);
            el_parent.find('style').each((ind, el_style)=>{
                css += $(el_style).html();
            });
            css = "<style>" + css +
                "\n<!--"+
                "@page WordSection1\n" +
                "{size:595.35pt 841.95pt;\n" +
                "margin:1.0in 1.0in 1.0in 1.0in;\n" +
                "mso-header-margin:.5in;\n" +
                "mso-footer-margin:.5in;\n" +
                "mso-paper-source:0;}\n" +
                "div.WordSection1 {\n" +
                "page:WordSection1;\n" +
                "} -->\n</style>";
            var header = "<html xmlns:o='urn:schemas-microsoft-com:office:office' "+
                "xmlns:w='urn:schemas-microsoft-com:office:word' "+
                "xmlns='http://www.w3.org/TR/REC-html40'>"+
                "<head>" +
                "<meta charset='utf-8'><title>Export HTML to Word Document with JavaScript</title>" +
                css +
                '</head><body><div class="WordSection1">';
            var footer = "</div></body></html>";
            var container = $('#'+id);
            container.find('input, textarea').each((ind, el)=>{
                $(el).attr('data-index', ind);
            });
            var innerHTML = container.html();
            var contain = $('<div>');
            contain.append(innerHTML);
            contain.find('input, textarea').each((ind, el)=>{
                $(el).after(container.find('*[data-index='+$(el).attr('data-index')+']').val().split("\n").join('<br/>')).remove();
            });
            // contain.find('.print-hidden').remove();
            var sourceHTML = header+contain.html()+footer;
            var source = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(sourceHTML);
            var fileDownload = document.createElement("a");
            document.body.appendChild(fileDownload);
            fileDownload.href = source;
            fileDownload.download = 'document.doc';
            fileDownload.click();
            document.body.removeChild(fileDownload);
        });
    },
    findParent: (el, parent_class)=>{
        parent_class || (parent_class = 'main-dialog-content');
        if(el.hasClass(parent_class) || el[0].localName == 'body') {
            return el;
        } else {
            return $.staticFile.findParent(el.parent(), parent_class);
        }
    }
};
function rand(min, max) {
    var $ran = (Math.random() + '').split('.')[1];
    var num = $ran;
    if (min && max) {
        if (max < 10) {
            num = Number(num[0]);
        } else {
            var tmp = ''
            for (var i = 0; i < (max + '').length; i++) {
                tmp += num[i];
            }
            num = Number(tmp);
        }
        if (num > max) {
            num = max;
        } else if (num <= min) {
            num = min;
        }
    }
    return num;
}

function dialogCloseAll(callback, type) {
    for (var i in BootstrapDialog_cache) {
        if (BootstrapDialog_cache[i]) {
            if (!type) {
                BootstrapDialog_cache[i].askBeforeClose = false;
            }
            BootstrapDialog_cache[i].close();
        }
    }
    BootstrapDialog_cache = {};
    if (typeof callback === 'function') {
        callback();
    }
}

function dialogClose(callback, type) {
    var id = undefined;
    for (var i in BootstrapDialog_cache) {
        id = i;
    }
    if (!type) {
        BootstrapDialog_cache[id].askBeforeClose = false;
    }
    BootstrapDialog_cache[id].close();
    delete BootstrapDialog_cache[id];
    if (typeof callback === 'function') {
        callback();
    }
}

function dialogRefresh() {
    var dialog = undefined;
    var id = undefined;
    for (var i in BootstrapDialog_cache) {
        id = i;
    }
    if (BootstrapDialog_cache[id]) {
        if (BootstrapDialog_cache[id].btnReload) {
            // console.log(BootstrapDialog_cache[id].btnReload);
            $(BootstrapDialog_cache[id].btnReload).click();
        }
    }
}

function comboboxInit(id, data, options) {
    options || (options = {});
    options.valueField || (options.valueField = 'id');
    options.textField || (options.textField = 'text');
    options.width || (options.width = 200);
    options.height || (options.height = 34);
    options.delay || (options.delay = 500);
    options.placeholder || (options.placeholder = '');
    options.queryParams || (options.queryParams = {});
    options.searchFields || (options.searchFields = options.textField);
    if (typeof id != 'object') {
        if (id.split('#').length == 1) {
            id = 'input#' + id;
        }
        id = $(id);
    }
    if (typeof options.searchFields === 'string') {
        options.searchFields = [options.searchFields];
    }
    if (id.attr('placeholder')) {
        options.placeholder = id.attr('placeholder');
    }
    var $options = {
        queryParams: options.queryParams,
        valueField: options.valueField,
        textField: options.textField,
        width: options.width,
        height: options.height,
        mode: options.mode,
        delay: options.delay,
        value: options.value,
        multiple: options.multiple,
        onBeforeLoad: function (params) {
            $(this).next().children('input').attr('placeholder', options.placeholder);
            if (options.mode != 'local') {
                $(this).next().children('span').append($(getLoadingHTML()));
                $(this).next().children('span').children('a').hide();
            }
        }, onSelect: function (row) {
            if (options.multiple) {
                var value = [];
                id.next().find('input.combo-value').each(function (index, el) {
                    value.push($(el).val());
                });
                id.val(value.join(','));
            } else {
                id.val(row[options.valueField])
            }
            if (typeof options.onSelect === 'function') {
                options.onSelect(row, this);
            }
        }, onLoadSuccess: function (data, el) {
            setTimeout(function () {
                id.next().children('span').children('a').show();
                id.next().children('span').children('div').hide();
            }, 300);
            if (options.multiple) {
                var value = [];
                id.next().find('input.combo-value').each(function (index, ele) {
                    value.push($(ele).val());
                });
                id.val(value.join(','));
            }
            if (typeof options.onLoadSuccess === 'function') {
                options.onLoadSuccess(data, el, this);
            }
        }, onLoadError: function () {
            id.next().children('span').children('div').hide();
            id.next().children('span').children('span').hide();
            id.next().children('span').append(getErrorHTML());

        }, onUnselect: function (row) {
            var value = [];
            id.next().find('input.combo-value').each(function (index, el) {
                value.push($(el).val());
            });
            id.val(value.join(','));
            if (typeof options.onUnselect === 'function') {
                options.onUnselect(row, this);
            }
        }, filter: function (q, row) {
            var opts = $(this).combobox('options');
            var qs = removeUnicode(q.toLowerCase());
            if (removeUnicode(row[opts.textField]).toLowerCase().indexOf(qs) >= 0) {
                return true;
            }
            return false;
        }, onChange: function (newvalue, oldvalue) {
            if (typeof options.onChange === 'function') {
                options.onChange(newvalue, oldvalue, this);
            }
        }
    };
    if (typeof options.formatter === 'function') {
        $options['formatter'] = options.formatter;
    }
    delete $options.url;
    delete $options.data;
    if (typeof data === 'string') {
        $options.url = data;
    } else {
        $options.data = data;
    }
    id.combobox($options);
}
function co_cau (type, inDay){
    $CFG || ($CFG = {});
    inDay || (inDay = '');
    var cocau_chuan = {
        protein: 4, fat: 9, sugar: 4
    };
    var cocau_chinh = {
        protein: 4.1, fat: 9.3, sugar: 4.1
    };
    if (!type){
        type = $CFG.co_cau;
    }
    if (type!=='cocau_chuan') {
        return cocau_chinh;
    }
    if (inDay.split('/').length >= 3 && $CFG.co_cau_from){
        if (dateparser($CFG.co_cau_from).getTime() <= dateparser(inDay).getTime()) {
            return cocau_chuan;
        }else{
            return cocau_chinh;
        }
    }else if ($CFG.co_cau_from){
        return cocau_chuan;
    }
    return cocau_chuan;
}
function init_uploader(id_fileupload, url, accept_file_types, limit_file_size, success_func) {
    var input_element = null;
    if (typeof id_fileupload === 'string') {
        input_element = $("#" + id_fileupload);
    } else {
        input_element = id_fileupload;
    }
    var container = $('<span class="container-input-file"></span>');
    input_element.after(container);
    var content_input = $('<span class="btn fileinput-button"></span>');
    var id_files = $('<span id="files" class="files"></span>');
    content_input.append($('<span class="btn btn-warring">Chọn file...</span>')).append(input_element);
    container.append(content_input).append(id_files);
    var length = arguments.length;
    var resizable = true;
    if (length == 7) {
        resizable = arguments[6];
    }
    input_element.fileupload({
        url: url,
        dataType: 'json',
        singleFileUploads: true,
        autoUpload: true,
        acceptFileTypes: new RegExp("(\.|\/)(" + accept_file_types + ")$", "i"),
        maxFileSize: (15 * 1024 * 1024), /* 15 MB */
        disableImageResize: !resizable, /* Android(?!.*Chrome)|Opera */
        previewMaxWidth: 70,
        previewMaxHeight: 70,
        previewCrop: true
    }).on('fileuploadprocessalways', function (e, data) {
        id_files.html('');
        var index = 0;
        file = data.files[index];
        if (file.error) {
            $.messager.alert('Lỗi', file.error);
        } else {
            statusloading();
            data.context = id_files;
            var node = $('<span class="file_upload_choise"/>')
                .text(file.name)
                .append('<span style="font-weight:bold">&nbsp; - ' + (file.size / 1024 / 1024).toFixed(2) + ' Mb</span>&nbsp;');
            node.append('<span id="upload_stt" style="color: red"></span>');
            node.appendTo(data.context);
        }
    }).on('fileuploadprogressall', function (e, data) {
        var progress = parseInt(data.loaded / data.total * 100, 10);
        $("#upload_stt").text(progress + "%");
    }).on('fileuploaddone', function (e, data) {
        success_func(data.result);
        if (data.result.files) {
            var index = 0, file = data.result.files[index];
            if (file.url) {
                var link = $('<a>')
                    .attr('target', '_blank')
                    .prop('href', file.url);
                if (data.context) {
                    $(data.context.children()[index]).wrap(link);
                }
            }
            if (file.error) {
                $.messager.alert('Lỗi', file.error);
            }
        }
        id_files.html('');
        statusloadingclose();
    }).on('fileuploadfail', function (e, data) {
        id_files.html('');
        $.messager.alert('Lỗi', 'Tải file không thành công');
        statusloadingclose();
    }).prop('disabled', !$.support.fileInput)
        .parent().addClass($.support.fileInput ? undefined : 'disabled');
}

function form_edit_pass() {
    $.dm_datagrid.showEditForm(
        {
            module: 'admin/user',
            action: 'changepass',
            title: 'Đổi mật khẩu',
            size: size.small,
            noteWarning: '',
            content: function (element) {
                loadForm('admin/user', 'changepass', {}, function (resp) {
                    $(element).html(resp);
                })
            }
        }
    );
}

function count($ob) {
    $rs = 0;
    if ($ob === undefined){

    }else if (typeof $ob === 'string' || typeof $ob === 'array') {
        $rs = $ob.length;
    } else if (typeof $ob === 'number') {
        var str = $ob + '';
        $rs = $str.length;
    } else if (typeof $ob === 'function') {
    } else if (typeof $ob === 'object') {
        for (var i in $ob) {
            $rs++;
        }
    }
    return $rs;
}
function openDlgThongBao(name_cache, filename, solan) {
    var tb_count = getCookie(name_cache);
    if (!tb_count) {
        tb_count = 1;
    }else{
        tb_count = parseInt(tb_count);
    }
    if (tb_count > solan) {
        return;
    }
    tb_count += 1;
    setCookie(name_cache, tb_count, 60*60*24*30*12);
    var content = '<div id="dlg_warning"><div class="dlg-content"></div></div>';
    if (!$('body > #dlg_warning').length) {
        $('body').append(content);
    }
    $('#dlg_warning').dialog({
        title: '',
        width: 550,
        height: 400,
        closed: false,
        cache: false,
        modal: true,
        onOpen : function (ele) {
            var img = $('<img style="width: 100%; height: 100%;">');
            img.attr('src',$CFG.remote.base_url+'/images/thongbao/'+filename+'?_' + Math.random().toString().split('.')[1]);
            $('.window').css({"background":"rgba(0,0,0,0)","border":"0px","box-shadow":"none"});
            var btn_close = $('<div style="top: 60px;right: 53px;position: absolute;font-size:17px; opacity: 0.3;" class="close"></div>');
            btn_close.append('<img src="' + $CFG.remote.base_url + '/images/close.png">');
            $('#dlg_warning').css({"background":"rgba(0,0,0,0)","border":"0px", "overflow": "unset"}).append(btn_close).append(img);
            btn_close.click(function(){
                $('#dlg_warning').dialog("close");
            });
            $('.window-mask').click(function(){
                $('#dlg_warning').dialog("close");
            });
        }
    });
}
function loadForm(module, action, params, callback) {
    statusloading();
    params || (params = {});
    params.dataType || (params.dataType = 'html');
    params.async || (params.async = false);
    /*Truyền số người online để*/
    var online = $('#tructuyen').html();
    params._online = online;
    var url = module;
    if (action != '') {
        url = url + '/' + action;
    }
    url = url + "?_=" + rand();
    if(url.split('//').length === 1){
		if(url[0] !== '/') {
			url = '/' + url;
		}
		url = $CFG.remote.base_url + url;
	}
    var note = {
        title: 'Thông báo',
        message: '',
        buttons: [{
            label: 'Đóng',
            icon: 'glyphicon glyphicon-log-out',
            action: function (dialog) {
                dialog.close();
            }
        }]
    };
    $.ajax({
        type: 'GET',
        url: url,
        data: params,
        dataType: params.dataType,
        async: params.async,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function (resp) {
            if (typeof callback === 'function') {
                callback(resp);
            }
            if (params.dataType == 'json') {
                if (resp.errors != undefined) {
                    if (count(resp.errors) > 0) {
                        var html = [];
                        $.each(resp.errors, function (index, item) {
                            if (typeof item != 'string') {
                                $.each(item, function (i1, value) {
                                    html.push('<div> - ' + value + '</div>');
                                })
                            } else {
                                html.push('<div> - ' + item + '</div>');
                            }
                        });
                        if (html.length > 0) {
                            note.message = '<div style="max-height:400px;overflow-y:scroll;">' + html.join(' ') + '</div>';
                            $.dm_datagrid.show(note);
                        }
                    }
                }
            }
            statusloadingclose();
        },
        error: function (e) {
            if (typeof callback === 'function') {
                callback('Lỗi khi xử lý');
            }
        }, beforeSend: function () {
            showSpinner('load_form_' + module);
        }, complete: function () {
            statusloadingclose();
        }
    });
}
function sum(arr) {
    var rs = 0;
    if (Array.isArray(arr)){
        for (var value of arr){
            rs = $['+'](rs, value);
        }
    }
    return rs;
}
function setDialog2fullscreen(dialogRef, options) {
    var header = dialogRef.getModalHeader().children('.bootstrap-dialog-header');
    var body_h = $('body').height() - 60;
    if(options.showButton) {
        body_h -= 40;
    }
    dialogRef.getModalBody().css({'max-height': body_h, 'overflow-y': 'auto'});
    if (options.fullScreen) {
        dialogRef.getModalDialog().addClass('full-screen');
    }
    header.dblclick(function () {
        if (dialogRef.getModalDialog().hasClass('full-screen')) {
            dialogRef.getModalDialog().removeClass('full-screen');
            dialogRef.getModalBody().css({'height': ''})
        } else {
            dialogRef.getModalDialog().addClass('full-screen');
            dialogRef.getModalBody().css({'height': body_h})
        }
        dialogRef.getModalDialog().css({top: 0, left: 0});
    });
    if (options.closable != false && options.closable != 'false') {
        header.children('.bootstrap-dialog-close-button').css('display', 'block');
    }
    var btnZoomOutIn = $('<div href="javascript: void(0);" class="bootstrap-dialog-close-button btn-dialog-fullscreen"></div>');
    var zoomOut = $('<span class="glyphicon glyphicon-fullscreen" title="Toàn màn hình"></span>');
    zoomOut.click(function () {
        dialogRef.getModalDialog().addClass('full-screen');
        dialogRef.getModalBody().css({'min-height': body_h})
    });
    var zoomIn = $('<span class="glyphicon glyphicon-resize-small" title="Thu nhỏ"></span>');
    zoomIn.click(function () {
        dialogRef.getModalDialog().removeClass('full-screen');
        dialogRef.getModalBody().css({'min-height': ''});
    });
    if(!options.disable_bt_full_screen === true) {
        btnZoomOutIn.append(zoomOut).append(zoomIn);
        header.append(btnZoomOutIn);
    }
    if (typeof options.content === 'function' || options.scope != undefined) {
        dialogRef.btnReload = $('<div href="javascript: void(0);" class="bootstrap-dialog-close-button btn-dialog-fullscreen"></div>')
            .append($('<span class="glyphicon glyphicon-refresh" title="Tải lại"></span>'));
        dialogRef.btnReload.click(function () {
            if (options.scope != undefined) {
                options.scope.getTemplate(options.content, function (template) {
                    dialogRef.getModalBody().html(options.scope.compile(template, options.scope));
                    if (typeof options.reload === 'function') {
                        options.reload(dialogRef);
                    }
                }, true);
            } else {
                dialogRef.getModalBody().html($('<div style="margin:20px; width:100%;height:50px;"></div>').append(getLoadingHTML()));
                options.content(dialogRef.getModalBody());
            }
        });
        if(!options.disable_bt_full_screen === true) {
            header.append(dialogRef.btnReload);
        }
    }
}

function utf8Encode(string) {
    string = string.replace(/\x0d\x0a/g, "\x0a");
    var output = "";
    for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
            output += String.fromCharCode(c);
        } else if ((c > 127) && (c < 2048)) {
            output += String.fromCharCode((c >> 6) | 192);
            output += String.fromCharCode((c & 63) | 128);
        } else {
            output += String.fromCharCode((c >> 12) | 224);
            output += String.fromCharCode(((c >> 6) & 63) | 128);
            output += String.fromCharCode((c & 63) | 128);
        }
    }
    return output;
}

function utf8Decode(input) {
    var string = "";
    var i = 0;
    var c = c1 = c2 = 0;
    while (i < input.length) {
        c = input.charCodeAt(i);
        if (c < 128) {
            string += String.fromCharCode(c);
            i++;
        } else if ((c > 191) && (c < 224)) {
            c2 = input.charCodeAt(i + 1);
            string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
            i += 2;
        } else {
            c2 = input.charCodeAt(i + 1);
            c3 = input.charCodeAt(i + 2);
            string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
            i += 3;
        }
    }
    return string;
}

function base64Encode(input) {
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    input = utf8Encode(input);
    while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
            enc4 = 64;
        }
        output = output + keyString.charAt(enc1) + keyString.charAt(enc2) + keyString.charAt(enc3) + keyString.charAt(enc4);
    }
    return output;
}

function base64Decode(input) {
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (i < input.length) {
        enc1 = keyString.indexOf(input.charAt(i++));
        enc2 = keyString.indexOf(input.charAt(i++));
        enc3 = keyString.indexOf(input.charAt(i++));
        enc4 = keyString.indexOf(input.charAt(i++));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output = output + String.fromCharCode(chr1);
        if (enc3 != 64) {
            output = output + String.fromCharCode(chr2);
        }
        if (enc4 != 64) {
            output = output + String.fromCharCode(chr3);
        }
    }
    output = utf8Decode(output);
    return output;
}

function arrayToJson(values) {
    var json = "";
    i = 1;
    for (t in values) {
        if (typeof values[t] == "object") {
            json += "\"" + values[t]['id'] + "\" : \"" + values[t]['value'] + "\",";
            i++;
        } else {
            json += "\"" + t + "\" : \"" + values[t] + "\",";
            i++;
        }
    }
    json = json.substring(0, json.lastIndexOf(','));
    json = "{" + json + "}";
    return json;
}

function checkNeeded(thisInput, ignoredIds, neededIds) {
    ignoredIds || (ignoredIds = []);
    neededIds || (neededIds = []);
    var check = true;
    if (neededIds.length > 0) {
        if ($.inArray(thisInput.attr("id"), neededIds) == -1) check = false;
    }
    if (ignoredIds.length > 0) {
        if ($.inArray(thisInput.attr("id"), ignoredIds) != -1) check = false;
    }
    return check;
}

function getSubmitForm(name, ignoredIds, neededIds) {
    ignoredIds || (ignoredIds = []);
    neededIds || (neededIds = []);
    var form = '';
    if (typeof name == 'string') {
        if (name[0] == '#') {
            form = $(name);
        } else {
            form = $("#" + name);
            if (!form.length) {
                form = $('.' + name);
            }
        }
    } else {
        form = name;
    }
    var data = [];
    form.find("input:text").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find('input[type="date"]').each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find('input[type="number"]').each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds)) {
            pushToArray(data, $(this));
        }
    });
    form.find("select").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:file").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:hidden").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:password").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("textarea").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds))
            pushToArray(data, $(this));
    });
    form.find("input:radio").each(function (i) {
        if (checkNeeded($(this), ignoredIds, neededIds)) {
            var id = $(this).attr('id');
            if (id != "" && id != null) {
                var value = $("input[id='" + id + "']:checked").val();
                if (value == undefined) value = "";
                //value = base64Encode(value);
                var item = {id: id, value: value};
                data.push(item);
            }
        }
    });
    form.find("input:checkbox").each(function (i) {
        var id = $(this).attr('id');
        const none_value = $(this).attr('none_value');
        // console.log($(this)[0].checked)
        if (id != "" && id != null) {
            if(none_value) {
                var item = {id: id, value: 0};
                if($(this)[0]) {
                    //item.value = base64Encode($(this)[0].checked ? '1' : '0') ;
                    item.value = $(this)[0].checked ? '1' : '0' ;
                }
                data.push(item);
            }
            else {
                var tmp = "";
                form.find("input[id='" + id + "']:checked").each(function (i) {
                    tmp += $(this).val() + ",";
                });
                tmp = tmp.substring(0, tmp.length - 1);
                //tmp = base64Encode(tmp);
                var item = {id: id, value: tmp};
                data.push(item);
            }
        }
    });
    return data;
}

function pushToArray(arr, obj) {
    var id = obj.attr('id');
    var css_class = obj.attr("class");
    var value = null;
    if (id != '' && id != undefined) {
        switch (css_class) {
            case 'datebox-f combo-f':
                value = encodeHTML($("#" + id).datebox('getValue'));
                break;
            case 'combobox-f combo-f':
                value = encodeHTML($("#" + id).combobox('getValue'));
                break;

            case 'easyui-datebox datebox-f combo-f':
                value = encodeHTML($("#" + id).combobox('getValue'));
                break;

            case 'tree':
                var nodes = $('#' + id).tree('getChecked');
                var s = '';
                for (var i = 0; i < nodes.length; i++) {
                    if (nodes[i].id != '') {
                        s += nodes[i].id + ',';
                    }
                }
                value = s;
                break;
            default:
                value = obj.val() + '';
                if (obj.attr("type") == 'number') {
                    value = value.replace(',', '.');
                }
                value = encodeHTML(value);
                break;
        }
        //value = base64Encode(value);
        var item = {id: id, value: value};
        arr.push(item);
    }
}

$.alert = function (msg) {
    if (typeof msg != 'string') {
        BootstrapDialog.show(msg);
    } else {
        BootstrapDialog.show({
            title: 'Thông báo',
            message: '<div class="col col-right col-md-12 col-sm-12">' + msg + '</div>',
            buttons: [
                {
                    label: 'Đóng',
                    icon: 'glyphicon glyphicon-log-out',
                    action: function (dialog) {
                        dialog.close();
                    }
                }
            ]
        });
    }
}

function encodeHTML(html) {
    if (html != null)
        return html.replace(/"/g, "\\'");
    else
        return "";
}

$(function () {
    $.fn.datebox.defaults.formatter = function (date) {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
    };
    $.fn.datebox.defaults.parser = function (s) {
        if (!s) return new Date();
        var ss = s.split('/');
        var d = parseInt(ss[0], 10);
        var m = parseInt(ss[1], 10);
        var y = parseInt(ss[2], 10);
        if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
            return new Date(y, m - 1, d);
        } else {
            return new Date();
        }
    };
});
processAll = (request_arr, callback_success, callback_error)=>{
    if ($.isArray(request_arr)) {
        var checkLoading = [];
        var responses = [];
        var string_check_success = '';
        angular.forEach(request_arr, (req, i) => {
            var req = request_arr[i];
            string_check_success += '1';
            req[1] || (req[1] = {});
            if(typeof req[1].async === 'undefined'){
                req[1].async = true;
            }
            process(req[0], req[1],(resp) => {
                if(resp.result == 'success') {
                    responses[i] = resp;
                    checkLoading.push(1);
                    if(typeof req[2] === 'function') {
                        req[2](resp);
                    }
                }else{
                    checkLoading.push(0);
                }
                if (checkLoading.length == request_arr.length) {
                    if(string_check_success == checkLoading.join('') && typeof callback_success === 'function') {
                        callback_success(request_arr.length>1?responses:responses[0]);
                    }
                }
            },()=>{
                checkLoading.push(0);
                if (checkLoading.length == request_arr.length) {
                    if(typeof callback_error == 'function') {
                        callback_error(request_arr.length>1?responses:responses[0]);
                    }
                }
            }, req[4], req[5], req[6]);
        });
    }
};
var selectedRow = null;
var block_requests = {};
function process(url, params, success_func, error_func, show_notification, show_loading) {
    params || (params = {});
    if (url.split('api').length == 1) {
        url = url.replace($CFG.remote.base_url + '/', '');
        url = url.replace('doing/', '');
        url = $CFG.remote.base_url + '/doing/' + url;
    }
    if (show_notification == 'false' || show_notification == false) {
        show_notification = false;
    } else {
        show_notification = true;
    }
    var type = 'POST';
    if (in_array((params.type+'').toLowerCase(), ['post', 'get'])) {
        type = params.type;
    }
    var text_status = undefined;
    if (typeof show_loading === 'string' && show_loading != '') {
        text_status = show_loading;
    }
    if (show_loading == 'false' || show_loading == false) {
        show_loading = false;
    } else {
        show_loading = true;
    }
    //console.log(params);
    var async = params.async;
    async || (async = false);
    if (async != true && async != 'true') {
        async = false;
    } else {
        async = true;
    }
    delete params.async;
    var key_request = url + JSON.stringify(params);
    if(block_requests[key_request]) {
        return ;
    }
    if (show_loading) {
        statusloading(key_request, text_status);
    }
    var note = {
        title: 'Thông báo',
        message: '',
        buttons: [{
            label: 'Đóng',
            icon: 'glyphicon glyphicon-log-out',
            action: function (dialog) {
                dialog.close();
            }
        }]
    };
    
    //alert($('meta[name="X-CSRF-TOKEN"]').attr('content'));
    return $.ajax({
        type: type,
        url: url,
        async: async,
        data: params,
        timeout: 120000,
        dataType: 'json',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3',
        success: function (resp) {
            delete block_requests[key_request];
            if (!resp) {
                if (show_notification != false) {
                    note.message = "Lỗi";
                    $.dm_datagrid.show(note);
                }
            } else if (resp.result == 'logouted') {
                var html = resp.html;
                $.dm_datagrid.showAddForm(
                    {
                        module: $CFG.project + '/menu_adjust',
                        action: 'add',
                        title: 'Đăng nhập',
                        size: size.wide,
                        fullScreen: false,
                        showButton: false,
                        content: function (element, dialogRef) {
                            if (resp.html) {
                                $(element).html(resp.html);
                            }
                        }
                    }
                );
            } else if (resp.result == 'success') {
                if (resp.errors != undefined) {
                    var html = [];
                    $.each(resp.errors, function (index, item) {
                        if (typeof item != 'string') {
                            $.each(item, function (i1, value) {
                                html.push('<div> - ' + value + '</div>');
                            })
                        } else {
                            html.push('<div> - ' + item + '</div>');
                        }
                    });
                    if (html.length > 0) {
                        note.message = '<div style="max-height:400px;overflow-y:auto;">' + html.join(' ') + '</div>';
                        $.dm_datagrid.show(note);
                    }
                }
                $.messager.show({
                    msg: '<i class="fa fa-bolt color-orange" aria-hidden="true" style="margin-right: 3px;font-size: 15px;"></i> Xử lý thành công!',
                    timeout: 1800,
                    showType: 'slide',
                    width: 150,
                    height: 40
                });
            } else if (resp.result == 'fail') {
                if (show_notification != false) {
                    note.message = "Lỗi xử lý";
                    $.dm_datagrid.show(note);
                }
            } else if (resp.result == 'fail_captcha') {
                note.message = resp.errors;
                $.dm_datagrid.show(note);
            } else {
                if (show_notification != false) {
                    var html = [];
                    if (resp.errors) {
                        $.each(resp.errors, function (index, item) {
                            html.push('<div> - ' + item + '</div>');
                        })
                    }
                    if (html.length == 0) {
                        html.push('Lỗi.');
                    }
                    note.message = html.join(' ');
                    $.dm_datagrid.show(note);
                }
            }
            if (typeof success_func === 'function' && resp.result != 'logouted') {
                success_func(resp, params);
            }
        },
        error: function () {
            if (show_notification != false) {
                setTimeout(function () {
                    note.message = 'Không xử lý được chức năng';
                    $.dm_datagrid.show(note);
                });
            }
            if (typeof error_func === 'function') {
                error_func();
            }
        }, beforeSend: function () {
            block_requests[key_request] = 1;
            setTimeout(function () {
                delete block_requests[key_request];
            }, 2000);
        }, complete: function () {
            delete block_requests[key_request];
            if (show_loading) {
                statusloadingclose();
            }
        }
    });
}

downloadFileCommon = function (url, data) {
    //url and data options required
    if (url) {
        if (data) {
            var form_submit = jQuery('<form action="' + url + '" method="post" style="position: fixed; width: 5px; height: 5px; top: -100px;"><input type="hidden" name="_token" value="'+$('meta[name="X-CSRF-TOKEN"]').attr('content')+'"></form>');

            jQuery('body').append(form_submit);
            jQuery.each(data, function (key, val) {
                if (typeof val === 'object') {
                    val = JSON.stringify(val);
                }
                var frm_input = jQuery('<input type="hidden" name="' + key + '" value="" />');
                form_submit.append(frm_input);
                frm_input.val(val);
            });
            $('<input type="submit" value="submit">').appendTo(form_submit);
            form_submit.submit(function () {
            });
            const a = form_submit.submit();

        }
    }
    ;
};

jQuery.downloadFile = function (url, data) {
    //url and data options required
    if (url) {
        if (data) {
            var form_submit = jQuery('<form action="' + url + '" method="post" style="position: fixed; width: 5px; height: 5px; top: -100px;"><input type="hidden" name="_token" value="'+$('meta[name="X-CSRF-TOKEN"]').attr('content')+'"></form>');

            jQuery('body').append(form_submit);
            jQuery.each(data, function (key, val) {
                if (typeof val === 'object') {
                    val = JSON.stringify(val);
                }
                var frm_input = jQuery('<input type="hidden" name="' + key + '" value="" />');
                form_submit.append(frm_input);
                frm_input.val(val);
            });
            $('<input type="submit" value="submit">').appendTo(form_submit);
            form_submit.submit(function () {
            });
            const a = form_submit.submit();

        }
    }
    ;
};
$.loading = {
    show: function (title) {
        var zindex = $("body div.window").css("z-index");
        if (title == undefined) title = "Tải dữ liệu...";
        $("body").append('<div id="loading"><div id="div-loading" class="loading window" style="background:white;color:black;display: block; width: 180px; z-index: 20001;"><img src="theme/img/loading3.gif" border=0 align="top"/>&nbsp;' + title + '</div><div class="window-mask" style="width: 100%; height: 100%; display: block; z-index: 20000;"></div></div>');
        $('#div-loading').center();
        $('#loading').show();
    },
    hide: function () {
        $('#div-loading').hide();
        $('#div-loading').remove();
        $('#loading').hide();
        $('#loading').remove();
    }
};
/*Hàm tính nhân chia số thực*/
FLOAT = {
    0: 10,
    1: 100,
    2: 1000,
    3: 10000,
    4: 100000,
    5: 1000000,
    6: 10000000,
    7: 100000000,
    8: 1000000000,
    9: 10000000000,
};
$['*'] = function (a, b) {
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return a * b / factor / factor;
};
$['/'] = function (a, b) {
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return rs = a / b;
};
$['+'] = function (a, b) {
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return (a + b) / factor;
};
$['-'] = function (a, b) {
    a += '';
    b += '';
    if ((a + '').split('.').length == 1) {
        a = a + '.0';
    }
    if ((b + '').split('.').length == 1) {
        b = b + '.0';
    }
    var len = a.split('.')[1].length;
    if (len < b.split('.')[1].length) {
        len = b.split('.')[1].length;
    }
    var factor = FLOAT[len];
    if (!factor) {
        factor = 10000000;
    }
    a = Math.round(parseFloat(a) * factor);
    b = Math.round(parseFloat(b) * factor);
    return (a - b) / factor;
};

/*  Chuẩn hóa chuỗi số*/
function clearnNumeric(value) {
    var number = ['-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'];
    var type_number = 'int';
    var is_number = false;
    if (typeof value === 'number') {
        is_number = true;
    }
    value += '';
    if (value.split('.') > 1) {
        type_number = 'float';
    }
    value = value.replace(',', '.');
    value = value.replace('..', '.');
    value = value.replace('..', '.');
    value = value.replace('--', '-');
    value = value.replace('--', '-');
    if (value[0] == '.') {
        value = value.substring(1, value.length);
    }
    if (value[value.length - 1] == '-') {
        value = value.substring(0, value.length - 1);
    }
    var rs = '';
    var number_dot = 0;
    for (var i = 0; i < value.length; i++) {
        if (value[i] == '.') {
            number_dot++;
        }
        if (i > 0 && i < value.length - 1 && value[i] == '-' || number_dot > 1 && value[i] == '.') {

        } else if (in_array(value[i], number)) {
            rs += value[i];
        }
    }
    if (is_number) {
        if (type_number == 'int') {
            value = parseInt(value);
        } else {
            value = parseFloat(value);
        }
    }
    return rs;
}

function absNumeric(value) { /* Trị tuỵet đối dùng hàm có sẵn trong Math sẽ không gõ được dâu chấm ở cuối nếu là số */
    var type_number = 'int';
    var is_number = false;
    if (typeof value === 'number') {
        is_number = true;
    }
    value += '';
    if (value.split('.') > 1) {
        type_number = 'float';
    }
    while (value[0] == '-') {
        value = value.substring(1, value.length);
    }
    if (is_number) {
        if (type_number == 'int') {
            value = parseInt(value);
        } else {
            value = parseFloat(value);
        }
    }
    return value;
}

function toFloat(value) {
    value += '';
    var arr = value.replace(',', '.').split('.');
    var rs = parseInt(arr[0]);
    if (arr.length == 2) {
        var d = 10;
        for (var i = 0; i < arr[1].length; i++) {
            var v = parseInt(arr[1][i]);
            rs += v / d;
            d *= 10;
        }
    }
    return rs;
}

function round(value, num, get_default, round_unit_money) {
    if (get_default == undefined) {
        get_default = 0;
    }
    if (value + '' == 'NaN' || value == undefined) {
        value = get_default;
    } else {
        if (num === undefined || num === "") {
            if ($CFG) {
                num = $CFG.round_number_config;
            }
        }
        if (num === undefined || num === "") {
            num = 3;
        }
        var per = 1;
        for (var i = 0; i < num; i++) {
            per *= 10;
        }

        if (typeof value != 'number') {
            value = parseFloat(value);
        }
        value = Math.round(value * per) / per;
        if (value + '' === '0') {
            value = get_default;
        }
    }
	var round_unit_moneys = [10, 100, 1000]; // 10đ, 100đ, 1000đ
	if(round_unit_money!=undefined && (round_unit_moneys.indexOf(parseInt(round_unit_money))>=0)) {
		round_unit_money = parseInt(round_unit_money);
		var surplus = value%round_unit_money;
		var tmp_plus = (surplus/round_unit_money>=0.5)? round_unit_money: 0;
		value = value - surplus + tmp_plus;
	}
    return value;
}

function myformatter(date) {
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
}

function myparser(s, spartor) {
    if (!s) return new Date();
    if (typeof s === 'object') {
        return s;
    }
    spartor || (spartor = '/');
    var ss = (s.split(spartor));
    var y = parseInt(ss[spartor=='/'?0:2], 10);
    var m = parseInt(ss[1], 10);
    var d = parseInt(ss[spartor=='/'?2:0], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(d, m - 1, y);
    } else {
        return new Date();
    }
}

function dateparser(s) {
    if (!s) return new Date();
    var ss = (s.split('/'));
    var d = parseInt(ss[0], 10);
    var m = parseInt(ss[1], 10);
    var y = parseInt(ss[2], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(y, m - 1, d);
    } else {
        return new Date();
    }
}

function converdate(s) {
    if (!s) return new Date();
    var ss = (s.split('/'));
    var y = parseInt(ss[0], 10);
    var m = parseInt(ss[1], 10);
    var d = parseInt(ss[2], 10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
        return new Date(y, m - 1, d);
    } else {
        return new Date();
    }
}

function $NaN(value, end) {
    end || (end = '');
    if (value + '' == 'NaN') {
        return end;
    }
    return value;
}

function $Infinity(value, ext) {
    ext || (ext = 0);
    if (value + '' == 'Infinity') {
        value = ext;
    }
    return value;
}

function dateboxOnSelect(date) {
    if (!date) {
        date = new Date();
    }
    if (typeof date.getFullYear === 'function') {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        var val = (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
        $(this).val(val);
        return val;
    }
    return date;
}

function monthNow() {
    var date = new Date();
    var m = date.getMonth() + 1;
    return m.toString();
}

function array_del_value($array, $value) {
    var res = null;
    if ($.type($array) === 'array') {
        res = [];
    }
    if ($.type($array) === 'object') {
        res = {};
    }
    if (res != null) {
        $.each($array, function (index, item) {
            if (item != $value) {
                if ($.type($array) === 'array') {
                    res.push(item);
                }
                if ($.type($array) === 'object') {
                    res[index] = item;
                }
            }
        });
    } else {
        res = $array;
    }
    return res;
}

function array_del_key($array, $value) {
    var res = null;
    if ($.type($array) === 'array') {
        res = [];
    }
    if ($.type($array) === 'object') {
        res = {};
    }
    if (res != null) {
        $.each($array, function (index, item) {
            if (index != $value) {
                if ($.type($array) === 'array') {
                    res.push(item);
                }
                if ($.type($array) === 'object') {
                    res[index] = item;
                }
            }
        });
    } else {
        res = $array;
    }
    return res;
}

function in_array($value, $array) {
    var res = false;
    $.each($array, function (index, item) {
        if (item == $value) {
            res = true;
            return true;
        }
    });
    return res;
}

function indexOf_array($value, $array) {
    var res = false;
    if ($value) {
        $value += '';
        for (var i in $array) {
            if ($value.toLowerCase().indexOf($array[i].toLowerCase()) >= 0) {
                return true;
            }
        }
    }
    return false;
}

function in_array_key($value, $array) {
    var res = false;
    $.each($array, function (index, item) {
        if (index + '' == $value + '') {
            res = true;
            return;
        }
    });
    return res;
}

function createLinkButton($script, $title, $iconclass, $text) {
    var html = '<a id="linkbutton" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" plain="true" '
        + ' onclick="' + $script + '" href="javascript:void(0)" group="" title="' + $title + '">'
        + '<span class="l-btn-left l-btn-icon-left">'
        + '<span class="l-btn-text">' + $text + '</span>'
        + '<span class="l-btn-icon ' + $iconclass + '"> </span>'
        + '</span>'
        + '</a>';
    return html;
}

function formatdate(value) {
    if (value) {
        value = value.split(' ')[0];
        var d = value.split("-");
        return d[2] + "/" + d[1] + "/" + d[0];
    } else {
        return '';
    }
}

function getLoadingHTML(msg, type) {
    if (!type) {
        var num = Math.floor((Math.random() * 10) + 1);
        if (num == 0) {
            num = 1;
        } else if (num > 4) {
            num = num - 4;
        }
        type = num;
    } else if (type > 4) {
        type = 4;
    }
    msg = '<div class="spinner-container spinner'
        + type + '"><div id="time-progress-bar" style="width:28px;text-align:center;padding-top:7px"></div></div>'
        + '<span style="position: absolute; padding: 5px;">' + (msg ? msg : 'Đang tải ...') + '</span>';
    return msg;
}

function getErrorHTML() {
    html = '<div><span style="padding: 5px;color: #ff8700;" title="Lỗi khi tải dữ liệu" class="glyphicon glyphicon-warning-sign"></span></div>';
    return html;
}
function decodeHTML(encodedStr) {
    return $("<div/>").html(encodedStr).text();
}
function removeUnicode(str) {
    if (typeof str != 'string') {
        return str;
    }
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|$|_/g, "");
    str = str.replace(/-+-/g, " ");
    str = str.replace(/^\-+|\-+$/g, " ");
    return str;
}
function setCookie(c_name, value, exdays) {
    exdays || (exdays = 60 * 60 * 24);
    var exdate = new Date();
    exdate.setDate(exdate.getDate() + exdays);
    var c_value = escape(value) + ((exdays == null) ? "" : "; expires=" + exdate.toUTCString());
    document.cookie = c_name + "=" + c_value;
};

function getCookie(c_name) {
    var i, x, y, ARRcookies = document.cookie.split(";");
    for (i = 0; i < ARRcookies.length; i++) {
        x = ARRcookies[i].substr(0, ARRcookies[i].indexOf("="));
        y = ARRcookies[i].substr(ARRcookies[i].indexOf("=") + 1);
        x = x.replace(/^\s+|\s+$/g, "");
        if (x == c_name) {
            return unescape(y);
        }
    }
}

function showSpinner(id) {
    if (id === 'tmp_layout') {
        dialogCloseAll();
    }
    if (!$CFG.spinController) {
        $CFG.spinController = {}
    }
    $CFG.spinController[id] = true;
}

function hiddenSpinner(id) {
    delete $CFG.spinController[id];
}

function setCacheProcess(id, msg) {
    statusloadingCache || (statusloadingCache = {});
    msg || (msg = 1);
    id || (id = Math.random());
    statusloadingCache[id] = msg;
    return id;
}

function delCacheProcess(delAll) {
    if (delAll) {
        statusloadingCache = {};
    } else {
        if (count(statusloadingCache) > 0) {
            delete statusloadingCache[Object.keys(statusloadingCache)[Object.keys(statusloadingCache).length - 1]];
        }
    }
}

statusloading = function (id, msg) {
    setCacheProcess(id, msg);
    setTimeout(function () {
        var thongbao = 'Đang xử lý...';
        if (typeof msg === 'string') {
            thongbao = msg;
        }
        $('#statusloading-mash').show().find('.spinner-text').html(thongbao);
    }, 0);
}
statusloadingclose = function (closeAll) {
    angular.element(document).ready(function () {
        delCacheProcess(closeAll);
        if (count(statusloadingCache) == 0) {
            setTimeout(function () {
                $('#statusloading-mash').hide();
            }, 100);
        }
    });
}
initStatusLoading = function () {
    var html = '<div id="statusloading-mash" style="display: none;"><div class="spinner-container-loading"><div class="spinner-container btn-hover spinner3" id="spinner-container"> <div id="time-progress-bar"></div></div><span class="spinner-text">Đang xử lý ...</span></div></div>';
    var spinner = $('#statusloading-mash');
    if (!spinner.length) {
        spinner = $(html);
        $('body').append(spinner);
    }
    spinner.children().click(function () {
        spinner.css({display: 'none'});
        statusloadingCache = {};
    })
}

function number_stand(value) {
    value || (value = 0);
    value += '';
    var tmp = value.split('.');
    if (tmp.length == 1) {
        value = parseInt(tmp[0]) + '';
    } else {
        tmp[0] = parseInt(tmp[0]);
        value = tmp.join('.');
    }
    return value;
}

function digit_grouping(value, ext) {
    $CFG || ($CFG = {});
    $CFG.digit_grouping_char || ($CFG.digit_grouping_char = ',');
    var phancach = $CFG.digit_grouping_char;
    var thapphan = '.';
    phancach || (phancach == ',')
    if (phancach == '.') {
        thapphan = ',';
    }
    if (ext == undefined) {
        if (isNaN(value) || typeof value === 'undefined') {
            ext = '';
        } else {
            ext = 0;
        }
    }
    if (value == '' || value + '' === '0' || isNaN(value)) {
        if(ext==1){
            return '';
        }
        return ext;   
    }
    var num = 3;
    value || (value = 0);
    value += '';
    var test_symboy = (3 / 2) + '';
    var symboy = '.';
    var smb_float = ',';
    if (test_symboy.split('.').length > 1) {
        symboy = ',';
        smb_float = '.';
    }
    if (value.length <= 3) {
        value = value.replace(smb_float, thapphan);
        var valInt = parseInt(value);
        if(value==valInt && ext==1 && value!=0){
            value += thapphan+'0';
        }
        return value;
    } else {
        if (value.split(smb_float).length == 2) {
            if (value.split(smb_float)[0].length <= 3 && value.split(smb_float)[1].length <= 3) {
                value = value.replace(smb_float, thapphan);
                return value;
            }
        }
    }
    var dau = '';
    if (value.split('-').length > 1) {
        dau = '-';
        value = value.replace('-', '');
    }
    value = value.split(smb_float);
    var tmp = value[0];
    if (value.length == 1) {
        tmp = parseInt(value[0]) + '';
    }
    if (tmp.length > num) {
        var arrn = [];
        var d = 0;
        for (var i = tmp.length - 1; i >= 0; i--) {
            d++;
            arrn.push(tmp[i]);
            if (d % num == 0 && i != 0) {
                arrn.push(phancach);
            }
        }
        value[0] = '';
        for (var i = arrn.length - 1; i >= 0; i--) {
            value[0] += arrn[i];
        }
    }
    return dau + value.join(thapphan);
}
function refresh() {
    return $CFG.debug?(new Date()).getTime() : '';
}
function clone(obj) {
    if (typeof obj === 'object') {
        return JSON.parse(JSON.stringify(obj));
    }
}

/* Xử lý đọc số thành chữ */
var mangso = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];

function dochangchuc(so, daydu) {
    var chuoi = "";
    chuc = Math.floor(so / 10);
    donvi = so % 10;
    if (chuc > 1) {
        chuoi = " " + mangso[chuc] + " mươi";
        if (donvi == 1) {
            chuoi += " mốt";
        }
    } else if (chuc == 1) {
        chuoi = " mười";
        if (donvi == 1) {
            chuoi += " một";
        }
    } else if (daydu && donvi > 0) {
        chuoi = " lẻ";
    }
    if (donvi == 5 && chuc > 1) {
        chuoi += " lăm";
    } else if (donvi > 1 || (donvi == 1 && chuc == 0)) {
        chuoi += " " + mangso[donvi];
    }
    return chuoi;
}

function docblock(so, daydu) {
    var chuoi = "";
    tram = Math.floor(so / 100);
    so = so % 100;
    if (daydu || tram > 0) {
        chuoi = " " + mangso[tram] + " trăm";
        chuoi += dochangchuc(so, true);
    } else {
        chuoi = dochangchuc(so, false);
    }
    return chuoi;
}

function dochangtrieu(so, daydu) {
    var chuoi = "";
    trieu = Math.floor(so / 1000000);
    so = so % 1000000;
    if (trieu > 0) {
        chuoi = docblock(trieu, daydu) + " triệu";
        daydu = true;
    }
    nghin = Math.floor(so / 1000);
    so = so % 1000;
    if (nghin > 0) {
        chuoi += docblock(nghin, daydu) + " nghìn";
        daydu = true;
    }
    if (so > 0) {
        chuoi += docblock(so, daydu);
    }
    return chuoi;
}

function convert_number_to_words(so) {
    if (so == 0) return mangso[0];
    var chuoi = "", hauto = "", tiento = ' ', phancach = ' ';
    var list = [];
    if ((so + '').split('.').length > 1) {
        phancach = ' phảy';
        return convert_number_to_words((so + '').split('.')[0]) + phancach + convert_number_to_words((so + '').split('.')[1]);
    } else if ((so + '').split('-').length > 1) {
        tiento = 'Âm';
        return tiento + convert_number_to_words((so + '').split('-')[1]);
    } else {
        do {
            ty = so % 1000000000;
            so = Math.floor(so / 1000000000);
            if (so > 0) {
                chuoi = dochangtrieu(ty, true) + hauto + chuoi;
            } else {
                chuoi = dochangtrieu(ty, false) + hauto + chuoi;
            }
            hauto = " tỷ";
        } while (so > 0);
        return chuoi;
    }
    return chuoi;
}

function getURLParameter(sParam) {
    var sPageURL = window.location.search.substring(1);
    var sURLVariables = sPageURL.split('&');
    for (var i = 0; i < sURLVariables.length; i++) {
        var sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] == sParam) {
            return sParameterName[1];
        }
    }
}

function replaceUrlParam(url, paramName, paramValue) {
    if (paramValue == null) {
        paramValue = '';
    }
    var pattern = new RegExp('\\b(' + paramName + '=).*?(&|#|$)');
    if (url.search(pattern) >= 0) {
        return url.replace(pattern, '$1' + paramValue + '$2');
    }
    url = url.replace(/[?#]$/, '');
    return url + (url.indexOf('?') > 0 ? '&' : '?') + paramName + '=' + paramValue;
}

function exportExcel(grouping_char) {
    var cols = []; /* Init data */
    var colsData = $("[data-col]");
    var lengthCol = colsData.length;
    for (var c = 0; c < lengthCol; c++) {
        if (colsData[c].id !== 'undefined') {
            var colId = colsData[c].id;
            var colElement = document.getElementById(colId);
            var colValue = colElement.getAttribute("data-col");
            var colCell = colElement.getAttribute("data-cells");
            if (grouping_char===0 && $(colElement).attr('tag')==='excelnumber') {
                colValue = colValue.replaceAll($CFG.digit_grouping_char, '');
            }
            cols.push({key: colCell, value: colValue});
        }
    }

    var cellsExcel = []; /* Init data */
    var cellsData = $("[data-name='cell']");
    var lengthCell = cellsData.length;
    for (var i = 0; i < lengthCell; i++) {
        if (cellsData[i].id !== 'undefined') {
            var cellId = cellsData[i].id;
            var cellElement = document.getElementById(cellId);
            var cellValue = cellElement.innerText;
            if (cellElement.type === 'text') {
                cellValue = cellElement.value;
            }
            if (grouping_char===0 && $(cellElement).attr('tag')==='excelnumber') {
                cellValue = cellValue.replaceAll($CFG.digit_grouping_char, '');
            }
            var cellsExcelId = cellId.replace("cell-insert.", "");
            cellsExcel.push({key: cellsExcelId, value: cellValue});
        }
    }

    var rowsExcel = []; /* Init data */
    var rowsData = $("[data-name='row']");
    var lengthRow = rowsData.length;
    for (var j = 0; j < lengthRow; j++) {
        if (rowsData[j].id !== 'undefined') {
            var rowId = rowsData[j].id;
            var rowElement = document.getElementById(rowId);
            var rowValue = rowElement.innerText;
            if (rowElement.type === 'text') {
                rowValue = rowElement.value;
            }
            if (grouping_char===0 && $(rowElement).attr('tag')==='excelnumber') {
                rowValue = rowValue.replaceAll($CFG.digit_grouping_char, '');
            }
            var rowsExcelId = rowId.replace("row-insert.", "");
            rowsExcel.push({key: rowsExcelId, value: rowValue});
        }
    }

    var style = []; /* Init data style */
    var styleData = $("[data-cells]");
    var lengthStyle = styleData.length;
    for (var s = 0; s < lengthStyle; s++) {
        if (styleData[s].id !== 'undefined') {
            var styleId = styleData[s].id;
            var styleElement = document.getElementById(styleId);
            var styleCell = styleElement.getAttribute("data-cells");
            var styleOptions = styleElement.getAttribute("data-options");
            var splitOptions = styleOptions.split(",");

            /*font*/
            var font = {};
            if (splitOptions.indexOf('bold') !== -1)
                font['bold'] = true;
            if (splitOptions.indexOf('italic') !== -1)
                font['italic'] = true;
            var tmp = {key: styleCell, font: font};

            /*merge*/
            if (splitOptions.indexOf('merge') !== -1)
                tmp['merge'] = true;

            /*alignment*/
            if (splitOptions.indexOf('center') !== -1)
                tmp['alignment'] = {'center': true};
            if (splitOptions.indexOf('left') !== -1)
                tmp['alignment'] = {'left': true};
            if (splitOptions.indexOf('right') !== -1)
                tmp['alignment'] = {'right': true};
            style.push(tmp);
        }
    }
    return {
        cells: JSON.stringify(cellsExcel),
        rows: JSON.stringify(rowsExcel),
        style: JSON.stringify(style),
        cols: JSON.stringify(cols),
    };

}


/*Excel new*/

/* attribute: data-row */
function getDataExcelByAttribute(attribute) {
    var data = [];
    var selectors = $('[' + attribute + ']');
    var length = selectors.length;
    for (var i = 0; i < length; i++) {
        var selector = selectors[i];
        var cell = selector.getAttribute(attribute);
        var value = selector.innerText;
        if (selector.type === 'text') {
            value = selector.value;
        }
        data.push({cell: cell, value: value})
    }
    return data;
}

function getDate(strDate) {
    var date = new Date();
    if (strDate)
        date = new Date(strDate);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return (d < 10 ? ('0' + d) : d) + '/' + (m < 10 ? ('0' + m) : m) + '/' + y;
}

function getCurrentDate(strDate) {
    var date = new Date();
    if (strDate)
        date = new Date(strDate);
    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    var first = getDayMonthYear(firstDay);
    var now = getDayMonthYear(date);
    var last = getDayMonthYear(lastDay);

    return {
        first: first.day + '/' + first.month + '/' + first.year,
        now: now.day + '/' + now.month + '/' + now.year,
        last: last.day + '/' + last.month + '/' + last.year,
    };
}

function getDayMonthYear(dateObject) {
    var y = dateObject.getFullYear();
    var m = dateObject.getMonth() + 1;
    var d = dateObject.getDate();
    d = (d < 10 ? ('0' + d) : d);
    m = (m < 10 ? ('0' + m) : m);
    return {
        day: d,
        month: m,
        year: y
    };
}
function lazyLoadingJS (url, callback) {
    statusloading();
    return $.ajax({
        url: url,
        dataType: 'script',
        async: true,
        cache: false, // or get new, fresh copy on every page load
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function() {
            if (typeof callback === 'function') {
                callback();
            }
            statusloadingclose();
        }, error: function () {
            statusloadingclose();
        }
    });
};
$.angularComplie = function (element, html, callback) { /* Biên dịch html cho popup để chạy angularjs*/
    var form = '<div >' + html + '</div>';
    // $(element).html(form);
    angular.element($(element)).scope().$apply(function (scope) {
        $(element).html(scope.compile(form, scope));
        if (typeof callback === 'function') {
            callback(scope);
        }
    });
};

/* Export PDF from HTML */
function exportPDFfromHTML(element_id, file_name, return_base64 = false, filter, type_cks = '1') {
    var HTML_Width = $("#" + element_id).width();
    var HTML_Height = $("#" + element_id).height();
    var top_left_margin = 15;
    var PDF_Width = HTML_Width + (top_left_margin * 2);
    var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
    var canvas_image_width = HTML_Width;
    var canvas_image_height = HTML_Height;

    var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;

    var btn_eles = document.querySelectorAll('.btn-primary.btn-sm');
    btn_eles.forEach(btn => {
        btn.style.display = 'none';
    })

    html2canvas($("#" + element_id)[0]).then(function (canvas) {
        var imgData = canvas.toDataURL("image/jpeg", 1.0);
        var pdf = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);
        pdf.addImage(imgData, 'JPG', top_left_margin, top_left_margin, canvas_image_width, canvas_image_height);
        for (var i = 1; i <= totalPDFPages; i++) { 
            pdf.addPage(PDF_Width, PDF_Height);
            pdf.addImage(imgData, 'JPG', top_left_margin, -(PDF_Height*i)+(top_left_margin*4),canvas_image_width,canvas_image_height);
        }
        if(return_base64 == true){
            var base = pdf.output('datauristring');
            var tmp_arr = base.split(',');
            // Post to save pdf
            sign_viettel_ca_pdf(tmp_arr[1], file_name, totalPDFPages+1, filter, type_cks);
        }else{
            pdf.save(file_name + ".pdf");
        // Chỉ khi dán xong chữ ký ảnh mới up lên S3
            // if(check_input_sign_img){
            //     saveReportToCloud(false, pdf);
            // }
            $("#" + element_id).hide();
        }
        // pdf.save(file_name + ".pdf");
        btn_eles.forEach(btn => {
            btn.style.display = 'inline';
        })
        
    });
}

function get_status_vnpt_ca(formData) {
    $.ajax({
        type: "POST",
        datatype: "json",
        data: formData,
        url: location.origin + '/api/vnpt_ca/get_status_vnpt_ca',
        crossDomain: true,
        async: true,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function (rs) {
            if (rs.status == 1) {
                alert(rs.message);
                window.open(rs.signed_file_url);
            }
            else {
                alert("Ký số không thành công!")
            }
        }
    })
}

function sign_viettel_ca_pdf(filebase64, file_name, number_page, filter, type_cks = '1') {
    var formData = {
        filebase64: filebase64,
        file_name: file_name,
        signed_report: filter.module,
        number_page: number_page,
        filter: filter,
    }
    const controller = type_cks == '1' ? 'viettel_ca' : 'vnpt_ca';
    $.ajax({
        type: "POST",
        datatype: "json",
        data: formData,
        url: location.origin + '/api/'+ controller +'/sign_pdf_base64_no_display',
        crossDomain: true,
        async: true,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        success: function (rs) {
            if (type_cks == '1') {
                if(rs.status==1){
                    insertedDigitalSign = rs.inserted
                    $('#lbl_sign_text').html('KÝ SỐ VIETTEL-CA');
                    alert(rs.message);
                    window.open(rs.signed_file_url, '_blank');
                }else {
                    alert("Đã có lỗi xảy ra! Ký số không thành công!");
                }
            }
            else {
                if (rs.status_code == 200) {
                    if (confirm('Bạn đã xác nhận ký số trên App chưa ?')) {
                        formData.transaction_id = rs.data.transaction_id;
                        formData.file_id = rs.fileID;
                        delete formData.filebase64;
                        get_status_vnpt_ca(formData);
                    }
                }
                else {
                    alert("Đã có lỗi xảy ra! Ký số không thành công!");
                }
            }
        },
        error: function () {

        }
    })
}

const findUnitSignConfig = async (
    {
        module,
        projectId = null,
        groupId = null,
        courseId = null,
        date = null,
        month = null,
        schoolYear = null,
    }
)=> {
    const data = {
        module: module
    }

    if(projectId !== null && projectId !== undefined) {
        data.project_id = projectId;
    }

    if(groupId !== null && groupId !== undefined) {
        data.group_id = groupId;
    }

    if(courseId !== null && courseId !== undefined) {
        data.course_id = courseId;
    }

    if(date !== null && date !== undefined) {
        data.date = date;
    }

    if(month !== null && month !== undefined) {
        data.month = month;
    }

    if(schoolYear !== null && schoolYear !== undefined) {
        data.schoolyear = schoolYear;
    }

    const url = location.origin + '/doing/admin/user/findUnitSignConfig';

    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        body: JSON.stringify(data)
    });

    return await response.json();
}

const saveUnitSignConfig = async (
    {
        module,
        type,
        projectId = null,
        groupId = null,
        courseId = null,
        date = null,
        month = null,
        schoolYear = null,
    }
) => {
    const data = {
        module: module,
        type: type
    }

    if(projectId !== null && projectId !== undefined) {
        data.project_id = projectId;
    }

    if(groupId !== null && groupId !== undefined) {
        data.group_id = groupId;
    }

    if(courseId !== null && courseId !== undefined) {
        data.course_id = courseId;
    }

    if(date !== null && date !== undefined) {
        data.date = date;
    }

    if(month !== null && month !== undefined) {
        data.month = month;
    }

    if(schoolYear !== null && schoolYear !== undefined) {
        data.schoolyear = schoolYear;
    }

    // console.log(data);

    const url = location.origin + '/doing/admin/user/saveUnitSignConfig';

    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="X-CSRF-TOKEN"]').attr('content')
        },
        body: JSON.stringify(data)
    });

    return await response.json();
}

const buildUnitSignImageUrl = (baseUrl, unitId, filename) => {
    if (filename.split('/').length > 1) {
        return filename + '?v=' + new Date().getTime();
    }
    return  baseUrl + '/images/signs/' + unitId + '/' + filename + '?v=' + new Date().getTime();
}

const snakeCase = (string, delimiter = '_') => {
    return string.replaceAll(/(.)(?=[A-Z])/g, match => match + delimiter).toLowerCase();
}

const fillObject = (
    source,
    destination,
    keyTransformer = key => key,
    valueTransformer = value => value
) => {
    Object.keys(destination).forEach(destinationKey => {
        if (typeof source !== 'object') {
            throw new TypeError('Source must be object');
        }
        if (typeof destination !== 'object') {
            throw new TypeError('Destination must be object');
        }
        if (typeof keyTransformer !== 'function') {
            throw new TypeError('KeyTransformer must be function');
        }
        if (typeof valueTransformer !== 'function') {
            throw new TypeError('ValueTransformer must be function');
        }

        const sourceKey = keyTransformer(destinationKey);

        if (source.hasOwnProperty(sourceKey)) {
            if (typeof source[sourceKey] === 'object' && typeof destination[destinationKey] === 'object') {
                fillObject(source[sourceKey], destination[destinationKey], keyTransformer, valueTransformer);
            } else {
                destination[destinationKey] = valueTransformer(source[sourceKey]);
            }
        }
    });
}

const deepCopy = source => JSON.parse(JSON.stringify(source));

const getQueryParam = (key = null, defaultValue = null, fromUrl = null) => {
    const url = (fromUrl !== null) ? new URL(fromUrl) : new URL(window.location.href);

    if(key === null) {
        const queryParams = {};

        for (const [paramKey, paramValue] of url.searchParams) {
            queryParams[paramKey] = paramValue;
        }

        return queryParams;
    }

    return url.searchParams.get(key) ?? defaultValue;
}

function formatDateForSigns (date_from, date_to) {
    let fromDate = new Date(date_from);
    let toDate = new Date(date_to);

    let fromDateString = `${fromDate.getDate()}/${fromDate.getMonth() + 1}/${fromDate.getFullYear()}`;
    let toDateString = `${toDate.getDate()}/${toDate.getMonth() + 1}/${toDate.getFullYear()}`;

    let fromTimeString = `${fromDate.getHours()}:${fromDate.getMinutes()}`;
    let toTimeString = `${toDate.getHours()}:${toDate.getMinutes()}`;

    return `${fromDateString} - ${toDateString} ${toTimeString}`;
}

/**
 * Phân bố chiều rộng của bảng cho các cột để xác định chiều rộng của mỗi cột.
 * Nếu chiều rộng không chia hết cho số lượng cột, số dư sẽ phát sinh.
 * Số dư sẽ được phân cho các cột (số dư là số nguyên và không vượt quá số lượng cột,
 * vì vậy mỗi cột có thể được cộng thêm một đơn vị chiều rộng).
 * @param {number} tableWidth - Chiều rộng của bảng.
 * @param {number} numberOfColumns - Số lượng cột.
 * @param {boolean} reverse - Chiều phân bố số dư
 * @returns {number[]} - Mảng chứa chiều rộng của các cột.
 */
function calculateColumnWidth(tableWidth, numberOfColumns, reverse = false) {
    // Tính chiều rộng nguyên của mỗi cột và số dư
    const columnWidth = Math.floor(tableWidth / numberOfColumns);
    const remainder = tableWidth % numberOfColumns;

    // Tạo một mảng với các phần tử có giá trị ban đầu là chiều rộng nguyên của mỗi cột
    const widths = new Array(numberOfColumns).fill(columnWidth);

    // Phân phối số dư cho các cột
    const startIndex = reverse ? numberOfColumns - remainder : 0;
    const endIndex = reverse ? numberOfColumns : remainder;

    for (let i = startIndex; i < endIndex; i++) {
        widths[i]++;
    }

    return widths;
}

function getSignPositionsInReport(module, dateFrom, dateTo) {
    return process($CFG.remote.base_url+'/doing/admin/user/getSignPosition',{module: module, date_from: dateFrom, date_to: dateTo});
}

function convertDate2DB(date) {
    if (date) {
        var d = date.split("/");
        return d[2]+"-"+d[1]+"-"+d[0];
    }else{
        return '';
    }
}

function removeAllSpecificChars(str) {
    return str.replace(/[!@#$%^&*()_+{}\[\]:;"'<>,.?\/\\|`~]/g, '');
}
