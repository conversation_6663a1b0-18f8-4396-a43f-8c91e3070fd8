html {
	overflow: hidden;
}
body{
	font-family: 'Roboto', sans-serif !important;
	color: #4d575c !important;
}
.page-wrapper{
	padding-bottom: 0px !important;
}
[class^="ti-"], [class*=" ti-"] {
	line-height: 43px;
}
.form-group {
	margin-bottom: 15px;
}
.main-content-view > div.col-12 div.card{
	height: 100%;
	margin-bottom: 0;
}
.main-content-view > div.col-12 {
	height: 100%;
	padding: 0;
}
.main-content-view {
	height: calc(100vh - 150px);
	display: inline-block;
	overflow: auto;
	width: 100%;
}
.card-body.angular-view-container {
	padding: 15px;
	padding-bottom: 0px;
}
.page-titles .breadcrumb .breadcrumb-item.active {
	color: #009eb2 !important;
	font-weight: bold;
}
.page-titles .breadcrumb {
	padding: .75rem 1rem !important;
}
.page-titles{
	padding: 0px;
	margin-bottom: 15px;
	margin-left: -15px;
}
.container-fluid{
	padding-left: 15px;
	padding-right: 15px;
}
.notify {
	top: -10px;
}
.mini-sidebar .user-profile.bg-image {
	display: none !important;
}
.mini-sidebar span.mini-hide{
	display: none !important;
}
.school-year-selected a.dropdown-toggle{
	font-weight: bold;
	background: rgb(38, 198, 218) !important;
}
.school-year-selected li a.active{
	color: orange;
}
/*CAN DO HOC SINH*/
.shw-rside{
	left: 0px !important;
}
.btn-circle.right-side-toggle{
	left: 20px;
	z-index: 20;
}
.phanhe{
	position: fixed;
	bottom: 20px;
	right: 60px;
	padding: 25px;
	left: 20px;
	z-index: 20;
}
.phanhe p{
	padding-left: 50px;
	margin-bottom: -14px;
	color: #333;
	font-weight: bold;
}
.topbar{
	background: #26c6da !important;
}
.topbar .top-navbar .navbar-nav>.nav-item>.nav-link {
	line-height: inherit !important;
}
.bg-image{
	height: 150px;
}
.sidebar-nav .has-arrow::after{
	display: none;
}
.name_school p {
    margin-top: 20px;
    color: #fff;
    font-weight: bold;
    margin-left: 15px;
}
.icon_school{
	margin-top: 11px;
}
.namhoc{
	position: absolute;
	bottom: 0;
	color: #ffffff !important;
width: 100%;
padding: 6px 30px;
background: rgba(0, 0, 0, 0.5);
display: block;
}
.text-themecolor-new{
	color: #009eb2 !important; 
}
.chisochuan img{
	padding-right: 10px;
}
.chisochuan{
	background: #fff !important;
	border: none;
	color: #009eb2 !important;
}
.chisochuan:hover{
 color: #444;
}
.table td, .table th{
	border: 1px solid #4444 !important;
	padding: 3px;
}
.table .table{
	margin-bottom: 0px;
	font-size: 14px;
	border: 1px solid #ececec;
}
.jsgrid-grid-body {
    overflow-x: auto !important;
    overflow-y: scroll !important;
}
.table_header{
	background: #eaeaea !important;
	font-weight: bold;
}
.jsgrid-cell a{
	color: #01adc3;
}
.jsgrid-cell a:hover{
	color: #333;
}

.form-group.lop{
	margin-left: 10px;
}
.taifilemau{
	color: #29a463;
	background: #fff;
	 float: left;
	 margin-right: 20px;
}
.taifilemau:hover{
	color: #333;
}
.taifilemau i{
	font-size: 20px;
}
.input_taifile{
	float: left;
	width: 70%;
}
.btn-file{
	background: #29a463 !important;
}
.card_upload{
	border: 1px solid #4444;
    padding: 10px;
}
.card_upload .col-md-7{
	border-right: 1px solid #4444;
}
.card_upload .col-md-5{
	width: 100%;
}
.div_loc{
	width: 40%;
	float: left;
}
.div_search input{
	text-align: center;
}
.div_search .fas.fa-search{
	position: absolute;
	right: 4px;
	top: 4px;
	opacity: 0.6;
}
.div_search{
	width: 100%;
	float: left;
	border: 1px solid #4444;
	border-radius: 5px;
	position: relative;
}
.div_loc select{
	border: none !important;
}
.jsgrid-pager-page{
	padding: .2em .6em;
}
.abc{
	text-align: center;
}
.pager div{
	float: left;
}

.save{
	margin-left: 0px;
	color: white;
}
/*END-CAN DO HOC SINH
------------------------*/

/*CAN DO HOC SINH _ CHI TIET*/
.fre{
	float: left;
	font-size: 30px;
	margin-top: -8px;
}
.info-student{
	background: #eaf5e4;
	border-top: 1px solid #cde6bf;
	margin:  15px 0px 50px 0px;

}
.info-student-right{
	border-left: 1px solid #cde6bf;
	padding: 10px 15px;
}
.info-student-left{
	padding: 20px 15px;
}
.info-student-left img{
	float: left;
	margin-right: 10px;
	margin-top: -6px;
}
.tl_name{
	color: #69aa46;
	font-size: 20px;
	font-weight: bold;
}
.info-student-left p{
	padding-left: 45px;
}
.info-student-right span{
	font-weight: bold;
}
.info-student-right ul {
  list-style: none;
}

.info-student-right ul li::before {
  content: "\2022";
  color: #60a33f;
  font-weight: bold;
  display: inline-block; 
  width: 1em;
  margin-left: -3em;
}
.card-title{
	color: #0091a3;
}
.card-title:hover{
	color: #353535;
}
.table_chitiet tbody tr:nth-of-type(2n+1){
	background: #fff !important;
}
.table_chitiet{
	margin-bottom: 20px;
}
.tl_header th{
	font-weight: bold !important;
}
.card-body .fre{
	color: #555;
}
.hr{
	height: 7px;
	background: #eeeeee;
	border-top: 1px solid #e3e3e3;
	margin: 40px 0px; 
}
.tl_danhgia{
	border-left: 5px solid #26c6da;
	color: #26c6da;
	font-weight:bold;
	margin-bottom: 15px;
}
.tl_danhgia p{
	padding-left: 10px;
}
.tl_thang{
	text-align: center;
}
.chart_cannang .ct-animation-chart .ct-series-a .ct-line, .chart_cannang .ct-animation-chart .ct-series-b .ct-line, .chart_cannang .ct-animation-chart .ct-series-c .ct-line,.chart_cannang .ct-animation-chart .ct-series-d .ct-line,.chart_cannang .ct-animation-chart .ct-series-e .ct-line{
	stroke: none;
}
.chart_cannang .ct-series-a .ct-area{
	fill: #009efb;
}
.chart_cannang .ct-series-f .ct-area{
	fill: none;
}
.chart_cannang .ct-series-f .ct-point{
	stroke: #26c6da;
}
.tl_month{
text-align: center;
}
.list_note i{
	margin-right: 10px;
}
.thua3{
	color: #99FF99;
}
.thua2{
	color: #FFFF99;
}
.bt{
	color: #00CCFF;
}
.suy2{
	color: #FF9900;
}
.suy3{
	color: #FF6600;
}
.hocsinh{
	color: #FF3399;
}
.table-striped tbody tr:nth-of-type(odd), .table-striped tbody tr:nth-of-type(odd) .absolute {
	background-color: #fff;
}
.table-striped tbody tr:nth-of-type(even), .table-striped tbody tr:nth-of-type(even) .absolute {
	background-color: #f1f1f1;
}
/*KET QUA CAN NANG*/
.kqcn .div_search{
	margin-right: 5px;
}
.tl_baocao{
	color: #0090a2;
	font-weight: bold !important;
}

.table .form-control {
	min-height: 24px !important;
	padding: 0px !important;
	line-height: 20px !important;
}
.table [type=checkbox]+label {
	padding-left: 20px;
	height: 20px;
	line-height: 20px;
	margin-bottom: 0;
}
table.table thead tr th{
	text-align: center;
	vertical-align: middle;
	font-weight: bold;
}
.color-orange{
	color: orange;
}
.font-size-11{
	font-size: 11px;
}
.font-size-12{
	font-size: 12px;
}
.btn.disabled, .btn:disabled {
	opacity: 0.3;
	border: 0px solid;
}
.btn-icon{
	cursor: pointer;
}
.btn-icon:hover{
	color: orange;
}


/*----BEGIN CAN DO HOC SINH-------*/
.fade:not(.show){
	opacity: 1;
	background: rgb(1,1,1,0.5);
}
.modal-header {
	display: block;
}
/*----END CAN DO HOC SINH-------*/
/*----BEGIN CAN DO KET QUA CAN NANG-------*/
#for_weight table th{
	color: #26c6da;
	font-size: 15px;
	/*border-radius: 4px;*/
	white-space: nowrap;
}
#for_weight table *{
	font-weight: normal;
}
/*----END CAN DO KET QUA CAN NANG-------*/

.table-bmi-for-weight-yearly-report .student-bmi-yearly-history--stt,
.table-bmi-for-height-yearly-report .student-bmi-yearly-history--stt,
.table-student-bmi-yearly-history .student-bmi-yearly-history--stt {
	min-width: 60px !important;
	word-break: break-all !important;
}

.table-bmi-for-weight-yearly-report .student-bmi-yearly-history--full-name,
.table-bmi-for-height-yearly-report .student-bmi-yearly-history--full-name,
.table-student-bmi-yearly-history .student-bmi-yearly-history--full-name {
	word-break: break-all !important;
	min-width: 200px !important;
}

.table-bmi-for-weight-yearly-report .student-bmi-yearly-history--birthday,
.table-bmi-for-height-yearly-report .student-bmi-yearly-history--birthday,
.table-student-bmi-yearly-history .student-bmi-yearly-history--birthday {
	min-width: 130px !important;
}

.table-bmi-for-weight-yearly-report .student-bmi-yearly-history--month--weight,
.table-bmi-for-height-yearly-report .student-bmi-yearly-history--month--height,
.table-student-bmi-yearly-history .student-bmi-yearly-history--month--height,
.table-student-bmi-yearly-history .student-bmi-yearly-history--month--weight {
	min-width: 50px !important;
}

.table-bmi {
	max-height: calc(100vh - 278px);
	overflow: auto;
	width: 100%;
	border: 1px solid #cccccc;
}

.table-bmi thead > tr {
	background-color: #eaeaea !important;
}

.table-bmi thead > tr > th {
	white-space: nowrap;
	border: 1px solid #cccccc !important;
}

.table-report-by-grade .table-report-by-grade--stt {
	min-width: 70px;
	width: 70px;
}

.table-report-by-grade .table-report-by-grade--grade-name {
	min-width: 150px;
}

.table-report-by-grade .table-report-by-grade--si-so {
	min-width: 80px;
	width: 80px;
}

.table-report-by-grade .table-report-by-grade--duoc-can {
	min-width: 80px;
	width: 80px;
}

.table-report-by-grade .table-report-by-grade--ty-le {
	min-width: 80px;
	width: 80px;
}

.table-report-by-grade .table-report-by-grade--can-nang {
	min-width: 420px;
}

.table-report-by-grade .table-report-by-grade--can-nang--bt,
.table-report-by-grade .table-report-by-grade--can-nang--nc,
.table-report-by-grade .table-report-by-grade--can-nang--ty-le,
.table-report-by-grade .table-report-by-grade--can-nang--dc-bp {
	min-width: 70px;
	width: 70px;
}

.table-report-by-grade .table-report-by-grade--ket-qua-can-do {
	min-width: 420px;
}

.table-report-by-grade .table-report-by-grade--ket-qua-can-do--vao,
.table-report-by-grade .table-report-by-grade--ket-qua-can-do--giam,
.table-report-by-grade .table-report-by-grade--ket-qua-can-do--ti-le {
	min-width: 70px;
	width: 70px;
}

.table-bmi-dashboard thead > tr > th {
    color: #4d575c !important;
    font-weight: bold !important;
}

.table-bmi-dashboard .table-bmi-dashboard--stt {
    min-width: 50px;
    width: 50px;
}

.table-bmi-dashboard .table-bmi-dashboard--full-name {
    min-width: 230px;
    width: 230px;
}

.table-bmi-dashboard .table-bmi-dashboard--student-id {
    min-width: 70px;
    width: 70px;
}

.table-bmi-dashboard .table-bmi-dashboard--birthday {
    min-width: 100px;
    width: 100px;
}

.table-bmi-dashboard .table-bmi-dashboard--month-old {
    min-width: 80px;
    width: 80px;
}

.table-bmi-dashboard .table-bmi-dashboard--gender {
    min-width: 50px;
    width: 50px;
}

.table-bmi-dashboard .table-bmi-dashboard--weight,
.table-bmi-dashboard .table-bmi-dashboard--height,
.table-bmi-dashboard .table-bmi-dashboard--beat-heart {
    min-width: 80px;
    width: 80px;
}

.table-bmi-dashboard td.table-bmi-dashboard--beat-heart,
.table-bmi-dashboard td.table-bmi-dashboard--blood-pressure,
.table-bmi-dashboard td.table-bmi-dashboard--eye-sight {
	background-color: #d4f4f8 !important;
}

.table-bmi-dashboard .table-bmi-dashboard--weight-result,
.table-bmi-dashboard .table-bmi-dashboard--height-result,
.table-bmi-dashboard .table-bmi-dashboard--bmi-result {
    min-width: 60px;
    width: 60px;
}

.table-bmi-dashboard .table-bmi-dashboard--blood-pressure,
.table-bmi-dashboard .table-bmi-dashboard--eye-sight {
	min-width: 160px;
    width: 160px;
}

.table-bmi-dashboard .table-bmi-dashboard--bmi {
    min-width: 80px;
    width: 80px;
}

.table-bmi-dashboard .table-bmi-dashboard--bmi-rate {
    min-width: 400px;
    width: 400px;
}

.table-bmi-dashboard .table-bmi-dashboard--note, .table-bmi-dashboard .table-bmi-dashboard--note-after {
    min-width: 300px;
    width: 300px;
}

.table-bmi-dashboard .table-bmi-dashboard--detail {
    min-width: 30px;
    width: 30px;
	padding-top:5px;
}

.table-bmi-dashboard td.table-bmi-dashboard--weight {
    background-color: #d2e7fa !important;
}

.table-bmi-dashboard td.table-bmi-dashboard--height {
    background-color: #d4f4f8 !important;
}

.table-bmi-dashboard td.table-bmi-dashboard--note {
    /*background-color: #d6f1f7 !important;*/
}

.custom-input-group.is-invalid {
	border: 1px solid var(--danger);
}

.custom-input-group {
	width: 100%;
	display: flex;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	background-color: var(--white);
	border: 1px solid #ced4da;
	border-radius: .25rem;
}

.custom-input-group .form-control {
	border: 0;
	border-radius: 0;
	margin-left: .25rem;
	min-height: 25px !important;
	padding: 0 !important;
	-moz-appearance: textfield;
	/*max-width: 70px !important;*/
}

.custom-input-group .form-control:last-child {
	margin-right: .25rem;
}

.custom-input-group .form-control:invalid {
	box-shadow: none;
}

.custom-input-group .form-control:focus {
	outline: none;
	box-shadow: none
}

.custom-input-group .form-control::-webkit-inner-spin-button,
.custom-input-group .form-control::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.custom-input-group .btn-reload {
	cursor: pointer;
	margin-right: .25rem;
}

.custom-input-group .btn-reload:hover {
	color: var(--primary);
}

.table-list-disease {
	max-height: calc(100vh - 230px) !important;
}

.table-student-health th,
.table-student-health td {
	vertical-align: middle !important;
}

.table-student-health [type=checkbox] {
	position: initial !important;
	opacity: 1 !important;
}

.table-student-health--full-name {
	min-width: 200px !important;
	white-space: initial !important;
	word-break: break-word !important;
}

.table-student-health--disease {
	width: 200px !important;
	max-width: 200px !important;
	min-width: 200px !important;
	white-space: initial !important;
	word-break: break-word !important;
}

.table-student-health--note {
	width: 200px !important;
	max-width: 200px !important;
	min-width: 200px !important;
}

.absolute {
	position: absolute;
	z-index: 1;
	width: inherit;
	margin-left: -4px;
}

.tr1-cell-fixed {
	height: 32px;
	top: 0;
	line-height: 32px;
	background: rgb(234, 234, 234) !important;
	border-right:1px solid rgb(190, 190, 190) !important;
	border-top:1px solid rgb(190, 190, 190) !important;
}

.tr2-cell-fixed {
	height: 55px;
    top: 0px;
    line-height: 55px;
    margin-left: -4px;
    background: rgb(234, 234, 234) !important;
    border-right:1px solid rgb(190, 190, 190) !important;
	border-top:1px solid rgb(190, 190, 190) !important;
}

.tbody-cell-fixed {
	padding: 3px;
	height: 34px;
	margin-top: -3px;
	border-right: 1px solid rgb(190, 190, 190) !important;
	border-bottom: 1px solid rgb(190, 190, 190) !important;
}

#student-bmi-table tbody tr {
	height: 34px
}

.table-bmi-dashboard--stt .absolute, .table-bmi-dashboard--info .absolute {
	border-left: 1px solid rgb(190, 190, 190) !important;
}

.school {
	width: 610px;
    min-width: 610px;
}

.class {
	width: 510px;
    min-width: 510px;
}
.disabled {
	background-color: #e9ecef;
}

.table-student-health--gender {
	min-width: 75px;
	width: 75px;
}

.th-fixed {
	top: 0;
	height: 56px;
	line-height: 56px;
	background: rgb(234, 234, 234) !important;
    border-right: 1px solid rgb(190, 190, 190) !important;
    border-top: 1px solid rgb(190, 190, 190) !important;
}

.td-fixed {
	height: 71px;
	line-height: 71px;
	border-right: 1px solid rgb(190, 190, 190) !important;
	border-bottom: 1px solid rgb(190, 190, 190) !important;
	margin-top: -35px;
}

.td-fixed-health {
	height: 231px;
	line-height: 231px;
	border-right: 1px solid rgb(190, 190, 190) !important;
	border-bottom: 1px solid rgb(190, 190, 190) !important;
	margin-top: -115px;
}

.table-bmi-dashboard--full-name .absolute {
	padding-left: 3px;
	padding-right: 3px;
}