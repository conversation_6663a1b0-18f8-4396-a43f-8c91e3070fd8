$.config = {
    module: 'config',
    init: function() {
        var self = this;
            process($CFG.project+'/'+self.module+'/userx',{},function(resp){
                // console.log(resp);
                self.config = resp.data.config;
                self.school_points = resp.data.school_points;
                self.school_point = resp.data.school_point;
                self.digit_grouping_char = resp.data.digit_grouping_char;
                if (self.config.attending_milestone_time != undefined) {
                    $('.timepicker').timepicker('setTime', self.config.attending_milestone_time);
                }
                if (self.config.attending_begin_time == undefined) {
                    self.config.attending_begin_time = '07:00 AM';
                }
                $.config.initAngular();
           },null,false);
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope) {
                scope.config = self.config;
                scope.config.school_points = self.school_points;
                scope.config.school_point = ''+self.school_point;
                if(!scope.config.school_points) {
                    scope.config.school_points = 1;
                }
                if(!scope.config.school_point) {
                    scope.config.school_point = 1;
                }
                scope.config.arSps = {};
                // for (i = 0; i < scope.config.school_points; i++) {
                //     scope.config.arSps[i] = {};
                //     scope.config.arSps[i]['name'] = i+1;
                //     scope.config.arSps[i]['value'] = i+1;
                // }
                // angular.forEach(scope.config.arSps, function(arSps, key) {
                //     if(scope.config.school_point == arSps['value']){
                //         scope.config.spselected = scope.config.arSps[key];
                //     }
                // })
                // scope.config.onChangeSP = function(){
                //     if(scope.config.school_points < 1 || scope.config.school_points == undefined){
                //         scope.config.school_points = 1;
                //     }else{
                //         scope.config.arSps = {};
                //         for (i = 0; i < scope.config.school_points; i++) {
                //             scope.config.arSps[i] = {};
                //             scope.config.arSps[i]['name'] = i+1;
                //             scope.config.arSps[i]['value'] = i+1;
                //         }
                //         angular.forEach(scope.config.arSps, function(arSps, key) {
                //             // console.log(scope.config.arSps[0]);
                //             if(scope.config.school_point == arSps['value']){
                //                 scope.config.spselected = scope.config.arSps[key];
                //             }else if(scope.config.school_point > scope.config.school_points){
                //                 scope.config.spselected = scope.config.arSps[0];
                //             }
                //         })
                //     }
                    
                // };
                scope.config.getArrSchoolpoint = function(schoolpoints){
                    var ar = [];
                    for (i = 0; i < scope.config.school_points; i++) {
                        ar.push(i+1);
                    }
                    return ar;
                }

               $('.timepicker').timepicker({ step: 5 , timeFormat: 'h:i A'});
            });
        });
    },
    angular: function(element,resp,callback,dialogRef) {
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope) {
            $(element).html(scope.compile(form,scope));
            if (typeof callback === 'function') {
                callback(scope);
            }
        });
    },
    saveButton: function() {
        data = arrayToJson(getSubmitForm('tbl_config',true));
        process('dinhduong/config/list',{data:data},function(resp){
            if (resp.result=='success') {
				alert("Lưu lại thành công");
				window.location.href = $CFG.remote.base_url + '/single/dinhduong/config/list';
            }else{
                alert("Điểm trường không được để trống");
            }
        });
    }
}
