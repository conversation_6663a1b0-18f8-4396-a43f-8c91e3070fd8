<div ng-controller="StudentHealthDashboardController as vm" class="position-relative w-100 h-100 mh-100">
    <div class="row">
        <div class="col-md-12 d-flex align-items-center">
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-options="year.id as year.title for year in vm.years"
                        ng-model="vm.filters.school_year"
                        ng-change="vm.onSchoolYearChange()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-options="month.id as month.title for month in vm.months"
                        ng-model="vm.filters.month"
                        ng-change="vm.onMonthChange()">
                </select>
            </div>
            <div class="form-group m-0 mr-2">
                <select class="form-control custom-select"
                        ng-change="vm.onCourseChange()"
                        ng-model="vm.filters.course_id">
                    <option ng-repeat="grade in vm.courses"
                            ng-value="grade.id" ng-if="!grade.courses">
                        {{grade.name}}
                    </option>
                    <optgroup ng-repeat="grade in vm.courses" label="{{grade.name}}" ng-if="grade.courses">
                        <option ng-repeat="course in grade.courses.data" ng-value="course.id">{{course.name}}</option>
                    </optgroup>
                </select>
            </div>
			<div><span ng-if="vm.diseases.length==0" style="color:orange;">Lưu ý: Bạn chưa thêm danh mục quản lý nhóm bệnh?</span></div>
            <button class="btn btn-secondary btn-sm"
                    ng-click="vm.reload()"
                    ng-if="vm.students">
                <i class="btn-icon btn-reload mdi mdi-reload"></i>
                Tải lại dữ liệu
            </button>
			<a class="has-arrow waves-effect waves-dark ml-auto" href="cando/view/student_temperature/dashboard">
				<i class="mdi mdi-temperature-celsius" style="color:orange;"></i>  Nhiệt độ của học sinh
			</a>
        </div>
    </div>

    <div id="student-health" class="h-100 table-responsive mt-2 table-bmi table-bmi-dashboard table-student-health" style="position: relative;">
        <form name="vm.studentBmiForm">
            <table id="student-health-table" class="jsgrid-table table table-striped table-hover">
                <thead style="position: absolute;z-index: 1;">
                <tr style="position: relative;height: 56px">
                    <th class="table-bmi-dashboard--stt">
                        <div class="absolute th-fixed">STT</div>
                    </th>
                    <th class="table-bmi-dashboard--full-name">
                        <div class="absolute th-fixed" style="padding-top: 14px">
                            <div class="div_search">
                                <input type="text" class="form-control"
                                       placeholder="Họ tên"
                                       ng-model-options="{ debounce: 1000 }"
                                       ng-model="vm.filters.keyword"
                                       ng-change="vm.onKeywordChange()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </th>
                    <th class="table-bmi-dashboard--student-id">
                        <div class="absolute th-fixed">Mã HS</div>
                    </th>
                    <th class="table-bmi-dashboard--birthday">Ngày sinh</th>
                    <th class="table-student-health--gender">Giới Tính</th>
                    <th class="{{ detachedZScore ? 'width-diseases' : ''}} table-student-health--disease" ng-repeat="disease in vm.diseases">{{ disease.name }}</th>
                    <th class="table-student-health--note" ng-if="!(['40'].includes($CFG.province))">Kết luận</th>
                    <th class="table-student-health--note" ng-bind = "(['40'].includes($CFG.province)) ? 'Kết luận' : 'Ghi chú'">Ghi chú</th>
                </tr>
                </thead>
                <tbody style="position: absolute;z-index: 0;margin-top: 56px">
                <tr ng-repeat="student in vm.students" style="position: relative;">
                    <td class="table-bmi-dashboard--stt text-center">
                        <div class="absolute {{ detachedZScore ? 'td-fixed-health' : 'td-fixed'}}">
                            {{(vm.paginate.page - 1) * vm.paginate.limit + $index + 1}}
                        </div>
                    </td>
                    <td class="table-bmi-dashboard--full-name">
                        <div class="absolute {{ detachedZScore ? 'td-fixed-health' : 'td-fixed'}}">
                            {{ student.last_name }} {{ student.first_name }}
                        </div>
                    </td>
                    <th class="table-bmi-dashboard--student-id text-center">
                        <div class="absolute {{ detachedZScore ? 'td-fixed-health' : 'td-fixed'}}">{{student.id}}</div>
                    </th>
                    <th class="table-bmi-dashboard--birthday">
                        {{ student.birthday | date: 'dd/MM/yyyy'}}
                    </th>
                    <td class="table-student-health--gender text-center">
                        <span ng-if="student.gender === 1">Nam</span>
                        <span ng-if="student.gender !== 1">Nữ</span>
                    </td>
                    <td style="padding: 5px;" ng-repeat="disease in vm.diseases" class="{{ detachedZScore ? 'width-diseases' : ''}} table-student-health--disease text-center">
                        <input type="checkbox"
                               ng-model="vm.medicalExaminations[student.id][disease.id]"
                               ng-change="vm.onDiseaseOfStudentChange(student, disease)"
                               ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock">
                        <textarea style="margin-top: 5px" class="form-control" type="text"
                        ng-model="vm.studentDiseasesNote[student.id][disease.id]" 
                        ng-blur=" student['old_'+disease.id] != vm.studentDiseasesNote[student.id][disease.id] ? vm.onDiseaseOfStudentChange(student, disease) : ''"
                        ng-focus = "student['old_'+disease.id] = vm.studentDiseasesNote[student.id][disease.id]"
                        ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock " rows="{{ detachedZScore ? 10 : -1 }}">
                        </textarea>
                    </td>
                    <td class="table-student-health--note" ng-if="!(['40'].includes($CFG.province))">
                        <select class="form-control form-control-sm" style="height : 42px" 
                        ng-model="vm.medicalExaminationConclusion[student.id]" 
                        ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"
                        ng-change="vm.storeMedicalExaminationNote(student,1)">
                            <option value="1">Loại I</option>
                            <option value="2">Loại II</option>
                            <option value="3">Loại III</option>
                            <option value="-1">Không khám</option>
                        </select>
                    </td>
                    <td class="table-student-health--note">
                        <textarea class="form-control"
                                  ng-model="vm.medicalExaminationNote[student.id]"
                                  ng-blur="vm.storeMedicalExaminationNote(student)"
                                  ng-disabled="vm.unit_level==2 || vm.unit_level==3 || student.is_lock"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
        </form>
    </div>

    <div class="d-flex justify-content-between mt-2">
        <div class="w-50 d-flex">
            <select class="form-control form-control-sm w-auto mr-2"
                    ng-model="vm.paginate.limit"
                    ng-change="vm.onPageLimitChange()"
                    ng-options="item as item for item in vm.paginate.perPages">
            </select>
            <ul uib-pagination
                class="pagination-sm"
                ng-if="vm.paginate.total > vm.paginate.limit"
                max-size="vm.paginate.maxSize"
                total-items="vm.paginate.total"
                items-per-page="vm.paginate.limit"
                ng-model="vm.paginate.page"
                ng-change="vm.onPageChange()"
                boundary-link-numbers="true"
                rotate="false"
                previous-text="&lsaquo;"
                next-text="&rsaquo;"
            ></ul>
        </div>

        <div class="w-50 align-items-end text-right">
            <button class="btn btn btn-info dropdown-toggle waves-effect waves-light" type="button" id="dropdownExcel"
                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                ng-disabled="!(vm.students.length > 0)">
                <i class="mdi mdi-download"></i> Xuất danh sách
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownExcel">
                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_periodic_health_all_school?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}" ng-hide="!(vm.students.length > 0) || vm.filters.course_id ">Theo dõi khám sức khỏe</a>
                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_periodic_health?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}" ng-if="vm.filters.course_id">Theo dõi khám sức khỏe</a>
                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_periodic_health_m2?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}" ng-if="vm.filters.course_id">Bảng tổng hợp khám sức khỏe học sinh theo lớp</a>
                <a class="dropdown-item" target="_blank" href="{{$CFG.remote.base_url}}/dinhduong/cando/rpt_health_monitoring?schoolyear={{vm.filters.school_year}}&course_id={{vm.filters.course_id}}&month={{vm.filters.month}}&r={{Math.random()}}" ng-hide="!(vm.students.length > 0) || vm.diseases.length==0" >Theo dõi khám sức khoẻ (+huyết áp, nhịp tim)</a>
                <!-- <a class="dropdown-item" href="#" ng-hide="!(vm.students.length > 0 && vm.filters.course_id == 0) || vm.diseases.length==0" ng-click="vm.formExportFollows()">Theo dõi trẻ toàn trường</a> -->
                <a class="dropdown-item" href="#" ng-hide="!(vm.students.length > 0 && vm.filters.course_id == 0) || vm.diseases.length==0" ng-click="vm.formExportReports()">Báo cáo tổng hợp</a>
                <a class="dropdown-item" href="#" ng-if="vm.filters.course_id" ng-click="vm.formChooseMonth()">Theo dõi thể lực, dinh dưỡng & Kết quả khám sức khỏe chuyên khoa</a>
            </div>
        </div>
    </div>
</div>
<script type="text/ng-template" id="exports_reports_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xuất báo cáo sức khoẻ định kỳ học sinh</h3>
    </div>
    <form name="$ctrl.exportForm" ng-submit="$ctrl.exports()" autocomplete="off" spellcheck="false">
        <div class="modal-body">
            <p class="input-group">
                <input type="text" class="form-control" id="txt_date_export" readonly="readonly" uib-datepicker-popup="dd/MM/yyyy" ng-model="$ctrl.date" is-open="$ctrl.opened" close-text="Close"/>
                <span class="input-group-btn">
                    <button type="button" class="btn btn-secondary p-2" ng-click="$ctrl.open()" style="padding: 0.8rem !important;">
                      <i class="fa-svg-icon">
                        <svg width="1792" height="1792" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M192 1664h288v-288h-288v288zm352 0h320v-288h-320v288zm-352-352h288v-320h-288v320zm352 0h320v-320h-320v320zm-352-384h288v-288h-288v288zm736 736h320v-288h-320v288zm-384-736h320v-288h-320v288zm768 736h288v-288h-288v288zm-384-352h320v-320h-320v320zm-352-864v-288q0-13-9.5-22.5t-22.5-9.5h-64q-13 0-22.5 9.5t-9.5 22.5v288q0 13 9.5 22.5t22.5 9.5h64q13 0 22.5-9.5t9.5-22.5zm736 864h288v-320h-288v320zm-384-384h320v-288h-320v288zm384 0h288v-288h-288v288zm32-480v-288q0-13-9.5-22.5t-22.5-9.5h-64q-13 0-22.5 9.5t-9.5 22.5v288q0 13 9.5 22.5t22.5 9.5h64q13 0 22.5-9.5t9.5-22.5zm384-64v1280q0 52-38 90t-90 38h-1408q-52 0-90-38t-38-90v-1280q0-52 38-90t90-38h128v-96q0-66 47-113t113-47h64q66 0 113 47t47 113v96h384v-96q0-66 47-113t113-47h64q66 0 113 47t47 113v96h128q52 0 90 38t38 90z"/></svg>
                      </i>
                    </button>
                </span>
            </p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit" ng-disabled="$ctrl.exportForm.$invalid">Xuất</button>
        </div>
    </form>
</script>
<script type="text/ng-template" id="choose_month_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xem báo cáo theo dõi thể lực</h3>
    </div>
    <!-- <form name="$ctrl.exportForm" ng-submit="$ctrl.exports()" autocomplete="off" spellcheck="false"> -->
        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>Tháng theo dõi thể lực của trẻ</strong>
                </div>
                <div class="col-md-6">
                    <strong>Tháng khám sức chuyên khoa</strong>
                </div>
            </div>
            
            <div class="row" style="margin-top : 20px">
                <div class="col-md-6 " style="border-right: thin solid #ccc">
                    <div class="row cando_selects" ng-repeat="x in [].constructor(5) track by $index" style="padding-left: 10px">
                        <div class="col-ms-4" style="line-height: 38px">Lần {{ $index + 1 }}</div>
                        <div class="col-md-8">
                            <select class="form-control custom-select" ng-change = "$ctrl.changeMonth($index + 1,'month_selected','error')" ng-model = "$ctrl.month_selected[$index + 1]" ng-options="month.id as month.title for month in $ctrl.months">
                        </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row cando_selects" ng-repeat="x in [].constructor(3) track by $index" style="padding-left: 10px">
                        <div class="col-ms-4" style="line-height: 38px">Lần {{ $index + 1 }}</div>
                        <div class="col-md-8">
                            <select class="form-control custom-select" ng-change = "$ctrl.changeMonth($index + 1,'months_conclusion','error_conclusion')" ng-model = "$ctrl.months_conclusion[$index + 1]" ng-options="month.id as month.title for month in $ctrl.months">
                        </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-6" ng-if="$ctrl.error" style=" color :red; text-align: center;">
                    Lựa chọn tháng không hợp lệ
                </div>
                <div class="col-md-6" ng-if="!$ctrl.error"></div>
                <div class="col-md-6" ng-if="$ctrl.error_conclusion" style=" color :red; text-align: center;">
                    Lựa chọn tháng không hợp lệ
                </div>
            </div>
            
        </div>
        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary" style="width:100% !important" ng-click="$ctrl.show(1)" ng-disabled="$ctrl.error">Xem</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-primary" style="width:100% !important" ng-click="$ctrl.show(2)" ng-disabled="$ctrl.error_conclusion">Xem</button>
                </div>
            </div>
            
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
        </div>
    <!-- </form> -->
</script>
<script type="text/javascript">
    $('#student-health').scroll(function() {
        var table = $('#student-health-table');
        
        var top = table.position().top;
        table.find('thead').css({top: top * -1});

        var left = table.position().left * -1-4;
        $('.table-bmi-dashboard--stt .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--full-name .absolute').css({'margin-left': left});
        $('.table-bmi-dashboard--student-id .absolute').css({'margin-left': left});
    })
</script>

<script type="text/ng-template" id="exports_follows_modal_content.html">
    <div class="modal-header">
        <h3 class="modal-title">Xuất báo cáo Theo dõi trẻ toàn trường</h3>
    </div>
    <form name="$ctrl.exportForm" ng-submit="$ctrl.exports()" autocomplete="off" spellcheck="false">
        <div class="modal-body row">
            <div class="col-md-6 table-student-health" ng-repeat="month_year in $ctrl.month_years">
                <label class="checkbox-inline">
                    <input type="checkbox" ng-value="month_year.id" name="month_year"/>
                    <text ng-bind="month_year.title" class="ng-binding">09/2019</text>
                </label>
            </div>      
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" type="button" ng-click="$ctrl.close()">Đóng</button>
            <button class="btn btn-primary" type="submit">Xuất</button>
        </div>
    </form>
</script>
<style>
    
    .cando_selects:not(:first-child) {
        margin-top : 20px;
    }
    .width-diseases {
        width: 300px !important;
        max-width: 300px !important;
	    min-width: 300px !important;
    }
</style>