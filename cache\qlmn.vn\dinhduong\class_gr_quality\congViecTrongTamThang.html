<!DOCTYPE html>
<html lang="en">
<head>
	
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="X-CSRF-TOKEN" id="csrf-token" content="CS4i5ImC0qWrIa3w5NkHurZOlUgFkNO2FZK5qBKR">
		<title>PMS - Dinh dưỡng - Thu chi</title>
	<link rel="shortcut icon" type="image/png" href="http://localhost:3000/favicon.png"/>
	
	<!-- Fonts -->
	<link href="//fonts.googleapis.com/css?family=Roboto:400,300" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/bootstrap-dialog.min.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/common.css" rel="stylesheet" type="text/css">
	<link href="http://localhost:3000/css/style_spinner.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/docs/font-awesome.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/metro/easyui.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.5.1/themes/icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/icons.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/ribbon-icon.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/js/jquery-handsontable/dist/jquery.handsontable.full.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/metro-bootstrap/css/metro-bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/bootstrap/css/bootstrap-social-gh-pages/bootstrap-social.css" />
	<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/main-report.css?_=1004315681" />
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
	<script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.0/xlsx.full.min.js"></script>

	<style type="text/css">
		*{
			font-family: Nunito_Regular;
			font-size: 13 !important;
		}
		@media  print {
			body > .panel {
				display: none !important;
			}
		}
	</style>
	<!-- <link href="http://localhost:3000/css/material.css" rel="stylesheet" type="text/css"> -->
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- 1. Load libraries -->
     <!-- Polyfill for older browsers -->
      <!-- 2. Configure SystemJS -->
	<script type="text/javascript" src="http://localhost:3000/js/lodash.min.js"></script>
    <script src="http://localhost:3000/js/jquery-3.0.0.min.js"></script>
	<script src="http://localhost:3000/bootstrap/js/bootstrap.min.js"></script>
	<script src="http://localhost:3000/js/bootstrap-dialog.min.js"></script>
	<script type="text/javascript">
		$CFG = {
			co_cau: 'cocau_chuan',
			co_cau_from: '01/01/2010',
        	local: { base_url: "http://localhost:3000/js/admin" },
        	remote: { base_url: 'http://localhost:3000' },
        	project: 'dinhduong',
			school_point: +'1',
			unit_id: parseInt('51461'),
            school_points: +'1',
            school_point_together: parseInt('0'),
            is_view_csdl_nganh: true,
            administrator: 1,//parseInt('0'),
			dialog_captcha: function (name) {
                return '<div style="display: flex; align-items: center; justify-content: start; margin: 10px;">' +
                '<input name="'+name+'_captcha" type="type" class="form-control" placeholder="Nhập mã bảo vệ" autocomplete="off" spellcheck="false" style="margin-right: 5px; max-width: 130px;"/>' +
                '<img src="/captcha?t=' + (new Date()).getTime() + '&style=confirm_dialog" style="width: 80px; height: 34px; margin-right: 5px;">' +
                '<button title="Làm mới mã bảo vệ" class="btn btn-link captcha-refresh-btn"><i class="fa fa-refresh"></i></button>' +
                '</div>'
            },
			level: '4',
      	};
	</script>
	<script src="http://localhost:3000/js/jquery.maskedinput.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.5.1/jquery.easyui.min.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/ribbon/jquery.ribbon.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/datagrid_view/datagrid-groupview.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/extensions/drag_drog_datagrid/datagrid-dnd.js"></script>
	<script src="http://localhost:3000/js/jquery-easyui-1.3.6/locale/easyui-lang-vn.js"></script>
	
	<script src="http://localhost:3000/js/datagrid-detailview.js"></script>
	<script src="http://localhost:3000/js/datagrid-filter.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular.min.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-animate.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-cookies.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-route.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-resource.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-touch.js"></script>
	<script src="http://localhost:3000/js/angular.1.6.4/angular-loader.js"></script>

	<script src="http://localhost:3000/js/common.js?_=1897020933"></script>
	<script type="text/javascript">
		angular_app = angular.module("angular_app_report",['ngRoute','ngResource','ngCookies','ngAnimate']);
		angular_app_report = angular_app;
	</script>
	<script src="http://localhost:3000/js/my-angular.js?_=3761012776551"></script>
	<script src="http://localhost:3000/js/dinhduong/main-angular-report.js?_=633483986"></script>
	<script type="text/javascript" src="http://localhost:3000/js/library.js?v=1664592880"></script>
</head>
<body ng-app="angular_app_report" ng-controller="appController">
	<div class="full-container" ng-controller="mainContentController">
		    <style>
        .w-100 {
            width: 100%;
        }

        .large {
            font-size: large;
        }

        .float-right {
            float: right;
        }

        .overlay {
            visibility: hidden;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
        }

        @keyframes  spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .sign-wrapper {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
            width: 250px!important;
            margin-left: 150px;
            position: relative;
            align-self: stretch;
            padding-bottom: 0px;
        }
        
        .sign-image {
            width: 150px;
            height: auto;
        }
        
        .sign-label {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        
        .sign-sub-label {
            font-size: 14px;
            color: #666;
        }

        .sign-action {
            margin-left: 10px;
            cursor: pointer;
        }
        
        .sign-full-name {
            font-weight: bold;
            margin-top: 12px;
            color: #555;
            position: absolute;
            bottom: -45px;
        }
    </style>

    <body style="margin-right:80px; margin-left:80px; font-size:18px">
        <div ng-controller="congViecTrongTamThangController">
            <div class="overlay">
                <div class="spinner"></div>
            </div>
        
            <div style="border: 1px dotted #ccc; margin-top: 20px; height: 38px;">
                <b>Chọn lớp: </b>
                <select class="form-control loadData" id="course_id" style="width:auto; height: 28px; display: inline" >
                    <option value="">- Chọn lớp học -</option>
                                    </select>
        
                <b>Tháng: </b>
                <select class="form-control loadData" id="month" style="width:auto; height: 30px; display: inline">
                                            <option value="9" >
                            9/2026
                        </option>
                                            <option value="10" >
                            10/2026
                        </option>
                                            <option value="11" >
                            11/2026
                        </option>
                                            <option value="12" >
                            12/2026
                        </option>
                                            <option value="1" >
                            1/2027
                        </option>
                                            <option value="2" >
                            2/2027
                        </option>
                                            <option value="3" >
                            3/2027
                        </option>
                                            <option value="4" >
                            4/2027
                        </option>
                                            <option value="5" >
                            5/2027
                        </option>
                                            <option value="6" >
                            6/2027
                        </option>
                                            <option value="7" >
                            7/2027
                        </option>
                                            <option value="8" selected>
                            8/2027
                        </option>
                                    </select>
        
                            </div>
            <div>
                <h3 style="text-align: center; color:#0652DD">CÔNG VIỆC TRỌNG TÂM THÁNG</h3>

                <div class="form-group">
                    <textarea class="form-control large" id="rpt_value" rows="6" placeholder="Nhập text"
                         disabled="disabled" ></textarea>
                </div>

                <div class="form-group">
                    <label class="text-center w-100" for="principal_note">Ý kiến của ban giám hiệu</label>
                    <textarea class="form-control large" id="rpt_description" rows="6" placeholder="Nhập text"
                        ></textarea>
                </div>

                <button class="btn btn-primary float-right" id="save-btn">Lưu</button>

                <div style="display: flex; justify-content: flex-end; margin-bottom: 60px;" ng-init="module='cong_viec_trong_tam_thang'">
                    <div class="sign-wrapper">
                        <span class="sign-label">Ban giám hiệu</span>
                        <span class="sign-sub-label">
                            (Ký, Họ tên)
                                                        <i class="fa fa-pencil no-print text-info sign-action" title="Ký" ng-click="sign(unitSignTypes.schoolAdmin)"></i>
                                                    </span>
                        <img src="{{ unitSigns[unitSignTypes.schoolAdmin]['url'] }}" title="{{ unitSigns[unitSignTypes.schoolAdmin]['title'] }}" class="sign-image">
                        <span class="text-capitalize sign-full-name" ng-bind="unitSigns[unitSignTypes.schoolAdmin]['fullname']"></span>
                    </div>
                </div>
            </div>
        </div>
    </body>

    <script src="http://localhost:3000/js/dinhduong/cong_viec_trong_tam_thang.js?_=1432906067"></script>
	</div>
</body>
<style>
	@media  print {
		.btn {
		  display: none !important;
		}
	}
</style>
<script>
	var apiUrl = $CFG.remote.base_url + '/doing/admin/user/';
	var url = $CFG.remote.base_url + '/images/signs/' + $CFG.unit_id + '/';
	function getSignConfig(date, module, group_id) {
		var params = {
			module : module,
			date : date,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'getSignConfig',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				if (response.success) {
					let data = JSON.parse(response.data.sign_data);
					Object.keys(data).forEach((v) => {
						$('#'+v).attr('src',data[v]['filename']) ;
						$('#'+v).attr('title',data[v]['time']) ;
					})
					let eleBrs = document.querySelectorAll(".break_line");
					eleBrs.forEach(v => {
						v.style.display = 'none';
					});
				}
			}
		});
	}

	function getSignWithType(date, module, group_id, type) {
		var params = {
			module : module,
			date : date,
			type : type,
			group_id : group_id
		};
		$.ajax({
			url: apiUrl+'signing',
			dataType: 'json',
			data: params,
			method: 'post',
			crossDomain: true,
			async: true,
			success: function (response) {
				alert(response.message)
				if (response.success) {
					$('#'+type).attr('src',response.signInfo.filename);
					$('#'+type).attr('title',response.signInfo.time);
				}
			}
		});
	}
</script>
</html>
