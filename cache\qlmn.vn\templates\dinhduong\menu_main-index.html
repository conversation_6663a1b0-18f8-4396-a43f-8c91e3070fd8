<div class="nav ace-nav ul-menu-main-dd">
    <div class="menu-main-option w60">
        <a href="#" class="dropdown-toggle" href="#" data-toggle="dropdown">
            <span class="glyphicon glyphicon-option-vertical clrWhite mT3">Menu</span>
        </a>
        <ul class="user-menu dropdown-menu-left dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
            <li class="option-schoolyear" ng-repeat="item in menu.data.top.concat(menu.data.bottom)"
                menu-dropdown-hover="">
                <a href="javascript: void(0);" ng-bind="item.name"></a>
                <ul
                    class="user-menu style-menu-main dropdown-menu-left dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
                    <li class="option-schoolyear" ng-repeat="itemi in item.children">
                        <div class="circle-menu"></div>
                        <a ng-href="{{$CFG.remote.base_url+ '/' + itemi.path}}">
                            <img
                                ng-src="{{$CFG.remote.base_url+'/css/'+$CFG.project+'/images/'+itemi.icon+'.png'}}">
                            {{itemi.name}}
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
    <div class="li-button-back">
        <a class="a-back-arrow" ng-href="{{$CFG.project}}">
            <img title="Trở về trang quản lý dinh dưỡng"
                ng-src="{{$CFG.remote.base_url+'/css/'+$CFG.project+'/images/back-arrow.png'}}">
        </a>
    </div>
    <div class="div-menu-con nav" style="height: 65px;">
        <!-- <li>
            <p ng-bind="getGroupMenu().name" style="opacity: 0; padding: 0 7px;"></p>
            <div class="menu-name-cha">
                <div class="bg-menu-name-cha selected">
                    <p ng-bind="getGroupMenu().name"></p>
                </div>
            </div>
        </li> -->
        <li class="light-blue menu-name-con head-sidebar" ng-repeat="mn in getGroupMenu().children"
            id="{{(mn.define==getParams().module?'active':'')}}">
            <div class="icon-hover" style="width:100%;text-align:center;">
                <a class="" ng-href="{{$CFG.remote.base_url+ '/' + mn.path}}">
                    <img ng-src="{{$CFG.remote.base_url+'/css/'+$CFG.project+'/images/'+mn.icon+'.png'}}">
                </a>
            </div>
            <a class="" ng-href="{{$CFG.remote.base_url+ '/' + mn.path}}" ng-bind="mn.name"> menu name </a>
        </li>
    </div>
</div>
<style>
    .function-kh-ct {
        margin-top: 32px !important;
    }

    .icon-hover {
        opacity: 1 !important;
    }

    .icon-hover img {
        -webkit-animation: none;
        animation: none;
    }
    .head-sidebar {
        height: 48px;
        width: 8%;
        border-right: 1px solid #ccc;
        text-align: center;
        margin-top: 10px;
        font-size: 15px;
    }

    .icon-hover img {
        margin-top: -20px;
    }

    @media only screen and (height: 768px) {
        .head-sidebar {
            height: 50px;
            width: 80px !important;
            font-size: 13px;
        }
    }

    @media only screen and (width: 1200px),
    screen and (height: 800px) {
        .head-sidebar {
            height: 50px;
            width: 90px;
            font-size: 13px;
        }
    }

    @media only screen and (width: 1280px),
    screen and (height: 800px) {
        .head-sidebar {
            height: 50px;
            width: 8%;
            font-size: 13px;
        }
    }

    @media only screen and (width: 1280px),
    screen and (height: 960px) {
        .head-sidebar {
            height: 50px;
            width: 8%;
            font-size: 13px;
        }
    }

    @media only screen and (width: 1280px),
    screen and (height: 1024px) {
        .head-sidebar {
            height: 50px;
            width: 8%;
            font-size: 13px;
        }
    }
</style>