$.user_permission_group = {
    module: 'user_permission_group',
    project: 'admin', /*Đ<PERSON>ờng dẫn tới lớp xử lý*/
    init: function(project_id,project_define, unit_id) {
    	var self = this;
        var urls = [$CFG.remote.base_url,'doing',self.project,self.module,'list'];
        $.dm_datagrid.init(
        	urls.join('/'), 
        	this.module, /*Định nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [[
                // { field:'ck', checkbox: true },
               	{ title:'Tên', field:'name', width:150, sortable:true },
               	{ title:'Nhóm chức năng', field:'method_group_ids', width:250, formatter: function(methods,row){
                    var pers = {};
                    $.each(methods,function(index,method){
                        pers[method.module_name] || (pers[method.module_name] = []);
                        pers[method.module_name].push(method.name);
                    });
                    var html = [];
                    $.each(pers, function(module_name,name){
                        html.push('<b>'+module_name+'</b>: '+name.join(','))
                    });
					var showhidehtml = '<span style="cursor:pointer; color:#428bca" onclick="$.user_permission_group.showPerDetail('+row.id+')"><b>Xem chi tiết chức năng</b></span>';
					return showhidehtml+'<span id="permission-group-detail-id'+row.id+'" style="display:none"> - '+html.join('<br/> - ')+'</span>';
                } },
				{ title:'Người tạo', field:'unit_id', sortable:true, formatter: function(value,row){
					var label = 'Trường';
                    if(value==2539) {
                        label = '<b>Hệ thống</b>';
                    }
                    return label;
                }},
				{ title:'Trạng thái', field:'status' },
                { title:'Hành động', field:'action', align:'center',
                    formatter:function(value,row,index){
                        if (row.unit_id == unit_id) {
                            var btn_edit = '<a href="javascript:void(0)" class="fa fa-pencil fs11 mR5" onclick="$.user_permission_group.showEditForm('+project_id+','+row.id+')" title="Sửa nhóm chức năng"></a>';
                            var btn_del = '<a href="javascript:void(0)" class="fa fa-trash-o fs11 clrRed mL5" onclick="$.user_permission_group.del('+row.id+')" title="Xóa nhóm chức năng"></a>';
                            return btn_edit + " | " + btn_del;
                        }
                    }
                }
            ]],
            {   
                queryParams: {id: project_id}
			}
        );
    }, showAddForm: function(project_id) { 
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: self.project+'/'+self.module,
                action:'add',
                title:'Thêm nhóm quyền',
                size: size.wide,
                content: function(element){
                    loadForm(self.project+'/'+self.module,'add', {project_id: project_id}, function(resp){
                        $(element).html(resp);
                    })
                }
            },
            function(resp){
                if(typeof callback === 'function') {
                    callback(resp);
                }
                $("#tbl_"+self.module).datagrid('reload');
            }
        );
    }, showEditForm: function(project_id, id) {
        var self = this;
        $.dm_datagrid.showEditForm(
            {
                module: self.project+'/'+self.module,
                action:'edit',
                title:'Sửa nhóm quyền',
                size: size.wide,
                content: function(element){
                    loadForm(self.project+'/'+self.module,'edit', {id: id,project_id:project_id}, function(resp){
                        $(element).html(resp);
                    })
                }
            },
            function(resp){
                $("#tbl_"+self.module).datagrid('reload');
            }
        );
       
    }, del: function(id){ // XÓA
    	var self = this;
		var captchaForm = $CFG.dialog_captcha('delete_user');
        var msg = '<div style = "font-size: 14px; color:red;">Bạn có chắc chắn muốn xóa nhóm chức năng này?</div>' + captchaForm;
		$.messager.confirm('Xác nhận', msg, function(r){
			if (r){
				var captcha = $('input[name="delete_user_captcha"]').val();
				var urls = [$CFG.remote.base_url,'doing',self.project,self.module,'del'];
				process(urls.join('/'),{ids: id, captcha:captcha}, function(resp){
					$("#tbl_"+self.module).datagrid('reload');
				})
			}
		});
    }, showPerDetail: function(id){ // Show permssion detail
    	var perDetailHtml = $('#permission-group-detail-id'+id).html();
		$.messager.alert('Chi tiết chức năng', perDetailHtml);
    }
}
