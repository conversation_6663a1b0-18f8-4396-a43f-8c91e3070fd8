<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/config.css?_=1260735232" />
<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/jquery.timepicker.min.css" />
<div class="tbl_container">
    <div class="tbl-container-header header-kh-ct" id="tb_config">
        <div class="title-kh-ct">
            <div id="header-title" class="header-title" tbl-menu-in-group="config"></div>       
        </div>
    </div>
    <div id="tbl_config" class="tbl-container-body">
        <div class="in-tbl-config">
            <div class="title-config"><img src="http://localhost:3000/css/dinhduong/images/cauhinhchung_icon.png"><h4>Thông tin cấu hình chung</h4></div>
            <div class="content-config">
                <div>
                    <label class="control-label" ng-init="together_price=1">Giá thực phẩm: </label>
                                            <label class="checkbox-inline ">
                            <input id="together_price" name="together_price"  ng-model="together_price" type="radio" ng-value="0" ng-false-value="0" ng-true-value="1"> Lưu chung
                        </label>
                                            <label class="checkbox-inline ">
                            <input id="together_price" name="together_price"  ng-model="together_price" type="radio" ng-value="1" ng-false-value="0" ng-true-value="1"> Lưu theo thực đơn
                        </label>
                                        
                </div>
                <div class="round_number_config_array" style="margin-top: 10px;">
                    <label class="control-label" ng-init="round = 2">Làm tròn </label>
                    <select id="round_number_config">
                                                    <option  value="2">2</option>
                                                    <option  value="3">3</option>
                                                    <option  value="4">4</option>
                                            </select>
                    <label class="control-label"> chữ số </label>
                </div>
                				<div class="school-point-config" style="margin-top: 10px;">
					<label class="control-label" >Chọn điểm trường làm việc : </label>
					<select ng-options="''+value as value for value in config.getArrSchoolpoint()" ng-model="config.school_point"></select>
					<input type="hidden" ng-value="config.school_point" id="school_point">
				</div>
				                <div class="school-point-config" style="margin-top: 10px;">
                    <label class="control-label" >Chọn ký tự phân cách hàng nghìn : </label>
                    <select ng-options="value as value for value in [',','.']" ng-model="config.digit_grouping_char" style="font-weight: bold;padding: 0 7px;"></select>
                    <input type="hidden" ng-value="config.digit_grouping_char" id="digit_grouping_char">
                </div>
                <div class="school-point-config" style="margin-top: 10px;">
                    <label class="control-label">Giờ chốt sổ điểm danh hàng ngày :</label>
                    <input style="width: 15%" ng-model="config.attending_milestone_time" type="text" class="timepicker" name="attending_milestone_time" id="attending_milestone_time" ng-disabled="config.parent_attending_milestone_time" title="{{config.parent_attending_milestone_time ? 'Phòng giáo dục đã khóa giờ chốt sổ điểm danh hàng ngày' : ''}}"/>
                </div>
                <div class="school-point-config" style="margin-top: 10px;">
                    <label class="control-label">Giờ khóa "Cân đối khẩu phần" hàng ngày :</label>
                    <input style="width: 15%" ng-model="config.menu_adjust_milestone_time" type="text" class="timepicker" name="menu_adjust_milestone_time" id="menu_adjust_milestone_time"/>
                </div>
								<div class="school-point-config" style="margin-top: 10px;">
                    <label class="control-label" style="color:red;">Tháng bắt đầu năm học :</label>
											<span ng-bind=" 'Tháng' +' '+ 9" style="color:red;"></span>
					                </div>
                								            </div>
            <div class="bottom-config">
                <button type="button" class="btn btn-primary" onclick="$.config.saveButton()">
                    <span class="bootstrap-dialog-button-icon glyphicon glyphicon-floppy-disk"></span>Lưu
                </button>
            </div>
        </div>
    </div>
    
</div>
<script src="http://localhost:3000/js/dinhduong/config.js?_=376"></script>
<script src="http://localhost:3000/js/jquery.timepicker.js"></script>
<script type="text/javascript">
    // $(function(){
    //  setTimeout(function(){
            $.config.init();
    //  },1000);
    // });
	function checkDataBegin() {
		var checkUrl   = $CFG.remote.base_url + '/' + $CFG.project + '/config/check_data_begin';
		process(checkUrl,{},function (resp) {
			var message = '<div style="height:200px;overflow-y:scroll; border:1px;"><b>Hệ thống đã kiểm tra dữ liệu trong các tháng 6,7 từ khi trường sử dụng phần mềm, kết quả như sau:</b><br/>';
			message += resp.message+"</div><br/>";
			if (resp.status==0) {
				message += "<span style='color:red;padding-top:5px;'>=> Bạn ko thể thay đổi tháng bắt đầu của năm học do dữ liệu của Cân đối KP, Phiếu kê chợ, Sổ tính Tiền ăn, Nhập kho, Xuất kho,  Thu chi có tính lũy kế theo qua các tháng/năm học.</span>"
			}else{
				$('#month_id').removeAttr("disabled");
				$("#month_id").css("background-color", "#fff");
				$("#month_id").css("color", "#000");
				message += "<span style='color:green;padding-top:5px;'>=> Bạn có thể thay đổi tháng bắt đầu của năm học. Tuy nhiên năm sau bạn sẽ ko thể thay đổi lại được vì dữ liệu Cân đối KP, Sổ tính Tiền ăn, Phiếu kê chợ, Thu chi có tính lũy kế qua các tháng/năm học!</span>"
			}
			$.messager.alert('KẾT QUẢ KIỂM TRA', message);
		});
	}
</script>
<style type="text/css">
    #tbl_config{
        height: 92%;
    }
    .in-tbl-config{
        width: 80%;
        margin: auto;
        margin-top: 40px;
        text-align: left;
        border: 1px solid #DDD;
        border-radius: 10px;
    }
    .content-config{
        width: 100%;
        background: #e9f6e5;
        padding: 30px 10px 30px 10px;
    }
    .title-config{
        width: 100%;
        border-bottom: 1px solid #DDD;
        padding: 5px 10px 5px 10px;
    }
    .title-config img{
        width: 30px;
        float: left;
        margin-right: 10px;
    }
    .title-config h4{
        margin: 0px;
        line-height: 30px;
    }
    .bottom-config{
        padding: 10px;
        width: 100%;
        display: inline-block;
    }
    .bottom-config button{
        float: right;
        background: #adca49;
        color: #FFF;
    }
</style>
<style>
    .phone-div{
        display: none;
        width: 100%;
        background: #142115;
        opacity: 0.9;
        position: fixed;
        bottom: 0px;
        height: 35px;
        z-index: 999;
    }
    .phone-div p{
        width: 55%;
        float: left;
        line-height: 40px;
        color: #FFF;
        font-weight: bold;
        text-align: left;
    }
    .thumb-img-phone{
        width: 45%;
        float: left;
    }
    .thumb-img-phone img{
        width: 33px;
        margin-top: 2px;
        margin-right: 5px;
    }
    table td, table th{
        border: 1px solid #ccc;
        padding-left: 10px;
    }
    
    @media (max-width: 650px) { 
        .phone-div {
            display: block
        }
    }

</style>
