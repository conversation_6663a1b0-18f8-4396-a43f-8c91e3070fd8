.header {
    border-bottom: 5px solid orange;
    padding: 10px;
    position: fixed;
    height: 60px;
    line-height: 30px;
    width: 100%;
    background: white;
    z-index: 1000;
}

.header b {
    font-size: 25px;
}

.input-group {
    width: 200%;
}

.context {
    padding-top: 60px;
    white-space: nowrap;
}

.module {
    width: 400px;
    padding: 5px 20px;
    margin-right: 20px;
    display: inline-table;
}

mark {
    padding: 0;
    background-color: #99c7ef;
}

.focus mark {
    padding: 0;
    background-color: #6fbb70 !important;
}

#search.input-group-addon {
    background: #3c763d;
    border: 1px solid #999999;
    cursor: pointer;
    color: white;
}

.header .form-group {
    width: 80%;
}

.input-group {
    width: 100%;
}

.tools i {
    cursor: pointer;
    font-size: 13px;
    color: #cccccc;
}

.tools i:hover {
    color: #38417b;
}

.fa.fa-circle {
    color: orange;
    font-size: 15px;
}

.l-btn-focus {
    outline: none;
}
.fa.fa-lightbulb-o{
    color: black;
}

a {
    color: black;
}