$domain = window.location.origin;
axios.interceptors.request.use(function (config) {
    loading();
    return config;
}, function (error) {
    return Promise.reject(error);
});
axios.interceptors.response.use(function (response) {
    loading(true);
    return response;
}, function (error) {
    loading(true);
    $.messager.alert('Warring', 'Xử lí không thành công!');
    return Promise.reject(error);
});

var app = angular.module("app", ['ngRoute', 'ngResource', 'ngCookies', 'ngAnimate']);
app.controller('appController', ['$scope', '$routeParams', '$compile', 'myCache', '$filter', '$cookies', function ($scope, $routeParams, $compile, MyCache, $filter) {
    $scope.$domain = $domain;
    $scope.compile = function (html, scope) {
        return $compile(html)(scope);
    };
}]).config(['$compileProvider', function ($compileProvider) {
    $compileProvider.debugInfoEnabled(true);
}]).factory('myCache', function ($cacheFactory) {
    return $cacheFactory('myCache');
});