angular_app_report.controller('unitReportController', ['$scope', '$compile', function ($scope) {
    $scope.data = {};
    $scope.value_old = "";
    $scope.semester = "1";
    $scope.school_level = parseInt($CFG.level);
    $scope.loadData = function() {
        process('dinhduong/school_report/unitReport',{semester : $scope.semester}, function (resp) {
            $scope.school_type_reports = {};
            $scope.default_reports = resp.reports.filter(v => v.unit_id == 0).reduce((obj, v) => {
                obj[v['excel_row']] = v;
                return obj;
            },{});
            let unit_reports = resp.reports.filter(v => v.unit_id == resp.unit_id).reduce((obj, v) => {
                obj[v['excel_row']] = v;
                return obj;
            },{});
                
            $scope.reports = Object.values($scope.default_reports).reduce((obj, v) => {
                obj[v['excel_row']] = v['rpt_value'];
                if(!(v['excel_row'] in $scope.school_type_reports)) {
                    $scope.school_type_reports[v['excel_row']] = {
                        'rpt_private' : v['rpt_private'],
                        'rpt_public' : v['rpt_public']
                    };
                }
                if(v['excel_row'] in unit_reports) {
                    obj[v['excel_row']] = unit_reports[v['excel_row']]['rpt_value'];
                    $scope.school_type_reports[v['excel_row']]['rpt_private'] = unit_reports[v['excel_row']]['rpt_private'];
                    $scope.school_type_reports[v['excel_row']]['rpt_public'] = unit_reports[v['excel_row']]['rpt_public'];
                }
                return obj;
            },{});

            if(resp.reports.filter(v => v.unit_id != 0).length == 0) 
                $scope.reports = Object.values($scope.default_reports).reduce((obj, v) => {
                    $scope.school_type_reports[v['excel_row']]['rpt_private'] = v['rpt_private'];
                    $scope.school_type_reports[v['excel_row']]['rpt_public'] = v['rpt_public'];
                    obj[v['excel_row']] = v['rpt_value'];
                    return obj;
                },{});
        }, null, false);
    }
    $scope.loadData();
    $scope.getDataFromUnit = function() {
        process('dinhduong/school_report/unitReport',{semester : $scope.semester, get_by_parent_id : 1}, function (resp) {
            Object.values($scope.default_reports).forEach((v) => {
                if(parseInt(v['rpt_level']) == 4) {
                    $scope.school_type_reports[v['excel_row']] = {
                        'rpt_private' : v['rpt_private'],
                        'rpt_public' : v['rpt_public']
                    };
                    $scope.reports[v['excel_row']] = 0;
                }
            });
            resp.reports.filter(v => v.parent_id == resp.unit_id).forEach((v) => {
                if(parseInt(v['rpt_level']) == 4) {
                    if(!(v['excel_row'] in $scope.reports)) {
                        $scope.reports[v['excel_row']] = 0;
                    }
                    $scope.reports[v['excel_row']] += parseInt(v['rpt_value']);
                    if($scope.school_level == 3) {
                        if(parseInt(v['school_type']) == 1) {
                            $scope.school_type_reports[v['excel_row']]['rpt_public'] += parseInt(v['rpt_value']);
                        }
                        else {
                            $scope.school_type_reports[v['excel_row']]['rpt_private'] += parseInt(v['rpt_value']);
                        }
                    }
                    else {
                        $scope.school_type_reports[v['excel_row']]['rpt_public'] += parseInt(v['rpt_public']);
                        $scope.school_type_reports[v['excel_row']]['rpt_private'] += parseInt(v['rpt_private']);
                    }
                }
            });
        })
    }
    $scope.allowOnlyNumbers = function(event) {
        var keyCode = event.keyCode || event.which;
      
        if (keyCode < 48 || keyCode > 57) {
          event.preventDefault();
        }
    };
    $scope.changeValueOld = function (value) {
        $scope.value_old = value;
    }
    $scope.changeInput = function (report, value, key ='rpt_value') {
        if(!Number.isInteger(parseInt(value) )) {
            alert("Định dạng không đúng");
            return;
        }
        if($scope.value_old != value) {
            delete report.id;
            report['rpt_public'] = $scope.school_type_reports[report['excel_row']]['rpt_public'];
            report['rpt_private'] = $scope.school_type_reports[report['excel_row']]['rpt_private'];
            report['rpt_value'] = $scope.reports[report['excel_row']];
            report[key] = value;
            report.semester = $scope.semester;
            process('dinhduong/school_report/saveUnitReport',{data : JSON.stringify(report), semester : $scope.semester}, function (resp) {

            })
        }
    }
    $scope.saveForParent = function() {
        let reports = Object.values($scope.default_reports).reduce((arr, v) => {
            v['rpt_value'] = $scope.reports[v['excel_row']];
            v['semester'] = $scope.semester;
            v['rpt_public'] = $scope.school_type_reports[v['excel_row']]['rpt_public'];
            v['rpt_private'] = $scope.school_type_reports[v['excel_row']]['rpt_private'];
            delete v.id;
            arr.push(v);
            return arr;
        },[])
        process('dinhduong/school_report/saveUnitReport',{data : JSON.stringify(reports), save_multi : 1, semester : $scope.semester}, function (resp) {

        })
    }
    $scope.returnFormula = function(report, column) {
        let tmp = {};
        key = "";
        if (column == 5) {
            tmp = $scope.reports;
        }
        if (column == 6) {
            tmp = $scope.school_type_reports;
            key = "rpt_public";
        }
        if (column == 7) {
            tmp = $scope.school_type_reports;
            key = "rpt_private";
        }
        if(report['formula']) {
            let arr_formula = report['formula'].match(/(\d+|[%+*/-])/g);
            if(arr_formula.length > 0) {
                let result = 0;
                if(column == 5) {
                    result = parseInt(arr_formula[0]) in tmp ? tmp[parseInt(arr_formula[0])] : 0;
                }
                if(column == 6 || column == 7) {
                    result = parseInt(arr_formula[0]) in tmp ? tmp[parseInt(arr_formula[0])][key] : 0;
                }
                for( let i = 1; i < arr_formula.length; i += 2) {
                    const operator = arr_formula[i];
                     
                    let number = 0;
                    if(column == 5) {
                        number = parseInt(arr_formula[i + 1]) in tmp ? tmp[parseInt(arr_formula[i + 1])] : 0;
                    }
                    if(column == 6 || column == 7) {
                        number = parseInt(arr_formula[i + 1]) in tmp ? tmp[parseInt(arr_formula[i + 1])][key] : 0;
                    }
                    switch (operator) { // xử lý phép tính tương ứng
                        case '*':
                            result *= number;
                            break;
                        case '/':
                            if(number != 0) {

                                result /= number;
                                
                            }
                            else result = 0;
                            break;
                        case '+':
                            result += number;
                            break;
                        case '-':
                            result -= number;
                            break;
                        case '%':
                            result *= 100;
                            break;
                    }
                }
                result = result.toFixed(2);
                if(column == 5) {
                    $scope.reports[report['excel_row']] = result;
                }
                if(column == 6 || column == 7) {
                    $scope.school_type_reports[report['excel_row']][key] = result;
                }
                return result;
            }
        }
        return 0;
    }

    $scope.export = function() {
        let params = {
            'default_reports' : JSON.stringify($scope.default_reports) ,
            'school_type_reports' : JSON.stringify($scope.school_type_reports) ,
            'reports' : JSON.stringify($scope.reports) 
        };
        var downloadUrl = $CFG.remote.base_url + '/' + $CFG.project + '/school_report/excelUnitReport?filename=';
        process('dinhduong/school_report/exportUnitReport',{...params},function(resp){
            window.location.href = downloadUrl +resp.filename;
        })
    }
}]);
