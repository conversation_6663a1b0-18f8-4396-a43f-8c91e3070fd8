$.calculate_calo_week = {
    module: 'calculate_calo_week',
    id: '', /*Mã project*/
    measures: null,
    warehouses: null,
    scope_a : null,
    group : null,
    init: function(id,project) {
        var self = this;
        self.id = id;
        /*<PERSON><PERSON>i đơn vị t<PERSON>h và danh sach kho trước*/
        angular.element($('body')).scope().$apply(function(scope) {
            process($CFG.project + '/' + self.module + '/special_categories', {}, function (resp) {
                $.calculate_calo_week.measures = resp.data.measures;
                $.calculate_calo_week.warehouses = resp.data.warehouses;
                $.calculate_calo_week.groups = scope.menu_report.groups;
                $.calculate_calo_week.date = resp.data.date;
                // $.calculate_calo_week.initAngular();
            }, null, false);
            $.calculate_calo_week.initView();
        })
    },initAngular: function(){
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                scope_a = scope;
                $.calculate_calo_week.scope = scope;
                scope.calculate_calo_week || (scope.calculate_calo_week = {});
                scope.calculate_calo_week.selected = {};
                scope.calculate_calo_week.date = dateboxOnSelect(new Date());
                scope.calculate_calo_week.calulate_for_rule = true;
                scope.calculate_calo_week.dateChange = function(){
                    scope.calculate_calo_week.load();
                };
                scope.calculate_calo_week.init = function(){
                    scope.calculate_calo_week.measures = $.calculate_calo_week.measures;
                    scope.calculate_calo_week.warehouses = $.calculate_calo_week.warehouses;
                    scope.calculate_calo_week.groups = $.calculate_calo_week.groups;
                    if($.calculate_calo_week.groups[0]){
                        scope.calculate_calo_week.selected.group_id = $.calculate_calo_week.groups[0].id;
                    }
                    scope.calculate_calo_week.date = $.calculate_calo_week.date;
                }
                scope.calculate_calo_week.sumSotre = function(){
                    var rs = 0;
                    angular.forEach(scope.calculate_calo_week.sotre, function(value,daymonth){
                        rs += value;
                    })
                    return rs;
                }
                scope.calculate_calo_week.sumQuality = function(food){
                    var rs = 0;
                    angular.forEach(food.date, function(value,daymonth){
                        rs += value;
                    })
                    return rs;
                }
                scope.calculate_calo_week.printPreview = function(date,warehouse_ids,group_id){
                    
                    group_id || (group_id = scope.calculate_calo_week.selected.group_id);
                    date || (date = scope.calculate_calo_week.date);
                    // var rule = scope.calculate_calo_week.calulate_for_rule;
                    
                    if(!warehouse_ids){
                        var warehouse_ids = [];
                        angular.forEach(scope.calculate_calo_week.warehouses, function(warehouse,index){
                            if(warehouse.selected){
                                warehouse_ids.push(warehouse.id);
                            }
                        });
                    }
                    
                    var urls = [$CFG.remote.base_url,$CFG.project,'calculate_calo_week','printPreview'];
                    var params = [
                            'warehouse_ids='+warehouse_ids.join(','),
                            'group_id='+group_id,
                            'date='+date,
                            'rule='+rule
                        ];
                    window.open(urls.join('/')+'?'+params.join('&'));
                }

                scope.calculate_calo_week.load = function(){
                    var warehouse_ids = [];
                    var group_id = scope.calculate_calo_week.selected.group_id;
                    var date = scope.calculate_calo_week.date;
                    angular.forEach(scope.calculate_calo_week.warehouses, function(warehouse,index){
                        if(warehouse.selected){
                            warehouse_ids.push(warehouse.id);
                        }
                    });
                    process($CFG.project+'/calculate_calo_week/list',{warehouse_ids:warehouse_ids,group_id:group_id,date:date},function(resp){
                        scope.calculate_calo_week.rows = resp.data;
                        scope.calculate_calo_week.weeks = resp.weeks;
                        scope.calculate_calo_week.sotre = resp.sotre;
                        scope.calculate_calo_week.tongtienchi = resp.tongtienchi;
                        scope.calculate_calo_week.thucdon = resp.thucdon;
                        scope.calculate_calo_week.norm = resp.norm;
                        var tong = {
                            damdv: 0,
                            damtv: 0,
                            beodv: 0,
                            beotv: 0,
                            duong: 0
                        }
                        angular.forEach(resp.data, function(food){
                            tong.damdv += food.dam_beo_duong.damdv/count(scope.calculate_calo_week.sotre);
                            tong.damtv += food.dam_beo_duong.damtv/count(scope.calculate_calo_week.sotre);
                            tong.beodv += food.dam_beo_duong.beodv/count(scope.calculate_calo_week.sotre);
                            tong.beotv += food.dam_beo_duong.beotv/count(scope.calculate_calo_week.sotre);
                            tong.duong += food.dam_beo_duong.duong/count(scope.calculate_calo_week.sotre);
                            tong.calo += food.dam_beo_duong.calo/count(scope.calculate_calo_week.sotre);
                        });
                        scope.calculate_calo_week.tong = tong;
                    },null,false);
                }
                scope.calculate_calo_week.getDayOfWeek = function(daymonth){
                    var rs = '';
                    angular.forEach(scope.calculate_calo_week.weeks, function(day,i){
                        if(daymonth == day.daymonth){
                            rs = day;
                        }
                    })
                    return rs;
                }
                /*Tính tổng calo*/
                scope.calculate_calo_week.sumCalo = function(){
                    var rs = 0;
                    angular.forEach(scope.calculate_calo_week.rows, function(food){
                        rs += (food.dam_beo_duong.damdv+food.dam_beo_duong.damtv)*scope.co_cau(null).protein +
                            (food.dam_beo_duong.beodv+food.dam_beo_duong.beotv)*scope.co_cau(null).fat +
                            food.dam_beo_duong.duong*scope.co_cau(null).sugar;
                    });
                    return rs;
                };
                // /* Tính toán tỉ lệ đạt của đạm*/
                // scope.calculate_calo_week.getTile_dat_protein = function(){
                //     var value = 0;
                //     value = (scope.calculate_calo_week.tong.damdv+scope.calculate_calo_week.tong.damtv)
                //         / (scope.calculate_calo_week.norm.nutritions.animal_protein+scope.calculate_calo_week.norm.nutritions.vegetable_protein);
                //     return value*100;
                // }
                // /* Tính toán tỉ lệ đạt của béo*/
                // scope.calculate_calo_week.getTile_dat_fat = function(){
                //     var value = 0;
                //     value = (scope.calculate_calo_week.tong.beodv+scope.calculate_calo_week.tong.beotv)
                //         / (scope.calculate_calo_week.norm.nutritions.animal_fat+scope.calculate_calo_week.norm.nutritions.vegetable_fat);
                //     return value*100;
                // }
                scope.calculate_calo_week.getTiletungloai = function(name){
                    var value = 0;
                    if(scope.calculate_calo_week.norm.nutritions) {
                        var calo = scope.calculate_calo_week.sumCalo();
                        var damdv = scope.calculate_calo_week.tong.damdv;
                        var damtv = scope.calculate_calo_week.tong.damtv;
                        var beodv = scope.calculate_calo_week.tong.beodv;
                        var beotv = scope.calculate_calo_week.tong.beotv;
                        value = scope.tong[name]/scope.selected.group.nutritions[name];
                    }
                    return value*100;
                }

                /*Tính tỉ lệ PLG*/
                scope.calculate_calo_week.getTile_PLG = function(name){
                    var rs = {
                        protein: 0,
                        fat: 0,
                        sugar: 0
                    };
                    var tongcalo = 0;
                    angular.forEach(scope.calculate_calo_week.rows, function(food,index){
                        rs.protein += scope.co_cau(scope.calculate_calo_week.calulate_for_rule).protein * food.luong1tre*food.nutritions.protein/100;
                        rs.fat += scope.co_cau(scope.calculate_calo_week.calulate_for_rule).fat * food.luong1tre*food.nutritions.fat/100;
                        rs.sugar += scope.co_cau(scope.calculate_calo_week.calulate_for_rule).sugar * food.luong1tre*food.nutritions.sugar/100;
                    });
                    tongcalo = rs.protein + rs.fat + rs.sugar;
                    rs.protein = rs.protein/tongcalo*100/count(scope.calculate_calo_week.sotre);
                    rs.fat = rs.fat/tongcalo*100/count(scope.calculate_calo_week.sotre);
                    rs.sugar = rs.sugar/tongcalo*100/count(scope.calculate_calo_week.sotre);
                    return rs;
                }
                setTimeout(function(){
                    // $.calculate_calo_week.datagrid();
                    $.calculate_calo_week.initView();
                },400);
                scope.calculate_calo_week.init();
                scope.calculate_calo_week.load();
            });
        });
    },initView: function(){
        var contaner = $('#container-calculate_calo_week');
        // var menu = $('#container-calculate_calo_week .tbl-container-header');
        var content = $('#container-calculate_calo_week .calo_week-content');
        var h_container = parseInt(contaner.css('height').replace('px',''));
        // var h_menu = parseInt(menu.css('height').replace('px','')); 
        content.css({height: h_container-65});
    }
}
