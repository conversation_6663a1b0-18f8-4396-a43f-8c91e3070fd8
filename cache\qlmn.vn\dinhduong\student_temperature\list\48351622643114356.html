<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/course_attendance.css?_=1445867642" />
<div class="tbl_container" id="tbl_container_student_temperature">
    <div class="tbl-container-header header-kh-ct" id="tb_student_temperature">
        <div class="title-kh-ct">
            <div id="header-title" class="header-title"></div>
            <div class="function-kh-ct">
                <form id="frm-storage">
					<span ng-show="student_temperature.gvcn == 1" style="margin-left:10px; float: left;">
						<select ng-model="student_temperature.select.course_id" ng-change="student_temperature.course_change()" style="height:24px">
                            <option ng-value="''">- Chọn lớp học -</option>
                            <optgroup label="{{grade.name}}" ng-repeat="grade in student_temperature.grades">
                                <option ng-value="course.id" ng-repeat="course in grade.courses">
                                    {{course.name}}
                                </option>
                            </optgroup>
                        </select>
					</span>
                    <span style="margin-left:10px; float: left;">
                        <span style="margin-right: 3px;"> 
                            Tháng: <select id="attendance_monthbox" ng-options="item.month as (item.month<10?'0'+item.month:item.month)+'/'+item.year for item in student_temperature.months" ng-model="student_temperature.select.month" style="width: 90px;height: 24px;" ng-change="student_temperature.load()"></select> 
                        </span>
                    </span>
                    <span class="btn btn-over-red color-blue" ng-click="student_temperature.load(student_temperature.today)" style="margin-top: -3px;">
                        <span class="glyphicon glyphicon-refresh"></span> Hôm nay {{student_temperature.today}}
                    </span>
                    <span ng-show="student_temperature.gvcn == 0">
                        <label style="color: #0093ff;">Khối học: {{student_temperature.select.grade_name}}</label>&nbsp;&nbsp;
                        <label style="color: coral;">Lớp học: {{student_temperature.select.course_name}}</label>&nbsp;&nbsp;
                    </span>
					<span style="float:right; margin-right:15px; margin-top:5px;"><b>NHIỆT ĐỘ CỦA HỌC SINH</b></span>
                </form>
            </div>
        </div>
    </div>
    <div style="border-left: 1px solid #ccc;">
        <div class="tbl-container-body-stu-att" style="position: relative;">
            <table id="tbl-attendance-data" class="table tbl-course_attendance" ng-model="confirmed" ng-change="student_temperature.change()" ng-style="student_temperature.getStyleTable()">
                <thead style="position: absolute;top: 0;background: #D9EDF6; z-index: 1" ng-style="{width: student_temperature.getStyleTable().width}">
                    <tr style="width: 100%" style="position: relative;">
                        <th ng-style="{width: std.col.stt,height:std.row_head.over}" rowspan="2" style="text-align: center; vertical-align: middle; border: 1px solid #ddd;">
                            <div class="col-stt" style="top: 0;height: 100%;padding-top: 15px;background: #D9EDF6;border: 1px solid #ccc;" ng-style="{width: std.col.stt+2,position:'absolute'}"><label>STT</label></div>
                        </th>
                        <th ng-style="{width: std.col.tenlop,height:std.row_head.over}" rowspan="2" style="text-align: center; vertical-align: middle; border: 1px solid #ddd;">
                            <div class="col-tenlop" style="top: 0;height: 100%;padding-top: 15px;background: #D9EDF6;border: 1px solid #ccc;" ng-style="{width: std.col.tenlop+2,position:'absolute'}">
                                <input type="text" class="form-control" placeholder="Họ và tên" ng-model-options="{ debounce: 1000 }" ng-model="student_temperature.keyword" ng-change="student_temperature.load()" style="border: 1px solid #ccc;padding: 0 5px;height: 28px;border-radius: 5px;text-align:center">
                                <i class="glyphicon glyphicon-search" style="position: absolute;right: 5px;top: 22px;opacity: 0.6;"></i>
                            </div>
                        </th>
                        <th style="border: 1px solid #ddd; text-align:center;" ng-repeat="(index, item) in student_temperature.daysOfMonth" id="{{(student_temperature.dateEqual(item,student_temperature.date)?'active_th':'')}}" ng-click="student_temperature.selectLock(item)" ng-style="{width:(item.holiday==1?std.col.day_none:std.col.day)}" class="{{class_item(item)}}">
    						<label ng-bind="item.day+'/'+item.month"></label>
                        </th>
                    </tr>
                    <tr style="width: 100%">
                        <th style="border: 1px solid #ddd; text-align:center;"
                            ng-repeat="(index, item) in student_temperature.daysOfMonth" id="{{(student_temperature.dateEqual(item,student_temperature.date)?'active_th':'')}}" ng-style="{width:(item.holiday==1?std.col.day_none:std.col.day)}" class="{{class_item(item)}}">
                            <label ng-bind="item.name"></label>
                        </th>
                    </tr>
                </thead>
                <tbody style="position: absolute; z-index: 0; margin-bottom: 23px;" ng-style="{width: student_temperature.getStyleTable().width}">
                    <tr style="width: 100%;" ng-if="false">
                        <td ng-style="{width: std.col.stt}">
                            <input type="number" ng-model="std.col.stt" style="width: 98%;height: 25px;">
                        </td>
                        <td ng-style="{width: std.col.tenlop}">
                            <input type="number" ng-model="std.col.tenlop" style="width: 98%;height: 25px;">
                        </td>
                        <th style="border: 1px solid #ddd; text-align:center;" 
                            ng-repeat="(index, item) in student_temperature.daysOfMonth" id="{{(student_temperature.dateEqual(item,student_temperature.date)?'active_th':'')}}" ng-style="{width:(item.holiday==1?std.col.day_none:std.col.day)}">
                            <input type="number" ng-model="std.col[(item.holiday==1?'day_none':'day')]" style="width: 98%;height: 25px;">
                        </th>
                    </tr>
                    <tr ng-repeat="(index,attend) in student_temperature.temperatures" ng-style="{position: 'relative'}" style="height: 25px;" class="tbl-attendance-open">
                        <td ng-style="{width: std.col.stt}" field="ck">
                            <div class="datagrid-cell-check col-stt" style="margin-left: -2px;padding-top: 3px;height: 25px;border: 1px solid #ccc;margin-top: -2px;border-top: 0px;" ng-style="{width: std.col.stt+2,position:'absolute'}">
								<label ng-bind="$index+1" style="padding-top: 1px;" class="{{(is_next_paid? 'fsBold' : '')}}"></label>
							</div>
                        </td>
                        <td ng-style="{width: std.col.tenlop}" field="name" style="text-align: left;">
                            <div class="datagrid-cell datagrid-cell-c1-name col-tenlop" style="margin-left: -2px;padding-top: 3px;height: 25px;border: 1px solid #ccc;margin-top: -2px;border-top: 0px;" ng-style="{width: std.col.tenlop+2,position:'absolute'}" ng-bind="attend.ho+' '+attend.ten"></div>
                        </td>
                        <td ng-repeat="(index, item) in student_temperature.daysOfMonth" id="{{(student_temperature.dateEqual(item, student_temperature.date)?'active_td':'')}}" ng-style="{width:(item.holiday==1?std.col.day_none:std.col.day)}" class="{{class_item(item)}}">
							<input type="text" ng-model="attend['day'+item.day]" ng-style="{color: ((attend['day'+item.day]!='' && attend['day'+item.day]!=37)? 'red': '')}" style="width:98%; text-align:center; padding:0px; border:1px solid #CCCCCC;" ng-blur="student_temperature.editAttendance(attend, item)" ng-focus="student_temperature.attendFocus(attend, item.day)" ng-if="item.holiday==0">
                        </td>
                    </tr>
                </tbody>
                <tfoot style="position: absolute; z-index: 1; background: #D9EDF6;" ng-style="{width: student_temperature.getStyleTable().width}" class="hide">
                    <tr ng-style="{position: 'relative'}">
                        <td ng-style="{width: std.col.stt+std.col.tenlop}" style="background-color: gold; text-align: center;">
                            <div class="col-stt" style="background-color: gold;padding-top: 3px; height: 25px;border: 1px solid #ccc;margin-top: -2px;border-top: 0px;" ng-style="{width: std.col.stt+std.col.tenlop+2,position:'absolute'}">
                                <b>Tổng cộng</b>
                            </div>
                        </td>
                        <td ng-repeat="(index, item) in student_temperature.daysOfMonth" id="{{(student_temperature.dateEqual(item, student_temperature.date)?'active_td':'')}}" ng-style="{width:(item.holiday==1?std.col.day_none:std.col.day)}" class="{{class_item(item)}} tAc">
							<span ng-bind="student_temperature.tongcong(item)" ng-if="item.holiday==0"></span>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
<script src="http://localhost:3000/js/dinhduong/student_temperature/student_temperature.js?_=1160006598"></script>
<script type="text/javascript">
    $.student_temperature.init('08/08/2025');
    $(window).resize(function(){
        student_temperature_buildTable()
    });
    setTimeout(function(){
        student_temperature_buildTable()
    },400)
    var std_table = $('.tbl-container-body-stu-att');
    function student_temperature_buildTable(){
        var h_container = parseInt($('#tbl_container_student_temperature').css('height'));
        std_table.css({height: h_container-71});
        var top = std_table.children().position().top;
        var left = std_table.children().position().left;
        std_table.children().children('tfoot').css({bottom: top});
        $('.col-stt').css({'margin-left':left*-1-3})
        $('.col-tenlop').css({'margin-left':left*-1-2})
    }
    std_table.scroll(function(){
        var top = std_table.children().position().top;
        var left = std_table.children().position().left;
        std_table.children().children('thead').css({top: top*-1});
        $('.col-stt').css({'margin-left':left*-1-3})
        $('.col-tenlop').css({'margin-left':left*-1-2})
        var top = std_table.children().position().top;
        std_table.children().children('tfoot').css({bottom: top});
    })
    window.onresize = student_temperature_buildTable;

    //text input fields with arrow keys
    $(document).on('keyup', 'select', function (e) {
        if (e.which == 39) { // right arrow
            $(this).closest('td').next().find('select').focus();

        } else if (e.which == 37) { // left arrow
            $(this).closest('td').prev().find('select').focus();

        } else if (e.which == 40) { // down arrow
            $(this).closest('tr').next().find('td:eq(' + $(this).closest('td').index() + ')').find('select').focus();

        } else if (e.which == 38) { // up arrow
            $(this).closest('tr').prev().find('td:eq(' + $(this).closest('td').index() + ')').find('select').focus();
        }
    })
</script>
<style type="text/css">
    tbody tr div.col-stt,
    tbody tr div.col-tenlop,
    tbody tr#active div.col-stt,
    tbody tr#active div.col-tenlop{
        background: #fff;
    }
    .glyphicon-refresh-animate {
    -animation: spin .7s infinite linear;
    -webkit-animation: spin2 .7s infinite linear;
    }

    @-webkit-keyframes spin2 {
        from { -webkit-transform: rotate(0deg);}
        to { -webkit-transform: rotate(360deg);}
    }

    @keyframes  spin {
        from { transform: scale(1) rotate(0deg);}
        to { transform: scale(1) rotate(360deg);}
    }
	input[disabled], select[disabled] {
		background-color: #eee!important;
	}
    .tbl_container {
        padding-top: 0;
    }

</style>