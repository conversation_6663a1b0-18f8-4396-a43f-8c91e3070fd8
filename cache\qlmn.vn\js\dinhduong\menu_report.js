$.menu_report = {
    module: 'menu_report',
    id: '',
    init: function (unlock, callback) {
        setTimeout(function () {
            var month = new Date().getMonth() +1;
            process('dinhduong/menu_report/list', {month: month, async: true}, function (resp) {
                var data = resp.data;
                $.menu_report.month = data.month;
                $.menu_report.months = data.months;
                $.menu_report.groupss = data.groups;
                $.menu_report.groups = data.groups;
                $.menu_report.calender = data.calender;
                $.menu_report.group_arr = data.group_arr;
                $.menu_report.warehouses = data.warehouses;
                $.menu_report.marketbill = data.marketbill;
                $.menu_report.cash_books = data.cash_books.rows;
                $.menu_report.initAngular(unlock);
                $.cash_book.init();
                if(typeof callback === 'function') {
                    callback();
                }
                // $.menu_report.group_olds = data.group_olds;
                // $.menu_report.menu_adjusts = data.menu_adjusts;
                // $.menu_report.date = data.date;
                // $.menu_report.config_cow_default = data.config_cow_default;
                // $.menu_report.config_cow = data.config_cow;
                // $.menu_report.configs = data.configs; //Config cho Setting
                // $.menu_report.service_price = data.service_price;
                // $.menu_report.measures = data.measures;
                // $.menu_report.suppliers = data.suppliers;
                // $.marketbill.init($.menu_report);
                // $.cash_book.init();
                // $.cash_book.init($.menu_report);
                // $.calculate_calo_week.init();
                // $.calculate_calo_week.init($.menu_report);
            });
        });
    }, initAngular: function (unlock) {
        angular.element($('#mainContentController')).scope().$apply(function (scope) {
            scope.menu_report || (scope.menu_report = {});
            scope.menu_report.selected = {};
            scope.menu_report.months = $.menu_report.months;
            // var today = new Date();
            // var dd = today.getDate();
            // var mm = today.getMonth()+1; //January is 0!
            // var yyyy = today.getFullYear();
            // if (dd<10) {
            //     dd = '0'+dd
            // }
            // if (mm<10) {
            //     mm = '0'+mm
            // }
            // today = mm + '/' + dd + '/' + yyyy;
            // if (typeof $.menu_report.date != 'undefined') {
            scope.menu_report.date = $.menu_report.date;
            // }else {
            //     scope.menu_report.date = today;
            // }
            scope.menu_report.marketbill = $.menu_report.marketbill;
            scope.menu_report.groupss = $.menu_report.groupss;
            scope.menu_report.groups = $.menu_report.groups;
            scope.menu_report.unlock = false;
            scope.menu_report.calender = $.menu_report.calender;
            scope.menu_report.menu_adjusts = $.menu_report.menu_adjusts;
            scope.menu_report.group_arr = $.menu_report.group_arr;
            scope.menu_report.warehouses = $.menu_report.warehouses;
            scope.menu_report.cash_books = $.menu_report.cash_books;
            scope.menu_report.configs = $.menu_report.configs;
            scope.menu_report.config_cow_default = $.menu_report.config_cow_default;
            scope.menu_report.config_cow = $.menu_report.config_cow;
            scope.menu_report.month = parseInt($.menu_report.month);
            scope.menu_report.selected = {
                month: {}
            };
            scope.keysearch = {
                grade_id: '',
                course_id: ''
            };
            var month_cache = scope.getMonth_selected();
            angular.forEach(scope.menu_report.months, function (item, index) {
                item.month = parseInt(item.month);
                if (month_cache == item.month) {
                    scope.menu_report.selected.month = item;
                }
            });
            scope.isPointTogether = function () {
                var rs = false;
                if (scope.school_point) {
                    if (count(scope.school_point.points) > 1 && scope.school_point.together === 1) {
                        rs = true;
                    }
                }
                return rs;
            };
            // angular.forEach(scope.menu_report.calender.rows, function (calende,index) {
            //     scope.menu_report.calender.rows[index]['disable'] = "disable";
            //     angular.forEach(calende, function (val,i) {
            //         if (val.class=="bg-green") {
            //             scope.menu_report.calender.rows[index]['disable'] = "";
            //         }
            //     })
            // })
            // scope.menu_report.tam = {};
            // angular.forEach(scope.menu_report.group_arr, function (group_ar,group_index) {
            //     // console.log(group_ar['id']);
            //     // var group_id = group_ar['id'];

            //     // console.log(scope.menu_report.menu_adjusts);
            //     angular.forEach(scope.menu_report.menu_adjusts, function (menu_adjust,key) {
            //         if (menu_adjust.group_id.indexOf(group_ar['id']) != -1) {
            //             scope.menu_report.tam[key] = menu_adjust;
            //         }
            //     })
            //     angular.forEach(scope.menu_report.calender.rows, function (calende,index) {
            //         angular.forEach(calende, function (val,i) {
            //             if (val.month == scope.menu_report.calender.month) {
            //                 calende[i]['class'] = "";
            //                 angular.forEach(scope.menu_report.tam, function (tams,k) {
            //                     if (tams.day == val.day) {
            //                         calende[i]['class'] = "bg-green";
            //                     }
            //                 })
            //                 // console.log(calende);
            //             }
            //         })
            //     });
            //     // console.log(scope.menu_report.calender.rows);
            //     // angular.forEach(scope.menu_report.calender.rows, function (calende,cal_index) {
            //     //     scope.menu_report.calender.rows[cal_index]['disable'] = "disable";
            //     //     angular.forEach(calende, function (val,i) {
            //     //         if (val.class=="bg-green") {
            //     //             scope.menu_report.calender.rows[cal_index]['disable'] = "";
            //     //         }
            //     //     })
            //     //     // console.log(calende);
            //     // })
            //     scope.menu_report.group_arr[group_index]['cal'] = scope.menu_report.calender.rows;
            // })
            // // console.log(scope.menu_report.group_arr);
            angular.forEach(scope.menu_report.marketbill, function (item, index) {
                var html = [];
                var mathucdon = [];
                angular.forEach(item.menu_plannings, function (group, index2) {
                    if (group.row) {
                        mathucdon.push(group.row.mathucdon);
                        angular.forEach(scope.menu_report.groupss, function (gr, i) {
                            if (gr.id == group.group_id) {
                                html.push(gr.name);
                                return;
                            }
                        })
                    }
                })
                scope.menu_report.marketbill[index].group_name = html.join(', ');
                scope.menu_report.marketbill[index].mathucdon = mathucdon.join(', ');
            });

            scope.menu_report.keysearch = function (keysearch) {
                scope.menu_report.onclickPayment_receipt(keysearch)
            };
            scope.menu_report.phieukecho = function (marketbill, type) {
                $.marketbill.phieukechoShowForm(marketbill.date, type, function () {
                    marketbill.is_bill = 1;
                });
            };
            scope.menu_report.check_ndt = function (rows) {
                var config_default = scope.menu_report.config_cow_default;
                var configs = scope.menu_report.configs;
                var check;
                var config_cow = [];
                angular.forEach(config_default, function (dlt, index) {
                    if (configs['week' + dlt] + 0 == 1) {
                        config_cow[dlt] = dlt;
                    }
                });
                if (rows) {
                    angular.forEach(rows, function (row, index) {
                        if (row.class == "bg-green") {
                            if (typeof config_cow[row.week_id] == 'undefined') {
                                check = true;
                            }
                        }
                    });
                    return check;
                }

            };

            scope.menu_report.calotuan = function (rows, month, disable, group_id) {
                if (!disable) {
                    var warehouse_ids = [];
                    if (!group_id) {
                        group_id = scope.menu_report.selected.group_id;
                    }
                    angular.forEach(scope.menu_report.warehouses, function (warehouse) {
                        if (warehouse.selected) {
                            warehouse_ids.push(warehouse.id);
                        }
                    });

                    var date = [];
                    for (var i in rows) {
                        var day = rows[i];
                        if (day.month == month.month) {
                            date = [day.day, day.month, month.year];
                            break;
                        }
                    }
                    if (date.length > 0 && warehouse_ids.length > 0 && group_id) {
                        date = date.join('/');
                        scope.menu_report.printPreview(date, warehouse_ids, group_id);
                    } else {
                        alert("Vui lòng chọn nhóm tuổi");
                    }
                }
            };
            scope.menu_report.printPreview = function (date, warehouse_ids, group_id) {
                if (!warehouse_ids) {
                    angular.forEach(scope.calculate_calo_week.warehouses, function (warehouse) {
                        if (warehouse.selected) {
                            warehouse_ids.push(warehouse.id);
                        }
                    });
                }
                var urls = [$CFG.remote.base_url, $CFG.project, 'calculate_calo_week', 'printPreview'];
                var params = [
                    'warehouse_ids=' + warehouse_ids.join(','),
                    'group_id=' + group_id,
                    'date=' + date
                ];
                window.open(urls.join('/') + '?' + params.join('&'));
            };
            scope.menu_report.sotinhtienan = function (date) {
                $.cash_book.initCashBook();
                $.cash_book.cashForm(null, date, function () {
                    process('dinhduong/menu_report/list', {
                        month: scope.menu_report.selected.month.month,
                        async: true
                    }, function (resp) {
                        var data = resp.data;
                        scope.$apply(function () {
                            scope.menu_report.cash_books = data.cash_books.rows;
                        });
                    });
                });
            };
            scope.menu_report.delsotinhtienan = function (date) {
                process('dinhduong/menu_report/checkCashBookOnDeleting', {
                    date: date,
                    async: true,
                }, function (resp) {
					if (!resp.allow_delete) {
						$.messager.alert('Thông báo', 'Bạn không thể xóa sổ tính tiền ăn các ngày trong tháng này, do đã tính tiền ăn ở tháng sau! Vui lòng xóa lần lượt theo từng tháng!');
					}else{
						var captcha = resp.captcha_required ? $CFG.dialog_captcha('delete_cash_book') : '';
						$.cash_book.del(null, date, function () {
							setTimeout(function () {
								scope.$apply(function () {
									process('dinhduong/menu_report/list', {
										month: scope.menu_report.selected.month.month,
										async: true
									}, function (resp) {
										var data = resp.data;
										scope.$apply(function () {
											scope.menu_report.cash_books = data.cash_books.rows;
										});
									});
								});
							})
						}, captcha);
					}
                }, null, false);
            };
            scope.menu_report.delphieukecho = function (date, market) {
                setTimeout(function (resp) {
                    $.marketbill.del_bill(date, function (resp) {
                        if (!resp) {
                            return;
                        }
                        if (resp.result == 'success') {
                            scope.$apply(function () {
                                market.is_bill = 0;
                            });
                        }
                    });
                });
            };

            scope.menu_report.group_selected = function (group_id) {
                if (!group_id) {
                    group_id = scope.menu_report.selected.group_id;
                }
                var cal = {};
                scope.menu_report.tam = {};
                angular.forEach(scope.menu_report.menu_adjusts, function (menu_adjust, key) {
                    if (menu_adjust.group_id.indexOf(group_id) > -1) {
                        scope.menu_report.tam[key] = menu_adjust;
                    }
                });
                var cal_new = {};
                angular.forEach(scope.menu_report.calender.rows, function (calende, index) {
                    cal_new[index] = {};
                    angular.forEach(calende, function (day, i) {
                        cal_new[index][i] = {};
                        angular.forEach(day, function (val, j) {
                            cal_new[index][i][j] = val;
                        })
                    })
                });
                angular.forEach(cal_new, function (calende, index) {
                    angular.forEach(calende, function (val, i) {
                        calende[i]['class'] = "";
                        angular.forEach(scope.menu_report.tam, function (tams, k) {
                            if (val.month == scope.menu_report.calender.month) {
                                if (tams.day == val.day) {
                                    calende[i]['class'] = "bg-green";
                                }
                            }
                        })

                    })
                });
                // angular.forEach(cal_new, function (calende,index) {
                //     cal_new[index]['disable'] = "disable";
                //     angular.forEach(calende, function (val,i) {
                //         if (val.class=="bg-green") {
                //             cal_new[index]['disable'] = "";
                //         }
                //     })
                //     // console.log(calende);
                // })
                return cal_new;
            };
            scope.menu_report.changeCL = function (group_id, key) {
                if (group_id) {
                    $('#table-' + group_id).show();
                    $('#lc-' + group_id).show();
                    $('#cl-' + group_id).hide();
                }
            };

            scope.menu_report.changeLC = function (group_id, key) {
                if (group_id) {
                    $('#table-' + group_id).hide();
                    $('#lc-' + group_id).hide();
                    $('#cl-' + group_id).show();
                }
            };
            scope.menu_report.monthChange = function () {
                scope.setMonth_selected(scope.menu_report.selected.month.month);
                process('dinhduong/menu_report/list', {
                    month: scope.menu_report.selected.month.month,
                    async: true
                }, function (resp) {
                    var data = resp.data;
                    scope.$apply(function () {
                        scope.menu_report.groups = data.groups;
                        scope.menu_report.grades = data.grades;
                        scope.menu_report.marketbill = data.marketbill;
                        scope.menu_report.cash_books = data.cash_books.rows;
                        scope.menu_report.calender = data.calender;
                        scope.menu_report.menu_adjusts = data.menu_adjusts;
                        scope.menu_report.group_arr = data.group_arr;
                        scope.menu_report.groupss = data.groups;
                        scope.menu_report.classes = data.classes;
                        scope.menu_report.rows = [];
                        scope.menu_report.students = data.students;
                        scope.menu_report.fee_config = data.fee_config;
                        scope.menu_report.months = data.months;
                        scope.menu_report.month = parseInt(data.month);
                        scope.menu_report.selected = {
                            month: {}
                        };
                        scope.keysearch = {
                            grade_id: '',
                            course_id: ''
                        };
                        angular.forEach(scope.menu_report.months, function (item, index) {
                            item.month = parseInt(item.month);
                            if (scope.menu_report.month == item.month) {
                                scope.menu_report.selected.month = item;
                            }
                        });
                        angular.forEach(scope.menu_report.months, function (item, index) {
                            item.month = parseInt(item.month);
                            if (scope.menu_report.month == item.month) {
                                scope.menu_report.selected.month = item;
                            }
                        });

                        angular.forEach(scope.menu_report.marketbill, function (item, index) {
                            var html = [];
                            var mathucdon = [];
                            angular.forEach(item.menu_plannings, function (group, index2) {
                                if (group.row) {
                                    mathucdon.push(group.row.mathucdon);
                                    angular.forEach(scope.menu_report.groupss, function (gr, i) {
                                        if (gr.id == group.group_id) {
                                            html.push(gr.name);
                                            return;
                                        }
                                    });
                                }
                            });
                            scope.menu_report.marketbill[index].group_name = html.join(', ');
                            scope.menu_report.marketbill[index].mathucdon = mathucdon.join(', ');
                        });
                    });
                });
            };
            scope.menu_report.getDataSelected = function () {
                var rs = '';
                angular.forEach(scope.menu_report.weeks, function (week, index) {
                    rs += week.mlp + ',';
                });
                return rs;
            };
            scope.menu_report.onChangeGroups = function () {
                scope.menu_report.load();
            };
            scope.menu_report.dateChange = function () {
                scope.menu_report.load();
            };
            scope.menu_report.onChangeMP = function (week, menu_plannings, selected) {
                if (selected) {
                    angular.forEach(menu_plannings, function (menu_planning, index) {
                        if (menu_planning.id == selected) {
                            angular.forEach(menu_planning.row.meals, function (meal, k) {
                                week[k] = "";
                                angular.forEach(meal.dishes, function (me, i) {
                                    week[k] += me.name + ', ';
                                })
                            })
                        }
                    });
                } else {
                    angular.forEach(menu_plannings, function (menu_planning, index) {
                        if (menu_planning.id != selected) {
                            angular.forEach(menu_planning.row.meals, function (meal, k) {
                                week[k] = "";
                                angular.forEach(meal.dishes, function (me, i) {
                                    week[k] = '';
                                })
                            })
                        }
                    });
                }
            };
            scope.menu_report.load = function () {
                var group_id = scope.menu_report.selected.group_id;
                var date = scope.menu_report.date;
                if (group_id && date) {
                    process($CFG.project + '/menu_report/weekly_menu', {
                        group_id: group_id,
                        date: date
                    }, function (resp) {
                        scope.menu_report.weeks = resp.weeks;
                        angular.forEach(scope.menu_report.weeks, function (week, index) {
                            angular.forEach(week.menu_pl, function (menu, k) {
                                if (menu.name == week.mathucdon) {
                                    week.mlp = week.menu_pl[k].id;
                                }
                            })
                        });
                        // scope.menu_report.showFormPrint = scope.menu_report.showFormPrint();
                    }, null, false);
                } else {
                    scope.menu_report.weeks = '';
                }

            };

            scope.menu_report.setLinkExcelWeekly = function () {

                var thucdons = [];
                var buasang = [];
                var buatrua = [];
                var buaxe = [];
                var buaphu = [];
                var MyRows = $('table#tbl_weekly_menu').find('tbody').find('tr');
                for (var i = 0; i < MyRows.length; i++) {
                    var td_thucdon = $(MyRows[i]).find('td:eq(1)');
                    var select = td_thucdon.find('select');
                    var selected_val = select.find(':selected').text();
                    var arr_selected_val = selected_val.split(" ");
                    selected_val = arr_selected_val.join('_');
                    thucdons.push(selected_val);

                    var td_buasang = $(MyRows[i]).find('td:eq(2)');
                    var text_buasang = td_buasang.find('input').val();
                    var arr_text_buasang = text_buasang.split(" ");
                    text_buasang = arr_text_buasang.join('_');
                    buasang.push(text_buasang);

                    var td_trua = $(MyRows[i]).find('td:eq(3)');
                    var text_trua = td_trua.find('input').val();
                    var arr_text_trua = text_trua.split(" ");
                    text_trua = arr_text_trua.join('_');
                    buatrua.push(text_trua);

                    var td_xe = $(MyRows[i]).find('td:eq(4)');
                    var text_xe = td_xe.find('input').val();
                    var arr_text_xe = text_xe.split(" ");
                    text_xe = arr_text_xe.join('_');
                    buaxe.push(text_xe);

                    var td_phu = $(MyRows[i]).find('td:eq(5)');
                    var text_phu = td_phu.find('input').val();
                    var arr_text_phu = text_phu.split(" ");
                    text_phu = arr_text_phu.join('_');
                    buaphu.push(text_phu);
                }

                var startweek = $('#startweek').val();
                var stopweek = $('#stopweek').val();

                var urls_export = $CFG.remote.base_url + '/report/dinhduong/menu_report/excel_weekly_menu' + "?startweek=" + startweek + "&stopweek=" + stopweek + "&thucdon=" + thucdons.join('|') + "&buasang=" + buasang.join('|') + "&buatrua=" + buatrua.join('|') + "&buaxe=" + buaxe.join('|') + "&buaphu=" + buaphu.join('|');
                window.location.assign(urls_export);
            };
            scope.menu_report.openDialogPrint = function () {
                if (scope.menu_report.weeks) {
                    var self = this;
                    // var urls_export = [$CFG.remote.base_url,$CFG.project,'menu_report','exportPrint'];
                    // var btn = $('<a id="print_weekly" class="btn btn-default" title="Xem và in" ng-click="menu_report.showFormPrint()">In</a>');
                    // var link = $('<a id="excel_weekly" class="btn btn-default" href="javascript:void(0)" ng-click="menu_report.setLinkExcelWeekly()">Xuất Excel!</a>');
                    // $.dm_datagrid.showAddForm(
                    //     {
                    //         module: $CFG.project+'/'+self.module,
                    //         title:'In thực đơn tuần',
                    //         draggable: true,
                    //         fullScreen: false,
                    //         showButton: false,
                    //         size: size.small,
                    //         content: function (element, dialogRef) {
                    //             var html = $('#export-dialog-lenthupham').css({'display':'','height':'100'});
                    //             element.html('').append(html);
                    //             scope.menu_report.showFormPrint();
                    //         }
                    //     }
                    // );


                    var urls_export = ['tmp', $CFG.project, 'menu_report'];
                    $.dm_datagrid.showEditForm({
                        title: 'In thực đơn tuần',
                        size: size.normal,
                        showButton: false,
                        content: function (element) {
                            loadForm(urls_export.join('/'), 'popup_thucdontuan.html', {async: true}, function (resp) {
                                scope.$apply(function () {
                                    $(element).html(scope.compile(resp, scope));
                                });
                            });
                        }
                    });
                } else {
                    alert("Vui lòng chọn nhóm tuổi !")
                }
            };
            scope.menu_report.showFormPrint = function () {

                // var self = this;
                // var urls_export = [$CFG.remote.base_url,$CFG.project,'menu_report','exportPrint'];
                // var btn = $('<input type="button" name="Xem và in" value="Xem và in" ng-click="menu_report.showFormPrint()" style="margin-left: 25px; margin-left: 90%; margin-top: 1%; margin-bottom: 1%;">');

                //     $('#frm-weekly').html('').append(btn).printPage({
                //         url: urls_export.join('/')+'?group_id='+scope.menu_report.selected.group_id+'&date='+scope.menu_report.date,
                //         attr: "href",
                //         message:"Phiếu xuất kho đang được tạo ..."
                //     });
                // var html = $('#export-dialog-lenthupham').css({'display':'','height':'200'});
                // var urls_export = [$CFG.remote.base_url,$CFG.project,'menu_report','exportPrint'];
                // var btn = $('<a id="print_weekly" class="btn btn-default" title="Xem và in" ng-click="menu_report.showFormPrint()">In</a>');
                // btn.printPage({
                //     url: urls_export.join('/')+'?month=123',
                //     attr: "href",
                //     message:"Phiếu đang được tạo ..."
                // });
                // $('#weekly-function').html('').append(btn);
                var thucdons = [];
                var buasang = [];
                var buatrua = [];
                var buaxe = [];
                var buaphu = [];
                var MyRows = $('table#tbl_weekly_menu').find('tbody').find('tr');
                for (var i = 0; i < MyRows.length; i++) {
                    var td_thucdon = $(MyRows[i]).find('td:eq(1)');
                    var select = td_thucdon.find('select');
                    var selected_val = select.find(':selected').text();
                    var arr_selected_val = selected_val.split(" ");
                    selected_val = arr_selected_val.join('_');
                    thucdons.push(selected_val);

                    var td_buasang = $(MyRows[i]).find('td:eq(2)');
                    var text_buasang = td_buasang.find('input').val();
                    var arr_text_buasang = text_buasang.split(" ");
                    text_buasang = arr_text_buasang.join('_');
                    buasang.push(text_buasang);

                    var td_trua = $(MyRows[i]).find('td:eq(3)');
                    var text_trua = td_trua.find('input').val();
                    var arr_text_trua = text_trua.split(" ");
                    text_trua = arr_text_trua.join('_');
                    buatrua.push(text_trua);

                    var td_xe = $(MyRows[i]).find('td:eq(4)');
                    var text_xe = td_xe.find('input').val();
                    var arr_text_xe = text_xe.split(" ");
                    text_xe = arr_text_xe.join('_');
                    buaxe.push(text_xe);

                    var td_phu = $(MyRows[i]).find('td:eq(5)');
                    var text_phu = td_phu.find('input').val();
                    var arr_text_phu = text_phu.split(" ");
                    text_phu = arr_text_phu.join('_');
                    buaphu.push(text_phu);
                }

                var startweek = $('#startweek').val();
                var stopweek = $('#stopweek').val();


                // window.location.assign(urls_export);
                var self = this;
                var urls_export = [$CFG.remote.base_url, $CFG.project, 'menu_report', 'exportPrint'];
                var btn = $('<a id="print_weekly" class="btn btn-default" title="Xem và in"><span class="glyphicon glyphicon-print"></span>Xem và In</a>');
                var link = $('<a id="excel_weekly" style="margin-left:10px;" class="btn btn-default" href="javascript:void(0)" ng-click="menu_report.setLinkExcelWeekly()">Xuất Excel!</a>');
                var html = $('#export-dialog-lenthupham').css({'display': '', 'height': '200'});

                var url_link = $CFG.remote.base_url + '/report/dinhduong/menu_report/excel_weekly_menu' + "?startweek=" + startweek + "&stopweek=" + stopweek + "&thucdon=" + thucdons.join('|') + "&buasang=" + buasang.join('|') + "&buatrua=" + buatrua.join('|') + "&buaxe=" + buaxe.join('|') + "&buaphu=" + buaphu.join('|');
                link.attr('href', url_link);
                html.html('').append(btn).append(link);
                btn.printPage({
                    url: urls_export.join('/') + '?startweek=' + startweek + '&stopweek=' + stopweek + '&thucdon=' + thucdons.join('|') + '&buasang=' + buasang.join('|') + '&buatrua=' + buatrua.join('|') + '&buaxe=' + buaxe.join('|') + '&buaphu=' + buaphu.join('|'),
                    attr: "href",
                    message: "Phiếu lên thực đơn đang được tạo ..."
                });


            }
            scope.suppliers = null;
            scope.supplier = '';
            scope.changeType = function (type) {
                var method = ['listFood', 'listFoodByMarket', 'listFoodAllPoints', 'qtctNCC', 'listFoodByMarketVat'];
                if (method.indexOf(type) === -1) {
                    scope.isPrint = false;
                } else {
                    scope.isPrint = true;
                }
                if (['listFood', 'listFoodByMarket', 'qtctNCC', 'listFoodByMarketVat'].includes(type) && scope.suppliers === null) {
                    process($CFG.project + '/action/getSuppliers', {async: true}, function (resp) {
                        scope.suppliers = resp.data.map;
                    });
                }
            };

            scope.checkBeginEnd = function (begin, end) {
                var tmp_begin = begin.split("/");
                var tmp_end = end.split("/");
                var check_valid = 0;
                if(tmp_begin.length==3 && tmp_end.length==3){
                    if(tmp_begin[1]==tmp_end[1] && tmp_begin[2]==tmp_end[2]){
                        check_valid = 1;
                    }
                }
                return check_valid;
            };

            scope.exportListFood = function () {
                var rptDate = scope.menu_report.selected.month.year+'-'+scope.menu_report.selected.month.month+'-1';
                $.dm_datagrid.showAddForm(
                    {
                        module: $CFG.project + '/' + self.module,
                        title: 'Bảng kê thực phẩm',
                        fullScreen: false,
                        showButton: false,
                        content: function (element) {
                            loadForm($CFG.remote.base_url + '/tmp/dinhduong/menu_report/bang-ke-thuc-pham.html', '', {async: true}, function (resp) {
                                scope.$apply(function () {
                                    /*Set thông tin cho form*/
                                    scope.begin = getCurrentDate(rptDate).first;
                                    scope.end = getCurrentDate(rptDate).last;
                                    scope.warehouse = '2';
                                    scope.type = 'exportListFood';
                                    scope.showTypeReport = true;
                                    scope.isPrint = false;
                                    $(element).html(scope.compile(resp, scope));
                                })
                            });
                        }
                    }
                );
            };

            scope.ExportSoBaoAn = function () {
                $.dm_datagrid.showAddForm(
                    {
                        module: $CFG.project + '/' + self.module,
                        title: 'Sổ báo thực phẩm theo điểm trường',
                        fullScreen: false,
                        showButton: false,
                        size: 380,
                        content: function (element) {
                            loadForm($CFG.remote.base_url + '/tmp/dinhduong/menu_report/so_bao_an-dialog.html', '', {async: true}, function (resp) {
                                scope.$apply(function () {
                                    /*Set thông tin cho form*/
                                    scope.date = getCurrentDate().first;
                                    scope.warehouse = '2';
                                    scope.type = 'exportSoBaoAn';
                                    scope.showTypeReport = true;
                                    scope.isPrint = false;
                                    $(element).html(scope.compile(resp, scope));
                                })
                            });
                        }
                    }
                );
            };

        });
    },
    tkthuchithangShowForm: function () {
        var self = this;
        var self = this;
        $.dm_datagrid.showAddForm({
            module: $CFG.project + '/' + self.module,
            action: '',
            title: 'Tổng kết thu trong tháng (Theo lớp)',
            size: size.wide,
            fullScreen: true,
            showButton: true,
            content: function (element, dialogRef) {
                var html = ``;
                loadForm($CFG.project + '/' + self.module, 'paymentByClass', {}, function (html) {
                    $.menu_report.angular(element, html, function (scope) {
                        var datas = scope.menu_report;
                        angular.forEach(datas.classes, function (value, key) {
                            datas.classes[key]['siso'] = 0;
                            datas.classes[key]['tronthang'] = 0;
                            datas.classes[key]['sodongdu'] = 0;
                            datas.classes[key]['chuadongdu'] = 0;
                            datas.classes[key]['truythu'] = 0;
                            datas.classes[key]['thatthu'] = 0;
                            datas.classes[key]['totals'] = 0;
                            datas.classes[key]['khoanthus'] = {};
                            angular.forEach(datas.fee_config.data, function (fee_val, fee_key) {
                                datas.classes[key]['khoanthus'][fee_val['fee_category_id']] = {};
                                datas.classes[key]['khoanthus'][fee_val['fee_category_id']]['id'] = fee_val['fee_category_id'];
                                datas.classes[key]['khoanthus'][fee_val['fee_category_id']]['name'] = fee_val['fee_category_name'];
                                datas.classes[key]['khoanthus'][fee_val['fee_category_id']]['thu'] = 0;
                            });
                        });
                        // tinh cong gop cac gia tri theo tung lop
                        angular.forEach(datas.students, function (val, key) {
                            angular.forEach(val, function (stu_val, stu_key) {
                                if (stu_key == 'course_id') {
                                    datas.classes[val['course_id']]['siso']++;
                                }
                                if (stu_key == 'is_not_attending' && stu_val == 1) {
                                    datas.classes[val['course_id']]['tronthang']++;
                                }
                                if (stu_key == 'fee_config' && val['is_not_attending'] != 1) {
                                    angular.forEach(val['fee_config'], function (fee_val, fee_key) {
                                        datas.classes[val['course_id']]['khoanthus'][fee_val['fee_category_id']]['thu'] += fee_val['amount_rule'];
                                        datas.classes[val['course_id']]['totals'] += fee_val['amount_rule'];
                                    });
                                }
                            });
                            if (val['is_not_attending'] != 1 && val['paid_missing'] == 0) {
                                datas.classes[val['course_id']]['sodongdu']++;
                            } else if (val['is_not_attending'] != 1 && val['paid_missing'] != 0) {
                                datas.classes[val['course_id']]['chuadongdu']++;
                            }
                        });
                        scope.menu_report = datas;
                    });
                });
            }, buttons: [{
                id: 'btn-print',
                icon: 'glyphicon glyphicon-print',
                label: 'In báo cáo',
                cssClass: 'btn-default',
                action: function (dialogRef) {

                }
            }]
        });
    },
    angular: function (element, resp, callback, dialogRef) {
        var form = '<div >' + resp + '</div>';
        angular.element($('#mainContentController')).scope().$apply(function (scope) {
            $(element).html(scope.compile(form, scope));
            if (typeof callback === 'function') {
                callback(scope);
            }
        });

    },
    showWeeklyMenuForm: function (callback) {
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project + '/' + self.module,
                action: 'weekly_menu',
                title: 'Thêm mới',
                showButton: false,
                // size: size.wide,
                fullScreen: true,
                content: function (element) {
                    loadForm($CFG.project + '/' + self.module, 'weekly_menu', {dataType: 'json'}, function (resp) {
                        $.menu_report.angular(element, resp.html, function (scope) {

                        });
                    })
                },
                // buttons: [
                //     {
                //         id: 'btn-print',
                //         icon: 'glyphicon glyphicon-print',
                //         label: 'In báo cáo',
                //         cssClass: 'btn-default',
                //         action: function (dialogRef) {
                //             var scope = angular.element($('#mainContentController')).scope();
                //             scope.menu_report.openDialogPrint();
                //         }
                //     }
                // ]
            },
            function (resp) {
                if (typeof callback === 'function') {
                    callback(resp);
                } else {
                    $("#tbl_" + self.module).datagrid('reload');
                }
            }
        );
    },
};
