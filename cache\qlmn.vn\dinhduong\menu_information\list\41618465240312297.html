<link rel="stylesheet" type="text/css" href="http://localhost:3000/css/dinhduong/menu_information.css" />
<div class="tbl_container">
	<div class="tbl-container-header header-kh-ct" id="tb_menu_information">
		<div class="title-kh-ct">
			<div id="header-title" class="header-title" tbl-menu-in-group="menu_information"></div>
			<div class="function-kh-ct">
				<div class="btn-group">
					<button type="button" class="btn btn-primary" ng-click="editAction(menu_informations)">
				  		<span class="glyphicon glyphicon-edit"></span>Lưu
				  	</button>
					<button type="button" title="Cập nhật lại dữ liệu gốc" onclick="$.menu_information.restorDefault()" class="btn btn-primary">
			  			<span class="glyphicon glyphicon-erase"></span><PERSON><PERSON><PERSON> thay đổ<PERSON>
				  	</button>
					<button type="button" title="Tải lại dữ liệu" onclick="$.menu_information.reload()" class="btn btn-primary">
			  			<span class="glyphicon glyphicon-refresh"></span>Tải lại
				  	</button>
				</div>
			</div>	
			<div class="support-video">
                <a target="_blank" ng-href="{{$CFG.help_base_url+'/pms/videos/cauhinh.mp4'}}">
                    <img src="http://localhost:3000/images/icon_hotro1.gif">
                </a>
            </div>		
		</div>
	</div>
	<div id="tbl_menu_information" class="tbl-container-body" 
		ng-init="">
		<div class="col-md-12 col-sm-12 month-container">
			<div class="calender-content">
				<table class="table-calender">
					<thead>
						<tr>
							<th colspan="4" class="year">
								<label >Thiết lập tên thông tin thực đơn</label>
							</th>
						</tr>
						<tr class="head-weekdays">
							<th><label>Thứ tự</label></th>
							<!-- <th><label>Định nghĩa</label></th> -->
							<th><label>Tên mặc định</label></th>
							<th><label>Tên chỉnh sửa</label></th>
							<th><label>Thứ tự</label></th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="(index,row) in menu_informations">
							<td class="" id="" width="70">
								<div class="day-content" >
									<label class="" ng-bind="index+1"></label>
								</div>
							</td>
							<td class="" id="" >
								<div class="day-content" >
									<label class="">
										<span ng-bind="row.menu_name"></span> 
										<span>
											(<span class="{{row.class_note}}" ng-bind="row.note" ></span>)
										</span>
																			</label>
								</div>
							</td>
							<td class="" id="" >
								<div class="day-content" >
									<input class="checkbox-day" ng-model="row.name" >
								</div>
							</td>
							<td class="" id="" >
								<div class="day-content" >
									<input class="checkbox-day" type="number" ng-model="row.order" >
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
<script src="http://localhost:3000/js/dinhduong/menu_information.js"></script>
<script type="text/javascript">
	// $(function(){
	// 	setTimeout(function(){
			$.menu_information.init();
	// 	},1000);
	// });
</script>


