.tbl_container{
	padding-top: 40px;
	text-align: left !important;
}

.tbl_container label{
	font-size: 15px;
	padding-top: 3px; 
}

.tbl_container td label,.tbl_container .head-weekdays th label{
	font-weight: initial;
}

.tbl_container .tbl-container-body-stu-att th{
	padding: 0px !important;
}
.tbl_container .tbl-container-body-stu-att td{
	padding: 2px 0px 0px 0px !important;
}
.tbl_container .tbl-container-body-stu-att td select{
	border: 0px;
}
.tbl_container .tbl-container-body-stu-att{
	overflow: scroll;
	background: #fff;
}
/*.tbl_container .tbl-container-body-stu-att .tbl-student_attendance{
	
	/*background: #fff;
	height: 80% !important;*/
/*}*/
.tbl-container-body-stu-att th.col-day,
.tbl-container-body-stu-att td.col-day{
	width: 200px;
}
.tbl-container-body-stu-att th.col-day-none,
.tbl-container-body-stu-att td.col-day-none{
	width: 200px;
}
.tbl-container-body-stu-att tfoot tr td{
	border-right: 1px solid #ccc;
}
.tbl_container .tbl-container-body .table{
	
}

.centered {
   text-align: center;
   margin-left: 19%;
}

.textbox{
	/*width: 120px !important;*/
	height: 24px;
	position: relative;
}

.pull-right{
    background-color: orange;
    margin-right: 5px;
}

#frm-storage{
    margin-top: 10px;
    margin-bottom: 10px;
}

input.checkbox-day{
	display: none;
}
td.day-enable:hover input.checkbox-day{
	display: block;
}
td.day-enable:hover .icon-checked{
	display: none;
}

/*.table thead>tr>th {
	text-align: center;
	vertical-align: bottom;
	border-bottom: 2px solid #ddd;
}*/
.table>tbody>tr>td {
	text-align: center;
	border: 1px solid #ddd;
}
.day-disabled .icon-checked{
	display:none;
}

.tbl-student_attendance thead tr #active_th{
	background-color: #fcf8e3;
}

.tbl-student_attendance tbody tr #active_td{
	background-color: #fcf8e3;
}

.tbl-student_attendance tbody #active{
	background: #96e8e8;
}
.btn-cell-lock:hover{
	cursor: pointer;
}