angular_app.controller('exportController', ['$scope', function ($scope) {
    $scope.control = 'export';
    $scope.templates = {

    };
    $scope.init = function () {
        var date = new Date();
        var month = date.getMonth() + 1;
        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                var dateExport = [];
                data.dateExport.forEach(function (element) {
                    dateExport.push({
                        'date': getDate(element.date)
                    });
                });
                $scope.months = data.months;
                $scope.month = month.toString();
                $scope.dateExport = dateExport;
            });
        });
    };
    $scope.monthChange = function () {
        var month = $scope.month;
        process($CFG.project + '/' + $scope.control + '/list?month=' + month, {async: true}, function (resp) {
            $scope.$apply(function () {
                var data = resp.data;
                var dateExport = [];
                data.dateExport.forEach(function (element) {
                    dateExport.push({
                        'date': getDate(element.date)
                    });
                });
                $scope.dateExport = dateExport;
            });
        });
    }
}]);