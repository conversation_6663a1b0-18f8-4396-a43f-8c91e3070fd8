{"name": "axios-cookiejar-support", "version": "6.0.4", "description": "Add tough-cookie support to axios.", "keywords": ["axios", "cookie", "cookiejar", "cookies", "tough-cookie"], "homepage": "https://github.com/3846masa/axios-cookiejar-support#readme", "bugs": {"url": "https://github.com/3846masa/axios-cookiejar-support/issues"}, "repository": {"type": "git", "url": "git+https://github.com/3846masa/axios-cookiejar-support.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "type": "module", "exports": {".": {"node": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "browser": {"types": "./dist/index.d.ts", "default": "./noop.js"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "noop.js"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc --project tsconfig.build.json", "format": "pnpm run --sequential \"/^format:.*/\"", "format:eslint": "eslint --fix .", "format:prettier": "prettier --write .", "prelint": "pnpm run build", "lint": "pnpm run \"/^lint:.*/\"", "lint:eslint": "eslint .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "test": "vitest run"}, "dependencies": {"http-cookie-agent": "^7.0.2"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#6d886368a53bbe52a8dc599de1b24441691439ed", "@babel/plugin-proposal-explicit-resource-management": "7.27.4", "@babel/preset-env": "7.28.0", "@babel/preset-typescript": "7.27.1", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "7.1.0", "@semantic-release/git": "10.0.1", "@types/eslint": "9.6.1", "@types/node": "20.19.8", "axios": "1.10.0", "disposablestack": "1.1.7", "rimraf": "6.0.1", "semantic-release": "24.2.7", "tough-cookie": "5.1.2", "typescript": "5.8.3", "vitest": "3.2.4"}, "peerDependencies": {"axios": ">=0.20.0", "tough-cookie": ">=4.0.0"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=20.0.0"}, "publishConfig": {"access": "public"}, "pnpm": {"patchedDependencies": {"@semantic-release/git@10.0.1": "patches/@<EMAIL>"}}}