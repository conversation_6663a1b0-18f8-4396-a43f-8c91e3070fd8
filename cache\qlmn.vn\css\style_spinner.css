#statusloading-mash{
	position: fixed;
	width: 200%;
	height: 200%;
	z-index: 999999;
	background: rgba(6,6,6,0.3);
	top: 0;
	left: 0;
}
.spinner-container-loading{
    position: fixed;
    min-width: 150px;
    width: auto;
    height: 40px;
    top: 40%;
    left: 40%;
    z-index: 99999;
    background: rgba(251, 176, 0, 0.75);
    padding: 5px 8px 9px 13px;
    border-radius: 25px;
}
.spinner-container-loading>span{
    float: left;
    margin-top: 5px;
}
.spinner-container{
	position:relative;
	float:left;
	width:30px;
	height:30px;
	margin-top:0px;
	padding:0px;
	border:none;
	border-radius:5px;
}

@keyframes spinner1{
	to{
		transform:rotate(360deg)
	}
}
@-webkit-keyframes spinner1{
	to{
		-webkit-transform:rotate(360deg)
	}
}
.spinner1{
	min-width:24px;
	min-height:24px
}
.spinner1:before{
	content:'Loading…';
	position:absolute;
	top:3px;
	left:3px;
	width:24px;
	height:24px;
	/*margin-top:-10px;
	margin-left:-10px*/
}
.spinner1:not(:required):before{
	content:'';
	border-radius:50%;
	border:2px solid rgba(0,0,0,.3);
	border-top-color:rgba(0,0,0,.6);
	animation:spinner1 .6s linear infinite;
	-webkit-animation:spinner1 .6s linear infinite
} 

@keyframes spinner2{
	to{
		transform:rotate(360deg)
	}
}
@-webkit-keyframes spinner2{
	to{
		-webkit-transform:rotate(360deg)
	}
}
.spinner2{
	min-width:24px;
	min-height:24px
}
.spinner2:before{
	content:'Loading…';
	position:absolute;
	top:3px;
	left:3px;
	width:24px;
	height:24px;
	/*margin-top:-13px;
	margin-left:-13px*/
}
.spinner2:not(:required):before{
	content:'';
	border-radius:50%;
	border:1px solid #ccc;
	border-top-color:#03ade0;
	animation:spinner2 .6s linear infinite;
	-webkit-animation:spinner2 .6s linear infinite
} 

@keyframes spinner3{
	to{
		transform:rotate(180deg)
	}
}
@-webkit-keyframes spinner3{
	to{
		-webkit-transform:rotate(180deg)
	}
}
.spinner3{
	min-width:24px;
	min-height:24px
}
.spinner3:before{
	content:'Loading…';
	position:absolute;
	top:3px;
	left:3px;
	width:24px;
	height:24px;
	/*margin-top:-10px;
	margin-left:-10px*/
}
.spinner3:not(:required):before{
	content:'';
	border-radius:50%;
	border:2px solid transparent;
	border-top-color:#03ade0;
	border-bottom-color:#03ade0;
	animation:spinner3 .8s ease infinite;
	-webkit-animation:spinner3 .8s ease infinite
} 

@keyframes spinner4{
	to{
		transform:rotate(360deg)
	}
}
@-webkit-keyframes spinner4{
	to{
		-webkit-transform:rotate(360deg)
	}
}
.spinner4{
	min-width:24px;
	min-height:24px
}
.spinner4:before{
	content:'Loading…';
	position:absolute;
	top:3px;
	left:3px;
	width:24px;
	height:24px;
	/*margin-top:-10px;
	margin-left:-10px*/
}
.spinner4:not(:required):before{
	content:'';
	border-radius:50%;
	border-top:2px solid #03ade0;
	border-right:2px solid transparent;
	animation:spinner4 .6s linear infinite;
	-webkit-animation:spinner4 .6s linear infinite
} 

@keyframes spinner5{
	to{
		transform:rotate(360deg)
	}
}
@-webkit-keyframes spinner5{
	to{
		-webkit-transform:rotate(360deg)
	}
}
.spinner5{
	min-width:24px;
	min-height:24px
}
.spinner5:before{
	content:'Loading…';
	position:absolute;
	top:3px;
	left:3px;
	width:24px;
	height:24px;
	/*margin-top:-10px;
	margin-left:-10px*/
}
.spinner5:not(:required):before{
	content:'';
	border-radius:50%;
	border:1px solid #f6f;
	border-top-color:#0e0;
	border-right-color:#0dd;
	border-bottom-color:#f90;
	animation:spinner5 .6s linear infinite;
	-webkit-animation:spinner5 .6s linear infinite
}
.spinner-container-loadin>span.content{
	position: absolute; padding: 5px; color: white;
}
.spinner-container-loadin>.spinner-container>.time-progress-bar{
	width:31px;text-align:center;padding-top:6px;
}