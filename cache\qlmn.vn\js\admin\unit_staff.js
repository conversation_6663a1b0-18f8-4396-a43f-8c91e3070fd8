$.unit_staff = {
    module: 'unit_staff',
    project: 'admin',
    rows: {},
    init: function() {
        var self = this;
        var urls = [$CFG.remote.base_url,'doing',$CFG.project,self.module,'list'];
        $.dm_datagrid.init(
            urls.join('/'),
            this.module, /*Đ<PERSON><PERSON> nghĩa thẻ để đổ dữ liệu ra: tbl_{this.module}*/
            '', /*Tiêu đề cho bảng dữ liệu*/
            [
                [
                    { title:'Mã', field:'id', width:100, sortable:true},
                    { title:'Họ & Tên', field:'fullname', width:150, sortable:true},
                    { title:'<PERSON><PERSON><PERSON> sinh', field:'birthday', width:150, sortable:true, formatter: formatDate},
                    { title:'Trình độ đào tạo', field:'degree_training', width:100, sortable:true},
                    { title:'Thời gian vào ngành', field:'time_industry', width:120, sortable:true,formatter: formatDate},
                    { title:'Địa chỉ gia đình', field:'address', width:120, sortable:true},
                    { field:'action',title:'Action',width:100,align:'center',
                    formatter:function(value,row,index){
                            var EDIT_LINK = '<a href="javascript:void(0)" class="edit-user fa fa-pencil fs11 mR5" data-id="'+ row.id +'" title="Sửa thông tin người dùng"></a>';
                            var DELETE_USER_LINK = '<a href="javascript:void(0)" class="delete-user fa fa-trash-o fs11 clrRed mL5" data-id="'+ row.id +'" title="Xóa người dùng"></a>';
                            return EDIT_LINK + "   |   " + DELETE_USER_LINK;
                        }
                    }
                ],
            ],
            process('admin/unit_staff/list', {async: true}, function(resp){
                $.unit_staff.rows = resp.rows
            }, null, false)
        );
    },initAngular: function(){
        var self = this;
        setTimeout(function(){
            angular.element($('body')).scope().$apply(function(scope){
                $.unit_staff.scope = scope;
            });
        });
    },angular: function(element,resp,callback,dialogRef) {
        var form = '<div >'+resp+'</div>';
        angular.element($(element)).scope().$apply(function(scope) {
            $(element).html(scope.compile(form,scope));
            if (typeof callback === 'function') {
                callback(scope);
            }
        });
    },showAddForm: function() {
        var self = this;
        $.dm_datagrid.showAddForm(
            {
                module: $CFG.project+'/'+self.module,
                action:'add',
                title:'Thêm cán bộ',
                size: size.wide,
                content: function(element){
                    loadForm($CFG.project+'/'+self.module,'add', {}, function(resp){
                        $(element).html(resp);
                    })
                }
            },
            function(resp){
                $("#tbl_"+self.module).datagrid('reload');
            }
        );
    }, showEditForm: function(e) {
        var self = this;
        var row = $("#tbl_"+self.module).datagrid('getSelected');
        if(row != null) {
            $.dm_datagrid.showEditForm(
                {
                    module: $CFG.project+'/'+self.module,
                    action:'edit',
                    title:'Sửa cán bộ',
                    size: size.wide,
                    content: function(element){
                        loadForm($CFG.project+'/'+self.module,'edit', {id: row.id}, function(resp){
                            $(element).html(resp);
                        })
                    }
                },
                function(resp){
                    $("#tbl_"+self.module).datagrid('reload');
                }
            );
        }else{
            BootstrapDialog.show({
                title: 'Thông báo',
                message: '<label class="checkbox-inline" style="color: red">Phải chọn một dòng! </label>',
                buttons: [{
                    label: 'Đồng ý',
                    icon: 'glyphicon glyphicon-log-out',
                    action: function(dialog){
                        dialog.close();
                    }
                }]
            });
        }
    },del: function(){ // XÓA
        var self = this;
        var ids = [];
        $.each($("#tbl_"+self.module).datagrid('getSelections'), function(index,row){
            ids.push(row.id);
        })
        if(!ids.length >0){
            // $.messager.alert('Thông báo','Hãy chọn dữ liệu cần xóa.');
            BootstrapDialog.show({
                title: 'Thông báo',
                message: '<label class="checkbox-inline" style="color: red">Hãy chọn dữ liệu cần xóa. </label>',
                buttons: [{
                    label: 'Đồng ý',
                    icon: 'glyphicon glyphicon-log-out',
                    action: function(dialog){
                        dialog.close();
                    }
                }]
            });
            return '';
        }
        var urls = ['admin','unit_staff','del'];
        process(urls.join('/'),{ids: ids.join(',')}, function(resp){
            $("#tbl_"+self.module).datagrid('reload');
        } )
    },importFromExcel: function() {
        var self = this;
        $.dm_datagrid.showAddForm({
            module: $CFG.project+'/'+self.module,
            action:'show_form_add_unit_staff',
            title: 'Thêm cán bộ từ file excel',  
            fullScreen: true,
            showButton: false,
            content: function(element){
                loadForm($CFG.project + '/' + self.module, 'show_form_add_unit_staff', { dataType: 'json' }, function(resp) {
                    $.unit_staff.angular(element,resp.html,function(scope){
                        scope.unit_staff = scope;
                        scope.unit_staff.data = {};
                        scope.unit_staff.uploadDone = function (data) {
                        };
                        
                        scope.unit_staff.uploadListUnitStaffExcel = function (data){                                       
                            var data = {
                                data: JSON.stringify(data)
                            }
                            process('admin/unit_staff/add_list_unit_staff_excel', data, function(resp){
                                console.log(resp)
                                if (resp.result == "success") {
                                    dialogCloseAll();
                                    if (typeof callback === 'function') {
                                        callback(resp);
                                    }
                                    var message = "Đã thêm thành công danh sách cán bộ";
                                    if (resp.exists.length > 0) {
                                        message += "\n" + "<br />Các cán bộ đã có trong danh sách:";
                                        for (var i = 0; i < resp.exists.length; i++) {
                                            message += "\n" + "<br /><mark>" + resp.exists[i] + "</mark>";
                                        }
                                        message += "\n" + "<br /><code>Vui lòng nhập lại nếu không phải cán bộ trong danh sách</code>";
                                    }
                                    $.messager.alert("Thông báo", message);
                                    $("#tbl_" + self.module).datagrid('reload');
                                }
                            });
                        }
                    })
                }, function () {
                    $.unit_staff.init();
                });
            }
        },)
    },onFileChange: function(event) {
        alert(1)
    }
};

$(document).on('click', '.edit-user', function(){
    var id = $(this).data('id');
    var self = this;
    $.dm_datagrid.showEditForm(
        {
            module: $CFG.project+'/'+ $.unit_staff.module,
            action:'edit',
            title:'Sửa cán bộ',
            size: size.wide,
            content: function(element){
                loadForm($CFG.project+'/'+ $.unit_staff.module,'edit', {id}, function(resp){
                    $(element).html(resp);
                })
            }
        },
        function(resp){
            $("#tbl_"+ $.unit_staff.module).datagrid('reload');
        }
    );
});

$(document).on('click', '.delete-user', function(){
    var ids = $(this).data('id');
    var urls = ['admin','unit_staff','del'];
    var captchaForm = $CFG.dialog_captcha('delete_user');
    var msg = '<div style = "font-size: 14px; color:red;">Hãy chắc chắn tài khoản này không có thành viên nào đang sử dụng trước khi xóa?</div>' + captchaForm;
    $.messager.confirm('Xác nhận', msg, function(r){
        if (r){
            var captcha = $('input[name="delete_user_captcha"]').val();
            process(urls.join('/'),{ids: ids, captcha:captcha},function(resp){
                if (resp.result == 'success') {
                    $.unit_staff.del();
                    $("#tbl_"+self.module).datagrid('reload');
                } else {
                    $.messager.alert('Thông báo', resp.errors);
                }
            },function(){
                // TO DO
            },false);
        }
    });
});

function formatDate(params) {
    var date = new Date(params);
    var day = String(date.getDate()).padStart(2, '0');
    var month = String(date.getMonth() + 1).padStart(2, '0');
    var year = date.getFullYear();

    // Return the formatted date
    return `${day}/${month}/${year}`;
}
